<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.iflytek.fpva</groupId>
    <artifactId>cdc-data-service</artifactId>
    <version>1.0-SNAPSHOT</version>

    <parent>
        <groupId>com.iflytek.medicalboot</groupId>
        <artifactId>parent</artifactId>
        <version>2.0.2-SNAPSHOT</version>
    </parent>

	<properties>
		<elasticsearch-client.version>7.17.24</elasticsearch-client.version>
        <jackson.version>2.14.1</jackson.version>
        <poi.version>5.2.2</poi.version>
        <poi-tl.version>1.12.2</poi-tl.version>
	</properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.iflytek.medicalboot</groupId>
            <artifactId>starter</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>spring-boot-starter-tomcat</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
			</exclusions>
        </dependency>
	    <!-- 瀚高数据库JDBC -->
		<dependency>
			<groupId>com.highgo</groupId>
			<artifactId>hgdb-pgjdbc</artifactId>
			<version>42.5.0</version>
		</dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <!--			<version>4.0.1</version>-->
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!--整合Knife4j-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>2.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.iflytek.fpva</groupId>
            <artifactId>uap-service-ext-api</artifactId>
            <version>5.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.iflytek.fpva</groupId>
            <artifactId>common</artifactId>
            <version>1.1.6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-ldap-core</artifactId>
                    <groupId>org.springframework.ldap</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.0</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.5.3.Final</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.5.3.Final</version>
        </dependency>

        <dependency>
            <groupId>com.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
            <version>0.3.2</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.4</version>
        </dependency>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>8.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>1.3.70</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.8.1</version>
        </dependency>
        <dependency>
            <groupId>com.iflytek.storage</groupId>
            <artifactId>storage-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>checker-qual</artifactId>
                    <groupId>org.checkerframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.github.ben-manes.caffeine/caffeine -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.8.5</version>
        </dependency>

	    <dependency>
                <groupId>co.elastic.clients</groupId>
                <artifactId>elasticsearch-java</artifactId>
                <version>${elasticsearch-client.version}</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
            <version>${elasticsearch-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>${poi-tl.version}</version>
        </dependency>

        <!-- 集成mdm sdk-->
        <dependency>
            <groupId>com.iflytek.zhyl</groupId>
            <artifactId>zyzl-mdm-api</artifactId>
            <version>3.0.8-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.25</version>
        </dependency>
    </dependencies>

	<profiles>
		<profile>
			<id>tomcat</id>
			<!-- 默认使用tomcat -->
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<properties>
				<profiles.active>tomcat</profiles.active>
				<maven.test.skip>true</maven.test.skip>
			</properties>
			<dependencies>
				<dependency>
					<artifactId>spring-boot-starter-tomcat</artifactId>
					<groupId>org.springframework.boot</groupId>
				</dependency>
			</dependencies>
		</profile>
		<profile>
			<!-- 东方通web容器 -->
			<id>tongWeb</id>
			<properties>
				<profiles.active>tongWeb</profiles.active>
				<maven.test.skip>true</maven.test.skip>
			</properties>
			<dependencies>
				<dependency>
					<groupId>com.tongweb.springboot</groupId>
					<artifactId>tongweb-spring-boot-starter-2.x</artifactId>
					<version>7.0.E.6</version>
				</dependency>
			</dependencies>

			<activation>
				<activeByDefault>false</activeByDefault>
			</activation>
		</profile>

		<profile>
			<!-- 中创web容器 -->
			<id>inforsuite</id>
			<properties>
				<profiles.active>inforsuite</profiles.active>
				<maven.test.skip>true</maven.test.skip>
			</properties>
			<dependencies>
				<dependency>
					<groupId>com.cvicse.embedded</groupId>
					<artifactId>spring-boot-starter-inforsuite</artifactId>
					<version>********.IFP01</version>
				</dependency>
			</dependencies>
			<activation>
				<activeByDefault>false</activeByDefault>
			</activation>
		</profile>
	</profiles>

    <build>
        <finalName>cdc-data-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>