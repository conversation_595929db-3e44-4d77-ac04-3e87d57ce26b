<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.pathogen.mapper.pg.AdsPdPathogenMapper">
    <sql id="dayCriteria">
        <if test="startDate != null and endDate != null">
            and day between #{startDate} and #{endDate}
        </if>
        <include refid="criteria"/>
    </sql>

    <sql id="monthCriteria">
        and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month})
        </foreach>
        <include refid="criteria"/>
    </sql>

    <sql id="yearCriteria">
        and year in
        <foreach collection="dateDims" open="(" close=")" separator="," item="item">
             #{item.year}
        </foreach>
        <include refid="criteria"/>
    </sql>

    <sql id="Base_Column">
        id,etl_create_datetime,etl_update_datetime,sample_org_id,sample_org_name,sample_org_province_code,
        sample_org_province,sample_org_city_code,sample_org_city,sample_org_district_code,sample_org_district,
        sample_org_func_district_code,sample_org_func_district,sample_org_street_code,sample_org_street,
        sample_person,sample_addr_detail,sample_addr_province_code,sample_addr_province,sample_addr_city_code,
        sample_addr_city,sample_addr_district_code,sample_addr_district,sample_addr_func_district_code,
        sample_addr_func_district,sample_addr_street_code,sample_addr_street,sample_addr_street_longitude,
        sample_addr_street_latitude,sample_addr_longitude,sample_addr_latitude,patient_name,patient_sex_name,
        patient_identity_no,patient_birth_day,patient_id,patient_age,patient_phone,living_addr_detail,
        living_addr_detail_std,living_addr_province_code,living_addr_province,living_addr_city_code,
        living_addr_city,living_addr_district_code,living_addr_district,living_addr_func_district_code,
        living_addr_func_district,living_addr_street_code,living_addr_street,living_addr_street_longitude,
        living_addr_street_latitude,living_addr_longitude,living_addr_latitude,job,company,company_address,
        company_province_code,company_province,company_city_code,company_city,company_district_code,company_district,
        company_func_district_code,company_func_district,company_street_code,company_street,
        company_street_longitude,company_street_latitude,company_longitude,company_latitude,sample_time,
        sample_id,sample_name,sample_type,sample_class,sample_character,sample_way,sample_source_type,
        send_org_id,send_org_name,send_doc_name,org_id,org_name,test_name,test_item_json,test_type,test_method,test_start_time,
        test_end_time,test_result_nominal,test_doc_name,test_report_id,test_report_name,test_report_time,test_report_signer,
        infect_type,infect_name,pathogen_name,pathogen_drug_json,pathogenic_spectrum_type,indata_way,inorg_type,pathogenic_multi_type,
        send_time,test_purpose,test_report_update_time,test_report_pdf_url,dept_name,serial_no,visit_time,test_support
    </sql>


    <sql id="criteria">
<!--        <if test="startDate != null and endDate != null">-->
<!--            and day between #{startDate} and #{endDate}-->
<!--        </if>-->
        <if test="areaCode != null and areaCode != ''">
            <if test="areaLevel == 1">
                and sample_addr_province_code = #{areaCode}
            </if>
            <if test="areaLevel == 2">
                and sample_addr_city_code = #{areaCode}
            </if>
            <if test="areaLevel == 3">
                and  sample_addr_func_district_code = #{areaCode}
            </if>
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and sample_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and sample_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and  sample_addr_func_district_code = #{districtCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and sample_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and sample_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and  sample_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="inOrgType != null and inOrgType != ''">
            and inorg_type = #{inOrgType}
        </if>
        <if test="orgName != null and orgName != ''">
            and org_name = #{orgName}
        </if>
        <if test="pathogenName != null and pathogenName != ''">
            and pathogen_name = #{pathogenName}
        </if>
        <if test="infectName != null and infectName != ''">
            and infect_name = #{infectName}
        </if>
        <if test="infectType != null and infectType != ''">
            and infect_type = #{infectType}
        </if>
        <if test="infectTypes != null and infectTypes.size() > 0">
            and infect_type in
            <foreach collection="infectTypes" open="(" separator="," close=")" item="item">
                 #{item}
            </foreach>
        </if>
        <if test="pathogenicSpectrumType != null and pathogenicSpectrumType != ''">
            and pathogenic_spectrum_type = #{pathogenicSpectrumType}
        </if>
        <if test="sampleType != null and sampleType != ''">
            and sample_type = #{sampleType}
        </if>
    </sql>

    <sql id="sampleTypeCnt">
        COALESCE(sum(case when sample_type = '临床样本' then sample_cnt else 0 end), 0) as clinicalCnt,
        COALESCE(sum(case when sample_type = '临床样本' then sample_cnt_last else 0 end), 0) as clinicalCntLast,
        COALESCE(sum(case when sample_type = '临床样本' then sample_cnt_last_y else 0 end), 0) as clinicalCntLastY,

        COALESCE(sum(case when sample_type = '环境样本' then sample_cnt else 0 end), 0) as environmentalCnt,
        COALESCE(sum(case when sample_type = '环境样本' then sample_cnt_last else 0 end), 0) as environmentalCntLast,
        COALESCE(sum(case when sample_type = '环境样本' then sample_cnt_last_y else 0 end), 0) as environmentalCntLastY,

        COALESCE(sum(case when sample_type = '生物样本' then sample_cnt else 0 end), 0) as biologicalCnt,
        COALESCE(sum(case when sample_type = '生物样本' then sample_cnt_last else 0 end), 0) as biologicalCntLast,
        COALESCE(sum(case when sample_type = '生物样本' then sample_cnt_last_y else 0 end), 0) as biologicalCntLastY
    </sql>

    <sql id="inOrgTypeCnt">
        COALESCE(sum(case when inorg_type = '疾控中心' then sample_cnt else 0 end), 0) as cdcCnt,
        COALESCE(sum(case when inorg_type = '医疗机构' then sample_cnt else 0 end), 0) as medicalCnt,
        COALESCE(sum(case when inorg_type = '第三方机构' then sample_cnt else 0 end), 0) as thirdPartyCnt
    </sql>

    <sql id="inOrgTypeCntLast">
        COALESCE(sum(case when inorg_type = '疾控中心' then sample_cnt_last else 0 end), 0) as cdcCntLast,
        COALESCE(sum(case when inorg_type = '医疗机构' then sample_cnt_last else 0 end), 0) as medicalCntLast,
        COALESCE(sum(case when inorg_type = '第三方机构' then sample_cnt_last else 0 end), 0) as thirdPartyCntLast
    </sql>

    <sql id="inOrgTypeCntLastY">
        COALESCE(sum(case when inorg_type = '疾控中心' then sample_cnt_last_y else 0 end), 0) as cdcCntLastY,
        COALESCE(sum(case when inorg_type = '医疗机构' then sample_cnt_last_y else 0 end), 0) as medicalCntLastY,
        COALESCE(sum(case when inorg_type = '第三方机构' then sample_cnt_last_y else 0 end), 0) as thirdPartyCntLastY
    </sql>
    <sql id="totalSampleCnt">
        COALESCE(sum(sample_cnt), 0) as total,
        COALESCE(sum(sample_cnt_last), 0) as totalLast,
        COALESCE(sum(sample_cnt_last_y), 0) as totalLastY
    </sql>
    <sql id="sampleCnt">
        COALESCE(sum(sample_cnt), 0) as currValue,
        COALESCE(sum(sample_cnt_last), 0) as coincidentValue
    </sql>

    <sql id="infectTypeCnt">
        COALESCE(sum(case when infect_type in ('甲类', '乙类') then sample_cnt else 0 end), 0) as classAAndBSampleCnt,
        COALESCE(sum(case when infect_type in ('甲类', '乙类') then sample_cnt_last else 0 end), 0) as classAAndBSampleCntLast,
        COALESCE(sum(case when infect_type in ('甲类', '乙类') then sample_positive_cnt else 0 end), 0) as classAAndBSamplePositiveCnt,
        COALESCE(sum(case when infect_type in ('甲类', '乙类') then sample_positive_cnt_last else 0 end), 0) as classAAndBSamplePositiveCntLast,


        COALESCE(sum(case when infect_type in ('丙类') then sample_cnt else 0 end), 0) as classCSampleCnt,
        COALESCE(sum(case when infect_type in ('丙类') then sample_cnt_last else 0 end), 0) as classCSampleCntLast,
        COALESCE(sum(case when infect_type in ('丙类') then sample_positive_cnt else 0 end), 0) as classCSamplePositiveCnt,
        COALESCE(sum(case when infect_type in ('丙类') then sample_positive_cnt_last else 0 end), 0) as classCSamplePositiveCntLast,

        COALESCE(sum(case when infect_type in ('其它法定') then sample_cnt else 0 end), 0) as otherNotifiableSampleCnt,
        COALESCE(sum(case when infect_type in ('其它法定') then sample_cnt_last else 0 end), 0) as otherNotifiableSampleCntLast,
        COALESCE(sum(case when infect_type in ('其它法定') then sample_positive_cnt else 0 end), 0) as otherNotifiableSamplePositiveCnt,
        COALESCE(sum(case when infect_type in ('其它法定') then sample_positive_cnt_last else 0 end), 0) as otherNotifiableSamplePositiveCntLast,

        COALESCE(sum(case when infect_type in ('其它非法定') then sample_cnt else 0 end), 0) as otherSampleCnt,
        COALESCE(sum(case when infect_type in ('其它非法定') then sample_cnt_last else 0 end), 0) as otherSampleCntLast,
        COALESCE(sum(case when infect_type in ('其它非法定') then sample_positive_cnt else 0 end), 0) as otherSamplePositiveCnt,
        COALESCE(sum(case when infect_type in ('其它非法定') then sample_positive_cnt_last else 0 end), 0) as otherSamplePositiveCntLast

    </sql>

    <sql id="infectTypePositiveCnt">
        COALESCE(sum(case when infect_type in ('甲类', '乙类') then sample_positive_cnt else 0 end), 0) as classAAndBCnt,
        COALESCE(sum(case when infect_type in ('甲类', '乙类') then sample_positive_cnt_last else 0 end), 0) as classAAndBCntLast,
        COALESCE(sum(case when infect_type in ('甲类', '乙类') then sample_positive_cnt_last_y else 0 end), 0) as classAAndBCntLastY,

        COALESCE(sum(case when infect_type in ('丙类') then sample_positive_cnt else 0 end), 0) as classCCnt,
        COALESCE(sum(case when infect_type in ('丙类') then sample_positive_cnt_last else 0 end), 0) as classCCntLast,
        COALESCE(sum(case when infect_type in ('丙类') then sample_positive_cnt_last_y else 0 end), 0) as classCCntLastY,

        COALESCE(sum(case when infect_type in ('其它法定') then sample_positive_cnt else 0 end), 0) as otherNotifiableCnt,
        COALESCE(sum(case when infect_type in ('其它法定') then sample_positive_cnt_last else 0 end), 0) as otherNotifiableCntLast,
        COALESCE(sum(case when infect_type in ('其它法定') then sample_positive_cnt_last_y else 0 end), 0) as otherNotifiableCntLastY,

        COALESCE(sum(case when infect_type in ('其它非法定') then sample_positive_cnt else 0 end), 0) as otherCnt,
        COALESCE(sum(case when infect_type in ('其它非法定') then sample_positive_cnt_last else 0 end), 0) as otherCntLast,
        COALESCE(sum(case when infect_type in ('其它非法定') then sample_positive_cnt_last_y else 0 end), 0) as otherCntLastY

    </sql>

    <sql id="samplePositiveStat">
        COALESCE(sum(sample_cnt), 0) as sampleCnt,
        COALESCE(sum(sample_cnt_last), 0) as sampleCntLast,
        COALESCE(sum(sample_positive_cnt), 0) as samplePositiveCnt,
        COALESCE(sum(sample_positive_cnt_last), 0) as samplePositiveCntLast
    </sql>

    <select id="inOrgTypeTotalCnt" resultType="com.iflytek.cdc.pathogen.vo.PdPathogenInOrgTypeStat">
        select
            COALESCE(sum(sample_cnt), 0) as total,
            COALESCE(sum(sample_cnt_last), 0) as totalLast,
            COALESCE(sum(sample_cnt_last_y), 0) as totalLastY,
            <include refid="inOrgTypeCnt" />,
            <include refid="inOrgTypeCntLast" />,
            <include refid="inOrgTypeCntLastY" />
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
    </select>

    <select id="sampleTypeTotalCnt" resultType="com.iflytek.cdc.pathogen.vo.PdPathogenSampleTypeStat">
        select
            COALESCE(sum(sample_cnt), 0) as total,
            COALESCE(sum(sample_cnt_last), 0) as totalLast,
            COALESCE(sum(sample_cnt_last_y), 0) as totalLastY,
            <include refid="sampleTypeCnt" />
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
    </select>

    <select id="sampleTypeDayStat" resultType="com.iflytek.cdc.pathogen.vo.PdPathogenSampleTypeStat">
        select
            CAST(day AS DATE) as statDate,
            <include refid="sampleTypeCnt" />
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by day
        order by day
    </select>

    <select id="sampleTypeMonthStat" resultType="com.iflytek.cdc.pathogen.vo.PdPathogenSampleTypeStat">
        select
            year, month,
            CONCAT(year, '年', month, '月') as statDate,
            <include refid="sampleTypeCnt" />
        from ads.ads_pd_pathogen_m
        where 1=1
        <include refid="monthCriteria" />
        group by year, month
        order by year, month
    </select>

    <select id="sampleTypeYearStat" resultType="com.iflytek.cdc.pathogen.vo.PdPathogenSampleTypeStat">
        select
            year,
            CONCAT(year, '年') as statDate,
            <include refid="sampleTypeCnt" />
        from ads.ads_pd_pathogen_y
        where 1=1
        <include refid="yearCriteria" />
        group by year
        order by year
    </select>

    <select id="sampleDayStat" resultType="com.iflytek.cdc.pathogen.vo.PathogenDateStatVO">
        select
        CAST(day AS VARCHAR)  as statDate,
        <include refid="sampleCnt" />
        from ads.ads_pd_pathogen_d
        where day between #{startDate} and #{endDate}
        <include refid="dayCriteria" />
        group by day
        order by day
    </select>

    <select id="sampleMonthStat" resultType="com.iflytek.cdc.pathogen.vo.PathogenDateStatVO">
        select
        year, month,
        CONCAT(year, '年', month, '月') as statDate,
        <include refid="sampleCnt" />
        from ads.ads_pd_pathogen_m
        where 1=1
        <include refid="monthCriteria" />
        group by year, month
        order by year, month
    </select>

    <select id="sampleYearStat" resultType="com.iflytek.cdc.pathogen.vo.PathogenDateStatVO">
        select
        year,
        CONCAT(year, '年') as statDate,
        <include refid="sampleCnt" />
        from ads.ads_pd_pathogen_y
        where 1=1
        <include refid="yearCriteria" />
        group by year
        order by year
    </select>

    <select id="inOrgTypeDayStat" resultType="com.iflytek.cdc.pathogen.vo.PdPathogenInOrgTypeStat">
        select
        CAST(day AS DATE) as statDate,
        <include refid="totalSampleCnt" />,
        <include refid="inOrgTypeCnt" />
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by day
        order by day
    </select>

    <select id="inOrgTypeMonthStat" resultType="com.iflytek.cdc.pathogen.vo.PdPathogenInOrgTypeStat">
        select
        year, month,
        CONCAT(year, '年', month, '月') as statDate,
        <include refid="totalSampleCnt" />,
        <include refid="inOrgTypeCnt" />
        from ads.ads_pd_pathogen_m
        where 1=1
        <include refid="monthCriteria" />
        group by year, month
        order by year, month
    </select>

    <select id="inOrgTypeYearStat" resultType="com.iflytek.cdc.pathogen.vo.PdPathogenInOrgTypeStat">
        select
        year,
        CONCAT(year, '年') as statDate,
        <include refid="totalSampleCnt" />,
        <include refid="inOrgTypeCnt" />
        from ads.ads_pd_pathogen_y
        where 1=1
        <include refid="yearCriteria" />
        group by year
        order by year
    </select>

    <select id="infectSampleCnt" resultType="com.iflytek.cdc.pathogen.vo.CommonGroupCntVO">
        select
            infect_name as name,
            sum(sample_cnt) as value
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by infect_name
        order by value desc
    </select>

    <select id="sampleCharacterCnt" resultType="com.iflytek.cdc.pathogen.vo.CommonGroupCntVO">
        select
            sample_character as name,
            sum(sample_cnt) as value
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by sample_character
    </select>

    <select id="sampleSourceTypeCnt" resultType="com.iflytek.cdc.pathogen.vo.CommonGroupCntVO">
        select
        sample_source_type as name,
        sum(sample_cnt) as value
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by sample_source_type
    </select>

    <select id="samplingAddressCnt" resultType="com.iflytek.cdc.pathogen.vo.CommonGroupCntVO">
        select
            sample_addr_street as name,
            sum(sample_cnt) as value
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by sample_addr_street_code, sample_addr_street
    </select>

    <select id="sampleTypeCnt" resultType="com.iflytek.cdc.pathogen.vo.CommonGroupCntVO">
        select
            sample_type as name,
            sum(sample_cnt) as value
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by sample_type
    </select>

    <select id="ageGroupCnt" resultType="com.iflytek.cdc.pathogen.vo.CommonGroupCntVO">
        select
        patient_age_group as name,
        sum(sample_cnt) as value
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by patient_age_group
    </select>

    <select id="ageGroupPositiveCnt" resultType="com.iflytek.cdc.pathogen.vo.CommonGroupCntVO">
        select
        patient_age_group as name,
        sum(sample_positive_cnt) as value
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by patient_age_group
    </select>

    <select id="sexNameGroupCnt" resultType="com.iflytek.cdc.pathogen.vo.CommonGroupCntVO">
        select
        patient_sex_name as name,
        sum(sample_cnt) as value
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by patient_sex_name
    </select>

    <select id="sexNameGroupPositiveCnt" resultType="com.iflytek.cdc.pathogen.vo.CommonGroupCntVO">
        select
        patient_sex_name as name,
        sum(sample_positive_cnt) as value
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by patient_sex_name
    </select>

    <select id="listPathogenInfo" resultType="com.iflytek.cdc.pathogen.vo.PdPathogenInfo">
        select
           <include refid="Base_Column"/>
        from ads.ads_pd_pathogen_info
        where infect_name is not null
        <if test="startDate != null and endDate != null">
            and CAST(sample_time as DATE) between #{startDate} and #{endDate}
        </if>
        <include refid="criteria"/>
        <if test="orgName != null and orgName != ''">
            and org_name = #{orgName}
        </if>
        <if test="sampleId != null and sampleId != ''">
            and sample_id like concat('%', #{sampleId}, '%')
        </if>
        <if test="testName != null and testName != ''">
            and test_name = #{testName}
        </if>
        <if test="name != null and name != ''">
            and patient_name like concat('%', #{name}, '%')
        </if>
        <if test="testResult != null and testResult != ''">
            and test_result_nominal = #{testResult}
        </if>
    </select>

    <select id="loadById" resultType="com.iflytek.cdc.pathogen.vo.PdPathogenInfo">
        select
            <include refid="Base_Column"/>
        from ads.ads_pd_pathogen_info
        where id = #{id}
    </select>

    <select id="orgSampleTypeStat" resultType="com.iflytek.cdc.pathogen.vo.PdPathogenOrgStat">
        select
            org_name as orgName,
            <include refid="sampleTypeCnt" />
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by org_name
    </select>
    
    <select id="infectTypeTotalCnt" resultType="com.iflytek.cdc.pathogen.vo.InfectTypeTotalCnt">
        select
            COALESCE(sum(sample_cnt), 0) totalSampleCnt,
            COALESCE(sum(sample_cnt_last), 0) totalSampleCntLast,
            COALESCE(sum(sample_positive_cnt), 0) totalSamplePositiveCnt,
            COALESCE(sum(sample_positive_cnt_last), 0) totalSamplePositiveCntLast,
            <include refid="infectTypeCnt" />
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
    </select>

    <select id="infectTypePositiveCntDayStat" resultType="com.iflytek.cdc.pathogen.vo.InfectTypeGroupCnt">
        select
        CAST(day AS DATE)  as statDate,
        <include refid="infectTypePositiveCnt" />
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by day
        order by day
    </select>

    <select id="infectTypePositiveCntMonthStat" resultType="com.iflytek.cdc.pathogen.vo.InfectTypeGroupCnt">
        select
        year, month,
        CONCAT(year, '年', month, '月') as statDate,
        <include refid="infectTypePositiveCnt" />
        from ads.ads_pd_pathogen_m
        where 1=1
        <include refid="monthCriteria" />
        group by year, month
        order by year, month
    </select>

    <select id="infectTypePositiveCntYearStat" resultType="com.iflytek.cdc.pathogen.vo.InfectTypeGroupCnt">
        select
        year,
        CONCAT(year, '年') as statDate,
        <include refid="infectTypePositiveCnt" />
        from ads.ads_pd_pathogen_y
        where 1=1
        <include refid="yearCriteria" />
        group by year
        order by year
    </select>

    <select id="infectTypeStreetPositiveStat" resultType="com.iflytek.cdc.pathogen.vo.InfectTypeGroupCnt">
        select
            sample_addr_street as name,
        sample_addr_street_code,
            <include refid="infectTypePositiveCnt" />
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by sample_addr_street, sample_addr_street_code
        order by sample_addr_street_code
    </select>

    <select id="infectTypeGroupPositiveCnt" resultType="com.iflytek.cdc.pathogen.vo.CommonGroupCntVO">
        select
        infect_type as name,
        sum(sample_positive_cnt) as value
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by infect_type
    </select>

    <select id="infectNamePositiveCnt" resultType="com.iflytek.cdc.pathogen.vo.PositiveStat">
        select groupName, samplePositiveCnt, sampleCnt,
        case when sampleCnt != 0 then round(samplePositiveCnt::numeric / sampleCnt::numeric, 4) else 0 end as positiveRate
        from (select
        infect_name as groupName,
        <include refid="samplePositiveStat" />
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by infect_name) tab
        <choose>
            <when test="property == 'positiveRate'">
                order by positiveRate desc
            </when>
            <otherwise>
                order by samplePositiveCnt desc
            </otherwise>
        </choose>
    </select>

    <select id="positiveCntDayStat" resultType="com.iflytek.cdc.pathogen.vo.PositiveStat">
        select
        CAST(day AS DATE)  as statDate,
        <include refid="samplePositiveStat" />
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by day
        order by day
    </select>

    <select id="positiveCntMonthStat" resultType="com.iflytek.cdc.pathogen.vo.PositiveStat">
        select
        year, month,
        CONCAT(year, '年', month, '月') as statDate,
        <include refid="samplePositiveStat" />
        from ads.ads_pd_pathogen_m
        where 1=1
        <include refid="monthCriteria" />
        group by year, month
        order by year, month
    </select>

    <select id="positiveCntYearStat" resultType="com.iflytek.cdc.pathogen.vo.PositiveStat">
        select
        year,
        CONCAT(year, '年') as statDate,
        <include refid="samplePositiveStat" />
        from ads.ads_pd_pathogen_y
        where 1=1
        <include refid="yearCriteria" />
        group by year
        order by year
    </select>

    <select id="positiveStreetGroupCnt" resultType="com.iflytek.cdc.pathogen.vo.CommonGroupCntVO">
        select
            sample_addr_street as name,
            sum(sample_positive_cnt) as value
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by sample_addr_street_code, sample_addr_street
        order by sample_addr_street_code
    </select>

    <select id="pathogenNamePositiveStat" resultType="com.iflytek.cdc.pathogen.vo.PositiveStat">
        select
        pathogen_name as groupName,
        <include refid="samplePositiveStat" />
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by pathogen_name
        order by samplePositiveCnt desc
    </select>

    <select id="pathogenMultiStat" resultType="com.iflytek.cdc.pathogen.vo.CommonGroupCntVO">
        select
            case when pathogenic_multi_type = '1' then '复合感染' else pathogen_name end as name,
            sum(sample_positive_cnt) as value
        from ads.ads_pd_pathogen_d
        where pathogen_name = #{pathogenName}
        <include refid="dayCriteria"/>
        group by name
    </select>

    <select id="testMethodStat" resultType="com.iflytek.cdc.pathogen.vo.CommonGroupCntVO">
        select
        test_method as name,
        sum(sample_positive_cnt) as value
        from ads.ads_pd_pathogen_d
        where 1=1
        <include refid="dayCriteria" />
        group by test_method
    </select>

    <select id="listPathogenDrug" resultType="com.iflytek.cdc.pathogen.vo.PdPathogenDrugVO">
        select
            pathogen_name,
            pathogen_drug_json
        from ads.ads_pd_pathogen_info
        where 1=1
        <if test="startDate != null and endDate != null">
            and sample_time between #{startDate} and #{endDate}
        </if>
        <include refid="criteria"/>
    </select>
</mapper>
