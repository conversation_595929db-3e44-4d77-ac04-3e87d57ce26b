<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.reportcard.mapper.bu.ExchangeInfoRecordMapper">
    <resultMap type="com.iflytek.cdc.reportcard.vo.ExchangeInfoRecordVO" id="exchangeInfoRecordVO">
        <result property="exchangeId" jdbcType="VARCHAR" column="exchangeId"/>
        <result property="templateId" jdbcType="VARCHAR" column="templateId"/>
        <result property="templateTitle" jdbcType="VARCHAR" column="templateTitle"/>
        <result property="exchangeTitle" jdbcType="VARCHAR" column="exchangeTitle"/>
        <result property="exchangeDescription" jdbcType="VARCHAR" column="exchangeDescription"/>
        <result property="exchangeStatus"  column="exchangeStatus"/>
        <result property="templateStatus"  column="templateStatus"/>
        <result property="createTime" jdbcType="TIMESTAMP" column="createTime"/>
        <result property="recipient" jdbcType="VARCHAR" column="recipient"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="sender" jdbcType="VARCHAR" column="sender"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>

    </resultMap>


    <select id="queryRecordById" resultMap="exchangeInfoRecordVO">

        select
        r.id as exchangeId,
        t.id as templateId ,
        t.template_title as templateTitle,
        r.exchange_title as exchangeTitle,
        r.exchange_description as exchangeDescription,
        r.exchange_status as exchangeStatus,
        t.template_status as templateStatus,
        r.create_time as createTime,
        r.recipient as recipient,
        r.sender as sender
        from
        tb_cdcmr_exchange_info_record r
        join tb_cdcmr_exchange_info_template t on
        r.template_id = t.id
        where
        r.id = #{recordId}


    </select>
    <select id="queryRecordList" resultMap="exchangeInfoRecordVO">
        select
        r.id as exchangeId,
        t.id as templateId ,
        t.template_title as templateTitle,
        r.exchange_title as exchangeTitle,
        r.exchange_description as exchangeDescription,
        r.exchange_status as exchangeStatus,
        t.template_status as templateStatus,
        r.create_time as createTime,
        r.recipient as recipient,
        r.sender as sender
        from
        tb_cdcmr_exchange_info_record r
        join tb_cdcmr_exchange_info_template t on
        r.template_id = t.id
        where 1 = 1
        <if test="templateId != null and templateId != ''">
            and r.template_id like concat('%', #{templateId}, '%')
        </if>
        <if test="templateTitle != null and templateTitle != ''">
            and t.template_title like concat('%', #{templateTitle}, '%')
        </if>
        <if test="templateStatus != null ">
            and t.template_status = #{templateStatus}
        </if>
        <if test="exchangeId != null and exchangeId != ''">
            and r.id like concat('%', #{exchangeId}, '%')
        </if>
        <if test="exchangeTitle != null and exchangeTitle != ''">
            and r.exchange_title like concat('%', #{exchangeTitle}, '%')
        </if>
        <if test="exchangeStatus != null ">
            and r.exchange_status = #{exchangeStatus}
        </if>
        <if test="startDate != null ">
            and r.create_time >= #{startDate}
        </if>
        <if test="endDate != null ">
            and r.create_time &lt;= #{endDate}
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and r.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and r.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and r.district_code = #{districtCode}
        </if>
        order by r.create_time desc
    </select>
</mapper>