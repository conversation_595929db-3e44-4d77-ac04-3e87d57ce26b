<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.reportcard.mapper.pg.AdsReportMsProcessStatMapper">
    <sql id="areaChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="provinceCode != null and provinceCode != ''">
                    and living_addr_province_code = #{provinceCode}
                </if>
                <if test="cityCode != null and cityCode != ''">
                    and living_addr_city_code = #{cityCode}
                </if>
                <if test="districtCode != null and districtCode != ''">
                    and living_addr_func_district_code = #{districtCode}
                </if>
                <if test="streetCode != null and streetCode != ''">
                    and living_addr_street_code = #{streetCode}
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and living_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and living_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and living_addr_func_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and living_addr_street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="provinceCode != null and provinceCode != ''">
                    and report_addr_province_code = #{provinceCode}
                </if>
                <if test="cityCode != null and cityCode != ''">
                    and report_addr_city_code = #{cityCode}
                </if>
                <if test="districtCode != null and districtCode != ''">
                    and report_addr_func_district_code = #{districtCode}
                </if>
                <if test="streetCode != null and streetCode != ''">
                    and report_addr_street_code = #{streetCode}
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and report_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and report_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and report_addr_func_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and report_addr_street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>
    <sql id="monthDb">
        <choose>
            <when test="timeType == 'approveTime'">
                ads.ads_ms_dyqw_infect_ct_m
            </when>
            <when test="timeType == 'onsetTime'">
                ads.ads_ms_dyqw_infect_ot_m
            </when>
            <when test="timeType == 'deathTime'">
                ads.ads_ms_dyqw_infect_dt_m
            </when>
            <otherwise>
                ads.ads_ms_dyqw_infect_it_m
            </otherwise>
        </choose>
    </sql>

    <sql id="quarterDb">
        <choose>
            <when test="timeType == 'approveTime'">
                ads.ads_ms_dyqw_infect_ct_q
            </when>
            <when test="timeType == 'onsetTime'">
                ads.ads_ms_dyqw_infect_ot_q
            </when>
            <when test="timeType == 'deathTime'">
                ads.ads_ms_dyqw_infect_dt_q
            </when>
            <otherwise>
                ads.ads_ms_dyqw_infect_it_q
            </otherwise>
        </choose>
    </sql>
    <sql id="yearDb">
        <choose>
            <when test="timeType == 'approveTime'">
                ads.ads_ms_dyqw_infect_ct_y
            </when>
            <when test="timeType == 'onsetTime'">
                ads.ads_ms_dyqw_infect_ot_y
            </when>
            <when test="timeType == 'deathTime'">
                ads.ads_ms_dyqw_infect_dt_y
            </when>
            <otherwise>
                ads.ads_ms_dyqw_infect_it_y
            </otherwise>
        </choose>
    </sql>

    <sql id="dayDb">
        <choose>
            <when test="timeType == 'approveTime'">
                ads.ads_ms_dyqw_infect_ct_d d
            </when>
            <when test="timeType == 'onsetTime' or timeType == 'visitTime' or timeType == 'firVisitTime'">
                ads.ads_ms_dyqw_infect_ot_d d
            </when>
            <when test="timeType == 'deathTime'">
                ads.ads_ms_dyqw_infect_dt_d d
            </when>
            <otherwise>
                ads.ads_ms_dyqw_infect_it_d d
            </otherwise>
        </choose>
    </sql>
    <sql id="weekDb">
        <choose>
            <when test="timeType == 'approveTime'">
                ads.ads_ms_dyqw_infect_ct_w
            </when>
            <when test="timeType == 'onsetTime'">
                ads.ads_ms_dyqw_infect_ot_w
            </when>
            <when test="timeType == 'deathTime'">
                ads.ads_ms_dyqw_infect_dt_w
            </when>
            <otherwise>
                ads.ads_ms_dyqw_infect_it_w
            </otherwise>
        </choose>
    </sql>

    <sql id="tenDaysDb">
        <choose>
            <when test="timeType == 'approveTime'">
                ads.ads_ms_dyqw_infect_ct_td
            </when>
            <when test="timeType == 'onsetTime'">
                ads.ads_ms_dyqw_infect_ot_td
            </when>
            <when test="timeType == 'deathTime'">
                ads.ads_ms_dyqw_infect_dt_td
            </when>
            <otherwise>
                ads.ads_ms_dyqw_infect_it_td
            </otherwise>
        </choose>
    </sql>
    <sql id="areaMultiChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and living_addr_province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and living_addr_city_code  in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and living_addr_func_district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and living_addr_street_code  in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and living_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and living_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and living_addr_func_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and report_addr_province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and report_addr_city_code in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and report_addr_func_district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and report_addr_street_code in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and report_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and report_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and report_addr_func_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and report_addr_street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>
    <select id="findMonthDistribution"
            resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="monthDimSelectColumns"/>,
        <include refid="sumCnt"/>
        <!-- ads.ads_ms_dyqw_infect_report r-->
        FROM ads.ads_rep_infect_report_info r
        <include refid="joinMonthDim"/>
        WHERE 1=1 and r.source_id = '11'
        and <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        <include refid="monthDimGroupOrder"/>
    </select>

    <select id="findQuarterDistribution"
            resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="quarterDimSelectColumns"/>,
        <include refid="sumCnt"/>
        FROM
       <!-- ads.ads_ms_dyqw_infect_report r-->
        ads.ads_rep_infect_report_info r
        <include refid="joinQuarterDim"/>
        WHERE 1=1 and r.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        <include refid="quarterDimGroupOrder"/>
    </select>

    <select id="findYearDistribution"
            resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="yearDimSelectColumns"/>,
        <include refid="sumCnt"/>
        FROM
        <!--ads.ads_ms_dyqw_infect_report r-->
         ads.ads_rep_infect_report_info r
        <include refid="joinYearDim"/>
        WHERE 1=1 and r.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        <include refid="yearDimGroupOrder"/>
    </select>

    <select id="groupArea" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="selectGroupArea"/>
        <include refid="sumCnt"/>
        FROM
       <!-- ads.ads_ms_dyqw_infect_report-->
        ads.ads_rep_infect_report_info
        WHERE 1=1 and source_id = '11'
        AND <include refid="dynamicDayField"/> BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>

        GROUP BY
        <include refid="whereGroupArea"/>
    </select>
    <select id="groupAreaCurrentLevel" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        <include refid="selectGroupAreaCurrentLevel"/>
        <include refid="sumCnt"/>
        from
        <!--ads.ads_ms_dyqw_infect_report-->
        ads.ads_rep_infect_report_info
        WHERE 1=1 and source_id = '11'
        AND <include refid="dynamicDayField"/> BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose" />
        group by
        <include refid="whereGroupAreaCurrentLevel"/>
    </select>
    <select id="groupAge" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="selectAgeGroup"/>,
        <include refid="sumCnt"/>
        FROM
        <include refid="fromWithAgeGroup"/>
        WHERE 1=1  and  b.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        CASE
        WHEN ag.value_code IS NOT NULL THEN ag.value_name
        ELSE '不详'
        END,
        value_sort
        ORDER BY
        value_sort
    </select>
    <select id="groupSex" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        patient_sex_name,
        patient_sex_name AS description,
        <include refid="sumCnt"/>
        FROM
        <!--ads.ads_ms_dyqw_infect_report-->
        ads.ads_rep_infect_report_info
        WHERE 1=1 and source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by patient_sex_name  order by patient_sex_name
    </select>
    <select id="groupJob" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        nultitude_type_name as patientJob,
        nultitude_type_name as description,
        <include refid="sumCnt"/>
        from
        <!--ads.ads_ms_dyqw_infect_report-->
        ads.ads_rep_infect_report_info
        WHERE 1=1 and source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose" />
        group by nultitude_type_name  order by processNewCnt desc
    </select>
    <select id="dayOverAll" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="dynamicDayField"/> AS statDate,
        <include refid="sumCnt"/>
        FROM
       <!-- ads.ads_ms_dyqw_infect_report-->
        ads.ads_rep_infect_report_info
        WHERE 1=1 and source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose" />
        GROUP BY <include refid="dynamicDayField"/>
        ORDER BY <include refid="dynamicDayField"/>
    </select>
    <select id="weekOverAll"
            resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="weekDimSelectColumns"/>,
        <include refid="sumCnt"/>
        FROM
        <!--ads.ads_ms_dyqw_infect_report r-->
        ads.ads_rep_infect_report_info r
        <include refid="joinWeekDim"/>
        WHERE 1=1 and r.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        <include refid="weekDimGroupOrder"/>
    </select>

    <select id="meadowOverAll"
            resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="tenDaysDimSelectColumns"/>,
        <include refid="sumCnt"/>
        FROM
       <!-- ads.ads_ms_dyqw_infect_report r-->
        ads.ads_rep_infect_report_info r
        <include refid="joinTenDaysDim"/>
        WHERE 1=1 and r.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        <include refid="tenDaysDimGroupOrder"/>
    </select>

    <select id="monthOverAll" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="monthDimSelectColumns"/>,
        <include refid="sumCnt"/>
        FROM
        ads.ads_rep_infect_report_info
        <include refid="joinMonthDim"/>
        WHERE 1=1 and source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose" />
        <include refid="monthDimGroupOrder"/>
    </select>

    <select id="quarterOverAll" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="quarterDimSelectColumns"/>,
        <include refid="sumCnt"/>
        FROM
       <!-- ads.ads_ms_dyqw_infect_report r-->
        ads.ads_rep_infect_report_info r
        <include refid="joinQuarterDim"/>
        WHERE 1=1 and r.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        <include refid="quarterDimGroupOrder"/>
    </select>
    <select id="yearOverAll" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="yearDimSelectColumns"/>,
        <include refid="sumCnt"/>
        FROM
       <!-- ads.ads_ms_dyqw_infect_report-->
        ads.ads_rep_infect_report_info
        <include refid="joinYearDim"/>
        WHERE 1=1 and source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        <include refid="yearDimGroupOrder"/>
    </select>
    <select id="dayAreaChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="dynamicDayField"/> AS statDate,
        <include refid="selectGroupArea"/>
        <include refid="sumCnt"/>
        FROM
       <!-- ads.ads_ms_dyqw_infect_report-->
        ads.ads_rep_infect_report_info
        WHERE 1=1 and source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        <include refid="dynamicDayField"/>
        <if test="'${whereGroupArea}' != null and '${whereGroupArea}' != ''">
            , <include refid="whereGroupArea"/>
        </if>
        ORDER BY
        <include refid="dynamicDayField"/>
        <if test="'${whereGroupArea}' != null and '${whereGroupArea}' != ''">
            , area_code
        </if>
        , processNewCnt DESC
    </select>
    <select id="weekAreaChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="weekDimSelectColumns"/>,
        <include refid="selectGroupArea"/>
        <include refid="sumCnt"/>
        FROM
       <!-- ads.ads_ms_dyqw_infect_report r-->
        ads.ads_rep_infect_report_info r
        <include refid="joinWeekDim"/>
        WHERE 1=1 and r.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        <include refid="weekCommonGroupBy"/>,
        <include refid="whereGroupArea"/>
        ORDER BY
        year,
        week,
        processNewCnt DESC
    </select>
    <select id="meadowAreaChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT *
        FROM (
        SELECT
        <include refid="tenDaysDimSelectColumns"/>,
        <include refid="selectGroupArea"/>
        <include refid="sumCnt"/>
        FROM
        <!--ads.ads_ms_dyqw_infect_report r-->
        ads.ads_rep_infect_report_info r
        <include refid="joinTenDaysDim"/>
        WHERE 1=1 and r.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        <include refid="tenDayCommonGroupBy"/>,
        <include refid="whereGroupArea"/>
        ) t
        ORDER BY
        year,
        month,
        tenDay,
        processNewCnt DESC
    </select>
    <select id="monthAreaChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT *
        FROM (
        SELECT
        <include refid="monthDimSelectColumns"/>,
        <include refid="selectGroupArea"/>
        <include refid="sumCnt"/>
        FROM
        <!--ads.ads_ms_dyqw_infect_report r-->
        ads.ads_rep_infect_report_info r
        <include refid="joinMonthDim"/>
        WHERE 1=1 and r.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        <include refid="monthCommonGroupBy"/>,
        <include refid="whereGroupArea"/>
        ) t
        ORDER BY
        year,
        month,
        processNewCnt DESC
    </select>
    <select id="quarterAreaChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT *
        FROM (
        SELECT
        <include refid="quarterDimSelectColumns"/>,
        <include refid="selectGroupArea"/>
        <include refid="sumCnt"/>
        FROM
       <!-- ads.ads_ms_dyqw_infect_report r-->
        ads.ads_rep_infect_report_info r
        <include refid="joinQuarterDim"/>
        WHERE 1=1 and r.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        <include refid="quarterCommonGroupBy"/>,
        <include refid="whereGroupArea"/>
        ) t
        ORDER BY
        year,
        quarter,
        processNewCnt DESC
    </select>
    <select id="yearAreaChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT *
        FROM (
        SELECT
        <include refid="yearDimSelectColumns"/>,
        <include refid="selectGroupArea"/>
        <include refid="sumCnt"/>
        FROM
       <!-- ads.ads_ms_dyqw_infect_report r-->
        ads.ads_rep_infect_report_info r
        <include refid="joinYearDim"/>
        WHERE 1=1 and r.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)

        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        year
        <if test="'${whereGroupArea}' != null and '${whereGroupArea}' != ''">
            , <include refid="whereGroupArea"/>
        </if>
        ) t
        ORDER BY
        year,
        processNewCnt DESC
    </select>
    <select id="daySexChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT *
        FROM (
        SELECT
        patient_sex_name,
        patient_sex_name as description,
        <include refid="dynamicDayField"/> AS statDate,
        <include refid="sumCnt"/>
        FROM
       <!-- ads.ads_ms_dyqw_infect_report-->
        ads.ads_rep_infect_report_info
        WHERE 1=1 and source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        <include refid="dynamicDayField"/>,
        patient_sex_name
        ) t
        ORDER BY
        statDate,
        processNewCnt DESC
    </select>
    <select id="weekSexChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT *
        FROM (
        SELECT
        <include refid="weekDimSelectColumns"/>,
        patient_sex_name,
        patient_sex_name AS description,
        <include refid="sumCnt"/>
        FROM
        <!--ads.ads_ms_dyqw_infect_report r-->
        ads.ads_rep_infect_report_info r
        <include refid="joinWeekDim"/>
        WHERE 1=1 and r.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        <include refid="weekCommonGroupBy"/>,
        patient_sex_name
        ) t
        ORDER BY
        year,
        week,
        processNewCnt DESC
    </select>
    <select id="meadowSexChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="tenDaysDimSelectColumns"/>,
        patient_sex_name,
        patient_sex_name AS description,
        <include refid="sumCnt"/>
        FROM
        <!--ads.ads_ms_dyqw_infect_report r-->
        ads.ads_rep_infect_report_info r
        <include refid="joinTenDaysDim"/>
        WHERE 1=1 and r.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        patient_sex_name,
        <include refid="tenDayCommonGroupBy"/>
        ORDER BY
        year, month, tenDay, processNewCnt DESC
    </select>
    <select id="monthSexChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="monthDimSelectColumns"/>,
        patient_sex_name,
        patient_sex_name AS description,
        <include refid="sumCnt"/>
        FROM
       <!-- ads.ads_ms_dyqw_infect_report r-->
        ads.ads_rep_infect_report_info r
        <include refid="joinMonthDim"/>
        WHERE 1=1 and r.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        <include refid="monthCommonGroupBy"/>,
        patient_sex_name
        ORDER BY
        year,
        month,
        processNewCnt DESC
    </select>
    <select id="quarterSexChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="quarterDimSelectColumns"/>,
        patient_sex_name,
        patient_sex_name AS description,
        <include refid="sumCnt"/>
        FROM
       <!-- ads.ads_ms_dyqw_infect_report r-->
        ads.ads_rep_infect_report_info r
        <include refid="joinQuarterDim"/>
        WHERE 1=1 and r.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        <include refid="quarterCommonGroupBy"/>,
        patient_sex_name
        ORDER BY
        year,
        quarter,
        processNewCnt DESC
    </select>
    <select id="yearSexChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="yearDimSelectColumns"/>,
        patient_sex_name,
        patient_sex_name as description,
        <include refid="sumCnt"/>
        FROM
       <!-- ads.ads_ms_dyqw_infect_report r-->
        ads.ads_rep_infect_report_info r
        <include refid="joinYearDim"/>
        WHERE 1=1 and r.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        year,
        patient_sex_name
        ORDER BY
        year,
        processNewCnt DESC
    </select>
    <select id="dayAgeChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="selectAgeGroup"/>,
        ag.value_down AS ageOrder,
        <include refid="dynamicDayField"/> AS statDate,
        <include refid="sumCnt"/>
        FROM
        <include refid="fromWithAgeGroup"/>
        WHERE 1=1 and b.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        <include refid="dynamicDayField"/>,
        ag.value_code,
        ag.value_name,
        ag.value_down
        ORDER BY
        <include refid="dynamicDayField"/>,
        ageOrder,
        processNewCnt DESC
    </select>
    <select id="weekAgeChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="weekDimSelectColumns"/>,
        <include refid="selectAgeGroup"/>,
        ag.value_down AS ageOrder,
        <include refid="sumCnt"/>
        FROM
        <include refid="fromWithAgeGroup"/>
        <include refid="joinWeekDim"/>
        WHERE 1=1 and b.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        <include refid="weekCommonGroupBy"/>,
        ag.value_code,
        ag.value_name,
        ag.value_down
        ORDER BY
        year,
        week,
        ageOrder,
        processNewCnt DESC
    </select>
    <select id="meadowAgeChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="tenDaysDimSelectColumns"/>,
        <include refid="selectAgeGroup"/>,
        ag.value_down AS ageOrder,
        <include refid="sumCnt"/>
        FROM
        <include refid="fromWithAgeGroup"/>
        <include refid="joinTenDaysDim"/>
        WHERE 1=1 and b.source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        <include refid="tenDayCommonGroupBy"/>,
        ag.value_code,
        ag.value_name,
        ag.value_down
        ORDER BY
        year,
        month,
        tenDay,
        ageOrder,
        processNewCnt DESC
    </select>

    <select id="monthAgeChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="monthDimSelectColumns"/>,
        <include refid="selectAgeGroup"/>,
        ag.value_down AS ageOrder,
        <include refid="sumCnt"/>
        FROM
        <include refid="fromWithAgeGroup"/>
        <include refid="joinMonthDim"/>
        WHERE 1=1 and b.source_id = '11'
        AND <include refid="dynamicDayField"/> BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        <include refid="monthCommonGroupBy"/>,
        ag.value_code,
        ag.value_name,
        ag.value_down
        ORDER BY
        year,
        month,
        ageOrder,
        processNewCnt DESC
    </select>

    <select id="quarterAgeChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="quarterDimSelectColumns"/>,
        <include refid="selectAgeGroup"/>,
        ag.value_down AS ageOrder,
        <include refid="sumCnt"/>
        FROM
        <include refid="fromWithAgeGroup"/>
        <include refid="joinQuarterDim"/>
        WHERE 1=1 and b.source_id = '11'
        AND <include refid="dynamicDayField"/> BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        <include refid="quarterCommonGroupBy"/>,
        ag.value_code,
        ag.value_name,
        ag.value_down
        ORDER BY
        year,
        quarter,
        ageOrder,
        processNewCnt DESC
    </select>

    <select id="yearAgeChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="yearDimSelectColumns"/>,
        <include refid="selectAgeGroup"/>,
        ag.value_down AS ageOrder,
        <include refid="sumCnt"/>
        FROM
        <include refid="fromWithAgeGroup"/>
        <include refid="joinYearDim"/>
        WHERE 1=1 and  b.source_id = '11'
        AND <include refid="dynamicDayField"/> BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        year,
        ag.value_code,
        ag.value_name,
        ag.value_down
        ORDER BY
        year,
        ageOrder,
        processNewCnt DESC
    </select>
    <select id="dayQOQTrendChart" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        day as statDate,
        COALESCE(sum(process_new_cnt),0)  as processNewCnt
        from
        <include refid="dayDb"/>
        where day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose" />
        group by day
        order by day
    </select>
    <select id="weekQOQTrendChart" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year,
        week,
        CONCAT(year, '年', week, '周') as statDate,
        COALESCE(sum(process_new_cnt),0)  as processNewCnt
        from
        <include refid="weekDb"/>
        where 1=1 and monday between CAST(#{startDate} AS DATE) and CAST(#{endDate} as DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, week
        order by year, week
    </select>

    <select id="meadowQOQTrendChart" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year , month , ten_day,
        CONCAT(year, '年', month, '月', ten_day, '旬') as statDate,
        COALESCE(sum(process_new_cnt),0)  as processNewCnt
        from
        <include refid="tenDaysDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month} and ten_day = #{item.meadow})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose" />
        group by year , month , ten_day
        order by year , month , ten_day
    </select>


    <select id="monthQOQTrendChart" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, month,
        CONCAT(year, '年', month, '月') as statDate,
        COALESCE(sum(process_new_cnt),0)  as processNewCnt
        from
        <include refid="monthDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose" />
        group by year,month
        order by year,month
    </select>

    <select id="quarterQOQTrendChart" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year,quarter,
        CONCAT(year, '年第', quarter, '季度') as statDate,
        COALESCE(sum(process_new_cnt),0)  as processNewCnt
        from
        <include refid="quarterDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and quarter = #{item.quarter})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose" />
        group by year, quarter
        order by year, quarter
    </select>

    <select id="yearQOQTrendChart" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year,
        CONCAT(year, '年') as statDate,
        COALESCE(sum(process_new_cnt),0)  as processNewCnt
        from
        <include refid="yearDb"/>
        where year in
        <foreach collection="dateDims" open="(" close=")" separator="," item="item">
            #{item.year}
        </foreach>
        <include refid="whereDistributionAreaMultiChoose" />
        group by year
        order by year
    </select>
    <select id="groupDiseaseName" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        infect_code as descriptionCode,
        infect_name as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistribution" />
        group by infect_code, infect_name order by infect_code, infect_name
    </select>
    <select id="groupIdentifyClass" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        identify_class,
        identify_class as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        and identify_class is not null
        <include refid="whereDistributionAreaMultiChoose" />
        group by identify_class order by identify_class
    </select>
    <select id="groupOutcomeStatus" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        outcome_status,
        outcome_status as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and cast(#{endDate} AS DATE)
        and outcome_status is not null
        <include refid="whereDistributionAreaMultiChoose" />
        group by outcome_status order by outcome_status
    </select>
    <select id="groupHistoryBefore" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        history_before,
        history_before as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        and history_before is not null
        <include refid="whereDistributionAreaMultiChoose" />
        group by history_before order by processNewCnt desc
    </select>
    <select id="groupPathogenResNominal" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        pathogen_res_nominal,
        pathogen_res_nominal as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        and pathogen_res_nominal is not null
        <include refid="whereDistributionAreaMultiChoose" />
        group by pathogen_res_nominal order by pathogen_res_nominal
    </select>
    <select id="groupSymptomFirst" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        symptom_first,
        symptom_first as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        and symptom_first is not null
        <include refid="whereDistributionAreaMultiChoose" />
        group by symptom_first order by processNewCnt desc
    </select>
    <select id="groupAddrType" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        <if test="'orgAddress' == addressType ">
            report_addr_type  description,
        </if>
        <if test="'livingAddress' == addressType ">
            living_addr_type  description,
        </if>
        <include refid="sumCnt"/>
        FROM
      <!--  ads.ads_ms_dyqw_infect_report-->
        ads.ads_rep_infect_report_info
        WHERE 1=1 and source_id = '11'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose" />
        group by
        <if test="'orgAddress' == addressType ">
            report_addr_type
        </if>
        <if test="'livingAddress' == addressType ">
            living_addr_type
        </if>
    </select>

    <sql id="sumCommonCnt">
        COALESCE(sum(process_new_cnt) , 0) as processNewCnt ,
        COALESCE(sum(process_new_cnt_last) , 0)as processNewCntLast ,
        COALESCE(sum(process_new_cnt_last_y), 0) as processNewCntLastY,
        COALESCE(sum(process_new_cnt_avg_s) , 0)as processNewCntAvgS,
        COALESCE(sum(process_new_cnt_pre), 0)  as processNewCntPre,

        COALESCE(sum(process_dead_cnt),0) as processDeadCnt,
        COALESCE(sum(process_dead_cnt_avg_s) , 0)as processDeadCntAvgS,
        COALESCE(sum(process_dead_cnt_last), 0)  as  processDeadCntLast,
        COALESCE(sum(process_dead_cnt_last_y), 0) as processDeadCntLastY,

        COALESCE(sum(process_now_cnt),0) as processNowCnt,
        COALESCE(sum(process_now_cnt_avg_s) , 0)as processNowCntAvgS,
        COALESCE(sum(process_now_cnt_last), 0)  as  processNowCntLast,
        COALESCE(sum(process_now_cnt_last_y), 0) as processNowCntLastY,

        COALESCE(sum(process_dead_illness_cnt),0) as processDeadIllnessCnt,
        COALESCE(sum(process_dead_illness_cnt_avg_s) , 0)as processDeadIllnessCntAvgS,
        COALESCE(sum(process_dead_illness_cnt_last), 0)  as  processDeadIllnessCntLast,
        COALESCE(sum(process_dead_illness_cnt_last_y), 0) as processDeadIllnessCntLastY,

        COALESCE(sum(process_cure_cnt),0) as processCureCnt,
        COALESCE(sum(process_cure_cnt_avg_s) , 0)as processCureCntAvgS,
        COALESCE(sum(process_cure_cnt_last), 0)  as  processCureCntLast,
        COALESCE(sum(process_cure_cnt_last_y), 0) as processCureCntLastY
    </sql>

    <sql id="sumCnt">
    COUNT(DISTINCT ms_infect_report_id) AS processNewCnt,
    SUM(CASE WHEN dead_datetime IS NOT NULL THEN 1 ELSE 0 END) AS processDeadCnt,
    0 AS processNowCnt,
    SUM(CASE WHEN dead_by_this_flag = '1' THEN 1 ELSE 0 END) AS processDeadIllnessCnt,
    0 AS processCureCnt
    </sql>

    <sql id="whereDistributionAreaMultiChoose">
        <if test="infectType != null and infectType != ''">
            and infect_type_name = #{infectType}
        </if>
        <if test="infectTransmitType != null and infectTransmitType != ''">
            and (
            select c.transmission_type_name
           <!-- from ads.ads_ms_dyqw_infect_report b-->
            from ads.ads_rep_infect_report_info b
            left join (
            select infected_sub_id, transmission_type_name
            from dim.dim_infected_info
            ) c
            on b.infect_code = c.infected_sub_id
            where  b.source_id = '11'
            ) = #{infectTransmitType}
        </if>
        <if test="infectCodeSubtypeList != null and infectCodeSubtypeList.size() > 0">
            and infect_code in
            <foreach collection="infectCodeSubtypeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test = "filterCodes != null and filterCodes.size() > 0">
            and infect_code not in
            <foreach collection="filterCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            and infect_name = #{diseaseName}
        </if>
        <include refid="areaMultiChoose"/>
    </sql>

    <sql id="selectGroupArea">
        <if test="'orgAddress' == addressType ">
            <if test="areaLevel == 1">
                report_addr_city_code  area_code,
                report_addr_city_name  area_name,
                report_addr_city_name  description,
                2 as areaLevel,
            </if>
            <if test="areaLevel == 2">
                report_addr_func_district_code  area_code,
                report_addr_func_district_name  area_name,
                report_addr_func_district_name  description,
                3 as areaLevel,
            </if>
            <if test="areaLevel == 3">
                report_addr_street_code  area_code,
                report_addr_street_name  area_name,
                report_addr_street_name  description,
                4 as areaLevel,
            </if>
        </if>

        <if test="'livingAddress' == addressType ">
            <if test="areaLevel == 1">
                living_addr_city_code  area_code,
                living_addr_city_name  area_name,
                living_addr_city_name  description,
                2 as areaLevel,
            </if>
            <if test="areaLevel == 2">
                living_addr_func_district_code  area_code,
                living_addr_func_district_name  area_name,
                living_addr_func_district_name  description,
                3 as areaLevel,
            </if>
            <if test="areaLevel == 3">
                living_addr_street_code  area_code,
                living_addr_street_name  area_name,
                living_addr_street_name  description,
                4 as areaLevel,
            </if>
        </if>
    </sql>
    <sql id="whereGroupArea">
        <if test="'orgAddress' == addressType ">
            <if test="areaLevel == 1">
                report_addr_city_code,
                report_addr_city_name
            </if>
            <if test="areaLevel == 2">
                report_addr_func_district_code  ,
                report_addr_func_district_name
            </if>
            <if test="areaLevel == 3">
                report_addr_street_code ,
                report_addr_street_name
            </if>
        </if>

        <if test="'livingAddress' == addressType ">
            <if test="areaLevel == 1">
                living_addr_city_code  ,
                living_addr_city_name
            </if>
            <if test="areaLevel == 2">
                living_addr_func_district_code  ,
                living_addr_func_district_name
            </if>
            <if test="areaLevel == 3">
                living_addr_street_code ,
                living_addr_street_name
            </if>
        </if>
    </sql>

    <sql id="selectGroupAreaCurrentLevel">
        <if test="'orgAddress' == addressType ">
            <if test="areaLevel == 1">
                report_addr_province_code  area_code,
                report_addr_province_name  area_name,
                report_addr_province_name  description,
                1 as areaLevel,
            </if>
            <if test="areaLevel == 2">
                report_addr_city_code  area_code,
                report_addr_city_name  area_name,
                report_addr_city_name  description,
                2 as areaLevel,
            </if>
            <if test="areaLevel == 3">
                report_addr_func_district_code  area_code,
                report_addr_func_district_name  area_name,
                report_addr_func_district_name  description,
                3 as areaLevel,
            </if>
        </if>
        <if test="'livingAddress' == addressType ">
            <if test="areaLevel == 1">
                living_addr_province_code  area_code,
                report_addr_province_name  area_name,
                report_addr_province_name  description,
                1 as areaLevel,
            </if>
            <if test="areaLevel == 2">
                living_addr_city_code  area_code,
                living_addr_city_name  area_name,
                living_addr_city_name  description,
                2 as areaLevel,
            </if>
            <if test="areaLevel == 3">
                living_addr_func_district_code  area_code,
                living_addr_func_district_name  area_name,
                living_addr_func_district_name  description,
                3 as areaLevel,
            </if>
        </if>
    </sql>

    <sql id="whereGroupAreaCurrentLevel">
        <if test="'orgAddress' == addressType ">
            <if test="areaLevel == 1">
                report_addr_province_code,
                report_addr_province_name
            </if>
            <if test="areaLevel == 2">
                report_addr_city_code,
                report_addr_city_name
            </if>
            <if test="areaLevel == 3">
                report_addr_func_district_code  ,
                report_addr_func_district_name
            </if>
        </if>

        <if test="'livingAddress' == addressType ">
            <if test="areaLevel == 1">
                living_addr_province_code  ,
                report_addr_province_name
            </if>
            <if test="areaLevel == 2">
                living_addr_city_code  ,
                living_addr_city_name
            </if>
            <if test="areaLevel == 3">
                living_addr_func_district_code  ,
                living_addr_func_district_name
            </if>
        </if>
    </sql>

    <sql id="whereDistribution">
        <if test="infectType != null and infectType != ''">
            and infect_type = #{infectType}
        </if>
        <if test="infectTransmitType != null and infectTransmitType != ''">
            and infect_transmit_type = #{infectTransmitType}
        </if>
        <if test="infectCode != null and infectCode != ''">
            and infect_code = #{infectCode}
        </if>
        <if test = "filterCodes != null and filterCodes.size() > 0">
            and infect_code not in
            <foreach collection="filterCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            and infect_name = #{diseaseName}
        </if>
        <include refid="areaChoose"/>
    </sql>

    <sql id="dynamicDayField">
        <choose>
            <when test="timeType == 'approveTime'">
                CAST(valid_time_province AS DATE)
            </when>
            <when test="timeType == 'onsetTime'">
                CAST(onset_datetime AS DATE)
            </when>
            <when test="timeType == 'deathTime'">
                CAST(dead_datetime AS DATE)
            </when>
            <otherwise>
                CAST(report_datetime AS DATE)
            </otherwise>
        </choose>
    </sql>

    <sql id="tenDayCommonGroupBy">
        year, month, ten_days, ten_days_desc
    </sql>

    <sql id="selectAgeGroup">
        CASE
        WHEN ag.value_code IS NOT NULL THEN ag.value_name
        ELSE '不详'
    END AS patient_age_group,
    CASE
        WHEN ag.value_code IS NOT NULL THEN ag.value_name
        ELSE '不详'
    END AS description
    </sql>

    <sql id="fromWithAgeGroup">
       <!-- ads.ads_ms_dyqw_infect_report b-->
        ads.ads_rep_infect_report_info b
    LEFT JOIN dim.dim_age_group ag
        ON b.age &gt;= ag.value_down
        AND b.age &lt; ag.value_up

    </sql>

    <!-- 季度分组 -->
    <sql id="quarterCommonGroupBy">
        year, quarter, quarter_desc
    </sql>

    <!-- 周维度分组字段 -->
    <sql id="weekCommonGroupBy">
        year, week, week_desc
    </sql>

    <!-- 月维度 GroupBy 字段 -->
    <sql id="monthCommonGroupBy">
        year, month, month_desc
    </sql>

    <select id="findHalfYearDistribution" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        d.half_year_desc AS description,
        <include refid="sumCnt"/>
        FROM
        <!--ads.ads_ms_dyqw_infect_report-->
        ads.ads_rep_infect_report_info
        join dim.dim_date_day d
        on <include refid="dynamicDayField"/> = d.date_day
        where 1=1 AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        and source_id = '11'
        GROUP BY
        description
        order by description
    </select>

    <!-- 月度维表选择列 -->
    <sql id="monthDimSelectColumns">
        d.month_desc AS description,
    d.year,
    d.month
    </sql>

    <!-- 月度维表分组与排序 -->
    <sql id="monthDimGroupOrder">
        GROUP BY
        d.year, d.month, d.month_desc
    ORDER BY
        d.year DESC,
        d.month DESC
    </sql>

    <!-- 公共部分：月度维度关联 -->
    <sql id="joinMonthDim">
        JOIN dim.dim_date_month d
        ON <include refid="dynamicDayField"/>
        BETWEEN d.start_time AND d.end_time
    </sql>

    <sql id="quarterDimSelectColumns">
        d.quarter_desc AS description,
    d.year,
    d.quarter
    </sql>

    <sql id="joinQuarterDim">
        JOIN dim.dim_date_quarter d
        ON
        <include refid="dynamicDayField"/>
        BETWEEN d.start_time AND d.end_time
    </sql>

    <sql id="quarterDimGroupOrder">
        GROUP BY
        d.year, d.quarter, d.quarter_desc
    ORDER BY
        d.year DESC,
        d.quarter DESC
    </sql>

    <sql id="joinTenDaysDim">
        JOIN dim.dim_date_ten_days d
        ON
        <include refid="dynamicDayField"/>
        BETWEEN d.start_time AND d.end_time
    </sql>

    <sql id="tenDaysDimSelectColumns">
        d.ten_days_desc AS statDate,
    d.year,
    d.month,
    d.ten_days as tenDay
    </sql>

    <sql id="tenDaysDimGroupOrder">
        GROUP BY
        d.year, d.month, d.ten_days, d.ten_days_desc
    ORDER BY
        d.year,
        d.month,
        d.ten_days
    </sql>

    <sql id="joinWeekDim">
        JOIN dim.dim_date_week d
        ON
        <include refid="dynamicDayField"/>
        BETWEEN d.start_time AND d.end_time
    </sql>

    <sql id="weekDimSelectColumns">
        d.week_desc AS statDate,
    d.year,
    d.week
    </sql>

    <sql id="weekDimGroupOrder">
        GROUP BY
        d.year, d.week, d.week_desc
    ORDER BY
        d.year,
        d.week
    </sql>

    <sql id="joinYearDim">
        JOIN dim.dim_date_month d
        ON
        <include refid="dynamicDayField"/>
        BETWEEN d.start_time AND d.end_time
    </sql>

    <sql id="yearDimSelectColumns">
        CONCAT(d.year, '年') AS description,
    d.year
    </sql>

    <sql id="yearDimGroupOrder">
        GROUP BY
        d.year
    ORDER BY
        d.year DESC
    </sql>

</mapper>