<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.reportcard.mapper.pg.ReportUploadRecordMapper">

    <sql id = "dyqw_Base_Column_List">
        t1.ms_infect_report_id as id,
        null as attachmentId,
        t1.card_id as reportCardId,
        t1.card_status as status,
        t1.patient_name as name,
        t1.patient_contact_name as parentName,
        t1.patient_identity_type as validCertType,
        t1.patient_identity_no as validCertNumber,
        t1.company as company,
        t1.patient_phone as phone,
        t1.addr_belong_type as attribution,
        t1.living_addr_detail_std as addressCode,
        t1.living_addr_detail as addressName,
        t1.nultitude_type_name as humanCategory,
        t1.cases_type_name as casesCategory,
        null as casesCategory2,
        t1.onset_datetime as onsetDate,
        t1.diag_datetime as diagnoseTime,
        t1.dead_datetime as deathDate,
        t1.infect_name as diseaseName,
        t1.infect_code as diseaseCode,
        t1.audit_disease_name as revisedPreviousDisease,
        null as revisedPreviousDiagnoseTime,
        null as revisedPreviousCheckTime,
        t1.fill_doc_id as fillDoctor,
        t1.card_fill_date as fillDate,
        t1.report_org_code as unitCode,
        t1.report_org_name as unitName,
        null as unitType,
        TO_CHAR(t1.report_datetime, 'YYYY-MM-DD HH24:MI:SS') as recordTime,
        t1.fill_doc_id as recordUser,
        null as recordUserCompany,
        t1.valid_time_district as districtCheckTime,
        t1.valid_time_city as cityCheckTime,
        t1.valid_time_province as provinceCheckTime,
        t1.valid_flag as checkStatus,
        t1.audit_create_date as revisedReportTime,
        audit_create_date as revisedFinalCheckTime,
        dead_valid_time as finalCheckDeathTime,
        null as revisedUser,
        null as revisedUserCompany,
        t1.delete_time as deleteTime,
        t1.delete_user_name as deleteUser,
        null as deleteUserCompany,
        t1.deleting_reason_details as deleteReason,
        t1.notes as remark,
        TO_CHAR(t1.etl_create_datetime, 'YYYY-MM-DD HH24:MI:SS') as createTime,
        TO_CHAR(t1.etl_update_datetime, 'YYYY-MM-DD HH24:MI:SS') as updateTime,
        null as creatorId,
        null as creator,
        t1.empi_id as globalPersonId,
        t1.card_code as reportCardCode,
        t1.patient_sex_name as sexDesc,
        t1.patient_birth_day as birthday,
        t1.age as age,
        t1.age_unit as ageUnit,
        t1.living_addr_province_code as provinceCode,
        t1.living_addr_province_name as provinceName,
        t1.living_addr_city_code as cityCode,
        t1.living_addr_city_name as cityName,
        t1.living_addr_district_code as districtCode,
        t1.living_addr_district_name as districtName,
        null as reportClass,
        t1.report_addr_province_code as reportOrgAddrProvinceCode,
        t1.report_addr_province_name as reportOrgAddrProvince,
        t1.report_addr_city_code as reportOrgAddrCityCode,
        t1.report_addr_city_name as reportOrgAddrCity,
        t1.report_addr_district_code as reportOrgAddrDistrictCode,
        t1.report_addr_district_name as reportOrgAddrDistrict,
        t1.life_id as lifeId,
        t1.archive_id as archiveId
    </sql>
    <sql id="dyqw_Base_Column_List_HN">
        ,
        t1.card_delete_flag as cardDeleteFlag,
        t1.card_delete_remark as cardDeleteRemark,
        t1.card_exclude_flag as cardExcludeFlag,
        t1.card_exclude_remark as cardExcludeRemark
    </sql>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t1.id,t1.attachment_id,t1.report_card_id,t1.status,t1.name,t1.parent_name,t1.valid_cert_type,t1.valid_cert_number,t1.company,
        t1.phone,t1.attribution,t1.address_code,t1.address_name,t1.human_category,t1.cases_category,t1.cases_category2,t1.onset_date,
        t1.diagnose_time,t1.death_date,t1.disease_name,t1.disease_code,t1.revised_previous_disease,t1.revised_previous_diagnose_time,
        t1.revised_previous_check_time,t1.fill_doctor,t1.fill_date,t1.unit_code,t1.unit_name,t1.unit_type,t1.record_time,t1.record_user,
        t1.record_user_company,t1.district_check_time,t1.city_check_time,t1.province_check_time,t1.check_status,
        t1.revised_report_time,t1.revised_final_check_time,t1.final_check_death_time,t1.revised_user,t1.revised_user_company,
        t1.delete_time,t1.delete_user,t1.delete_user_company,t1.delete_reason,t1.remark,t1.create_time,t1.update_time,t1.creator_id,
        t1.creator,t1.global_person_id,t1.report_card_code,t1.sex_desc,t1.birthday,t1.age,t1.age_unit,t1.province_code,t1.province_name,
        t1.city_code,t1.city_name,t1.district_code,t1.district_name,t1.report_class,report_org_addr_province_code,
        report_org_addr_province,report_org_addr_city_code,report_org_addr_city,report_org_addr_district_code,report_org_addr_district,
        t1.life_id,t1.archive_id
    </sql>

    <!-- 时间字段映射配置 -->
    <sql id="time_field_mapping">
        <choose>
            <when test="timeType == 1">t1.diag_datetime</when>
            <when test="timeType == 2">t1.card_fill_date</when>
            <when test="timeType == 3">t1.report_datetime</when>
            <when test="timeType == 4">t1.onset_datetime</when>
            <when test="timeType == 5">t1.dead_valid_time</when>
            <when test="timeType == 6">t1.audit_create_date</when>
            <when test="timeType == 7">t1.dead_datetime</when>
        </choose>
    </sql>

    <sql id="time_type_choose">
        <if test="timeTypeList != null and timeTypeList.size() > 0 and (startDate != null or endDate != null)">
            and (
            <foreach collection="timeTypeList" item="timeType" index="index" open="" separator="and" close="">
                <if test="startDate != null or endDate != null">
                    <if test="startDate != null">
                        <include refid="time_field_mapping"/> >= #{startDate}
                    </if>
                    <if test="startDate != null and endDate != null"> and </if>
                    <if test="endDate != null">
                        <include refid="time_field_mapping"/> &lt; #{endDate}
                    </if>
                </if>
            </foreach>
            )
        </if>
    </sql>
    <sql id="type_of_card_choose">
       and patient_identity_type in
        <foreach collection="typeOfCardList" item="typeOfCard" open="(" separator="," close=")">
            #{typeOfCard}
        </foreach>
    </sql>
    <sql id="review_status_choose">
        and (
        <foreach collection="reviewStatusList" item="reviewStatus" index="index" open="" separator="or" close="">
            <choose>
            <when test="reviewStatus == '未审核'">
                (t1.valid_flag not IN ('省级审核/终审', '已删除') or t1.valid_flag is null or valid_flag='')
            </when>
            <when test="reviewStatus == '已审核'">
                 t1.valid_flag = '省级审核/终审'
            </when>
            <when test="reviewStatus == '已删除'">
                  t1.valid_flag = '已删除'
            </when>
            </choose>
        </foreach>
        )
    </sql>


    <select id="findDyqwList" resultType="com.iflytek.cdc.reportcard.vo.ReportUploadRecordVO">
        select
        <include refid="dyqw_Base_Column_List"/>
        <if test="listDisplayType != null ">
            <if test="listDisplayType != 0">
                <include refid="dyqw_Base_Column_List_HN"/>
            </if>
        </if>
        <!--from ads.ads_ms_dyqw_infect_report  t1-->
        from ads.ads_rep_infect_report_info t1
        join dim.dim_infected_info dii on t1.infect_code = dii.id and delete_flag = '0'
        where 1=1
        and t1.source_id = '11'
        <if test="listDisplayType != null ">
            <if test="listDisplayType == 1">
                and t1.card_delete_flag = '0'
            </if>
            <if test="listDisplayType == 2">
                and t1.card_delete_flag = '1'
            </if>
            <if test="listDisplayType == 3">
                and t1.card_exclude_flag = '1'
            </if>
        </if>
        <if test="casesCategory != null and casesCategory!=''">
            and t1.cases_type_name = #{casesCategory}
        </if>
        <if test="attribution != null and attribution!=''">
            and t1.addr_belong_type = #{attribution}
        </if>

        <if test="status != null and status!=''">
            and t1.card_status = #{status}
        </if>
        <if test="validCertNumber != null and validCertNumber!=''">
            and t1.patient_identity_no like concat('%',#{validCertNumber},'%')
        </if>
        <if test="diseaseName != null and diseaseName!=''">
            and t1.infect_name like concat('%',#{diseaseName},'%')
        </if>
        <if test="reportCardId != null and reportCardId!=''">
            and t1.card_id like concat('%',#{reportCardId},'%')
        </if>
        <if test="name != null and name!=''">
            and t1.patient_name like concat('%',#{name},'%')
        </if>
        <if test="unitName != null and unitName!=''">
            and t1.report_org_name like concat('%',#{unitName},'%')
        </if>
        <if test="queryKey!= null and queryKey!=''">
            and (t1.patient_name like concat('%',#{queryKey},'%')
            or t1.card_id like concat('%',#{queryKey},'%')
            or t1.infect_name like concat('%',#{queryKey},'%')
            or t1.patient_identity_no like concat('%',#{queryKey},'%')
            or t1.notes like concat('%',#{queryKey},'%')
            or t1.report_org_name like concat('%',#{queryKey},'%')
            or t1.living_addr_detail like concat('%',#{queryKey},'%')
            )
        </if>
        <if test="addressName != null and addressName!=''">
            and t1.living_addr_detail like concat('%',#{addressName},'%')
        </if>
        <choose>
            <when test="timeType == 1">
                <if test="startDate != null">
                    and CAST(t1.diag_datetime as DATE) >= #{startDate}
                </if>
                <if test="endDate != null">
                    and CAST(t1.diag_datetime as DATE) &lt;= #{endDate}
                </if>
            </when>
            <when test="timeType == 2">
                <if test="startDate != null">
                    and CAST(t1.card_fill_date as DATE) >= #{startDate}
                </if>
                <if test="endDate != null">
                    and CAST(t1.card_fill_date as DATE) &lt;= #{endDate}
                </if>
            </when>
            <when test="timeType == 4">
                <if test="startDate != null">
                    and CAST(t1.onset_datetime as DATE) >= #{startDate}
                </if>
                <if test="endDate != null">
                    and CAST(t1.onset_datetime as DATE) &lt;= #{endDate}
                </if>
            </when>

        </choose>

        <if test="infectCodeList !=null and infectCodeList.size() &gt; 0">
            and (dii.id in
            <foreach item="item" index="index" collection="infectCodeList"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            or dii.infected_id in
            <foreach item="item" index="index" collection="infectCodeList"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
        <if test="infectTypeList !=null and infectTypeList.size() &gt; 0">
            and dii.infected_type_code in
            <foreach item="item" index="index" collection="infectTypeList"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>

        <if test="reportCardDataType != 0">
            <include refid="reportCardDataTypeDyqw"/>
        </if>
        <if test="reportCardDataType == 0">
            <if test="provinceCode != null and provinceCode!=''">
                and t1.living_addr_province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode!=''">
                and t1.living_addr_city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode!=''">
                and t1.living_addr_district_code = #{districtCode}
            </if>
            <if test="reportOrgAddrProvinceCode != null and reportOrgAddrProvinceCode!=''">
                and t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
            </if>
            <if test="reportOrgAddrCityCode != null and reportOrgAddrCityCode!=''">
                and t1.report_addr_city_code = #{reportOrgAddrCityCode}
            </if>
            <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                and t1.report_addr_district_code = #{reportOrgAddrDistrictCode}
            </if>
        </if>
        order by t1.report_datetime desc
    </select>

    <select id="findList" resultType="com.iflytek.cdc.reportcard.vo.ReportUploadRecordVO">
        select
        <include refid="Base_Column_List"/>
        from ads.ads_report_upload_record  t1
        join dim.dim_infected_info dii on t1.disease_code = dii.id and delete_flag = '0'
        where 1=1

        <if test="casesCategory != null and casesCategory!=''">
            and t1.cases_category = #{casesCategory}
        </if>
        <if test="attribution != null and attribution!=''">
            and t1.attribution = #{attribution}
        </if>

        <if test="status != null and status!=''">
            and t1.status = #{status}
        </if>
        <if test="checkStatus != null and checkStatus!=''">
            and t1.check_status = #{checkStatus}
        </if>
        <if test="reportCardId != null and reportCardId!=''">
            and t1.report_card_id like concat('%',#{reportCardId},'%')
        </if>
        <if test="name != null and name!=''">
            and t1.name like concat('%',#{name},'%')
        </if>
        <if test="unitName != null and unitName!=''">
            and t1.unit_name like concat('%',#{unitName},'%')
        </if>
        <if test="queryKey!= null and queryKey!=''">
            and (t1.name like concat('%',#{queryKey},'%')
            or t1.report_card_id like concat('%',#{queryKey},'%') )
        </if>
        <if test="addressName != null and addressName!=''">
            and t1.address_name like concat('%',#{addressName},'%')
        </if>
        <choose>
            <when test="timeType == 1">
                <if test="startDate != null">
                    and t1.diagnose_time AS DATE >= #{startDate}
                </if>
                <if test="endDate != null">
                    and t1.diagnose_time AS DATE &lt;= #{endDate}
                </if>
            </when>
            <when test="timeType == 2">
                <if test="startDate != null">
                    and t1.fill_date AS DATE >= #{startDate}
                </if>
                <if test="endDate != null">
                    and t1.fill_date AS DATE &lt;= #{endDate}
                </if>
            </when>
            <when test="timeType == 4">
                <if test="startDate != null">
                    and t1.onset_date AS DATE >= #{startDate}
                </if>
                <if test="endDate != null">
                    and t1.onset_date AS DATE &lt;= #{endDate}
                </if>
            </when>
            <when test="timeType == 5">
                <if test="startDate != null">
                    and t1.revised_final_check_time AS DATE >= #{startDate}
                </if>
                <if test="endDate != null">
                    and t1.revised_final_check_time AS DATE &lt;= #{endDate}
                </if>
            </when>
        </choose>

        <if test="infectCodeList !=null and infectCodeList.size() &gt; 0">
            and (dii.id in
            <foreach item="item" index="index" collection="infectCodeList"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            or dii.infected_id in
            <foreach item="item" index="index" collection="infectCodeList"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
        <if test="infectTypeList !=null and infectTypeList.size() &gt; 0">
            and dii.infected_type_code in
            <foreach item="item" index="index" collection="infectTypeList"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>

        <if test="reportCardDataType != 0">
            <include refid="reportCardDataType"/>
        </if>
        <if test="reportCardDataType == 0">
            <if test="provinceCode != null and provinceCode!=''">
                and t1.province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode!=''">
                and t1.city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode!=''">
                and t1.district_code = #{districtCode}
            </if>
            <if test="reportOrgAddrProvinceCode != null and reportOrgAddrProvinceCode!=''">
                and t1.report_org_addr_province_code = #{reportOrgAddrProvinceCode}
            </if>
            <if test="reportOrgAddrCityCode != null and reportOrgAddrCityCode!=''">
                and t1.report_org_addr_city_code = #{reportOrgAddrCityCode}
            </if>
            <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                and t1.report_org_addr_district_code = #{reportOrgAddrDistrictCode}
            </if>
        </if>
        order by t1.record_time desc
    </select>

    <sql id="reportCardDataTypeDyqw">
        <trim prefix="and">
            <if test="reportCardDataType == 1">
                <choose>
                    <when test="regionLevel == 1">
                        t1.living_addr_province_code != #{reportOrgAddrProvinceCode} and t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        <if test="provinceCode != null and provinceCode!=''">
                            and t1.living_addr_province_code = #{provinceCode}
                        </if>
                    </when>
                    <when test="regionLevel == 2">
                        t1.living_addr_city_code != #{reportOrgAddrCityCode} and t1.report_addr_city_code = #{reportOrgAddrCityCode}
                        <if test="reportOrgAddrProvinceCode != null and reportOrgAddrProvinceCode!=''">
                            and t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        </if>

                        <if test="cityCode != null and cityCode!=''">
                            and t1.living_addr_city_code = #{cityCode}
                        </if>
                    </when>
                    <when test="regionLevel == 3">
                        t1.living_addr_district_code != #{reportOrgAddrDistrictCode} and t1.report_addr_district_code = #{reportOrgAddrDistrictCode}
                        <if test="reportOrgAddrProvinceCode != null and reportOrgAddrProvinceCode!=''">
                            and t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        </if>
                        <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                            and t1.report_addr_city_code = #{reportOrgAddrCityCode}
                        </if>
                        <if test="provinceCode != null and provinceCode!=''">
                            and t1.living_addr_province_code = #{provinceCode}
                        </if>
                        <if test="cityCode != null and cityCode!=''">
                            and t1.living_addr_city_code = #{cityCode}
                        </if>
                        <if test="districtCode != null and districtCode!=''">
                            and t1.living_addr_district_code = #{districtCode}
                        </if>
                    </when>
                </choose>
            </if>
            <if test="reportCardDataType == 2">
                <choose>
                    <when test="regionLevel == 1">
                        t1.living_addr_province_code = #{provinceCode} and t1.report_addr_province_code != #{provinceCode}
                    </when>
                    <when test="regionLevel == 2">
                        t1.living_addr_city_code = #{cityCode} and t1.report_addr_city_code != #{cityCode}
                        <if test="provinceCode != null and provinceCode!=''">
                            and t1.living_addr_province_code = #{provinceCode}
                        </if>
                    </when>
                    <when test="regionLevel == 3">
                        t1.living_addr_district_code = #{districtCode} and t1.report_addr_district_code != #{districtCode}
                        <if test="provinceCode != null and provinceCode!=''">
                            and t1.living_addr_province_code = #{provinceCode}
                        </if>
                        <if test="cityCode != null and cityCode!=''">
                            and t1.living_addr_city_code = #{cityCode}
                        </if>
            </when>
                </choose>
            </if>
            <if test="reportCardDataType == 3">
                <choose>
                    <when test="regionLevel == 1">
                        t1.living_addr_province_code = #{provinceCode}
                        <if test="cityCode != null and cityCode!=''">
                            and t1.living_addr_city_code = #{cityCode}
                        </if>
                        <if test="districtCode != null and districtCode!=''">
                            and t1.living_addr_district_code = #{districtCode}
                        </if>
                    </when>
                    <when test="regionLevel == 2">
                        t1.living_addr_province_code = #{provinceCode}
                        <if test="cityCode != null and cityCode!=''">
                            and t1.living_addr_city_code = #{cityCode}
                        </if>
                        <if test="districtCode != null and districtCode!=''">
                            and t1.living_addr_district_code = #{districtCode}
                        </if>
                    </when>
                    <when test="regionLevel == 3">
                        t1.living_addr_province_code = #{provinceCode}
                        <if test="cityCode != null and cityCode!=''">
                            and t1.living_addr_city_code = #{cityCode}
                        </if>
                        <if test="districtCode != null and districtCode!=''">
                            and t1.living_addr_district_code = #{districtCode}
                        </if>
                    </when>
                </choose>
            </if>
            <if test="reportCardDataType == 4">
                <choose>
                    <when test="regionLevel == 1">
                        t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        <if test="reportOrgAddrCityCode != null and reportOrgAddrCityCode!=''">
                            and t1.report_addr_city_code = #{reportOrgAddrCityCode}
                        </if>
                    </when>
                    <when test="regionLevel == 2">
                        t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        <if test="reportOrgAddrCityCode != null and reportOrgAddrCityCode!=''">
                            and t1.report_addr_city_code = #{reportOrgAddrCityCode}
                        </if>
                        <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                            and t1.report_addr_district_code = #{reportOrgAddrDistrictCode}
                        </if>
                    </when>
                    <when test="regionLevel == 3">
                        t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        <if test="reportOrgAddrCityCode != null and reportOrgAddrCityCode!=''">
                            and t1.report_addr_city_code = #{reportOrgAddrCityCode}
                        </if>
                        <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                            and t1.report_addr_district_code = #{reportOrgAddrDistrictCode}
                        </if>
                    </when>
                </choose>
            </if>
        </trim>
    </sql>

    <sql id="reportCardDataType">
        <trim prefix="and">
            <if test="reportCardDataType == 1">
                <choose>
                    <when test="regionLevel == 1">
                        t1.province_code != #{reportOrgAddrProvinceCode} and t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        <if test="provinceCode != null and provinceCode!=''">
                            and t1.province_code = #{provinceCode}
                        </if>
                        <if test="cityCode != null and cityCode!=''">
                            and t1.city_code = #{cityCode}
                        </if>
                        <if test="districtCode != null and districtCode!=''">
                            and t1.district_code = #{districtCode}
                        </if>
                    </when>
                    <when test="regionLevel == 2">
                        t1.city_code != #{reportOrgAddrCityCode} and t1.report_org_addr_city_code = #{reportOrgAddrCityCode}
                        <if test="provinceCode != null and provinceCode!=''">
                            and t1.province_code = #{provinceCode}
                        </if>
                        <if test="cityCode != null and cityCode!=''">
                            and t1.city_code = #{cityCode}
                        </if>
                        <if test="districtCode != null and districtCode!=''">
                            and t1.district_code = #{districtCode}
                        </if>
                    </when>
                    <when test="regionLevel == 3">
                        t1.district_code != #{reportOrgAddrDistrictCode} and t1.report_org_addr_district_code = #{reportOrgAddrDistrictCode}
                        <if test="provinceCode != null and provinceCode!=''">
                            and t1.province_code = #{provinceCode}
                        </if>
                        <if test="cityCode != null and cityCode!=''">
                            and t1.city_code = #{cityCode}
                        </if>
                        <if test="districtCode != null and districtCode!=''">
                            and t1.district_code = #{districtCode}
                        </if>
                    </when>
                </choose>
            </if>
            <if test="reportCardDataType == 2">
                <choose>
                    <when test="regionLevel == 1">
                        t1.province_code = #{provinceCode} and t1.report_addr_province_code != #{provinceCode}
                        <if test="reportOrgAddrProvinceCode != null and reportOrgAddrProvinceCode!=''">
                            and t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        </if>
                        <if test="reportOrgAddrCityCode != null and reportOrgAddrCityCode!=''">
                            and t1.report_org_addr_city_code = #{reportOrgAddrCityCode}
                        </if>
                        <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                            and t1.report_org_addr_district_code = #{reportOrgAddrDistrictCode}
                        </if>
                    </when>
                    <when test="regionLevel == 2">
                        t1.city_code = #{cityCode} and t1.report_org_addr_city_code != #{cityCode}
                        <if test="reportOrgAddrProvinceCode != null and reportOrgAddrProvinceCode!=''">
                            and t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        </if>
                        <if test="reportOrgAddrCityCode != null and reportOrgAddrCityCode!=''">
                            and t1.report_org_addr_city_code = #{reportOrgAddrCityCode}
                        </if>
                        <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                            and t1.report_org_addr_district_code = #{reportOrgAddrDistrictCode}
                        </if>
                    </when>
                    <when test="regionLevel == 3">
                        t1.district_code = #{districtCode} and t1.report_org_addr_district_code != #{districtCode}
                        <if test="reportOrgAddrProvinceCode != null and reportOrgAddrProvinceCode!=''">
                            and t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
                        </if>
                        <if test="reportOrgAddrCityCode != null and reportOrgAddrCityCode!=''">
                            and t1.report_org_addr_city_code = #{reportOrgAddrCityCode}
                        </if>
                        <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                            and t1.report_org_addr_district_code = #{reportOrgAddrDistrictCode}
                        </if>
                    </when>
                </choose>
            </if>
        </trim>
    </sql>

    <select id="findByReportCardId" resultType="com.iflytek.cdc.reportcard.vo.ReportUploadRecordVO">
        select
        <include refid="Base_Column_List"/>
        from ads.ads_report_upload_record t1
        where t1.report_card_id = #{reportCardId}
        limit 1
    </select>

    <select id="queryArchiveByCertNum" resultType="com.iflytek.cdc.reportcard.vo.ReportCardArchiveVO">
        select
        a.life_id as lifeId,
        a.lv_patient_id as patientId,
        b.archive_id as archiveId
        from ads.ads_ms_life_info a
        left join ads.ads_ms_edr_person_info b on a.life_id = b.life_id
        where a.patient_identity_no = #{validCertNumber}
        order by a.etl_update_datetime desc, b.etl_update_datetime desc nulls last
        limit 1
    </select>

    <update id="updateReportCardArchive">
        update ads.ads_report_upload_record set life_id = #{reportCardArchiveVO.lifeId},
            set archive_id = #{reportCardArchiveVO.archiveId},set global_person_id = #{reportCardArchiveVO.patientId}
        where id = #{reportRecordId}
    </update>

    <select id="queryDyqwBatchIds" resultType="com.iflytek.cdc.reportcard.vo.ReportUploadRecordVO">
        select
        <include refid="dyqw_Base_Column_List"/>
        <!--from ads.ads_ms_dyqw_infect_report  t1-->
        from ads.ads_rep_infect_report_info t1
        where t1.source_id = '11'
        and t1.ms_infect_report_id in
        <foreach collection="idList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>

    </select>
    <select id="selectDyqwById" resultType="com.iflytek.cdc.reportcard.vo.ReportUploadRecordVO">
        select
        <include refid="dyqw_Base_Column_List"/>
        <!--from ads.ads_ms_dyqw_infect_report  t1-->
        from ads.ads_rep_infect_report_info t1
        where t1.source_id = '11'
        and t1.ms_infect_report_id = #{id}
    </select>

    <select id="findDyqwByReportCardId" resultType="com.iflytek.cdc.reportcard.vo.ReportUploadRecordVO">
        select
        <include refid="dyqw_Base_Column_List"/>
        <!--from ads.ads_ms_dyqw_infect_report  t1-->
        from ads.ads_rep_infect_report_info t1
        where t1.source_id = '11'
        and t1.ms_infect_report_id = #{id}
    </select>

    <update id="updateReportCardOperate">
        <!--update ads.ads_ms_dyqw_infect_report set-->
        update ads.ads_rep_infect_report_info set
        <if test="operateType == 1">
            card_delete_flag = 1,
            card_delete_remark = #{operateRemark}
        </if>
        <if test="operateType == 2">
            card_exclude_flag = 1,
            card_exclude_remark = #{operateRemark}
        </if>
        where ms_infect_report_id = #{id}
    </update>

    <select id="tobeReviewedCardCount" resultType="java.lang.Integer">
        select count(1) from ads.ads_rep_infect_report_info t1
        where t1.source_id = '11' and (t1.valid_flag not IN ('省级审核/终审', '已删除') or valid_flag is null or valid_flag='')
        <if test="typeOfCardList !=null and typeOfCardList.size() >0 and validCertNumber != null and validCertNumber !=''">
            <include refid="type_of_card_choose"/>
        </if>
        <if test="timeTypeList !=null and timeTypeList.size() &gt; 0">
            <include refid="time_type_choose"/>
        </if>
        <if test="listDisplayType != null ">
            <if test="listDisplayType == 1">
                and t1.card_delete_flag = '0'
            </if>
            <if test="listDisplayType == 2">
                and t1.card_delete_flag = '1'
            </if>
            <if test="listDisplayType == 3">
                and t1.card_exclude_flag = '1'
            </if>
        </if>
        <if test="infectCodeList !=null and infectCodeList.size() &gt; 0">
            and t1.infect_code in
            <foreach item="item" index="index" collection="infectCodeList"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="casesCategory != null and casesCategory!=''">
            and t1.cases_type_name = #{casesCategory}
        </if>
        <if test="attribution != null and attribution!=''">
            and t1.addr_belong_type = #{attribution}
        </if>

        <if test="status != null and status!=''">
            and t1.card_status = #{status}
        </if>
        <if test="validCertNumber != null and validCertNumber!=''">
            and t1.patient_identity_no like concat('%',#{validCertNumber},'%')
        </if>
        <if test="diseaseName != null and diseaseName!=''">
            and t1.infect_name like concat('%',#{diseaseName},'%')
        </if>
        <if test="reportCardId != null and reportCardId!=''">
            and t1.card_id like concat('%',#{reportCardId},'%')
        </if>
        <if test="name != null and name!=''">
            and t1.patient_name like concat('%',#{name},'%')
        </if>
        <if test="unitName != null and unitName!=''">
            and t1.report_org_name like concat('%',#{unitName},'%')
        </if>
        <if test="queryKey!= null and queryKey!=''">
            and (t1.patient_name like concat('%',#{queryKey},'%')
            or t1.card_id like concat('%',#{queryKey},'%')
            or t1.infect_name like concat('%',#{queryKey},'%')
            or t1.patient_identity_no like concat('%',#{queryKey},'%')
            or t1.report_org_name like concat('%',#{queryKey},'%')
            or t1.living_addr_detail like concat('%',#{queryKey},'%')
            )
        </if>
        <if test="addressName != null and addressName!=''">
            and t1.living_addr_detail like concat('%',#{addressName},'%')
        </if>
        <if test="reportCardDataType != 0">
            <include refid="reportCardDataTypeDyqw"/>
        </if>
        <if test="reportCardDataType == 0">
            <if test="provinceCode != null and provinceCode!=''">
                and t1.living_addr_province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode!=''">
                and t1.living_addr_city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode!=''">
                and t1.living_addr_district_code = #{districtCode}
            </if>
            <if test="reportOrgAddrProvinceCode != null and reportOrgAddrProvinceCode!=''">
                and t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
            </if>
            <if test="reportOrgAddrCityCode != null and reportOrgAddrCityCode!=''">
                and t1.report_addr_city_code = #{reportOrgAddrCityCode}
            </if>
            <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                and t1.report_addr_district_code = #{reportOrgAddrDistrictCode}
            </if>
        </if>
    </select>

    <select id="reportingUnitInquiry" resultType="java.lang.String">
        select distinct t1.report_org_name from ads.ads_rep_infect_report_info t1
        where t1.source_id = '11'
        <if test="unitName != null and unitName!=''">
            and t1.report_org_name like concat('%',#{unitName},'%')
        </if>
        <if test="reportCardDataType != 0">
            <include refid="reportCardDataTypeDyqw"/>
        </if>
        <if test="reportCardDataType == 0">
            <if test="provinceCode != null and provinceCode!=''">
                and t1.living_addr_province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode!=''">
                and t1.living_addr_city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode!=''">
                and t1.living_addr_district_code = #{districtCode}
            </if>
            <if test="reportOrgAddrProvinceCode != null and reportOrgAddrProvinceCode!=''">
                and t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
            </if>
            <if test="reportOrgAddrCityCode != null and reportOrgAddrCityCode!=''">
                and t1.report_addr_city_code = #{reportOrgAddrCityCode}
            </if>
            <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                and t1.report_addr_district_code = #{reportOrgAddrDistrictCode}
            </if>
        </if>
    </select>

    <select id="findDyqwListByGX" resultType="com.iflytek.cdc.reportcard.vo.ReportUploadRecordVO">
        select
        <include refid="dyqw_Base_Column_List"/>
        <if test="listDisplayType != null ">
            <if test="listDisplayType != 0">
                <include refid="dyqw_Base_Column_List_HN"/>
            </if>
        </if>
        <!--from ads.ads_ms_dyqw_infect_report  t1-->
        from ads.ads_rep_infect_report_info t1
        where 1=1
        <if test="sourceId != null and sourceId != ''">
            and t1.source_id = #{sourceId}
        </if>
        <if test="typeOfCardList !=null and typeOfCardList.size() >0 and validCertNumber != null and validCertNumber !=''">
            <include refid="type_of_card_choose"/>
        </if>
        <if test="reviewStatusList !=null and reviewStatusList.size() &gt; 0">
            <include refid="review_status_choose"/>
        </if>
        <if test="timeTypeList !=null and timeTypeList.size() &gt; 0">
            <include refid="time_type_choose"/>
        </if>
        <if test="infectCodeList !=null and infectCodeList.size() &gt; 0">
            and t1.infect_code in
            <foreach item="item" index="index" collection="infectCodeList"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="listDisplayType != null ">
            <if test="listDisplayType == 1">
                and t1.card_delete_flag = '0'
            </if>
            <if test="listDisplayType == 2">
                and t1.card_delete_flag = '1'
            </if>
            <if test="listDisplayType == 3">
                and t1.card_exclude_flag = '1'
            </if>
        </if>
        <if test="casesCategory != null and casesCategory!=''">
            and t1.cases_type_name = #{casesCategory}
        </if>
        <if test="attribution != null and attribution!=''">
            and t1.addr_belong_type = #{attribution}
        </if>
        <if test="status != null and status!=''">
            and t1.card_status = #{status}
        </if>
        <if test="validCertNumber != null and validCertNumber!=''">
            and t1.patient_identity_no like concat('%',#{validCertNumber},'%')
        </if>
        <if test="diseaseName != null and diseaseName!=''">
            and t1.infect_name like concat('%',#{diseaseName},'%')
        </if>
        <if test="reportCardId != null and reportCardId!=''">
            and t1.card_id like concat('%',#{reportCardId},'%')
        </if>
        <if test="name != null and name!=''">
            and t1.patient_name like concat('%',#{name},'%')
        </if>
        <if test="unitName != null and unitName!=''">
            and t1.report_org_name like concat('%',#{unitName},'%')
        </if>
        <if test="queryKey!= null and queryKey!=''">
            and (t1.patient_name like concat('%',#{queryKey},'%')
            or t1.card_id like concat('%',#{queryKey},'%')
            or t1.infect_name like concat('%',#{queryKey},'%')
            or t1.patient_identity_no like concat('%',#{queryKey},'%')
            or t1.report_org_name like concat('%',#{queryKey},'%')
            or t1.living_addr_detail like concat('%',#{queryKey},'%')
            )
        </if>
        <if test="addressName != null and addressName!=''">
            and t1.living_addr_detail like concat('%',#{addressName},'%')
        </if>
        <if test="reportCardDataType != 0">
            <include refid="reportCardDataTypeDyqw"/>
        </if>
        <if test="reportCardDataType == 0">
            <if test="provinceCode != null and provinceCode!=''">
                and t1.living_addr_province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode!=''">
                and t1.living_addr_city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode!=''">
                and t1.living_addr_district_code = #{districtCode}
            </if>
            <if test="reportOrgAddrProvinceCode != null and reportOrgAddrProvinceCode!=''">
                and t1.report_addr_province_code = #{reportOrgAddrProvinceCode}
            </if>
            <if test="reportOrgAddrCityCode != null and reportOrgAddrCityCode!=''">
                and t1.report_addr_city_code = #{reportOrgAddrCityCode}
            </if>
            <if test="reportOrgAddrDistrictCode != null and reportOrgAddrDistrictCode!=''">
                and t1.report_addr_district_code = #{reportOrgAddrDistrictCode}
            </if>
        </if>
        order by t1.report_datetime desc
    </select>
</mapper>
