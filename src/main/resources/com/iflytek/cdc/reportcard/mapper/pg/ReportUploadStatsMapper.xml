<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.reportcard.mapper.pg.ReportUploadStatsMapper">

    <select id="notifiableInfectTypeStat" resultType="com.iflytek.cdc.reportcard.vo.stats.ReportUploadSummaryVO">
        select
            coalesce(cur.notifiableDiseaseNewCnt, 0) as notifiableDiseaseNewCnt,
            coalesce(lastD.notifiableDiseaseNewCnt, 0) as notifiableDiseaseNewCntLast,
            coalesce(lastY.notifiableDiseaseNewCnt, 0) as notifiableDiseaseNewCntLastY,
            coalesce(cur.classAAndBNewCnt, 0) as classAAndBNewCnt,
            coalesce(lastD.classAAndBNewCnt, 0) as classAAndBNewCntLast,
            coalesce(lastY.classAAndBNewCnt, 0) as classAAndBNewCntLastY,
            coalesce(cur.classCNewCnt, 0) as classCNewCnt,
            coalesce(lastD.classCNewCnt, 0) as classCNewCntLast,
            coalesce(lastY.classCNewCnt, 0) as classCNewCntLastY,
            coalesce(cur.otherNewCnt, 0) as otherNewCnt,
            coalesce(lastD.otherNewCnt, 0) as otherNewCntLast,
            coalesce(lastY.otherNewCnt, 0) as otherNewCntLastY,
             coalesce(cur.processDeadCnt, 0) as notifiableDiseaseDeadCnt,
             coalesce(lastD.processDeadCnt, 0) as notifiableDiseaseDeadCntLast,
             coalesce(lastY.processDeadCnt, 0) as notifiableDiseaseDeadCntLastY,
             coalesce(cur.classAAndBDeadCnt, 0) as classAAndBDeadCnt,
             coalesce(lastD.classAAndBDeadCnt, 0) as classAAndBDeadCntLast,
             coalesce(lastY.classAAndBDeadCnt, 0) as classAAndBDeadCntLastY,
             coalesce(cur.classCDeadCnt, 0) as classCDeadCnt,
             coalesce(lastD.classCDeadCnt, 0) as classCDeadCntLast,
             coalesce(lastY.classCDeadCnt, 0) as classCDeadCntLastY,
             coalesce(cur.otherDeadCnt, 0) as otherDeadCnt,
             coalesce(lastD.otherDeadCnt, 0) as otherDeadCntLast,
             coalesce(lastY.otherDeadCnt, 0) as otherDeadCntLastY
        from
        (
            select
            <include refid="notifiableInfectTypeStatQuery" />
            <!--ads.ads_ms_dyqw_infect_report-->
            from  ads.ads_rep_infect_report_info d
            left join dim.dim_infected_info dii on d.infect_code = dii.id
            where 1=1
            and source_id = '11'
            <include refid="curDate"/>
            <include refid="whereDetailTableCriteria" />
        )cur
        full join
        (
        select
        <include refid="notifiableInfectTypeStatQuery" />
        <!--from ads.ads_ms_dyqw_infect_report-->
        from ads.ads_rep_infect_report_info d
        left join dim.dim_infected_info dii on d.infect_code = dii.id
        where 1=1  and source_id = '11'
        <include refid="lastDate"/>
        <include refid="whereDetailTableCriteria" />
        ) lastD
        on 1=1
        full join
        (
        select
        <include refid="notifiableInfectTypeStatQuery" />
        <!--from ads.ads_ms_dyqw_infect_report-->
        from ads.ads_rep_infect_report_info d
        left join dim.dim_infected_info dii on d.infect_code = dii.id
        where 1 = 1 and source_id = '11'
        <include refid="lastYDate"/>
        <include refid="whereDetailTableCriteria" />
        ) lastY
        on 1=1
    </select>

    <sql id="notifiableInfectTypeStatQuery">
        coalesce(count(distinct ms_infect_report_id), 0) notifiableDiseaseNewCnt,
        coalesce(sum(case when dii.infected_type_name in ('甲类传染病', '乙类传染病') then 1 else 0 end), 0) classAAndBNewCnt,
        coalesce(sum(case when dii.infected_type_name = '丙类传染病' then 1 else 0 end), 0) classCNewCnt,
        coalesce(sum(case when dii.infected_type_name = '其他法定管理及重点监测传染病' then 1 else 0 end), 0) otherNewCnt,
        coalesce(sum(case when dead_datetime is not null then 1 else 0 end),0) as processDeadCnt,
        coalesce(sum(case when dii.infected_type_name in ('甲类传染病', '乙类传染病') and dead_datetime is not null then 1 else 0 end), 0) classAAndBDeadCnt,
        coalesce(sum(case when dii.infected_type_name = '丙类传染病' and dead_datetime is not null then 1 else 0 end), 0) classCDeadCnt,
        coalesce(sum(case when dii.infected_type_name = '其他法定管理及重点监测传染病' and dead_datetime is not null then 1 else 0 end), 0) otherDeadCnt
    </sql>

    <sql id="curDate">
        <if test="startDate != null and endDate != null">
            and CAST(report_datetime AS DATE) between #{startDate} and #{endDate}
        </if>
    </sql>

    <sql id="lastDate">
        <if test="lastStartDate != null and lastEndDate != null">
            and CAST(report_datetime AS DATE) between #{lastStartDate} and #{lastEndDate}
        </if>
    </sql>

    <sql id="lastYDate">
        <if test="lastYStartDate != null and lastYEndDate != null">
            and CAST(report_datetime AS DATE) between #{lastYStartDate} and #{lastYEndDate}
        </if>
    </sql>



    <sql id="chooseTable">
        <choose>
            <when test="dateDimType == 'week'">
                ads.ads_ms_dyqw_infect_it_w
            </when>
            <when test="dateDimType == 'meadow'">
                ads.ads_ms_dyqw_infect_it_td
            </when>
            <when test="dateDimType == 'month'">
                ads.ads_ms_dyqw_infect_it_m
            </when>
            <when test="dateDimType == 'quarter'">
                ads.ads_ms_dyqw_infect_it_q
            </when>
            <when test="dateDimType == 'year'">
                ads.ads_ms_dyqw_infect_it_y
            </when>
            <otherwise>
                ads.ads_ms_dyqw_infect_it_d
            </otherwise>
        </choose>
    </sql>
    <sql id="whereCriteria">
        <include refid="areaCriteria" />
        <include refid="dateDimCriteria" />
        <if test="infectClass != null and infectClass != ''">
            and infect_class = #{infectClass, jdbcType=VARCHAR}
        </if>
        <!--  传染病编码和名称不同时生效，避免大疫情网传染病名称不一致的问题  -->
        <if test="infectCode != null and infectCode != '' and (infectName == null or infectName == '')">
            and infect_code = #{infectCode, jdbcType=VARCHAR}
        </if>
        <if test="infectName != null and infectName != '' and (infectCode == null or infectCode == '')">
            and infect_name = #{infectName, jdbcType=VARCHAR}
        </if>
        <if test="infectCode != null and infectCode != '' and infectName != null and infectName != ''">
            and (infect_code = #{infectCode, jdbcType=VARCHAR} or infect_name = #{infectName, jdbcType=VARCHAR})
        </if>
        <if test="infectType != null and infectType != ''">
            and infect_type = #{infectType, jdbcType=VARCHAR}
        </if>
        <if test="infectTypes != null and infectTypes.size() > 0">
            and infect_type in
            <foreach collection="infectTypes" item= "item" open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test = "filterCodes != null and filterCodes.size() > 0">
            and infect_code not in
            <foreach collection="filterCodes" open="(" separator="," close=")" item="item">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="infectTransmitType != null and infectTransmitType != ''">
            and infect_transmit_type = #{infectTransmitType, jdbcType=VARCHAR}
        </if>
    </sql>

    <sql id="areaCriteria">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="provinceCode != null and provinceCode != ''">
                    and living_addr_province_code = #{provinceCode}
                </if>
                <if test="cityCode != null and cityCode != ''">
                    and living_addr_city_code = #{cityCode}
                </if>
                <if test="districtCode != null and districtCode != ''">
                    and living_addr_district_code = #{districtCode}
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and living_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and living_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and living_addr_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="provinceCode != null and provinceCode != ''">
                    and org_addr_province_code = #{provinceCode}
                </if>
                <if test="cityCode != null and cityCode != ''">
                    and org_addr_city_code = #{cityCode}
                </if>
                <if test="districtCode != null and districtCode != ''">
                    and org_addr_district_code = #{districtCode}
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and org_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and org_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and org_addr_province_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="dateDimCriteria">
        <choose>
            <when test="dateDimType == 'week'">
                and
                <foreach collection="dateDims" item="item" separator=" or " open="(" close=")">
                    ("year" = #{item.year} and "week" = #{item.week})
                </foreach>
            </when>
            <when test="dateDimType == 'meadow'">
                and
                <foreach collection="dateDims" item="item" separator=" or " open="(" close=")">
                    ("year" = #{item.year} and "month" = #{item.month} and ten_day = #{item.meadow,jdbcType=VARCHAR})
                </foreach>
            </when>
            <when test="dateDimType == 'month'">
                and
                <foreach collection="dateDims" item="item" separator=" or " open="(" close=")">
                    ("year" = #{item.year} and "month" = #{item.month})
                </foreach>
            </when>
            <when test="dateDimType == 'quarter'">
                and
                <foreach collection="dateDims" item="item" separator=" or " open="(" close=")">
                    ("year" = #{item.year} and "quarter" = #{item.quarter})
                </foreach>
            </when>
            <when test="dateDimType == 'halfYear'">
                and
                <foreach collection="dateDims" item="item" separator=" or " open="(" close=")">
                    ("year" = #{item.year} and half_year = #{item.halfYear})
                </foreach>
            </when>
            <when test="dateDimType == 'year'">
                and "year" in
                <foreach collection="dateDims" item="item" separator="," open="(" close=")">
                    #{item.year}
                </foreach>
            </when>
            <otherwise>
                <if test="startDate != null and endDate != null">
                    and "day" between #{startDate} and #{endDate}
                </if>
            </otherwise>
        </choose>
    </sql>

    <select id="groupInfectNewCntTop10" resultType="com.iflytek.cdc.reportcard.vo.stats.ReportUploadStatsVO">
        <include refid="groupInfectNewCntSql"/>
        order by processNewCntYearGrowth desc
        limit 10
    </select>

    <sql id="groupInfectNewCntSql">
        select
        infectCode,
        infectName,
        processNewCnt,
        processNewCntLastY,
        case
        when processNewCntLastY = 0 then 1
        else round((processNewCnt::numeric - processNewCntLastY::numeric) / processNewCntLastY::numeric, 4)
        end processNewCntYearGrowth
        from
        (
        select
        coalesce(cur.infectCode, lastY.infectCode) as infectCode,
        coalesce(cur.infectName, lastY.infectName) as infectName,
        coalesce(cur.processNewCnt, 0) as processNewCnt,
        coalesce(lastY.processNewCnt, 0) as processNewCntLastY
        from
        (
            (
                select
                dii.id as infectCode,
                dii.infected_sub_name as infectName,
                coalesce(count(distinct ms_infect_report_id), 0) as processNewCnt
               <!-- from ads.ads_ms_dyqw_infect_report d-->
                from ads.ads_rep_infect_report_info d
                join dim.dim_infected_info dii on d.infect_code = dii.id
                where 1 = 1 and d.source_id = '11'
               <include refid="whereDetailTableCriteria" />
                <include refid="curDate"/>
                group by dii.id, dii.infected_sub_name
            ) cur
            full join
            (
                select
                dii.id as infectCode,
                dii.infected_sub_name as infectName,
                coalesce(count(distinct ms_infect_report_id), 0) as processNewCnt
               <!-- from ads.ads_ms_dyqw_infect_report d-->
                from ads.ads_rep_infect_report_info d
                join dim.dim_infected_info dii on d.infect_code = dii.id
                where 1 = 1 and d.source_id = '11'
                <include refid="whereDetailTableCriteria" />
                <include refid="lastYDate"/>
                group by dii.id, dii.infected_sub_name
                ) lastY
                on cur.infectCode = lastY.infectCode
            )
        ) tab
        where processNewCntLastY > 0
    </sql>

    <select id="groupInfectNewCntDeclineTop10" resultType="com.iflytek.cdc.reportcard.vo.stats.ReportUploadStatsVO">
        <include refid="groupInfectNewCntSql"/>
        order by processNewCntYearGrowth asc
        limit 10
    </select>

    <select id="groupDateDimCnt" resultType="com.iflytek.cdc.reportcard.vo.stats.ReportUploadStatsDateDimVO">
        select
         coalesce(cur.statDate, lastY.statDate) as statDate,
         coalesce(cur.processNewCnt, 0) as processNewCnt,
         0 as processNewCntAvgS,
         coalesce(lastY.processNewCnt, 0) as processNewCntLastY,
         coalesce(cur.processDeadCnt, 0) as processDeadCnt,
         0 as processDeadCntAvgS,
         coalesce(lastY.processDeadCnt, 0) as processDeadCntLastY
        from
        (
            select
            <include refid="groupDateDimCntQuery" />
           <!-- from ads.ads_ms_dyqw_infect_report-->
            from ads.ads_rep_infect_report_info d
            left join dim.dim_infected_info dii on d.infect_code = dii.id
            where 1 = 1 and source_id = '11'
            <include refid="whereDetailTableCriteria" />
            <include refid="curDate" />
            group by statDate
        )cur
        full join
        (
            select
            <include refid="groupDateDimCntQuery" />
           <!-- from ads.ads_ms_dyqw_infect_report-->
             from ads.ads_rep_infect_report_info d
             left join dim.dim_infected_info dii on d.infect_code = dii.id
             where 1 = 1 and source_id = '11'
            <include refid="whereDetailTableCriteria" />
            <include refid="lastYDate" />
            group by statDate
        ) lastY
        on EXTRACT(MONTH FROM cur.statDate) = EXTRACT(MONTH FROM lastY.statDate)
        AND EXTRACT(DAY FROM cur.statDate) = EXTRACT(DAY FROM lastY.statDate)
    </select>

    <sql id="groupDateDimCntQuery">
        CAST(report_datetime AS DATE) as statDate,
        coalesce(count(distinct ms_infect_report_id), 0) as processNewCnt,
        coalesce(sum(case when dead_datetime is not null then 1 else 0 end),0) as processDeadCnt
    </sql>
    <sql id="chooseStatDateSelect">
        <choose>
            <when test="dateDimType == 'week'">
                CONCAT("year", '年第', "week", '周') as statDate
            </when>
            <when test="dateDimType == 'meadow'">
                CONCAT("year", '年', LPAD("month", 2, '0'), '月', (case when ten_day = '1' then '上旬' when ten_day = '2' then '中旬' else '下旬' end)) as statDate
            </when>
            <when test="dateDimType == 'month'">
                CONCAT("year", '年', LPAD("month", 2, '0'), '月') as statDate
            </when>
            <when test="dateDimType == 'quarter'">
                CONCAT("year", '年第', "quarter", '季度') as statDate
            </when>
            <when test="dateDimType == 'halfYear'">
                CONCAT("year", '年', (case when half_year = 'U' then '上半年' else '下半年' end)) as statDate
            </when>
            <when test="dateDimType == 'year'">
                CONCAT("year", '年') as statDate
            </when>
            <otherwise>
                cast("day" AS date) as statDate
            </otherwise>
        </choose>
    </sql>
    <sql id="chooseStatDateGroupOrder">
        <choose>
            <when test="dateDimType == 'week'">
                group by "year", "week"
                order by "year", "week"
            </when>
            <when test="dateDimType == 'meadow'">
                group by "year", "month", "ten_day"
                order by "year", "month", "ten_day"
            </when>
            <when test="dateDimType == 'month'">
                group by "year", "month"
                order by "year", "month"
            </when>
            <when test="dateDimType == 'quarter'">
                group by "year", "quarter"
                order by "year", "quarter"
            </when>
            <when test="dateDimType == 'halfYear'">
                group by "year", "half_year"
                order by "year", "half_year"
            </when>
            <when test="dateDimType == 'year'">
                group by "year"
                order by "year"
            </when>
            <otherwise>
                group by "day"
                order by "day"
            </otherwise>
        </choose>
    </sql>

    <select id="groupAreaCnt" resultType="com.iflytek.cdc.reportcard.vo.stats.ReportUploadStatsAreaVO">
        select
        sum(process_new_cnt) as processNewCnt,
        sum(process_new_cnt_last) as processNewCntLast,
        sum(process_new_cnt_last_y) as processNewCntLastY,
        sum(process_dead_cnt) as processDeadCnt,
        sum(process_dead_cnt_last) as processDeadCntLast,
        sum(process_dead_cnt_last_y) as processDeadCntLastY,
        <include refid="chooseAreaSelect"/>
        from <include refid="chooseTable" />
        where 1=1 <include refid="whereCriteria" />
        <include refid="chooseAreaGroupOrder"/>
    </select>

    <sql id="chooseAreaSelect">
        <choose>
            <when test="addressType == 'livingAddress'">
                <choose>
                    <when test="areaLevel == 1">
                        living_addr_city_code as areaCode,
                        living_addr_city as areaName,
                        living_addr_city as description,
                        2 as areaLevel
                    </when>
                    <otherwise>
                        living_addr_district_code as areaCode,
                        living_addr_district as areaName,
                        living_addr_district as description,
                        3 as areaLevel
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                <choose>
                    <when test="areaLevel == 1">
                        org_addr_city_code as areaCode,
                        org_addr_city as areaName,
                        org_addr_city as description,
                        2 as areaLevel
                    </when>
                    <otherwise>
                        org_addr_district_code as areaCode,
                        org_addr_district as areaName,
                        org_addr_district as description,
                        3 as areaLevel
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </sql>

    <sql id="chooseAreaGroupOrder">
        <choose>
            <when test="addressType == 'livingAddress'">
                <choose>
                    <when test="areaLevel == 1">
                        group by living_addr_city_code, living_addr_city
                        order by living_addr_city_code, living_addr_city
                    </when>
                    <otherwise>
                        group by living_addr_district_code, living_addr_district
                        order by living_addr_district_code, living_addr_district
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                <choose>
                    <when test="areaLevel == 1">
                        group by org_addr_city_code, org_addr_city
                        order by org_addr_city_code, org_addr_city
                    </when>
                    <otherwise>
                        group by org_addr_district_code, org_addr_district
                        order by org_addr_district_code, org_addr_district
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </sql>

    <select id="groupAgeCnt" resultType="com.iflytek.cdc.reportcard.vo.stats.ReportUploadStatsAgeVO">
        select
        sum(process_new_cnt) as processNewCnt,
        sum(process_new_cnt_last) as processNewCntLast,
        sum(process_new_cnt_last_y) as processNewCntLastY,
        sum(process_dead_cnt) as processDeadCnt,
        sum(process_dead_cnt_last) as processDeadCntLast,
        sum(process_dead_cnt_last_y) as processDeadCntLastY,
        patient_age_group as patientAgeGroup,
        patient_age_group as description
        from <include refid="chooseTable" />
        where 1=1 <include refid="whereCriteria" />
        group by patient_age_group
        order by patient_age_group
    </select>

    <select id="groupGenderCnt" resultType="com.iflytek.cdc.reportcard.vo.stats.ReportUploadStatsGenderVO">
        select
        sum(process_new_cnt) as processNewCnt,
        sum(process_new_cnt_last) as processNewCntLast,
        sum(process_new_cnt_last_y) as processNewCntLastY,
        sum(process_dead_cnt) as processDeadCnt,
        sum(process_dead_cnt_last) as processDeadCntLast,
        sum(process_dead_cnt_last_y) as processDeadCntLastY,
        patient_sex_name as patientSexName,
        patient_sex_name as description
        from <include refid="chooseTable" />
        where 1=1 <include refid="whereCriteria" />
        group by patient_sex_name
        order by patient_sex_name
    </select>

    <select id="groupJobCnt" resultType="com.iflytek.cdc.reportcard.vo.stats.ReportUploadStatsJobVO">
        select
        sum(process_new_cnt) as processNewCnt,
        sum(process_new_cnt_last) as processNewCntLast,
        sum(process_new_cnt_last_y) as processNewCntLastY,
        sum(process_dead_cnt) as processDeadCnt,
        sum(process_dead_cnt_last) as processDeadCntLast,
        sum(process_dead_cnt_last_y) as processDeadCntLastY,
        patient_job as patientJob,
        patient_job as description
        from <include refid="chooseTable" />
        where 1=1 <include refid="whereCriteria" />
        group by patient_job
        order by patient_job
    </select>



    <select id="groupInfectCnt" resultType="com.iflytek.cdc.reportcard.vo.stats.ReportUploadStatsVO">
        select
             coalesce(cur.infectCode, lastD.infectCode, lastY.infectCode) as infectCode,
             coalesce(cur.infectName, lastD.infectName, lastY.infectName) as infectName,
             coalesce(cur.infectClass, lastD.infectClass, lastY.infectClass) as infectClass,
             coalesce(cur.infectType, lastD.infectType, lastY.infectType) as infectType,
             coalesce(cur.infectTransmitType, lastD.infectTransmitType, lastY.infectTransmitType) as infectTransmitType,
             coalesce(cur.parentDiseaseId, lastD.parentDiseaseId, lastY.parentDiseaseId) as parentDiseaseId,
             coalesce(cur.orderFlag, lastD.orderFlag, lastY.orderFlag) as orderFlag,
             COALESCE(cur.processNewCnt, 0) as processNewCnt,
             COALESCE(lastD.processNewCnt, 0) as processNewCntLast,
             COALESCE(lastY.processNewCnt, 0) as processNewCntLastY,
             COALESCE(cur.processDeadCnt, 0) as processDeadCnt,
             COALESCE(lastD.processDeadCnt, 0) as processDeadCntLast,
             COALESCE(lastY.processDeadCnt, 0) as processDeadCntLastY
        from
        (
            select
            idi.id as infectCode,
            infected_sub_name as infectName,
            infect_class_name as infectClass,
            infected_type_name as infectType,
            transmission_type_name as infectTransmitType,
            infected_id as parentDiseaseId,
            idi.order_flag as orderFlag,
            coalesce(count(distinct ms_infect_report_id), 0) as processNewCnt,
            coalesce(sum(case when dead_datetime is not null then 1 else 0 end),0) as processDeadCnt
            from dim.dim_infected_info idi
            left join
            ads.ads_rep_infect_report_info t
        <!--ads.ads_ms_dyqw_infect_report t -->
            on idi.id = t.infect_code and t.source_id = '11'
            <include refid="areaDetailChoose" />
            <include refid="curDate"/>
            <include refid="groupInfectCntWhere"/>
        )cur
        full join
        (
            select
            idi.id as infectCode,
            infected_sub_name as infectName,
            infect_class_name as infectClass,
            infected_type_name as infectType,
            transmission_type_name as infectTransmitType,
            infected_id as parentDiseaseId,
            idi.order_flag as orderFlag,
            coalesce(count(distinct ms_infect_report_id), 0) as processNewCnt,
            coalesce(sum(case when dead_datetime is not null then 1 else 0 end),0) as processDeadCnt
            from dim.dim_infected_info idi
           <!-- left join ads.ads_ms_dyqw_infect_report t -->
            left join ads.ads_rep_infect_report_info t
             on idi.id = t.infect_code and t.source_id = '11'
            <include refid="areaDetailChoose" />
            <include refid="lastDate"/>
            <include refid="groupInfectCntWhere"/>
        )lastD
        on cur.infectCode = lastD.infectCode
        full join
        (
            select
            idi.id as infectCode,
            infected_sub_name as infectName,
            infect_class_name as infectClass,
            infected_type_name as infectType,
            transmission_type_name as infectTransmitType,
            infected_id as parentDiseaseId,
            idi.order_flag as orderFlag,
            coalesce(count(distinct ms_infect_report_id), 0) as processNewCnt,
            coalesce(sum(case when dead_datetime is not null then 1 else 0 end),0) as processDeadCnt
            from dim.dim_infected_info idi
            <!--left join ads.ads_ms_dyqw_infect_report t-->
            left join ads.ads_rep_infect_report_info  t
             on idi.id = t.infect_code and t.source_id = '11'
            <include refid="areaDetailChoose" />
            <include refid="lastYDate"/>
            <include refid="groupInfectCntWhere"/>
        )lastY
        on COALESCE(cur.infectCode, lastD.infectCode) = lastY.infectCode
    </select>

    <sql id="groupInfectCntWhere">
        where idi.delete_flag ='0'
        <include refid="whereSubtypeCriteria" />
        GROUP BY
        idi.id,
        infected_sub_name,
        infect_class_name,
        infected_type_name,
        transmission_type_name,
        infected_id
        ORDER BY
        CASE
        WHEN infected_type_name = '甲类传染病' THEN 1
        WHEN infected_type_name = '乙类传染病' THEN 2
        WHEN infected_type_name = '丙类传染病' THEN 3
        WHEN infected_type_name = '其他传染病' THEN 4
        ELSE 0
        END,
        idi.id,
        case
        when level_type = '传染病病种' then 1
        when level_type = '传染病' then 2
        when level_type = '传染病亚型' then 3
        else 0
        end
    </sql>

    <sql id="whereSubtypeCriteria">
        <if test="infectClass != null and infectClass != ''">
            and idi.infect_class_name = #{infectClass, jdbcType=VARCHAR}
        </if>
        <!--  传染病编码和名称不同时生效，避免大疫情网传染病名称不一致的问题  -->
        <if test="infectCode != null and infectCode != '' and (infectName == null or infectName == '')">
            and idi.id = #{infectCode, jdbcType=VARCHAR}
        </if>
        <if test="infectName != null and infectName != '' and (infectCode == null or infectCode == '')">
            and idi.infected_sub_name = #{infectName, jdbcType=VARCHAR}
        </if>
        <if test="infectCode != null and infectCode != '' and infectName != null and infectName != ''">
            and (idi.id = #{infectCode, jdbcType=VARCHAR} or idi.infected_sub_name = #{infectName, jdbcType=VARCHAR})
        </if>
        <if test="infectType != null and infectType != ''">
            and idi.infected_type_name = #{infectType, jdbcType=VARCHAR}
        </if>
        <if test="infectTypes != null and infectTypes.size() > 0">
            and idi.infected_type_name in
            <foreach collection="infectTypes" item= "item" open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="infectCodeList != null and infectCodeList.size() > 0">
            and idi.id in
            <foreach collection="infectCodeList" item= "item" open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test = "filterCodes != null and filterCodes.size() > 0">
            and idi.id not in
            <foreach collection="filterCodes" open="(" separator="," close=")" item="item">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="infectTransmitType != null and infectTransmitType != ''">
            and idi.transmission_type_name = #{infectTransmitType, jdbcType=VARCHAR}
        </if>
    </sql>




    <select id="groupInfectTransmitTypeCnt"
            resultType="com.iflytek.cdc.reportcard.vo.stats.ReportUploadStatsVO">
        select
            coalesce(cur.transmission_type_name, lastD.transmission_type_name, lastY.transmission_type_name) as infectTransmitType,
            coalesce(cur.processNewCnt, 0) as processNewCnt,
            coalesce(lastD.processNewCnt, 0) as processNewCntLast,
            coalesce(lastY.processNewCnt, 0) as processNewCntLastY,
            coalesce(cur.processDeadCnt, 0) as processDeadCnt,
            coalesce(lastD.processDeadCnt, 0) as processDeadCntLast,
            coalesce(lastY.processDeadCnt, 0) as processDeadCntLastY
        from
        (
            select
            transmission_type_name,
            coalesce(count(distinct ms_infect_report_id), 0) as processNewCnt,
            coalesce(sum(case when dead_datetime is not null then 1 else 0 end),0) as processDeadCnt
            from dim.dim_infected_info dii
            <!--join ads.ads_ms_dyqw_infect_report d -->
            join  ads.ads_rep_infect_report_info d
            on d.infect_code = dii.id
            <include refid="whereDetailTableCriteria" />
            <include refid="curDate" />
            where d.source_id = '11'
            and dii.delete_flag ='0'
            group by transmission_type_name
        ) cur
        full join
        (
            select
            transmission_type_name,
            coalesce(count(distinct ms_infect_report_id), 0) as processNewCnt,
            coalesce(sum(case when dead_datetime is not null then 1 else 0 end),0) as processDeadCnt
            from dim.dim_infected_info dii
           <!-- join ads.ads_ms_dyqw_infect_report d-->
            join ads.ads_rep_infect_report_info d
            on d.infect_code = dii.id
            <include refid="whereDetailTableCriteria" />
            <include refid="curDate" />
            where d.source_id = '11'
                 and dii.delete_flag ='0'
                 and d.source_id = '11'
            group by transmission_type_name
        ) lastD
        on cur.transmission_type_name = lastD.transmission_type_name
        full join
        (
            select
            transmission_type_name,
            coalesce(count(distinct ms_infect_report_id), 0) as processNewCnt,
            coalesce(sum(case when dead_datetime is not null then 1 else 0 end),0) as processDeadCnt
            from dim.dim_infected_info dii
            <!--join ads.ads_ms_dyqw_infect_report d -->
            join  ads.ads_rep_infect_report_info d
            on d.infect_code = dii.id
            <include refid="whereDetailTableCriteria" />
            <include refid="curDate" />
            where  d.source_id = '11'
            and dii.delete_flag ='0'
            and  d.source_id = '11'
            group by transmission_type_name
        ) lastY
        on coalesce(cur.transmission_type_name, lastD.transmission_type_name) = lastY.transmission_type_name
        order by infectTransmitType
    </select>


    <select id="groupInfectDiseaseCnt" resultType="com.iflytek.cdc.reportcard.vo.stats.ReportUploadStatsVO">
        select
            dii.id as infectCode,
            infected_sub_name as infectName,
            infect_class_name as infectClass,
            infected_type_name as infectType,
            coalesce(count(distinct ms_infect_report_id), 0) as processNewCnt,
            coalesce(sum(case when dead_by_this_flag = '1' then 1 else 0 end ),0) as processDeadIllnessCnt
      <!--  from
            ads.ads_ms_dyqw_infect_report-->
        from ads.ads_rep_infect_report_info d
        left join dim.dim_infected_info dii on d.infect_code = dii.id
        where  source_id = '11'
        <include refid="whereDetailTableCriteria" />
        <include refid="curDate" />
        group by infectCode, infectName,infectClass, infectType
        order by infectCode
    </select>


    <sql id="chooseStat">
        <choose>
            <when test="statFlag == 'deathRate'">
                0 as processNewCnt,
                count(distinct amdir.ms_infect_report_id) as processDeadIllnessCnt,
            </when>
            <otherwise>
                count(distinct amdir.ms_infect_report_id) as processNewCnt,
                0 as processDeadIllnessCnt,
            </otherwise>
        </choose>
    </sql>


    <sql id="chooseDescription">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="areaLevel == 1">
                    amdir.living_addr_city_name as description,
                    amdir.living_addr_city_code as areaCode
                </if>
                <if test="areaLevel == 2">
                    amdir.living_addr_district_name as description,
                    amdir.living_addr_district_code as areaCode
                </if>
                <if test="areaLevel == 3">
                    amdir.living_addr_street_name as description,
                    amdir.living_addr_street_code as areaCode
                </if>
            </when>
            <otherwise>
                <if test="areaLevel == 1">
                    amdir.visit_addr_city_name as description,
                    amdir.visit_addr_city_code as areaCode
                </if>
                <if test="areaLevel == 2">
                    amdir.visit_addr_district_name as description,
                    amdir.visit_addr_district_code as areaCode
                </if>
                <if test="areaLevel == 3">
                    amdir.visit_addr_street_name as description,
                    amdir.visit_addr_street_code as areaCode
                </if>
            </otherwise>
        </choose>
    </sql>

    <!-- ads.ads_ms_dyqw_infect_report amdir 表 -->
    <sql id="areaChooseInfo">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="provinceCode != null and provinceCode != ''">
                    and amdir.living_addr_province_code = #{provinceCode}
                </if>
                <if test="cityCode != null and cityCode != ''">
                    and amdir.living_addr_city_code = #{cityCode}
                </if>
                <if test="districtCode != null and districtCode != ''">
                    and amdir.living_addr_district_code = #{districtCode}
                </if>
                <if test="streetCode != null and streetCode != ''">
                    and amdir.living_addr_street_code = #{streetCode}
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and amdir.living_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and amdir.living_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and amdir.living_addr_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size() > 0">
                    and amdir.living_addr_street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="provinceCode != null and provinceCode != ''">
                    and amdir.visit_addr_province_code = #{provinceCode}
                </if>
                <if test="cityCode != null and cityCode != ''">
                    and amdir.visit_addr_city_code = #{cityCode}
                </if>
                <if test="districtCode != null and districtCode != ''">
                    and amdir.visit_addr_district_code = #{districtCode}
                </if>
                <if test="streetCode != null and streetCode != ''">
                    and amdir.visit_addr_street_code = #{streetCode}
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and amdir.visit_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and amdir.visit_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and amdir.visit_addr_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and amdir.visit_addr_street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="InfoAreaGroup">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="areaLevel == 1">
                    amdir.living_addr_city_code,
                    amdir.living_addr_city_name
                </if>
                <if test="areaLevel == 2">
                    amdir.living_addr_district_code,
                    amdir.living_addr_district
                </if>
                <if test="areaLevel == 3">
                    amdir.living_addr_street_code,
                    amdir.living_addr_street_name
                </if>
            </when>
            <otherwise>
                <if test="areaLevel == 1">
                    amdir.visit_addr_city_code,
                    amdir.visit_addr_city_name
                </if>
                <if test="areaLevel == 2">
                    amdir.visit_addr_district_code,
                    amdir.visit_addr_district_name
                </if>
                <if test="areaLevel == 3">
                    amdir.visit_addr_street_code,
                    amdir.visit_addr_street_name
                </if>
            </otherwise>
        </choose>
    </sql>

    <select id="groupInfectAreaCnt" resultType="com.iflytek.cdc.reportcard.vo.stats.ReportUploadStatsVO">
        select
            <include refid="chooseStat"/>
            <include refid="chooseDescription"/>
        from
            <!--ads.ads_ms_dyqw_infect_report amdir-->
            ads.ads_rep_infect_report_info  amdir
        left join dim.dim_infected_info dii on amdir.infect_code = dii.id
        where 1=1  and amdir.source_id = '11'
        <if test="(statFlag == 'newCnt' or statFlag == 'newRate') and (startDate != null and endDate != null)">
            and amdir.onset_datetime between #{startDate} and #{endDate}
        </if>
        <if test="(statFlag == 'deathRate') and (startDate != null and endDate != null)">
            and amdir.dead_datetime between #{startDate} and #{endDate}
        </if>
        <if test="infectClass != null and infectClass != ''">
            and dii.infect_class_name = #{infectClass}
        </if>
        <include refid="areaChooseInfo"/>
        group by
        <include refid="InfoAreaGroup"/>
    </select>

    <select id="groupInfectAreaOrgCnt" resultType="com.iflytek.cdc.reportcard.vo.stats.ReportUploadStatsVO">
        select
            <include refid="chooseStat"/>
            amdir.visit_org_name as description
        from
           <!-- ads.ads_ms_dyqw_infect_report amdir-->
            ads.ads_rep_infect_report_info amdir
        left join dim.dim_infected_info dii on amdir.infect_code = dii.id
        where 1=1 and amdir.source_id = '11'
            <if test="(statFlag == 'newCnt' or statFlag == 'newRate') and (startDate != null and endDate != null)">
                and amdir.onset_datetime between #{startDate} and #{endDate}
            </if>
            <if test="(statFlag == 'deathRate') and (startDate != null and endDate != null)">
                and amdir.dead_datetime between #{startDate} and #{endDate}
            </if>
            <if test="infectClass != null and infectClass != ''">
                and dii.infect_class_name = #{infectClass}
            </if>
            <include refid="areaChooseInfo"/>
        group by
            amdir.visit_org_name
    </select>

    <sql id="whereDetailTableCriteria">
        <include refid="areaDetailChoose" />
        <if test="infectClass != null and infectClass != ''">
            and dii.infect_class_name = #{infectClass, jdbcType=VARCHAR}
        </if>
        <!--  传染病编码和名称不同时生效，避免大疫情网传染病名称不一致的问题  -->
        <if test="infectCode != null and infectCode != '' and (infectName == null or infectName == '')">
            and dii.id = #{infectCode, jdbcType=VARCHAR}
        </if>
        <if test="infectName != null and infectName != '' and (infectCode == null or infectCode == '')">
            and dii.infected_sub_name = #{infectName, jdbcType=VARCHAR}
        </if>
        <if test="infectCode != null and infectCode != '' and infectName != null and infectName != ''">
            and (dii.id = #{infectCode, jdbcType=VARCHAR} or dii.infected_sub_name = #{infectName, jdbcType=VARCHAR})
        </if>
        <if test="infectType != null and infectType != ''">
            and dii.infected_type_name = #{infectType, jdbcType=VARCHAR}
        </if>
        <if test="infectTypes != null and infectTypes.size() > 0">
            and dii.infected_type_name in
            <foreach collection="infectTypes" item= "item" open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test = "filterCodes != null and filterCodes.size() > 0">
            and dii.id not in
            <foreach collection="filterCodes" open="(" separator="," close=")" item="item">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>


    <sql id="areaDetailChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and living_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and living_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and living_addr_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and living_addr_street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and report_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and report_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and report_addr_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and report_addr_street_name in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>


</mapper>