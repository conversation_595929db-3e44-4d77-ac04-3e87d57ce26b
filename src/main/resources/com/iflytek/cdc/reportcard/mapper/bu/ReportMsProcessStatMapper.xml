<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.reportcard.mapper.bu.ReportMsProcessStatMapper">

    <!-- 数据库表选择 SQL 片段 -->
    <sql id="dayDb">
        app.tb_cdcew_report_upload_stat_d
    </sql>

    <sql id="weekDb">
        app.tb_cdcew_report_upload_stat_w
    </sql>

    <sql id="tenDaysDb">
        app.tb_cdcew_report_upload_stat_td
    </sql>

    <sql id="monthDb">
        app.tb_cdcew_report_upload_stat_m
    </sql>

    <sql id="quarterDb">
        app.tb_cdcew_report_upload_stat_q
    </sql>

    <sql id="yearDb">
        app.tb_cdcew_report_upload_stat_y
    </sql>

    <!-- 地区多选条件 SQL 片段 -->
    <sql id="areaMultiChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and living_province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and living_city_code in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and living_district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <when test="addressType == 'orgAddress'">
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and org_province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and org_city_code in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and org_district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
        </choose>
    </sql>

    <!-- 通用统计字段 SQL 片段 -->
    <sql id="sumCommonCnt">
        COALESCE(sum(report_new_cnt), 0) as processNewCnt,
        COALESCE(sum(report_new_cnt_last), 0) as processNewCntLast,
        COALESCE(sum(report_new_cnt_last_y), 0) as processNewCntLastY,
        COALESCE(sum(report_new_cnt_avg_s), 0) as processNewCntAvgS,
        COALESCE(sum(report_new_cnt_pre), 0) as processNewCntPre,

        COALESCE(sum(report_dead_cnt), 0) as processDeadCnt,
        COALESCE(sum(report_dead_cnt_avg_s), 0) as processDeadCntAvgS,
        COALESCE(sum(report_dead_cnt_last), 0) as processDeadCntLast,
        COALESCE(sum(report_dead_cnt_last_y), 0) as processDeadCntLastY
    </sql>

    <!-- 分布查询条件 SQL 片段 -->
    <sql id="whereDistributionAreaMultiChoose">
        <if test="infectType != null and infectType != ''">
            and infect_type = #{infectType}
        </if>
        <if test="infectTransmitType != null and infectTransmitType != ''">
            and infect_transmit_type = #{infectTransmitType}
        </if>
        <if test="infectCodeSubtypeList != null and infectCodeSubtypeList.size() > 0">
            and infect_code in
            <foreach collection="infectCodeSubtypeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="filterCodes != null and filterCodes.size() > 0">
            and infect_code not in
            <foreach collection="filterCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            and infect_name = #{diseaseName}
        </if>
        <include refid="areaMultiChoose"/>
    </sql>

    <!-- 简化的分布查询条件 SQL 片段 -->
    <sql id="whereDistribution">
        <if test="infectType != null and infectType != ''">
            and infect_type = #{infectType}
        </if>
        <if test="infectTransmitType != null and infectTransmitType != ''">
            and infect_transmit_type = #{infectTransmitType}
        </if>
        <if test="infectCodeSubtypeList != null and infectCodeSubtypeList.size() > 0">
            and infect_code in
            <foreach collection="infectCodeSubtypeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="filterCodes != null and filterCodes.size() > 0">
            and infect_code not in
            <foreach collection="filterCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            and infect_name = #{diseaseName}
        </if>
    </sql>

    <!-- 时间分布相关查询 -->
    <select id="findQuarterDistribution" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select concat("year",'年',quarter ,'季度') as description,
        year,
        quarter,
        <include refid="sumCommonCnt"/>
        from
        <include refid="quarterDb"/>
        where 1=1
        <if test="dateDims != null and dateDims.size() &gt; 0">
            and
            <foreach close=")" collection="dateDims" index="index" item="item" open="(" separator=" or ">
                (1=1
                <if test="item.year != null">
                    and "year" = #{item.year}
                </if>
                <if test="item.quarter != null">
                    and "quarter" = #{item.quarter}
                </if>
                )
            </foreach>
        </if>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year,quarter
        order by "year" desc,"quarter" desc
    </select>

    <select id="findMonthDistribution" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        concat("year",'年',"month",'月') as description,
        year,
        month,
        <include refid="sumCommonCnt"/>
        from
        <include refid="monthDb"/>
        where 1=1
        <if test="dateDims != null and dateDims.size() &gt; 0">
            and
            <foreach close=")" collection="dateDims" index="index" item="item" open="(" separator=" or ">
                (1=1
                <if test="item.year != null">
                    and "year" = #{item.year}
                </if>
                <if test="item.month != null">
                    and "month" = #{item.month}
                </if>
                )
            </foreach>
        </if>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year,month
        order by "year" desc,"month" desc
    </select>

    <select id="findYearDistribution" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select concat("year",'年') as description,
        year,
        <include refid="sumCommonCnt"/>
        from
        <include refid="yearDb"/>
        where 1=1
        <if test="dateDims != null and dateDims.size() &gt; 0">
            and
            <foreach close=")" collection="dateDims" index="index" item="item" open="(" separator=" or ">
                (1=1
                <if test="item.year != null">
                    and "year" = #{item.year}
                </if>
                )
            </foreach>
        </if>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year
        order by "year" desc
    </select>

    <!-- 地区分布相关查询 -->
    <!-- 地区选择 SQL 片段 -->
    <sql id="selectGroupArea">
        <if test="'orgAddress' == addressType">
            <if test="areaLevel == 1">
                org_city_code as areaCode,
                org_city_name as areaName,
                org_city_name as description,
                2 as areaLevel,
            </if>
            <if test="areaLevel == 2">
                org_district_code as areaCode,
                org_district_name as areaName,
                org_district_name as description,
                3 as areaLevel,
            </if>
            <if test="areaLevel == 3">
                null as  areaCode,
                null as  areaName,
                null as  description,
                4 as areaLevel,
            </if>
        </if>
        <if test="'livingAddress' == addressType">
            <if test="areaLevel == 1">
                living_city_code as areaCode,
                living_city_name as areaName,
                living_city_name as description,
                2 as areaLevel,
            </if>
            <if test="areaLevel == 2">
                living_district_code as areaCode,
                living_district_name as areaName,
                living_district_name as description,
                3 as areaLevel,
            </if>
            <if test="areaLevel == 3">
                null as  areaCode,
                null as  areaName,
                null as  description,
                4 as areaLevel,
            </if>
        </if>
    </sql>

    <sql id="whereGroupArea">
        <if test="'orgAddress' == addressType">
            <if test="areaLevel == 1">
                org_city_code, org_city_name
            </if>
            <if test="areaLevel == 2">
                org_district_code, org_district_name
            </if>
            <if test="areaLevel == 3">
                org_district_code, org_district_name
            </if>
        </if>
        <if test="'livingAddress' == addressType">
            <if test="areaLevel == 1">
                living_city_code, living_city_name
            </if>
            <if test="areaLevel == 2">
                living_district_code, living_district_name
            </if>
            <if test="areaLevel == 3">
                living_district_code, living_district_name
            </if>

        </if>
    </sql>

    <select id="groupArea" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        <include refid="selectGroupArea"/>
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by
        <include refid="whereGroupArea"/>
    </select>

    <select id="groupAreaCurrentLevel" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        <include refid="selectGroupAreaCurrentLevel"/>
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by
        <include refid="whereGroupAreaCurrentLevel"/>
        order by processNewCnt desc
    </select>

    <sql id="selectGroupAreaCurrentLevel">
        <if test="'orgAddress' == addressType ">
            <if test="areaLevel == 1">
                org_province_code  areaCode,
                org_province_name  areaName,
                org_province_name  description,
                1 as areaLevel,
            </if>
            <if test="areaLevel == 2">
                org_city_code  areaCode,
                org_city_name  areaName,
                org_city_name  description,
                2 as areaLevel,
            </if>
            <if test="areaLevel == 3">
                org_district_code  areaCode,
                org_district_name  areaName,
                org_district_name  description,
                3 as areaLevel,
            </if>
        </if>
        <if test="'livingAddress' == addressType ">
            <if test="areaLevel == 1">
                living_province_code  areaCode,
                living_province_name  areaName,
                living_province_name  description,
                1 as areaLevel,
            </if>
            <if test="areaLevel == 2">
                living_city_code  areaCode,
                living_city_name  areaName,
                living_city_name  description,
                2 as areaLevel,
            </if>
            <if test="areaLevel == 3">
                living_district_code  areaCode,
                living_district_name  areaName,
                living_district_name  description,
                3 as areaLevel,
            </if>
        </if>
    </sql>

    <sql id="whereGroupAreaCurrentLevel">
        <if test="'orgAddress' == addressType ">
            <if test="areaLevel == 1">
                org_province_code,
                org_province_name
            </if>
            <if test="areaLevel == 2">
                org_city_code,
                org_city_name
            </if>
            <if test="areaLevel == 3">
                org_district_code  ,
                org_district_name
            </if>
        </if>

        <if test="'livingAddress' == addressType ">
            <if test="areaLevel == 1">
                living_province_code  ,
                living_province_name
            </if>
            <if test="areaLevel == 2">
                living_city_code  ,
                living_city_name
            </if>
            <if test="areaLevel == 3">
                living_district_code  ,
                living_district_name
            </if>
        </if>
    </sql>
    <select id="groupAreaDripDetail" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        <include refid="selectGroupArea"/>
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by
        <include refid="whereGroupArea"/>
        order by processNewCnt desc
    </select>

    <select id="groupAddrType" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        '现住址' as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
    </select>

    <!-- 人群分布相关查询 -->
    <select id="groupAge" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        age_group as patientAgeGroup,
        age_group as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by age_group
        order by age_group
    </select>

    <select id="groupSex" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        gender_name as patientSexName,
        gender_name as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by gender_name
        order by gender_name
    </select>

    <select id="groupJob" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        job_name as patientJob,
        job_name as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by job_name
        order by processNewCnt desc
    </select>

    <!-- 流行动态变化相关查询 -->
    <select id="dayOverAll" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        CAST(day AS DATE) as statDate,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by day
        order by day
    </select>

    <select id="weekOverAll" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year,
        week,
        CONCAT(year, '年', week, '周') as statDate,
        <include refid="sumCommonCnt"/>
        from
        <include refid="weekDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and week = #{item.week})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, week
        order by year, week
    </select>

    <select id="meadowOverAll" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, month, ten_day,
        CONCAT(year, '年', month, '月', ten_day, '旬') as statDate,
        <include refid="sumCommonCnt"/>
        from
        <include refid="tenDaysDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month} and ten_day = #{item.tenDays}::varchar)
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, month, ten_day
        order by year, month, ten_day
    </select>

    <select id="monthOverAll" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, month,
        CONCAT(year, '年', month, '月') as statDate,
        <include refid="sumCommonCnt"/>
        from
        <include refid="monthDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, month
        order by year, month
    </select>

    <select id="quarterOverAll" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, quarter,
        CONCAT(year, '年第', quarter, '季度') as statDate,
        <include refid="sumCommonCnt"/>
        from
        <include refid="quarterDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and quarter = #{item.quarter})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, quarter
        order by year, quarter
    </select>

    <select id="yearOverAll" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year,
        CONCAT(year, '年') as statDate,
        <include refid="sumCommonCnt"/>
        from
        <include refid="yearDb"/>
        where year in
        <foreach collection="dateDims" open="(" close=")" separator="," item="item">
            #{item.year}
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year
        order by year
    </select>

    <!-- 地区变化趋势相关查询 -->
    <select id="dayAreaChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        CAST(day AS DATE) as statDate,
        <include refid="selectGroupArea"/>
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by day, <include refid="whereGroupArea"/>
        order by day, areaCode, processNewCnt desc
    </select>

    <select id="weekAreaChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, week,
        CONCAT(year, '年', week, '周') as statDate,
        <include refid="selectGroupArea"/>
        <include refid="sumCommonCnt"/>
        from
        <include refid="weekDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and week = #{item.week})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, week, <include refid="whereGroupArea"/>
        order by year, week, processNewCnt desc
    </select>

    <select id="meadowAreaChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, month, ten_day,
        CONCAT(year, '年', month, '月', ten_day, '旬') as statDate,
        <include refid="selectGroupArea"/>
        <include refid="sumCommonCnt"/>
        from
        <include refid="tenDaysDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month} and ten_day = #{item.tenDays}::varchar)
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, month, ten_day, <include refid="whereGroupArea"/>
        order by year, month, ten_day, processNewCnt desc
    </select>

    <select id="monthAreaChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, month,
        CONCAT(year, '年', month, '月') as statDate,
        <include refid="selectGroupArea"/>
        <include refid="sumCommonCnt"/>
        from
        <include refid="monthDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, month, <include refid="whereGroupArea"/>
        order by year, month, processNewCnt desc
    </select>

    <select id="quarterAreaChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, quarter,
        CONCAT(year, '年第', quarter, '季度') as statDate,
        <include refid="selectGroupArea"/>
        <include refid="sumCommonCnt"/>
        from
        <include refid="quarterDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and quarter = #{item.quarter})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, quarter, <include refid="whereGroupArea"/>
        order by year, quarter, processNewCnt desc
    </select>

    <select id="yearAreaChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year,
        CONCAT(year, '年') as statDate,
        <include refid="selectGroupArea"/>
        <include refid="sumCommonCnt"/>
        from
        <include refid="yearDb"/>
        where year in
        <foreach collection="dateDims" open="(" close=")" separator="," item="item">
            #{item.year}
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, <include refid="whereGroupArea"/>
        order by year, processNewCnt desc
    </select>

    <!-- 性别变化趋势相关查询 -->
    <select id="daySexChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        CAST(day AS DATE) as statDate,
        gender_name as patientSexName,
        gender_name as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by day, gender_name
        order by day, gender_name, processNewCnt desc
    </select>

    <select id="weekSexChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, week,
        CONCAT(year, '年', week, '周') as statDate,
        gender_name as patientSexName,
        gender_name as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="weekDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and week = #{item.week})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, week, gender_name
        order by year, week, gender_name, processNewCnt desc
    </select>

    <select id="meadowSexChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, month, ten_day,
        CONCAT(year, '年', month, '月', ten_day, '旬') as statDate,
        gender_name as patientSexName,
        gender_name as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="tenDaysDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month} and ten_day = #{item.tenDays}::varchar)
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, month, ten_day, gender_name
        order by year, month, ten_day, gender_name, processNewCnt desc
    </select>

    <select id="monthSexChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, month,
        CONCAT(year, '年', month, '月') as statDate,
        gender_name as patientSexName,
        gender_name as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="monthDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, month, gender_name
        order by year, month, gender_name, processNewCnt desc
    </select>

    <select id="quarterSexChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, quarter,
        CONCAT(year, '年第', quarter, '季度') as statDate,
        gender_name as patientSexName,
        gender_name as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="quarterDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and quarter = #{item.quarter})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, quarter, gender_name
        order by year, quarter, gender_name, processNewCnt desc
    </select>

    <select id="yearSexChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year,
        CONCAT(year, '年') as statDate,
        gender_name as patientSexName,
        gender_name as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="yearDb"/>
        where year in
        <foreach collection="dateDims" open="(" close=")" separator="," item="item">
            #{item.year}
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, gender_name
        order by year, gender_name, processNewCnt desc
    </select>

    <!-- 年龄变化趋势相关查询 -->
    <select id="dayAgeChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        CAST(day AS DATE) as statDate,
        age_group as patientAgeGroup,
        age_group as description,
        split_part(age_group, '-', 2) as ageOrder,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by day, age_group
        order by day, ageOrder, processNewCnt desc
    </select>

    <select id="weekAgeChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, week,
        CONCAT(year, '年', week, '周') as statDate,
        age_group as patientAgeGroup,
        age_group as description,
        split_part(age_group, '-', 2) as ageOrder,
        <include refid="sumCommonCnt"/>
        from
        <include refid="weekDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and week = #{item.week})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, week, age_group
        order by year, week, ageOrder, processNewCnt desc
    </select>

    <select id="meadowAgeChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, month, ten_day,
        CONCAT(year, '年', month, '月', ten_day, '旬') as statDate,
        age_group as patientAgeGroup,
        age_group as description,
        split_part(age_group, '-', 2) as ageOrder,
        <include refid="sumCommonCnt"/>
        from
        <include refid="tenDaysDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month} and ten_day = #{item.tenDays}::varchar)
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, month, ten_day, age_group
        order by year, month, ten_day, ageOrder, processNewCnt desc
    </select>

    <select id="monthAgeChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, month,
        CONCAT(year, '年', month, '月') as statDate,
        age_group as patientAgeGroup,
        age_group as description,
        split_part(age_group, '-', 2) as ageOrder,
        <include refid="sumCommonCnt"/>
        from
        <include refid="monthDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, month, age_group
        order by year, month, ageOrder, processNewCnt desc
    </select>

    <select id="quarterAgeChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, quarter,
        CONCAT(year, '年第', quarter, '季度') as statDate,
        age_group as patientAgeGroup,
        age_group as description,
        split_part(age_group, '-', 2) as ageOrder,
        <include refid="sumCommonCnt"/>
        from
        <include refid="quarterDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and quarter = #{item.quarter})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, quarter, age_group
        order by year, quarter, ageOrder, processNewCnt desc
    </select>

    <select id="yearAgeChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year,
        CONCAT(year, '年') as statDate,
        age_group as patientAgeGroup,
        age_group as description,
        split_part(age_group, '-', 2) as ageOrder,
        <include refid="sumCommonCnt"/>
        from
        <include refid="yearDb"/>
        where year in
        <foreach collection="dateDims" open="(" close=")" separator="," item="item">
            #{item.year}
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, age_group
        order by year, ageOrder, processNewCnt desc
    </select>

    <!-- 环比趋势图相关查询 -->
    <select id="dayQOQTrendChart" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        CAST(day AS DATE) as statDate,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by day
        order by day
    </select>

    <select id="weekQOQTrendChart" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, week,
        CONCAT(year, '年', week, '周') as statDate,
        <include refid="sumCommonCnt"/>
        from
        <include refid="weekDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and week = #{item.week})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, week
        order by year, week
    </select>

    <select id="meadowQOQTrendChart" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, month, ten_day,
        CONCAT(year, '年', month, '月', ten_day, '旬') as statDate,
        <include refid="sumCommonCnt"/>
        from
        <include refid="tenDaysDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month} and ten_day = #{item.tenDays}::varchar)
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, month, ten_day
        order by year, month, ten_day
    </select>

    <select id="monthQOQTrendChart" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, month,
        CONCAT(year, '年', month, '月') as statDate,
        <include refid="sumCommonCnt"/>
        from
        <include refid="monthDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, month
        order by year, month
    </select>

    <select id="quarterQOQTrendChart" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year, quarter,
        CONCAT(year, '年第', quarter, '季度') as statDate,
        <include refid="sumCommonCnt"/>
        from
        <include refid="quarterDb"/>
        where 1=1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and quarter = #{item.quarter})
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year, quarter
        order by year, quarter
    </select>

    <select id="yearQOQTrendChart" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        year,
        CONCAT(year, '年') as statDate,
        <include refid="sumCommonCnt"/>
        from
        <include refid="yearDb"/>
        where year in
        <foreach collection="dateDims" open="(" close=")" separator="," item="item">
            #{item.year}
        </foreach>
        <include refid="whereDistributionAreaMultiChoose"/>
        group by year
        order by year
    </select>

    <!-- 病情分布分析相关查询 -->
    <select id="groupIdentifyClass" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        identify_class,
        identify_class as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        and identify_class is not null
        <include refid="whereDistributionAreaMultiChoose"/>
        group by identify_class
        order by identify_class
    </select>

    <select id="groupOutcomeStatus" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        outcome_status,
        outcome_status as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        and outcome_status is not null
        <include refid="whereDistributionAreaMultiChoose"/>
        group by outcome_status
        order by outcome_status
    </select>

    <select id="groupSymptomFirst" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        symptom_first,
        symptom_first as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        and symptom_first is not null
        <include refid="whereDistributionAreaMultiChoose"/>
        group by symptom_first
        order by processNewCnt desc
    </select>

    <select id="groupHistoryBefore" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        history_before,
        history_before as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        and history_before is not null
        <include refid="whereDistributionAreaMultiChoose"/>
        group by history_before
        order by processNewCnt desc
    </select>

    <select id="groupPathogenResNominal" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        pathogen_res_nominal,
        pathogen_res_nominal as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        and pathogen_res_nominal is not null
        <include refid="whereDistributionAreaMultiChoose"/>
        group by pathogen_res_nominal
        order by pathogen_res_nominal
    </select>

    <!-- 疾病分布分析相关查询 -->
    <select id="groupDiseaseName" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        infect_code as descriptionCode,
        infect_name as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistribution"/>
        <include refid="areaMultiChoose"/>
        group by infect_code, infect_name
        order by infect_code, infect_name
    </select>

    <select id="groupDiseaseCodeAndName" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        infect_code as descriptionCode,
        infect_name as description,
        CONCAT(infect_code, '-', infect_name) as diseaseCodeAndName,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistribution"/>
        <include refid="areaMultiChoose"/>
        group by infect_code, infect_name
        order by processNewCnt desc
    </select>

    <!-- 态势感知相关查询 -->
    <select id="loadAwarenessResult" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.ProcessAwarenessRespVO">
        select
        infect_code as infectCode,
        infect_name as infectName,
        living_province_code as provinceCode,
        living_province_name as provinceName,
        living_city_code as cityCode,
        living_city_name as cityName,
        living_district_code as districtCode,
        living_district_name as districtName,
        sum(report_new_cnt) as newCnt,
        sum(report_dead_cnt) as deadCnt,
        max(day) as latestDate,
        'report_card' as dataSource
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by infect_code, infect_name, living_province_code, living_province,
                 living_city_code, living_city, living_district_code, living_district
        having sum(report_new_cnt) > 0 or sum(report_dead_cnt) > 0
        order by sum(report_new_cnt) desc, sum(report_dead_cnt) desc
    </select>

    <!-- 区域病例数统计相关查询 -->
    <select id="areaMedDateCount" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        <choose>
            <when test="addressType == 'livingAddress'">
                <choose>
                    <when test="areaLevel == 1">
                        living_province_code as areaCode,
                        living_province_name as areaName,
                        living_province_name as description,
                        1 as areaLevel
                    </when>
                    <when test="areaLevel == 2">
                        living_city_code as areaCode,
                        living_city_name as areaName,
                        living_city_name as description,
                        2 as areaLevel
                    </when>
                    <when test="areaLevel == 3">
                        living_district_code as areaCode,
                        living_district_name as areaName,
                        living_district_name as description,
                        3 as areaLevel
                    </when>
                </choose>
            </when>
            <when test="addressType == 'orgAddress'">
                <choose>
                    <when test="areaLevel == 1">
                        org_province_code as areaCode,
                        org_province_name as areaName,
                        org_province_name as description,
                        1 as areaLevel
                    </when>
                    <when test="areaLevel == 2">
                        org_city_code as areaCode,
                        org_city_name as areaName,
                        org_city_name as description,
                        2 as areaLevel
                    </when>
                    <when test="areaLevel == 3">
                        org_district_code as areaCode,
                        org_district_name as areaName,
                        org_district_name as description,
                        3 as areaLevel
                    </when>
                </choose>
            </when>
                        </choose>,
        CAST(day AS DATE) as statDate,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by
        <choose>
            <when test="addressType == 'livingAddress'">
                <choose>
                    <when test="areaLevel == 1">
                        living_province_code, living_province_name
                    </when>
                    <when test="areaLevel == 2">
                        living_city_code, living_city_name
                    </when>
                    <when test="areaLevel == 3">
                        living_district_code, living_district_name
                    </when>
                </choose>
            </when>
            <when test="addressType == 'orgAddress'">
                <choose>
                    <when test="areaLevel == 1">
                        org_province_code, org_province_name
                    </when>
                    <when test="areaLevel == 2">
                        org_city_code, org_city_name
                    </when>
                    <when test="areaLevel == 3">
                        org_district_code, org_district_name
                    </when>
                </choose>
            </when>
        </choose>, day
        order by day, processNewCnt desc
    </select>

</mapper>
