<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.reportcard.mapper.bu.TbCdcMrAttachmentMapper">
    <sql id="Base_Column_List">
        id
        , attachment_name, attachment_path, create_time, update_time, status
    </sql>

    <select id="selectByPrimaryKey" resultType="com.iflytek.cdc.edr.entity.TbCdcAttachment">
        select
        <include refid="Base_Column_List"/>
        from tb_cdcmr_attachment
        where id = #{id,jdbcType=VARCHAR}
    </select>
</mapper>