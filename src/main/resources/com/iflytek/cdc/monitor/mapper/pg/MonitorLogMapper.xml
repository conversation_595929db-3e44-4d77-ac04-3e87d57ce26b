<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.monitor.mapper.pg.MonitorLogMapper">

    <!-- 查询汇总日志 -->
    <select id="selectEpidemicLogSummary" resultType="com.iflytek.cdc.monitor.vo.MonitorEpidemicLogVo">
        select
        org_id,
        org_name,
        province_code,
        province_name,
        city_code,
        city_name,
        district_code,
        district_name,
        sum(inject_cn) as injectCn,
        0 as failCount,
        '0%' as failRate,
        sum(inject_cn) as successCount
        from
        ads.ads_jg_inject_dataload
        where
        1=1
        <if test="orgId != null and orgId != ''">
            and org_id = #{orgId}
        </if>
        <if test="orgName != null and orgName != ''">
            and org_name like CONCAT('%', #{orgName}, '%')
        </if>
        <if test="areaCode != null and areaCode != ''">
            <if test="areaLevel == 1">
                and province_code = #{areaCode}
            </if>
            <if test="areaLevel == 2">
                and city_code = #{areaCode}
            </if>
            <if test="areaLevel == 3">
                and  district_code = #{areaCode}
            </if>
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and  district_code = #{districtCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and  district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="startDate != null">
            and full_date &gt;= cast(#{startDate} as date)
        </if>
        <if test="endDate != null">
            and full_date &lt;= cast(#{endDate} as date)
        </if>
        group by
            org_id,
            org_name,
            province_code,
            province_name,
            city_code,
            city_name,
            district_code,
            district_name
        order by
            org_name
    </select>

    <!-- 查询交互记录 -->
    <select id="queryDataInteractionLogs" resultType="com.iflytek.cdc.monitor.vo.MonitorDataInteractionLogVo">
        select
        report_org_id orgId,
        report_org_name AS orgName,
        '1' AS executeStatus,
        '成功' AS executeStatusName,
        ms_infect_report_id AS reportCardId,
        card_fill_date AS cardFillDate,
        etl_update_datetime AS etlUpdateDatetime,
        dept_name AS departmentName,
        fill_doc_name AS doctorName,
        patient_name AS patientName,
        diag_name AS diagName,
        diag_state_name AS diagStateName,
        visit_datetime AS visitDatetime
        FROM
        ads.v_ads_hn_ms_infect_report
        WHERE
        1=1
        <if test="departmentName != null and departmentName != ''">
            and dept_name like CONCAT('%', #{departmentName}, '%')
        </if>
        <if test="doctorName != null and doctorName != ''">
            and fill_doc_name like CONCAT('%', #{doctorName}, '%')
        </if>
        <if test="orgId != null and orgId != ''">
            and report_org_id = #{orgId}
        </if>
        <if test="startDate != null">
            and card_fill_date &gt;= cast(#{startDate} as date)
        </if>
        <if test="endDate != null">
            and card_fill_date &lt;= cast(#{endDate} as date)
        </if>
        order by
        card_fill_date DESC,ms_infect_report_id
    </select>

    <!-- 查询原始数据 -->
    <select id="selectOriginalData" resultType="com.iflytek.cdc.monitor.vo.MonitorDatilOriginalDataVo">
            SELECT
            patient_name AS patientName,
            patient_identity_no AS patientIdentityNo,
            patient_birth_day AS patientBirthDay,
            patient_nationality_name AS patientNationalityName,
            patient_sex_name AS patientSexName,
            patient_ethnic_name AS patientEthnicName,
            patient_permanent_addr_name AS patientPermanentAddrName,
            permanent_addr_detail AS permanentAddrDetail,
            living_area_name AS livingAreaName,
            living_addr_detail AS livingAddrDetail,
            company AS company,
            patient_marry_name AS patientMarryName,
            patient_contact_name AS patientContactName,
            patient_contact_phone AS patientContactPhone,
            report_org_name AS reportOrgName,
            patient_educate_name AS patientEducateName,


            diag_name AS diagName,
            serial_number AS serialNumber,
            diag_datetime AS diagDatetime,

            suit AS suit,
            disease_history_now AS diseaseHistoryNow,
            physical_examination AS physicalExamination,
            studies_summary_result AS studiesSummaryResult,

            report_org_id AS reportOrgId,

            visit_datetime AS visitDatetime,
            fill_doc_name AS fillDocName,
            dept_name AS deptName,
            infect_type_name AS infectTypeName

            FROM
                ads.v_ads_hn_ms_infect_report
            WHERE
            ms_infect_report_id = #{reportCardId}

    </select>


</mapper>