<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrDataSearchMapper">

   <sql id="criteries">
      <if test="patientIds != null and patientIds.size() > 0">
         and global_patient_id in
         <foreach collection="patientIds" open="(" separator="," close=")" item="id">
            #{id}
         </foreach>
      </if>
   </sql>

   <select id="pageList" resultType="com.iflytek.cdc.province.model.edr.vo.MedicalPatientListInfo">
       select
          global_patient_id AS patientId,
          patient_name AS patientName,
          id_card AS idCard,
          birth_datetime AS birthDate,
          gender_code AS genderCode,
          nationality_code AS nationalityCode,
          ethnicity_code as nation_code,
          domicile_addr_code,
          domicile_addr_province_code,
          domicile_addr_city_code,
          domicile_addr_district_code,
          domicile_addr_street_code,
          domicile_addr_detail,
          current_addr_code,
          current_addr_province_code,
          current_addr_city_code,
          current_addr_district_code,
          current_addr_street_code,
          current_addr_longitude,
          current_addr_latitude,
          current_addr_detail,
          marital_code as marital_status_code,
          --marital_status_name AS maritalStatusName,
          contacts_tel AS contactsTel
      from ads.ads_edr_patient_file
      where 1=1
      <include refid="criteries"/>
       <choose>
           <when test="property != null and property != ''">
                order by ${property} ${direction}
           </when>
           <otherwise>
               order by patient_file_create_time desc
           </otherwise>
       </choose>


   </select>

    <select id="listExposureHistoryInfoByPatientId" resultType="com.iflytek.cdc.province.model.edr.vo.ExposureHistoryInfo">
        SELECT
            --contact_flag_code,
            --contact_flag_name,
            contact_type_code,
            --contact_type_name,
            contact_object_code,
            --contact_object_name,
            contact_datetime as contact_time,
            discovery_mode_code
            --discovery_mode_name
        FROM
            ads.ads_edr_exposure_history
        where global_patient_id = #{patientId}
   </select>

    <select id="listResidenceHistoryInfoByPatientId" resultType="com.iflytek.cdc.province.model.edr.vo.ResidenceHistoryInfo">
        SELECT
            foreign_type_code,
            --foreign_type_name,
            foreign_addr_code,
            foreign_addr_province_code,
            foreign_addr_city_code,
            foreign_addr_district_code,
            foreign_addr_street_code,
            --place_name,
            onset_residence_days as residence_days,
            epidemic_source_risk_code
            --epidemic_source_risk_name
        FROM
            ads.ads_edr_travel_history aeth
        where global_patient_id = #{patientId}
   </select>

    <select id="listClinicalInfos" resultType="com.iflytek.cdc.province.model.edr.vo.ClinicalInfo">
       SELECT
          ameor.visit_datetime  as visit_date,
          --ameor.inital_diagnosis_code as initialDiagnosisCode,
          --ameor.inital_diagnosis_name as initialDiagnosisName,
          ameor.visit_org_code as orgCode,
          ameor.visit_org_name as orgName,
          ameor.event_id as event_id ,
          amehi.admiss_datetime as admissionDate,
          amehi.discharge_datetime as dischargeDate,
          amehi.admiss_diag_code as admissionDiagnosisCode,
          amehi.admiss_diag_name as admissionDiagnosisName,
          amehi.discharge_diag_code as dischargeDiagnosisCode,
          amehi.discharge_diag_name as dischargeDiagnosisName,
          amehi.disease_progression_code as diseaseProgressionCode,
          amehi.disease_progression_name as diseaseProgressionName
        FROM
            ads.ads_ms_edr_outpat_record ameor
        left join ads.ads_ms_edr_hospital_info amehi on amehi.event_id = ameor.event_id
        where ameor.event_id in
        <foreach collection="eventIds" open="(" separator="," close=")" item="eventId">
            #{eventId}
        </foreach>
   </select>

    <select id="loadByPatientId" resultType="com.iflytek.cdc.province.model.edr.vo.MedicalPatientDetailInfo">
      SELECT
          --id,
          --mpi_id,
          amepi.patient_id  as patientId ,
          amepi.patient_name,
          identity_no as id_card,
          birth_day as birth_date,
          sex_code as gender_code,
          sex_name as gender_name,
          nationality_code,
          nationality_name,
          ethnic_code  as nation_code,
          ethnic_name as nation_name,
          permanent_addr_code,
          permanent_addr_detail,
          living_area_code,
          living_area_name,
          living_addr_province_code,
          living_addr_city_code,
          living_addr_district_code,
          living_addr_street_code,
          living_addr_longitude,
          living_addr_latitude,
          living_addr_detail,
          marry_code  as marital_status_code,
          marry_name as marital_status_name,
          guardian_phone as  contacts_tel,
          amedi.dead_datetime  as dead_date,
          amedi.root_dead_reason_code,
          amedi.root_dead_reason_name,
          amedi.direct_dead_reason_code ,
          amedi.direct_dead_reason_name ,
          amedi.indirect_dead_reason_code ,
          amedi.indirect_dead_reason_name ,
          amedi.dead_diag_code ,
          amedi.dead_diag_name
      FROM
          ads.ads_ms_edr_person_info amepi
      left join ads.ads_ms_edr_dead_info amedi on amedi.patient_id  = amepi.patient_id
      where amepi.patient_id = #{patientId}
   </select>

    <select id="listSymptomInfoByEventIds" resultType="com.iflytek.cdc.province.model.edr.vo.SymptomInfo">
     SELECT
        symptom_code,
        symptom_name ,
        symptom_start_datetime as afp_palsy_date,
        event_id
      FROM
          ads.ads_ms_edr_symptom_info amesi
      where amesi.event_id in
        <foreach collection="eventIds" open="(" separator="," close=")" item="eventId">
            #{eventId}
        </foreach>
   </select>

    <select id="listExaminationInfoByEventIds" resultType="com.iflytek.cdc.province.model.edr.vo.ExaminationInfo">
      select
            event_id,
            report_datetime as reportDate,
            physical_exam_item_code as examinationItemCode,
            physical_exam_item_name as examinationItemName,
            physical_exam_result_code as examinationResultCode
            --physical_exam_result_name as  examinationResultName
        from ads.ads_ms_edr_physical_exam_info amepei
       where event_id in
        <foreach collection="eventIds" open="(" separator="," close=")" item="eventId">
            #{eventId}
        </foreach>
   </select>

    <select id="listImagingInfoByEventIds" resultType="com.iflytek.cdc.province.model.edr.vo.ImagingInfo">
      SELECT
        event_id ,
        exam_item_code  as examination_item_code,
        exam_item_name  as examination_item_name,
        exam_org_code  as org_code,
        exam_org_name as org_name,
        exam_qualitative_res_name as examinationResultName,
        month_seq as month
      FROM
          ads.ads_ms_edr_ris_info ameri
       where event_id in
        <foreach collection="eventIds" open="(" separator="," close=")" item="eventId">
            #{eventId}
        </foreach>
   </select>

    <select id="listLabTestInfoByEventIds" resultType="com.iflytek.cdc.province.model.edr.vo.LabTestInfo">
         SELECT
            test_item_code  as examination_item_code,
            test_item_name as examination_item_name,
            sample_no  as specimen_no,
            sample_class_name  as specimen_category,
            sample_datetime as specimen_sampling_date,
            sample_receive_datetime as specimen_receiving_date,
            test_datetime  as  examination_date,
            test_class_code  as examination_method_code,
            test_class_name as examination_method_name,
            test_quantity_res  as examination_quantification,
            test_quantity_res_unit  as examination_quantification_unit,
            test_quantity_res_upper   as examination_quantification_upper,
            test_quantity_res_lower as examination_quantification_lower,
             test_quantity_res_diff_type as  examination_quantification_ri,
            report_datetime  as examination_report_date,
            month_seq as month,
            event_id ,
            confirm_flag
                    --confirm_status_name
        from ads.ads_ms_edr_lis_info
        where event_id in
        <foreach collection="eventIds" open="(" separator="," close=")" item="eventId">
            #{eventId}
        </foreach>
   </select>

    <select id="listDiagnosisResultInfoByEventIds" resultType="com.iflytek.cdc.province.model.edr.vo.DiagnosisResultInfo">
         SELECT
            activity_type_code as visit_type_code,
            activity_type_name as visit_type_name,
            diag_support_class_code  as diagnose_classification_code,
            --diagnose_classification_name,
            diag_state_code as diagnose_state_code,
            diag_state_name as diagnose_state_name,
            diag_datetime as diagnose_time,
            onset_datetime as onset_date,
            diag_std_code  as disease_code,
            diag_std_name as disease_name,
            cases_type_code  as case_type_code,
            cases_type_name as cases_type_name,
            severe_type_code as ncv_severity_code,
            --severe_type_name as ncv_severity_name,
            visit_org_code  as diagnosis_org_code,
            --visit_org_name as  diagnosis_org_name,
            dept_code,
               -- dept_name,
            event_id
        FROM
            ads.ads_ms_edr_confirm_diag_info amecdi
        where event_id in
        <foreach collection="eventIds" open="(" separator="," close=")" item="eventId">
            #{eventId}
        </foreach>
   </select>

    <select id="listTherapeuticDrugInfoByEventIds" resultType="com.iflytek.cdc.province.model.edr.vo.TherapeuticDrugInfo">
         SELECT
            ms_treatment_info_id as edr_treatment_drug_info_id,
            treat_type_code as treatment_type_code,
            treat_type_name as treatment_type_name,
            treat_no as  registration_no,
            treat_code as treatment_shceme_code,
            treat_name as treatment_scheme_name,
            treat_start_datetime as begin_treatment_date,
            treat_end_datetime as stop_treatment_date,
            end_treat_reason_code as end_treatment_reason_code,
            end_treat_reason_name as end_treatment_reason_name,
            non_treat_reason_code as non_treatment_reason_code,
            non_treat_reason_name as non_treatment_reason_name,
            recovery_days,
            treat_class_code as treatment_category_code,
            treat_class_name as treatment_category_name,
            event_id
        FROM
            ads.ads_ms_edr_treatment_info
        where event_id in
        <foreach collection="eventIds" open="(" separator="," close=")" item="eventId">
            #{eventId}
        </foreach>
   </select>

    <select id="listTherapeuticDrugInfoDetailByPatientId" resultType="com.iflytek.cdc.province.model.edr.vo.TherapeuticDrugInfoDetail">
         SELECT
            ms_treatment_info_id as edr_treatment_drug_info_id,
            drug_code,
            drug_name,
            drug_dose_unit_code as drug_dose_code,
            drug_dose_unit_name as drug_dose_name,
            drug_dosage_code,
            drug_dosage_name,
            drug_use_freq as  drug_frequency,
            drug_useway_code as drug_dosage_route_code,
            drug_useway_name as drug_dosage_route_name,
            drug_use_dose_total as drug_given_quantity,
            drug_start_datetime as drug_begin_date,
            drug_end_datetime as  drug_end_date,
            event_id
        FROM
            ads.ads_ms_edr_treatment_detail ametd
        where event_id in
        <foreach collection="eventIds" open="(" separator="," close=")" item="eventId">
            #{eventId}
        </foreach>
   </select>

    <select id="findEdrIdentityByEmpiId" resultType="com.iflytek.cdc.province.model.vo.EdrIdentityVO">
        select
        a.life_id as lifeId,
        a.lv_patient_id as patientId,
        b.archive_id as archiveId
        from ads.ads_ms_life_info a
        left join ads.ads_ms_edr_person_info b on a.life_id = b.life_id
        where 1=1
        <choose>
            <when test="empiId != null and empiId != ''">
                and a.lv_patient_id = #{empiId}
            </when>
            <otherwise>
                <if test="residentIdCard != null and residentIdCard != ''">
                    and a.patient_identity_no = #{residentIdCard}
                </if>
            </otherwise>
        </choose>
        order by a.etl_update_datetime desc, b.etl_update_datetime desc nulls last
        limit 1
    </select>

    <select id="listPatientTransferInfo" resultType="com.iflytek.cdc.province.model.vo.PatientTransferInfoVO">
        SELECT
            patient_name AS patientName,
            admiss_datetime AS transferTime,
            inpat_org_name AS toHospital,
            treatment AS treatmentMeasure,
            admiss_diag_name AS diseaseName
        FROM ads.ads_ms_edr_hospital_info
        <where>
            <if test="startDate != null and endDate != null">
                AND admiss_datetime between #{startDate} and #{endDate}
            </if>
        </where>
    </select>
</mapper>