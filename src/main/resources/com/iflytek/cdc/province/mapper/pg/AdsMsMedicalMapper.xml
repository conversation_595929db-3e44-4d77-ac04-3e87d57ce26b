<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMsMedicalMapper">
    <sql id="areaChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="provinceCode != null and provinceCode != ''">
                    and living_addr_province_code = #{provinceCode}
                </if>
                <if test="cityCode != null and cityCode != ''">
                    and living_addr_city_code = #{cityCode}
                </if>
                <if test="districtCode != null and districtCode != ''">
                    and living_addr_func_district_code = #{districtCode}
                </if>
                <if test="streetCode != null and streetCode != ''">
                    and living_addr_street_code = #{streetCode}
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and living_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and living_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and living_addr_func_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="provinceCode != null and provinceCode != ''">
                    and org_addr_province_code = #{provinceCode}
                </if>
                <if test="cityCode != null and cityCode != ''">
                    and org_addr_city_code = #{cityCode}
                </if>
                <if test="districtCode != null and districtCode != ''">
                    and org_addr_func_district_code = #{districtCode}
                </if>
                <if test="streetCode != null and streetCode != ''">
                    and org_addr_street_code = #{streetCode}
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and org_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and org_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and org_addr_func_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and org_addr_street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="statSql">
        sum(medical_cnt) as medicalCnt,
        sum(medical_cnt_last) as medicalCntLast,
        sum(medical_cnt_last_y) as medicalCntLastY,
        sum(medical_cnt_avg_s) as medicalCntAvgS,

        sum(medical_outpat_cnt) as medicalOutpatCnt,
        sum(medical_outpat_cnt_last) as medicalOutpatCntLast,
        sum(medical_outpat_cnt_last_y) as medicalOutpatCntLastY,
        sum(medical_outpat_cnt_avg_s) as medicalOutpatCntAvgS,

        sum(medical_inpat_cnt) as medicalInpatCnt,
        sum(medical_inpat_cnt_last) as medicalInpatCntLast,
        sum(medical_inpat_cnt_last_y) as medicalInpatCntLastY,
        sum(medical_inpat_cnt_avg_s) as medicalInpatCntAvgS,


        sum(medical_outpat_cnt_heat) as medicalOutpatCntHeat,
        sum(medical_outpat_cnt_heat_last) as medicalOutpatCntHeatLast,
        sum(medical_outpat_cnt_heat_last_y) as medicalOutpatCntHeatLastY,
        sum(medical_outpat_cnt_heat_avg_s) as medicalOutpatCntHeatAvgS,

        sum(medical_outpat_cnt_bowel) as medicalOutpatCntBowel,
        sum(medical_outpat_cnt_bowel_last) as medicalOutpatCntBowelLast,
        sum(medical_outpat_cnt_bowel_last_y) as medicalOutpatCntBowelLastY,
        sum(medical_outpat_cnt_bowel_avg_s) as medicalOutpatCntBowelAvgS

    </sql>
    <select id="medicalVisitStat" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsMedicalVisitStatVO">
        select
            COALESCE(sum(case when visit_type_code is not null then 1 else 0 end), 0) as medicalCnt,
            COALESCE(sum(case when visit_type_code = '4' then 1 else 0 end), 0) as medicalInpatCnt,
            COALESCE(sum(case when visit_type_code = '2' then 1 else 0 end) , 0)as medicalOutpatCnt,
            COALESCE(sum(case when visit_type_code = '2' and heat_flag = '1' then 1 else 0 end), 0) as medicalOutpatCntHeat,
            COALESCE(sum(case when visit_type_code = '2' and bowel_flag = '1' then 1 else 0 end), 0) as medicalOutpatCntBowel
        from
        ads.ads_ms_medical_info
        where rep_card_flag ='0' and visit_time between #{startDate} and #{endDate}
        <include refid="areaChoose" />

    </select>

    <sql id="areaMultiChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and living_addr_province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and living_addr_city_code  in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and living_addr_func_district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and living_addr_street_code  in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and living_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and living_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and living_addr_func_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and org_addr_province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and org_addr_city_code in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and org_addr_func_district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and org_addr_street_code in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and org_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and org_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and org_addr_func_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and org_addr_street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>

    <select id="dayStat" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsMedicalVisitStatVO">
        select
            CAST(day AS DATE)  as statDate,
            <include refid="statSql" />
        from
        ads.ads_ms_medical_d
        where day between #{startDate} and #{endDate}
        <include refid="areaMultiChoose" />
        group by day
        order by day asc
    </select>

    <select id="weekStat" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsMedicalVisitStatVO">
        select
            year,
            week,
        year ',' '年第' ',' week ',' '周' as statDate,
        <include refid="statSql" />
        from
        ads.ads_ms_medical_w
        where monday between #{startDate} and #{endDate}
        <include refid="areaMultiChoose" />
        group by year, week
        order by year asc, week asc
    </select>

    <select id="meadowStat" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsMedicalVisitStatVO">
        select
            year , month , ten_day,
        year ',' '年' ',' month ',' '月' ',' ten_day ',' '旬' as statDate,
            <include refid="statSql" />
        from
        ads.ads_ms_medical_td
        where 1 = 1
        and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month} and ten_day = #{item.meadow})
        </foreach>
        <include refid="areaMultiChoose" />
        group by year, month, ten_day
        order by year asc, month asc, ten_day asc
    </select>

    <select id="monthStat" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsMedicalVisitStatVO">
        select
            year, month,
            year ',' '年' ',' month ',' '月' as statDate,
            <include refid="statSql" />
        from
        ads.ads_ms_medical_m
        where 1 = 1
        and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month})
        </foreach>
        <include refid="areaMultiChoose" />
        group by year, month
        order by year asc, month asc
    </select>

    <select id="quarterStat" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsMedicalVisitStatVO">
        select
            year,quarter,
            year ',' '年第' ',' quarter ',' '季度' as statDate,
            <include refid="statSql" />
        from
            ads.ads_ms_medical_q
        where 1 = 1
        and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and quarter = #{item.quarter})
        </foreach>
        <include refid="areaMultiChoose" />
        group by year,quarter
        order by year asc, quarter asc
    </select>

    <select id="yearStat" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsMedicalVisitStatVO">
        select
        year,
        year ',' '年' as statDate,
        <include refid="statSql" />
        from
        ads.ads_ms_medical_y
        where year in
        <foreach collection="dateDims" open="(" close=")" separator="," item="item">
            #{item.year}
        </foreach>
        <include refid="areaMultiChoose" />
        group by year
        order by year asc
    </select>


    <select id="listMsMedicalInfo" resultType="com.iflytek.cdc.province.entity.ads.AdsMsMedicalInfo">
        select *
        from ads.ads_ms_medical_info
        where event_id in
        <foreach collection="eventIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="dataStat" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsMedicalVisitStatVO">
        select
        cast(visit_time as date) as statDate,
        COALESCE(sum(case when visit_type_code is not null then 1 else 0 end), 0) as medicalCnt,
        COALESCE(sum(case when visit_type_code = '4' then 1 else 0 end), 0) as medicalInpatCnt,
        COALESCE(sum(case when visit_type_code = '2' then 1 else 0 end) , 0)as medicalOutpatCnt,
        COALESCE(sum(case when visit_type_code = '2' and heat_flag = '1' then 1 else 0 end), 0) as medicalOutpatCntHeat,
        COALESCE(sum(case when visit_type_code = '2' and bowel_flag = '1' then 1 else 0 end), 0) as medicalOutpatCntBowel
        from
        ads.ads_ms_medical_info
        where visit_time between #{startDate} and #{endDate}
        <include refid="areaMultiChoose" />
        group by statDate
        order by statDate asc
    </select>


</mapper>