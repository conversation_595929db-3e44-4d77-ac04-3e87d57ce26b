<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrRisInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsEdrRisInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_edr_ris_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="serial_number" jdbcType="TIMESTAMP" property="serialNumber" />
    <result column="timestamp" jdbcType="TIMESTAMP" property="timestamp" />
    <result column="examination_item_code" jdbcType="VARCHAR" property="examinationItemCode" />
    <result column="examination_item" jdbcType="VARCHAR" property="examinationItem" />
    <result column="examination_res" jdbcType="VARCHAR" property="examinationRes" />
    <result column="month" jdbcType="INTEGER" property="month" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="mpi_id" jdbcType="VARCHAR" property="mpiId" />
    <result column="update_org_name" jdbcType="VARCHAR" property="updateOrgName" />
    <result column="update_org" jdbcType="VARCHAR" property="updateOrg" />
    <result column="create_org" jdbcType="VARCHAR" property="createOrg" />
    <result column="create_org_name" jdbcType="VARCHAR" property="createOrgName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, etl_create_datetime, etl_update_datetime, event_id, empi_id, org_code, org_id, 
    org_name, serial_number, "timestamp", examination_item_code, examination_item, examination_res, 
    "month", create_user, create_time, update_user, update_time, source_id, mpi_id, update_org_name, 
    update_org, create_org, create_org_name
  </sql>

  <select id="getRisByEventId" parameterType="string"
          resultType="com.iflytek.cdc.province.model.vo.edrcase.AdsEdrRisInfoVO">

    select
    id,
    etl_create_datetime,
    etl_update_datetime,
    event_id,
    empi_id,
    org_code,
    org_id,
    org_name,
    serial_number,
    timestamp,
    examination_item_code,
    examination_item,
    examination_res,
    month
    from ads.ads_edr_ris_info
    where event_id = #{eventId}
    order by  timestamp desc
  </select>
</mapper>