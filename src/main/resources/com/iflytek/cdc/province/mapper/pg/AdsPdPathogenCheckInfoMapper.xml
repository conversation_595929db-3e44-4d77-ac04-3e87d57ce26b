<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsPdPathogenCheckInfoMapper">

    <select id="getPatientPathogenList" resultType="com.iflytek.cdc.province.model.vo.PatientMedicalInfoVO">
        select
        sample_time as visitDay,
        id as dataId
        from ads.ads_pd_pathogen_check_info
        where patient_id = #{patientId}
        order by sample_time desc
    </select>

    <select id="getPathogenInfectionSituation"
            resultType="com.iflytek.cdc.province.model.pathogen.PathogenInfectionSituationVO">
        with tmp as (
            select
                process_id,
                pathogen_name,
                pneumonia_flag,
                count(pathogen_name) over(partition by process_id) as pathogenCount
            from ads.ads_pd_pathogen_check_info
            where 1=1
            <if test="sourceKeyList != null and sourceKeyList.size() &gt; 0">
                and process_id in
                <foreach collection="sourceKeyList" separator="," index="index" item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
            <if test="pathogenNameList != null and pathogenNameList.size() &gt; 0">
                and pathogen_name in
                <foreach collection="pathogenNameList" separator="," index="index" item="item" close=")" open="(">
                    #{item}
                </foreach>
            </if>
        )
        SELECT
            t.pathogen_name,
            t.pneumonia_flag                                          as isPneumonia,
            COUNT(DISTINCT t.process_id)                              as total,
            COUNT(CASE WHEN pathogenCount = 1 THEN t.process_id END)  as singleInfection,
            COUNT(CASE WHEN pathogenCount = 2 THEN t.process_id END)  as doubleInfection,
            COUNT(CASE WHEN pathogenCount = 3 THEN t.process_id END)  as tripleInfection,
            COUNT(CASE WHEN pathogenCount = 4 THEN t.process_id END)  as quadrupleInfection
        FROM tmp t
        GROUP BY t.pathogen_name, t.pneumonia_flag
    </select>

    <sql id="chooseProcessTable">
        <if test="warningType == 'multichannel'">
            ads.ads_mul_process_info
        </if>
        <if test="warningType == 'emerging'">
            ads.ads_ms_emerging_process_info
        </if>
        <!-- 综合预警暂时使用多渠道，后续数仓表结构出来替换-->
        <if test="warningType == 'integrated'">
            ads.ads_mul_process_info
        </if>
    </sql>

    <sql id="chooseProcessId">
        <if test="warningType == 'multichannel'">
            i.mul_process_id
        </if>
        <if test="warningType == 'emerging'">
            i.infect_process_id
        </if>
        <!-- 综合预警暂时使用多渠道，后续数仓表结构出来替换-->
        <if test="warningType == 'integrated'">
            ads.mul_process_id
        </if>
    </sql>

    <select id="listPositivePathogenCheckResult"
            resultType="com.iflytek.cdc.province.model.vo.PathogenCheckVO">
        select
            <include refid="chooseProcessId"/> as processId,
            r.pathogen_name
        from <include refid="chooseProcessTable"/> i
        left join ads.ads_pd_pathogen_check_info r
        on <include refid="chooseProcessId"/> = r.process_id
        where <include refid="chooseProcessId"/> in
        <foreach collection="sourceKeyList" separator="," index="index" item="item" close=")" open="(">
            #{item}
        </foreach>
        <if test="isPneumonia != null and isPneumonia != ''">
            and i.pneumonia_flag = #{isPneumonia}
        </if>
    </select>

</mapper>