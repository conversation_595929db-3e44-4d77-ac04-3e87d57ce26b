<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsFsEdrInfoMapper">

    <sql id="Base_Column_List">
        life_id, model_id, model_name, permit_living_area_code, permit_company_area_code, permit_org_area_code,
        content_json, etl_create_datetime, etl_update_datetime, archive_id, delete_flag
    </sql>

    <sql id="permitCriteria">
        and (exists(
        select 1 from dim.dim_region_nation n
        where i.permit_living_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>)
        or
        exists(
        select 1 from dim.dim_region_nation n
        where i.permit_company_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>)
        or exists(
        select 1 from dim.dim_region_nation n
        where i.permit_org_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>))
    </sql>

    <select id="getTotalCount" resultType="java.lang.Integer">
        select coalesce(count(*), 0)
        from ads.ads_fs_edr_info
        where delete_flag = '0'
    </select>

    <select id="getPermitCodeById" resultType="com.iflytek.cdc.province.model.edr.vo.CommonRecordLogVO">
        select
        permit_living_area_code,
        permit_company_area_code,
        permit_org_area_code
        from ads.ads_fs_edr_info
        where archive_id = #{id}
    </select>

    <select id="getEdrInfoBy" resultType="com.iflytek.cdc.province.entity.ads.AdsFsEdrInfo">
        select <include refid="Base_Column_List"/>
        from ads.ads_fs_edr_info i
        where i.archive_id = #{id}
        <include refid="permitCriteria"/>
    </select>

</mapper>