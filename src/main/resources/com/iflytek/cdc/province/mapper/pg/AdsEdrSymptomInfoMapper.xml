<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrSymptomInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsEdrSymptomInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_edr_symptom_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="patient_id" jdbcType="VARCHAR" property="patientId" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="serial_number" jdbcType="TIMESTAMP" property="serialNumber" />
    <result column="afp_palsy_date" jdbcType="TIMESTAMP" property="afpPalsyDate" />
    <result column="symptom_code" jdbcType="VARCHAR" property="symptomCode" />
    <result column="current_addr_code" jdbcType="VARCHAR" property="currentAddrCode" />
    <result column="current_addr" jdbcType="VARCHAR" property="currentAddr" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="mpi_id" jdbcType="VARCHAR" property="mpiId" />
    <result column="update_org_name" jdbcType="VARCHAR" property="updateOrgName" />
    <result column="update_org" jdbcType="VARCHAR" property="updateOrg" />
    <result column="create_org" jdbcType="VARCHAR" property="createOrg" />
    <result column="create_org_name" jdbcType="VARCHAR" property="createOrgName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, etl_create_datetime, etl_update_datetime, event_id, patient_id, empi_id, serial_number, 
    afp_palsy_date, symptom_code, current_addr_code, current_addr, create_user, create_time, 
    update_user, update_time, source_id, mpi_id, update_org_name, update_org, create_org, 
    create_org_name
  </sql>


  <select id="getSymptomByEventId" parameterType="string" resultType="com.iflytek.cdc.province.model.vo.edrcase.AdsEdrSymptomInfoVO">
    select
    distinct
    etl_create_datetime,
    etl_update_datetime,
    event_id ,
    patient_id,
    empi_id,
    serial_number,
    symptom_code,
    symptom_name,
    afp_palsy_date
    from ads.ads_edr_symptom_info
    where event_id = #{eventId}
    order by serial_number desc

  </select>


</mapper>