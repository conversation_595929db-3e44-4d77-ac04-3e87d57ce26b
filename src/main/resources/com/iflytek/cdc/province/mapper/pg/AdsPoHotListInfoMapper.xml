<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsPoHotListInfoMapper">

    <sql id="areaCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and city_code = #{cityCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="topicCriteria">
        <if test="topicKeywords != null and topicKeywords.size() != 0">
            and key_word in (
            <foreach collection="topicKeywords" item="item" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
    </sql>

    <select id="selectKeywordCountBy"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorKeywordVO">
        select
            key_word as keyword,
            count(1) as count,
            count(1) as record_count,
            count(distinct channel) as platform_count,
            sum(continue_h) as listingRecordDuration
        from ads.ads_po_hot_list_info
        where day between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
        <if test="keyword != null and keyword != ''">
            and key_word = #{keyword,jdbcType=VARCHAR}
        </if>
        <include refid="areaCriteria"/>
        <include refid="topicCriteria"/>
        group by key_word
    </select>

    <select id="searchKeywordSituation"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorKeywordVO">
        select
            count(1) as count,
            count(1) as record_count,
            count(distinct channel) as platform_count,
            sum(continue_h) as listingRecordDuration
        from ads.ads_po_hot_list_info
        where day between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
        <if test="keyword != null and keyword != ''">
            and key_word = #{keyword,jdbcType=VARCHAR}
        </if>
        <include refid="areaCriteria"/>
        <include refid="topicCriteria"/>
    </select>
</mapper>