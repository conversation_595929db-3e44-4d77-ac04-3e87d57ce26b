<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsPoInfectTransmitInfoMapper">

    <sql id="areaCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and city_code = #{cityCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="selectIncidentCountBy"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.IncidentCaseNoticeVO">
        select
            day as curr,
            sum(new_confirm_inport_cnt) as curr_count
        from ads.ads_po_infect_transmit_info
        where day between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
        <if test="keyword != null and keyword != ''">
            and std_word = #{keyword}
        </if>
        <include refid="areaCriteria"/>
        group by day
        order by day
    </select>
</mapper>