<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.EmergingProcessInfoMapper">

    <select id="listMedCntIndicator" resultType="com.iflytek.cdc.province.model.vo.MedCntIndicatorVO">
        select
        CAST(<include refid="emergingTimeChoose" /> AS DATE) as stat_date,
        count(*) as medCaseCnt
        from ads.ads_ms_emerging_process_info i
        where 1= 1
        <include refid="emergingMedCntCommonCriteria"/>
        <!-- 病例过滤 -->
        <include refid="emergingOrgAddressCriteria"/>
        <include refid="emergingProcessCriteria"/>
        <include refid="emergingProcessOtherCriteria"/>
        <if test="startDate != null and endDate != null">
            and <include refid="emergingTimeChoose" /> between #{startDate} and #{endDate}
        </if>
        group by stat_date order by stat_date
    </select>

    <select id="listOutcomeCntIndicator" resultType="com.iflytek.cdc.province.model.vo.MedCntIndicatorVO">
        select
        CAST(lo_outcome_time AS DATE) as stat_date,
        sum(case when lo_outcome_status = '治愈' then 1 else 0 end) as recoverCnt ,
        sum(case when lo_outcome_status = '死亡' then 1 else 0 end) as deathCnt,
        sum(case when end_flag = '0' then 1 else 0 end) as existingCaseCnt ,
        sum(case when lo_dead_reason = '因该病死亡' then 1 else 0 end) as diseaseDeathCnt
        from ads.ads_ms_emerging_process_info i
        where 1=1
        <include refid="emergingMedCntCommonCriteria"/>
        <!-- 病例过滤 -->
        <include refid="emergingOrgAddressCriteria"/>
        <include refid="emergingProcessCriteria"/>
        <include refid="emergingProcessOtherCriteria"/>
        <if test="startDate != null and endDate != null">
            and lo_outcome_time between #{startDate} and #{endDate}
        </if>
        and lo_outcome_time is not null
        group by stat_date order by stat_date
    </select>

    <select id="listAreaMedCntIndicator" resultType="com.iflytek.cdc.province.model.vo.AreaMedCntIndicatorVO">
        select
        CAST(<include refid="emergingTimeChoose" /> AS DATE) as stat_date,
        count(*) as medCaseCnt,
        <include refid="emergingAreaChoose" />
        from ads.ads_ms_emerging_process_info i
        where 1= 1
        <include refid="emergingMedCntCommonCriteria"/>
        <if test="startDate != null and endDate != null">
            and <include refid="emergingTimeChoose" /> between #{startDate} and #{endDate}
        </if>
        group by stat_date, areaLevel, areaCode, areaName order by stat_date
    </select>

    <select id="listAreaOutcomeCntIndicator"
            resultType="com.iflytek.cdc.province.model.vo.AreaMedCntIndicatorVO">
        select
        CAST(lo_outcome_time AS DATE) as stat_date,
        sum(case when lo_outcome_status = '治愈' then 1 else 0 end) as recoverCnt ,
        sum(case when lo_outcome_status = '死亡' then 1 else 0 end) as deathCnt,
        sum(case when end_flag = '0' then 1 else 0 end) as existingCaseCnt ,
        sum(case when lo_dead_reason = '因该病死亡' then 1 else 0 end) as diseaseDeathCnt,
        <include refid="emergingAreaChoose"/>
        from ads.ads_ms_emerging_process_info i
        where lo_outcome_time is not null
        <include refid="emergingMedCntCommonCriteria"/>
        <if test="startDate != null and endDate != null">
            and lo_outcome_time between #{startDate} and #{endDate}
        </if>
        group by stat_date, areaLevel, areaCode, areaName order by stat_date
    </select>

    <select id="listAddressByIds" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO">
        select
        infect_process_id as id,
        <include refid="emergingAddressMap"/>
        from ads.ads_ms_emerging_process_info i
        where infect_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
        <!-- 病例过滤 -->
        <include refid="emergingOrgAddressCriteria"/>
        <include refid="emergingProcessCriteria"/>
        <include refid="emergingProcessOtherCriteria"/>
    </select>

    <select id="listSimpleInfo" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO">
        select
        <include refid="emergingSimpleInfoMap"/>,
        patient_name AS patientName,
        patient_identity_no AS idCard
        from ads.ads_ms_emerging_process_info i
        where  1 =1
        <if test="ids != null and ids.size() > 0">
            and infect_process_id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and infect_process_id = #{id}
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            and fi_inf_infect_name = #{diseaseName}
        </if>
        <include refid="emergingOrgAddressCriteria"/>
        <include refid="emergingProcessCriteria"/>
        <include refid="emergingProcessOtherCriteria"/>
        <include refid="emergingOrder"/>
    </select>

    <select id="loadProcessSimpleById"
            resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoDetailVO">
        select
        <include refid="emergingSimpleInfoMap"/>,
        patient_name AS patientName,
        patient_identity_no AS idCard,
        patient_birth_day AS birthDate,
        patient_sex_name AS genderName,
        lv_living_addr_detail  AS livingAddrDetail,
        lv_living_addr_province_code AS livingAddrProvinceCode,
        lv_living_addr_province AS livingAddrProvinceName,

        lv_living_addr_city_code AS livingAddrCityCode,
        lv_living_addr_city AS livingAddrCityName,

        lv_living_addr_func_district_code AS livingAddrDistrictCode,
        lv_living_addr_func_district AS livingAddrDistrictName,

        lv_living_addr_street_code AS livingAddrStreetCode,
        lv_living_addr_street AS livingAddrStreet,


        lv_living_addr_street_longitude AS livingAddrLongitude,
        lv_living_addr_street_latitude AS livingAddrLatitude
        from ads.ads_ms_emerging_process_info i
        where infect_process_id = #{id}
    </select>

    <select id="listPatientInfoByProcessIds" resultType="com.iflytek.cdc.province.model.vo.MsPatientInfoVO">
        select
        infect_process_id as id,
        patient_name as patientName,
        patient_sex_name as patientSexName,
        fi_inf_patient_age as patientAge,
        empi_id as patientId,
        fi_inf_event_id as eventId,
        event_json,
        fi_inf_job as job
        from ads.ads_ms_emerging_process_info i
        where infect_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
        <!-- 病例过滤 -->
        <include refid="emergingOrgAddressCriteria"/>
        <include refid="emergingProcessCriteria"/>
        <include refid="emergingProcessOtherCriteria"/>
    </select>

    <select id="countEmerging" resultType="java.lang.Integer">
        select
        count(*)
        from ads.ads_ms_emerging_process_info i
        where infect_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="listProcessModelSimpleInfo"
            resultType="com.iflytek.cdc.province.model.vo.EmergingProcessModelSimpleInfo">
        select
        infect_process_id as process_id,
        patient_sex_name  as patientSexName,
        lv_living_addr_province_code as livingAddressProvinceCode,
        lv_living_addr_province as livingAddressProvinceName,
        lv_living_addr_city_code as livingAddressCityCode,
        lv_living_addr_city as livingAddressCityName,
        lv_living_addr_func_district_code as livingAddressDistrictCode,
        lv_living_addr_func_district as livingAddressDistrictName,
        lv_living_addr_street_code as livingAddressStreetCode,
        lv_living_addr_street as livingAddressStreetName,
        lv_living_addr_detail_std as currentAddress,
        lv_person_type as personType,
        lv_school_type  as schoolType,
        lv_company_province_code  as schoolAddressProvinceCode,
        lv_company_province  as schoolAddressProvinceName,
        lv_company_city_code  as schoolAddressCityCode,
        lv_company_city  as schoolAddressCityName,
        lv_company_func_district_code  as schoolAddressDistrictCode,
        lv_company_func_district  as schoolAddressDistrictName,
        lv_company_street_code  as schoolAddressStreetCode,
        lv_company_street  as schoolAddressStreetName,
        lv_company_addr_detail_std as schoolAddress,
        fi_inf_org_name as firstDiagnosisOrg,
        fi_pathogen_res_type  as firstPathogenTestResult,
        lv_pathogen_res_type  as lastPathogenTestResult,
        lo_outcome_status as outCome,
        lo_dead_reason as deadReason,
        li_inf_infect_type as diseaseType,
        li_inf_infect_name as diseaseName,
        fi_syn_main_diag_class  as diagnosisType,
        lv_visit_time as diagnosisTime,
        li_inf_infect_class as diagnosisStatus
        from ads.ads_ms_emerging_process_info i
        where infect_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="listProcessLog" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO">
        select
        occur_time as date,
        label_type as type,
        label_value as value,
        case when source_desc = '传染病识别' then 'infected' else '' end as specialMark
        from ads.ads_ms_infect_process_view
        where delete_flag = '0' and infect_process_id = #{id}
        <if test="type !=null">
            and label_type = #{type}
        </if>
        order by occur_time
    </select>

    <select id="getProcessInfoBy"
            resultType="com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo">
        select
        infect_process_id as id,
        patient_name as patient_name,
        patient_sex_name as patient_sex_name,
        patient_birth_day as birth_day,
        fi_inf_patient_age as patient_age,
        fi_inf_patient_age_unit as patient_age_unit,
        fi_inf_job as job,
        fi_inf_person_type as person_type,

        fi_inf_living_addr_detail AS living_addr_detail,
        fi_inf_living_addr_province_code AS living_province_code,
        fi_inf_living_addr_province AS living_province_name,
        fi_inf_living_addr_city_code AS living_city_code,
        fi_inf_living_addr_city AS living_city_name,
        fi_inf_living_addr_district_code AS living_district_code,
        fi_inf_living_addr_district AS living_district_name,
        fi_inf_living_addr_street_code AS living_street_code,
        fi_inf_living_addr_street AS living_street_name,
        fi_inf_living_addr_longitude AS living_addr_longitude,
        fi_inf_living_addr_latitude AS living_addr_latitude,

        fi_inf_company AS company,
        fi_inf_company_province_code AS company_province_code,
        fi_inf_company_province AS company_province_name,
        fi_inf_company_city_code AS company_city_code,
        fi_inf_company_city AS company_city_name,
        fi_inf_company_district_code AS company_district_code,
        fi_inf_company_district AS company_district_name,
        fi_inf_company_street_code AS company_street_code,
        fi_inf_company_street AS company_street_name,
        fi_inf_company_longitude AS company_addr_longitude,
        fi_inf_company_latitude AS company_addr_latitude,

        fi_inf_org_id AS org_id,
        fi_inf_org_name AS org_name,
        fi_inf_org_addr_province_code AS org_province_code,
        fi_inf_org_addr_province AS org_province_name,
        fi_inf_org_addr_city_code AS org_city_code,
        fi_inf_org_addr_city AS org_city_name,
        fi_inf_org_addr_district_code AS org_district_code,
        fi_inf_org_addr_district AS org_district_name,
        fi_inf_org_addr_street_code AS org_street_code,
        fi_inf_org_addr_street AS org_street_name,
        fi_inf_org_longitude AS org_addr_longitude,
        fi_inf_org_latitude AS org_addr_latitude,

        fi_inf_onset_time as onset_time,
        fi_inf_visit_time as first_visit_time,
        fi_inf_org_id as first_visit_org_id,
        fi_inf_org_name as first_visit_org_name,
        fi_inf_identify_time as diagnose_time,
        'emerging' as disease_type,
        fi_inf_infect_code as disease_code,
        fi_inf_infect_name as disease_name,
        lo_outcome_status as outcome_status,
        lo_outcome_time as outcome_time,
        lo_dead_time as death_time,
        severe_flag as severe_flag,
        li_inf_infect_out_flag as out_flag,
        'emerging' as process_type
        from ads.ads_ms_emerging_process_info
        where infect_process_id in
        <foreach collection="ids" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getProcessPathogenInfoBy" resultType="com.iflytek.cdc.province.model.vo.ProcessPathogenInfoVO">
        select
        sum(case when pathogen_flag = '1' then 1 else 0 end) as totalTests,
        sum(case when pathogen_flag = '1' and pathogen_name is not null then 1 else 0 end) as positiveCnt,
        sum(case when pathogen_flag = '1' and pathogen_name is null then 1 else 0 end) as negativeCnt
        from ads.ads_ms_emerging_process_info
        where infect_process_id in
        <foreach collection="ids" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <sql id="emergingTimeChoose">
        <choose>

            <!--        TODO: 数仓此字段已删除    <when test="timeType == 'approveTime'">-->
            <!--                 fi_inf_check_time-->
            <!--            </when>-->
            <when test="timeType == 'identifyTime'">
                fi_inf_identify_time
            </when>
            <otherwise>
                fi_inf_visit_day
            </otherwise>
        </choose>
    </sql>

    <sql id="emergingMedCntCommonCriteria">
        <if test="ids != null and ids.size() > 0">
            and infect_process_id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and infect_process_id =  #{id}
        </if>
        <if test="fvOrgId != null and fvOrgId != ''">
            and fi_inf_org_id = #{fvOrgId}
        </if>
        <if test="fvOrgName != null and fvOrgName != ''">
            and fi_inf_org_name like CONCAT('%', #{fvOrgName}, '%')
        </if>
        <include refid="emergingAreaChooseCriteria" />
        <if test="diseaseName != null and diseaseName != ''">
            and fi_inf_infect_name = #{diseaseName}
        </if>
    </sql>

    <sql id="emergingOrgAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and fi_inf_org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and fi_inf_org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and fi_inf_org_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and fi_inf_org_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and fi_inf_org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and fi_inf_org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and fi_inf_org_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="emergingAreaChooseCriteria">
        <choose>
            <when test="addressType == 'livingAddress'">
                <include refid="emergingLivingAddressCriteria"/>
            </when>
            <otherwise>
                <include refid="emergingOrgAddressCriteria"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="emergingProcessCriteria">
        <if test="fvOrgId != null and fvOrgId != ''">
            and fi_inf_org_id = #{fvOrgId}
        </if>
        <if test="fvOrgName != null and fvOrgName != ''">
            and fi_inf_org_name like CONCAT('%', #{fvOrgName}, '%')
        </if>
        <if test="queryKey != null and queryKey !=''">
            and (infect_process_id like CONCAT('%', #{queryKey}, '%') or fi_inf_org_name like CONCAT('%', #{queryKey}, '%'))
        </if>
        <if test="job != null and job != ''">
            and fi_inf_job = #{job}
        </if>
        <if test="company != null and company != ''">
            and fi_inf_company like CONCAT('%', #{company}, '%')
        </if>
        <if test="patientSexName != null and patientSexName != ''">
            and patient_sex_name = #{patientSexName}
        </if>
        <if test="visitStartDate != null">
            and fi_inf_visit_day >= #{visitStartDate}
        </if>
        <if test="visitEndDate != null">
            and fi_inf_visit_day &lt;= #{visitEndDate}
        </if>
        <if test="mainDiag != null and mainDiag != ''">
            and fi_inf_main_diag like CONCAT('%', #{mainDiag}, '%')
        </if>
        <if test="diagnoseStatus != null and diagnoseStatus != ''">
            and li_inf_infect_class = #{diagnoseStatus}
        </if>
        <if test="onsetStartDate != null">
            and fi_inf_onset_time >= #{onsetStartDate}
        </if>
        <if test="onsetEndDate != null">
            and fi_inf_onset_time &lt;= #{onsetEndDate}
        </if>
    </sql>

    <sql id="emergingProcessOtherCriteria">
        <!-- 地址选择 -->
        <if test="livingProvinceCode != null and livingProvinceCode != ''">
            and fi_inf_living_addr_province_code = #{livingProvinceCode}
        </if>
        <if test="livingCityCode != null and livingCityCode != ''">
            and fi_inf_living_addr_city_code = #{livingCityCode}
        </if>
        <if test="livingDistrictCode != null and livingDistrictCode != ''">
            and fi_inf_living_addr_district_code = #{livingDistrictCode}
        </if>
        <if test="companyProvinceCode != null and companyProvinceCode != ''">
            and fi_inf_company_province_code = #{companyProvinceCode}
        </if>
        <if test="companyCityCode != null and companyCityCode != ''">
            and fi_inf_company_city_code = #{companyCityCode}
        </if>
        <if test="companyDistrictCode != null and companyDistrictCode != ''">
            and fi_inf_company_district_code = #{companyDistrictCode}
        </if>
        <!-- 年龄筛选 -->
        <if test="ageMin != null">
            and
            case
            when fi_inf_patient_age_unit = '岁' or fi_inf_patient_age_unit is null then fi_inf_patient_age
            when fi_inf_patient_age_unit = '月' then fi_inf_patient_age / 12.0
            when fi_inf_patient_age_unit = '天' then fi_inf_patient_age / 365.0
            else -1
            end >= #{ageMin}
        </if>
        <if test="ageMax != null">
            and
            case
            when fi_inf_patient_age_unit = '岁' or fi_inf_patient_age_unit is null then fi_inf_patient_age
            when fi_inf_patient_age_unit = '月' then fi_inf_patient_age / 12.0
            when fi_inf_patient_age_unit = '天' then fi_inf_patient_age / 365.0
            else 9999
            end &lt;= #{ageMax}
        </if>
        <!-- 病原监测 -->
        <if test="firstPathogenResult != null and firstPathogenResult != ''">
            and fi_pathogen_res_type = #{firstPathogenResult}
        </if>
        <if test="lastPathogenResult != null and lastPathogenResult != ''">
            and lv_pathogen_res_type = #{lastPathogenResult}
        </if>
    </sql>

    <sql id="emergingLivingAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and fi_inf_living_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and fi_inf_living_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and fi_inf_living_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and fi_inf_living_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and fi_inf_living_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and fi_inf_living_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and fi_inf_living_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="emergingAreaChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <include refid="emergingLivingAreaChoose"/>
            </when>
            <otherwise>
                <include refid="emergingOrgAreaChoose"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="emergingLivingAreaChoose">
        <if test="areaLevel == 1">
            2 as areaLevel,
            fi_inf_living_addr_city_code as areaCode,
            fi_inf_living_addr_city as areaName

        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            3 as areaLevel,
            fi_inf_living_addr_func_district_code as areaCode,
            fi_inf_living_addr_func_district_code as areaName
        </if>
    </sql>

    <sql id="emergingOrgAreaChoose">
        <if test="areaLevel == 1">
            2 as areaLevel,
            fi_inf_org_addr_city_code as areaCode,
            fi_inf_org_addr_city as areaName

        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            3 as areaLevel,
            fi_inf_org_addr_func_district_code as areaCode,
            fi_inf_org_addr_func_district as areaName
        </if>
    </sql>
    <sql id="emergingAddressMap">
        fi_inf_living_addr_detail as fv_living_addr_detail ,
        fi_inf_living_addr_detail_std as fv_living_addr_detail_std,
        fi_inf_living_addr_street_longitude as fv_living_addr_street_longitude ,
        fi_inf_living_addr_street_latitude as  fv_living_addr_street_latitude ,
        fi_inf_company  as fv_company ,
        fi_inf_company_addr_detail_std as fv_company_addr_detail_std ,
        fi_inf_company_longitude  as fv_company_longitude ,
        fi_inf_company_latitude  as fv_company_latitude ,
        fi_inf_org_name  as fv_org_name ,
        fi_inf_org_longitude  as fv_org_longitude ,
        fi_inf_org_latitude  as fv_org_latitude ,
        lv_living_addr_detail ,
        lv_living_addr_detail_std,
        lv_living_addr_longitude ,
        lv_living_addr_latitude ,
        lv_company ,
        lv_company_addr_detail_std ,
        lv_company_longitude ,
        lv_company_latitude ,
        lv_org_name ,
        lv_org_longitude ,
        lv_org_latitude
    </sql>

    <sql id="emergingSimpleInfoMap">
        event_json,
        empi_id as patient_id,
        infect_process_id as  id,
        CASE
            WHEN COALESCE(fi_inf_living_addr_province_code, '') = COALESCE(fi_inf_org_addr_province_code, '')
                THEN fi_inf_living_addr_province_code
            ELSE fi_inf_org_addr_province_code
            END AS provinceCode,
        CASE
            WHEN COALESCE(fi_inf_living_addr_province_code, '') = COALESCE(fi_inf_org_addr_province_code, '')
                THEN fi_inf_living_addr_province
            ELSE fi_inf_org_addr_province
            END AS province,
        CASE
            WHEN COALESCE(fi_inf_living_addr_province_code, '') = COALESCE(fi_inf_org_addr_province_code, '')
                THEN fi_inf_living_addr_city_code
            ELSE fi_inf_org_addr_city_code
            END AS cityCode,
        CASE
            WHEN COALESCE(fi_inf_living_addr_province_code, '') = COALESCE(fi_inf_org_addr_province_code, '')
                THEN fi_inf_living_addr_city
            ELSE fi_inf_org_addr_city
            END AS city,
        CASE
            WHEN COALESCE(fi_inf_living_addr_province_code, '') = COALESCE(fi_inf_org_addr_province_code, '')
                THEN fi_inf_living_addr_func_district_code
            ELSE fi_inf_org_addr_func_district_code
            END AS funcDistrictCode,
        CASE
            WHEN COALESCE(fi_inf_living_addr_province_code, '') = COALESCE(fi_inf_org_addr_province_code, '')
                THEN fi_inf_living_addr_func_district
            ELSE fi_inf_org_addr_func_district
            END AS funcDistrict,
        CASE
            WHEN COALESCE(fi_inf_living_addr_province_code, '') = COALESCE(fi_inf_org_addr_province_code, '')
                THEN fi_inf_living_addr_street_code
            ELSE fi_inf_org_addr_street_code
            END AS streetCode,
        CASE
            WHEN COALESCE(fi_inf_living_addr_province_code, '') = COALESCE(fi_inf_org_addr_province_code, '')
                THEN fi_inf_living_addr_street
            ELSE fi_inf_org_addr_street
        END AS street,
        fi_inf_org_id as fvOrgId,
        fi_inf_org_name as fvOrgName,
        fi_inf_identify_time as firstIdentifyTime,
        li_inf_infect_name as diseaseName,
        li_inf_infect_code as diseaseCode,
        li_inf_infect_class as diagnoseStatus,
        fi_inf_identify_time as identifyTime,
        lv_living_addr_detail_std as livingAddrStd,
        fi_inf_company  as company,
        patient_sex_name as patient_sex_name ,
        fi_inf_patient_age as patientAge,
        lo_outcome_status as outComeStatus,
        lo_outcome_time as outComeTime,
        fi_inf_job  as job,
        fi_inf_onset_time as onsetTime,
        fi_inf_visit_time as visitTime,
        fi_inf_visit_day as visitDay,
        fi_inf_main_diag as mainDiag,
        fi_pathogen_res_type,
        lv_pathogen_res_type,
        fi_inf_visit_time as fiVisitTime,
        fi_inf_org_name   as fiOrgName,
        lv_visit_time     as lvVisitTime,
        lv_org_name       as lv_org_name

    </sql>

    <sql id="emergingOrder">
        order by
        <choose>
            <when test="property == 'fiInfOnsetTime'">
                fi_inf_onset_time
            </when>
            <when test="property == 'fiIdentifyTime'">
                fi_inf_identify_time
            </when>
            <when test="property == 'patientAge'">
                fi_inf_patient_age
            </when>
            <when test="property == 'mainDiag'">
                fi_inf_main_diag_std_code
            </when>
            <when test="property == 'livingAddrStd'">
                lv_living_addr_detail_std
            </when>
            <when test="property == 'company'">
                fi_inf_company
            </when>
            <when test="property == 'visitOrg'">
                fi_inf_org_id
            </when>
            <when test="property == 'cityCode'">
                <choose>
                    <when test="addressType == 'livingAddress'">
                        fi_inf_living_addr_city_code
                    </when>
                    <otherwise>
                        fi_inf_org_addr_city_code
                    </otherwise>
                </choose>
            </when>
            <when test="property == 'districtCode'">
                <choose>
                    <when test="addressType == 'livingAddress'">
                        fi_inf_living_addr_func_district_code
                    </when>
                    <otherwise>
                        fi_inf_org_addr_func_district_code
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                fi_inf_visit_time
            </otherwise>
        </choose>
        <if test="direction == 'desc' or direction == 'asc'">
            ${direction}
        </if>
    </sql>

    <select id="listEmergingPathogenCheckResult"
            resultType="com.iflytek.cdc.province.model.vo.PathogenCheckVO">
        select
        infect_process_id       as processId,
        patient_name            as patientName,
        patient_sex_name        as sexDesc,
        fi_inf_patient_age      as patientAge,
        pneumonia_flag          as isPneumonia,
        case when virus_positive_flag = '1' or bacteria_positive_flag = '1' then '1' else '0' end as isPositive
        from ads.ads_ms_emerging_process_info
        where infect_process_id in
        <foreach collection="sourceKeyList" separator="," index="index" item="item" close=")" open="(">
            #{item}
        </foreach>
        <if test="isPneumonia != null and isPneumonia != ''">
            and pneumonia_flag = #{isPneumonia}
        </if>
        <if test="pathogenName != null and pathogenName != ''">
            and pathogen_name like CONCAT('%', #{pathogenName}, '%')
        </if>
    </select>

</mapper>