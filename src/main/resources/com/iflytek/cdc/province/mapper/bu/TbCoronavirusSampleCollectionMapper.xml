<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCoronavirusSampleCollectionMapper">



    <select id="groupSubtype" resultType="com.iflytek.cdc.province.model.vo.CoronavirusSampleCollectionVO">
        select count(1) as count, subtype_name as subtypeName from tb_coronavirus_sample_collection group by subtype_name
    </select>

    <select id="countTotal" resultType="java.lang.Integer">
        select count(1) from tb_coronavirus_sample_collection
    </select>

    <select id="selectAllList" resultType="com.iflytek.cdc.province.entity.bu.TbCoronavirusSampleCollection">
        select
            id,
            laboratory_name as laboratoryName,
            specimen_type as specimenType,
            collect_time as collectTime,
            specimen_source as specimenSource,
            inspect_time as inspectTime,
            reagent_verder as reagentVerder,
            orf1abct,
            nct,
            check_sequence_date as checkSequenceDate,
            check_seq_method as checkSeqMethod,
            seq_cover_rate as seqCoverRate,
            seq_compare_result as seqCompareResult,
            subtype_name as subtypeName
            from tb_coronavirus_sample_collection
        <where>
            <if test="sampleStartDate != null and sampleEndDate != null">
                collect_time between #{sampleStartDate} and #{sampleEndDate}
            </if>
        </where>
        order by collect_time
    </select>


</mapper>