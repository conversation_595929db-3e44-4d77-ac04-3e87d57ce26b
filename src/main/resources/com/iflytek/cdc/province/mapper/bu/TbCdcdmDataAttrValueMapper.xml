<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcdmDataAttrValueMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.bu.TbCdcdmDataAttrValue">
    <!--@mbg.generated-->
    <!--@Table tb_cdcdm_data_attr_value-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="data_attr_id" jdbcType="VARCHAR" property="dataAttrId" />
    <result column="attr_value1" jdbcType="VARCHAR" property="attrValue1" />
    <result column="attr_value2" jdbcType="VARCHAR" property="attrValue2" />
    <result column="attr_value3" jdbcType="VARCHAR" property="attrValue3" />
    <result column="attr_value4" jdbcType="VARCHAR" property="attrValue4" />
    <result column="attr_value5" jdbcType="VARCHAR" property="attrValue5" />
    <result column="attr_value6" jdbcType="VARCHAR" property="attrValue6" />
    <result column="attr_value7" jdbcType="VARCHAR" property="attrValue7" />
    <result column="attr_value8" jdbcType="VARCHAR" property="attrValue8" />
    <result column="attr_value9" jdbcType="VARCHAR" property="attrValue9" />
    <result column="attr_value10" jdbcType="VARCHAR" property="attrValue10" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, data_attr_id, attr_value1, attr_value2, attr_value3, attr_value4, attr_value5, 
    attr_value6, attr_value7, attr_value8, attr_value9, attr_value10, is_deleted, create_time, 
    creator, update_time, updater
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcdm_data_attr_value
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectAttrsBydataAttrId" resultType="java.lang.String">
    select attr_value1, attr_value2, attr_value3, attr_value4, attr_value5,
    attr_value6, attr_value7, attr_value8, attr_value9, attr_value10
    from tb_cdcdm_data_attr_value
    where data_attr_id = #{datAttrId}

  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from tb_cdcdm_data_attr_value
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataAttrValue">
    <!--@mbg.generated-->
    insert into tb_cdcdm_data_attr_value (id, data_attr_id, attr_value1,
      attr_value2, attr_value3, attr_value4, 
      attr_value5, attr_value6, attr_value7, 
      attr_value8, attr_value9, attr_value10, 
      is_deleted, create_time, creator, 
      update_time, updater)
    values (#{id,jdbcType=VARCHAR}, #{dataAttrId,jdbcType=VARCHAR}, #{attrValue1,jdbcType=VARCHAR}, 
      #{attrValue2,jdbcType=VARCHAR}, #{attrValue3,jdbcType=VARCHAR}, #{attrValue4,jdbcType=VARCHAR}, 
      #{attrValue5,jdbcType=VARCHAR}, #{attrValue6,jdbcType=VARCHAR}, #{attrValue7,jdbcType=VARCHAR}, 
      #{attrValue8,jdbcType=VARCHAR}, #{attrValue9,jdbcType=VARCHAR}, #{attrValue10,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataAttrValue">
    <!--@mbg.generated-->
    insert into tb_cdcdm_data_attr_value
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        id,
      </if>
      <if test="dataAttrId != null and dataAttrId != ''">
        data_attr_id,
      </if>
      <if test="attrValue1 != null and attrValue1 != ''">
        attr_value1,
      </if>
      <if test="attrValue2 != null and attrValue2 != ''">
        attr_value2,
      </if>
      <if test="attrValue3 != null and attrValue3 != ''">
        attr_value3,
      </if>
      <if test="attrValue4 != null and attrValue4 != ''">
        attr_value4,
      </if>
      <if test="attrValue5 != null and attrValue5 != ''">
        attr_value5,
      </if>
      <if test="attrValue6 != null and attrValue6 != ''">
        attr_value6,
      </if>
      <if test="attrValue7 != null and attrValue7 != ''">
        attr_value7,
      </if>
      <if test="attrValue8 != null and attrValue8 != ''">
        attr_value8,
      </if>
      <if test="attrValue9 != null and attrValue9 != ''">
        attr_value9,
      </if>
      <if test="attrValue10 != null and attrValue10 != ''">
        attr_value10,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null and creator != ''">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null and updater != ''">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="dataAttrId != null and dataAttrId != ''">
        #{dataAttrId,jdbcType=VARCHAR},
      </if>
      <if test="attrValue1 != null and attrValue1 != ''">
        #{attrValue1,jdbcType=VARCHAR},
      </if>
      <if test="attrValue2 != null and attrValue2 != ''">
        #{attrValue2,jdbcType=VARCHAR},
      </if>
      <if test="attrValue3 != null and attrValue3 != ''">
        #{attrValue3,jdbcType=VARCHAR},
      </if>
      <if test="attrValue4 != null and attrValue4 != ''">
        #{attrValue4,jdbcType=VARCHAR},
      </if>
      <if test="attrValue5 != null and attrValue5 != ''">
        #{attrValue5,jdbcType=VARCHAR},
      </if>
      <if test="attrValue6 != null and attrValue6 != ''">
        #{attrValue6,jdbcType=VARCHAR},
      </if>
      <if test="attrValue7 != null and attrValue7 != ''">
        #{attrValue7,jdbcType=VARCHAR},
      </if>
      <if test="attrValue8 != null and attrValue8 != ''">
        #{attrValue8,jdbcType=VARCHAR},
      </if>
      <if test="attrValue9 != null and attrValue9 != ''">
        #{attrValue9,jdbcType=VARCHAR},
      </if>
      <if test="attrValue10 != null and attrValue10 != ''">
        #{attrValue10,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null and updater != ''">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataAttrValue">
    <!--@mbg.generated-->
    update tb_cdcdm_data_attr_value
    <set>
      <if test="dataAttrId != null and dataAttrId != ''">
        data_attr_id = #{dataAttrId,jdbcType=VARCHAR},
      </if>
      <if test="attrValue1 != null and attrValue1 != ''">
        attr_value1 = #{attrValue1,jdbcType=VARCHAR},
      </if>
      <if test="attrValue2 != null and attrValue2 != ''">
        attr_value2 = #{attrValue2,jdbcType=VARCHAR},
      </if>
      <if test="attrValue3 != null and attrValue3 != ''">
        attr_value3 = #{attrValue3,jdbcType=VARCHAR},
      </if>
      <if test="attrValue4 != null and attrValue4 != ''">
        attr_value4 = #{attrValue4,jdbcType=VARCHAR},
      </if>
      <if test="attrValue5 != null and attrValue5 != ''">
        attr_value5 = #{attrValue5,jdbcType=VARCHAR},
      </if>
      <if test="attrValue6 != null and attrValue6 != ''">
        attr_value6 = #{attrValue6,jdbcType=VARCHAR},
      </if>
      <if test="attrValue7 != null and attrValue7 != ''">
        attr_value7 = #{attrValue7,jdbcType=VARCHAR},
      </if>
      <if test="attrValue8 != null and attrValue8 != ''">
        attr_value8 = #{attrValue8,jdbcType=VARCHAR},
      </if>
      <if test="attrValue9 != null and attrValue9 != ''">
        attr_value9 = #{attrValue9,jdbcType=VARCHAR},
      </if>
      <if test="attrValue10 != null and attrValue10 != ''">
        attr_value10 = #{attrValue10,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null and updater != ''">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataAttrValue">
    <!--@mbg.generated-->
    update tb_cdcdm_data_attr_value
    set data_attr_id = #{dataAttrId,jdbcType=VARCHAR},
      attr_value1 = #{attrValue1,jdbcType=VARCHAR},
      attr_value2 = #{attrValue2,jdbcType=VARCHAR},
      attr_value3 = #{attrValue3,jdbcType=VARCHAR},
      attr_value4 = #{attrValue4,jdbcType=VARCHAR},
      attr_value5 = #{attrValue5,jdbcType=VARCHAR},
      attr_value6 = #{attrValue6,jdbcType=VARCHAR},
      attr_value7 = #{attrValue7,jdbcType=VARCHAR},
      attr_value8 = #{attrValue8,jdbcType=VARCHAR},
      attr_value9 = #{attrValue9,jdbcType=VARCHAR},
      attr_value10 = #{attrValue10,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>