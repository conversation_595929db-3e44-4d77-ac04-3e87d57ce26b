<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcdmDataModelFormMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelForm">
    <!--@mbg.generated-->
    <!--@Table tb_cdcdm_data_model_form-->
    <id column="model_form_id" jdbcType="VARCHAR" property="modelFormId" />
    <result column="model_id" jdbcType="VARCHAR" property="modelId" />
    <result column="model_version_id" jdbcType="VARCHAR" property="modelVersionId" />
    <result column="form_name" jdbcType="VARCHAR" property="formName" />
    <result column="is_repeat" jdbcType="SMALLINT" property="isRepeat" />
    <result column="max_repeat_cnt" jdbcType="INTEGER" property="maxRepeatCnt" />
    <result column="form_identity_column" jdbcType="VARCHAR" property="formIdentityColumn" />
    <result column="form_json" jdbcType="VARCHAR" property="formJson" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="order_flag" jdbcType="VARCHAR" property="orderFlag" />
    <result column="quote_model" jdbcType="VARCHAR" property="quoteModel" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    model_form_id, model_id, model_version_id, form_name, is_repeat, max_repeat_cnt, 
    form_identity_column, form_json, is_deleted, create_time, creator, update_time, updater, order_flag, quote_model
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcdm_data_model_form
    where model_form_id = #{modelFormId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from tb_cdcdm_data_model_form
    where model_form_id = #{modelFormId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelForm">
    <!--@mbg.generated-->
    insert into tb_cdcdm_data_model_form (model_form_id, model_id, model_version_id,
      form_name, is_repeat, max_repeat_cnt, 
      form_identity_column, form_json, is_deleted, 
      create_time, creator, update_time, 
      updater, order_flag, quote_model)
    values (#{modelFormId,jdbcType=VARCHAR}, #{modelId,jdbcType=VARCHAR}, #{modelVersionId,jdbcType=VARCHAR}, 
      #{formName,jdbcType=VARCHAR}, #{isRepeat,jdbcType=SMALLINT}, #{maxRepeatCnt,jdbcType=INTEGER}, 
      #{formIdentityColumn,jdbcType=VARCHAR}, #{formJson,jdbcType=VARCHAR}, #{isDeleted,jdbcType=SMALLINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=VARCHAR}, #{orderFlag,jdbcType=VARCHAR}, #{quoteModel,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelForm">
    <!--@mbg.generated-->
    insert into tb_cdcdm_data_model_form
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="modelFormId != null and modelFormId != ''">
        model_form_id,
      </if>
      <if test="modelId != null and modelId != ''">
        model_id,
      </if>
      <if test="modelVersionId != null and modelVersionId != ''">
        model_version_id,
      </if>
      <if test="formName != null and formName != ''">
        form_name,
      </if>
      <if test="isRepeat != null">
        is_repeat,
      </if>
      <if test="maxRepeatCnt != null">
        max_repeat_cnt,
      </if>
      <if test="formIdentityColumn != null and formIdentityColumn != ''">
        form_identity_column,
      </if>
      <if test="formJson != null and formJson != ''">
        form_json,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null and creator != ''">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null and updater != ''">
        updater,
      </if>
      <if test="orderFlag != null and orderFlag != ''">
        order_flag,
      </if>
      <if test="quoteModel != null and quoteModel != ''">
        quote_model,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="modelFormId != null and modelFormId != ''">
        #{modelFormId,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null and modelId != ''">
        #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="modelVersionId != null and modelVersionId != ''">
        #{modelVersionId,jdbcType=VARCHAR},
      </if>
      <if test="formName != null and formName != ''">
        #{formName,jdbcType=VARCHAR},
      </if>
      <if test="isRepeat != null">
        #{isRepeat,jdbcType=SMALLINT},
      </if>
      <if test="maxRepeatCnt != null">
        #{maxRepeatCnt,jdbcType=INTEGER},
      </if>
      <if test="formIdentityColumn != null and formIdentityColumn != ''">
        #{formIdentityColumn,jdbcType=VARCHAR},
      </if>
      <if test="formJson != null and formJson != ''">
        #{formJson,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null and updater != ''">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="orderFlag != null and orderFlag != ''">
        #{orderFlag,jdbcType=VARCHAR},
      </if>
      <if test="quoteModel != null and quoteModel != ''">
        #{quoteModel,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelForm">
    <!--@mbg.generated-->
    update tb_cdcdm_data_model_form
    <set>
      <if test="modelId != null and modelId != ''">
        model_id = #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="modelVersionId != null and modelVersionId != ''">
        model_version_id = #{modelVersionId,jdbcType=VARCHAR},
      </if>
      <if test="formName != null and formName != ''">
        form_name = #{formName,jdbcType=VARCHAR},
      </if>
      <if test="isRepeat != null">
        is_repeat = #{isRepeat,jdbcType=SMALLINT},
      </if>
      <if test="maxRepeatCnt != null">
        max_repeat_cnt = #{maxRepeatCnt,jdbcType=INTEGER},
      </if>
      <if test="formIdentityColumn != null and formIdentityColumn != ''">
        form_identity_column = #{formIdentityColumn,jdbcType=VARCHAR},
      </if>
      <if test="formJson != null and formJson != ''">
        form_json = #{formJson,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null and updater != ''">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="orderFlag != null and orderFlag != ''">
        order_flag = #{orderFlag,jdbcType=VARCHAR},
      </if>
      <if test="quoteModel != null and quoteModel != ''">
        quote_model = #{quoteModel,jdbcType=VARCHAR},
      </if>
    </set>
    where model_form_id = #{modelFormId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelForm">
    <!--@mbg.generated-->
    update tb_cdcdm_data_model_form
    set model_id = #{modelId,jdbcType=VARCHAR},
      model_version_id = #{modelVersionId,jdbcType=VARCHAR},
      form_name = #{formName,jdbcType=VARCHAR},
      is_repeat = #{isRepeat,jdbcType=SMALLINT},
      max_repeat_cnt = #{maxRepeatCnt,jdbcType=INTEGER},
      form_identity_column = #{formIdentityColumn,jdbcType=VARCHAR},
      form_json = #{formJson,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      order_flag = #{orderFlag,jdbcType=VARCHAR},
      quote_model = #{quoteModel,jdbcType=VARCHAR}
    where model_form_id = #{modelFormId,jdbcType=VARCHAR}
  </update>
</mapper>