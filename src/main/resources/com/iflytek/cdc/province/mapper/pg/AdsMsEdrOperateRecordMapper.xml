<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMsEdrOperateRecordMapper">

    <sql id="Base_Column_List">
        id, archive_id, life_id, patient_name, operate_type, success_flag, permit_living_area_code,
        permit_company_area_code, permit_org_area_code, update_time, update_id, updater, operate_class, patient_id
    </sql>

    <sql id="chooseAddress">
        and (exists(
        select 1 from dim.dim_region_nation n
        where r.permit_living_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and n.province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and n.city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and n.district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>)
        or
        exists(
        select 1 from dim.dim_region_nation n
        where r.permit_company_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and n.province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and n.city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and n.district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>)
        or exists(
        select 1 from dim.dim_region_nation n
        where r.permit_org_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and n.province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and n.city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and n.district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>))
    </sql>

    <sql id="criteria">
        <include refid="chooseAddress"/>
        <if test="startDate != null and endDate != null">
            and r.update_time between #{startDate} and #{endDate}
        </if>
        <if test="maintenancePerson != null and maintenancePerson != ''">
            and r.updater like concat('%', #{maintenancePerson}, '%')
        </if>
        <if test="status != null and status != ''">
            and r.success_flag = #{status}
        </if>
        <if test="maintenanceOperating != null and maintenanceOperating != ''">
            and r.operate_class = #{maintenanceOperating}
        </if>
        <if test="recordId != null and recordId != ''">
            and r.archive_id like concat('%', #{recordId}, '%')
        </if>
        <if test="patientId != null and patientId != ''">
            and r.patient_id like concat('%', #{patientId}, '%')
        </if>
    </sql>

    <insert id="insertOnce">
        insert into ads.ads_ms_edr_operate_record
        (id, archive_id, life_id, patient_name, operate_type, success_flag, permit_living_area_code, permit_company_area_code,
        permit_org_area_code, update_time, update_id, updater, operate_class, patient_id, content_json)
        values
        (#{id}, #{archiveId}, #{lifeId}, #{patientName}, #{operateType}, #{successFlag}, #{permitLivingAreaCode},
        #{permitCompanyAreaCode}, #{permitOrgAreaCode}, #{updateTime}, #{updateId}, #{updater}, #{operateClass},
        #{patientId}, #{contentJson})
    </insert>

    <select id="maintenanceLogsStat"
            resultType="com.iflytek.cdc.province.model.edr.vo.RecordMaintenanceStatVO">
        select
        COUNT(id) AS maintenanceTotalCount,
        COALESCE(SUM(CASE WHEN update_time >= CURRENT_DATE - 7 THEN 1 ELSE 0 END), 0) AS recent7DaysMaintenanceCount,
        COALESCE(SUM(CASE WHEN update_time >= CURRENT_DATE - 1 AND update_time &lt; CURRENT_DATE THEN 1 ELSE 0 END), 0) AS yesterdayMaintenanceCount,
        COALESCE(SUM(CASE WHEN update_time >= CURRENT_DATE AND update_time &lt; CURRENT_DATE + 1 THEN 1 ELSE 0 END), 0) AS todayMaintenanceCount
        from ads.ads_ms_edr_operate_record r
        where 1=1
        <include refid="chooseAddress"/>
    </select>

    <select id="getMaintenanceLogs"
            resultType="com.iflytek.cdc.province.entity.ads.AdsMsEdrOperateRecord">
        select <include refid="Base_Column_List"/>
        from ads.ads_ms_edr_operate_record r
        where 1=1
        <include refid="criteria"/>
        order by update_time desc
    </select>

    <select id="countMaintenanceLogs" resultType="java.lang.Integer">
        select count(*)
        from ads.ads_ms_edr_operate_record r
        where 1=1
        <include refid="criteria"/>
    </select>

</mapper>