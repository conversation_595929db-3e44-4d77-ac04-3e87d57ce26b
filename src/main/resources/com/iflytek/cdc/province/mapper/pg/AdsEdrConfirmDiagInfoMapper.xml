<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrConfirmDiagInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsEdrConfirmDiagInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_edr_confirm_diag_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="dept_code" jdbcType="VARCHAR" property="deptCode" />
    <result column="dept" jdbcType="VARCHAR" property="dept" />
    <result column="diagnose_time" jdbcType="TIMESTAMP" property="diagnoseTime" />
    <result column="diagnose_classification_code" jdbcType="VARCHAR" property="diagnoseClassificationCode" />
    <result column="diagnose_classification" jdbcType="VARCHAR" property="diagnoseClassification" />
    <result column="case_type_code" jdbcType="VARCHAR" property="caseTypeCode" />
    <result column="case_type" jdbcType="VARCHAR" property="caseType" />
    <result column="disease_code" jdbcType="VARCHAR" property="diseaseCode" />
    <result column="disease" jdbcType="VARCHAR" property="disease" />
    <result column="diagnose_state_code" jdbcType="VARCHAR" property="diagnoseStateCode" />
    <result column="diagnose_state" jdbcType="VARCHAR" property="diagnoseState" />
    <result column="onset_date" jdbcType="TIMESTAMP" property="onsetDate" />
    <result column="statistics_flag_code" jdbcType="VARCHAR" property="statisticsFlagCode" />
    <result column="ncv_severity_code" jdbcType="VARCHAR" property="ncvSeverityCode" />
    <result column="ncv_severity" jdbcType="VARCHAR" property="ncvSeverity" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="diagnosis_org_code" jdbcType="VARCHAR" property="diagnosisOrgCode" />
    <result column="mpi_id" jdbcType="VARCHAR" property="mpiId" />
    <result column="diagnosis_org" jdbcType="VARCHAR" property="diagnosisOrg" />
    <result column="visit_type_code" jdbcType="VARCHAR" property="visitTypeCode" />
    <result column="visit_type" jdbcType="VARCHAR" property="visitType" />
    <result column="create_org" jdbcType="VARCHAR" property="createOrg" />
    <result column="create_org_name" jdbcType="VARCHAR" property="createOrgName" />
    <result column="update_org" jdbcType="VARCHAR" property="updateOrg" />
    <result column="update_org_name" jdbcType="VARCHAR" property="updateOrgName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, etl_create_datetime, etl_update_datetime, event_id, empi_id, dept_code, dept, 
    diagnose_time, diagnose_classification_code, diagnose_classification, case_type_code, 
    case_type, disease_code, disease, diagnose_state_code, diagnose_state, onset_date, 
    statistics_flag_code, ncv_severity_code, ncv_severity, create_user, create_time, 
    update_user, update_time, source_id, diagnosis_org_code, mpi_id, diagnosis_org, visit_type_code, 
    visit_type, create_org, create_org_name, update_org, update_org_name
  </sql>

  <select id="getDiagInfoByEventId" parameterType="string"
          resultType="com.iflytek.cdc.province.model.vo.edrcase.AdsEdrConfirmDiagInfoVO">
    select
    id,
    etl_create_datetime,
    etl_update_datetime,
    event_id,
    empi_id,
    dept_code,
    dept,
    diagnose_time,
    diagnose_classification_code,
    diagnose_classification,
    case_type_code,
    case_type,
    disease_code,
    disease,
    diagnose_state_code,
    diagnose_state,
    onset_date,
    statistics_flag_code,
    ncv_severity_code,
    ncv_severity
    from ads.ads_edr_confirm_diag_info
    where event_id = #{eventId}
  </select>

</mapper>