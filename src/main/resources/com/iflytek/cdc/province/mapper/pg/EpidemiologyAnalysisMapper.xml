<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.EpidemiologyAnalysisMapper">

    <sql id="syndromeCaseInfoTableJoin">
        <include refid="regionJoinCondition"/>
        <include refid="syndromeJoinCondition"/>
    </sql>

    <sql id="regionJoinCondition">
        join dim.dim_region_nation drn
        on
        <choose>
            <when test="addressType == 'livingAddress'">
                i.living_region_code = drn.region_code
            </when>
            <when test="addressType == 'companyAddress'">
                i.company_region_code = drn.region_code
            </when>
            <otherwise>
                i.org_region_code = drn.region_code
            </otherwise>
        </choose>
    </sql>

    <sql id="syndromeJoinCondition">
        join dim.dim_syndrome_info dii
        on dii.disease_code = i.fi_syn_syndrome_code
    </sql>

    <sql id="timeTypeWhere">
        <choose>
            <when test="timeType == 'onsetTime'">
                coalesce(i.fi_syn_onset_day, i.fi_syn_visit_day)
            </when>
            <otherwise>
                i.fi_syn_identify_day
            </otherwise>
        </choose>
    </sql>

    <sql id="inOutFlagWhere">
        <choose>
            <when test="inOutFlag == 'leave'">
                and lv_discharge_time is not null
                and lv_discharge_time between #{startDate} and #{endDate}
            </when>
            <when test="inOutFlag == 'admiss'">
                and fi_syn_visit_time is not null
                and fi_syn_visit_time between #{startDate} and #{endDate}
            </when>
            <otherwise>
                and 1=1
            </otherwise>
        </choose>
    </sql>

    <sql id="regionChoose">
        <if test="provinceCodeList != null and provinceCodeList.size() > 0">
            and drn.province_code in
            <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodeList != null and cityCodeList.size() > 0">
            and drn.city_code in
            <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodeList != null and districtCodeList.size() > 0">
            and drn.district_code in
            <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodeList != null and streetCodeList.size() > 0">
            and drn.street_code  in
            <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and drn.province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and drn.city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and drn.district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size() > 0">
            and drn.street_code  in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="syndromeProcessWhere">
        <if test="startDate != null and endDate != null">
            and <include refid="timeTypeWhere"/> between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        </if>
        <if test="syndromeCode != null and syndromeCode != ''">
            and dii.disease_code = #{syndromeCode}
        </if>
        <include refid="regionChoose"/>
    </sql>


    <select id="groupPatientVisitType"
            resultType="com.iflytek.cdc.province.model.epidemiology.PatientVisitSituationVO">
        select
            coalesce(count(distinct i.syndrome_process_id), 0) as patientCnt,
            i.lv_visit_type_name as visitTypeName
        from
            ads.ads_ms_syndrome_process_case_info i
            <include refid="syndromeCaseInfoTableJoin"/>
        where 1=1
        and i.rep_card_flag = '0'
        <include refid="syndromeProcessWhere"/>
        group by i.lv_visit_type_name
    </select>

    <select id="groupPatientVisitDept"
            resultType="com.iflytek.cdc.province.model.epidemiology.PatientVisitSituationVO">
        select
            coalesce(count(distinct i.syndrome_process_id), 0) as patientCnt,
            i.lv_dept_name as deptName
        from
            ads.ads_ms_syndrome_process_case_info i
            <include refid="syndromeCaseInfoTableJoin"/>
        where 1=1
            and i.rep_card_flag = '0'
            <include refid="syndromeProcessWhere"/>
        group by i.lv_dept_name
    </select>


    <select id="groupOutcomeStatus"
            resultType="com.iflytek.cdc.province.model.epidemiology.PatientVisitSituationVO">
        select
            coalesce(count(distinct i.syndrome_process_id), 0) as patientCnt,
            coalesce(count(distinct i.syndrome_process_id), 0) as admissCnt,
            coalesce(sum(case when i.lo_outcome_status = '稳定' then 1 else 0 end) , 0) as stableCnt,
            coalesce(sum(case when i.lo_outcome_status = '治愈' then 1 else 0 end) , 0) as cureCnt,
            coalesce(sum(case when i.lo_outcome_status = '恶化' then 1 else 0 end) , 0) as worsenCnt,
            coalesce(sum(case when i.lo_outcome_status = '好转' then 1 else 0 end) , 0) as improveCnt,
            coalesce(sum(case when i.lo_outcome_status = '死亡' then 1 else 0 end) , 0) as deathCnt,
            coalesce(sum(case when i.lo_outcome_status = '其他' then 1 else 0 end) , 0) as otherCnt,
            coalesce(sum(case when i.fi_syn_syndrome_severe_flag = '0' then 1 else 0 end) , 0) as notCriticalCnt,
            coalesce(sum(case when i.fi_syn_syndrome_severe_flag = '1' and i.lo_outcome_status <![CDATA[<>]]> '死亡' then 1 else 0 end) , 0) as criticalNotDeadCnt
        from
            ads.ads_ms_syndrome_process_case_info i
            <include refid="syndromeCaseInfoTableJoin"/>
        where 1=1
            and i.rep_card_flag = '0'
            <include refid="syndromeProcessWhere"/>
            <include refid="inOutFlagWhere"/>
    </select>


    <select id="selectCritiaclDeathProcessBy"
            resultType="com.iflytek.cdc.province.model.epidemiology.CritiaclDeathProcessVO">
        select
            drn.city_name as areaName,
            lv_org_name as orgName,
            patient_name as patientName,
            lv_dept_name as deptName,
            '--' as diagName,
            dii.disease_name as diseaseName,
            lo_outcome_status as outcomeStatusName
        from
            ads.ads_ms_syndrome_process_case_info i
            <include refid="syndromeCaseInfoTableJoin"/>
        where 1=1
            and i.rep_card_flag = '0'
            and fi_syn_syndrome_severe_flag = '1'
            and i.lo_outcome_status = '死亡'
            <include refid="syndromeProcessWhere"/>
    </select>





</mapper>