<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrRegisterManageInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsEdrRegisterManageInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_edr_register_manage_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="registration_type_code" jdbcType="VARCHAR" property="registrationTypeCode" />
    <result column="registration_type" jdbcType="VARCHAR" property="registrationType" />
    <result column="mpi_id" jdbcType="VARCHAR" property="mpiId" />
    <result column="hivab_date" jdbcType="DATE" property="hivabDate" />
    <result column="hivab_result_code" jdbcType="VARCHAR" property="hivabResultCode" />
    <result column="outhos_date" jdbcType="DATE" property="outhosDate" />
    <result column="diagnostic_classification_code" jdbcType="VARCHAR" property="diagnosticClassificationCode" />
    <result column="diagnostic_classification" jdbcType="VARCHAR" property="diagnosticClassification" />
    <result column="treatment_type_code" jdbcType="VARCHAR" property="treatmentTypeCode" />
    <result column="treatment_type" jdbcType="VARCHAR" property="treatmentType" />
    <result column="treatment_mode_code" jdbcType="VARCHAR" property="treatmentModeCode" />
    <result column="treatment_mode" jdbcType="VARCHAR" property="treatmentMode" />
    <result column="wait_treatment_reason_code" jdbcType="VARCHAR" property="waitTreatmentReasonCode" />
    <result column="wait_treatment_reason" jdbcType="VARCHAR" property="waitTreatmentReason" />
    <result column="treatment_shceme_days" jdbcType="NUMERIC" property="treatmentShcemeDays" />
    <result column="diagnose_typing_code" jdbcType="VARCHAR" property="diagnoseTypingCode" />
    <result column="diagnose_typing" jdbcType="VARCHAR" property="diagnoseTyping" />
    <result column="anti_tb_his" jdbcType="VARCHAR" property="antiTbHis" />
    <result column="focus_group_code" jdbcType="VARCHAR" property="focusGroupCode" />
    <result column="focus_group" jdbcType="VARCHAR" property="focusGroup" />
    <result column="record_type_code" jdbcType="VARCHAR" property="recordTypeCode" />
    <result column="record_type" jdbcType="VARCHAR" property="recordType" />
    <result column="transin_date" jdbcType="DATE" property="transinDate" />
    <result column="transin_org_code" jdbcType="VARCHAR" property="transinOrgCode" />
    <result column="transin_org" jdbcType="VARCHAR" property="transinOrg" />
    <result column="trace_update" jdbcType="DATE" property="traceUpdate" />
    <result column="non_transin_reason_code" jdbcType="VARCHAR" property="nonTransinReasonCode" />
    <result column="non_transin_reason" jdbcType="VARCHAR" property="nonTransinReason" />
    <result column="dot_org_code" jdbcType="VARCHAR" property="dotOrgCode" />
    <result column="dot_org" jdbcType="VARCHAR" property="dotOrg" />
    <result column="registration_org_code" jdbcType="VARCHAR" property="registrationOrgCode" />
    <result column="registration_org" jdbcType="VARCHAR" property="registrationOrg" />
    <result column="supervision_org_code" jdbcType="VARCHAR" property="supervisionOrgCode" />
    <result column="supervision_org" jdbcType="VARCHAR" property="supervisionOrg" />
    <result column="trace_org_code" jdbcType="VARCHAR" property="traceOrgCode" />
    <result column="trace_org" jdbcType="VARCHAR" property="traceOrg" />
    <result column="trace_zone_code" jdbcType="VARCHAR" property="traceZoneCode" />
    <result column="trace_result_code" jdbcType="VARCHAR" property="traceResultCode" />
    <result column="trace_result" jdbcType="VARCHAR" property="traceResult" />
    <result column="visit_date" jdbcType="DATE" property="visitDate" />
    <result column="preliminary_diagnostic_org_code" jdbcType="VARCHAR" property="preliminaryDiagnosticOrgCode" />
    <result column="preliminary_diagnostic_org" jdbcType="VARCHAR" property="preliminaryDiagnosticOrg" />
    <result column="preliminary_diagnostic_zone_code" jdbcType="VARCHAR" property="preliminaryDiagnosticZoneCode" />
    <result column="preliminary_diagnostic_zone" jdbcType="VARCHAR" property="preliminaryDiagnosticZone" />
    <result column="first_diagnostic_org_code" jdbcType="VARCHAR" property="firstDiagnosticOrgCode" />
    <result column="first_diagnostic_org" jdbcType="VARCHAR" property="firstDiagnosticOrg" />
    <result column="first_diagnostic_zone_code" jdbcType="VARCHAR" property="firstDiagnosticZoneCode" />
    <result column="first_diagnostic_zone" jdbcType="VARCHAR" property="firstDiagnosticZone" />
    <result column="first_manage_org_code" jdbcType="VARCHAR" property="firstManageOrgCode" />
    <result column="first_manage_org" jdbcType="VARCHAR" property="firstManageOrg" />
    <result column="first_manage_zone_code" jdbcType="VARCHAR" property="firstManageZoneCode" />
    <result column="first_manage_zone" jdbcType="VARCHAR" property="firstManageZone" />
    <result column="current_diagnostic_org_code" jdbcType="VARCHAR" property="currentDiagnosticOrgCode" />
    <result column="current_diagnostic_org" jdbcType="VARCHAR" property="currentDiagnosticOrg" />
    <result column="current_diagnostic_zone_code" jdbcType="VARCHAR" property="currentDiagnosticZoneCode" />
    <result column="current_diagnostic_zone" jdbcType="VARCHAR" property="currentDiagnosticZone" />
    <result column="current_manage_org_code" jdbcType="VARCHAR" property="currentManageOrgCode" />
    <result column="current_manage_org" jdbcType="VARCHAR" property="currentManageOrg" />
    <result column="current_manage_zone_code" jdbcType="VARCHAR" property="currentManageZoneCode" />
    <result column="current_manage_zone" jdbcType="VARCHAR" property="currentManageZone" />
    <result column="create_org" jdbcType="VARCHAR" property="createOrg" />
    <result column="create_org_name" jdbcType="VARCHAR" property="createOrgName" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_org" jdbcType="VARCHAR" property="updateOrg" />
    <result column="update_org_name" jdbcType="VARCHAR" property="updateOrgName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, etl_create_datetime, etl_update_datetime, event_id, registration_type_code, registration_type, 
    mpi_id, hivab_date, hivab_result_code, outhos_date, diagnostic_classification_code, 
    diagnostic_classification, treatment_type_code, treatment_type, treatment_mode_code, 
    treatment_mode, wait_treatment_reason_code, wait_treatment_reason, treatment_shceme_days, 
    diagnose_typing_code, diagnose_typing, anti_tb_his, focus_group_code, focus_group, 
    record_type_code, record_type, transin_date, transin_org_code, transin_org, trace_update, 
    non_transin_reason_code, non_transin_reason, dot_org_code, dot_org, registration_org_code, 
    registration_org, supervision_org_code, supervision_org, trace_org_code, trace_org, 
    trace_zone_code, trace_result_code, trace_result, visit_date, preliminary_diagnostic_org_code, 
    preliminary_diagnostic_org, preliminary_diagnostic_zone_code, preliminary_diagnostic_zone, 
    first_diagnostic_org_code, first_diagnostic_org, first_diagnostic_zone_code, first_diagnostic_zone, 
    first_manage_org_code, first_manage_org, first_manage_zone_code, first_manage_zone, 
    current_diagnostic_org_code, current_diagnostic_org, current_diagnostic_zone_code, 
    current_diagnostic_zone, current_manage_org_code, current_manage_org, current_manage_zone_code, 
    current_manage_zone, create_org, create_org_name, create_user, create_time, update_org, 
    update_org_name, update_user, update_time, remark, source_id, empi_id
  </sql>

   <select id="getAdsEdrRegisterManageInfoList" resultType="com.iflytek.cdc.province.model.vo.edrcase.AdsEdrRegisterManageInfoVO">
    select
    event_id, registration_type_code, registration_type, 
    mpi_id, hivab_date, hivab_result_code, outhos_date, diagnostic_classification_code, 
    diagnostic_classification, treatment_type_code, treatment_type, treatment_mode_code, 
    treatment_mode, wait_treatment_reason_code, wait_treatment_reason, treatment_shceme_days, 
    diagnose_typing_code, diagnose_typing, anti_tb_his, focus_group_code, focus_group, 
    record_type_code, record_type, transin_date, transin_org_code, transin_org, trace_update, 
    non_transin_reason_code, non_transin_reason, dot_org_code, dot_org, registration_org_code, 
    registration_org, supervision_org_code, supervision_org, trace_org_code, trace_org, 
    trace_zone_code, trace_result_code, trace_result, visit_date, preliminary_diagnostic_org_code, 
    preliminary_diagnostic_org, preliminary_diagnostic_zone_code, preliminary_diagnostic_zone, 
    first_diagnostic_org_code, first_diagnostic_org, first_diagnostic_zone_code, first_diagnostic_zone, 
    first_manage_org_code, first_manage_org, first_manage_zone_code, first_manage_zone, 
    current_diagnostic_org_code, current_diagnostic_org, current_diagnostic_zone_code, 
    current_diagnostic_zone, current_manage_org_code, current_manage_org, current_manage_zone_code, 
    current_manage_zone, create_org, create_org_name
    from ads.ads_edr_register_manage_info
    where 1=1
     <if test="empiId != null and empiId != ''">
       and empi_id=#{empiId}
     </if>
     <if test="eventIds != null and eventIds.length>0 ">
       and event_id in
       <foreach item="eventId" collection="eventIds" open="(" separator="," close=")">
         #{eventId}
       </foreach>
     </if>
     order by create_time desc
  </select>
</mapper>