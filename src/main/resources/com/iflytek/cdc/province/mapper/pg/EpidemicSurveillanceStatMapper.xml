<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.EpidemicSurveillanceStatMapper">



    <sql id="areaChoose">
        <choose>
            <when test="addressType != null and addressType == 'livingAddress'">
                <if test="provinceCode != '' and provinceCode != null">
                    and arcr.province_code = #{provinceCode}
                </if>
                <if test="cityCode != '' and cityCode != null">
                    and arcr.city_code = #{cityCode}
                </if>
                <if test="districtCode != '' and districtCode != null">
                    and arcr.district_code = #{districtCode}
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and arcr.province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and arcr.city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and arcr.district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="provinceCode != '' and provinceCode != null">
                    and arcr.report_org_addr_province_code = #{provinceCode}
                </if>
                <if test="cityCode != '' and cityCode != null">
                    and arcr.report_org_addr_city_code = #{cityCode}
                </if>
                <if test="districtCode != '' and districtCode != null">
                    and arcr.report_org_addr_district_code = #{districtCode}
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and arcr.report_org_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and arcr.report_org_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and arcr.report_org_addr_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>


    <select id="reportcardTimeTrend" resultType="com.iflytek.cdc.province.model.vo.ReportCardStatVO">
        select
            CAST(collect_time AS DATE) as statDate,
            CAST(collect_time AS DATE) as description,
            COALESCE(sum(case when status is not null then 1 else 0 end), 0) as collect
        from
            ads.ads_report_collect_record arcr
        where
            1=1
        <if test="startDate != null and endDate != null">
            and arcr.collect_time between #{startDate} and #{endDate}
        </if>
        <if test="infectTransmitType != null and infectTransmitType != ''">
            and arcr.transmit_type = #{infectTransmitType}
        </if>
        <if test="infectType != null and infectType != ''">
            and arcr.infect_type = #{infectType}
        </if>
        <if test="infectCode != null and infectCode != ''">
            and arcr.disease_code = #{infectCode}
        </if>
        <include refid="areaChoose"/>
        group by
            statDate, description
        order by statDate asc
    </select>

</mapper>