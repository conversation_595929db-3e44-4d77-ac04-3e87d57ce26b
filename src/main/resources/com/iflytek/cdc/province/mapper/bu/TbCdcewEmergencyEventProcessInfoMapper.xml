<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcewEmergencyEventProcessInfoMapper">

    <insert id="insertBatch">
        insert into tb_cdcew_emergency_event_process_info
        (id, patient_name, patient_sex_name, birth_day, patient_age, patient_age_unit, job, person_type, person_type_other,
        living_province_code, living_province_name, living_city_code, living_city_name, living_district_code, living_district_name,
        living_street_code, living_street_name, living_addr_detail, living_addr_longitude, living_addr_latitude, company_province_code,
        company_province_name, company_city_code, company_city_name, company_district_code, company_district_name, company_street_code,
        company_street_name, company, company_addr_longitude, company_addr_latitude, org_province_code, org_province_name, org_city_code,
        org_city_name, org_district_code, org_district_name, org_street_code, org_street_name, org_id, org_name, org_addr_longitude,
        org_addr_latitude, onset_time, first_visit_time, first_visit_org_id, first_visit_org_name, diagnose_time, disease_type,
        disease_code, disease_name, outcome_status, outcome_time, death_time, severe_flag, severe_time, out_flag, out_time, process_type,
        create_time, creator, creator_id, update_time, updater, updater_id, delete_flag)
        values
        <foreach collection="entities" item="item" separator=",">
            (#{item.id}, #{item.patientName}, #{item.patientSexName}, #{item.birthDay}, #{item.patientAge}, #{item.patientAgeUnit},
            #{item.job}, #{item.personType}, #{item.personTypeOther}, #{item.livingProvinceCode}, #{item.livingProvinceName},
            #{item.livingCityCode}, #{item.livingCityName}, #{item.livingDistrictCode}, #{item.livingDistrictName},
            #{item.livingStreetCode}, #{item.livingStreetName}, #{item.livingAddrDetail}, #{item.livingAddrLongitude},
            #{item.livingAddrLatitude}, #{item.companyProvinceCode}, #{item.companyProvinceName}, #{item.companyCityCode},
            #{item.companyCityName}, #{item.companyDistrictCode}, #{item.companyDistrictName}, #{item.companyStreetCode},
            #{item.companyStreetName}, #{item.company}, #{item.companyAddrLongitude}, #{item.companyAddrLatitude},
            #{item.orgProvinceCode}, #{item.orgProvinceName}, #{item.orgCityCode}, #{item.orgCityName}, #{item.orgDistrictCode},
            #{item.orgDistrictName}, #{item.orgStreetCode}, #{item.orgStreetName}, #{item.orgId}, #{item.orgName}, #{item.orgAddrLongitude},
            #{item.orgAddrLatitude}, #{item.onsetTime}, #{item.firstVisitTime}, #{item.firstVisitOrgId}, #{item.firstVisitOrgName},
            #{item.diagnoseTime}, #{item.diseaseType}, #{item.diseaseCode}, #{item.diseaseName}, #{item.outcomeStatus}, #{item.outcomeTime},
            #{item.deathTime}, #{item.severeFlag}, #{item.severeTime}, #{item.outFlag}, #{item.outTime}, #{item.processType},
            #{item.createTime}, #{item.creator}, #{item.creatorId}, #{item.updateTime}, #{item.updater}, #{item.updaterId}, #{item.deleteFlag})
        </foreach>
    </insert>

    <select id="listEventMedCntIndicator" resultType="com.iflytek.cdc.province.model.vo.MedCntIndicatorVO">
        select
        CAST(first_visit_time AS DATE) as stat_date,
        count(*) as medCaseCnt
        from tb_cdcew_emergency_event_process_info i
        where 1=1
        <include refid="medCntCommonCriteria"/>
        <include refid="orgAddressCriteria"/>
        <include refid="processCriteria"/>
        <include refid="processOtherCriteria"/>
        <if test="startDate != null and endDate != null">
            and CAST(first_visit_time AS DATE) between #{startDate} and #{endDate}
        </if>
        group by stat_date order by stat_date
    </select>

    <select id="listEventOutcomeIndicator" resultType="com.iflytek.cdc.province.model.vo.MedCntIndicatorVO">
        select
        CAST(outcome_time AS DATE) as stat_date,
        sum(case when outcome_status = '治愈' then 1 else 0 end) as recoverCnt ,
        sum(case when outcome_status = '死亡' then 1 else 0 end) as deathCnt,
        sum(case when outcome_status not in ('治愈', '死亡') then 1 else 0 end) as existingCaseCnt,
        sum(case when dead_reason = '因该病死亡' then 1 else 0 end) as diseaseDeathCnt
        from tb_cdcew_emergency_event_process_info i
        where 1=1
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        and outcome_time is not null
        group by stat_date order by stat_date
    </select>

    <select id="listEventAddressByIds" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO">
        select
        id                      as id,
        living_addr_detail      as lv_living_addr_detail ,
        living_addr_detail      as lv_living_addr_detail_std,
        living_addr_longitude   as lv_living_addr_longitude ,
        living_addr_latitude    as lv_living_addr_latitude ,
        company                 as lv_company ,
        company                 as lv_company_addr_detail_std ,
        company_addr_longitude  as lv_company_longitude ,
        company_addr_latitude   as lv_company_latitude ,
        org_name                as lv_org_name ,
        org_addr_longitude      as lv_org_longitude ,
        org_addr_latitude       as lv_org_latitude
        from tb_cdcew_emergency_event_process_info i
        where 1=1
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="listEventPatientInfoByIds" resultType="com.iflytek.cdc.province.model.vo.MsPatientInfoVO">
        select
        id                  as id,
        patient_name        as patientName,
        patient_sex_name    as patientSexName,
        patient_age         as patientAge,
        person_type         as job
        from tb_cdcew_emergency_event_process_info i
        where 1=1
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="listEventSimpleInfo" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO">
        select <include refid="SimpleInfoMap"/>
        from tb_cdcew_emergency_event_process_info
        where  1 =1
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and id = #{id}
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            and disease_name = #{diseaseName}
        </if>
        <if test="startDate != null and endDate != null">
            and CAST(first_visit_time AS DATE) between #{startDate} and #{endDate}
        </if>
        <include refid="OrgAddressCriteria"/>
        <include refid="processCriteria"/>
        <include refid="processOtherCriteria"/>
        <include refid="eventOrder"/>
    </select>
    <sql id="SimpleInfoMap">
        id as id,
        CASE
            WHEN COALESCE(living_province_code, '') = COALESCE(org_province_code, '')
                THEN living_province_code
            ELSE org_province_code
            END AS provinceCode,
        CASE
            WHEN COALESCE(living_province_code, '') = COALESCE(org_province_code, '')
                THEN living_city_code
            ELSE org_city_code
            END AS cityCode,
        CASE
            WHEN COALESCE(living_province_code, '') = COALESCE(org_province_code, '')
                THEN living_district_code
            ELSE org_district_code
            END AS funcDistrictCode,
        first_visit_org_id as fvOrgId,
        first_visit_org_name as fvOrgName,
        disease_name as diseaseName,
        disease_code as diseaseCode,
        company  as company,
        patient_name as patientName,
        patient_identity_no as idCard,
        living_addr_detail as livingAddrStd,
        patient_sex_name as patientSexName,
        patient_age as patientAge,
        outcome_status as outComeStatus,
        outcome_time as outComeTime,
        job  as job,
        onset_time as onsetTime,
        first_visit_time as visitTime,
        CAST(first_visit_time AS DATE) as visitDay
    </sql>
    <sql id="OrgAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and org_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and org_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and org_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and org_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and org_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>
    <sql id="processCriteria">
        <if test="fvOrgId != null and fvOrgId != ''">
            and first_visit_org_id = #{fvOrgId}
        </if>
        <if test="fvOrgName != null and fvOrgName != ''">
            and first_visit_org_name like concat('%', #{fvOrgName}, '%')
        </if>
        <if test="job != null and job != ''">
            and job = #{job}
        </if>
        <if test="company != null and company != ''">
            and company like concat('%', #{company}, '%')
        </if>
        <if test="patientSexName != null and patientSexName != ''">
            and patient_sex_name = #{patientSexName}
        </if>
        <if test="visitStartDate != null">
            and CAST(first_visit_time AS DATE) >= #{visitStartDate}
        </if>
        <if test="visitEndDate != null">
            and CAST(first_visit_time AS DATE) &lt;= #{visitEndDate}
        </if>
        <if test="onsetStartDate != null">
            and onset_time >= #{onsetStartDate}
        </if>
        <if test="onsetEndDate != null">
            and onset_time &lt;= #{onsetEndDate}
        </if>
    </sql>
    <sql id="processOtherCriteria">
        <!-- 地址选择 -->
        <if test="livingProvinceCode != null and livingProvinceCode != ''">
            and living_province_code = #{livingProvinceCode}
        </if>
        <if test="livingCityCode != null and livingCityCode != ''">
            and living_city_code = #{livingCityCode}
        </if>
        <if test="livingDistrictCode != null and livingDistrictCode != ''">
            and living_district_code = #{livingDistrictCode}
        </if>
        <if test="companyProvinceCode != null and companyProvinceCode != ''">
            and company_province_code = #{companyProvinceCode}
        </if>
        <if test="companyCityCode != null and companyCityCode != ''">
            and company_city_code = #{companyCityCode}
        </if>
        <if test="companyDistrictCode != null and companyDistrictCode != ''">
            and company_district_code = #{companyDistrictCode}
        </if>
        <!-- 年龄筛选 -->
        <if test="ageMin != null">
            and
            patient_age >= #{ageMin}
        </if>
        <if test="ageMax != null">
            and
            patient_age &lt;= #{ageMax}
        </if>
    </sql>
    <sql id="eventOrder">
        order by
        <choose>
            <when test="property == 'fiInfOnsetTime'">
                onset_time
            </when>
            <when test="property == 'patientAge'">
                patient_age
            </when>
            <when test="property == 'company'">
                company
            </when>
            <when test="property == 'visitOrg'">
                first_visit_org_id
            </when>
            <when test="property == 'cityCode'">
                <choose>
                    <when test="addressType == 'livingAddress'">
                        living_city_code
                    </when>
                    <otherwise>
                        org_city_code
                    </otherwise>
                </choose>
            </when>
            <when test="property == 'districtCode'">
                <choose>
                    <when test="addressType == 'livingAddress'">
                        living_district_code
                    </when>
                    <otherwise>
                        org_district_code
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                first_visit_time
            </otherwise>
        </choose>
        <if test="direction == 'desc' or direction == 'asc'">
            ${direction}
        </if>
    </sql>
    <sql id="medCntCommonCriteria">
        <if test="ids != null and ids.size() > 0">
            and id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and id =  #{id}
        </if>
        <if test="fvOrgId != null and fvOrgId != ''">
            and first_visit_org_id = #{fvOrgId}
        </if>
        <if test="fvOrgName != null and fvOrgName != ''">
            and first_visit_org_name like concat('%', #{fvOrgName}, '%')
        </if>
        <include refid="areaChooseCriteria" />
        <if test="diseaseName != null and diseaseName != ''">
            and disease_name = #{diseaseName}
        </if>
    </sql>
    <sql id="orgAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and org_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and org_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and org_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and org_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and org_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>
    <sql id="areaChooseCriteria">
        <choose>
            <when test="addressType == 'livingAddress'">
                <include refid="livingAddressCriteria"/>
            </when>
            <otherwise>
                <include refid="orgAddressCriteria"/>
            </otherwise>
        </choose>
    </sql>
    <sql id="livingAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and living_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and living_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and living_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and living_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and living_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and living_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and living_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>
</mapper>