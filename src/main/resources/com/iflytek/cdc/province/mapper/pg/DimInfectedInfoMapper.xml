<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.DimInfectedInfoMapper">

    <sql id="Base_Column_List">
        id, infected_sub_code, infected_sub_name, infected_code, infected_name, infected_type_code, infected_type_name,
        infected_source, memo, mdm_infected_sub_code, mdm_infected_sub_name, mdm_infected_code, mdm_infected_name,
        mdm_infected_type_code, mdm_infected_type_name, create_datetime, update_datetime, etl_create_datetime,
        etl_update_datetime, management_type_code, management_type_name, infect_class_code, infect_class_name,
        transmission_type_code, transmission_type_name, recovery_days, latent_days, report_deadline_h, check_deadline_h,
        transmit_type, single_flag
    </sql>

    <select id="listInfectedClass" resultType="com.iflytek.cdc.edr.vo.ConstantVO">
        select
            distinct infect_class_code as code, infect_class_name as desc
        from dim.dim_infected_info
    </select>

    <select id="listInfectedType" resultType="com.iflytek.cdc.edr.vo.ConstantVO">
        select
        distinct infected_type_code as code, infected_type_name as desc
        from dim.dim_infected_info
        where 1=1
        <if test="infectedClassCode != '' and infectedClassCode != null">
            and infect_class_code = #{infectedClassCode}
        </if>
        <if test="infectedClassName != '' and infectedClassName != null">
            and infect_class_name = #{infectedClassName}
        </if>
        order by infected_type_code

    </select>

    <select id="listInfectedInfoBy" resultType="com.iflytek.cdc.edr.vo.ConstantVO">
        select
        distinct infected_code as code, infected_name as desc
        from dim.dim_infected_info
        where 1=1
        <if test="infectedTypeCode != '' and infectedTypeCode != null">
            and infected_type_code = #{infectedTypeCode}
        </if>
        <if test="infectedTypeName != '' and infectedTypeName != null">
            and infected_type_name = #{infectedTypeName}
        </if>
        <if test="infectedClassCode != '' and infectedClassCode != null">
            and infect_class_code = #{infectedClassCode}
        </if>
        <if test="infectedClassName != '' and infectedClassName != null">
            and infect_class_name = #{infectedClassName}
        </if>
    </select>

    <select id="listAll" resultType="com.iflytek.cdc.province.entity.dim.DimInfectedInfo">
        select <include refid="Base_Column_List"/>
        from dim.dim_infected_info
    </select>
    <select id="getInfectedCodeByNames" resultType="java.lang.String">
        select
        id
        from dim.dim_infected_info
        where delete_flag = '0'
        and infected_sub_name in
        <foreach collection="names" item="infectedName" open="(" separator="," close=")">
            #{infectedName}
        </foreach>
    </select>

    <select id="getInfectCodeList" resultType="java.lang.String">
        select
        distinct id
        from dim.dim_infected_info dii
        where delete_flag = '0'
        <if test="infectCodeList !=null and infectCodeList.size() &gt; 0">
            and (dii.id in
            <foreach item="item" index="index" collection="infectCodeList"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            or dii.infected_id in
            <foreach item="item" index="index" collection="infectCodeList"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
        <if test="infectTypeList !=null and infectTypeList.size() &gt; 0">
            and dii.infected_type_code in
            <foreach item="item" index="index" collection="infectTypeList"
                     open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

</mapper>