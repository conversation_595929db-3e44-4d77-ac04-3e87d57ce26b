<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.BizEderInfoMapper">

    <select id="getBizEdrInfoByNameAndCard" resultType="java.util.HashMap">
        select id as archiveId,empi_id as empiId,name,id_card as validCertNumber
        from ads.ads_biz_edr_info
        where 1=1 and
        <if test="dtoList != null and dtoList.size() > 0">
            <foreach collection="dtoList" item="dto" separator="or">
                (name = #{dto.name} and id_card = #{dto.validCertNumber})
            </foreach>
        </if>
    </select>

</mapper>