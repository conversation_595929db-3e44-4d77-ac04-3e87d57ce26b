<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMsReportInfoMapper">
    <sql id="Infect_Base_Column_List">
        infect_report_id, infect_process_id, life_id, permit_living_area_code, permit_company_area_code,
        permit_org_area_code, etl_create_datetime, etl_update_datetime, identify_way, infect_code, infect_name,
        infect_type, infect_transmit_type, identify_class, infect_rule, infect_support, identify_time,
        identify_intime_flag, check_status, check_time, check_intime_flag, checked_flag,
        manage_disease_code, manage_disease_name, empi_id, patient_name, patient_sex_name, patient_identity_type,
        patient_identity_no, patient_birth_day, medical_id, event_id, patient_id, patient_age, patient_age_unit,
        patient_phone, living_addr_detail, living_addr_detail_std, living_addr_type, living_addr_province_code,
        living_addr_province, living_addr_city_code, living_addr_city, living_addr_district_code, living_addr_district,
        living_addr_func_district_code, living_addr_func_district, living_addr_street_code, living_addr_street,
        living_addr_street_longitude, living_addr_street_latitude, living_addr_longitude, living_addr_latitude,
        person_type, focus_person_flag, job, job_risk, company, company_addr_detail_std, company_province_code,
        company_province, company_city_code, company_city, company_district_code, company_district,
        company_func_district_code, company_func_district, company_street_code,
        company_street, company_street_longitude, company_street_latitude, company_longitude, company_latitude,
        company_addr_type, org_id, org_name, org_class, org_type_name, org_addr_detail, org_addr_detail_std,
        org_addr_type, org_addr_province_code, org_addr_province, org_addr_city_code, org_addr_city,
        org_addr_district_code, org_addr_district, org_addr_func_district_code, org_addr_func_district,
        org_addr_street_code, org_addr_street, org_longitude, org_latitude, pathogen_json, addr_belong_type,
        addr_area_json, focus_place_flag, onset_time, visit_time, visit_day, dept_code, dept_name, suit, symptom,
        medical_history_now, medical_history_before, checkup, assisted_exam, main_diag_code, main_diag,
        main_diag_std_code, main_diag_std, main_diag_type, diag_json, expose_history_json, travel_history_json,
        checkup_json, symptom_extr_json, symptom_attent_extr_json, discharge_time, discharge_diag_code, discharge_diag,
        outcome_time, outcome_status, recovery_flag, dead_time, dead_reason, dead_this_flag, full_flag, conform_flag,
        pneumonia_flag, pathogen_flag, leak_flag, severe_flag, infect_revise_time, infect_code_old,
        infect_name_old, last_revise_sql, diag_time, visit_type_name, first_diag_flag
    </sql>

    <sql id="chooseTime">
        <if test="startDate != null and endDate != null">
            <choose>
                <when test="dateType == 'diagnoseTime' or dateType == 'visitTime'">
                    and r.visit_time between #{startDate} and #{endDate}
                </when>
                <when test="dateType == 'checkTime'">
                    and r.check_time between #{startDate} and #{endDate}
                </when>
                <when test="dateType == 'identifyTime'">
                    and r.identify_time between #{startDate} and #{endDate}
                </when>
                <otherwise>
                    and r.onset_time between #{startDate} and #{endDate}
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id="permitCriteria">
        and (exists(
        select 1 from dim.dim_region_nation n
        where r.permit_living_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and n.province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and n.city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and n.district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>)
        or
        exists(
        select 1 from dim.dim_region_nation n
        where r.permit_company_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and n.province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and n.city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and n.district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>)
        or exists(
        select 1 from dim.dim_region_nation n
        where r.permit_org_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and n.province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and n.city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and n.district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>))
    </sql>

    <sql id="addressCriteria">
        <if test="addressType != null and addressType == 'livingAddress'">
            <if test="provinceCode != null and provinceCode != ''">
                and living_addr_province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode != ''">
                and living_addr_city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode != ''">
                and living_addr_func_district_code = #{districtCode}
            </if>
            <if test="streetCode != null and streetCode != ''">
                and living_addr_street_code = #{streetCode}
            </if>
            <if test="provinceCodes != null and provinceCodes.size()>0">
                and living_addr_province_code in
                <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodes != null and cityCodes.size()>0">
                and living_addr_city_code in
                <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodes != null and districtCodes.size()>0">
                and living_addr_func_district_code in
                <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="addressType != null and addressType == 'orgAddress'">
            <if test="provinceCode != null and provinceCode != ''">
                and org_addr_province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode != ''">
                and org_addr_city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode != ''">
                and org_addr_func_district_code = #{districtCode}
            </if>
            <if test="streetCode != null and streetCode != ''">
                and org_addr_street_code = #{streetCode}
            </if>
            <if test="provinceCodes != null and provinceCodes.size()>0">
                and org_addr_province_code in
                <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodes != null and cityCodes.size()>0">
                and org_addr_city_code in
                <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodes != null and districtCodes.size()>0">
                and org_addr_func_district_code in
                <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </if>
    </sql>

    <sql id="criteria">
        <include refid="chooseTime"/>
        <include refid="permitCriteria"/>
        <include refid="addressCriteria"/>
        <if test="diseaseCodeList != null and diseaseCodeList.size() &gt; 0">
            <if test="diseaseType == 'infected'">
                and r.infect_code in
            </if>
            <if test="diseaseType == 'syndrome'">
                and r.syndrome_code in
            </if>
            <foreach collection="diseaseCodeList" separator="," close=")" open="(" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="diagnoseStatus != null and diagnoseStatus != '' and diseaseType == 'infected'">
            and r.identify_class = #{diagnoseStatus}
        </if>
        <if test="outFlag != null and outFlag != '' and diseaseType == 'syndrome'">
            and r.syndrome_out_flag = #{outFlag}
        </if>
        <if test="reportCheckFlag != null and reportCheckFlag != ''">
            <if test="reportCheckFlag == 'waitingCheck'">
                and r.check_status = '1'
            </if>
            <if test="reportCheckFlag == 'noNeedCheck'">
                and r.check_status = '0'
            </if>
            <if test="reportCheckFlag == 'checked'">
                and r.check_status = '2'
            </if>
        </if>
        <if test="identifyIntimeFlag != null and identifyIntimeFlag != ''">
            and r.identify_intime_flag = #{identifyIntimeFlag}
        </if>
        <if test="checkIntimeFlag != null and checkIntimeFlag != ''">
            and r.check_intime_flag = #{checkIntimeFlag}
        </if>
        <if test="checkedFlag != null and checkedFlag != ''">
            and r.checked_flag = #{checkedFlag}
        </if>
        <if test="leakFlag != null and leakFlag != ''">
            and r.leak_flag = #{leakFlag}
        </if>
        <if test="fullFlag != null and fullFlag != ''">
            and r.full_flag = #{fullFlag}
        </if>
        <if test="conformFlag != null and conformFlag != ''">
            and r.conform_flag = #{conformFlag}
        </if>
        <if test="patientName != null and patientName != ''">
            and r.patient_name like concat('%', #{patientName}, '%')
        </if>
        <if test="processId != null and processId != ''">
            <if test="diseaseType == 'infected'">
                and r.infect_process_id like concat('%', #{processId}, '%')
            </if>
            <if test="diseaseType == 'syndrome'">
                and r.syndrome_process_id like concat('%', #{processId}, '%')
            </if>
        </if>
        <if test="reportId != null and reportId != ''">
            <if test="diseaseType == 'infected'">
                and r.infect_report_id like concat('%', #{reportId}, '%')
            </if>
            <if test="diseaseType == 'syndrome'">
                and r.syndrome_report_id like concat('%', #{reportId}, '%')
            </if>
        </if>
    </sql>

    <sql id="chooseTable">
        <if test="diseaseType == 'infected'">
            ads.ads_ms_infect_report_info
        </if>
        <if test="diseaseType == 'syndrome'">
            ads.ads_ms_syndrome_report_info
        </if>
    </sql>

    <sql id="syndromeTaskList">
        syndrome_report_id as report_id,
        syndrome_process_id as process_id,
        syndrome_code as disease_code,
        syndrome_name as disease_name,
        syndrome_out_flag as outFlag,
        syndrome_severe_flag as severeFlag,
        identify_time as report_time,
        case
            when check_status in ('0', '1') then 'notStarted'
            when check_status = '2' then 'checkedCompleted'
            when check_status = '3' then 'isChecking'
        end as checkProcessStatus,
        check_time as check_time,
        patient_name as patient_name,
        patient_sex_name as sex_name,
        patient_age as patientAge,
        patient_age_unit as patientAgeUnit,
        living_addr_func_district as districtName,
        onset_time as onsetTime,
        visit_time as visitTime,
        permit_living_area_code as permitLivingAreaCode,
        permit_company_area_code as permitCompanyAreaCode,
        permit_org_area_code as permitOrgAreaCode
    </sql>

    <sql id="infectedTaskList">
        infect_report_id as report_id,
        infect_process_id as process_id,
        permit_living_area_code as permitLivingAreaCode,
        permit_company_area_code as permitCompanyAreaCode,
        permit_org_area_code as permitOrgAreaCode,
        infect_code as diseaseCode,
        infect_name as diseaseName,
        identify_time as reportTime,
        case
            when check_status in ('0', '1') then 'notStarted'
            when check_status = '2' then 'checkedCompleted'
            when check_status = '3' then 'isChecking'
        end as checkProcessStatus,
        check_time as checkTime,
        patient_name as patientName,
        patient_sex_name as sexName,
        patient_age as patientAge,
        patient_age_unit as patientAgeUnit,
        visit_time as diagnoseTime,
        main_diag_type as diagnoseStatus,
        living_addr_func_district as districtName
    </sql>

    <select id="getCheckTaskList" resultType="com.iflytek.cdc.province.model.pandemic.vo.CheckTaskRecordVO">
        select
        <if test="diseaseType == 'infected'">
            <include refid="infectedTaskList"/>
        </if>
        <if test="diseaseType == 'syndrome'">
            <include refid="syndromeTaskList"/>
        </if>
        from <include refid="chooseTable"/> r
        where 1=1
        <include refid="criteria"/>
        order by r.identify_time desc
    </select>

    <select id="getReportCardBy" resultType="com.iflytek.cdc.province.model.pandemic.vo.CheckTaskRecordVO">
        select
        <if test="diseaseType == 'infected'">
            <include refid="infectedTaskList"/>
        </if>
        <if test="diseaseType == 'syndrome'">
            <include refid="syndromeTaskList"/>
        </if>
        from <include refid="chooseTable"/> r
        where 1=1
        <if test="reportId != null and reportId != ''">
            <if test="diseaseType == 'infected'">
                and r.infect_report_id = #{reportId}
            </if>
            <if test="diseaseType == 'syndrome'">
                and r.syndrome_report_id = #{reportId}
            </if>
        </if>
    </select>
    <select id="getInfectedReportForQcs" resultType="com.iflytek.cdc.province.entity.ads.AdsMsInfectReportInfo">
        select <include refid="Infect_Base_Column_List"/>
        from ads.ads_ms_infect_report_info
        where 1=1
        <choose>
            <when test="compareDateType =='onsetTime'">
                and onset_time between  #{compareStartDate} and #{compareStartEnd}
            </when>
            <when test="compareDateType =='diagTime'">
                and diag_time  between  #{compareStartDate} and #{compareStartEnd}
            </when>
            <otherwise>
                and visit_time  between  #{compareStartDate} and #{compareStartEnd}
            </otherwise>
        </choose>
        <choose>
            <when test="compareAddrType=='livingAddress'">
                <if test="compareProvinceCode != null and compareProvinceCode != ''">
                    and living_addr_province_code = #{compareProvinceCode}
                </if>
                <if test="compareCityCode != null and compareCityCode != ''">
                    and living_addr_city_code = #{compareCityCode}
                </if>
                <if test="compareDistrictCode != null and compareDistrictCode != ''">
                    and living_addr_district_code = #{compareDistrictCode}
                </if>
            </when>
            <otherwise>
                <if test="compareProvinceCode != null and compareProvinceCode != ''">
                    and org_addr_province_code = #{compareProvinceCode}
                </if>
                <if test="compareCityCode != null and compareCityCode != ''">
                    and org_addr_city_code = #{compareCityCode}
                </if>
                <if test="compareDistrictCode != null and compareDistrictCode != ''">
                    and org_addr_district_code = #{compareDistrictCode}
                </if>
            </otherwise>
        </choose>


    </select>


    <select id="selectEnterLogReport" resultType="com.iflytek.cdc.province.model.pandemic.vo.SyndromeEnterLogReportVO">
        select a.syndrome_process_id as processId,
            a.syndrome_code as syndromeCode,
            a.syndrome_name as syndromeName,
            a.identify_way as identifyWay,
            a.identify_time as identifyTime,
            a.patient_name as patientName,
            a.org_id as orgId,
            a.org_name as orgName,
            a.visit_time as visitTime
        from ads.ads_ms_syndrome_report_info a
        <where>
            <if test="processId != null and processId != ''">
                a.syndrome_process_id like concat('%', #{processId}, '%')
            </if>
            <if test="syndromeCode != null and syndromeCode != ''">
                and a.syndrome_code like concat('%', #{syndromeCode}, '%')
            </if>
            <if test="syndromeName != null and syndromeName != ''">
                and a.syndrome_name like concat('%', #{syndromeName}, '%')
            </if>
            <if test="identifyWay != null and identifyWay != ''">
                and a.identify_way = #{identifyWay}
            </if>
            <if test="patientName != null and patientName != ''">
                and a.patient_name like concat('%', #{patientName}, '%')
            </if>
            <if test="orgId != null and orgId != ''">
                and a.org_id = #{orgId}
            </if>
            <if test="orgName != null and orgName != ''">
                and a.org_name like concat('%', #{orgName}, '%')
            </if>
            <if test="queryKey != null and queryKey !=''">
                and a.syndrome_process_id like concat('%', #{queryKey}, '%')
                or a.syndrome_name like concat('%', #{queryKey}, '%')
                or a.patient_name like concat('%', #{queryKey}, '%')
                or a.org_name like concat('%', #{queryKey}, '%')
            </if>
            <if test="startDate != null and endDate != null">
                <choose>
                    <when test="dateType == '1'.toString()">
                        and a.visit_time between #{startDate} and #{endDate}
                    </when>
                    <otherwise>
                        and a.identify_time between #{startDate} and #{endDate}
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

</mapper>