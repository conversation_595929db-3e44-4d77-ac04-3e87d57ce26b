<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsPdPathogenTwofoldStatMapper">

    <sql id="regionChoose">
        <if test="provinceCodeList != null and provinceCodeList.size() > 0">
            and drn.province_code in
            <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodeList != null and cityCodeList.size() > 0">
            and drn.city_code in
            <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodeList != null and districtCodeList.size() > 0">
            and drn.district_code in
            <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodeList != null and streetCodeList.size() > 0">
            and drn.street_code  in
            <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and drn.province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and drn.city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and drn.district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size() > 0">
            and drn.street_code  in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="orgArea">
        <if test="provinceCode != null and provinceCode != ''">
            and org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and org_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and org_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and org_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="livingArea">
        <if test="provinceCode != null and provinceCode != ''">
            and living_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and living_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and living_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and living_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and living_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and living_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and living_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and living_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="chooseArea">
        <choose>
            <when test="addressType == 'orgAddress'">
                <include refid="orgArea"/>
            </when>
            <when test="addressType == 'livingAddress'">
                <include refid="livingArea"/>
            </when>
            <otherwise>
                <include refid="livingArea"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="statCriteria">
        <if test="processType != null and processType != ''">
            and process_type = #{processType}
        </if>
        <if test="pathogenName != null and pathogenName != ''">
            and (pathogen_name = #{pathogenName} or pathogen_name = #{pathogenName})
        </if>
        <if test="pneumoniaFlag != null and pneumoniaFlag != ''">
            and pneumonia_flag = #{pneumoniaFlag}
        </if>
        <if test="diseaseCode != null and diseaseCode != ''">
            and disease_code = #{diseaseCode}
        </if>
        <if test="allPathogenNameList != null and allPathogenNameList.size() &gt; 0">
            and pathogen_name in
            <foreach collection="allPathogenNameList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
            and pathogen_name in
            <foreach collection="allPathogenNameList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>


    <sql id="timeTypeWhere">
        <choose>
            <when test="timeType == 'reportTime'">
                test_report_day
            </when>
            <when test="timeType == 'onsetTime'">
                process_fi_onset_day
            </when>
            <when test="timeType == 'identifyTime'">
                process_fi_identify_day
            </when>
            <otherwise>
                process_fi_onset_day
            </otherwise>
        </choose>
    </sql>

    <select id="groupTwofoldByName" resultType="com.iflytek.cdc.province.model.pathogen.PathogenTwofoldStatVO">
        WITH positive_tests AS (
        SELECT DISTINCT process_id, pathogen_name, test_out_flag
        FROM ads.ads_pd_pathogen_check_case_info i
        join dim.dim_region_nation drn
        on
        <choose>
            <when test="addressType == 'orgAddress'">
                i.org_region_code = drn.region_code
            </when>
            <otherwise>
                i.living_region_code = drn.region_code
            </otherwise>
        </choose>
        <include refid="regionChoose"/>
        where
        <include refid="timeTypeWhere"/> between #{startDate} and #{endDate}
        <include refid="statCriteria"/>
        ),
        pathogen_pairs AS (
        SELECT
        LEAST(p1.pathogen_name, p2.pathogen_name) AS pathogen1,
        GREATEST(p1.pathogen_name, p2.pathogen_name) AS pathogen2,
        count(distinct p1.process_id) as check_cnt,
        COUNT(DISTINCT case when p1.test_out_flag = '1' and p2.test_out_flag = '1' then p1.process_id end) AS coinfection_count
        FROM positive_tests p1
        JOIN positive_tests p2 ON p1.process_id = p2.process_id
        WHERE p1.pathogen_name <![CDATA[<>]]> p2.pathogen_name
        GROUP BY LEAST(p1.pathogen_name, p2.pathogen_name), GREATEST(p1.pathogen_name, p2.pathogen_name)
        )
        SELECT pathogen1 as pathogenNameO,
        pathogen2 as pathogenNameT,
        check_cnt as processTestCnt,
        coinfection_count as processPositiveCnt
        FROM pathogen_pairs
        ORDER BY coinfection_count DESC;
    </select>

</mapper>
