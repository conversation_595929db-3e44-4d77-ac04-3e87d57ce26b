<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsPdVirusGeneStatMapper">

    <sql id="orgArea">
        <if test="provinceCode != null and provinceCode != ''">
            and org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and org_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and org_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and org_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="livingArea">
        <if test="provinceCode != null and provinceCode != ''">
            and living_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and living_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and living_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and living_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and living_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and living_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and living_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and living_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="chooseArea">
        <choose>
            <when test="addressType == 'orgAddress'">
                <include refid="orgArea"/>
            </when>
            <when test="addressType == 'livingAddress'">
                <include refid="livingArea"/>
            </when>
            <otherwise>
                <include refid="livingArea"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="dayCriteria">
        <if test="startDate != null and endDate != null">
            and day between #{startDate} and #{endDate}
        </if>
    </sql>

    <sql id="weekCriteria">
        <if test="startDate != null and endDate != null">
            and monday between #{startDate} and #{endDate}
        </if>
    </sql>

    <sql id="meadowCriteria">
        and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month} and ten_day = #{item.meadow})
        </foreach>
    </sql>

    <sql id="monthCriteria">
        and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month})
        </foreach>
    </sql>

    <sql id="quarterCriteria">
        and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and quarter = #{item.quarter})
        </foreach>
    </sql>

    <sql id="yearCriteria">
        and year in
        <foreach collection="dateDims" open="(" close=")" separator="," item="item">
            #{item.year}
        </foreach>
    </sql>

    <sql id="chooseTime">
        <choose>
            <when test="dateDimType == 'day'">
                <include refid="dayCriteria"/>
            </when>
            <when test="dateDimType == 'week'">
                <include refid="weekCriteria"/>
            </when>
            <when test="dateDimType == 'meadow'">
                <include refid="meadowCriteria"/>
            </when>
            <when test="dateDimType == 'month'">
                <include refid="monthCriteria"/>
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterCriteria"/>
            </when>
            <when test="dateDimType == 'year'">
                <include refid="yearCriteria"/>
            </when>
            <otherwise>
                <include refid="dayCriteria"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="whereCriteria">
        <include refid="chooseArea"/>
        <include refid="chooseTime"/>
        <if test="processType != null and processType != ''">
            and process_type = #{processType}
        </if>
        <if test="labIdList != null and labIdList.size() &gt; 0">
            and test_org_id in
            <foreach collection="labIdList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="allPathogenNameList != null and allPathogenNameList.size() &gt; 0">
            and virus_name in
            <foreach collection="allPathogenNameList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="virusType != null and virusType.size() &gt; 0">
            and virus_sub_name in
            <foreach collection="virusType" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="referAntigenId != null and referAntigenId.size() &gt; 0">
            and refer_pathogen in
            <foreach collection="referAntigenId" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="getAntigenAnalysisResult"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.VirusAntigenResultVO">
        select
            virus_sub_name as type,
            refer_pathogen as referAntigenName,
            test_org_name as labName,
            COALESCE(sum(similar_cnt), 0) as similarStrainCount,
            COALESCE(sum(low_cnt), 0) as lowResponseStrainCount,
            COALESCE(sum(sample_cnt), 0) as totalCount
        from ads.ads_pd_virus_gene_stat
        where 1=1
            <include refid="whereCriteria"/>
        group by virus_sub_name, refer_pathogen, test_org_name
        order by virus_sub_name, refer_pathogen, test_org_name
    </select>

    <select id="getInfluenzaPathogenType" resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.VirusAntigenResultVO">
        select distinct virus_sub_name as id, virus_sub_name as type
        from ads.ads_pd_virus_gene_stat
        where virus_sub_name is not null and virus_sub_name != ''
        order by virus_sub_name
    </select>

    <select id="getInfluenzaReferPathogen" resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.VirusAntigenResultVO">
        select distinct refer_pathogen as referAntigenId, refer_pathogen as referAntigenName
        from ads.ads_pd_virus_gene_stat
        where refer_pathogen is not null and refer_pathogen != ''
        order by refer_pathogen
    </select>    
</mapper>
