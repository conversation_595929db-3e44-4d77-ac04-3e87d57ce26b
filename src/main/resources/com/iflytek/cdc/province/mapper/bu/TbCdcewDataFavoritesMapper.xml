<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcewDataFavoritesMapper">

    <sql id="Base_Column_List">
        id, favorites_name, favorites_type, total_count, notes, create_time, creator, update_time,
        updater, delete_flag, model_version_id, creator_id, updater_id, app_code
    </sql>

    <insert id="editFavorites">
        insert into tb_cdcew_data_favorites
        (id, favorites_name, favorites_type, model_version_id, total_count, notes, create_time, creator, update_time,
        updater, delete_flag, creator_id, updater_id, app_code)
        values
        (#{id, jdbcType=VARCHAR}, #{favoritesName, jdbcType=VARCHAR}, #{favoritesType, jdbcType=VARCHAR},
        #{modelVersionId, jdbcType=VARCHAR}, #{totalCount, jdbcType=INTEGER}, #{notes, jdbcType=VARCHAR},
        #{createTime, jdbcType=TIMESTAMP}, #{creator, jdbcType=VARCHAR}, #{updateTime, jdbcType=TIMESTAMP},
        #{updater, jdbcType=VARCHAR}, #{deleteFlag, jdbcType=INTEGER},#{creatorId, jdbcType=VARCHAR},
        #{updaterId, jdbcType=VARCHAR}, #{appCode, jdbcType=VARCHAR})
        on conflict(id)
        do update set
        favorites_name  = excluded.favorites_name,
        notes           = excluded.notes,
        update_time     = excluded.update_time,
        updater         = excluded.updater,
        updater_id      = excluded.updater_id
    </insert>

    <update id="updateFavorites">
        update tb_cdcew_data_favorites
        set
        delete_flag     = '1',
        update_time     = now(),
        updater         = #{loginUserName, jdbcType=VARCHAR},
        updater_id      = #{loginUserId, jdbcType=VARCHAR}
        where id        = #{favoritesId, jdbcType=VARCHAR}
    </update>

    <update id="updateFavoritesDataCount">
        update tb_cdcew_data_favorites
        set
        total_count     = (select count(distinct data_record_id) from tb_cdcew_data_favorites_detail where data_favorites_id = #{favoritesId, jdbcType=VARCHAR} and status = 1),
        update_time     = now(),
        updater         = #{loginUserName, jdbcType=VARCHAR},
        updater_id      = #{loginUserId, jdbcType=VARCHAR}
        where id = #{favoritesId, jdbcType=VARCHAR}
    </update>

    <update id="updateFavoritesModelVersion">
        update tb_cdcew_data_favorites
        set
        model_version_id =  #{modelVersionId, jdbcType=VARCHAR},
        app_code = #{moduleType}
        where id = #{favoritesId, jdbcType=VARCHAR}
    </update>

    <select id="getFavoritesList" resultType="com.iflytek.cdc.edr.vo.dm.FavoritesListVO">
        select id, favorites_name, favorites_type, total_count, create_time, update_time, notes, model_version_id
        from tb_cdcew_data_favorites
        where delete_flag = '0'
        and creator_id = #{creatorId, jdbcType=VARCHAR}
        <if test="dateType == 'createDate' and startDate != null and endDate != null">
            and create_time between #{startDate, jdbcType=TIMESTAMP} and #{endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="dateType == 'updateDate' and startDate != null and endDate != null">
            and update_time between #{startDate, jdbcType=TIMESTAMP} and #{endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="favoritesName != null and favoritesName != ''">
            and favorites_name like concat('%', #{favoritesName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="modelVersionId != null and modelVersionId != ''">
            and (model_version_id = #{modelVersionId} or model_version_id is null)
        </if>
        <choose>
            <when test="favoritesType != null and favoritesType != ''">
                and favorites_type = #{favoritesType}
            </when>
            <otherwise>
                and (favorites_type is null or favorites_type = '')
            </otherwise>
        </choose>
        <if test="moduleType != null and moduleType != ''">
            and app_code = #{moduleType}
        </if>
        order by update_time desc
    </select>

    <select id="getFavoritesInfo" resultType="com.iflytek.cdc.province.entity.bu.TbCdcewDataFavorites">
        select <include refid="Base_Column_List"/>
        from tb_cdcew_data_favorites
        where id = #{favoritesId, jdbcType=VARCHAR}
    </select>

    <select id="getByIdAndUser" resultType="com.iflytek.cdc.province.entity.bu.TbCdcewDataFavorites">
        select <include refid="Base_Column_List"/>
        from tb_cdcew_data_favorites
        where id = #{favoritesId, jdbcType=VARCHAR} and creator_id=#{loginUserId, jdbcType=VARCHAR}
    </select>

</mapper>