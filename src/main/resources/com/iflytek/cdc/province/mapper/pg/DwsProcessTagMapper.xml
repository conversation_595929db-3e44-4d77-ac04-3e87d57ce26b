<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.DwsProcessTagMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.DwsProcessTag">
    <!--@mbg.generated-->
    <!--@Table dws.dws_process_tag-->
    <id column="tag_id" jdbcType="VARCHAR" property="tagId" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="tag" jdbcType="VARCHAR" property="tag" />
    <result column="tag_value" jdbcType="VARCHAR" property="tagValue" />
    <result column="occur_time" jdbcType="TIMESTAMP" property="occurTime" />
    <result column="tag_category" jdbcType="VARCHAR" property="tagCategory" />
    <result column="tag_source" jdbcType="VARCHAR" property="tagSource" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="enabled" jdbcType="VARCHAR" property="enabled" />
    <result column="etl_create_time" jdbcType="TIMESTAMP" property="etlCreateTime" />
    <result column="etl_update_time" jdbcType="TIMESTAMP" property="etlUpdateTime" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    tag_id, source_id, empi_id, tag, tag_value, occur_time, tag_category, tag_source, 
    remark, enabled, etl_create_time, etl_update_time, event_id
  </sql>
  <select id="getDwsProcessTagList" resultType="com.iflytek.cdc.province.model.vo.edrcase.DwsProcessTagVO">
      select
      distinct
      tag_value,
      CASE tag
      WHEN 'symptom_name' THEN '症状'
      WHEN 'syndrome_name' THEN '症候群'
      WHEN 'infectious_name' THEN '传染病'
      WHEN 'outcome_status_name' THEN '转归'
      -- 根据实际业务补充更多映射
      ELSE tag  -- 未匹配的保留原值
      END AS tagCategory,
      to_char(occur_time,'yyyy-mm-dd') occur_time
      from dws.dws_process_tag
      where empi_id=#{empiId}
      and tag in('symptom_name','infectious_name','syndrome_name','outcome_status_name')
     order by occur_time desc
  </select>
</mapper>