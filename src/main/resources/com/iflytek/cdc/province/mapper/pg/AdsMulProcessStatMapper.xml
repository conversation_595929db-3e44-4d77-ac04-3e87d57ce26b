<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMulProcessStatMapper">

    <sql id="regionJoinCondition">
        left join dim.dim_region_nation drn
        on
        <choose>
            <when test="addressType == 'livingAddress'">
                i.fv_living_region_code = drn.region_code
            </when>
            <when test="addressType == 'companyAddress'">
                i.fv_company_region_code = drn.region_code
            </when>
            <otherwise>
                i.fv_org_region_code = drn.region_code
            </otherwise>
        </choose>
    </sql>

    <sql id="orgRegionJoinCondition">
        left join dim.dim_region_nation drn
        on i.fv_org_region_code = drn.region_code
    </sql>


    <sql id="regionChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and drn.province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and drn.city_code in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and drn.district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and drn.street_code  in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and drn.province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and drn.city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and drn.district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and drn.street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and drn.province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and drn.city_code in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and drn.district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and drn.street_code in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and drn.province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and drn.city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and drn.district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and drn.street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="orgRegionChoose">
        <if test="provinceCode != null and provinceCode != ''">
            and org.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org.district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and org.street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and org.province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and org.city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and org.district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and org.street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="timeChoose">
        <choose>
            <when test="timeType = 'approveTime'">
                li_check_time
            </when>
            <when test="timeType = 'identifyTime'">
                fi_identify_time
            </when>
            <when test="timeType = 'onsetTime'">
                fv_onset_time
            </when>
            <otherwise>
                fv_onset_time
            </otherwise>
        </choose>
    </sql>

    <sql id="dayChoose">
        <choose>
            <when test="timeType = 'approveTime'">
                li_check_day
            </when>
            <when test="timeType = 'identifyTime'">
                fi_identify_day
            </when>
            <when test="timeType = 'onsetTime'">
                fv_onset_day
            </when>
            <otherwise>
                fv_onset_day
            </otherwise>
        </choose>
    </sql>

    <sql id="orgAddress">
        <if test="provinceCode != null and provinceCode != ''">
            and org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and org_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and org_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and org_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="livingAddress">
        <if test="provinceCode != null and provinceCode != ''">
            and living_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and living_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and living_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and living_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and living_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and living_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and living_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and living_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="chooseAddress">
        <choose>
            <when test="addressType == 'livingAddress'">
                <include refid="livingAddress"/>
            </when>
            <when test="addressType == 'orgAddress'">
                <include refid="orgAddress"/>
            </when>
            <otherwise>
                <include refid="orgAddress"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="dayCriteria">
        <if test="startDate != null and endDate != null">
            and day between #{startDate} and #{endDate}
        </if>
    </sql>

    <sql id="weekCriteria">
        <if test="startDate != null and endDate != null">
            and monday between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        </if>
    </sql>

    <sql id="meadowCriteria">
        and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month} and ten_day = #{item.meadow})
        </foreach>
    </sql>

    <sql id="monthCriteria">
        and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month})
        </foreach>
    </sql>

    <sql id="quarterCriteria">
        and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and quarter = #{item.quarter})
        </foreach>
    </sql>

    <sql id="yearCriteria">
        and year in
        <foreach collection="dateDims" open="(" close=")" separator="," item="item">
            #{item.year}
        </foreach>
    </sql>

    <sql id="chooseTime">
        <choose>
            <when test="dateDimType == 'day'">
                <include refid="dayCriteria"/>
            </when>
            <when test="dateDimType == 'week'">
                <include refid="weekCriteria"/>
            </when>
            <when test="dateDimType == 'meadow'">
                <include refid="meadowCriteria"/>
            </when>
            <when test="dateDimType == 'month' or dateDimType == 'halfYear'">
                <include refid="monthCriteria"/>
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterCriteria"/>
            </when>
            <otherwise>
                <include refid="yearCriteria"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="chooseStatDate">
        <choose>
            <when test="dateDimType == 'day'">
                d.date_day,
                d.day_desc as statDate,
            </when>
            <when test="dateDimType == 'week'">
                d.week_desc AS statDate,
                d.year,
                d.week,
            </when>
            <when test="dateDimType == 'meadow'">
                d.ten_days_desc AS statDate,
                d.year,
                d.month,
                d.ten_days as ten_day,
            </when>
            <when test="dateDimType == 'month' or dateDimType == 'halfYear'">
                d.month_desc AS statDate,
                d.year,
                d.month,
            </when>
            <when test="dateDimType == 'quarter'">
                d.quarter_desc AS statDate,
                d.year,
                d.quarter,
            </when>
            <otherwise>
                year,
                year as statDate,
            </otherwise>
        </choose>
    </sql>

    <sql id="groupByDateDim">
        <choose>
            <when test="dateDimType == 'day'">
                d.date_day,d.day_desc
            </when>
            <when test="dateDimType == 'week'">
                d.week_desc, d.year, d.week
            </when>
            <when test="dateDimType == 'meadow'">
                d.ten_days_desc, d.year, d.month, d.ten_days
            </when>
            <when test="dateDimType == 'month' or dateDimType == 'halfYear'">
                d.month_desc, d.year, d.month
            </when>
            <when test="dateDimType == 'quarter'">
                d.quarter_desc, d.year, d.quarter
            </when>
            <otherwise>
                d.year
            </otherwise>
        </choose>
    </sql>


    <sql id="trendStatSql">
        count(process_id) as processNewCnt,
        0 as processNewCntAvgS
    </sql>

    <sql id="mulProcessCriteria">
        <if test="startDate != null and endDate != null">
            and <include refid="timeChoose"/> between #{startDate} and #{endDate}
        </if>
        <if test="diseaseTypeCode != null and diseaseTypeCode != ''">
            and process_type = #{diseaseTypeCode}
        </if>
        <if test="diseaseCodeList != null and diseaseCodeList.size() &gt; 0">
            and fi_disease_code in
            <foreach collection="diseaseCodeList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test = "filterInfectCodes != null and filterInfectCodes.size() > 0">
            and fi_disease_code not in
            <foreach collection="filterInfectCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <include refid="chooseWarningScenario"/>
    </sql>

    <sql id="whereProcessCriteria">
        <include refid="mulProcessCriteria"/>
        <include refid="regionChoose"/>
    </sql>

    <sql id="chooseWarningScenario">
        <if test="personTypeList != null and personTypeList.size() &gt; 0">
            and person_type_name
            <choose>
                <when test="otherWarningScenarioType != null and otherWarningScenarioType != ''">
                    not in
                </when>
                <otherwise>
                    in
                </otherwise>
            </choose>
            <foreach collection="personTypeList" open="(" close=")" item="item" separator="," index="index">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="timeTrendStatDateChoose">
        <choose>
            <when test="dateDimType == 'year'">
                ddd.year
            </when>
            <when test="dateDimType == 'halfYear'">
                ddd.half_year_desc
            </when>
            <when test="dateDimType == 'quarter'">
                ddd.quarter_desc
            </when>
            <when test="dateDimType == 'month'">
                ddd.month_desc
            </when>
            <when test="dateDimType == 'meadow'">
                ddd.ten_days_desc
            </when>
            <when test="dateDimType == 'week'">
                ddd.week_desc
            </when>
            <otherwise>
                ddd.date_day
            </otherwise>
        </choose>
    </sql>

    <select id="diseaseOverAll" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        fi_disease_code as descriptionCode,
        fi_disease_name as description,
        COALESCE(count(process_id), 0) as processNewCnt,
        COALESCE(sum(case when lo_outcome_status = '死亡' then 1 else 0 end), 0)  as processDeadCnt
        from
        ads.ads_mul_process_case_info i
        <include refid="regionJoinCondition"/>
        where rep_card_flag ='0'
        <include refid="whereProcessCriteria"/>
        group by
        fi_disease_code,
        fi_disease_name
    </select>

    <select id="diseaseOnsetTrend"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.ProcessTimeLineStatVO">
        select
        <include refid="timeChoose"/> as statDate,
        <include refid="trendStatSql"/>
        from
        ads.ads_mul_process_case_info i
        <include refid="regionJoinCondition"/>
        <include refid="joinDayDim"/>
        where 1=1
        <include refid="whereProcessCriteria" />
        group by <include refid="groupByDateDim"/>,
        <include refid="timeChoose"/>
        order by <include refid="groupByDateDim"/>
    </select>

    <sql id="pneumoniaStat">
        COALESCE(count(process_id), 0) as totalProcessCount ,
        COALESCE(count(case when pneumonia_flag = '1' then process_id end), 0) as pneumoniaProcessCount,
        COALESCE(count(case when pneumonia_flag = '1' and pathogen_flag = '1' then process_id end), 0) as pathogenDetectProcessCount
    </sql>

    <select id="pneumoniaSituation"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.PneumoniaOccursSituationVO">
        select
        fi_disease_code as diseaseCode,
        fi_disease_name as diseaseName,
        <include refid="pneumoniaStat"/>
        from
        ads.ads_mul_process_case_info i
        <include refid="regionJoinCondition" />
        where rep_card_flag ='0'
        <include refid="whereProcessCriteria" />
        group by
        fi_disease_code,
        fi_disease_name
    </select>

<!--   左连接区域表-->
    <select id="existsProcessStat"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.IndicatorDataVO">
        select
        <include refid="timeTrendStatDateChoose"/> as statDate,
        COALESCE(sum(case when i.end_time is null AND i.process_id IS NOT NULL then 1 else 0 end) , 0)  as value
        from
        dim.dim_date_day ddd
        left join ads.ads_mul_process_case_info i
        on
        ddd.date_day =
        <include refid="dayChoose"/>
        and  rep_card_flag ='0'
        <include refid="mulProcessCriteria"/>
        <include refid="regionJoinCondition"/>
        <include refid="regionChoose"/>
        where 1=1
        <if test="startDate != null">
            and ddd.date_day &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            and ddd.date_day &lt;= #{endDate}
        </if>
        group by statDate
        order by statDate
    </select>

    <select id="sexDistributionStat"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.ProcessSexDistributionVO">
        select
        fi_disease_code as diseaseCode,
        fi_disease_name as diseaseName,
        COUNT(DISTINCT CASE WHEN patient_sex_name = '男性' THEN process_id END) AS maleProcessCount,
        COUNT(DISTINCT CASE WHEN patient_sex_name = '女性' THEN process_id END) AS femaleProcessCount
        from
        ads.ads_mul_process_case_info i
        <include refid="regionJoinCondition"/>
        <include refid="joinDayDim"/>
        where 1=1
        <include refid="whereProcessCriteria" />
        group by
        fi_disease_code,
        fi_disease_name
    </select>

    <select id="sexTimeLineTrend"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.ProcessTimeLineStatVO">
        select
        <include refid="chooseStatDate"/>
        COUNT(DISTINCT CASE WHEN patient_sex_name = '男性' THEN process_id END) AS maleProcessCount,
        COUNT(DISTINCT CASE WHEN patient_sex_name = '女性' THEN process_id END) AS femaleProcessCount
        from
        ads.ads_mul_process_case_info i
        <include refid="regionJoinCondition"/>
        <include refid="joinDayDim"/>
        where 1=1
        <include refid="whereProcessCriteria" />
        group by <include refid="groupByDateDim"/>
        order by <include refid="groupByDateDim"/>
    </select>

    <select id="ageDistributionStat"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.DoubleIndicatorStatVO">
        select
        fi_disease_code as xIndicatorCode,
        fi_disease_name as xIndicatorName,
        <include refid="selectAgeGroup" /> as patient_age_group,
        count(process_id) as statValue
        from
        <include refid="fromWithAgeGroup" />
        <include refid="regionJoinCondition"/>
        <include refid="joinDayDim"/>
        where 1=1
        and rep_card_flag ='0'
        <include refid="whereProcessCriteria" />
        group by fi_disease_code, fi_disease_name,
        CASE
        WHEN ag.value_code IS NOT NULL THEN ag.value_name
        ELSE '不详'
        END
    </select>

    <select id="ageTimeLineTrend"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.GroupSumIntVO">
        select
        <include refid="chooseStatDate"/>
        <include refid="selectAgeGroup"/> as name,
        count(process_id) as value
        from
        <include refid="fromWithAgeGroup" />
        <include refid="regionJoinCondition"/>
        <include refid="joinDayDim"/>
        where 1=1
        <include refid="whereProcessCriteria" />
        group by
        CASE
        WHEN ag.value_code IS NOT NULL THEN ag.value_name
        ELSE '不详'
        END,
        <include refid="groupByDateDim"/>
        order by <include refid="groupByDateDim"/>
    </select>

    <select id="jobDistributionStat"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.DoubleIndicatorStatVO">
        select
        person_type_name as xIndicatorName,
        fi_disease_code as yIndicatorCode,
        fi_disease_name as yIndicatorName,
        count(process_id) as statValue
        from
        ads.ads_mul_process_case_info i
        <include refid="regionJoinCondition"/>
        <include refid="joinDayDim"/>
        where 1=1
        <include refid="whereProcessCriteria" />
        group by fi_disease_code, fi_disease_name, person_type_name
    </select>

    <select id="characterDistributionStat"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.ProcessInfoVO">
        select
        <include refid="selectAgeGroup" /> as patient_age_group,
        patient_sex_name, person_type_name,
        count(process_id) as count
        from
        <include refid="fromWithAgeGroup"/>
        <include refid="regionJoinCondition"/>
        <include refid="joinDayDim"/>
        where 1=1
        <include refid="whereProcessCriteria" />
        group by
        CASE
        WHEN ag.value_code IS NOT NULL THEN ag.value_name
        ELSE '不详'
        END,
        patient_sex_name, person_type_name
    </select>

    <sql id="regionGroupBy">
        <if test="areaLevel == 1">
            drn.city_code,
            drn.city_name
        </if>
        <if test="areaLevel == 2">
            drn.district_code,
            drn.district_name
        </if>
        <if test="areaLevel == 3">
            drn.district_code,
            drn.district_name
        </if>
    </sql>

    <select id="regionDistributionStat"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.DoubleIndicatorStatVO">
        select
        fi_disease_code as xIndicatorCode, fi_disease_name as xIndicatorName,
        <if test="areaLevel == 1">
            drn.city_code as yIndicatorCode,
            drn.city_name as yIndicatorName,
        </if>
        <if test="areaLevel == 2">
            drn.district_code as yIndicatorCode,
            drn.district_name as yIndicatorName,
        </if>
        <if test="areaLevel == 3">
            drn.district_code as yIndicatorCode,
            drn.district_name as yIndicatorName,
        </if>
        count(process_id) as statValue
        from
        ads.ads_mul_process_case_info i
        <include refid="orgRegionJoinCondition"/>
        where 1=1
        <include refid="whereProcessCriteria" />
        group by fi_disease_code, fi_disease_name,
        <include refid="regionGroupBy"/>
    </select>

    <select id="regionTimeLineTrend"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.GroupSumIntVO">
        select
        <include refid="chooseStatDate" />
        <if test="areaLevel == 1">
            drn.city_code as code,
            drn.city_name as name,
        </if>
        <if test="areaLevel == 2">
            drn.district_code as code,
            drn.district_name as name,
        </if>
        <if test="areaLevel == 3">
            drn.district_code as code,
            drn.district_name as name,
        </if>
        count(process_id) as value
        from
        ads.ads_mul_process_case_info i
        <include refid="orgRegionJoinCondition"/>
        <include refid="joinDayDim"/>
        where 1=1
        <include refid="whereProcessCriteria" />
        group by <include refid="groupByDateDim" />,
        <include refid="regionGroupBy"/>
        order by <include refid="groupByDateDim" />
    </select>

    <select id="urbanRuralDistributionStat"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.ProcessUrbanRuralDistributionVO">
        select
        fi_disease_code as diseaseCode,
        fi_disease_name as diseaseName,
        0 as urbanProcessCount,
        0 as ruralProcessCount
        from
        ads.ads_mul_process_case_info i
        <include refid="regionJoinCondition"/>
        <include refid="joinDayDim"/>
        where 1=1
        <include refid="whereProcessCriteria" />
        group by
        fi_disease_code,
        fi_disease_name
    </select>

    <select id="urbanRuralTimeLineTrend"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.GroupSumIntVO">
        select
        <include refid="chooseStatDate" />
        focus_place_type as name,
        count(process_id) as value
        from
        ads.ads_mul_process_case_info i
        <include refid="regionJoinCondition"/>
        <include refid="joinDayDim"/>
        where 1=1
        <include refid="whereProcessCriteria" />
        group by focus_place_type,
        <include refid="groupByDateDim" />
        order by <include refid="groupByDateDim" />
    </select>

    <select id="getAreaDistributionStat"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.ProcessAreaDistributionVO">
        select
        <if test="areaLevel == 1">
            drn.city_code as areaCode,
            drn.city_name as areaName,
        </if>
        <if test="areaLevel == 2">
            drn.district_code as areaCode,
            drn.district_name as areaName,
        </if>
        <if test="areaLevel == 3">
            drn.district_code as areaCode,
            drn.district_name as areaName,
        </if>
        count(process_id) as processCount
        from
        ads.ads_mul_process_case_info i
        <include refid="orgRegionJoinCondition"/>
        where 1=1
        <include refid="whereProcessCriteria" />
        group by
        <include refid="regionGroupBy"/>
    </select>

    <select id="getInfectYearOnYearGrowthByDate"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.ProcessTimeLineStatVO">
        select
        fi_disease_code as diseaseCode,
        fi_disease_name as diseaseName,
        d.year, d.month,
        CONCAT(d.year, '年', d.month, '月') as statDate,
        count(process_id) as processNewCnt
        from ads.ads_mul_process_case_info i
        <include refid="regionJoinCondition"/>
        <include refid="joinDayDim"/>
        where 1=1
        <include refid="whereProcessCriteria" />
        group by fi_disease_code, fi_disease_name, d.year, d.month
        order by d.year, d.month
    </select>

    <select id="getInfectYearOnYearGrowthByArea"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.DoubleIndicatorStatVO">
        select
        fi_disease_code as xIndicatorCode, fi_disease_name as xIndicatorName,
        <if test="areaLevel == 1">
            drn.city_code as yIndicatorCode,
            drn.city_name as yIndicatorName,
        </if>
        <if test="areaLevel == 2">
            drn.district_code as yIndicatorCode,
            drn.district_name as yIndicatorName,
        </if>
        <if test="areaLevel == 3">
            drn.district_code as yIndicatorCode,
            drn.district_name as yIndicatorName,
        </if>
        d.year, d.month,
        CONCAT(d.year, '年', d.month, '月') as statDate
        from ads.ads_mul_process_case_info i
        <include refid="regionJoinCondition"/>
        <include refid="joinDayDim"/>
        where 1=1
        <include refid="whereProcessCriteria" />
        group by fi_disease_code, fi_disease_name, d.year, d.month,
        <include refid="regionGroupBy"/>
    </select>

    <sql id="pathogenStat">
        COALESCE(sum(process_virus_cnt), 0) as virusDetectCount,
        COALESCE(sum(process_virus_cnt_last), 0) as virusDetectCountLast,
        COALESCE(sum(process_virus_cnt_last_y), 0) as virusDetectCountLastY,
        COALESCE(sum(process_virus_positive_cnt), 0) as virusPositiveCount,
        COALESCE(sum(process_virus_positive_cnt_last), 0) as virusPositiveCountLast,
        COALESCE(sum(process_virus_positive_cnt_last_y), 0) as virusPositiveCountLastY,
        COALESCE(sum(process_bacteria_cnt), 0) as bacteriaDetectCount,
        COALESCE(sum(process_bacteria_cnt_last), 0) as bacteriaDetectCountLast,
        COALESCE(sum(process_bacteria_cnt_last_y), 0) as bacteriaDetectCountLastY,
        COALESCE(sum(process_bacteria_positive_cnt), 0) as bacteriaPositiveCount,
        COALESCE(sum(process_bacteria_positive_cnt_last), 0) as bacteriaPositiveCountLast,
        COALESCE(sum(process_bacteria_positive_cnt_last_y), 0) as bacteriaPositiveCountLastY,
        COALESCE(sum(process_bacteria_virus_cnt), 0) as virusAndBacteriaCount,
        COALESCE(sum(process_bacteria_virus_cnt_last), 0) as virusAndBacteriaCountLast,
        COALESCE(sum(process_bacteria_virus_cnt_last_y), 0) as virusAndBacteriaCountLastY
    </sql>

    <sql id="pathogenPositiveStat">
        count(distinct case when virus_flag='1' then process_id end) as virusDetectCount,
        count(distinct case when virus_flag='1' and virus_positive_flag='1' then  process_id end) as virusPositiveCount,
        count(distinct case when bacteria_flag='1' then process_id end)  as bacteriaDetectCount,
        count(distinct case when bacteria_flag='1' and bacteria_positive_flag='1' then  process_id end) as bacteriaPositiveCount,
        count(distinct case when bacteria_virus_flag='1' then  process_id end) as virusAndBacteriaCount
    </sql>

    <sql id="wherePathogenCriteria">
        <include refid="chooseWarningScenario"/>
        <include refid="regionChoose"/>
        <if test="startDate != null and endDate != null">
            and <include refid="timeChoose"/> between #{startDate} and #{endDate}
        </if>
    </sql>

    <select id="listPositiveSituation"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.ProcessPositiveSituationVO">
        select
        <include refid="pathogenPositiveStat"/>
        from
        ads.ads_mul_process_case_info i
        <include refid="regionJoinCondition"/>
        <include refid="joinDayDim"/>
        where 1=1
        <include refid="wherePathogenCriteria"/>
    </select>

    <select id="listPositiveTimeLine"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.ProcessPositiveSituationVO">
        select
        <include refid="chooseStatDate"/>
        <include refid="pathogenPositiveStat"/>
        from
        ads.ads_mul_process_case_info i
        <include refid="regionJoinCondition"/>
        <include refid="joinDayDim"/>
        where 1=1
        <include refid="wherePathogenCriteria"/>
        group by
        <include refid="groupByDateDim"/>
    </select>

    <sql id="fromWithAgeGroup">
        ads.ads_mul_process_case_info i
        JOIN dim.dim_age_group ag
        ON i.patient_age &gt;= ag.value_down
        AND i.patient_age &lt; ag.value_up
    </sql>

    <sql id="selectAgeGroup">
        CASE
        WHEN ag.value_code IS NOT NULL THEN ag.value_name
        ELSE '不详'
    END
    </sql>

    <sql id="joinDayDim">
        JOIN dim.dim_date_day d
        ON
        <include refid="dayChoose"/>
        BETWEEN d.start_time AND d.end_time
    </sql>

</mapper>