<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.DyqwEventMapper">


    <select id="getEmergencyEvents" resultType="com.iflytek.cdc.province.entity.ads.DyqwEvent">
        select
            vde.event_id,
            vde.event_type_id,
            vde.event_type,
            vde.event_name,
            vde.severity_grade_code,
            vde.severity_grade,
            vde.report_zone_code,
            vde.report_zone,
            vde.report_organization_code,
            vde.report_organization,
            vde.event_status_code,
            vde.event_status,
            vde.review_grade_code,
            vde.review_grade,
            vde.last_report_id,
            vde.event_create_time,
            vde.event_final_time,
            vde.event_delete_time,
            vde.latest_modify_time,
            vde.nation_review_time,
            vde.prov_review_time,
            vde.city_review_time,
            vde.county_review_time,
            vde.event_happen_time,
            vde.school_type_code,
            vde.event_review_status_code,
            vde.event_review_status,
            vde.event_review_person_id,
            vde.event_review_person,
            vde.event_review_time,
            vde.first_review_time,
            vde.yesterday_modify_time,
            vde.if_delete_once,
            vde.delete_reason,
            vde.merge_identity,
            vde.relate_event_id,
            vde.event_happen_district_code,
            vde.event_happen_district,
            vde.event_report_person_id,
            vde.event_report_person,
            vde.school_type_name
        from ads.v_dyqw_event vde
        join dim.dim_region_nation t
        on vde.event_happen_district_code =t.region_code
        <where>
            <include refid="areaChoose"/>
            <if test="startDate != null">
                <![CDATA[ and vde.event_happen_time >= #{startDate} ]]>
            </if>
            <if test="endDate != null">
                <![CDATA[ and vde.event_happen_time <= #{endDate} ]]>
            </if>
            <if test="eventStatus != null and eventStatus != ''">
                and vde.event_status = #{eventStatus}
            </if>
            <if test="eventType != null and eventType != ''">
                and vde.event_type = #{eventType}
            </if>
            <if test="severityGrade != null and severityGrade != ''">
                and vde.severity_grade = #{severityGrade}
            </if>
            <if test="eventId != null and eventId != ''">
                and vde.event_id like concat('%', #{eventId}, '%')
            </if>
        </where>
        order by vde.event_happen_time desc
    </select>
    <select id="getDownBox" resultType="com.iflytek.cdc.province.entity.ads.DyqwEvent">
        SELECT DISTINCT
            'eventType' as type,
            event_type as value
        FROM ads.v_dyqw_event vde
        JOIN dim.dim_region_nation t
        ON vde.event_happen_district_code =t.region_code
        WHERE event_type IS NOT NULL AND event_type != ''
        <include refid="areaChoose"/>
        UNION ALL

        SELECT DISTINCT
            'severityGrade' as type,
            severity_grade as value
        FROM ads.v_dyqw_event vde
        JOIN dim.dim_region_nation t
        ON vde.event_happen_district_code =t.region_code
        WHERE severity_grade IS NOT NULL AND severity_grade != ''
        <include refid="areaChoose"/>
        UNION ALL

        SELECT DISTINCT
            'eventStatus' as type,
            event_status as value
        FROM ads.v_dyqw_event vde
        JOIN dim.dim_region_nation t
        ON vde.event_happen_district_code =t.region_code
        WHERE event_status IS NOT NULL AND event_status != ''
        <include refid="areaChoose"/>

    </select>

    <resultMap id="dyqwEventResultMap" type="com.iflytek.cdc.province.entity.ads.DyqwEvent">
        <!-- 事件表(vde)字段映射 -->
        <id property="eventId" column="vde_event_id"/>
        <result property="eventTypeId" column="vde_event_type_id"/>
        <result property="eventType" column="vde_event_type"/>
        <result property="eventName" column="vde_event_name"/>
        <result property="severityGradeCode" column="vde_severity_grade_code"/>
        <result property="severityGrade" column="vde_severity_grade"/>
        <result property="reportZoneCode" column="vde_report_zone_code"/>
        <result property="reportZone" column="vde_report_zone"/>
        <result property="reportOrganizationCode" column="vde_report_organization_code"/>
        <result property="reportOrganization" column="vde_report_organization"/>
        <result property="eventStatusCode" column="vde_event_status_code"/>
        <result property="eventStatus" column="vde_event_status"/>
        <result property="reviewGradeCode" column="vde_review_grade_code"/>
        <result property="reviewGrade" column="vde_review_grade"/>
        <result property="lastReportId" column="vde_last_report_id"/>
        <result property="eventCreateTime" column="vde_event_create_time"/>
        <result property="eventFinalTime" column="vde_event_final_time"/>
        <result property="eventDeleteTime" column="vde_event_delete_time"/>
        <result property="latestModifyTime" column="vde_latest_modify_time"/>
        <result property="nationReviewTime" column="vde_nation_review_time"/>
        <result property="provReviewTime" column="vde_prov_review_time"/>
        <result property="cityReviewTime" column="vde_city_review_time"/>
        <result property="countyReviewTime" column="vde_county_review_time"/>
        <result property="eventHappenTime" column="vde_event_happen_time"/>
        <result property="schoolTypeCode" column="vde_school_type_code"/>
        <result property="eventReviewStatusCode" column="vde_event_review_status_code"/>
        <result property="eventReviewStatus" column="vde_event_review_status"/>
        <result property="eventReviewPersonId" column="vde_event_review_person_id"/>
        <result property="eventReviewPerson" column="vde_event_review_person"/>
        <result property="eventReviewTime" column="vde_event_review_time"/>
        <result property="firstReviewTime" column="vde_first_review_time"/>
        <result property="yesterdayModifyTime" column="vde_yesterday_modify_time"/>
        <result property="ifDeleteOnce" column="vde_if_delete_once"/>
        <result property="deleteReason" column="vde_delete_reason"/>
        <result property="mergeIdentity" column="vde_merge_identity"/>
        <result property="relateEventId" column="vde_relate_event_id"/>
        <result property="eventHappenDistrictCode" column="vde_event_happen_district_code"/>
        <result property="eventHappenDistrict" column="vde_event_happen_district"/>
        <result property="eventReportPersonId" column="vde_event_report_person_id"/>
        <result property="eventReportPerson" column="vde_event_report_person"/>
        <result property="schoolTypeName" column="vde_school_type_name"/>

        <!-- 一对多：事件对应的报告集合 -->
        <collection property="reportList" ofType="com.iflytek.cdc.province.entity.ads.DyqwEventReport">
            <!-- 报告表(vder)字段映射 -->
            <result property="reportId" column="vder_report_id"/>
            <result property="eventId" column="vder_event_id"/>
            <result property="eventName" column="vder_event_name"/>
            <result property="typeId" column="vder_type_id"/>
            <result property="typeName" column="vder_type_name"/>
            <result property="eventSeverityLevelCode" column="vder_event_severity_level_code"/>
            <result property="eventSeverityLevel" column="vder_event_severity_level"/>
            <result property="reportDistrictCode" column="vder_report_district_code"/>
            <result property="reportDistrict" column="vder_report_district"/>
            <result property="reportUnitCode" column="vder_report_unit_code"/>
            <result property="reportUnit" column="vder_report_unit"/>
            <result property="reportHappenDistrictCode" column="vder_report_happen_district_code"/>
            <result property="reportHappenDistrict" column="vder_report_happen_district"/>
            <result property="reportAddress" column="vder_report_address"/>
            <result property="exposedPopulation" column="vder_exposed_population"/>
            <result property="diseaseNumber" column="vder_disease_number"/>
            <result property="deadNumber" column="vder_dead_number"/>
            <result property="receiveTime" column="vder_receive_time"/>
            <result property="createTime" column="vder_create_time"/>
            <result property="userId" column="vder_user_id"/>
            <result property="latestModifyTime" column="vder_latest_modify_time"/>
            <result property="ifFinalReport" column="vder_if_final_report"/>
            <result property="firstPatientTime" column="vder_first_patient_time"/>
            <result property="lastPatientTime" column="vder_last_patient_time"/>
            <result property="eventHappenTime" column="vder_event_happen_time"/>
            <result property="nationReviewTime" column="vder_nation_review_time"/>
            <result property="provReviewTime" column="vder_prov_review_time"/>
            <result property="cityReviewTime" column="vder_city_review_time"/>
            <result property="countyReviewTime" column="vder_county_review_time"/>
            <result property="reviewLevelCode" column="vder_review_level_code"/>
            <result property="reviewLevel" column="vder_review_level"/>
            <result property="reportPerson" column="vder_report_person"/>
            <result property="telephone" column="vder_telephone"/>
            <result property="eventScope" column="vder_event_scope"/>
            <result property="directLost" column="vder_direct_lost"/>
            <result property="indirectLost" column="vder_indirect_lost"/>
            <result property="relateRepoerId" column="vder_relate_repoer_id"/>
            <result property="firstCheckTime" column="vder_first_check_time"/>
            <result property="pubMN" column="vder_pub_m_n"/>
            <result property="pubMP" column="vder_pub_m_p"/>
            <result property="pubMC" column="vder_pub_m_c"/>
            <result property="pubMCounty" column="vder_pub_m_county"/>
            <result property="medTN" column="vder_med_t_n"/>
            <result property="medTP" column="vder_med_t_p"/>
            <result property="medTCity" column="vder_med_t_city"/>
            <result property="medTC" column="vder_med_t_c"/>
            <result property="disPCN" column="vder_dis_p_c_n"/>
            <result property="disPCP" column="vder_dis_p_c_p"/>
            <result property="disPCCity" column="vder_dis_p_c_city"/>
            <result property="disPCC" column="vder_dis_p_c_c"/>
            <result property="heaSLN" column="vder_hea_s_l_n"/>
            <result property="heaSLP" column="vder_hea_s_l_p"/>
            <result property="heaSLCity" column="vder_hea_s_l_city"/>
            <result property="heaSLC" column="vder_hea_s_l_c"/>
            <result property="comSN" column="vder_com_s_n"/>
            <result property="comSP" column="vder_com_s_p"/>
            <result property="comSCity" column="vder_com_s_city"/>
            <result property="comSC" column="vder_com_s_c"/>
            <result property="otherNation" column="vder_other_nation"/>
            <result property="otherProvince" column="vder_other_province"/>
            <result property="otherCity" column="vder_other_city"/>
            <result property="otherContry" column="vder_other_contry"/>
            <result property="eventInformationSource" column="vder_event_information_source"/>
            <result property="eventHappenPlace" column="vder_event_happen_place"/>
            <result property="eventHappenReason1" column="vder_event_happen_reason_1"/>
            <result property="patientTreatmentProcess1" column="vder_patient_treatment_process_1"/>
            <result property="incidentControlMeasures" column="vder_incident_control_measures"/>
            <result property="poisonName" column="vder_poison_name"/>
            <result property="responsibilityUnit" column="vder_responsibility_unit"/>
            <result property="sceneBeginWork" column="vder_scene_begin_work"/>
            <result property="pathogenicFactors2" column="vder_pathogenic_factors_2"/>
            <result property="eventHappenReason2" column="vder_event_happen_reason_2"/>
            <result property="pollutantsTriggerEvents2" column="vder_pollutants_trigger_events_2"/>
            <result property="pollutedEnvironment" column="vder_polluted_environment"/>
            <result property="patientTreatmentProcess2" column="vder_patient_treatment_process_2"/>
            <result property="incidentControlMeasures2" column="vder_incident_control_measures_2"/>
            <result property="pathogenicFactors3" column="vder_pathogenic_factors_3"/>
            <result property="eventHappenReason3" column="vder_event_happen_reason_3"/>
            <result property="pollutantsTriggerEvents3" column="vder_pollutants_trigger_events_3"/>
            <result property="patientTreatmentProcess3" column="vder_patient_treatment_process_3"/>
            <result property="incidentControlMeasures3" column="vder_incident_control_measures_3"/>
            <result property="triggerEventsPesticide" column="vder_trigger_events_pesticide"/>
            <result property="pathogenicFactors4" column="vder_pathogenic_factors_4"/>
            <result property="eventHappenReason4" column="vder_event_happen_reason_4"/>
            <result property="patientTreatmentProcess4" column="vder_patient_treatment_process_4"/>
            <result property="incidentControlMeasures4" column="vder_incident_control_measures_4"/>
            <result property="schoolTypeName" column="vder_school_type_name"/>

            <!-- 一对多：报告对应的附件集合 -->
            <collection property="attachmentList" ofType="com.iflytek.cdc.province.entity.ads.DyqwEventAttachment">
                <!-- 附件表(vdea)字段映射 -->
                <result property="attachmentsId" column="vdea_attachments_id"/>
                <result property="attachmentsDesc" column="vdea_attachments_desc"/>
                <result property="blobFile" column="vdea_blob_file"/>
                <result property="reportId" column="vdea_report_id"/>
                <result property="attachmentsName" column="vdea_attachments_name"/>
                <result property="relateId" column="vdea_relate_id"/>
            </collection>
        </collection>
    </resultMap>


    <select id="getEventByEventId" resultMap="dyqwEventResultMap">
       SELECT
            vde.event_id AS vde_event_id,
            vde.event_type_id AS vde_event_type_id,
            vde.event_type AS vde_event_type,
            vde.event_name AS vde_event_name,
            vde.severity_grade_code AS vde_severity_grade_code,
            vde.severity_grade AS vde_severity_grade,
            vde.report_zone_code AS vde_report_zone_code,
            vde.report_zone AS vde_report_zone,
            vde.report_organization_code AS vde_report_organization_code,
            vde.report_organization AS vde_report_organization,
            vde.event_status_code AS vde_event_status_code,
            vde.event_status AS vde_event_status,
            vde.review_grade_code AS vde_review_grade_code,
            vde.review_grade AS vde_review_grade,
            vde.last_report_id AS vde_last_report_id,
            vde.event_create_time AS vde_event_create_time,
            vde.event_final_time AS vde_event_final_time,
            vde.event_delete_time AS vde_event_delete_time,
            vde.latest_modify_time AS vde_latest_modify_time,
            vde.nation_review_time AS vde_nation_review_time,
            vde.prov_review_time AS vde_prov_review_time,
            vde.city_review_time AS vde_city_review_time,
            vde.county_review_time AS vde_county_review_time,
            vde.event_happen_time AS vde_event_happen_time,
            vde.school_type_code AS vde_school_type_code,
            vde.event_review_status_code AS vde_event_review_status_code,
            vde.event_review_status AS vde_event_review_status,
            vde.event_review_person_id AS vde_event_review_person_id,
            vde.event_review_person AS vde_event_review_person,
            vde.event_review_time AS vde_event_review_time,
            vde.first_review_time AS vde_first_review_time,
            vde.yesterday_modify_time AS vde_yesterday_modify_time,
            vde.if_delete_once AS vde_if_delete_once,
            vde.delete_reason AS vde_delete_reason,
            vde.merge_identity AS vde_merge_identity,
            vde.relate_event_id AS vde_relate_event_id,
            vde.event_happen_district_code AS vde_event_happen_district_code,
            vde.event_happen_district AS vde_event_happen_district,
            vde.event_report_person_id AS vde_event_report_person_id,
            vde.event_report_person AS vde_event_report_person,
            vde.school_type_name AS vde_school_type_name,
            vder.report_id AS vder_report_id,
            vder.event_id AS vder_event_id,
            vder.event_name AS vder_event_name,
            vder.type_id AS vder_type_id,
            vder.type_name AS vder_type_name,
            vder.event_severity_level_code AS vder_event_severity_level_code,
            vder.event_severity_level AS vder_event_severity_level,
            vder.report_district_code AS vder_report_district_code,
            vder.report_district AS vder_report_district,
            vder.report_unit_code AS vder_report_unit_code,
            vder.report_unit AS vder_report_unit,
            vder.report_happen_district_code AS vder_report_happen_district_code,
            vder.report_happen_district AS vder_report_happen_district,
            vder.report_address AS vder_report_address,
            vder.school_type_code AS vder_school_type_code,
            vder.exposed_population AS vder_exposed_population,
            vder.disease_number AS vder_disease_number,
            vder.dead_number AS vder_dead_number,
            vder.receive_time AS vder_receive_time,
            vder.create_time AS vder_create_time,
            vder.user_id AS vder_user_id,
            vder.latest_modify_time AS vder_latest_modify_time,
            vder.if_final_report AS vder_if_final_report,
            vder.first_patient_time AS vder_first_patient_time,
            vder.last_patient_time AS vder_last_patient_time,
            vder.event_happen_time AS vder_event_happen_time,
            vder.nation_review_time AS vder_nation_review_time,
            vder.prov_review_time AS vder_prov_review_time,
            vder.city_review_time AS vder_city_review_time,
            vder.county_review_time AS vder_county_review_time,
            vder.review_level_code AS vder_review_level_code,
            vder.review_level AS vder_review_level,
            vder.report_person AS vder_report_person,
            vder.telephone AS vder_telephone,
            vder.event_scope AS vder_event_scope,
            vder.direct_lost AS vder_direct_lost,
            vder.indirect_lost AS vder_indirect_lost,
            vder.relate_repoer_id AS vder_relate_repoer_id,
            vder.first_check_time AS vder_first_check_time,
            vder.pub_m_n AS vder_pub_m_n,
            vder.pub_m_p AS vder_pub_m_p,
            vder.pub_m_c AS vder_pub_m_c,
            vder.pub_m_county AS vder_pub_m_county,
            vder.med_t_n AS vder_med_t_n,
            vder.med_t_p AS vder_med_t_p,
            vder.med_t_city AS vder_med_t_city,
            vder.med_t_c AS vder_med_t_c,
            vder.dis_p_c_n AS vder_dis_p_c_n,
            vder.dis_p_c_p AS vder_dis_p_c_p,
            vder.dis_p_c_city AS vder_dis_p_c_city,
            vder.dis_p_c_c AS vder_dis_p_c_c,
            vder.hea_s_l_n AS vder_hea_s_l_n,
            vder.hea_s_l_p AS vder_hea_s_l_p,
            vder.hea_s_l_city AS vder_hea_s_l_city,
            vder.hea_s_l_c AS vder_hea_s_l_c,
            vder.com_s_n AS vder_com_s_n,
            vder.com_s_p AS vder_com_s_p,
            vder.com_s_city AS vder_com_s_city,
            vder.com_s_c AS vder_com_s_c,
            vder.other_nation AS vder_other_nation,
            vder.other_province AS vder_other_province,
            vder.other_city AS vder_other_city,
            vder.other_contry AS vder_other_contry,
            vder.event_information_source AS vder_event_information_source,
            vder.event_happen_place AS vder_event_happen_place,
            vder.event_happen_reason_1 AS vder_event_happen_reason_1,
            vder.patient_treatment_process_1 AS vder_patient_treatment_process_1,
            vder.incident_control_measures AS vder_incident_control_measures,
            vder.poison_name AS vder_poison_name,
            vder.responsibility_unit AS vder_responsibility_unit,
            vder.scene_begin_work AS vder_scene_begin_work,
            vder.pathogenic_factors_2 AS vder_pathogenic_factors_2,
            vder.event_happen_reason_2 AS vder_event_happen_reason_2,
            vder.pollutants_trigger_events_2 AS vder_pollutants_trigger_events_2,
            vder.polluted_environment AS vder_polluted_environment,
            vder.patient_treatment_process_2 AS vder_patient_treatment_process_2,
            vder.incident_control_measures_2 AS vder_incident_control_measures_2,
            vder.pathogenic_factors_3 AS vder_pathogenic_factors_3,
            vder.event_happen_reason_3 AS vder_event_happen_reason_3,
            vder.pollutants_trigger_events_3 AS vder_pollutants_trigger_events_3,
            vder.patient_treatment_process_3 AS vder_patient_treatment_process_3,
            vder.incident_control_measures_3 AS vder_incident_control_measures_3,
            vder.trigger_events_pesticide AS vder_trigger_events_pesticide,
            vder.pathogenic_factors_4 AS vder_pathogenic_factors_4,
            vder.event_happen_reason_4 AS vder_event_happen_reason_4,
            vder.patient_treatment_process_4 AS vder_patient_treatment_process_4,
            vder.incident_control_measures_4 AS vder_incident_control_measures_4,
            vder.school_type_name AS vder_school_type_name,
            vdea.attachments_id AS vdea_attachments_id,
            vdea.attachments_desc AS vdea_attachments_desc,
            vdea.blob_file AS vdea_blob_file,
            vdea.report_id AS vdea_report_id,
            vdea.attachments_name AS vdea_attachments_name,
            vdea.relate_id AS vdea_relate_id
        FROM ads.v_dyqw_event vde
        LEFT JOIN  ads.v_dyqw_event_report vder ON vde.event_id = vder.event_id
        LEFT JOIN ads.v_dyqw_event_attachment vdea  ON vder.report_id = vdea.report_id
        WHERE vde.event_id = #{eventId}
    </select>

    <sql id="areaChoose">
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and t.province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and t.city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and t.district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and t.street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>
</mapper>