<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcdmMetadataTableColumnInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableColumnInfo">
    <!--@mbg.generated-->
    <!--@Table tb_cdcdm_metadata_table_column_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="table_id" jdbcType="VARCHAR" property="tableId" />
    <result column="column_code" jdbcType="VARCHAR" property="columnCode" />
    <result column="column_name" jdbcType="VARCHAR" property="columnName" />
    <result column="column_type" jdbcType="VARCHAR" property="columnType" />
    <result column="column_desc" jdbcType="VARCHAR" property="columnDesc" />
    <result column="data_type" jdbcType="VARCHAR" property="dataType" />
    <result column="business_column" jdbcType="VARCHAR" property="businessColumn" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, table_id, column_code, "column_name", column_type, column_desc, data_type, business_column, 
    memo, create_time, update_time, creator, updater, delete_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcdm_metadata_table_column_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from tb_cdcdm_metadata_table_column_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableColumnInfo">
    <!--@mbg.generated-->
    insert into tb_cdcdm_metadata_table_column_info (id, table_id, column_code,
      "column_name", column_type, column_desc, 
      data_type, business_column, memo,
      create_time, update_time, creator,
      updater, delete_flag)
    values (#{id,jdbcType=VARCHAR}, #{tableId,jdbcType=VARCHAR}, #{columnCode,jdbcType=VARCHAR}, 
      #{columnName,jdbcType=VARCHAR}, #{columnType,jdbcType=VARCHAR}, #{columnDesc,jdbcType=VARCHAR}, 
      #{dataType,jdbcType=VARCHAR}, #{businessColumn,jdbcType=VARCHAR}, #{memo,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{v,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR},
      #{updater,jdbcType=VARCHAR}, #{deleteFlag,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableColumnInfo">
    <!--@mbg.generated-->
    insert into tb_cdcdm_metadata_table_column_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        id,
      </if>
      <if test="tableId != null and tableId != ''">
        table_id,
      </if>
      <if test="columnCode != null and columnCode != ''">
        column_code,
      </if>
      <if test="columnName != null and columnName != ''">
        "column_name",
      </if>
      <if test="columnType != null and columnType != ''">
        column_type,
      </if>
      <if test="columnDesc != null and columnDesc != ''">
        column_desc,
      </if>
      <if test="dataType != null and dataType != ''">
        data_type,
      </if>
      <if test="businessColumn != null and businessColumn != ''">
        business_column,
      </if>
      <if test="memo != null and memo != ''">
        memo,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null and creator != ''">
        creator,
      </if>
      <if test="updater != null and updater != ''">
        updater,
      </if>
      <if test="deleteFlag != null and deleteFlag != ''">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="tableId != null and tableId != ''">
        #{tableId,jdbcType=VARCHAR},
      </if>
      <if test="columnCode != null and columnCode != ''">
        #{columnCode,jdbcType=VARCHAR},
      </if>
      <if test="columnName != null and columnName != ''">
        #{columnName,jdbcType=VARCHAR},
      </if>
      <if test="columnType != null and columnType != ''">
        #{columnType,jdbcType=VARCHAR},
      </if>
      <if test="columnDesc != null and columnDesc != ''">
        #{columnDesc,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null and dataType != ''">
        #{dataType,jdbcType=VARCHAR},
      </if>
      <if test="businessColumn != null and businessColumn != ''">
        #{businessColumn,jdbcType=VARCHAR},
      </if>
      <if test="memo != null and memo != ''">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null and updater != ''">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null and deleteFlag != ''">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableColumnInfo">
    <!--@mbg.generated-->
    update tb_cdcdm_metadata_table_column_info
    <set>
      <if test="tableId != null and tableId != ''">
        table_id = #{tableId,jdbcType=VARCHAR},
      </if>
      <if test="columnCode != null and columnCode != ''">
        column_code = #{columnCode,jdbcType=VARCHAR},
      </if>
      <if test="columnName != null and columnName != ''">
        "column_name" = #{columnName,jdbcType=VARCHAR},
      </if>
      <if test="columnType != null and columnType != ''">
        column_type = #{columnType,jdbcType=VARCHAR},
      </if>
      <if test="columnDesc != null and columnDesc != ''">
        column_desc = #{columnDesc,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null and dataType != ''">
        data_type = #{dataType,jdbcType=VARCHAR},
      </if>
      <if test="businessColumn != null and businessColumn != ''">
        business_column = #{businessColumn,jdbcType=VARCHAR},
      </if>
      <if test="memo != null and memo != ''">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null and updater != ''">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null and deleteFlag != ''">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableColumnInfo">
    <!--@mbg.generated-->
    update tb_cdcdm_metadata_table_column_info
    set table_id = #{tableId,jdbcType=VARCHAR},
      column_code = #{columnCode,jdbcType=VARCHAR},
      "column_name" = #{columnName,jdbcType=VARCHAR},
      column_type = #{columnType,jdbcType=VARCHAR},
      column_desc = #{columnDesc,jdbcType=VARCHAR},
      data_type = #{dataType,jdbcType=VARCHAR},
      business_column = #{businessColumn,jdbcType=VARCHAR},
      memo = #{memo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      updater = #{updater,jdbcType=VARCHAR},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="selectByTableAndBusiColName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tb_cdcdm_metadata_table_column_info
    where table_code = #{tableCode,jdbcType=VARCHAR}
      and (column_name = #{name,jdbcType=VARCHAR} or business_column = #{name,jdbcType=VARCHAR}
            or column_code = #{name,jdbcType=VARCHAR} or id = #{name,jdbcType=VARCHAR})
    limit 1
  </select>

    <select id="getAllColumnInfo"
            resultType="com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableColumnInfo">
        select <include refid="Base_Column_List"/>
        from tb_cdcdm_metadata_table_column_info
        where delete_flag = '0'
    </select>

    <select id="getTableColumnInfoByTableCode"
            resultType="com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableColumnInfo">
      select <include refid="Base_Column_List"/>
      from tb_cdcdm_metadata_table_column_info
      where delete_flag = '0' and table_code = #{tableCode}
    </select>
</mapper>