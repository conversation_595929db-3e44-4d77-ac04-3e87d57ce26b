<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrExposureHistoryInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsEdrExposureHistoryInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_edr_exposure_history_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="create_org" jdbcType="VARCHAR" property="createOrg" />
    <result column="update_org" jdbcType="VARCHAR" property="updateOrg" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="contactflag_code" jdbcType="VARCHAR" property="contactflagCode" />
    <result column="venereal_dis_code" jdbcType="VARCHAR" property="venerealDisCode" />
    <result column="venereal_dis" jdbcType="VARCHAR" property="venerealDis" />
    <result column="contact_type_code" jdbcType="VARCHAR" property="contactTypeCode" />
    <result column="contact_type" jdbcType="VARCHAR" property="contactType" />
    <result column="inject_count" jdbcType="INTEGER" property="injectCount" />
    <result column="nonweb_count" jdbcType="INTEGER" property="nonwebCount" />
    <result column="sm_count" jdbcType="INTEGER" property="smCount" />
    <result column="contact_object_code" jdbcType="VARCHAR" property="contactObjectCode" />
    <result column="contact_object" jdbcType="VARCHAR" property="contactObject" />
    <result column="contact_time" jdbcType="TIMESTAMP" property="contactTime" />
    <result column="discovery_mode_code" jdbcType="VARCHAR" property="discoveryModeCode" />
    <result column="discovery_mode" jdbcType="VARCHAR" property="discoveryMode" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="mpi_id" jdbcType="VARCHAR" property="mpiId" />
    <result column="create_org_name" jdbcType="VARCHAR" property="createOrgName" />
    <result column="update_org_name" jdbcType="VARCHAR" property="updateOrgName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, etl_create_datetime, etl_update_datetime, event_id, empi_id, create_org, update_org, 
    org_name, contactflag_code, venereal_dis_code, venereal_dis, contact_type_code, contact_type, 
    inject_count, nonweb_count, sm_count, contact_object_code, contact_object, contact_time, 
    discovery_mode_code, discovery_mode, create_user, create_time, update_user, update_time, 
    source_id, mpi_id, create_org_name, update_org_name
  </sql>

  <select id="getExposureHistoryByEventId" parameterType="string"
          resultType="com.iflytek.cdc.province.model.vo.edrcase.AdsEdrExposureHistoryInfoVO">

    select
    id,
    etl_create_datetime,
    etl_update_datetime,
    event_id,
    empi_id,
    create_org,
    update_org,
    org_name,
    contactflag_code,
    venereal_dis_code,
    venereal_dis,
    contact_type_code,
    contact_type,
    inject_count,
    nonweb_count,
    sm_count,
    contact_object_code,
    contact_object,
    contact_time,
    discovery_mode_code,
    discovery_mode
    from ads.ads_edr_exposure_history_info
    where event_id = #{eventId}
    order by create_time desc
  </select>
</mapper>