<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcewProcessReportCheckLogMapper">

    <sql id="Base_Column_List">
        id, report_id, process_id, disease_type, check_identify_status, reject_reason, create_time,
        creator, creator_id
    </sql>

    <insert id="insert">
        insert into tb_cdcew_process_report_check_log
        (id, report_id, process_id, disease_type, check_identify_status, reject_reason, create_time, creator,
        creator_id)
        values
        (#{id}, #{reportId}, #{processId}, #{diseaseType}, #{checkIdentifyStatus}, #{rejectReason}, #{createTime},
        #{creator}, #{creatorId})
    </insert>

    <select id="getCheckLogsByReportId"
            resultType="com.iflytek.cdc.province.entity.bu.TbCdcewProcessReportCheckLog">
        select <include refid="Base_Column_List"/>
        from tb_cdcew_process_report_check_log
        where report_id = #{reportId}
        <if test="diseaseType != null and diseaseType != ''">
            and disease_type = #{diseaseType}
        </if>
        order by create_time desc
    </select>

    <select id="getLatestCheckLogByReportId"
            resultType="com.iflytek.cdc.province.entity.bu.TbCdcewProcessReportCheckLog">
        select <include refid="Base_Column_List"/>
        from tb_cdcew_process_report_check_log
        where report_id = #{reportId}
        <if test="diseaseType != null and diseaseType != ''">
            and disease_type = #{diseaseType}
        </if>
        order by create_time desc
        limit 1
    </select>

</mapper>