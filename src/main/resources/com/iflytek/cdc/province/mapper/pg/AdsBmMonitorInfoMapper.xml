<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsBmMonitorInfoMapper">

    <select id="getMiFoOvitrapDTOList" resultType="com.iflytek.cdc.province.model.dto.MiFoOvitrapDTO">
        select * from ads.v_ads_mi_fo_ovitrap where monitor_job_id = #{relationTaskId}
    </select>

    <select id="getMiFoBreteauIndexDTOList" resultType="com.iflytek.cdc.province.model.dto.MiFoBreteauIndexDTO">
        select * from ads.v_ads_mi_fo_breteau_index where monitor_job_id = #{relationTaskId}
    </select>

    <select id="getMiFoDoubleMosquitoDTOList" resultType="com.iflytek.cdc.province.model.dto.MiFoDoubleMosquitoDTO">
        select * from ads.v_ads_mi_fo_double_mosquito where monitor_job_id = #{relationTaskId}
    </select>

    <select id="getPopulationSampleDTOList" resultType="com.iflytek.cdc.province.model.dto.PdPopulationSampleDTO">
        select * from ads.v_ads_pd_population_sample where task_id = #{relationTaskId}
    </select>

    <select id="getObjectSampleDTOList" resultType="com.iflytek.cdc.province.model.dto.PdObjectSampleDTO">
        select * from ads.v_ads_pd_object_sample where task_id = #{relationTaskId}
    </select>

    <select id="getBiologicalSampleDTOList" resultType="com.iflytek.cdc.province.model.dto.PdBiologicalSampleDTO">
        select * from ads.v_ads_pd_biological_sample where task_id = #{relationTaskId}
    </select>

    <select id="getFoodSampleDTOList" resultType="com.iflytek.cdc.province.model.dto.PdFoodSampleDTO">
        select * from ads.v_ads_pd_food_sample where task_id = #{relationTaskId}
    </select>

    <select id="getEnvironmentalSampleDTOList" resultType="com.iflytek.cdc.province.model.dto.PdEnvironmentalSampleDTO">
        select * from ads.v_ads_pd_environmental_sample where task_id = #{relationTaskId}
    </select>
</mapper>