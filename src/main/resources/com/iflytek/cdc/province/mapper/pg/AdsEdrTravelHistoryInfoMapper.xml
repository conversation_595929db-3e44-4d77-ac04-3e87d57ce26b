<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrTravelHistoryInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsEdrTravelHistoryInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_edr_travel_history_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="create_org" jdbcType="VARCHAR" property="createOrg" />
    <result column="create_org_name" jdbcType="VARCHAR" property="createOrgName" />
    <result column="foreign_type_code" jdbcType="VARCHAR" property="foreignTypeCode" />
    <result column="foreign_type" jdbcType="VARCHAR" property="foreignType" />
    <result column="place_code" jdbcType="VARCHAR" property="placeCode" />
    <result column="place" jdbcType="VARCHAR" property="place" />
    <result column="residence_days" jdbcType="INTEGER" property="residenceDays" />
    <result column="epidemic_source_risk_code" jdbcType="VARCHAR" property="epidemicSourceRiskCode" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="mpi_id" jdbcType="VARCHAR" property="mpiId" />
    <result column="update_org" jdbcType="VARCHAR" property="updateOrg" />
    <result column="update_org_name" jdbcType="VARCHAR" property="updateOrgName" />
    <result column="create_org_id" jdbcType="VARCHAR" property="createOrgId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, etl_create_datetime, etl_update_datetime, event_id, empi_id, create_org, create_org_name, 
    foreign_type_code, foreign_type, place_code, place, residence_days, epidemic_source_risk_code, 
    create_user, update_user, update_time, source_id, mpi_id, update_org, update_org_name, 
    create_org_id
  </sql>


  <select id="getTravelHistoryByEventId" parameterType="string"
          resultType="com.iflytek.cdc.province.model.vo.edrcase.AdsEdrTravelHistoryInfoVO">
    select
    id,
    etl_create_datetime,
    etl_update_datetime,
    event_id,
    empi_id,
    foreign_type_code,
    foreign_type,
    place_code,
    place,
    residence_days,
    epidemic_source_risk_code
    from ads.ads_edr_travel_history_info
    where event_id = #{eventId}


  </select>
</mapper>