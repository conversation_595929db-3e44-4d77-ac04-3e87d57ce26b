<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcdmDataFormValueMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormValue">
    <!--@mbg.generated-->
    <!--@Table tb_cdcdm_data_form_value-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="form_template_detail_id" jdbcType="VARCHAR" property="formTemplateDetailId" />
    <result column="model_id" jdbcType="VARCHAR" property="modelId" />
    <result column="model_version_id" jdbcType="VARCHAR" property="modelVersionId" />
    <result column="model_form_id" jdbcType="VARCHAR" property="modelFormId" />
    <result column="model_form_identity_id" jdbcType="VARCHAR" property="modelFormIdentityId" />
    <result column="model_form_name" jdbcType="VARCHAR" property="modelFormName" />
    <result column="data_json" jdbcType="VARCHAR" property="dataJson" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, form_template_detail_id, model_id, model_version_id, model_form_id, model_form_identity_id, 
    model_form_name, data_json, create_time, creator, update_time, updater
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcdm_data_form_value
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from tb_cdcdm_data_form_value
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormValue">
    <!--@mbg.generated-->
    insert into tb_cdcdm_data_form_value (id, form_template_detail_id, model_id,
      model_version_id, model_form_id, model_form_identity_id, 
      model_form_name, data_json, create_time, 
      creator, update_time, updater
      )
    values (#{id,jdbcType=VARCHAR}, #{formTemplateDetailId,jdbcType=VARCHAR}, #{modelId,jdbcType=VARCHAR}, 
      #{modelVersionId,jdbcType=VARCHAR}, #{modelFormId,jdbcType=VARCHAR}, #{modelFormIdentityId,jdbcType=VARCHAR}, 
      #{modelFormName,jdbcType=VARCHAR}, #{dataJson,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormValue">
    <!--@mbg.generated-->
    insert into tb_cdcdm_data_form_value
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        id,
      </if>
      <if test="formTemplateDetailId != null and formTemplateDetailId != ''">
        form_template_detail_id,
      </if>
      <if test="modelId != null and modelId != ''">
        model_id,
      </if>
      <if test="modelVersionId != null and modelVersionId != ''">
        model_version_id,
      </if>
      <if test="modelFormId != null and modelFormId != ''">
        model_form_id,
      </if>
      <if test="modelFormIdentityId != null and modelFormIdentityId != ''">
        model_form_identity_id,
      </if>
      <if test="modelFormName != null and modelFormName != ''">
        model_form_name,
      </if>
      <if test="dataJson != null and dataJson != ''">
        data_json,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null and creator != ''">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null and updater != ''">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="formTemplateDetailId != null and formTemplateDetailId != ''">
        #{formTemplateDetailId,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null and modelId != ''">
        #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="modelVersionId != null and modelVersionId != ''">
        #{modelVersionId,jdbcType=VARCHAR},
      </if>
      <if test="modelFormId != null and modelFormId != ''">
        #{modelFormId,jdbcType=VARCHAR},
      </if>
      <if test="modelFormIdentityId != null and modelFormIdentityId != ''">
        #{modelFormIdentityId,jdbcType=VARCHAR},
      </if>
      <if test="modelFormName != null and modelFormName != ''">
        #{modelFormName,jdbcType=VARCHAR},
      </if>
      <if test="dataJson != null and dataJson != ''">
        #{dataJson,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null and updater != ''">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormValue">
    <!--@mbg.generated-->
    update tb_cdcdm_data_form_value
    <set>
      <if test="formTemplateDetailId != null and formTemplateDetailId != ''">
        form_template_detail_id = #{formTemplateDetailId,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null and modelId != ''">
        model_id = #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="modelVersionId != null and modelVersionId != ''">
        model_version_id = #{modelVersionId,jdbcType=VARCHAR},
      </if>
      <if test="modelFormId != null and modelFormId != ''">
        model_form_id = #{modelFormId,jdbcType=VARCHAR},
      </if>
      <if test="modelFormIdentityId != null and modelFormIdentityId != ''">
        model_form_identity_id = #{modelFormIdentityId,jdbcType=VARCHAR},
      </if>
      <if test="modelFormName != null and modelFormName != ''">
        model_form_name = #{modelFormName,jdbcType=VARCHAR},
      </if>
      <if test="dataJson != null and dataJson != ''">
        data_json = #{dataJson,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null and updater != ''">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormValue">
    <!--@mbg.generated-->
    update tb_cdcdm_data_form_value
    set form_template_detail_id = #{formTemplateDetailId,jdbcType=VARCHAR},
      model_id = #{modelId,jdbcType=VARCHAR},
      model_version_id = #{modelVersionId,jdbcType=VARCHAR},
      model_form_id = #{modelFormId,jdbcType=VARCHAR},
      model_form_identity_id = #{modelFormIdentityId,jdbcType=VARCHAR},
      model_form_name = #{modelFormName,jdbcType=VARCHAR},
      data_json = #{dataJson,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>