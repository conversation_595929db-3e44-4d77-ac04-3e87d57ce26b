<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcdmDataFormTemplateMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormTemplate">
    <!--@mbg.generated-->
    <!--@Table tb_cdcdm_data_form_template-->
    <id column="form_template_detail_id" jdbcType="VARCHAR" property="formTemplateDetailId" />
    <result column="form_template_code" jdbcType="VARCHAR" property="formTemplateCode" />
    <result column="form_template_name" jdbcType="VARCHAR" property="formTemplateName" />
    <result column="config_info" jdbcType="VARCHAR" property="configInfo" />
    <result column="config_json" jdbcType="VARCHAR" property="configJson" />
    <result column="model_id" jdbcType="VARCHAR" property="modelId" />
    <result column="model_name" jdbcType="VARCHAR" property="modelName" />
    <result column="model_version_id" jdbcType="VARCHAR" property="modelVersionId" />
    <result column="is_enable" jdbcType="SMALLINT" property="isEnable" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="key_field" jdbcType="VARCHAR" property="keyField" />
    <result column="master_table_id" jdbcType="VARCHAR" property="masterTableId"/>
    <result column="master_table" jdbcType="VARCHAR" property="masterTable"/>
    <result column="fields_config" jdbcType="VARCHAR" property="fieldsConfig"/>
    <result column="model_group" jdbcType="VARCHAR" property="modelGroup"/>
    <result column="web_config" jdbcType="VARCHAR" property="webConfig"/>
    <result column="related_disease" jdbcType="VARCHAR" property="relatedDisease"/>
    <result column="business_key" jdbcType="VARCHAR" property="businessKey"/>
    <result column="retrieve_table" jdbcType="VARCHAR" property="retrieveTable"/>
    <result column="es_index_name" jdbcType="VARCHAR" property="esIndexName"/>
 </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    form_template_detail_id, form_template_code, form_template_name, config_info, config_json, model_id, model_name, model_version_id,
    is_enable, is_deleted, create_time, creator, update_time, updater, key_field, master_table_id, master_table, fields_config, model_group,
    web_config, related_disease, business_key, retrieve_table,es_index_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcdm_data_form_template
    where form_template_detail_id = #{formTemplateDetailId,jdbcType=VARCHAR}
  </select>

  <select id="getDataModelByCode" resultType="com.iflytek.cdc.edr.vo.dm.DataModelDetailVO">
    select
    t.model_id, t.model_name, t.model_version_id, t.key_field, t.config_info, t.master_table_id, t.master_table,
    t.fields_config, m.model_type, v.model_label, t.web_config, t.business_key
    from tb_cdcdm_data_form_template t
    left join tb_cdcdm_data_model m
    on t.model_id = m.model_id
    left join tb_cdcdm_data_model_version v
    on m.model_id = v.model_id
    where v.status = #{status, jdbcType=VARCHAR}
    <if test="formTemplateCode != null and formTemplateCode != ''">
      and form_template_code like concat ('%', #{formTemplateCode, jdbcType=VARCHAR}, '%')
    </if>
    <if test="modelVersionId != null and modelVersionId != ''">
      and t.model_version_id = #{modelVersionId}
    </if>
    <if test="configInfoList != null and configInfoList.size() > 0">
      and config_info in
      <foreach collection="configInfoList" item="item" index="index" open="(" close=")" separator=",">
          #{item}
      </foreach>
    </if>
  </select>

  <select id="getDataModelByModelId" resultType="com.iflytek.cdc.edr.vo.dm.DataModelDetailVO">
    select
    t.model_id, t.model_name, t.model_version_id, t.key_field, t.config_info, t.master_table_id, t.master_table,
    t.fields_config, m.model_type, v.model_label, t.web_config, t.related_disease, t.business_key, t.retrieve_table
    from tb_cdcdm_data_form_template t
    left join tb_cdcdm_data_model m
    on t.model_id = m.model_id
    left join tb_cdcdm_data_model_version v
    on m.model_id = v.model_id
    where v.status = #{status, jdbcType=VARCHAR}
    <if test="modelId != null and modelId != ''">
      and t.model_id = #{modelId}
    </if>
    limit 1
  </select>

    <select id="getModelConfigByModelId"
            resultType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormTemplate">
      select <include refid="Base_Column_List"/>
      from tb_cdcdm_data_form_template
      where model_id =#{modelId, jdbcType=VARCHAR}
      limit 1
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from tb_cdcdm_data_form_template
    where form_template_detail_id = #{formTemplateDetailId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormTemplate">
    <!--@mbg.generated-->
    insert into tb_cdcdm_data_form_template (form_template_detail_id, form_template_code,
      form_template_name, config_info, config_json, 
      model_id, model_name, model_version_id, is_enable,
      is_deleted, create_time, creator, 
      update_time, updater, key_field)
    values (#{formTemplateDetailId,jdbcType=VARCHAR}, #{formTemplateCode,jdbcType=VARCHAR}, 
      #{formTemplateName,jdbcType=VARCHAR}, #{configInfo,jdbcType=VARCHAR}, #{configJson,jdbcType=VARCHAR}, 
      #{modelId,jdbcType=VARCHAR}, #{modelName,jdbcType=VARCHAR}, #{modelVersionId,jdbcType=VARCHAR}, #{isEnable,jdbcType=SMALLINT},
      #{isDeleted,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}, #{keyField,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormTemplate">
    <!--@mbg.generated-->
    insert into tb_cdcdm_data_form_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="formTemplateDetailId != null and formTemplateDetailId != ''">
        form_template_detail_id,
      </if>
      <if test="formTemplateCode != null and formTemplateCode != ''">
        form_template_code,
      </if>
      <if test="formTemplateName != null and formTemplateName != ''">
        form_template_name,
      </if>
      <if test="configInfo != null and configInfo != ''">
        config_info,
      </if>
      <if test="configJson != null and configJson != ''">
        config_json,
      </if>
      <if test="modelId != null and modelId != ''">
        model_id,
      </if>
      <if test="modelName != null and modelName != ''">
        model_name,
      </if>
      <if test="modelVersionId != null and modelVersionId != ''">
        model_version_id,
      </if>
      <if test="isEnable != null">
        is_enable,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null and creator != ''">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null and updater != ''">
        updater,
      </if>
      <if test="keyField != null and keyField != ''">
        key_field,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="formTemplateDetailId != null and formTemplateDetailId != ''">
        #{formTemplateDetailId,jdbcType=VARCHAR},
      </if>
      <if test="formTemplateCode != null and formTemplateCode != ''">
        #{formTemplateCode,jdbcType=VARCHAR},
      </if>
      <if test="formTemplateName != null and formTemplateName != ''">
        #{formTemplateName,jdbcType=VARCHAR},
      </if>
      <if test="configInfo != null and configInfo != ''">
        #{configInfo,jdbcType=VARCHAR},
      </if>
      <if test="configJson != null and configJson != ''">
        #{configJson,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null and modelId != ''">
        #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null and modelName != ''">
        #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="modelVersionId != null and modelVersionId != ''">
        #{modelVersionId,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null and updater != ''">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="keyField != null and keyField != ''">
        #{keyField,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormTemplate">
    <!--@mbg.generated-->
    update tb_cdcdm_data_form_template
    <set>
      <if test="formTemplateCode != null and formTemplateCode != ''">
        form_template_code = #{formTemplateCode,jdbcType=VARCHAR},
      </if>
      <if test="formTemplateName != null and formTemplateName != ''">
        form_template_name = #{formTemplateName,jdbcType=VARCHAR},
      </if>
      <if test="configInfo != null and configInfo != ''">
        config_info = #{configInfo,jdbcType=VARCHAR},
      </if>
      <if test="configJson != null and configJson != ''">
        config_json = #{configJson,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null and modelId != ''">
        model_id = #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null and modelName != ''">
        model_name = #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="modelVersionId != null and modelVersionId != ''">
        model_version_id = #{modelVersionId,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        is_enable = #{isEnable,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null and updater != ''">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="keyField != null and keyField != ''">
        key_field = #{keyField,jdbcType=VARCHAR},
      </if>
    </set>
    where form_template_detail_id = #{formTemplateDetailId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormTemplate">
    <!--@mbg.generated-->
    update tb_cdcdm_data_form_template
    set form_template_code = #{formTemplateCode,jdbcType=VARCHAR},
      form_template_name = #{formTemplateName,jdbcType=VARCHAR},
      config_info = #{configInfo,jdbcType=VARCHAR},
      config_json = #{configJson,jdbcType=VARCHAR},
      model_id = #{modelId,jdbcType=VARCHAR},
      model_name = #{modelName,jdbcType=VARCHAR},
      model_version_id = #{modelVersionId,jdbcType=VARCHAR},
      is_enable = #{isEnable,jdbcType=SMALLINT},
      is_deleted = #{isDeleted,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR},
      key_field = #{keyField,jdbcType=VARCHAR}
    where form_template_detail_id = #{formTemplateDetailId,jdbcType=VARCHAR}
  </update>
</mapper>