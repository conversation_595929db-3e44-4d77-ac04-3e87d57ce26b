<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMonitorClueInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.dim.AdsMonitorClueInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_monitor_clue_info-->
    <id column="clue_id" jdbcType="VARCHAR" property="clueId" />
    <id column="type" jdbcType="VARCHAR" property="type" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="day" jdbcType="DATE" property="day" />
    <result column="week" jdbcType="SMALLINT" property="week" />
    <result column="ten_day" jdbcType="VARCHAR" property="tenDay" />
    <result column="month" jdbcType="SMALLINT" property="month" />
    <result column="quarter" jdbcType="SMALLINT" property="quarter" />
    <result column="year" jdbcType="SMALLINT" property="year" />
    <result column="key_word" jdbcType="VARCHAR" property="keyWord" />
    <result column="source_info" jdbcType="VARCHAR" property="sourceInfo" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="district_code" jdbcType="VARCHAR" property="districtCode" />
    <result column="district_name" jdbcType="VARCHAR" property="districtName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    clue_id, "type", etl_create_datetime, etl_update_datetime, "day", week, ten_day, 
    "month", quarter, "year", key_word, source_info, province_code, province_name, city_code, 
    city_name, district_code, district_name
  </sql>


  <sql id="areaCriteria">
    <if test="provinceCode != null and provinceCode != ''">
      and province_code = #{provinceCode}
    </if>
    <if test="cityCode != null and cityCode != ''">
      and city_code = #{cityCode}
    </if>
    <if test="districtCode != null and districtCode != ''">
      and district_code = #{districtCode}
    </if>
    <if test="streetCode != null and streetCode != ''">
      and street_code = #{streetCode}
    </if>
    <if test="provinceCodes != null and provinceCodes.size()&gt;0">
      and province_code in
      <foreach close=")" collection="provinceCodes" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="cityCodes != null and cityCodes.size()&gt;0">
      and city_code in
      <foreach close=")" collection="cityCodes" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="districtCodes != null and districtCodes.size()&gt;0">
      and district_code in
      <foreach close=")" collection="districtCodes" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="streetCodes != null and streetCodes.size()&gt;0">
      and street_code in
      <foreach close=")" collection="streetCodes" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
  </sql>

  <sql id="topicCriteria">
    <if test="topicKeywords != null and topicKeywords.size() != 0">
      and key_word in (
      <foreach collection="topicKeywords" item="item" separator=",">
        #{item,jdbcType=VARCHAR}
      </foreach>
      )
    </if>
  </sql>

  <select id="selectClueSources" resultType="java.lang.String">
      select distinct source_info
      from ads.ads_monitor_clue_info
  </select>

  <select id="clueSearch" resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorClueInfoVO">
      select
      <include refid="Base_Column_List" />
      from ads.ads_monitor_clue_info
      where 1 = 1
      <if test="clueId != null and clueId != ''">
          and clue_id = #{clueId,jdbcType=VARCHAR}
      </if>
      <if test="keyword != null and keyword != ''">
          and key_word like concat('%', #{keyword,jdbcType=VARCHAR}, '%')
      </if>
      <if test="startDate != null ">
          and "day" &gt;=  CAST(#{startDate,jdbcType=TIMESTAMP} AS DATE)
      </if>
      <if test="endDate != null ">
          and "day" &lt;= CAST(#{endDate,jdbcType=TIMESTAMP} AS DATE)
      </if>
      <if test="clueType != null and clueType != ''">
          and type = #{clueType,jdbcType=VARCHAR}
      </if>
      <if test="clueSource != null and clueSource != ''">
          and source_info = #{clueSource,jdbcType=VARCHAR}
      </if>
      <include refid="areaCriteria" />
      <include refid="topicCriteria" />
      order by day desc, etl_update_datetime desc
  </select>

  <select id="selectByClueId" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from ads.ads_monitor_clue_info
      where clue_id = #{clueId,jdbcType=VARCHAR}
      and type = #{type,jdbcType=VARCHAR}
    </select>
</mapper>