<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMsSyndromeReportQMapper">

    <sql id="dayDb">
        <choose>
            <when test="timeType == 'identifyTime' || timeType == 'reportTime'">
                ads.ads_ms_syndrome_report_q_it_d
            </when>
            <when test="timeType == 'checkTime'">
                ads.ads_ms_syndrome_report_q_ct_d
            </when>
            <otherwise>
                ads.ads_ms_syndrome_report_q_ot_d
            </otherwise>
        </choose>
    </sql>

    <sql id="sumEval">
        sum( coalesce(process_new_cnt,0) )       processNewCnt,
        sum( coalesce(need_check_cnt,0) )        needCheckCnt,
        sum( coalesce(noneed_check_cnt,0) )      noneedCheckCnt,
        sum( coalesce(check_cnt,0) )             checkCnt,
        sum( coalesce(identify_intime_cnt,0) )   identifyIntimeCnt,
        sum( coalesce(identify_delay_cnt,0) )    identifyDelayCnt,
        sum( coalesce(check_intime_cnt,0) )      checkIntimeCnt,
        sum( coalesce(check_delay_cnt,0) )       checkDelayCnt,
        sum( coalesce(identify_true_cnt,0) )     identifyTrueCnt,
        sum( coalesce(true_out_cnt,0) )          trueOutCnt,
        sum( coalesce(leak_cnt,0) )              leakCnt,
        sum( coalesce(false_identify_cnt,0) )    falseIdentifyCnt,
        sum( coalesce(full_cnt,0) )              fullCnt,
        sum( coalesce(conform_cnt,0) )           conformCnt
    </sql>

    <sql id="whereEval">
        <if test="syndromeCode != null and syndromeCode != ''">
            and syndrome_code = #{syndromeCode}
        </if>
        <if test="orgId != null and orgId != ''">
            and org_id = #{orgId}
        </if>
        <include refid="areaCondition"/>
    </sql>

    <sql id="areaCondition">
        <if test="addressType == 'livingAddress'">
            <if test="provinceCode != null and provinceCode != ''">
                and living_addr_province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode != ''">
                and living_addr_city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode != ''">
                and living_addr_func_district_code = #{districtCode}
            </if>
            <if test="streetCode != null and streetCode != ''">
                and living_addr_street_code = #{streetCode}
            </if>
            <if test="provinceCodes != null and provinceCodes.size()>0">
                and living_addr_province_code in
                <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodes != null and cityCodes.size()>0">
                and living_addr_city_code in
                <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodes != null and districtCodes.size()>0">
                and living_addr_func_district_code in
                <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="addressType == 'orgAddress'">
            <if test="provinceCode != null and provinceCode != ''">
                and org_addr_province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode != ''">
                and org_addr_city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode != ''">
                and org_addr_func_district_code = #{districtCode}
            </if>
            <if test="streetCode != null and streetCode != ''">
                and org_addr_street_code = #{streetCode}
            </if>
            <if test="provinceCodes != null and provinceCodes.size()>0">
                and org_addr_province_code in
                <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodes != null and cityCodes.size()>0">
                and org_addr_city_code in
                <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodes != null and districtCodes.size()>0">
                and org_addr_func_district_code in
                <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </if>
    </sql>

    <select id="dayEvalStat" resultType="com.iflytek.cdc.edr.vo.adsMsQualityEval.QualityEvalRespVO">
        select
        syndrome_code, syndrome_name,
        <include refid="sumEval"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereEval"/>
        group by syndrome_code, syndrome_name
        order by syndrome_code
    </select>

</mapper>
