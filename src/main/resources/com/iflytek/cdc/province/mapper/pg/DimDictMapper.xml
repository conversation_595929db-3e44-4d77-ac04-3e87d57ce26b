<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.DimDictMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.dim.DimDict">
    <!--@mbg.generated-->
    <!--@Table dim.dim_dict-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="dict_category" jdbcType="VARCHAR" property="dictCategory" />
    <result column="dict_type" jdbcType="VARCHAR" property="dictType" />
    <result column="dict_code" jdbcType="VARCHAR" property="dictCode" />
    <result column="dict_name" jdbcType="VARCHAR" property="dictName" />
    <result column="dict_value_code" jdbcType="VARCHAR" property="dictValueCode" />
    <result column="dict_value_name" jdbcType="VARCHAR" property="dictValueName" />
    <result column="dict_source" jdbcType="VARCHAR" property="dictSource" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="parent_coded_value" jdbcType="VARCHAR" property="parentCodedValue" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, dict_category, dict_type, dict_code, dict_name, dict_value_code, dict_value_name, 
    dict_source, memo, etl_create_datetime, etl_update_datetime, parent_coded_value
  </sql>

  <select id="searchBy" resultType="com.iflytek.cdc.province.model.vo.DictValueVO">
      select id, dict_value_code as code, dict_value_name as name
      from dim.dim_dict
      where dict_code = #{dictCode,jdbcType=VARCHAR}
      <if test="searchValue != null and searchValue != ''">
          and (dict_value_code like concat('%', #{searchValue, jdbcType=VARCHAR}, '%')
                or dict_value_name like concat('%', #{searchValue, jdbcType=VARCHAR}, '%'))
      </if>
  </select>

    <select id="searchRecurBy" resultType="com.iflytek.cdc.province.entity.dim.DimDict">
        WITH RECURSIVE dict_hierarchy AS (
            SELECT *
            FROM dim.dim_dict
            WHERE dict_code = #{dictCode,jdbcType=VARCHAR}
            <if test="searchValue != null and searchValue != ''">
                and dict_value_name like concat('%', #{searchValue, jdbcType=VARCHAR}, '%')
            </if>
            UNION ALL
            SELECT parent.*
            FROM dim.dim_dict parent
                     JOIN dict_hierarchy child ON parent.dict_value_code = child.parent_coded_value
        )
        SELECT *
        FROM dict_hierarchy;
    </select>

  <select id="getOneBy" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from dim.dim_dict
      <where>
          <if test="dictCode != null and dictCode != ''">
              dict_code = #{dictCode,jdbcType=VARCHAR}
          </if>
          <if test="valueCode != null and valueCode != ''">
              and dict_value_code = #{valueCode,jdbcType=VARCHAR}
          </if>
      </where>
      limit 1
    </select>
</mapper>