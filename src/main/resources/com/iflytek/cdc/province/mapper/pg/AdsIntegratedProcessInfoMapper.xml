<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsIntegratedProcessInfoMapper">

    <sql id="integratedSimpleInfoMap">
        mul_process_id as id,
        mul_process_id as processId,
        lv_addr_belong_area_code as regionId,
        lv_addr_belong_area as regionName,
        fv_living_addr_province_code as provinceCode,
        fv_living_addr_province as province,
        fv_living_addr_city_code as cityCode,
        fv_living_addr_city as city,
        fv_living_addr_func_district_code as funcDistrictCode,
        fv_living_addr_func_district as funcDistrict,
        fv_living_addr_street_code as streetCode,
        fv_living_addr_street as street,
        fv_org_id as fvOrgId,
        fv_org_name as fvOrgName,
        fi_identify_time as firstIdentifyTime,
        li_identify_time as lastIdentifyTime,
        li_disease_code as diseaseCode,
        li_disease_name as diseaseName,
        li_infect_class as diagnoseStatus,
        fv_diag_time as identifyTime,
        fv_living_addr_detail as livingAddrStd,
        fv_company as company,
        patient_sex_name as patient_sex_name,
        fv_patient_age as patientAge,
        fv_outcome_status as outComeStatus,
        fv_visit_time as visitTime,
        cast(fv_visit_time as date) as visitDay
    </sql>

    <select id="listIntegratedMedCntIndicator" resultType="com.iflytek.cdc.province.model.vo.MedCntIndicatorVO">
        select
        <include refid="integratedTimeChoose"/> AS DATE as stat_date,
        count(mul_process_id) as medCaseCnt
        from ads.ads_mul_process_info
        where 1= 1
        <include refid="medCntCommonCriteria"/>
        <if test="startDate != null and endDate != null">
            and <include refid="integratedTimeChoose"/> between #{startDate} and #{endDate}
        </if>
        group by stat_date order by stat_date
    </select>

    <sql id="integratedTimeChoose">
        <choose>
            <when test="timeType == 'identifyTime'">
                fi_identify_time
            </when>
            <otherwise>
                fv_visit_time
            </otherwise>
        </choose>
    </sql>

    <sql id="medCntCommonCriteria">
        <if test="ids != null and ids.size() > 0">
            and mul_process_id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and mul_process_id =  #{id}
        </if>
        <if test="processId != null and processId != ''">
            and mul_process_id = #{processId}
        </if>
        <if test="fvOrgId != null and fvOrgId != ''">
            and fv_org_id = #{fvOrgId}
        </if>
        <if test="fvOrgName != null and fvOrgName != ''">
            and fv_org_name = #{fvOrgName}
        </if>
        <include refid="integratedAreaChoose"/>
        <if test="diseaseName != null and diseaseName != ''">
            and li_disease_name = #{diseaseName}
        </if>
        <if test="queryKey != null and queryKey !=''">
            and (mul_process_id like concat('%', #{queryKey}, '%') or fv_org_name like concat('%', #{queryKey}, '%'))
        </if>
    </sql>

    <sql id="integratedAreaChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <include refid="integratedLivingAddressCriteria"/>
            </when>
            <otherwise>
                <include refid="integratedOrgAddressCriteria"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="integratedLivingAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and fv_living_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and fv_living_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and fv_living_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and fv_living_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and fv_living_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and fv_living_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and fv_living_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and fv_living_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="integratedOrgAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and fv_org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and fv_org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and fv_org_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and fv_org_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and fv_org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and fv_org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and fv_org_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and fv_org_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="listIntegratedAddressByIds"
            resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO">
        select
        <include refid="multichannelAddress"/>
        mul_process_id as id,
        mul_process_id as processId
        from ads.ads_mul_process_info
        where 1=1
        <include refid="medCntCommonCriteria"/>
    </select>

    <sql id="multichannelAddress">
        lv_living_addr_detail,
        lv_living_addr_detail_std,
        lv_living_addr_longitude,
        lv_living_addr_latitude,

        lv_company,
        lv_company_addr_detail_std,
        lv_company_longitude,
        lv_company_latitude,

        lv_org_name,
        lv_org_longitude,
        lv_org_latitude,
    </sql>

    <select id="listIntegratedPatientInfo" resultType="com.iflytek.cdc.province.model.vo.MsPatientInfoVO">
        select
        mul_process_id as id,
        mul_process_id as processId,
        patient_name as patientName,
        patient_sex_name as patientSexName,
        fv_patient_age as patientAge,
        fv_person_type as job
        from ads.ads_mul_process_info
        where 1=1
        <include refid="medCntCommonCriteria"/>
    </select>

    <select id="listIntegratedProcessInfo"
            resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO">
        select <include refid="integratedSimpleInfoMap"/>,
        patient_name AS patientName,
        patient_identity_no AS idCard
        from ads.ads_mul_process_info
        where 1=1
        <if test="ids != null and ids.size() > 0">
            and mul_process_id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="queryKey != null and queryKey !=''">
            and (mul_process_id like concat('%', #{queryKey}, '%') or fv_org_name like concat('%', #{queryKey}, '%'))
        </if>
        <if test="id != null and id != ''">
            and mul_process_id = #{id}
        </if>
        <if test="fvOrgId != null and fvOrgId != ''">
            and fv_org_id = #{fvOrgId}
        </if>
        <if test="fvOrgName != null and fvOrgName != ''">
            and fv_org_name = #{fvOrgName}
        </if>
        <if test="startDate != null and endDate != null">
            and fv_visit_time between #{startDate} and #{endDate}
        </if>
        <include refid="IntegratedLivingAddressCriteria"/>
        <choose>
            <when test="property != null and direction != null">
                order by ${property} ${direction}
            </when>
            <otherwise>
                order by etl_create_datetime desc
            </otherwise>
        </choose>
    </select>

    <sql id="IntegratedLivingAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and fv_living_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and fv_living_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and fv_living_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and fv_living_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and fv_living_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and fv_living_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and fv_living_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and fv_living_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="listIntegratedAreaMedCntIndicator"
            resultType="com.iflytek.cdc.province.model.vo.AreaMedCntIndicatorVO">
        select
        cast(<include refid="integratedTimeChoose"/> as date) as stat_date,
        count(mul_process_id) as medCaseCnt,
        <include refid="areaChoose"/>
        from ads.ads_mul_process_info
        where 1= 1
        <include refid="medCntCommonCriteria"/>
        <if test="startDate != null and endDate != null">
            and <include refid="integratedTimeChoose"/> between #{startDate} and #{endDate}
        </if>
        group by stat_date, area_level, area_code, area_name
        order by <include refid="integratedTimeChoose"/>
    </select>

    <sql id="areaChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <include refid="livingAreaChoose"/>
            </when>
            <when test="addressType == 'companyAddress'">
                <include refid="companyAreaChoose"/>
            </when>
            <otherwise>
                <include refid="orgAreaChoose"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="livingAreaChoose">
        <if test="areaLevel == 1">
            2 as area_level,
            fv_living_addr_city_code as area_code,
            fv_living_addr_city as area_name

        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            3 as area_level,
            fv_living_addr_func_district_code as area_code,
            fv_living_addr_func_district_code as area_name
        </if>
    </sql>

    <sql id="companyAreaChoose">
        <if test="areaLevel == 1">
            2 as area_level,
            fv_company_city_code as area_code,
            fv_company_city as area_name
        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            3 as area_level,
            fv_company_func_district_code as area_code,
            fv_company_func_district as area_name
        </if>
    </sql>

    <sql id="orgAreaChoose">
        <if test="areaLevel == 1">
            2 as area_level,
            fv_org_addr_city_code as area_code,
            fv_org_addr_city as area_name
        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            3 as area_level,
            fv_org_addr_func_district_code as area_code,
            fv_org_addr_func_district as area_name
        </if>
    </sql>

    <select id="listIntegrateAreaOutcomeCntIndicator"
            resultType="com.iflytek.cdc.province.model.vo.AreaMedCntIndicatorVO">
        select
        cast(case when lo_outcome_time is null then lo_dead_time else lo_outcome_time end as date) as stat_date,
        sum(case when lo_outcome_status = '治愈' then 1 else 0 end) as recoverCnt,
        sum(case when lo_outcome_status = '死亡' then 1 else 0 end) as deathCnt,
        sum(case when lo_outcome_time = null then 1 else 0 end) as existingCaseCnt,
        sum(case when lo_dead_reason = '因该病死亡' then 1 else 0 end) as diseaseDeathCnt,
        <include refid="integratedAreaChoose"/>
        from ads.ads_mul_process_info
        where 1=1
        <include refid="medCntCommonCriteria"/>
        <if test="startDate != null and endDate != null">
            and (lo_outcome_time between #{startDate} and #{endDate} or lo_dead_time between #{startDate} and #{endDate})
        </if>
        group by stat_date, area_level, area_code, area_name
        order by <include refid="integratedTimeChoose"/>
    </select>

    <select id="countIntegratedProcess" resultType="java.lang.Integer">
        select count(*)
        from ads.ads_mul_process_info
        where 1=1
        <include refid="medCntCommonCriteria"/>
    </select>

    <select id="listIntegratedPathogenCheckResult"
            resultType="com.iflytek.cdc.province.model.vo.PathogenCheckVO">
        select
        mul_process_id          as processId,
        patient_name            as patientName,
        patient_sex_name        as sexDesc,
        fv_patient_age          as patientAge,
        pneumonia_flag          as isPneumonia,
        case when virus_positive_flag = '1' or bacteria_positive_flag = '1' then '1' else '0' end as isPositive
        from ads.ads_mul_process_info
        where mul_process_id in
        <foreach collection="sourceKeyList" separator="," index="index" item="item" close=")" open="(">
            #{item}
        </foreach>
        <if test="isPneumonia != null and isPneumonia != ''">
            and pneumonia_flag = #{isPneumonia}
        </if>
        <if test="pathogenName != null and pathogenName != ''">
            and pathogen_name like concat('%', #{pathogenName}, '%')
        </if>
    </select>

</mapper>