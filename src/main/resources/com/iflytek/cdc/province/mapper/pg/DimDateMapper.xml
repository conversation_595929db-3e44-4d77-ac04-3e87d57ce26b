<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.DimDateMapper">

    <select id="day" resultType="com.iflytek.cdc.province.model.dto.DateDim">
        select
            date_day as business_id,
            year, month, date_day as date, half_year_desc as halfYear
        from dim.dim_date_day
        where CAST(start_time AS DATE) between #{startDate} and #{endDate}
        order by CAST(start_time AS DATE)
    </select>

    <select id="yearAndWeek" resultType="com.iflytek.cdc.province.model.dto.DateDim">
        select
            CONCAT(year, week) as business_id, year, week, week_desc as date
        from dim.dim_date_week
        where CAST(start_time AS DATE) between #{startDate} and #{endDate}
        order by CAST(start_time AS DATE)
    </select>

    <select id="yearAndMonth" resultType="com.iflytek.cdc.province.model.dto.DateDim">
        select
            CONCAT(year, month) as business_id ,year, month, month_desc as date, half_year_desc as halfYear
        from dim.dim_date_month
        where CAST(start_time AS DATE) between #{startDate} and #{endDate}
        order by CAST(start_time AS DATE)
    </select>

    <select id="yearAndMonthAndTenDays" resultType="com.iflytek.cdc.province.model.dto.DateDim">
        select
            CONCAT(year, month, ten_days) as business_id ,year, month, ten_days, half_year_desc as halfYear,
           case
           when  ten_days=1 then '上'
           when  ten_days=2 then '中'
           else  '下' end meadow,
        ten_days_desc as date
        from dim.dim_date_ten_days
        where CAST(start_time AS DATE) between #{startDate} and #{endDate}
        order by CAST(start_time AS DATE)
    </select>

    <select id="yearAndQuarter" resultType="com.iflytek.cdc.province.model.dto.DateDim">
        select
        CONCAT(year, quarter) as business_id ,year, quarter, quarter_desc as date, half_year_desc as halfYear
        from dim.dim_date_quarter
        where CAST(start_time AS DATE) between #{startDate} and #{endDate}
        order by CAST(start_time AS DATE)
    </select>

    <select id="year" resultType="com.iflytek.cdc.province.model.dto.DateDim">
        select
            distinct year as business_id ,year, year as date
        from dim.dim_date_day
        where CAST(start_time AS DATE) between #{startDate} and #{endDate}
        order by year
    </select>

</mapper>
