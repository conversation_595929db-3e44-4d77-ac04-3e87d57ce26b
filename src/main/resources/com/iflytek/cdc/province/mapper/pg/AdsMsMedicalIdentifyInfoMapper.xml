<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMsMedicalIdentifyInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsMsMedicalIdentifyInfo">
        <id column="identify_id" property="identifyId" />
        <result column="medical_id" property="medicalId" />
        <result column="event_id" property="eventId" />
        <result column="process_id" property="processId" />
        <result column="life_id" property="lifeId" />
        <result column="etl_create_datetime" property="etlCreateDatetime" />
        <result column="etl_update_datetime" property="etlUpdateDatetime" />
        <result column="patient_name" property="patientName" />
        <result column="patient_sex_name" property="patientSexName" />
        <result column="patient_identity_no" property="patientIdentityNo" />
        <result column="patient_birth_day" property="patientBirthDay" />
        <result column="patient_id" property="patientId" />
        <result column="patient_age" property="patientAge" />
        <result column="patient_phone" property="patientPhone" />
        <result column="living_addr_detail" property="livingAddrDetail" />
        <result column="living_addr_poi" property="livingAddrPoi" />
        <result column="living_addr_type" property="livingAddrType" />
        <result column="living_addr_detail_std" property="livingAddrDetailStd" />
        <result column="living_addr_province_code" property="livingAddrProvinceCode" />
        <result column="living_addr_province" property="livingAddrProvince" />
        <result column="living_addr_city_code" property="livingAddrCityCode" />
        <result column="living_addr_city" property="livingAddrCity" />
        <result column="living_addr_district_code" property="livingAddrDistrictCode" />
        <result column="living_addr_district" property="livingAddrDistrict" />
        <result column="living_addr_func_district_code" property="livingAddrFuncDistrictCode" />
        <result column="living_addr_func_district" property="livingAddrFuncDistrict" />
        <result column="living_addr_street_code" property="livingAddrStreetCode" />
        <result column="living_addr_street" property="livingAddrStreet" />
        <result column="living_addr_street_longitude" property="livingAddrStreetLongitude" />
        <result column="living_addr_street_latitude" property="livingAddrStreetLatitude" />
        <result column="living_addr_longitude" property="livingAddrLongitude" />
        <result column="living_addr_latitude" property="livingAddrLatitude" />
        <result column="person_type" property="personType" />
        <result column="job" property="job" />
        <result column="job_risk" property="jobRisk" />
        <result column="company" property="company" />
        <result column="company_address" property="companyAddress" />
        <result column="company_province_code" property="companyProvinceCode" />
        <result column="company_province" property="companyProvince" />
        <result column="company_city_code" property="companyCityCode" />
        <result column="company_city" property="companyCity" />
        <result column="company_district_code" property="companyDistrictCode" />
        <result column="company_district" property="companyDistrict" />
        <result column="company_func_district_code" property="companyFuncDistrictCode" />
        <result column="company_function_district" property="companyFunctionDistrict" />
        <result column="company_street_code" property="companyStreetCode" />
        <result column="company_street" property="companyStreet" />
        <result column="company_street_longitude" property="companyStreetLongitude" />
        <result column="company_street_latitude" property="companyStreetLatitude" />
        <result column="company_longitude" property="companyLongitude" />
        <result column="company_latitude" property="companyLatitude" />
        <result column="org_id" property="orgId" />
        <result column="org_name" property="orgName" />
        <result column="org_class" property="orgClass" />
        <result column="org_type_name" property="orgTypeName" />
        <result column="org_addr_detail" property="orgAddrDetail" />
        <result column="org_addr_detail_std" property="orgAddrDetailStd" />
        <result column="org_addr_province_code" property="orgAddrProvinceCode" />
        <result column="org_addr_province" property="orgAddrProvince" />
        <result column="org_addr_city_code" property="orgAddrCityCode" />
        <result column="org_addr_city" property="orgAddrCity" />
        <result column="org_addr_district_code" property="orgAddrDistrictCode" />
        <result column="org_addr_district" property="orgAddrDistrict" />
        <result column="org_addr_function_district_code" property="orgAddrFunctionDistrictCode" />
        <result column="org_addr_function_district" property="orgAddrFunctionDistrict" />
        <result column="org_addr_street_code" property="orgAddrStreetCode" />
        <result column="org_addr_street" property="orgAddrStreet" />
        <result column="org_longitude" property="orgLongitude" />
        <result column="org_latitude" property="orgLatitude" />
        <result column="onset_time" property="onsetTime" />
        <result column="visit_time" property="visitTime" />
        <result column="dept_code" property="deptCode" />
        <result column="dept_name" property="deptName" />
        <result column="suit" property="suit" />
        <result column="symptom" property="symptom" />
        <result column="diag_json" property="diagJson" />
        <result column="pathogen_json" property="pathogenJson" />
        <result column="drug_json" property="drugJson" />
        <result column="identify_time" property="identifyTime" />
        <result column="identify_disease_type" property="identifyDiseaseType" />
        <result column="identify_disease_code" property="identifyDiseaseCode" />
        <result column="identify_disease_name" property="identifyDiseaseName" />
        <result column="identify_infect_type" property="identifyInfectType" />
        <result column="identify_infect_transmit_type" property="identifyInfectTransmitType" />
        <result column="identify_support_type" property="identifySupportType" />
        <result column="identify_type" property="identifyType" />
        <result column="identify_org_id" property="identifyOrgId" />
        <result column="identify_org_name" property="identifyOrgName" />
        <result column="outcome_time" property="outcomeTime" />
        <result column="outcome_status" property="outcomeStatus" />
        <result column="dead_reason" property="deadReason" />
        <result column="check_result" property="checkResult" />
        <result column="check_time" property="checkTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.identify_id, t.medical_id, t.event_id, t.process_id, t.life_id, t.etl_create_datetime, t.etl_update_datetime, t.patient_name, t.patient_sex_name, t.patient_identity_no, t.patient_birth_day, t.patient_id, t.patient_age, t.patient_phone, t.living_addr_detail, t.living_addr_poi, t.living_addr_type, t.living_addr_detail_std, t.living_addr_province_code, t.living_addr_province, t.living_addr_city_code, t.living_addr_city, t.living_addr_district_code, t.living_addr_district, t.living_addr_func_district_code, t.living_addr_func_district, t.living_addr_street_code, t.living_addr_street, t.living_addr_street_longitude, t.living_addr_street_latitude, t.living_addr_longitude, t.living_addr_latitude, t.person_type, t.job, t.job_risk, t.company, t.company_address, t.company_province_code, t.company_province, t.company_city_code, t.company_city, t.company_district_code, t.company_district, t.company_func_district_code, t.company_function_district, t.company_street_code, t.company_street, t.company_street_longitude, t.company_street_latitude, t.company_longitude, t.company_latitude, t.org_id, t.org_name, t.org_class, t.org_type_name, t.org_addr_detail, t.org_addr_detail_std, t.org_addr_province_code, t.org_addr_province, t.org_addr_city_code, t.org_addr_city, t.org_addr_district_code, t.org_addr_district, t.org_addr_function_district_code, t.org_addr_function_district, t.org_addr_street_code, t.org_addr_street, t.org_longitude, t.org_latitude, t.onset_time, t.visit_time, t.dept_code, t.dept_name, t.suit, t.symptom, t.diag_json, t.pathogen_json, t.drug_json, t.identify_time, t.identify_disease_type, t.identify_disease_code, t.identify_disease_name, t.identify_infect_type, t.identify_infect_transmit_type, t.identify_support_type, t.identify_type, t.identify_org_id, t.identify_org_name, t.outcome_time, t.outcome_status, t.dead_reason, t.check_result, t.check_time
    </sql>

</mapper>
