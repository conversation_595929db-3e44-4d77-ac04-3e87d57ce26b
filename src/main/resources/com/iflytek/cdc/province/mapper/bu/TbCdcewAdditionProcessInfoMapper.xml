<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcewAdditionProcessInfoMapper">

    <insert id="insert">
        insert into tb_cdcew_addition_process_info
        (id, process_id, model_id, empi_id, permit_living_area_code, permit_company_area_code, permit_org_area_code,
        patient_name, sex_name, identity_no, birth_day, content_json, disease_type, disease_code, disease_name,
        addition_type, create_time, creator, creator_id)
        values
        (#{id}, #{processId}, #{modelId}, #{empiId}, #{permitLivingAreaCode}, #{permitCompanyAreaCode}, #{permitOrgAreaCode},
        #{patientName}, #{sexName}, #{identityNo}, #{birthDay}, #{contentJson}::jsonb, #{diseaseType}, #{diseaseCode}, #{diseaseName},
        #{additionType}, #{createTime}, #{creator}, #{creatorId})
    </insert>

</mapper>