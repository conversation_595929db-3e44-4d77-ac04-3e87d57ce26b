<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMulProcessCaseInfoMapper">

    <sql id="regionJoinCondition">
        left join dim.dim_region_nation drn
        on
        <choose>
            <when test="addressType == 'livingAddress'">
                i.fv_living_region_code = drn.region_code
            </when>
            <when test="addressType == 'companyAddress'">
                i.fv_company_region_code = drn.region_code
            </when>
            <otherwise>
                i.fv_org_region_code = drn.region_code
            </otherwise>
        </choose>
    </sql>

    <sql id="allRegionJoinCondition">
        left join dim.dim_region_nation drn
        on i.fv_living_region_code = drn.region_code
        left join dim.dim_region_nation org
        on i.fv_org_region_code = org.region_code
        left join dim.dim_region_nation co
        on i.fv_company_region_code = co.region_code
    </sql>

    <sql id="lvRegionJoinCondition">
        left join dim.dim_region_nation lvDrn
        on i.lv_living_region_code = lvDrn.region_code
        left join dim.dim_region_nation lvOrg
        on i.lv_org_region_code = lvOrg.region_code
        left join dim.dim_region_nation lvCo
        on i.lv_company_region_code = lvCo.region_code
    </sql>

    <sql id="regionChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and drn.province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and drn.city_code in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and drn.district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and drn.street_code  in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and drn.province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and drn.city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and drn.district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and drn.street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and drn.province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and drn.city_code in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and drn.district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and drn.street_code in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and drn.province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and drn.city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and drn.district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and drn.street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="orgRegionChoose">
        <if test="provinceCode != null and provinceCode != ''">
            and org.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org.district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and org.street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and org.province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and org.city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and org.district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and org.street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>



    <sql id="infectedCaseInfoOrder">
        order by
        <choose>
            <when test="property == 'fiInfOnsetTime'">
                i.fi_inf_onset_day
            </when>
            <when test="property == 'fiIdentifyTime'">
                i.fi_inf_identify_day
            </when>
            <when test="property == 'patientAge'">
                i.fi_inf_patient_age
            </when>
            <when test="property == 'mainDiag'">
                i.lv_main_diag_code
            </when>
            <when test="property == 'livingAddrStd'">
                i.fi_inf_living_addr_detail_std
            </when>
            <when test="property == 'company'">
                i.fi_inf_company
            </when>
            <when test="property == 'visitOrg'">
                i.fi_inf_org_id
            </when>
            <when test="property == 'cityCode'">
                drn.city_code
            </when>
            <when test="property == 'districtCode'">
                drn.district_code
            </when>
            <otherwise>
                i.fi_inf_visit_day
            </otherwise>
        </choose>
        <if test="direction == 'desc' or direction == 'asc'">
            ${direction}
        </if>
    </sql>

    <sql id="addressMap">
        fv_living_addr_detail,
        fv_living_addr_detail_std,
        drn.longitude  as fv_living_addr_street_longitude ,
        drn.latitude as  fv_living_addr_street_latitude ,
        fv_company,
        fv_company_addr_detail_std ,
        co.longitude  as fv_company_longitude ,
        co.latitude  as fv_company_latitude ,
        fv_org_name ,
        org.longitude  as fv_org_longitude ,
        org.latitude  as fv_org_latitude ,

        lv_living_addr_detail ,
        lv_living_addr_detail_std,
        lvDrn.longitude as  lv_living_addr_longitude ,
        lvDrn.latitude as  lv_living_addr_latitude ,

         lv_company ,
        lv_company_addr_detail_std ,
        lvCo.longitude  as lv_company_longitude ,
        lvCo.latitude as lv_company_latitude ,

        lv_org_name,
        lvOrg.longitude as lv_org_longitude ,
        lvOrg.latitude as lv_org_latitude
    </sql>


    <sql id="infectedSimpleInfoMap">
--        event_json,
        empi_id as patient_id,
        infect_process_id as  id,
        CASE
            WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN  drn.province_code
            ELSE org.province_code
            END AS provinceCode,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.province_name
            ELSE org.province_name
            END AS province,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.city_code
            ELSE org.city_code
            END AS cityCode,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.city_name
            ELSE org.city_name
            END AS city,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.district_code
            ELSE org.district_code
            END AS funcDistrictCode,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.district_name
            ELSE org.district_name
            END AS funcDistrict,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.street_code
            ELSE org.street_code
            END AS streetCode,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.street_name
            ELSE org.street_name
        END AS street,
        fi_inf_org_id as fvOrgId,
        fi_inf_org_name as fvOrgName,
        fi_inf_identify_time as firstIdentifyTime,
        dii.infected_sub_name as diseaseName,
        li_inf_infect_code as diseaseCode,
        li_inf_infect_class as diagnoseStatus,
        fi_inf_identify_time as identifyTime,
        fi_inf_living_addr_detail_std as livingAddrStd,
        fi_inf_company  as company,
        patient_sex_name as patient_sex_name ,
        fi_inf_patient_age as patientAge,
        lo_outcome_status as outComeStatus,
        lo_outcome_time as outComeTime,
        i.person_type  as job,
        fi_inf_onset_time as onsetTime,
        fi_inf_visit_time as visitTime,
        fi_inf_visit_day as visitDay,
        lv_main_diag as mainDiag,
        lv_pathogen_res_type as fi_pathogen_res_type,
        lv_pathogen_res_type,
        fi_inf_visit_time as fiVisitTime,
        fi_inf_org_name   as fiOrgName,
        lv_visit_time     as lvVisitTime,
            lv_org_name       as lv_org_name

    </sql>

    <sql id="simpleInfoMap">
        process_id as id,
        out_flag as hasExcluded,
        severe_flag as severe,
        lo_outcome_status as outComeStatus,
        lo_outcome_time as outComeTime,

        out_time as excludedTime,
        severe_time as severeTime,

        lv_living_addr_detail_std as livingAddrStd,
        fv_company as company,
        patient_sex_name as patientSexName,
        patient_age  as patientAge,
        li_disease_name as diseaseName,
        li_disease_code as diseaseCode,

        fi_identify_time as identifyTime,
        li_identify_time as lastIdentifyTime,
        fv_visit_time as fiVisitTime,
        fv_visit_time     as lvVisitTime,
        fi_identify_time as firstIdentifyTime,
        fv_onset_time as onsetTime,
        fv_visit_time  as visitTime,
        fv_visit_day as visitDay,
--        lv_symptom_attent_json as symptomAttentExtrJson,
        person_type_name as job,
--        fi_inf_infect_json as infectJson,
        li_diag_main as mainDiag,
        fv_pathogen_res_type,
        lv_pathogen_res_type,

        fv_org_name as fvOrgName,
        fv_org_id as fvOrgId,
        fv_org_name   as fiOrgName,
        lv_org_name,

        lo_outcome_status as outComeStatus,
        lo_outcome_time as outComeTime,
        lo_dead_reason as deathReason,
        lo_dead_day as deathTime,
        li_infect_class as diagnoseStatus,
        CASE
            WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN  drn.province_code
            ELSE org.province_code
            END AS provinceCode,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.province_name
            ELSE org.province_name
            END AS province,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.city_code
            ELSE org.city_code
            END AS cityCode,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.city_name
            ELSE org.city_name
            END AS city,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.district_code
            ELSE org.district_code
            END AS funcDistrictCode,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.district_name
            ELSE org.district_name
            END AS funcDistrict,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.street_code
            ELSE org.street_code
            END AS streetCode,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.street_name
            ELSE org.street_name
        END AS street
    </sql>

    <sql id="orgAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and org.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org.district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and org.street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and org.province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and org.city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and org.district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="livingAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and drn.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and drn.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and drn.district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and drn.street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and drn.province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and drn.city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and drn.district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="commonAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and drn.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and drn.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and drn.district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and drn.street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and drn.province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and drn.city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and drn.district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>


    <sql id="areaChooseCriteria">
        <choose>
            <when test="addressType == 'livingAddress'">
                <include refid="commonAddressCriteria"/>
            </when>
            <otherwise>
                <include refid="orgAddressCriteria"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="timeChoose">
        <choose>
            <when test="timeType == 'approveTime'">
                li_check_time
            </when>
            <when test="timeType == 'identifyTime'">
                fi_identify_time
            </when>
            <otherwise>
                fv_visit_time
            </otherwise>
        </choose>
    </sql>


    <sql id="dayChoose">
        <choose>
            <when test="timeType == 'approveTime'">
                li_check_day
            </when>
            <when test="timeType == 'identifyTime'">
                fi_identify_day
            </when>
            <otherwise>
                fv_visit_day
            </otherwise>
        </choose>
    </sql>

    <sql id="dynamicDayField">
        <choose>
            <when test="timeType == 'approveTime'">
                i.li_inf_check_day
            </when>
            <when test="timeType == 'onsetTime' or timeType == 'visitTime' or timeType == 'firVisitTime'">
                COALESCE( i.fi_inf_onset_day, i.fi_inf_visit_day )
            </when>
            <when test="timeType == 'deathTime'">
                i.lo_dead_day
            </when>
            <otherwise>
                i.fi_inf_identify_day
            </otherwise>
        </choose>
    </sql>

    <sql id="mulCntCommonCriteria">
        <include refid="commonCriteria" />
        <include refid="areaChooseCriteria" />
    </sql>

    <sql id="cntCommonCriteria">
        <include refid="commonCriteria" />
        <include refid="commonAddressCriteria" />
    </sql>

    <sql id="commonCriteria">
        <if test="ids != null and ids.size() > 0">
            and process_id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and process_id =  #{id}
        </if>
        <if test="processId != null and processId != ''">
            and process_id = #{processId}
        </if>
        <if test="fvOrgId != null and fvOrgId != ''">
            and fv_org_id = #{fvOrgId}
        </if>
        <if test="fvOrgName != null and fvOrgName != ''">
            and fv_org_name like CONCAT('%', #{fvOrgName}, '%')
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            and fi_disease_name = #{diseaseName}
        </if>
        <if test="queryKey != null and queryKey !=''">
            and (process_id like CONCAT('%', #{queryKey}, '%') or fv_org_name like CONCAT('%', #{queryKey}, '%'))
        </if>
    </sql>


    <sql id="areaChoose">
        <if test="areaLevel == 1">
            2 as areaLevel,
            drn.city_code  as area_code,
            drn.city_name as areaName
        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            3 as areaLevel,
            drn.district_code as area_code,
            drn.district_name  as areaName
        </if>
    </sql>

    <sql id="orderCondition">
        order by
        <choose>
            <when test="property == 'fiInfOnsetTime'">
                fv_onset_time
            </when>
            <when test="property == 'fiIdentifyTime'">
                fi_identify_time
            </when>
            <when test="property == 'patientAge'">
                patient_age
            </when>
            <when test="property == 'mainDiag'">
                li_diag_main
            </when>
            <when test="property == 'livingAddrStd'">
                fv_living_addr_detail_std
            </when>
            <when test="property == 'company'">
                fv_company
            </when>
            <when test="property == 'visitOrg'">
                fv_org_id
            </when>
            <when test="property == 'cityCode'">
                <choose>
                    <when test="addressType == 'livingAddress'">
                         drn.city_code
                    </when>
                    <otherwise>
                        org.city_code
                    </otherwise>
                </choose>
            </when>
            <when test="property == 'districtCode'">
                <choose>
                    <when test="addressType == 'livingAddress'">
                        drn.district_code
                    </when>
                    <otherwise>
                        org.district_code
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                fv_visit_time
            </otherwise>
        </choose>
        <if test="direction == 'desc' or direction == 'asc'">
            ${direction}
        </if>
    </sql>


    <sql id="processOtherAreaAndAgeCriteria">
        <!-- 地址选择 -->
        <if test="livingProvinceCode != null and livingProvinceCode != ''">
            and drn.province_code = #{livingProvinceCode}
        </if>
        <if test="livingCityCode != null and livingCityCode != ''">
            and drn.city_code = #{livingCityCode}
        </if>
        <if test="livingDistrictCode != null and livingDistrictCode != ''">
            and drn.district_code = #{livingDistrictCode}
        </if>
        <if test="companyProvinceCode != null and companyProvinceCode != ''">
            and co.province_code = #{companyProvinceCode}
        </if>
        <if test="companyCityCode != null and companyCityCode != ''">
            and co.city_code = #{companyCityCode}
        </if>
        <if test="companyDistrictCode != null and companyDistrictCode != ''">
            and co.district_code = #{companyDistrictCode}
        </if>
        <!-- 年龄筛选 -->
        <if test="ageMin != null">
            and
            case
            when i.patient_age_unit = '岁' or i.patient_age_unit is null then i.patient_age
            when i.patient_age_unit = '月' then i.patient_age / 12.0
            when i.patient_age_unit = '天' then i.patient_age / 365.0
                else -1
            end >= #{ageMin}
        </if>
        <if test="ageMax != null">
            and
            case
            when i.patient_age_unit = '岁' or i.patient_age_unit is null then i.patient_age
            when i.patient_age_unit = '月' then i.patient_age / 12.0
            when i.patient_age_unit = '天' then i.patient_age / 365.0
                else 9999
            end &lt;= #{ageMax}
        </if>
    </sql>

    <sql id="processOtherCriteria">
        <include refid="processOtherAreaAndAgeCriteria"/>
        <!-- 病原监测 -->
        <if test="firstPathogenResult != null and firstPathogenResult != ''">
            and lv_pathogen_res_type = #{firstPathogenResult}
        </if>
        <if test="lastPathogenResult != null and lastPathogenResult != ''">
            and lv_pathogen_res_type = #{lastPathogenResult}
        </if>
    </sql>

    <sql id="processCriteria">
        <if test="fvOrgId != null and fvOrgId != ''">
            and fv_org_id = #{fvOrgId}
        </if>
        <if test="fvOrgName != null and fvOrgName != ''">
            and fv_org_name like CONCAT('%', #{fvOrgName}, '%')
        </if>
        <if test="queryKey != null and queryKey !=''">
            and (process_id like CONCAT('%', #{queryKey}, '%') or fv_org_name like CONCAT('%', #{queryKey}, '%'))
        </if>
        <if test="job != null and job != ''">
            and i.person_type_name = #{job}
        </if>
        <if test="company != null and company != ''">
            and fv_company like CONCAT('%', #{company}, '%')
        </if>
        <if test="patientSexName != null and patientSexName != ''">
            and patient_sex_name = #{patientSexName}
        </if>
        <if test="visitStartDate != null">
            and fv_visit_day >= #{visitStartDate}
        </if>
        <if test="visitEndDate != null">
            and fv_visit_day &lt;= #{visitEndDate}
        </if>
        <if test="mainDiag != null and mainDiag != ''">
            and li_diag_main like CONCAT('%', #{mainDiag}, '%')
        </if>
        <if test="diagnoseStatus != null and diagnoseStatus != ''">
            and li_infect_class = #{diagnoseStatus}
        </if>
        <if test="onsetStartDate != null">
            and fv_onset_time >= #{onsetStartDate}
        </if>
        <if test="onsetEndDate != null">
            and fv_onset_time &lt;= #{onsetEndDate}
        </if>
        <if test="hasExcluded != null and hasExcluded != ''">
            and i.out_flag = #{hasExcluded}
        </if>
        <if test="isSevere != null and isSevere != ''">
            and i.severe_flag = #{isSevere}
        </if>
        <if test="outComeStatus != null and outComeStatus != ''">
            and i.lo_outcome_status = #{outComeStatus}
        </if>
    </sql>


    <sql id="cntCommonQuery">
        sum(case when lo_outcome_status = '治愈' then 1 else 0 end) as recoverCnt ,
        sum(case when lo_outcome_status = '死亡' then 1 else 0 end) as deathCnt,
        sum(case when end_time is null then 1 else 0 end) as existingCaseCnt ,
        sum(case when lo_dead_reason = '因该病死亡' then 1 else 0 end) as diseaseDeathCnt
    </sql>

    <select id="listMedCntIndicator" resultType="com.iflytek.cdc.province.model.vo.MedCntIndicatorVO">
        select
            <include refid="dayChoose"/> as stat_date,
            count(*) as medCaseCnt,
            <include refid="cntCommonQuery"/>
        from ads.ads_mul_process_case_info i
        <include refid="allRegionJoinCondition"/>
        where 1=1
        <if test="startDate != null and endDate != null">
            and <include refid="timeChoose"/> between #{startDate} and #{endDate}
        </if>
        <include refid="mulCntCommonCriteria"/>
        <if test="processType != 'integrated' and processType != 'multichannel'">
            <include refid="processCriteria"/>
            <include refid="orgAddressCriteria"/>
            <include refid="processOtherCriteria"/>
        </if>
        group by stat_date order by stat_date
    </select>

<!--    integrated返回的空集合-->
    <select id="listOutcomeIndicator" resultType="com.iflytek.cdc.province.model.vo.MedCntIndicatorVO">
        select
        lo_outcome_day as stat_date,
        <include refid="cntCommonQuery"/>
        from ads.ads_mul_process_case_info i
        <include refid="allRegionJoinCondition"/>
        where 1=1
        <include refid="mulCntCommonCriteria"/>
        <if test="processType != 'integrated' and processType != 'multichannel'">
            <include refid="processCriteria"/>
            <include refid="orgAddressCriteria"/>
            <include refid="processOtherCriteria"/>
        </if>
        <if test="startDate != null and endDate != null">
            and lo_outcome_time between #{startDate} and #{endDate}
        </if>
        and lo_outcome_time is not null
        group by stat_date order by stat_date
    </select>

    <select id="listAreaMedCntIndicator" resultType="com.iflytek.cdc.province.model.vo.AreaMedCntIndicatorVO">
        select
        <include refid="dayChoose" />  as stat_date,
        count(*) as medCaseCnt,
        <include refid="areaChoose" />
        from ads.ads_ms_emerging_process_info i
        <include refid="regionJoinCondition"/>
        where 1= 1
        <include refid="cntCommonCriteria"/>
        <if test="startDate != null and endDate != null">
            and <include refid="timeChoose" /> between #{startDate} and #{endDate}
        </if>
        group by stat_date, areaLevel, area_code, areaName order by stat_date
    </select>

    <select id="listAreaOutcomeIndicator" resultType="com.iflytek.cdc.province.model.vo.AreaMedCntIndicatorVO">
        select
        lo_outcome_day as stat_date,
        <include refid="cntCommonQuery"/>,
        <include refid="areaChoose"/>
        from ads.ads_ms_infect_process_case_info i
        <include refid="regionJoinCondition"/>
        where 1=1
        and lo_outcome_time is not null
        <include refid="cntCommonCriteria"/>
        <if test="startDate != null and endDate != null">
            and lo_outcome_time between #{startDate} and #{endDate}
        </if>
        group by stat_date, areaLevel, area_code, areaName order by stat_date
    </select>

    <select id="getMedicalList" resultType="com.iflytek.cdc.edr.vo.dm.ProcessRecordVO">
        select
        process_id as id,
        patient_age as patient_age,
        patient_name,
        patient_sex_name,
        li_disease_name,
        dept_name  as fiSyndeptName,
        dept_name  as lvDeptName,
        fi_disease_name as syndromeName,
        fv_visit_time as fvVisitTime,
        fv_visit_time as lv_visit_time,
        case when severe_flag = '1' then '重症'
        when severe_flag = '0' then '非重症' end as severityLevel,
        fv_org_name as fvOrgName,
        fi_syn_org_addr_func_district as lvOrgAddrFuncDistrict,
        fi_syn_identify_time as fi_syn_syndrome_identify_time,
        lv_medical_id,
        fi_syn_org_addr_province_code as provinceCode,
        fi_syn_org_addr_province as provinceName,
        fi_syn_org_addr_city_code as cityCode,
        fi_syn_org_addr_city as cityName,
        fi_syn_org_addr_func_district_code as districtCode,
        fi_syn_org_addr_func_district as districtName,
        <!-- 审核存储信息-->
        '1' as diseaseType,
        li_syn_syndrome_code as li_disease_code,
        li_syn_syndrome_name as li_disease_name,
        fi_syn_identify_time as fiIdentifyTime
        from ads.ads_ms_syndrome_process_info
        where 1=1
        <if test="diseaseCodeList != null and diseaseCodeList.size() &gt; 0">
            and fi_syn_syndrome_code in
            <foreach collection="diseaseCodeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and process_id = #{id}
        </if>
<!--        <if test="symptomName != null and symptomName != ''">-->
<!--            and symptom_attent_extr_json LIKE CONCAT('%', #{symptomName}, '%')-->
<!--        </if>-->
        <if test="confirmDisease != null and confirmDisease != ''">
            and confirm_disease like CONCAT('%',  #{confirmDisease}, '%')
        </if>
        <if test="personInfo != null and personInfo != ''">
            and (patient_name like CONCAT('%', #{personInfo}, '%') or
            process_id like CONCAT('%', #{personInfo}, '%'))
        </if>
        <if test="severityLevel == '重症'">
            and severe_flag = '1'
        </if>
        <if test="severityLevel == '非重症'">
            and  severe_flag = '0'
        </if>
        <if test="severityLevel == '死亡'">
            and lo_outcome_status like CONCAT('%', #{severityLevel}, '%')
        </if>
        <if test="visitTimeType == 'firstVisit'">
            <if test="startDate != null">
                and fv_visit_time >= #{startDate}
            </if>
            <if test="endDate != null">
                and fv_visit_time &lt;= #{endDate}
            </if>
        </if>
        <if test="visitTimeType == 'latestVisit'">
            <if test="startDate != null">
                and fv_visit_time >= #{startDate}
            </if>
            <if test="endDate != null">
                and fv_visit_time &lt;= #{endDate}
            </if>
        </if>
        <if test="visitTimeType == 'monitorReport'">
            <if test="startDate != null">
                and fi_identify_time >= #{startDate}
            </if>
            <if test="endDate != null">
                and fi_identify_time &lt;= #{endDate}
            </if>
        </if>
        <if test="fvOrgId != null and fvOrgId != ''">
            and fv_org_id = #{fvOrgId}
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and fi_syn_org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and fi_syn_org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and fi_syn_org_addr_func_district_code = #{districtCode}
        </if>
        <if test="fiSyndeptName != null and fiSyndeptName != ''">
            and dept_name = #{fiSyndeptName}
        </if>
        <if test="keyword != null and keyword != ''">
            and (process_id like  CONCAT('%', #{keyword}, '%') or li_syn_syndrome_name like  CONCAT('%', #{keyword}, '%') )
        </if>
        <if test="queryKey != null and queryKey != ''">
            and ( process_id like  CONCAT('%', #{queryKey}, '%')
            or li_disease_name like  CONCAT('%', #{queryKey}, '%')
            or patient_name like CONCAT('%', #{queryKey}, '%'))
        </if>
        <include refid="orderCondition"/>
        <if test="limitNum != null and limitNum >0">
            limit #{limitNum}
        </if>
    </select>

    <select id="listAddressByIds" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO">
        select
        process_id as id,
        process_id as processId,
        <include refid="addressMap"/>
        from ads.ads_mul_process_case_info i
        <include refid="allRegionJoinCondition"/>
        <include refid="lvRegionJoinCondition"/>
        where process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
        <!-- 病例过滤 -->
         <if test="processType != 'integrated' and processType != 'multichannel'">
            <include refid="orgAddressCriteria"/>
            <include refid="processCriteria"/>
            <include refid="processOtherCriteria"/>
        </if>
    </select>

    <select id="listSimpleInfo" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO">
        select
        <include refid="simpleInfoMap"/>,
        patient_name AS patientName,
        patient_identity_no AS idCard
        from ads.ads_mul_process_case_info i
        <include refid="allRegionJoinCondition"/>
        where 1=1
        <if test="ids != null and ids.size() > 0">
            and process_id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and process_id = #{id}
        </if>
<!--        <if test="symptomName != null and symptomName != ''">-->
<!--            and symptom_attent_extr_json AS CHAR like CONCAT('%',  #{symptomName}, '%')-->
<!--        </if>-->
        <if test="startDate != null and endDate != null">
            and <include refid="timeChoose"/> between #{startDate} and #{endDate}
        </if>
         <if test="processType != 'integrated' and processType != 'multichannel'">
            <include refid="orgAddressCriteria"/>
            <include refid="processOtherCriteria"/>
        </if>
        <if test="processType == 'integrated' || processType == 'multichannel'">
            <include refid="livingAddressCriteria"/>
        </if>
        <include refid="processCriteria"/>
    </select>

    <select id="loadSimpleInfo" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoDetailVO">
        select
        <include refid="simpleInfoMap"/>,
        patient_name AS patientName,
        patient_identity_no AS idCard,
        patient_birth_day AS birthDate,
        patient_sex_name AS genderName,
        fv_living_addr_detail  AS livingAddrDetail,
        drn.province_code AS livingAddrProvinceCode,
        drn.province_name AS livingAddrProvinceName,

        drn.city_code AS livingAddrCityCode,
        drn.city_name AS livingAddrCityName,

        drn.district_code AS livingAddrDistrictCode,
        drn.district_name AS livingAddrDistrictName,

        drn.street_code AS livingAddrStreetCode,
        drn.street_name AS livingAddrStreetName,
        drn.longitude AS livingAddrLongitude,
        drn.latitude AS livingAddrLatitude
        from ads.ads_mul_process_case_info i
        join dim.dim_region_nation drn
        on i.lv_living_region_code = drn.region_code
        where process_id = #{id}
    </select>

    <select id="countByProcessId" resultType="java.lang.Integer">
        select
        count(*)
        from  ads.ads_mul_process_case_info
        where process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="listPatientInfoByIds" resultType="com.iflytek.cdc.province.model.vo.MsPatientInfoVO">
        select
        process_id as id,
        process_id as processId,
        patient_name as patientName,
        patient_sex_name as patientSexName,
        patient_age as patientAge,
        empi_id as patientId,
        --            fi_inf_event_id as eventId,
        --            event_json,
        person_type_name as job,
        severe_flag as li_severe_flag,
        lo_outcome_status,
        event_id,
        syndrome_subgroup as li_subgroup
        from ads.ads_mul_process_case_info i
        <include refid="allRegionJoinCondition"/>
        where process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
        <!-- 病例过滤 -->
         <if test="processType != 'integrated' and processType != 'multichannel'">
            <include refid="orgAddressCriteria"/>
            <include refid="processCriteria"/>
            <include refid="processOtherCriteria"/>
        </if>
    </select>

    <select id="listSyndromeProcessDetailInfo" resultType="com.iflytek.cdc.province.model.vo.MsProcessMonitorInfoVO">
        select
        i.process_id         as id,
        i.fv_visit_day                as visitDay,
        p.focus_place_type            as focusPlace
--        i.symptom_attent_extr_json    as monitorSymptomJson
        from
        ads.ads_mul_process_case_info i
        left join ads.ads_ms_syndrome_process_focus_place_info p
        on i.process_id = p.syndrome_process_id
        <include refid="allRegionJoinCondition"/>
        where i.process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
        <!-- 病例过滤 -->
        <include refid="orgAddressCriteria"/>
        <include refid="processCriteria"/>
        <include refid="processOtherCriteria"/>
    </select>

    <select id="getProcessInfoByPerson"
            resultType="com.iflytek.cdc.province.model.pandemic.vo.PersonProcessInfo">
        select
        process_id as processId,
        fv_onset_time as onsetTime,
        patient_name as patientName,
        etl_update_datetime as updateTime,
        empi_id as empiId
        from
        ads.ads_mul_process_case_info i
        where 1=1 and
        <if test="diseaseCode != null and diseaseCode != ''">
            fi_disease_code = #{diseaseCode}
            and
        </if>
        (patient_identity_no = #{identityNo} or (patient_name = #{patientName} and patient_sex_name = #{sexName} and patient_birth_day = #{birthDay}))
    </select>

    <select id="listModelSimpleInfo"
            resultType="com.iflytek.cdc.province.model.vo.InfectedProcessModelSimpleInfo">
        select
        process_id as process_id,
        patient_sex_name  as patientSexName,
        lvDrn.province_code as livingAddressProvinceCode,
        lvDrn.province_name as livingAddressProvinceName,
        lvDrn.city_code as livingAddressCityCode,
        lvDrn.city_name as livingAddressCityName,
        lvDrn.district_code as livingAddressDistrictCode,
        lvDrn.district_name as livingAddressDistrictName,
        lvDrn.street_code as livingAddressStreetCode,
        lvDrn.street_name as livingAddressStreetName,
        person_type_name as personType,
--        fi_syn_school_type  as schoolType,
        lvCo.province_code  as schoolAddressProvinceCode,
        lvCo.province_name  as schoolAddressProvinceName,
        lvCo.city_code  as schoolAddressCityCode,
        lvCo.city_name  as schoolAddressCityName,
        lvCo.district_code  as schoolAddressDistrictCode,
        lvCo.district_name  as schoolAddressDistrictName,
        lvCo.street_code  as schoolAddressStreetCode,
        lvCo.street_name  as schoolAddressStreetName,
        lv_company_addr_detail_std as schoolAddress,
        fv_org_name as firstDiagnosisOrg,
        fv_pathogen_res_type  as firstPathogenTestResult,
        lv_pathogen_res_type  as lastPathogenTestResult,
        lo_outcome_status as outCome,
        lo_dead_reason as deadReason,
        li_disease_name  syndrome,
        confirm_disease as confirmedDisease,
        out_flag as isExcluded,
        severe_flag as isSevere,
        infect_type  as caseType,
        severe_reason as severeReason
        from ads.ads_mul_process_case_info
        <include refid="lvRegionJoinCondition"/>
        where process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="listInfectedProcessLog" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO">
        select
        occur_time as date,
        label_type as type,
        label_value as value,
        case when source_desc = '传染病识别' then 'infected' else '' end as specialMark
        from ads.ads_ms_infect_process_view
        where delete_flag = '0' and infect_process_id = #{id}
        <if test="type !=null">
            and label_type = #{type}
        </if>
        order by occur_time
    </select>

    <select id="listSyndromeProcessLog" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO">
        select
        occur_time as date,
        label_type as type,
        label_value as value,
        case when source_desc = '传染病识别' then 'infected' else '' end as specialMark
        from ads.ads_ms_syndrome_process_view
        where delete_flag = '0' and syndrome_process_id = #{id}
        <if test="type !=null">
            and label_type = #{type}
        </if>
        order by occur_time
    </select>

    <select id="getProcessPathogenInfoBy" resultType="com.iflytek.cdc.province.model.vo.ProcessPathogenInfoVO">
        select
        sum(case when pathogen_flag = '1' then 1 else 0 end) as totalTests,
        sum(case when pathogen_flag = '1' and pathogen_name is not null then 1 else 0 end) as positiveCnt,
        sum(case when pathogen_flag = '1' and pathogen_name is null then 1 else 0 end) as negativeCnt
        from ads.ads_mul_process_case_info
        where process_id in
        <foreach collection="ids" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="syndromeSimpleInfoPageListByConditions"
            resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO">
        select
        <include refid="simpleInfoMap"/>,
        patient_name AS patientName,
        patient_identity_no AS idCard
        from ads.ads_mul_process_case_info i
        <include refid="allRegionJoinCondition"/>
        where 1=1
        <include refid="orgRegionChoose"/>
        <if test="ids != null and ids.size() > 0">
            and process_id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
<!--        <if test="symptomName != null and symptomName.size() > 0">-->
<!--            <foreach collection="symptomName" item="item" open="AND (" close=")" separator="OR">-->
<!--                symptom_attent_extr_json LIKE CONCAT('%', #{item}, '%')-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="fvOrgId != null and fvOrgId.size() > 0">
            and i.fv_org_id in
            <foreach collection="fvOrgId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="fvOrgName != null and fvOrgName.size() > 0">
            <foreach collection="fvOrgName" item="item" open="AND (" close=")" separator="OR">
                i.fv_org_name like CONCAT('%', #{fvOrgName}, '%')
            </foreach>
        </if>
        <if test="job != null and job.size() > 0">
            and i.person_type_name in
            <foreach collection="job" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="company != null and company.size() > 0">
            and i.fv_company in
            <foreach collection="company" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="patientSexName != null">
            and i.patient_sex_name =  #{patientSexName}
        </if>
        <if test="visitStartDate != null">
            and i.fv_visit_day >= #{visitStartDate}
        </if>
        <if test="visitEndDate != null">
            and i.fv_visit_day &lt;= #{visitEndDate}
        </if>
        <if test="onsetStartDate != null">
            and i.fv_onset_time >= #{onsetStartDate}
        </if>
        <if test="onsetEndDate != null">
            and i.fv_onset_time &lt;= #{onsetEndDate}
        </if>
        <if test="mainDiag != null and mainDiag.size() > 0">
            and i.li_diag_main in
            <foreach collection="mainDiag" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="hasExcluded != null and hasExcluded.size() > 0">
            and i.out_flag in
            <foreach collection="hasExcluded" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="isSevere != null and isSevere.size() > 0">
            and i.severe_flag in
            <foreach collection="isSevere" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="outComeStatus != null and outComeStatus.size() > 0">
            and i.lo_outcome_status in
            <foreach collection="outComeStatus" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queryKey != null and queryKey.size() > 0">
            <foreach collection="queryKey" item="item" open="AND (" close=")" separator="OR">
                (i.process_id like CONCAT('%', #{item}, '%') or i.fv_org_name like CONCAT('%', #{item}, '%'))
            </foreach>
        </if>
        <include refid="processOtherAreaAndAgeCriteria"/>
        <!-- 病原监测 -->
        <if test="firstPathogenResult != null and firstPathogenResult.size() > 0">
            and i.fv_pathogen_res_type in
            <foreach collection="firstPathogenResult" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="lastPathogenResult != null and lastPathogenResult.size() > 0">
            and i.lv_pathogen_res_type  in
            <foreach collection="lastPathogenResult" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <include refid="orderCondition"/>
    </select>

    <select id="listSyndromeOrgProcessStats"
            resultType="com.iflytek.cdc.province.model.vo.SyndromeOrgProcessStatsVO">
        SELECT
        fv_org_name AS orgName,
        fi_disease_name AS diseaseName,
        COUNT(process_id) AS processCnt,
        ROUND(
        COUNT(process_id) * 100.0 / SUM(COUNT(process_id)) OVER (),
        2
        ) AS radio
        FROM ads.ads_mul_process_case_info i
        <include refid="regionJoinCondition"/>
        where 1=1
        <include refid="regionChoose"/>
        <if test="startDate != null and endDate != null">
            and <include refid="timeChoose"/> between #{startDate} and #{endDate}
        </if>
        GROUP BY fv_org_name, fi_disease_name
    </select>

    <select id="getMedicalDept" resultType="java.lang.String">
        select distinct dept_name from  ads.ads_mul_process_case_info where dept_name is not null
    </select>

    <select id="listPathogenCheckResult" resultType="com.iflytek.cdc.province.model.vo.PathogenCheckVO">
        select
        process_id       as processId,
        patient_name            as patientName,
        patient_sex_name        as sexDesc,
        patient_age      as patientAge,
        pneumonia_flag          as isPneumonia,
        case when virus_positive_flag = '1' or bacteria_positive_flag = '1' then '1' else '0' end as isPositive
        from ads.ads_mul_process_case_info i
        where process_id in
        <foreach collection="sourceKeyList" separator="," index="index" item="item" close=")" open="(">
            #{item}
        </foreach>
        <if test="isPneumonia != null and isPneumonia != ''">
            and pneumonia_flag = #{isPneumonia}
        </if>
        <if test="pathogenName != null and pathogenName != ''">
            and pathogen_name like CONCAT('%', #{pathogenName}, '%')
        </if>
    </select>

    <select id="getProcessInfoBy"
            resultType="com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo">
        select
        process_id as id,
        patient_name,
        patient_sex_name,
        patient_birth_day as birth_day,
        patient_age,
        patient_age_unit,
        person_type_name as job,
        person_type_name as person_type,

        fv_living_addr_detail AS living_addr_detail,
        drn.province_code AS living_province_code,
        drn.province_name AS living_province_name,
        drn.city_code AS living_city_code,
        drn.city_name AS living_city_name,
        drn.province_code AS living_district_code,
        drn.district_name AS living_district_name,
        drn.street_code AS living_street_code,
        drn.street_name AS living_street_name,
        drn.longitude AS living_addr_longitude,
        drn.latitude AS living_addr_latitude,

        fv_company AS company,
        co.province_code AS company_province_code,
        co.province_name AS company_province_name,
        co.city_code AS company_city_code,
        co.city_name AS company_city_name,
        co.district_code AS company_district_code,
        co.district_name AS company_district_name,
        co.street_code AS company_street_code,
        co.street_name AS company_street_name,
        co.longitude AS company_addr_longitude,
        co.latitude AS company_addr_latitude,

        fv_org_id AS org_id,
        fv_org_name AS org_name,
        org.province_code AS org_province_code,
        org.province_name AS org_province_name,
        org.city_code AS org_city_code,
        org.city_name AS org_city_name,
        org.district_code AS org_district_code,
        org.district_name AS org_district_name,
        org.street_code AS org_street_code,
        org.street_name AS org_street_name,
        org.longitude AS org_addr_longitude,
        org.latitude AS org_addr_latitude,

        fv_onset_time as onset_time,
        fv_visit_time as first_visit_time,
        fv_org_id as first_visit_org_id,
        fv_org_name as first_visit_org_name,
        fi_identify_time as diagnose_time,

        fi_disease_code as disease_code,
        fi_disease_name as disease_name,
        lo_outcome_status as outcome_status,
        lo_outcome_time as outcome_time,
        lo_dead_day as death_time
        from ads.ads_mul_process_case_info i
        <include refid="allRegionJoinCondition"/>
        where process_id in
        <foreach collection="ids" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>


</mapper>