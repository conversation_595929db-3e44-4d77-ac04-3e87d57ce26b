<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsDiIndataStatMapper">

    <sql id="areaChoose">
        <if test="provinceCode != null and provinceCode != ''">
            and org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and org_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size() > 0">
            and org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() > 0">
            and org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() > 0">
            and org_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size() > 0">
            and org_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="statSql">
        COALESCE(sum(medical_cnt), 0)               as medicalCnt,
        COALESCE(sum(medical_cnt_last_y), 0)        as medicalCntLastY,
        COALESCE(sum(medical_outpat_cnt), 0)        as medicalOutpatCnt,
        COALESCE(sum(medical_outpat_cnt_last_y), 0) as medicalOutpatCntLastY,
        COALESCE(sum(medical_inpat_cnt), 0)         as medicalInpatCnt,
        COALESCE(sum(medical_inpat_cnt_last_y), 0)  as medicalInpatCntLastY,
        COALESCE(sum(global_patient_cnt), 0)        as globalPatientCnt,
        COALESCE(sum(global_patient_cnt_last_y), 0) as globalPatientCntLastY
    </sql>

    <select id="getLastCount" resultType="com.iflytek.cdc.edr.vo.DiIndataStatVO">
        SELECT
        COALESCE(COUNT(DISTINCT CASE WHEN visit_type_code IS NOT NULL THEN medical_id END), 0) AS medicalCntTotal,
        COALESCE(COUNT(DISTINCT CASE WHEN visit_type_code = '2' THEN medical_id END), 0) AS medicalOutpatCntTotal,
        COALESCE(COUNT(DISTINCT CASE WHEN visit_type_code = '4' THEN medical_id END), 0) AS medicalInpatCntTotal
        FROM
        ads.ads_ms_medical_info m
        WHERE m.rep_card_flag = '0' and m.org_class = #{queryParam.orgType}
        <if test="startDate != null and endDate != null">
            and visit_time between #{startDate} and #{endDate}
        </if>
    </select>

    <select id="dayStat" resultType="com.iflytek.cdc.edr.vo.DiIndataStatVO">
        select
            CAST(day AS DATE)  as statDate,
            day as startDate,
            <include refid="statSql" />
        from
        ads.ads_di_indata_stat_d
        where org_type = #{orgType}
        <if test="startDate != null and endDate != null">
            and day between #{startDate} and #{endDate}
        </if>
        <include refid="areaChoose" />
        group by CAST(day AS DATE) , day
        order by day asc
    </select>

    <select id="weekStat" resultType="com.iflytek.cdc.edr.vo.DiIndataStatVO">
        select
            year,
            week,
            CONCAT(year, '年第', week, '周') as statDate,
            monday as startDate,
            <include refid="statSql" />
        from
        ads.ads_di_indata_stat_w
        where org_type = #{orgType}
        <if test="startDate != null and endDate != null">
            and monday between #{startDate} and #{endDate}
        </if>
        <include refid="areaChoose" />
        group by year, week, monday
        order by year asc ,week asc
    </select>

    <select id="meadowStat" resultType="com.iflytek.cdc.edr.vo.DiIndataStatVO">
        select
            year , month , ten_day,
            CONCAT(year, '年', month, '月', ten_day, '旬') as statDate,
            <include refid="statSql" />
        from
        ads.ads_di_indata_stat_td
        where org_type = #{orgType}
        <if test="dateDims != null and dateDims.size() >0">
            and
            <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
                (year = #{item.year} and month = #{item.month} and ten_day = #{item.meadow})
            </foreach>
        </if>
        <include refid="areaChoose" />
        group by year , month , ten_day
        order by year asc, month asc, ten_day asc
    </select>

    <select id="monthStat" resultType="com.iflytek.cdc.edr.vo.DiIndataStatVO">
        select
            year, month,
            CONCAT(year, '年', month, '月') as statDate,
            <include refid="statSql" />
        from
        ads.ads_di_indata_stat_m
        where org_type = #{orgType}
        <if test="dateDims != null and dateDims.size() >0">
            and
            <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
                (year = #{item.year} and month = #{item.month})
            </foreach>
        </if>
        <include refid="areaChoose" />
        group by  year, month
        order by year asc, month asc
    </select>

    <select id="quarterStat" resultType="com.iflytek.cdc.edr.vo.DiIndataStatVO">
        select
            year,quarter,
            CONCAT(year, '年第', quarter, '季度') as statDate,
            <include refid="statSql" />
        from
            ads.ads_di_indata_stat_q
        where org_type = #{orgType}
        <if test="dateDims != null and dateDims.size() >0">
            and
            <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
                (year = #{item.year} and quarter = #{item.quarter})
            </foreach>
        </if>
        <include refid="areaChoose" />
        group by  year,quarter
        order by year asc, quarter asc
    </select>

    <select id="yearStat" resultType="com.iflytek.cdc.edr.vo.DiIndataStatVO">
        select
        year,
        CONCAT(year, '年') as statDate,
        <include refid="statSql" />
        from
        ads.ads_di_indata_stat_y
        where org_type = #{orgType}
        <if test="dateDims != null and dateDims.size() >0">
            and year in
            <foreach collection="dateDims" open="(" close=")" separator="," item="item">
                #{item.year}
            </foreach>
        </if>
        <include refid="areaChoose" />
        group by year
        order by year asc
    </select>

    <select id="getLastStatDate" resultType="java.util.Date">
        select CAST(m.visit_time AS DATE)
        from ads.ads_ms_medical_info m
        where m.rep_card_flag = '0' and m.org_class = #{orgType}
        <include refid="areaChoose"/>
        order by m.visit_time desc
        limit 1
    </select>

    <select id="dataStat" resultType="com.iflytek.cdc.edr.vo.DiIndataStatVO">
        select
        cast(visit_time as date)                                                                as statDate,
        COALESCE(count(distinct case when visit_type_code is not null then medical_id end), 0)  as medicalCnt,
        COALESCE(count(distinct case when visit_type_code = '2' then medical_id end), 0)        as medicalOutpatCnt,
        COALESCE(count(distinct case when visit_type_code = '4' then medical_id end) , 0)       as medicalInpatCnt
        from ads.ads_ms_medical_info m
        where m.rep_card_flag = '0' and org_class = #{orgType}
        <if test="startDate != null and endDate != null">
            and visit_time between #{startDate} and #{endDate}
        </if>
        <include refid="areaChoose" />
        group by statDate
    </select>


</mapper>