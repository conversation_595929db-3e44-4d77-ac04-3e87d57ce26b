<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsDiInorgInfoMapper">
    <resultMap type="com.iflytek.cdc.province.entity.ads.AdsDiInorgInfo" id="AdsDiInorgInfoMap">
        <result property="etlCreateDatetime" column="etl_create_datetime" jdbcType="TIMESTAMP"/>
        <result property="etlUpdateDatetime" column="etl_update_datetime" jdbcType="TIMESTAMP"/>
        <result property="orgAddrProvinceCode" column="org_addr_province_code" jdbcType="VARCHAR"/>
        <result property="orgAddrProvince" column="org_addr_province" jdbcType="VARCHAR"/>
        <result property="orgAddrCityCode" column="org_addr_city_code" jdbcType="VARCHAR"/>
        <result property="orgAddrCity" column="org_addr_city" jdbcType="VARCHAR"/>
        <result property="orgAddrDistrictCode" column="org_addr_district_code" jdbcType="VARCHAR"/>
        <result property="orgAddrDistrict" column="org_addr_district" jdbcType="VARCHAR"/>
        <result property="orgAddrFuncDistrictCode" column="org_addr_func_district_code" jdbcType="VARCHAR"/>
        <result property="orgAddrFuncDistrict" column="org_addr_func_district" jdbcType="VARCHAR"/>
        <result property="orgAddrStreetCode" column="org_addr_street_code" jdbcType="VARCHAR"/>
        <result property="orgAddrStreet" column="org_addr_street" jdbcType="VARCHAR"/>
        <result property="orgId" column="org_id" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="orgType" column="org_type" jdbcType="VARCHAR"/>
        <result property="rootOrgId" column="root_org_id" jdbcType="VARCHAR"/>
        <result property="rootOrgName" column="root_org_name" jdbcType="VARCHAR"/>
        <result property="manaOrgId" column="mana_org_id" jdbcType="VARCHAR"/>
        <result property="manaOrgName" column="mana_org_name" jdbcType="VARCHAR"/>
        <result property="inStatus" column="in_status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="areaChoose">
        <if test="provinceCode != null and provinceCode != ''">
            and org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and org_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size() > 0">
            and org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() > 0">
            and org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() > 0">
            and org_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size() > 0">
            and org_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <!-- 通过ID查询单条数据 -->
    <select id="queryById" resultMap="AdsDiInorgInfoMap">
        select
        etl_create_datetime, etl_update_datetime, org_addr_province_code, org_addr_province,
        org_addr_city_code, org_addr_city, org_addr_district_code, org_addr_district, org_addr_func_district_code,
        org_addr_func_district, org_addr_street_code, org_addr_street, org_id, org_name, org_type, root_org_id,
        root_org_name, mana_org_id, mana_org_name, in_status
        from ads_di_inorg_info
        where etl_create_datetime = #{etlCreateDatetime}
    </select>

    <select id="getLastStatDate" resultType="date">
        select max(day) from ads.ads_di_inorg_stat
        where 1=1
        <if test="provinceCode != null and provinceCode != ''">
            and org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org_addr_district_code = #{districtCode}
        </if>
    </select>

    <select id="queryBy" resultMap="AdsDiInorgInfoMap">
        select
        etl_create_datetime,etl_update_datetime,org_addr_province_code,org_addr_province,org_addr_city_code,org_addr_city,
        org_addr_func_district_code,org_addr_func_district,org_addr_func_district_code,org_addr_func_district,
        org_addr_street_code,org_addr_street,org_id,org_name,org_type,root_org_id,root_org_name,mana_org_id,mana_org_name,in_status
        from ads.ads_di_inorg_info
        <where>
            <if test="provinceCode != null and provinceCode != ''">
                and org_addr_province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode != ''">
                and org_addr_city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode != ''">
                and org_addr_func_district_code = #{districtCode}
            </if>
            <if test="orgName != null and orgName != ''">
                and org_name = #{orgName}
            </if>
            <if test="orgType != null and orgType != ''">
                and org_type = #{orgType}
            </if>
            <if test="manaOrgId != null and manaOrgId != ''">
                and mana_org_id = #{manaOrgId}
            </if>
            <if test="inStatus != null and inStatus != ''">
                and in_status = #{inStatus}
            </if>
            <if test="provinceCodes != null and provinceCodes.size()>0">
                and org_addr_province_code in
                <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodes != null and cityCodes.size()>0">
                and org_addr_city_code in
                <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodes != null and districtCodes.size()>0">
                and org_addr_func_district_code in
                <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="inOrgTypeCount" resultType="com.iflytek.cdc.edr.vo.InOrgTypeCountVO">
        select
            orgType,
            shouldInCount,
            hadInCount,
            case when shouldInCount != 0 then round(CAST(hadInCount AS DECIMAL(10,4)) / CAST(shouldInCount AS DECIMAL(10,4)), 4) else 0 end as hadInCountRate
        from
        (select
            org_type as orgType,
            sum(case when in_status in ('未对接','正在对接','已对接') then org_cnt_now else 0 end) shouldInCount,
            sum(case when in_status in ('已对接') then org_cnt_now else 0 end )hadInCount
        from ads.ads_di_inorg_stat
        where day = #{statDate}
        <include refid="areaChoose"/>

        group by org_type) tab

    </select>

    <select id="areaCount" resultType="com.iflytek.cdc.edr.vo.AreaInOrgCountVO">
        select
            areaCode,
            areaLevel,
            areaName,
            shouldInCount,
            hadInCount,
            case when shouldInCount != 0 then round(CAST(hadInCount AS DECIMAL(10,4)) / CAST(shouldInCount AS DECIMAL(10,4)), 4) else 0 end as hadInCountRate
        from
        (select
            <if test="areaLevel == 1">
                org_addr_city_code as areaCode,
                org_addr_city as areaName,
                '2' as areaLevel,
            </if>
            <if test="areaLevel == 2 or areaLevel == 3">
                org_addr_func_district_code as areaCode,
                org_addr_func_district as areaName,
                '3' as areaLevel,
            </if>
            sum(case when in_status in ('未对接','正在对接','已对接') then org_cnt_now else 0 end) shouldInCount,
            sum(case when in_status in ('已对接') then org_cnt_now else 0 end ) hadInCount
        from ads.ads_di_inorg_stat
        where day <![CDATA[ <= ]]> #{statDate}
        <include refid="areaChoose"/>

        <if test="orgType != null">
            and org_type = #{orgType}
        </if>
        <if test="areaCode != null">
            <if test="areaLevel ==1 ">
                and org_addr_province_code = #{areaCode}
            </if>
            <if test="areaLevel ==2 ">
                and org_addr_city_code = #{areaCode}
            </if>
            <if test="areaLevel ==3 ">
                and org_addr_func_district_code = #{areaCode}
            </if>
        </if>
        group by
        <if test="areaLevel == 1">
            org_addr_city_code,
            org_addr_city

        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            org_addr_func_district_code,
            org_addr_func_district
        </if>) tab
        order by hadInCountRate desc
        <if test="limitSize != null">
            limit #{limitSize}
        </if>
    </select>

    <select id="areaStatistic" resultType="com.iflytek.cdc.edr.vo.AreaInOrgStatisticVO">
        select
            areaCode,
            areaName,
            areaLevel,
            cityCode,
            cityName,
            districtCode,
            districtName,
            levelHospitalNum,
            levelHospitalHadInNum,
            basicMedicalNum,
            basicMedicalHadInNum,
            laboratoryNum,
            laboratoryHadInNum
        from
        (select
        <if test="areaLevel == 1">
            org_addr_city_code as cityCode,
            org_addr_city as cityName,
            org_addr_city_code as areaCode,
            org_addr_city as areaName,
            '2' as areaLevel,
            null as districtCode,
            null as districtName,
        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            org_addr_city_code as cityCode,
            org_addr_city as cityName,
            org_addr_func_district_code as areaCode,
            org_addr_func_district as areaName,
            org_addr_func_district_code as districtCode,
            org_addr_func_district as districtName,
            '3' as areaLevel,
        </if>
        sum(case when org_type = '等级医院' and  in_status in ('未对接','正在对接','已对接') then org_cnt_now else 0 end) levelHospitalNum,
        sum(case when org_type = '等级医院' and in_status in ('已对接') then org_cnt_now else 0 end ) levelHospitalHadInNum,
        sum(case when org_type = '基层医疗机构' and  in_status in ('未对接','正在对接','已对接') then org_cnt_now else 0 end) basicMedicalNum,
        sum(case when org_type = '基层医疗机构' and in_status in ('已对接') then org_cnt_now else 0 end ) basicMedicalHadInNum,
        sum(case when org_type like '%实验室%' and in_status in ('未对接','正在对接','已对接') then org_cnt_now else 0 end ) laboratoryNum,
        sum(case when org_type like '%实验室%' and in_status in ('已对接') then org_cnt_now else 0 end ) laboratoryHadInNum
        from ads.ads_di_inorg_stat
        where day = #{statDate}
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and org_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="provinceCodeList != null and provinceCodeList.size()>0">
            and org_addr_province_code in
            <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodeList != null and cityCodeList.size()>0">
            and org_addr_city_code in
            <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodeList != null and districtCodeList.size()>0">
            and org_addr_func_district_code in
            <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org_addr_func_district_code = #{districtCode}
        </if>
        <if test="orgType != null">
            and org_type = #{orgType}
        </if>
        <if test="areaCode != null">
            <if test="areaLevel ==1 ">
                and org_addr_province_code = #{areaCode}
            </if>
            <if test="areaLevel ==2 ">
                and org_addr_city_code = #{areaCode}
            </if>
            <if test="areaLevel ==3 ">
                and org_addr_func_district_code = #{areaCode}
            </if>
        </if>
        group by
        <if test="areaLevel == 1">
            org_addr_city_code,
            org_addr_city

        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            org_addr_city_code,
            org_addr_city,
            org_addr_func_district_code,
            org_addr_func_district
        </if>) tab
        order by areaCode
    </select>

    <select id="areaStatisticBy" resultType="com.iflytek.cdc.edr.vo.AreaInOrgStatisticVO">
        select
        areaCode,
        areaName,
        areaLevel,
        cityCode,
        cityName,
        districtCode,
        districtName,
        levelHospitalNum,
        levelHospitalHadInNum,
        basicMedicalNum,
        basicMedicalHadInNum
        from
        (select
            org_addr_city_code as cityCode,
            org_addr_city as cityName,
            org_addr_func_district_code as areaCode,
            org_addr_func_district as areaName,
            org_addr_func_district_code as districtCode,
            org_addr_func_district as districtName,
            '3' as areaLevel,
            sum(case when org_type = '等级医院' and  in_status in ('未对接','正在对接','已对接') then org_cnt_now else 0 end) levelHospitalNum,
            sum(case when org_type = '等级医院' and in_status in ('已对接') then org_cnt_now else 0 end ) levelHospitalHadInNum,
            sum(case when org_type = '基层医疗机构' and  in_status in ('未对接','正在对接','已对接') then org_cnt_now else 0 end) basicMedicalNum,
            sum(case when org_type = '基层医疗机构' and in_status in ('已对接') then org_cnt_now else 0 end ) basicMedicalHadInNum,
            sum(case when org_type = '实验室' and in_status in ('未对接','正在对接','已对接') then org_cnt_now else 0 end ) laboratoryNum,
            sum(case when org_type = '实验室' and in_status in ('已对接') then org_cnt_now else 0 end ) laboratoryHadInNum
        from ads.ads_di_inorg_stat
        where day = #{statDate}
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and org_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org_addr_func_district_code = #{districtCode}
        </if>
        <if test="orgType != null">
            and org_type = #{orgType}
        </if>
        group by
            org_addr_city_code,
            org_addr_city,
            org_addr_func_district_code,
            org_addr_func_district) tab
    </select>


    <select id="selectDiInorgType" resultMap="AdsDiInorgInfoMap">
        select distinct org_type
            from ads.ads_di_inorg_info
            where in_status <![CDATA[ <> ]]> '不对接'
<!--            <include refid="areaChoose" />-->
    </select>
    <select id="getLastStatDateMaxDay" resultType="java.util.Date">
        select max(day) from ads.ads_di_inorg_stat
        where 1=1
        <if test="provinceCode != null and provinceCode != ''">
            and org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org_addr_district_code = #{districtCode}
        </if>
        <if test="date != null">
            and day <![CDATA[ <= ]]> #{date}
        </if>
    </select>

    <select id="getLastStatDateMinDay" resultType="java.util.Date">
        select max(day) from ads.ads_di_inorg_stat
        where 1=1
        <if test="provinceCode != null and provinceCode != ''">
            and org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org_addr_district_code = #{districtCode}
        </if>
        <if test="date != null">
            and day <![CDATA[ <= ]]> #{date}
        </if>
    </select>

    <select id="inOrgTypeTimeTrend" resultType="com.iflytek.cdc.province.model.vo.TimeTrendVO">
        select
            "day"  as stat_date,
            sum(org_cnt_now) as value
        from ads.ads_di_inorg_stat adis
        where 1=1 <include refid="areaChoose"/>
        <if test="orgType != null">
            and org_type = #{orgType}
        </if>
        <if test="inStatus != null">
            and in_status = #{inStatus}
        </if>
        <if test="startDate != null and endDate != null">
            and day between #{startDate} and #{endDate}
        </if>
        group by day
    </select>

    <!-- 查询区域下的具体机构信息 -->
    <select id="queryOrgDetails" resultType="com.iflytek.cdc.edr.vo.OrgDetailInfoVO">
        select
            org_id as orgId,
            org_name as orgName,
            org_type as orgType,
            root_org_id as rootOrgId,
            root_org_name as rootOrgName,
            mana_org_id as manaOrgId,
            mana_org_name as manaOrgName,
            in_status as inStatus,
            org_addr_province_code as provinceCode,
            org_addr_province as provinceName,
            org_addr_city_code as cityCode,
            org_addr_city as cityName,
            org_addr_district_code as districtCode,
            org_addr_district as districtName,
            org_addr_func_district_code as funcDistrictCode,
            org_addr_func_district as funcDistrictName,
            org_addr_street_code as streetCode,
            org_addr_street as streetName
        from ads.ads_di_inorg_stat
        where day = #{statDate}
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and org_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and org_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="orgType != null and orgType != ''">
            and org_type = #{orgType}
        </if>
        <if test="inStatus != null and inStatus != ''">
            and in_status = #{inStatus}
        </if>
        <if test="orgName != null and orgName != ''">
            and org_name like concat('%', #{orgName}, '%')
        </if>
        <if test="manaOrgId != null and manaOrgId != ''">
            and mana_org_id = #{manaOrgId}
        </if>
        <if test="areaCode != null and areaCode != ''">
            <if test="areaLevel == 1">
                and org_addr_province_code = #{areaCode}
            </if>
            <if test="areaLevel == 2">
                and org_addr_city_code = #{areaCode}
            </if>
            <if test="areaLevel == 3">
                and org_addr_func_district_code = #{areaCode}
            </if>
        </if>
        order by org_addr_province_code, org_addr_city_code, org_addr_func_district_code, org_name
    </select>

    <!-- 查询区域下的机构对接状态分组信息 -->
    <select id="queryOrgDetailsByStatus" resultType="com.iflytek.cdc.edr.vo.OrgDetailInfoVO">
        select
            org_id as orgId,
            org_name as orgName,
            org_type as orgType,
            root_org_id as rootOrgId,
            root_org_name as rootOrgName,
            mana_org_id as manaOrgId,
            mana_org_name as manaOrgName,
            in_status as inStatus,
            org_addr_province_code as provinceCode,
            org_addr_province as provinceName,
            org_addr_city_code as cityCode,
            org_addr_city as cityName,
            org_addr_district_code as districtCode,
            org_addr_district as districtName,
            org_addr_func_district_code as funcDistrictCode,
            org_addr_func_district as funcDistrictName,
            org_addr_street_code as streetCode,
            org_addr_street as streetName
        from ads.ads_di_inorg_stat
        where day = #{statDate}
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and org_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and org_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="orgType != null and orgType != ''">
            and org_type = #{orgType}
        </if>
        <if test="orgName != null and orgName != ''">
            and org_name like concat('%', #{orgName}, '%')
        </if>
        <if test="manaOrgId != null and manaOrgId != ''">
            and mana_org_id = #{manaOrgId}
        </if>
        <if test="areaCode != null and areaCode != ''">
            <if test="areaLevel == 1">
                and org_addr_province_code = #{areaCode}
            </if>
            <if test="areaLevel == 2">
                and org_addr_city_code = #{areaCode}
            </if>
            <if test="areaLevel == 3">
                and org_addr_func_district_code = #{areaCode}
            </if>
        </if>
        order by in_status, org_addr_province_code, org_addr_city_code, org_addr_func_district_code, org_name
    </select>

</mapper>