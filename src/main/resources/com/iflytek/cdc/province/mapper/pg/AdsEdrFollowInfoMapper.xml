<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrFollowInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsEdrFollowInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_edr_follow_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="followup_date" jdbcType="DATE" property="followupDate" />
    <result column="followup_mode_code" jdbcType="VARCHAR" property="followupModeCode" />
    <result column="followup_mode" jdbcType="VARCHAR" property="followupMode" />
    <result column="followup_status_code" jdbcType="VARCHAR" property="followupStatusCode" />
    <result column="followup_status" jdbcType="VARCHAR" property="followupStatus" />
    <result column="lost_reason_code" jdbcType="VARCHAR" property="lostReasonCode" />
    <result column="lost_reason" jdbcType="VARCHAR" property="lostReason" />
    <result column="mother_idcard" jdbcType="VARCHAR" property="motherIdcard" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="gestational_age" jdbcType="INTEGER" property="gestationalAge" />
    <result column="height" jdbcType="VARCHAR" property="height" />
    <result column="weight" jdbcType="VARCHAR" property="weight" />
    <result column="last_condom_use_code" jdbcType="VARCHAR" property="lastCondomUseCode" />
    <result column="last_condom_use" jdbcType="VARCHAR" property="lastCondomUse" />
    <result column="spouse_hiv_code" jdbcType="VARCHAR" property="spouseHivCode" />
    <result column="spouse_hiv" jdbcType="VARCHAR" property="spouseHiv" />
    <result column="spouse_hiv_date" jdbcType="DATE" property="spouseHivDate" />
    <result column="spouse_idcard" jdbcType="VARCHAR" property="spouseIdcard" />
    <result column="spouse_status_code" jdbcType="VARCHAR" property="spouseStatusCode" />
    <result column="spouse_status" jdbcType="VARCHAR" property="spouseStatus" />
    <result column="dot_mode_code" jdbcType="VARCHAR" property="dotModeCode" />
    <result column="dot_mode" jdbcType="VARCHAR" property="dotMode" />
    <result column="non_dot_reason_code" jdbcType="VARCHAR" property="nonDotReasonCode" />
    <result column="non_dot_reason" jdbcType="VARCHAR" property="nonDotReason" />
    <result column="dot_date" jdbcType="DATE" property="dotDate" />
    <result column="planned_med_days" jdbcType="INTEGER" property="plannedMedDays" />
    <result column="actualmed_days" jdbcType="NUMERIC" property="actualmedDays" />
    <result column="drug_leakages" jdbcType="INTEGER" property="drugLeakages" />
    <result column="smz_code" jdbcType="VARCHAR" property="smzCode" />
    <result column="next_followup_date" jdbcType="DATE" property="nextFollowupDate" />
    <result column="drug_change_reason_code" jdbcType="VARCHAR" property="drugChangeReasonCode" />
    <result column="drug_change_reason" jdbcType="VARCHAR" property="drugChangeReason" />
    <result column="drug_change_sideeffect_code" jdbcType="VARCHAR" property="drugChangeSideeffectCode" />
    <result column="drug_change_sideeffect" jdbcType="VARCHAR" property="drugChangeSideeffect" />
    <result column="drug_stop_reason_code" jdbcType="VARCHAR" property="drugStopReasonCode" />
    <result column="drug_stop_reason" jdbcType="VARCHAR" property="drugStopReason" />
    <result column="drug_stop_sideeffect_code" jdbcType="VARCHAR" property="drugStopSideeffectCode" />
    <result column="drug_stop_sideeffect" jdbcType="VARCHAR" property="drugStopSideeffect" />
    <result column="drug_followup_result_code" jdbcType="VARCHAR" property="drugFollowupResultCode" />
    <result column="drug_followup_result" jdbcType="VARCHAR" property="drugFollowupResult" />
    <result column="paralysis_position_code" jdbcType="VARCHAR" property="paralysisPositionCode" />
    <result column="paralysis_position" jdbcType="VARCHAR" property="paralysisPosition" />
    <result column="paralysis_degree_code" jdbcType="VARCHAR" property="paralysisDegreeCode" />
    <result column="paralysis_degree" jdbcType="VARCHAR" property="paralysisDegree" />
    <result column="walking_ability_code" jdbcType="VARCHAR" property="walkingAbilityCode" />
    <result column="walking_ability" jdbcType="VARCHAR" property="walkingAbility" />
    <result column="non_treatment_reason_code" jdbcType="VARCHAR" property="nonTreatmentReasonCode" />
    <result column="non_treatment_reason" jdbcType="VARCHAR" property="nonTreatmentReason" />
    <result column="who_ctnm_code" jdbcType="VARCHAR" property="whoCtnmCode" />
    <result column="who_ctnm" jdbcType="VARCHAR" property="whoCtnm" />
    <result column="clinical_measures_code" jdbcType="VARCHAR" property="clinicalMeasuresCode" />
    <result column="clinical_measures" jdbcType="VARCHAR" property="clinicalMeasures" />
    <result column="transout_date" jdbcType="DATE" property="transoutDate" />
    <result column="transout_org_code" jdbcType="VARCHAR" property="transoutOrgCode" />
    <result column="transout_org" jdbcType="VARCHAR" property="transoutOrg" />
    <result column="transin_date" jdbcType="DATE" property="transinDate" />
    <result column="transin_org_code" jdbcType="VARCHAR" property="transinOrgCode" />
    <result column="transin_org" jdbcType="VARCHAR" property="transinOrg" />
    <result column="disease_progression_code" jdbcType="VARCHAR" property="diseaseProgressionCode" />
    <result column="disease_progression" jdbcType="VARCHAR" property="diseaseProgression" />
    <result column="disease_sequelae_code" jdbcType="VARCHAR" property="diseaseSequelaeCode" />
    <result column="disease_sequelae" jdbcType="VARCHAR" property="diseaseSequelae" />
    <result column="payment_type_code" jdbcType="VARCHAR" property="paymentTypeCode" />
    <result column="payment_type" jdbcType="VARCHAR" property="paymentType" />
    <result column="create_org" jdbcType="VARCHAR" property="createOrg" />
    <result column="create_org_name" jdbcType="VARCHAR" property="createOrgName" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_org" jdbcType="VARCHAR" property="updateOrg" />
    <result column="update_org_name" jdbcType="VARCHAR" property="updateOrgName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="mpi_id" jdbcType="VARCHAR" property="mpiId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, etl_create_datetime, etl_update_datetime, event_id, followup_date, followup_mode_code, 
    followup_mode, followup_status_code, followup_status, lost_reason_code, lost_reason, 
    mother_idcard, empi_id, gestational_age, height, weight, last_condom_use_code, last_condom_use, 
    spouse_hiv_code, spouse_hiv, spouse_hiv_date, spouse_idcard, spouse_status_code, 
    spouse_status, dot_mode_code, dot_mode, non_dot_reason_code, non_dot_reason, dot_date, 
    planned_med_days, actualmed_days, drug_leakages, smz_code, next_followup_date, drug_change_reason_code, 
    drug_change_reason, drug_change_sideeffect_code, drug_change_sideeffect, drug_stop_reason_code, 
    drug_stop_reason, drug_stop_sideeffect_code, drug_stop_sideeffect, drug_followup_result_code, 
    drug_followup_result, paralysis_position_code, paralysis_position, paralysis_degree_code, 
    paralysis_degree, walking_ability_code, walking_ability, non_treatment_reason_code, 
    non_treatment_reason, who_ctnm_code, who_ctnm, clinical_measures_code, clinical_measures, 
    transout_date, transout_org_code, transout_org, transin_date, transin_org_code, transin_org, 
    disease_progression_code, disease_progression, disease_sequelae_code, disease_sequelae, 
    payment_type_code, payment_type, create_org, create_org_name, create_user, create_time, 
    update_org, update_org_name, update_user, update_time, source_id, mpi_id
  </sql>
  <select id="getAdsEdrFollowInfoVOList" resultType="com.iflytek.cdc.province.model.vo.edrcase.AdsEdrFollowInfoVO">
    select
    event_id, followup_date, followup_mode_code,
    followup_mode, followup_status_code, followup_status, lost_reason_code, lost_reason,
    mother_idcard, empi_id, gestational_age, height, weight, last_condom_use_code, last_condom_use,
    spouse_hiv_code, spouse_hiv, spouse_hiv_date, spouse_idcard, spouse_status_code,
    spouse_status, dot_mode_code, dot_mode, non_dot_reason_code, non_dot_reason, dot_date,
    planned_med_days, actualmed_days, drug_leakages, smz_code, next_followup_date, drug_change_reason_code,
    drug_change_reason, drug_change_sideeffect_code, drug_change_sideeffect, drug_stop_reason_code,
    drug_stop_reason, drug_stop_sideeffect_code, drug_stop_sideeffect, drug_followup_result_code,
    drug_followup_result, paralysis_position_code, paralysis_position, paralysis_degree_code,
    paralysis_degree, walking_ability_code, walking_ability, non_treatment_reason_code,
    non_treatment_reason, who_ctnm_code, who_ctnm, clinical_measures_code, clinical_measures,
    transout_date, transout_org_code, transout_org, transin_date, transin_org_code, transin_org,
    disease_progression_code, disease_progression, disease_sequelae_code, disease_sequelae,
    payment_type_code, payment_type
    from ads.ads_edr_follow_info
    where 1=1
    <if test="empiId != null and empiId != ''">
      and empi_id=#{empiId}
    </if>
    <if test="eventIds != null and eventIds.length>0 ">
      and event_id in
      <foreach item="eventId" collection="eventIds" open="(" separator="," close=")">
        #{eventId}
      </foreach>
    </if>
    order by followup_date desc
  </select>
</mapper>