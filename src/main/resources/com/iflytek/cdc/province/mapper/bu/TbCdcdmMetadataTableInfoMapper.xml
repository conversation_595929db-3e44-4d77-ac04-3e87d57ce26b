<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcdmMetadataTableInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableInfo">
    <!--@mbg.generated-->
    <!--@Table tb_cdcdm_metadata_table_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="table_code" jdbcType="VARCHAR" property="tableCode" />
    <result column="schema" jdbcType="VARCHAR" property="schema" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="table_type" jdbcType="VARCHAR" property="tableType" />
    <result column="alias" jdbcType="VARCHAR" property="alias" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="data_source_key" jdbcType="VARCHAR" property="dataSourceKey" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, table_code, "schema", "table_name", table_type, "alias", memo, create_time,
    update_time, creator, updater, delete_flag, filter_condition, data_source_key
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcdm_metadata_table_info
    where id = #{id,jdbcType=VARCHAR}
  </select>

   <select id="selectByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
     select
    <include refid="Base_Column_List" />
    from tb_cdcdm_metadata_table_info
    where table_code = #{code,jdbcType=VARCHAR}
  </select>

    <select id="getAllTableInfo" resultType="com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableInfo">
        select <include refid="Base_Column_List"/>
        from tb_cdcdm_metadata_table_info
        where delete_flag = '0'
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from tb_cdcdm_metadata_table_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableInfo">
    <!--@mbg.generated-->
    insert into tb_cdcdm_metadata_table_info (id, table_code, "schema",
      "table_name", table_type, "alias", 
      memo, create_time, update_time,
      creator, updater, delete_flag, data_source_key
      )
    values (#{id,jdbcType=VARCHAR}, #{tableCode,jdbcType=VARCHAR}, #{schema,jdbcType=VARCHAR}, 
      #{tableName,jdbcType=VARCHAR}, #{tableType,jdbcType=VARCHAR}, #{alias,jdbcType=VARCHAR}, 
      #{memo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=VARCHAR}, #{updater,jdbcType=VARCHAR}, #{deleteFlag,jdbcType=VARCHAR}, #{dataSourceKey,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableInfo">
    <!--@mbg.generated-->
    insert into tb_cdcdm_metadata_table_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        id,
      </if>
      <if test="tableCode != null and tableCode != ''">
        table_code,
      </if>
      <if test="schema != null and schema != ''">
        "schema",
      </if>
      <if test="tableName != null and tableName != ''">
        "table_name",
      </if>
      <if test="tableType != null and tableType != ''">
        table_type,
      </if>
      <if test="alias != null and alias != ''">
        "alias",
      </if>
      <if test="memo != null and memo != ''">
        memo,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="creator != null and creator != ''">
        creator,
      </if>
      <if test="updater != null and updater != ''">
        updater,
      </if>
      <if test="deleteFlag != null and deleteFlag != ''">
        delete_flag,
      </if>
      <if test="dataSourceKey != null and dataSourceKey != ''">
        data_source_key,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="tableCode != null and tableCode != ''">
        #{tableCode,jdbcType=VARCHAR},
      </if>
      <if test="schema != null and schema != ''">
        #{schema,jdbcType=VARCHAR},
      </if>
      <if test="tableName != null and tableName != ''">
        #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="tableType != null and tableType != ''">
        #{tableType,jdbcType=VARCHAR},
      </if>
      <if test="alias != null and alias != ''">
        #{alias,jdbcType=VARCHAR},
      </if>
      <if test="memo != null and memo != ''">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null and updater != ''">
        #{updater,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null and deleteFlag != ''">
        #{deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="dataSourceKey != null and dataSourceKey != ''">
        #{dataSourceKey,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableInfo">
    <!--@mbg.generated-->
    update tb_cdcdm_metadata_table_info
    <set>
      <if test="tableCode != null and tableCode != ''">
        table_code = #{tableCode,jdbcType=VARCHAR},
      </if>
      <if test="schema != null and schema != ''">
        "schema" = #{schema,jdbcType=VARCHAR},
      </if>
      <if test="tableName != null and tableName != ''">
        "table_name" = #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="tableType != null and tableType != ''">
        table_type = #{tableType,jdbcType=VARCHAR},
      </if>
      <if test="alias != null and alias != ''">
        "alias" = #{alias,jdbcType=VARCHAR},
      </if>
      <if test="memo != null and memo != ''">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="createDatetime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDatetime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null and updater != ''">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null and deleteFlag != ''">
        delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      </if>
      <if test="dataSourceKey != null and dataSourceKey != ''">
        data_source_key = #{dataSourceKey,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableInfo">
    <!--@mbg.generated-->
    update tb_cdcdm_metadata_table_info
    set table_code = #{tableCode,jdbcType=VARCHAR},
      "schema" = #{schema,jdbcType=VARCHAR},
      "table_name" = #{tableName,jdbcType=VARCHAR},
      table_type = #{tableType,jdbcType=VARCHAR},
      "alias" = #{alias,jdbcType=VARCHAR},
      memo = #{memo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      updater = #{updater,jdbcType=VARCHAR},
      delete_flag = #{deleteFlag,jdbcType=VARCHAR},
      data_source_key = #{dataSourceKey,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>