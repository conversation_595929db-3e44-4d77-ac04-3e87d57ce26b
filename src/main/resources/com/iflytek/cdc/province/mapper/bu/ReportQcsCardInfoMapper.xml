<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.ReportQcsCardInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.bu.ReportQcsCardInfo">
        <id column="id" property="id" />
        <result column="creator_id" property="creatorId" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater_id" property="updaterId" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="memo" property="memo" />
        <result column="main_record_id" property="mainRecordId" />
        <result column="process_id" property="processId" />
        <result column="report_id" property="reportId" />
        <result column="patient_name" property="patientName" />
        <result column="sex_name" property="sexName" />
        <result column="age" property="age" />
        <result column="age_unit" property="ageUnit" />
        <result column="living_addr_province_code" property="livingAddrProvinceCode" />
        <result column="living_addr_province" property="livingAddrProvince" />
        <result column="living_addr_city_code" property="livingAddrCityCode" />
        <result column="living_addr_city" property="livingAddrCity" />
        <result column="living_addr_district_code" property="livingAddrDistrictCode" />
        <result column="living_addr_district" property="livingAddrDistrict" />
        <result column="living_addr_func_district_code" property="livingAddrFuncDistrictCode" />
        <result column="living_addr_func_district" property="livingAddrFuncDistrict" />
        <result column="living_addr_street_code" property="livingAddrStreetCode" />
        <result column="living_addr_street" property="livingAddrStreet" />
        <result column="onset_time" property="onsetTime" />
        <result column="first_visit_time" property="firstVisitTime" />
        <result column="org_id" property="orgId" />
        <result column="org_name" property="orgName" />
        <result column="org_addr_province_code" property="orgAddrProvinceCode" />
        <result column="org_addr_province" property="orgAddrProvince" />
        <result column="org_addr_city_code" property="orgAddrCityCode" />
        <result column="org_addr_city" property="orgAddrCity" />
        <result column="org_addr_district_code" property="orgAddrDistrictCode" />
        <result column="org_addr_district" property="orgAddrDistrict" />
        <result column="org_addr_func_district_code" property="orgAddrFuncDistrictCode" />
        <result column="org_addr_func_district" property="orgAddrFuncDistrict" />
        <result column="org_addr_street_code" property="orgAddrStreetCode" />
        <result column="org_addr_street" property="orgAddrStreet" />
        <result column="disease_name" property="diseaseName" />
        <result column="disease_code" property="diseaseCode" />
        <result column="diag_time" property="diagTime" />
        <result column="patient_identity_type" property="patientIdentityType" />
        <result column="patient_identity_no" property="patientIdentityNo" />
        <result column="patient_birth_day" property="patientBirthDay" />
        <result column="dead_time" property="deadTime" />
        <result column="company" property="company" />
        <result column="patient_phone" property="patientPhone" />
        <result column="living_addr_detail" property="livingAddrDetail" />
        <result column="person_type" property="personType" />
        <result column="identify_class" property="identifyClass" />
        <result column="visit_type_name" property="visitTypeName" />
        <result column="first_diag_flag" property="firstDiagFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, memo, main_record_id, process_id, report_id, patient_name, sex_name, age, age_unit, living_addr_province_code, living_addr_province, living_addr_city_code, living_addr_city, living_addr_district_code, living_addr_district, living_addr_func_district_code, living_addr_func_district, living_addr_street_code, living_addr_street, onset_time, first_visit_time, org_id, org_name, org_addr_province_code, org_addr_province, org_addr_city_code, org_addr_city, org_addr_district_code, org_addr_district, org_addr_func_district_code, org_addr_func_district, org_addr_street_code, org_addr_street, disease_name, disease_code, diag_time, patient_identity_type, patient_identity_no, patient_birth_day, dead_time, company, patient_phone, living_addr_detail, person_type, identify_class, visit_type_name, first_diag_flag
    </sql>
    <insert id="mergeInto">
        INSERT INTO app.tb_cdcew_report_qcs_card_info
        ( id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, memo, main_record_id, process_id, report_id, patient_name, sex_name, age, age_unit, living_addr_province_code, living_addr_province, living_addr_city_code, living_addr_city, living_addr_district_code, living_addr_district, living_addr_func_district_code, living_addr_func_district, living_addr_street_code, living_addr_street, onset_time, first_visit_time, org_id, org_name, org_addr_province_code, org_addr_province, org_addr_city_code, org_addr_city, org_addr_district_code, org_addr_district, org_addr_func_district_code, org_addr_func_district, org_addr_street_code, org_addr_street, disease_name, disease_code, diag_time, patient_identity_type, patient_identity_no, patient_birth_day, dead_time, company, patient_phone, living_addr_detail, person_type, identify_class, visit_type_name, first_diag_flag)
        VALUES
        <foreach collection="cardInfos" item="item" separator=",">
            ( #{item.id}, #{item.creatorId}, #{item.creator}, #{item.createTime}, #{item.updaterId}, #{item.updater}, #{item.updateTime}, #{item.deleteFlag}, #{item.memo}, #{item.mainRecordId}, #{item.processId}, #{item.reportId}, #{item.patientName}, #{item.sexName}, #{item.age}, #{item.ageUnit}, #{item.livingAddrProvinceCode}, #{item.livingAddrProvince}, #{item.livingAddrCityCode}, #{item.livingAddrCity}, #{item.livingAddrDistrictCode}, #{item.livingAddrDistrict}, #{item.livingAddrFuncDistrictCode}, #{item.livingAddrFuncDistrict}, #{item.livingAddrStreetCode}, #{item.livingAddrStreet}, #{item.onsetTime}, #{item.firstVisitTime}, #{item.orgId}, #{item.orgName}, #{item.orgAddrProvinceCode}, #{item.orgAddrProvince}, #{item.orgAddrCityCode}, #{item.orgAddrCity}, #{item.orgAddrDistrictCode}, #{item.orgAddrDistrict}, #{item.orgAddrFuncDistrictCode}, #{item.orgAddrFuncDistrict}, #{item.orgAddrStreetCode}, #{item.orgAddrStreet}, #{item.diseaseName}, #{item.diseaseCode}, #{item.diagTime}, #{item.patientIdentityType}, #{item.patientIdentityNo}, #{item.patientBirthDay}, #{item.deadTime}, #{item.company}, #{item.patientPhone}, #{item.livingAddrDetail}, #{item.personType}, #{item.identifyClass}, #{item.visitTypeName}, #{item.firstDiagFlag} )
        </foreach>

    </insert>
    <update id="deleteByMainRecordId">
        delete from app.tb_cdcew_report_qcs_card_info where main_record_id=#{mainRecordId}
    </update>


</mapper>
