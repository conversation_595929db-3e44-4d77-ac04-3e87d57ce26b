<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMsMedicalInfoMapper">

    <sql id="visitDetailAddressCriteria">
        <if test="addressType == 'livingAddress'">
            <if test="provinceCode != null and provinceCode != ''">
                and living_addr_province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode != ''">
                and living_addr_city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode != ''">
                and living_addr_func_district_code = #{districtCode}
            </if>
            <if test="streetCode != null and streetCode != ''">
                and living_addr_street_code = #{streetCode}
            </if>
            <if test="provinceCodes != null and provinceCodes.size()>0">
                and living_addr_province_code in
                <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodes != null and cityCodes.size()>0">
                and living_addr_city_code in
                <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodes != null and districtCodes.size()>0">
                and living_addr_func_district_code in
                <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="streetCodes != null and streetCodes.size()>0">
                and living_addr_street_code in
                <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="addressType == 'orgAddress'">
            <if test="provinceCode != null and provinceCode != ''">
                and org_addr_province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode != ''">
                and org_addr_city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode != ''">
                and org_addr_func_district_code = #{districtCode}
            </if>
            <if test="streetCode != null and streetCode != ''">
                and org_addr_street_code = #{streetCode}
            </if>
            <if test="provinceCodes != null and provinceCodes.size()>0">
                and org_addr_province_code in
                <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodes != null and cityCodes.size()>0">
                and org_addr_city_code in
                <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodes != null and districtCodes.size()>0">
                and org_addr_func_district_code in
                <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="streetCodes != null and streetCodes.size()>0">
                and org_addr_street_code in
                <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </if>
    </sql>

    <select id="getPatientMedicalList" resultType="com.iflytek.cdc.province.model.vo.PatientMedicalInfoVO">
        select
        medical_id as dataId,
        visit_time as visitDay,
        visit_type_code,
        visit_type_name
        from ads.ads_ms_medical_info
        where patient_id = #{patientId} and (visit_type_code is not null or visit_type_name is not null)
        order by visit_time desc
    </select>

    <select id="getVisitPersonDetailBy" resultType="com.iflytek.cdc.province.model.vo.VisitPersonInfoVO">
        select
        medical_id,
        patient_name,
        patient_sex_name,
        CONCAT(
            CAST(
                EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM patient_birth_day) - 
                CASE 
                    WHEN EXTRACT(MONTH FROM CURRENT_DATE) &lt; EXTRACT(MONTH FROM patient_birth_day) 
                        OR (EXTRACT(MONTH FROM CURRENT_DATE) = EXTRACT(MONTH FROM patient_birth_day) 
                            AND EXTRACT(DAY FROM CURRENT_DATE) &lt; EXTRACT(DAY FROM patient_birth_day))
                    THEN 1 
                    ELSE 0 
                END AS SIGNED
            ), '岁'
        ) as patientAge,
        <if test="addressType == 'livingAddress'">
            living_addr_func_district as region,
        </if>
        <if test="addressType == 'orgAddress'">
            org_addr_func_district as region,
        </if>
        visit_time,
        visit_type_name,
        org_name,
        dept_name
        from ads.ads_ms_medical_info
        where visit_time between #{startDate} and #{endDate}
        <include refid="visitDetailAddressCriteria"/>
        <if test="visitType != null and visitType != ''">
            and visit_type_code = #{visitType}
        </if>
        <if test="isHeat != null and isHeat != ''">
            and heat_flag = #{isHeat}
        </if>
        <if test="isBowel != null and isBowel != ''">
            and bowel_flag = #{isBowel}
        </if>
    </select>

    <select id="getMedicalIdByEventIdList" resultType="java.lang.String">
        select medical_id
        from ads.ads_ms_medical_info
        where event_id in
        <foreach collection="eventIdList" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

</mapper>