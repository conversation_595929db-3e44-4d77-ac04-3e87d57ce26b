<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.mpp.DataModelMPPBaseMapper">

    <select id="selectDataBySql" resultType="map">
        ${customSql}
    </select>

    <select id="getRecordLifeCycle" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO">
        select distinct occur_time as "date", tag as "type", tag_value as "value"
        from dws.dws_ms_tag
        where remark = 'KIE' and empi_id in (select empi_id from ads.ads_fs_edr_info where archive_id = #{archiveId})
        order by occur_time
    </select>

    <select id="getCommonRecordLogById" resultType="com.iflytek.cdc.province.model.edr.vo.CommonRecordLogVO">
        select
            a.archive_id,
            b.patient_id,
            b.patient_name,
            a.life_id,
            a.permit_living_area_code,
            a.permit_company_area_code,
            a.permit_org_area_code
        from ads.ads_fs_edr_info a
        join dim.dim_patient_info b on a.empi_id = b.empi_id
        where a.archive_id = #{archiveId}
    </select>
</mapper>
