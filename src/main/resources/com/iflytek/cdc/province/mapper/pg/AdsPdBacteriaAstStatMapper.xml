<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsPdBacteriaAstStatMapper">

    <sql id="dayCriteria">
        <if test="startDate != null and endDate != null">
            and day between #{startDate} and #{endDate}
        </if>
    </sql>

    <sql id="weekCriteria">
        <if test="startDate != null and endDate != null">
            and monday between #{startDate} and #{endDate}
        </if>
    </sql>

    <sql id="meadowCriteria">
        and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month} and ten_day = #{item.meadow})
        </foreach>
    </sql>

    <sql id="monthCriteria">
        and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month})
        </foreach>
    </sql>

    <sql id="quarterCriteria">
        and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and quarter = #{item.quarter})
        </foreach>
    </sql>

    <sql id="yearCriteria">
        and year in
        <foreach collection="dateDims" open="(" close=")" separator="," item="item">
            #{item.year}
        </foreach>
    </sql>

    <sql id="chooseTime">
        <choose>
            <when test="dateDimType == 'day'">
                <include refid="dayCriteria"/>
            </when>
            <when test="dateDimType == 'week'">
                <include refid="weekCriteria"/>
            </when>
            <when test="dateDimType == 'meadow'">
                <include refid="meadowCriteria"/>
            </when>
            <when test="dateDimType == 'month'">
                <include refid="monthCriteria"/>
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterCriteria"/>
            </when>
            <when test="dateDimType == 'year'">
                <include refid="yearCriteria"/>
            </when>
            <otherwise>
                <include refid="dayCriteria"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="whereCriteria">
        <include refid="chooseTime"/>
        <if test="labIdList != null and labIdList.size() &gt; 0">
            and test_org_id in
            <foreach collection="labIdList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="allPathogenNameList != null and allPathogenNameList.size() &gt; 0">
            and bacteria_name in
            <foreach collection="allPathogenNameList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="getDrugSensitivityBy"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.DrugSituationVO">
        select
            drug_name,
            COALESCE(sum(case when ast_quality_res = '耐药' then sample_cnt else 0 end), 0) as drugResistance ,
            COALESCE(sum(case when ast_quality_res = '中介' then sample_cnt else 0 end), 0) as mediumSensitivity,
            COALESCE(sum(case when ast_quality_res = '敏感' then sample_cnt else 0 end), 0) as sensitivity
        from ads.ads_pd_bacteria_ast_stat
        where drug_name is not null
            <include refid="whereCriteria"/>
        group by drug_name
    </select>

</mapper>
