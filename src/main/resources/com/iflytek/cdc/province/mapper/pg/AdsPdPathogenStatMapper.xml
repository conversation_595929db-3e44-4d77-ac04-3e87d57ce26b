<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsPdPathogenStatMapper">

    <sql id="regionChoose">
        <if test="provinceCodeList != null and provinceCodeList.size() > 0">
            and drn.province_code in
            <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodeList != null and cityCodeList.size() > 0">
            and drn.city_code in
            <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodeList != null and districtCodeList.size() > 0">
            and drn.district_code in
            <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodeList != null and streetCodeList.size() > 0">
            and drn.street_code  in
            <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and drn.province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and drn.city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and drn.district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size() > 0">
            and drn.street_code  in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="orgArea">
        <if test="provinceCode != null and provinceCode != ''">
            and org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and org_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and org_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and org_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="orgMultiArea">
        <if test="provinceCodeList != null and provinceCodeList.size()>0">
            and org_addr_province_code in
            <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodeList != null and cityCodeList.size()>0">
            and org_addr_city_code in
            <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodeList != null and districtCodeList.size()>0">
            and org_addr_func_district_code in
            <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodeList != null and streetCodeList.size()>0">
            and org_addr_street_code in
            <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and org_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and org_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="livingArea">
        <if test="provinceCode != null and provinceCode != ''">
            and living_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and living_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and living_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and living_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and living_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and living_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and living_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and living_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="livingMultiArea">
        <if test="provinceCodeList != null and provinceCodeList.size() > 0">
            and living_addr_province_code in
            <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodeList != null and cityCodeList.size() > 0">
            and living_addr_city_code  in
            <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodeList != null and districtCodeList.size() > 0">
            and living_addr_func_district_code in
            <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodeList != null and streetCodeList.size() > 0">
            and living_addr_street_code  in
            <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and living_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and living_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and living_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and living_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="chooseArea">
        <choose>
            <when test="addressType == 'orgAddress'">
                <include refid="orgArea"/>
            </when>
            <when test="addressType == 'livingAddress'">
                <include refid="livingArea"/>
            </when>
            <otherwise>
                <include refid="livingArea"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="chooseMultiArea">
        <choose>
            <when test="addressType == 'orgAddress'">
                <include refid="orgMultiArea"/>
            </when>
            <when test="addressType == 'livingAddress'">
                <include refid="livingMultiArea"/>
            </when>
            <otherwise>
                <include refid="livingMultiArea"/>
            </otherwise>
        </choose>
    </sql>


    <sql id="dayTable">
        <choose>
            <when test="timeType == 'reportTime'">
                ads.ads_pd_pathogen_rt_d
            </when>
            <when test="timeType == 'onsetTime'">
                ads.ads_pd_pathogen_ot_d
            </when>
            <when test="timeType == 'identifyTime'">
                ads.ads_pd_pathogen_it_d
            </when>
            <otherwise>
                ads.ads_pd_pathogen_ot_d
            </otherwise>
        </choose>
    </sql>

    <sql id="weekTable">
        <choose>
            <when test="timeType == 'reportTime'">
                ads.ads_pd_pathogen_rt_w
            </when>
            <when test="timeType == 'onsetTime'">
                ads.ads_pd_pathogen_ot_w
            </when>
            <when test="timeType == 'identifyTime'">
                ads.ads_pd_pathogen_it_w
            </when>
            <otherwise>
                ads.ads_pd_pathogen_ot_w
            </otherwise>
        </choose>
    </sql>

    <sql id="meadowTable">
        <choose>
            <when test="timeType == 'reportTime'">
                ads.ads_pd_pathogen_rt_td
            </when>
            <when test="timeType == 'onsetTime'">
                ads.ads_pd_pathogen_ot_td
            </when>
            <when test="timeType == 'identifyTime'">
                ads.ads_pd_pathogen_it_td
            </when>
            <otherwise>
                ads.ads_pd_pathogen_ot_td
            </otherwise>
        </choose>
    </sql>

    <sql id="monthTable">
        <choose>
            <when test="timeType == 'reportTime'">
                ads.ads_pd_pathogen_rt_m
            </when>
            <when test="timeType == 'onsetTime'">
                ads.ads_pd_pathogen_ot_m
            </when>
            <when test="timeType == 'identifyTime'">
                ads.ads_pd_pathogen_it_m
            </when>
            <otherwise>
                ads.ads_pd_pathogen_ot_m
            </otherwise>
        </choose>
    </sql>

    <sql id="quarterTable">
        <choose>
            <when test="timeType == 'reportTime'">
                ads.ads_pd_pathogen_rt_q
            </when>
            <when test="timeType == 'onsetTime'">
                ads.ads_pd_pathogen_ot_q
            </when>
            <when test="timeType == 'identifyTime'">
                ads.ads_pd_pathogen_it_q
            </when>
            <otherwise>
                ads.ads_pd_pathogen_ot_q
            </otherwise>
        </choose>
    </sql>

    <sql id="yearTable">
        <choose>
            <when test="timeType == 'reportTime'">
                ads.ads_pd_pathogen_rt_y
            </when>
            <when test="timeType == 'onsetTime'">
                ads.ads_pd_pathogen_ot_y
            </when>
            <when test="timeType == 'identifyTime'">
                ads.ads_pd_pathogen_it_y
            </when>
            <otherwise>
                ads.ads_pd_pathogen_ot_y
            </otherwise>
        </choose>
    </sql>

    <sql id="chooseTable">
        <choose>
            <when test="dateDimType == 'day'">
                <include refid="dayTable"/>
            </when>
            <when test="dateDimType == 'week'">
                <include refid="weekTable"/>
            </when>
            <when test="dateDimType == 'meadow'">
                <include refid="meadowTable"/>
            </when>
            <when test="dateDimType == 'month' or dateDimType == 'halfYear'">
                <include refid="monthTable"/>
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterTable"/>
            </when>
            <when test="dateDimType == 'year'">
                <include refid="yearTable"/>
            </when>
            <otherwise>
                <include refid="dayTable"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="dayCriteria">
        <if test="startDate != null and endDate != null">
            and day between #{startDate} and #{endDate}
        </if>
    </sql>

    <sql id="weekCriteria">
        <if test="startDate != null and endDate != null">
            and monday between #{startDate} and #{endDate}
        </if>
    </sql>

    <sql id="meadowCriteria">
        and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month} and ten_day = #{item.meadow})
        </foreach>
    </sql>

    <sql id="monthCriteria">
        and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month})
        </foreach>
    </sql>

    <sql id="quarterCriteria">
        and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and quarter = #{item.quarter})
        </foreach>
    </sql>

    <sql id="yearCriteria">
        and year in
        <foreach collection="dateDims" open="(" close=")" separator="," item="item">
            #{item.year}
        </foreach>
    </sql>

    <sql id="chooseTime">
        <choose>
            <when test="dateDimType == 'day'">
                <include refid="dayCriteria"/>
            </when>
            <when test="dateDimType == 'week'">
                <include refid="weekCriteria"/>
            </when>
            <when test="dateDimType == 'meadow'">
                <include refid="meadowCriteria"/>
            </when>
            <when test="dateDimType == 'month' or dateDimType == 'halfYear'">
                <include refid="monthCriteria"/>
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterCriteria"/>
            </when>
            <when test="dateDimType == 'year'">
                <include refid="yearCriteria"/>
            </when>
            <otherwise>
                <include refid="dayCriteria"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="statCriteria">
        <if test="processType != null and processType != ''">
            and process_type = #{processType}
        </if>
        <if test="labIdList != null and labIdList.size() &gt; 0">
            and test_org_id in
            <foreach collection="labIdList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="pathogenType != null and pathogenType != ''">
            and pathogen_type = #{pathogenType}
        </if>
        <if test="pathogenName != null and pathogenName != ''">
            and (pathogen_name = #{pathogenName} or pathogen_sub_name = #{pathogenName})
        </if>
        <if test="pneumoniaFlag != null and pneumoniaFlag != ''">
            and pneumonia_flag = #{pneumoniaFlag}
        </if>
        <if test="diseaseCode != null and diseaseCode != ''">
            and disease_code = #{diseaseCode}
        </if>
        <if test="pathogenClassCode != null and pathogenClassCode.size() &gt; 0">
            and pathogen_type in
            <foreach collection="pathogenClassCode" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="allPathogenNameList != null and allPathogenNameList.size() &gt; 0">
            and (
            pathogen_name in
            <foreach collection="allPathogenNameList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
            or
            pathogen_sub_name in
            <foreach collection="allPathogenNameList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
    </sql>

    <sql id="timeTypeWhere">
        <choose>
            <when test="timeType == 'reportTime'">
                i.test_report_day
            </when>
            <when test="timeType == 'onsetTime'">
                i.process_fi_onset_day
            </when>
            <when test="timeType == 'identifyTime'">
                i.process_fi_identify_day
            </when>
            <otherwise>
                i.process_fi_onset_day
            </otherwise>
        </choose>
    </sql>


    <sql id="whereCriteria">
        <include refid="chooseArea"/>
        <include refid="chooseTime"/>
        <if test="processType != null and processType != ''">
            and process_type = #{processType}
        </if>
        <if test="labIdList != null and labIdList.size() &gt; 0">
            and test_org_id in
            <foreach collection="labIdList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="pathogenType != null and pathogenType != ''">
            and pathogen_type = #{pathogenType}
        </if>
        <if test="pathogenName != null and pathogenName != ''">
            and (pathogen_name = #{pathogenName} or pathogen_sub_name = #{pathogenName})
        </if>
        <if test="pneumoniaFlag != null and pneumoniaFlag != ''">
            and pneumonia_flag = #{pneumoniaFlag}
        </if>
        <if test="diseaseCode != null and diseaseCode != ''">
            and manage_disease_code = #{diseaseCode}
        </if>
        <if test="pathogenClassCode != null and pathogenClassCode.size() &gt; 0">
            and pathogen_type in
            <foreach collection="pathogenClassCode" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="allPathogenNameList != null and allPathogenNameList.size() &gt; 0">
            and (
            pathogen_name in
            <foreach collection="allPathogenNameList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
            or
            pathogen_sub_name in
            <foreach collection="allPathogenNameList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
    </sql>

    <select id="groupPositiveByName" resultType="com.iflytek.cdc.province.model.pathogen.PathogenNameGroupVO">
        select
            COALESCE(cur.pathogen_name, lastY.pathogen_name) as pathogen_name,
            COALESCE(cur.pathogen_type, lastY.pathogen_type) as pathogen_type,
            COALESCE(cur.processTestCnt, 0) as processTestCnt,
            COALESCE(lastY.processTestCnt, 0) as processTestCntLastY,
            COALESCE(cur.processPositiveCnt, 0) as processPositiveCnt,
            COALESCE(lastY.processPositiveCnt, 0) as processPositiveCntLastY,
            COALESCE(cur.sampleCnt, 0) as sampleCnt,
            COALESCE(cur.samplePositiveCnt, 0) as samplePositiveCnt,
            COALESCE(cur.drugPositiveCnt, 0) as drugPositiveCnt
        from
        (
            select
            pathogen_name,
            pathogen_type,
            COALESCE(COUNT(DISTINCT process_id), 0) as processTestCnt,
            COALESCE(COUNT(DISTINCT CASE WHEN test_out_flag = '1' THEN process_id END), 0) as processPositiveCnt,
            COALESCE(COUNT(DISTINCT sample_id), 0) as sampleCnt,
            COALESCE(COUNT(DISTINCT CASE WHEN test_out_flag = '1' THEN sample_id END), 0) as samplePositiveCnt,
            COALESCE(COUNT(DISTINCT CASE WHEN pathogen_drug_flag = '1' THEN sample_id END), 0) as drugPositiveCnt
            from
            ads.ads_pd_pathogen_check_case_info i
            join dim.dim_region_nation drn
            on
            <choose>
                <when test="addressType == 'orgAddress'">
                    i.org_region_code = drn.region_code
                </when>
                <otherwise>
                    i.living_region_code = drn.region_code
                </otherwise>
            </choose>
            <include refid="regionChoose"/>
            where
            <include refid="timeTypeWhere"/> between #{startDate} and #{endDate}
            <include refid="statCriteria"/>
            <if test="pathogenNameList != null and pathogenNameList.size() > 0">
                and (
                pathogen_name in
                <foreach collection="pathogenNameList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
                or
                pathogen_sub_name in
                <foreach collection="pathogenNameList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            group by pathogen_name, pathogen_type
        ) cur
        full join
        (
            select
            pathogen_name,
            pathogen_type,
            COALESCE(COUNT(DISTINCT process_id), 0) as processTestCnt,
            COALESCE(COUNT(DISTINCT CASE WHEN test_out_flag = '1' THEN process_id END), 0) as processPositiveCnt
            from
            ads.ads_pd_pathogen_check_case_info i
            join dim.dim_region_nation drn
            on
            <choose>
                <when test="addressType == 'orgAddress'">
                    i.org_region_code = drn.region_code
                </when>
                <otherwise>
                    i.living_region_code = drn.region_code
                </otherwise>
            </choose>
            <include refid="regionChoose"/>
            where
            <include refid="timeTypeWhere"/> between  #{lastYStartDate} and #{lastYEndDate}
            <include refid="statCriteria"/>
            <if test="pathogenNameList != null and pathogenNameList.size() > 0">
                and (
                pathogen_name in
                <foreach collection="pathogenNameList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
                or
                pathogen_sub_name in
                <foreach collection="pathogenNameList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>
            group by pathogen_name, pathogen_type
        ) lastY
        on lastY.pathogen_name = cur.pathogen_name and lastY.pathogen_type = cur.pathogen_type
        order by pathogen_name
    </select>

    <sql id="dimDateJoinCondition">
        <choose>
            <when test="timeType == 'reportTime'">
                i.test_report_day = d.date_day
            </when>
            <when test="timeType == 'onsetTime'">
                i.process_fi_onset_day = d.date_day
            </when>
            <when test="timeType == 'identifyTime'">
                i.process_fi_identify_day = d.date_day
            </when>
            <otherwise>
                i.process_fi_onset_day = d.date_day
            </otherwise>
        </choose>
    </sql>

    <sql id="detailWhereCriteria">
        <include refid="regionChoose"/>
        and <include refid="timeTypeWhere"/> between #{startDate} and #{endDate}
        <include refid="statCriteria"/>
    </sql>

    <select id="listPositiveRatioByAgeAndSex"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.PathogenPositiveAgeAndSexVO">
        select
            pathogen_name,
            patient_age_group as ageGroup,
            COALESCE(COUNT(DISTINCT process_id), 0) as detectCount,
            COALESCE(COUNT(DISTINCT CASE WHEN test_out_flag = '1' THEN process_id END), 0) as positiveCount,
            COALESCE(sum(case when patient_sex_name = '男性' then 1 else 0 end), 0) as maleCount,
            COALESCE(sum(case when patient_sex_name = '女性' then 1 else 0 end), 0) as femaleCount,
            COALESCE(sum(case when patient_sex_name = '男性' and test_out_flag = '1' then 1 else 0 end), 0) as malePositiveCount,
            COALESCE(sum(case when patient_sex_name = '女性' and test_out_flag = '1' then 1 else 0 end), 0) as femalePositiveCount
        from
            ads.ads_pd_pathogen_check_case_info i
            left join dim.dim_date_day d on <include refid="dimDateJoinCondition"/>
            join dim.dim_region_nation drn
            on
            <choose>
                <when test="addressType == 'orgAddress'">
                    i.org_region_code = drn.region_code
                </when>
                <otherwise>
                    i.living_region_code = drn.region_code
                </otherwise>
            </choose>
        where pathogen_name is not null
            <include refid="detailWhereCriteria"/>
        group by pathogen_name, patient_age_group
    </select>

    <select id="listPathogenSpectrumResult"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.PathogenSpectrumCompareVO">
        select
            pathogen_name,
            COALESCE(sum(case when pneumonia_flag = '1' then 1 else 0 end), 0) as pneumoniaCount,
            COALESCE(sum(case when pneumonia_flag = '1' and test_out_flag = '1' then 1 else 0 end), 0) as pneumoniaPositiveCount,
            COALESCE(sum(case when pneumonia_flag = '0' then 1 else 0 end), 0) as notPneumoniaCount,
            COALESCE(sum(case when pneumonia_flag = '0' and test_out_flag = '1' then 1 else 0 end), 0) as notPneumoniaPositiveCount,
            COALESCE(sum(case when heat_flag = '1' then 1 else 0 end), 0) as feverCount,
            COALESCE(sum(case when heat_flag = '1' and test_out_flag = '1' then 1 else 0 end), 0) as feverPositiveCount,
            COALESCE(sum(case when heat_flag = '0' then 1 else 0 end), 0) as notFeverCount,
            COALESCE(sum(case when heat_flag = '0' and test_out_flag = '1' then 1 else 0 end), 0) as notFeverPositiveCount
        from
            ads.ads_pd_pathogen_check_case_info i
            left join dim.dim_date_day d on <include refid="dimDateJoinCondition"/>
            join dim.dim_region_nation drn
            on
            <choose>
                <when test="addressType == 'orgAddress'">
                    i.org_region_code = drn.region_code
                </when>
                <otherwise>
                    i.living_region_code = drn.region_code
                </otherwise>
            </choose>
        where pathogen_name is not null
            <include refid="detailWhereCriteria"/>
        group by pathogen_name
    </select>

    <select id="getVirusCheckStat"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.VirusCheckResultVO">
        SELECT
            pathogen_name,
            coalesce(nullif(pathogen_sub_name, ''), pathogen_name) as type,
            coalesce(COUNT(DISTINCT sample_id), 0) as sampleCount,
            coalesce(COUNT(DISTINCT CASE WHEN test_out_flag = '1' THEN sample_id END), 0) as positiveCount,
            coalesce(COUNT(DISTINCT process_id), 0) as detectedCaseCount,
            coalesce(COUNT(DISTINCT CASE WHEN test_out_flag = '1' THEN process_id END), 0) as positiveCaseCount,
            ROUND( case
            when COUNT(DISTINCT sample_id) = 0 then 0
            else COUNT(DISTINCT CASE WHEN test_out_flag = '1' THEN sample_id END) * 1.0 / nullif(COUNT(DISTINCT sample_id), 0)
            end, 4 ) as pathogenDetectionRate,
            ROUND( case
            when COUNT(DISTINCT process_id) = 0 then 0
            else COUNT(DISTINCT CASE WHEN test_out_flag = '1' THEN process_id END) * 1.0 / nullif(COUNT(DISTINCT process_id), 0)
            end, 4 ) as positiveCaseRate
        from
            ads.ads_pd_pathogen_check_case_info i
            left join dim.dim_date_day d on <include refid="dimDateJoinCondition"/>
            join dim.dim_region_nation drn
            on
            <choose>
                <when test="addressType == 'orgAddress'">
                    i.org_region_code = drn.region_code
                </when>
                <otherwise>
                    i.living_region_code = drn.region_code
                </otherwise>
            </choose>
        where pathogen_name is not null
        <include refid="detailWhereCriteria"/>
        <if test="pathogenNameList != null and pathogenNameList.size() > 0">
            and (
            pathogen_name in
            <foreach collection="pathogenNameList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
            or
            pathogen_sub_name in
            <foreach collection="pathogenNameList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        group by pathogen_name, COALESCE(NULLIF(pathogen_sub_name, ''), pathogen_name)
    </select>
    
    <select id="getMDRCheckRateByGradeHospital"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.DrugResistantHospitalVO">
        select
            concat('耐', pathogen_name) as pathogenName,
            (case when sample_org_childre_flag = '1' then '儿童医院' else sample_org_class end) as orgType,
            COALESCE(COUNT(DISTINCT sample_id), 0) as sampleCount,
            COALESCE(COUNT(DISTINCT CASE WHEN pathogen_drug_flag = '1' THEN sample_id END), 0) as positiveCount
        from
            ads.ads_pd_pathogen_check_case_info i
            left join dim.dim_date_day d on <include refid="dimDateJoinCondition"/>
            join dim.dim_region_nation drn
        on
        <choose>
            <when test="addressType == 'orgAddress'">
                i.org_region_code = drn.region_code
            </when>
            <otherwise>
                i.living_region_code = drn.region_code
            </otherwise>
        </choose>
        where pathogen_name is not null
        <include refid="detailWhereCriteria"/>
        group by concat('耐', pathogen_name), (case when sample_org_childre_flag = '1' then '儿童医院' else sample_org_class end)
    </select>

    <select id="getMDRCheckByDistrict"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.MDRCheckAreaDistributionVO">
        select
        concat('耐', pathogen_name) as pathogenName,
        <if test="areaLevel == 1">
            drn.province_name as areaName,
        </if>
        <if test="areaLevel == 2">
            drn.city_name as areaName,
        </if>
        <if test="areaLevel == 3">
            drn.district_name as areaName,
        </if>
        COALESCE(COUNT(DISTINCT sample_id), 0) as sampleCount,
        COALESCE(COUNT(DISTINCT CASE WHEN pathogen_drug_flag = '1' THEN sample_id END), 0) as positiveCount
        from
            ads.ads_pd_pathogen_check_case_info i
            left join dim.dim_date_day d on <include refid="dimDateJoinCondition"/>
            join dim.dim_region_nation drn on i.org_region_code = drn.region_code
        where pathogen_name is not null
            <include refid="detailWhereCriteria"/>
        group by concat('耐', pathogen_name),
        <if test="areaLevel == 1">
            drn.province_name
        </if>
        <if test="areaLevel == 2">
            drn.city_name
        </if>
        <if test="areaLevel == 3">
            drn.district_name
        </if>
    </select>

    <select id="getInfluenzaLabList"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.VirusAntigenResultVO">
        select distinct test_org_id as labId, test_org_name as labName
        from ads.ads_pd_pathogen_check_case_info
        where test_org_id is not null and test_org_id != ''
        and test_org_name is not null and test_org_name != ''
        order by test_org_name
    </select>
</mapper>
