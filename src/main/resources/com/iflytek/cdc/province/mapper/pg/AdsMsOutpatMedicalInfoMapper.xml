<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMsOutpatMedicalInfoMapper">

    <select id="getCommonRecordLogById" resultType="com.iflytek.cdc.province.model.edr.vo.CommonRecordLogVO">
        select
        medical_id as recordId,
        patient_id,
        patient_name,
        living_addr_province_code as province_code,
        living_addr_province as province_name,
        living_addr_city_code as city_code,
        living_addr_city as city_name,
        living_addr_district_code as district_code,
        living_addr_district as district_name
        from ads.ads_ms_outpat_medical_info
        where medical_id = #{id}
    </select>

</mapper>