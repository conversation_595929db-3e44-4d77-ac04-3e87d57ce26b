<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMsProcessInfoMapper">

    <sql id="infectCaseInfoTableJoin">
        <include refid="regionJoinCondition"/>
        <include refid="infectJoinCondition"/>
    </sql>

    <sql id="syndromeCaseInfoTableJoin">
        <include refid="regionJoinCondition"/>
        <include refid="syndromeJoinCondition"/>
    </sql>

    <sql id="regionJoinCondition">
        left join dim.dim_region_nation drn
        on
        <choose>
            <when test="addressType == 'livingAddress'">
                i.living_region_code = drn.region_code
            </when>
            <when test="addressType == 'companyAddress'">
                i.company_region_code = drn.region_code
            </when>
            <otherwise>
                i.org_region_code = drn.region_code
            </otherwise>
        </choose>
    </sql>

    <sql id="infectJoinCondition">
        join dim.dim_infected_info dii
        on dii.id = i.fi_inf_infect_code
    </sql>

    <sql id="syndromeJoinCondition">
        join dim.dim_syndrome_info dii
        on dii.id = i.fi_disease_code
    </sql>

    <sql id="allRegionAndInfectJoinCondition">
        <include refid="infectJoinCondition"/>
        left join dim.dim_region_nation drn
        on i.living_region_code = drn.region_code
        left join dim.dim_region_nation org
        on i.org_region_code = org.region_code
        left join dim.dim_region_nation co
        on i.company_region_code = co.region_code
    </sql>

    <sql id="regionChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and drn.province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and drn.city_code in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and drn.district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and drn.street_code  in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and drn.province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and drn.city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and drn.district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and drn.street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and drn.province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and drn.city_code in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and drn.district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and drn.street_code in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and drn.province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and drn.city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and drn.district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and drn.street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="infectedCaseInfoOrder">
        order by
        <choose>
            <when test="property == 'fiInfOnsetTime'">
                i.fi_inf_onset_day
            </when>
            <when test="property == 'fiIdentifyTime'">
                i.fi_inf_identify_day
            </when>
            <when test="property == 'patientAge'">
                i.fi_inf_patient_age
            </when>
            <when test="property == 'mainDiag'">
                i.lv_main_diag_code
            </when>
            <when test="property == 'livingAddrStd'">
                i.fi_inf_living_addr_detail_std
            </when>
            <when test="property == 'company'">
                i.fi_inf_company
            </when>
            <when test="property == 'visitOrg'">
                i.fi_inf_org_id
            </when>
            <when test="property == 'cityCode'">
                drn.city_code
            </when>
            <when test="property == 'districtCode'">
                drn.district_code
            </when>
            <otherwise>
                i.fi_inf_visit_day
            </otherwise>
        </choose>
        <if test="direction == 'desc' or direction == 'asc'">
            ${direction}
        </if>
    </sql>

    <sql id="infectedAddressMap">
        fi_inf_living_addr_detail as fv_living_addr_detail ,
        fi_inf_living_addr_detail_std as fv_living_addr_detail_std,
--        fi_inf_living_addr_street_longitude as fv_living_addr_street_longitude ,
--        fi_inf_living_addr_street_latitude as  fv_living_addr_street_latitude ,
        fi_inf_company  as fv_company ,
        fi_inf_company_addr_detail_std as fv_company_addr_detail_std ,
--        fi_inf_company_longitude  as fv_company_longitude ,
--        fi_inf_company_latitude  as fv_company_latitude ,
        fi_inf_org_name  as fv_org_name ,
--        fi_inf_org_longitude  as fv_org_longitude ,
--        fi_inf_org_latitude  as fv_org_latitude ,
        fi_inf_living_addr_detail as lv_living_addr_detail ,
        fi_inf_living_addr_detail_std as lv_living_addr_detail_std,
--        lv_living_addr_longitude ,
--        lv_living_addr_latitude ,
         fi_inf_company  as lv_company ,
        fi_inf_company_addr_detail_std as lv_company_addr_detail_std ,
--        lv_company_longitude ,
--        lv_company_latitude ,
        fi_inf_org_name as lv_org_name
--        lv_org_longitude ,
--        lv_org_latitude
    </sql>

    <sql id="syndromeAddressMap">
        fi_syn_living_addr_detail as fv_living_addr_detail ,
        fi_syn_living_addr_detail_std as fv_living_addr_detail_std,
        fi_syn_living_addr_street_longitude as fv_living_addr_street_longitude ,
        fi_syn_living_addr_street_latitude as  fv_living_addr_street_latitude ,
        fi_syn_company  as fv_company ,
        fi_syn_company_addr_detail_std as fv_company_addr_detail_std ,
        fi_syn_company_longitude  as fv_company_longitude ,
        fi_syn_company_latitude  as fv_company_latitude ,
        fi_syn_org_name  as fv_org_name ,
        fi_syn_org_longitude  as fv_org_longitude ,
        fi_syn_org_latitude  as fv_org_latitude ,
        lv_living_addr_detail ,
        lv_living_addr_detail_std,
        lv_living_addr_longitude ,
        lv_living_addr_latitude ,
        lv_company ,
        lv_company_addr_detail_std ,
        lv_company_longitude ,
        lv_company_latitude ,
        lv_org_name ,
        lv_org_longitude ,
        lv_org_latitude
    </sql>

    <sql id="infectedSimpleInfoMap">
--        event_json,
        empi_id as patient_id,
        infect_process_id as  id,
        CASE
            WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN  drn.province_code
            ELSE org.province_code
            END AS provinceCode,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.province_name
            ELSE org.province_name
            END AS province,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.city_code
            ELSE org.city_code
            END AS cityCode,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.city_name
            ELSE org.city_name
            END AS city,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.district_code
            ELSE org.district_code
            END AS funcDistrictCode,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.district_name
            ELSE org.district_name
            END AS funcDistrict,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.street_code
            ELSE org.street_code
            END AS streetCode,
        CASE
           WHEN COALESCE(drn.province_code, '') = COALESCE(org.province_code, '')
                THEN drn.street_name
            ELSE org.street_name
        END AS street,
        fi_inf_org_id as fvOrgId,
        fi_inf_org_name as fvOrgName,
        fi_inf_identify_time as firstIdentifyTime,
        dii.infected_sub_name as diseaseName,
        li_inf_infect_code as diseaseCode,
        li_inf_infect_class as diagnoseStatus,
        fi_inf_identify_time as identifyTime,
        fi_inf_living_addr_detail_std as livingAddrStd,
        fi_inf_company  as company,
        patient_sex_name as patient_sex_name ,
        fi_inf_patient_age as patientAge,
        lo_outcome_status as outComeStatus,
        lo_outcome_time as outComeTime,
        i.person_type  as job,
        fi_inf_onset_time as onsetTime,
        fi_inf_visit_time as visitTime,
        fi_inf_visit_day as visitDay,
        lv_main_diag as mainDiag,
        lv_pathogen_res_type as fi_pathogen_res_type,
        lv_pathogen_res_type,
        fi_inf_visit_time as fiVisitTime,
        fi_inf_org_name   as fiOrgName,
        lv_visit_time     as lvVisitTime,
        lv_org_name       as lv_org_name

    </sql>

    <sql id="syndromeSimpleInfoMap">
        event_json,
        empi_id as patient_id,
        syndrome_process_id as id,
        li_syn_syndrome_out_flag as hasExcluded,
        li_syn_syndrome_severe_flag as severe,
        lo_outcome_status as outComeStatus,
        lo_outcome_time as outComeTime,
        fi_syn_onset_time as onsetTime,
        fi_syn_visit_time  as visitTime,
        fi_syn_visit_day as visitDay,
        fi_syn_syndrome_out_time as excludedTime,
        fi_syn_syndrome_severe_time as severeTime,
        lv_living_addr_detail_std as livingAddrStd,
        fi_syn_company as company,
        patient_sex_name as patientSexName,
        fi_syn_patient_age  as patientAge,
        li_syn_syndrome_name as diseaseName,
        li_syn_syndrome_code as diseaseCode,
        fi_syn_identify_time as identifyTime,
        lv_symptom_attent_json as symptomAttentExtrJson,
        fi_syn_job as job,
        fi_inf_infect_json as infectJson,
        fi_syn_org_name as fvOrgName,
        fi_syn_main_diag as mainDiag,
        fi_pathogen_res_type,
        lv_pathogen_res_type,
        fi_syn_visit_time as fiVisitTime,
        fi_syn_org_name   as fiOrgName,
        lv_visit_time     as lvVisitTime,
        lv_org_name       as lv_org_name,
        lo_dead_reason as deathReason,
        lo_dead_time as deathTime
    </sql>

    <sql id="infectedOrgAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and org.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org.district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and org.street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and org.province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and org.city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and org.district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="infectedLivingAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and drn.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and drn.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and drn.district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and drn.street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and drn.province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and drn.city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and drn.district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="infectedCompanyAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and fi_inf_company_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and fi_inf_company_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and fi_inf_company_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and fi_inf_company_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and fi_inf_company_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and fi_inf_company_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and fi_inf_company_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>


    <sql id="infectedUnifyTableNameAreaChooseCriteria">
        <include refid="infectedLivingAddressCriteria"/>
    </sql>

    <sql id="infectedAreaChooseCriteria">
        <choose>
            <when test="addressType == 'livingAddress'">
                <include refid="infectedLivingAddressCriteria"/>
            </when>
            <otherwise>
                <include refid="infectedOrgAddressCriteria"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="infectedTimeChoose">
        <choose>
            <when test="timeType == 'approveTime'">
                li_inf_check_time
            </when>
            <when test="timeType == 'onsetTime' or timeType == 'visitTime' or timeType == 'firVisitTime'">
                coalesce( fi_inf_onset_time, fi_inf_visit_time )
            </when>
            <when test="timeType == 'deathTime'">
                lo_dead_time
            </when>
            <otherwise>
                fi_inf_identify_time
            </otherwise>
        </choose>
    </sql>

    <sql id="dynamicDayField">
        <choose>
            <when test="timeType == 'approveTime'">
                i.li_inf_check_day
            </when>
            <when test="timeType == 'onsetTime' or timeType == 'visitTime' or timeType == 'firVisitTime'">
                COALESCE( i.fi_inf_onset_day, i.fi_inf_visit_day )
            </when>
            <when test="timeType == 'deathTime'">
                i.lo_dead_day
            </when>
            <otherwise>
                i.fi_inf_identify_day
            </otherwise>
        </choose>
    </sql>

    <sql id="infectedMedCntCommonCriteria">
        <include refid="commonCriteria" />
        <include refid="infectedAreaChooseCriteria" />
    </sql>

    <sql id="commonCriteria">
        <if test="ids != null and ids.size() > 0">
            and infect_process_id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and infect_process_id =  #{id}
        </if>
        <if test="fvOrgId != null and fvOrgId != ''">
            and fi_inf_org_id = #{fvOrgId}
        </if>
        <if test="fvOrgName != null and fvOrgName != ''">
            and fi_inf_org_name like CONCAT('%', #{fvOrgName}, '%')
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            and dii.infected_sub_name = #{diseaseName}
        </if>
    </sql>

    <sql id="infectedCompanyAreaChoose">
        <if test="areaLevel == 1">
            2 as areaLevel,
            fi_inf_company_city_code as areaCode,
            fi_inf_company_city as areaName

        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            3 as areaLevel,
            fi_inf_company_func_district_code as areaCode,
            fi_inf_company_func_district_code as areaName
        </if>
    </sql>

    <sql id="infectedAreaChoose">
        <if test="areaLevel == 1">
            2 as areaLevel,
            drn.city_code  as area_code,
            drn.city_name as areaName
        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            3 as areaLevel,
            drn.district_code as area_code,
            drn.district_name  as areaName
        </if>
    </sql>

    <sql id="syndromeLivingAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and fi_syn_living_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and fi_syn_living_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and fi_syn_living_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and fi_syn_living_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and fi_syn_living_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and fi_syn_living_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and fi_syn_living_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="syndromeCompanyAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and fi_syn_company_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and fi_syn_company_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and fi_syn_company_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and fi_syn_company_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and fi_syn_company_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and fi_syn_company_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and fi_syn_company_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="syndromeOrgAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and i.fi_syn_org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and i.fi_syn_org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and i.fi_syn_org_addr_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and i.fi_syn_org_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and i.fi_syn_org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and i.fi_syn_org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and i.fi_syn_org_addr_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and i.fi_syn_org_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="syndromeAreaChooseCriteria">
        <choose>
            <when test="addressType == 'livingAddress'">
                <include refid="syndromeLivingAddressCriteria"/>
            </when>
            <otherwise>
                <include refid="syndromeOrgAddressCriteria"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="syndromeTimeChoose">
        <choose>
            <when test="timeType == 'approveTime'">
                 fi_syn_check_time
            </when>
            <when test="timeType == 'identifyTime'">
                 fi_syn_identify_time
            </when>
            <otherwise>
                 fi_syn_visit_day
            </otherwise>
        </choose>
    </sql>

    <sql id="syndromeLivingAreaChoose">
        <if test="areaLevel == 1">
            2 as areaLevel,
            fi_syn_living_addr_city_code as areaCode,
            fi_syn_living_addr_city as areaName

        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            3 as areaLevel,
            fi_syn_living_addr_func_district_code as areaCode,
            fi_syn_living_addr_func_district as areaName
        </if>
    </sql>

    <sql id="syndromeCompanyAreaChoose">
        <if test="areaLevel == 1">
            2 as areaLevel,
            fi_syn_company_city_code as areaCode,
            fi_syn_company_city as areaName

        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            3 as areaLevel,
            fi_syn_company_func_district_code as areaCode,
            fi_syn_company_func_district as areaName
        </if>
    </sql>

    <sql id="syndromeOrgAreaChoose">
        <if test="areaLevel == 1">
            2 as areaLevel,
            fi_syn_org_addr_city_code as areaCode,
            fi_syn_org_addr_city as areaName

        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            3 as areaLevel,
            fi_syn_org_addr_func_district_code as areaCode,
            fi_syn_org_addr_func_district as areaName
        </if>
    </sql>

    <sql id="syndromeAreaChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <include refid="syndromeLivingAreaChoose"/>
            </when>
            <otherwise>
                <include refid="syndromeOrgAreaChoose"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="syndromeMedCntCriteria">
        <if test="ids != null and ids.size() > 0">
            and syndrome_process_id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and syndrome_process_id = #{id}
        </if>
        <if test="fvOrgId != null and fvOrgId != ''">
            and fi_syn_org_id = #{fvOrgId}
        </if>
        <if test="fvOrgName != null and fvOrgName != ''">
            and fi_syn_org_name = #{fvOrgName}
        </if>
         <include refid="syndromeAreaChooseCriteria"/>
    </sql>

    <sql id="infectedOrder">
        order by
        <choose>
            <when test="property == 'fiInfOnsetTime'">
                fi_inf_onset_time
            </when>
            <when test="property == 'fiIdentifyTime'">
                 fi_inf_identify_time
            </when>
            <when test="property == 'patientAge'">
                 fi_inf_patient_age
            </when>
            <when test="property == 'mainDiag'">
                lv_main_diag_code
            </when>
            <when test="property == 'livingAddrStd'">
                fi_inf_living_addr_detail_std
            </when>
            <when test="property == 'company'">
                fi_inf_company
            </when>
            <when test="property == 'visitOrg'">
                fi_inf_org_id
            </when>
            <when test="property == 'cityCode'">
                <choose>
                    <when test="addressType == 'livingAddress'">
                         drn.city_code
                    </when>
                    <otherwise>
                        org.city_code
                    </otherwise>
                </choose>
            </when>
            <when test="property == 'districtCode'">
                <choose>
                    <when test="addressType == 'livingAddress'">
                        drn.district_code
                    </when>
                    <otherwise>
                        org.district_code
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                fi_inf_visit_time
            </otherwise>
        </choose>
        <if test="direction == 'desc' or direction == 'asc'">
            ${direction}
        </if>
    </sql>

    <sql id="syndromeOrder">
        order by
        <choose>
            <when test="property == 'firstIdentifyTime'">
                 fi_syn_identify_time
            </when>
            <when test="property == 'onsetTime'">
                fi_syn_onset_time
            </when>
            <when test="property == 'fiIdentifyTime'">
                 fi_syn_identify_time
            </when>
            <when test="property == 'patientAge'">
                 fi_syn_patient_age
            </when>
            <when test="property == 'mainDiag'">
                 fi_syn_main_diag_std_code
            </when>
            <when test="property == 'livingAddrStd'">
                lv_living_addr_detail_std
            </when>
            <when test="property == 'company'">
                fi_syn_company
            </when>
            <when test="property == 'visitOrg'">
                fi_syn_org_id
            </when>
            <when test="property == 'cityCode'">
                <choose>
                    <when test="addressType == 'livingAddress'">
                         fi_syn_living_addr_city_code
                    </when>
                    <otherwise>
                        fi_syn_org_addr_city_code
                    </otherwise>
                </choose>
            </when>
            <when test="property == 'districtCode'">
                <choose>
                    <when test="addressType == 'livingAddress'">
                         fi_syn_living_addr_func_district_code
                    </when>
                    <otherwise>
                         fi_syn_org_addr_func_district_code
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                fi_syn_visit_time
            </otherwise>
        </choose>
        <if test="direction == 'desc' or direction == 'asc'">
                ${direction}
        </if>
    </sql>

    <sql id="infectedProcessOtherCriteria">
        <!-- 地址选择 -->
        <if test="livingProvinceCode != null and livingProvinceCode != ''">
            and drn.province_code = #{livingProvinceCode}
        </if>
        <if test="livingCityCode != null and livingCityCode != ''">
            and drn.city_code = #{livingCityCode}
        </if>
        <if test="livingDistrictCode != null and livingDistrictCode != ''">
            and drn.district_code = #{livingDistrictCode}
        </if>
        <if test="companyProvinceCode != null and companyProvinceCode != ''">
            and co.province_code = #{companyProvinceCode}
        </if>
        <if test="companyCityCode != null and companyCityCode != ''">
            and co.city_code = #{companyCityCode}
        </if>
        <if test="companyDistrictCode != null and companyDistrictCode != ''">
            and co.district_code = #{companyDistrictCode}
        </if>
        <!-- 年龄筛选 -->
        <if test="ageMin != null">
            and
            case
                when fi_inf_patient_age_unit = '岁' or fi_inf_patient_age_unit is null then fi_inf_patient_age
                when fi_inf_patient_age_unit = '月' then fi_inf_patient_age / 12.0
                when fi_inf_patient_age_unit = '天' then fi_inf_patient_age / 365.0
                else -1
            end >= #{ageMin}
        </if>
        <if test="ageMax != null">
            and
            case
                when fi_inf_patient_age_unit = '岁' or fi_inf_patient_age_unit is null then fi_inf_patient_age
                when fi_inf_patient_age_unit = '月' then fi_inf_patient_age / 12.0
                when fi_inf_patient_age_unit = '天' then fi_inf_patient_age / 365.0
                else 9999
            end &lt;= #{ageMax}
        </if>
        <!-- 病原监测 -->
        <if test="firstPathogenResult != null and firstPathogenResult != ''">
            and lv_pathogen_res_type = #{firstPathogenResult}
        </if>
        <if test="lastPathogenResult != null and lastPathogenResult != ''">
            and lv_pathogen_res_type = #{lastPathogenResult}
        </if>
    </sql>

    <sql id="syndromeProcessOtherCriteria">
        <!-- 地址选择 -->
        <if test="livingProvinceCode != null and livingProvinceCode != ''">
            and i.fi_syn_living_addr_province_code = #{livingProvinceCode}
        </if>
        <if test="livingCityCode != null and livingCityCode != ''">
            and i.fi_syn_living_addr_city_code = #{livingCityCode}
        </if>
        <if test="livingDistrictCode != null and livingDistrictCode != ''">
            and i.fi_syn_living_addr_district_code = #{livingDistrictCode}
        </if>
        <if test="companyProvinceCode != null and companyProvinceCode != ''">
            and i.fi_syn_company_province_code = #{companyProvinceCode}
        </if>
        <if test="companyCityCode != null and companyCityCode != ''">
            and i.fi_syn_company_city_code = #{companyCityCode}
        </if>
        <if test="companyDistrictCode != null and companyDistrictCode != ''">
            and i.fi_syn_company_district_code = #{companyDistrictCode}
        </if>
        <!-- 年龄筛选 -->
        <if test="ageMin != null">
            and
            case
                when i.fi_syn_patient_age_unit = '岁' or i.fi_syn_patient_age_unit is null then i.fi_syn_patient_age
                when i.fi_syn_patient_age_unit = '月' then i.fi_syn_patient_age / 12.0
                when i.fi_syn_patient_age_unit = '天' then i.fi_syn_patient_age / 365.0
                else -1
            end >= #{ageMin}
        </if>
        <if test="ageMax != null">
            and
            case
                when i.fi_syn_patient_age_unit = '岁' or i.fi_syn_patient_age_unit is null then i.fi_syn_patient_age
                when i.fi_syn_patient_age_unit = '月' then i.fi_syn_patient_age / 12.0
                when i.fi_syn_patient_age_unit = '天' then i.fi_syn_patient_age / 365.0
                else 9999
            end &lt;= #{ageMax}
        </if>
        <!-- 病原监测 -->
        <if test="firstPathogenResult != null and firstPathogenResult != ''">
            and i.fi_pathogen_res_type = #{firstPathogenResult}
        </if>
        <if test="lastPathogenResult != null and lastPathogenResult != ''">
            and i.lv_pathogen_res_type = #{lastPathogenResult}
        </if>
    </sql>

    <sql id="infectedProcessCriteria">
        <if test="fvOrgId != null and fvOrgId != ''">
            and fi_inf_org_id = #{fvOrgId}
        </if>
        <if test="fvOrgName != null and fvOrgName != ''">
            and fi_inf_org_name like CONCAT('%', #{fvOrgName}, '%')
        </if>
        <if test="queryKey != null and queryKey !=''">
            and (infect_process_id like CONCAT('%', #{queryKey}, '%') or fi_inf_org_name like CONCAT('%', #{queryKey}, '%'))
        </if>
        <if test="job != null and job != ''">
            and i.person_type = #{job}
        </if>
        <if test="company != null and company != ''">
            and fi_inf_company like CONCAT('%', #{company}, '%')
        </if>
        <if test="patientSexName != null and patientSexName != ''">
            and patient_sex_name = #{patientSexName}
        </if>
        <if test="visitStartDate != null">
            and fi_inf_visit_day >= #{visitStartDate}
        </if>
        <if test="visitEndDate != null">
            and fi_inf_visit_day &lt;= #{visitEndDate}
        </if>
        <if test="mainDiag != null and mainDiag != ''">
            and lv_main_diag like CONCAT('%', #{mainDiag}, '%')
        </if>
        <if test="diagnoseStatus != null and diagnoseStatus != ''">
            and li_inf_infect_class = #{diagnoseStatus}
        </if>
        <if test="onsetStartDate != null">
            and fi_inf_onset_time >= #{onsetStartDate}
        </if>
        <if test="onsetEndDate != null">
            and fi_inf_onset_time &lt;= #{onsetEndDate}
        </if>
    </sql>

    <sql id="syndromeProcessCriteria">
        <if test="fvOrgId != null and fvOrgId != ''">
            and i.fi_syn_org_id = #{fvOrgId}
        </if>
        <if test="fvOrgName != null and fvOrgName != ''">
            and i.fi_syn_org_name like CONCAT('%', #{fvOrgName}, '%')
        </if>
        <if test="job != null and job != ''">
            and i.fi_syn_job = #{job}
        </if>
        <if test="company != null and company != ''">
            and i.fi_syn_company = #{company}
        </if>
        <if test="patientSexName != null and patientSexName != ''">
            and i.patient_sex_name = #{patientSexName}
        </if>
        <if test="visitStartDate != null">
            and i.fi_syn_visit_day >= #{visitStartDate}
        </if>
        <if test="visitEndDate != null">
            and i.fi_syn_visit_day &lt;= #{visitEndDate}
        </if>
        <if test="onsetStartDate != null">
            and i.fi_syn_onset_time >= #{onsetStartDate}
        </if>
        <if test="onsetEndDate != null">
            and i.fi_syn_onset_time &lt;= #{onsetEndDate}
        </if>
        <if test="mainDiag != null and mainDiag != ''">
            and i.fi_syn_main_diag = #{mainDiag}
        </if>
        <if test="hasExcluded != null and hasExcluded != ''">
            and i.li_syn_syndrome_out_flag = #{hasExcluded}
        </if>
        <if test="isSevere != null and isSevere != ''">
            and i.li_syn_syndrome_severe_flag = #{isSevere}
        </if>
        <if test="outComeStatus != null and outComeStatus != ''">
            and i.lo_outcome_status = #{outComeStatus}
        </if>
        <if test="queryKey != null and queryKey !=''">
            and (i.syndrome_process_id like CONCAT('%', #{queryKey}, '%') or i.fi_syn_org_name like CONCAT('%', #{queryKey}, '%'))
        </if>
    </sql>


    <select id="listInfectedMedCntIndicator" resultType="com.iflytek.cdc.province.model.vo.MedCntIndicatorVO">
        select
            <include refid="dynamicDayField" />  as stat_date,
            count(*) as medCaseCnt
        from ads.ads_ms_infect_process_case_info i
        <include refid="allRegionAndInfectJoinCondition"/>
        where 1=1
        <include refid="infectedMedCntCommonCriteria"/>
        <!-- 病例过滤 -->
        <include refid="infectedOrgAddressCriteria"/>
        <include refid="infectedProcessCriteria"/>
        <include refid="infectedProcessOtherCriteria"/>
        <if test="startDate != null and endDate != null">
            and <include refid="infectedTimeChoose" /> between #{startDate} and #{endDate}
        </if>
        group by stat_date order by stat_date
    </select>

    <select id="listInfectedOutcomeIndicator" resultType="com.iflytek.cdc.province.model.vo.MedCntIndicatorVO">
        select
            lo_outcome_day as stat_date,
            sum(case when lo_outcome_status = '治愈' then 1 else 0 end) as recoverCnt ,
            sum(case when lo_outcome_status = '死亡' then 1 else 0 end) as deathCnt,
            sum(case when end_time is null then 1 else 0 end) as existingCaseCnt ,
            sum(case when lo_dead_reason = '因该病死亡' then 1 else 0 end) as diseaseDeathCnt
        from ads.ads_ms_infect_process_case_info i
        <include refid="allRegionAndInfectJoinCondition"/>
        where 1=1
        <include refid="infectedMedCntCommonCriteria"/>
        <!-- 病例过滤 -->
        <include refid="infectedOrgAddressCriteria"/>
        <include refid="infectedProcessCriteria"/>
        <include refid="infectedProcessOtherCriteria"/>
        <if test="startDate != null and endDate != null">
            and lo_outcome_time between #{startDate} and #{endDate}
        </if>
        and lo_outcome_time is not null
        group by stat_date order by stat_date
    </select>

    <select id="listInfectedAreaMedCntIndicator" resultType="com.iflytek.cdc.province.model.vo.AreaMedCntIndicatorVO">
        select
            <include refid="dynamicDayField" />  as stat_date,
            count(*) as medCaseCnt,
            <include refid="infectedAreaChoose" />
        from ads.ads_ms_infect_process_case_info i
        <include refid="infectCaseInfoTableJoin"/>
        where 1=1
        <include refid="commonCriteria"/>
        <include refid="infectedUnifyTableNameAreaChooseCriteria"/>
        <if test="startDate != null and endDate != null">
            and <include refid="infectedTimeChoose" /> between #{startDate} and #{endDate}
        </if>
        group by stat_date, areaLevel, area_code, areaName order by stat_date
    </select>

    <select id="listInfectedAreaOutcomeIndicator" resultType="com.iflytek.cdc.province.model.vo.AreaMedCntIndicatorVO">
        select
            lo_outcome_day as stat_date,
            sum(case when lo_outcome_status = '治愈' then 1 else 0 end) as recoverCnt ,
            sum(case when lo_outcome_status = '死亡' then 1 else 0 end) as deathCnt,
            sum(case when end_time is null then 1 else 0 end) as existingCaseCnt ,
            sum(case when lo_dead_reason = '因该病死亡' then 1 else 0 end) as diseaseDeathCnt,
            <include refid="infectedAreaChoose"/>
        from ads.ads_ms_infect_process_case_info i
        <include refid="infectCaseInfoTableJoin"/>
        where 1=1
        and lo_outcome_time is not null
        <include refid="commonCriteria"/>
        <include refid="infectedUnifyTableNameAreaChooseCriteria"/>
        <if test="startDate != null and endDate != null">
            and lo_outcome_time between #{startDate} and #{endDate}
        </if>
        group by stat_date, areaLevel, areaCode, areaName order by stat_date
    </select>

    <select id="listSyndromeMedCntIndicator" resultType="com.iflytek.cdc.province.model.vo.MedCntIndicatorVO">
        select
            CAST(<include refid="syndromeTimeChoose"/> AS DATE) as stat_date,
            sum(case when lo_outcome_status = '治愈' then 1 else 0 end) as recoverCnt ,
            sum(case when lo_outcome_status = '死亡' then 1 else 0 end) as deathCnt,
            sum(case when end_flag = '0' then 1 else 0 end) as existingCaseCnt ,
            sum(case when lo_dead_reason = '因该病死亡' then 1 else 0 end) as diseaseDeathCnt,
            count(syndrome_process_id) as medCaseCnt
        from ads.ads_ms_syndrome_process_info i
        where 1= 1
        <include refid="syndromeMedCntCriteria"/>
        <!-- 病例过滤 -->
        <include refid="syndromeOrgAddressCriteria"/>
        <include refid="syndromeProcessCriteria"/>
        <include refid="syndromeProcessOtherCriteria"/>
        <if test="startDate != null and endDate != null">
            and <include refid="syndromeTimeChoose"/> between #{startDate} and #{endDate}
        </if>
        and <include refid="syndromeTimeChoose"/> is not null
        group by stat_date order by stat_date
    </select>

    <select id="listSyndromeOutcomeIndicator" resultType="com.iflytek.cdc.province.model.vo.MedCntIndicatorVO">
        select
            CAST(lo_outcome_time AS DATE) as stat_date,
            sum(case when lo_outcome_status = '治愈' then 1 else 0 end) as recoverCnt ,
            sum(case when lo_outcome_status = '死亡' then 1 else 0 end) as deathCnt,
            sum(case when end_flag = '0' then 1 else 0 end) as existingCaseCnt ,
            sum(case when lo_dead_reason = '因该病死亡' then 1 else 0 end) as diseaseDeathCnt
        from ads.ads_ms_syndrome_process_info i
        where lo_outcome_time is not null
        <include refid="syndromeMedCntCriteria"/>
        <!-- 病例过滤 -->
        <include refid="syndromeOrgAddressCriteria"/>
        <include refid="syndromeProcessCriteria"/>
        <include refid="syndromeProcessOtherCriteria"/>
        <if test="startDate != null and endDate != null">
            and lo_outcome_time between #{startDate} and #{endDate}
        </if>
        group by stat_date order by stat_date
    </select>

    <select id="listSyndromeAreaMedCntIndicator" resultType="com.iflytek.cdc.province.model.vo.AreaMedCntIndicatorVO">
        select
            CAST(<include refid="syndromeTimeChoose"/> AS DATE) as stat_date,
            count(syndrome_process_id) as medCaseCnt,
            <include refid="syndromeAreaChoose"/>
        from ads.ads_ms_syndrome_process_info i
        where 1= 1
        <include refid="syndromeMedCntCriteria"/>
        <if test="startDate != null and endDate != null">
            and <include refid="syndromeTimeChoose"/> between #{startDate} and #{endDate}
        </if>
        group by stat_date, areaLevel, areaCode, areaName
        order by stat_date
    </select>

    <select id="listSyndromeAreaOutcomeIndicator" resultType="com.iflytek.cdc.province.model.vo.AreaMedCntIndicatorVO">
        select
            CAST(lo_outcome_time AS DATE) as stat_date,
            sum(case when lo_outcome_status = '治愈' then 1 else 0 end) as recoverCnt ,
            sum(case when lo_outcome_status = '死亡' then 1 else 0 end) as deathCnt,
            sum(case when end_flag = '0' then 1 else 0 end) as existingCaseCnt ,
            sum(case when lo_dead_reason = '因该病死亡' then 1 else 0 end) as diseaseDeathCnt,
            <include refid="syndromeAreaChoose"/>
        from ads.ads_ms_syndrome_process_info i
        where 1= 1
        <include refid="syndromeMedCntCriteria"/>
        <if test="startDate != null and endDate != null">
            and lo_outcome_time between #{startDate} and #{endDate}
        </if>
        group by stat_date, areaLevel, areaCode, areaName
        order by stat_date
    </select>

    <select id="getInfectedMedicalList" resultType="com.iflytek.cdc.edr.vo.dm.ProcessRecordVO">
        select
            i.infect_process_id as id,
            i.patient_name,
            i.fi_inf_patient_age as patient_age,
            i.patient_sex_name,
            drn.district_name as lvOrgAddrFuncDistrict,
            dii.id as li_disease_code,
            dii.infected_sub_name as li_disease_name,
            i.fi_inf_identify_time as fiIdentifyTime,
            i.lv_org_name,
            i.lv_visit_time,
            i.fi_inf_dept_name as fiSyndeptName,
            i.li_inf_infect_class as li_infect_class,
            i.lo_outcome_status,
            fi_inf_org_name as fv_org_name,
            fi_inf_visit_time as fv_visit_time,
            <!-- 传染病病例类型-->
            '2' as diseaseType,
            drn.province_code as provinceCode,
            drn.province_name as provinceName,
            drn.city_code as cityCode,
            drn.city_name as cityName,
            drn.district_code as districtCode,
            drn.district_name as districtName
        from ads.ads_ms_infect_process_case_info i
        join dim.dim_region_nation drn
        on i.org_region_code = drn.region_code
        join dim.dim_infected_info dii
        on dii.id = i.li_inf_infect_code
        where 1=1
        <if test="visitTimeType == 'firstVisit'">
            <if test="startDate != null">
                and i.fi_inf_visit_day >= #{startDate}
            </if>
            <if test="endDate != null">
                and i.fi_inf_visit_day &lt;= #{endDate}
            </if>
        </if>
        <if test="visitTimeType == 'latestVisit'">
            <if test="startDate != null">
                and i.lv_visit_day >= #{startDate}
            </if>
            <if test="endDate != null">
                and i.lv_visit_day &lt;= #{endDate}
            </if>
        </if>
        <if test="visitTimeType == 'onset'">
            <if test="startDate != null">
                and i.fi_inf_onset_day >= CAST(#{startDate} AS DATE)
            </if>
            <if test="endDate != null">
                and i.fi_inf_onset_day &lt;= CAST(#{endDate} AS DATE)
            </if>
        </if>
        <if test="visitTimeType == 'diagnose'">
            <if test="startDate != null">
                and i.lv_visit_day >= #{startDate}
            </if>
            <if test="endDate != null">
                and i.lv_visit_day &lt;= #{endDate}
            </if>
        </if>
        <if test="visitTimeType == 'monitorReport'">
            <if test="startDate != null">
                and i.fi_inf_identify_day >= #{startDate}
            </if>
            <if test="endDate != null">
                and i.fi_inf_identify_day &lt;= #{endDate}
            </if>
        </if>
        <if test="fiSyndeptName != null and fiSyndeptName != ''">
            AND i.fi_inf_dept_name LIKE CONCAT('%', #{fiSyndeptName}, '%')
        </if>
       <include refid="regionChoose"/>
        <if test="infectCodeList != null and infectCodeList.size()>0">
            and i.li_inf_infect_code in
            <foreach collection="infectCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="diagnoseStatus != null and diagnoseStatus != ''">
            and i.li_inf_infect_class = #{diagnoseStatus}
        </if>
        <if test="outcome != null and outcome != ''">
            and i.lo_outcome_status = #{outcome}
        </if>
        <if test="personInfo != null and personInfo != ''">
            and (i.infect_process_id like CONCAT('%', #{personInfo}, '%') or i.patient_name like CONCAT('%', #{personInfo}, '%'))
        </if>
        <if test="keyword != null and keyword != ''">
            and ( i.infect_process_id like  CONCAT('%', #{keyword}, '%') or dii.infected_sub_name like  CONCAT('%', #{keyword}, '%') )
        </if>
        <if test="idList != null and idList.size()>0">
            and i.infect_process_id in
            <foreach collection="idList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="idList != null and idList.size()>0">
            and infect_process_id in
            <foreach collection="idList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryKey != null and queryKey != ''">
            and ( i.infect_process_id like  CONCAT('%', #{queryKey}, '%')
            or dii.infected_sub_name like  CONCAT('%', #{queryKey}, '%')
            or i.patient_name like CONCAT('%', #{queryKey}, '%'))
        </if>
        <if test="fvOrgId != null and fvOrgId != ''">
            and i.fi_inf_org_id = #{fvOrgId}
        </if>
        <include refid="infectedCaseInfoOrder"/>
        <if test="limitNum != null and limitNum >0">
            limit #{limitNum}
        </if>
    </select>

    <select id="getSyndromeProcessListBy" resultType="com.iflytek.cdc.edr.vo.dm.ProcessRecordVO">
        select
            syndrome_process_id as id,
            fi_syn_patient_age as patient_age,
            patient_name,
            patient_sex_name,
            li_syn_syndrome_name,
            fi_syn_dept_name  as fiSyndeptName,
            lv_dept_name  as lvDeptName,
            fi_syn_syndrome_name as syndromeName,
            fi_syn_visit_time as fvVisitTime,
            lv_visit_time,
            case when li_syn_syndrome_severe_flag = '1' then '重症'
                 when li_syn_syndrome_severe_flag = '0' then '非重症' end as severityLevel,
            fi_syn_org_name as fvOrgName,
            fi_syn_org_addr_func_district as lvOrgAddrFuncDistrict,
            fi_syn_identify_time as fi_syn_syndrome_identify_time,
            lv_medical_id,
            fi_syn_org_addr_province_code as provinceCode,
            fi_syn_org_addr_province as provinceName,
            fi_syn_org_addr_city_code as cityCode,
            fi_syn_org_addr_city as cityName,
            fi_syn_org_addr_func_district_code as districtCode,
            fi_syn_org_addr_func_district as districtName,
            <!-- 审核存储信息-->
            '1' as diseaseType,
            li_syn_syndrome_code as li_disease_code,
            li_syn_syndrome_name as li_disease_name,
            fi_syn_identify_time as fiIdentifyTime
        from ads.ads_ms_syndrome_process_info
        where 1=1
        <if test="diseaseCodeList != null and diseaseCodeList.size() &gt; 0">
            and fi_syn_syndrome_code in
            <foreach collection="diseaseCodeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and syndrome_process_id = #{id}
        </if>
        <if test="idList != null and idList.size()>0">
            and syndrome_process_id in
            <foreach collection="idList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="idList != null and idList.size()>0">
            and syndrome_process_id in
            <foreach collection="idList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="symptomName != null and symptomName != ''">
            and symptom_attent_extr_json::text LIKE CONCAT('%', #{symptomName}, '%')
        </if>
        <if test="confirmDisease != null and confirmDisease != ''">
            and confirm_disease like CONCAT('%',  #{confirmDisease}, '%')
        </if>
        <if test="personInfo != null and personInfo != ''">
            and (patient_name like CONCAT('%', #{personInfo}, '%') or
                 syndrome_process_id like CONCAT('%', #{personInfo}, '%'))
        </if>
        <if test="severityLevel == '重症'">
            and li_syn_syndrome_severe_flag = '1'
        </if>
        <if test="severityLevel == '非重症'">
            and li_syn_syndrome_severe_flag = '0'
        </if>
        <if test="severityLevel == '死亡'">
            and lo_outcome_status like CONCAT('%', #{severityLevel}, '%')
        </if>
        <if test="visitTimeType == 'firstVisit'">
            <if test="startDate != null">
                and fi_syn_visit_time >= #{startDate}
            </if>
            <if test="endDate != null">
                and fi_syn_visit_time &lt;= #{endDate}
            </if>
        </if>
        <if test="visitTimeType == 'latestVisit'">
            <if test="startDate != null">
                and lv_visit_time >= #{startDate}
            </if>
            <if test="endDate != null">
                and lv_visit_time &lt;= #{endDate}
            </if>
        </if>
        <if test="visitTimeType == 'monitorReport'">
            <if test="startDate != null">
                and fi_syn_identify_time >= #{startDate}
            </if>
            <if test="endDate != null">
                and fi_syn_identify_time &lt;= #{endDate}
            </if>
        </if>
        <if test="fvOrgId != null and fvOrgId != ''">
            and fi_syn_org_id = #{fvOrgId}
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and fi_syn_org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and fi_syn_org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and fi_syn_org_addr_func_district_code = #{districtCode}
        </if>
        <if test="fiSyndeptName != null and fiSyndeptName != ''">
            and fi_syn_dept_name = #{fiSyndeptName}
        </if>
        <if test="keyword != null and keyword != ''">
            and ( syndrome_process_id like  CONCAT('%', #{keyword}, '%') or li_syn_syndrome_name like  CONCAT('%', #{keyword}, '%') )
        </if>
        <if test="queryKey != null and queryKey != ''">
            and ( syndrome_process_id like  CONCAT('%', #{queryKey}, '%')
            or li_syn_syndrome_name like  CONCAT('%', #{queryKey}, '%')
            or patient_name like CONCAT('%', #{queryKey}, '%'))
        </if>
        <include refid="syndromeOrder"/>
        <if test="limitNum != null and limitNum >0">
            limit #{limitNum}
        </if>
    </select>

    <select id="listInfectedAddressByIds" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO">
        select
            infect_process_id as id,
            <include refid="infectedAddressMap"/>
        from ads.ads_ms_infect_process_case_info i
        <include refid="allRegionAndInfectJoinCondition"/>
        where infect_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
        <!-- 病例过滤 -->
        <include refid="infectedOrgAddressCriteria"/>
        <include refid="infectedProcessCriteria"/>
        <include refid="infectedProcessOtherCriteria"/>
    </select>

    <select id="listSyndromeAddressByIds" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO">
        select
            syndrome_process_id as id,
            <include refid="syndromeAddressMap"/>
        from ads.ads_ms_syndrome_process_info i
        where syndrome_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
        <!-- 病例过滤 -->
        <include refid="syndromeOrgAddressCriteria"/>
        <include refid="syndromeProcessCriteria"/>
        <include refid="syndromeProcessOtherCriteria"/>
    </select>

    <select id="listInfectedSimpleInfo" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO">
        select
            <include refid="infectedSimpleInfoMap"/>,
            patient_name AS patientName,
            patient_identity_no AS idCard
        from ads.ads_ms_infect_process_case_info i
        <include refid="allRegionAndInfectJoinCondition"/>
        where 1=1
        <if test="ids != null and ids.size() > 0">
            and infect_process_id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
         <if test="id != null and id != ''">
            and infect_process_id = #{id}
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            and dii.infected_sub_name = #{diseaseName}
        </if>
        <include refid="infectedOrgAddressCriteria"/>
        <include refid="infectedProcessCriteria"/>
        <include refid="infectedProcessOtherCriteria"/>
        <include refid="infectedOrder"/>
    </select>

    <select id="loadInfectedSimpleInfo" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoDetailVO">
        select
            <include refid="infectedSimpleInfoMap"/>,
            patient_name AS patientName,
            patient_identity_no AS idCard,
            patient_birth_day AS birthDate,
            patient_sex_name AS genderName,
            fi_living_addr_detail  AS livingAddrDetail,
            drn.province_code AS livingAddrProvinceCode,
            drn.province_name AS livingAddrProvinceName,

            drn.city_code AS livingAddrCityCode,
            drn.city_name AS livingAddrCityName,

            drn.district_code AS livingAddrDistrictCode,
            drn.district_name AS livingAddrDistrictName,

            drn.street_code AS livingAddrStreetCode,
            drn.street_name AS livingAddrStreetName
--            lv_living_addr_street_longitude AS livingAddrLongitude,
--            lv_living_addr_street_latitude AS livingAddrLatitude
        from ads.ads_ms_infect_process_case_info i
        join dim.dim_region_nation drn
        on i.living_region_code = drn.region_code
        where infect_process_id = #{id}
    </select>


    <select id="listSyndromeSimpleInfo" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO">
        select
            <include refid="syndromeSimpleInfoMap"/>,
            patient_name AS patientName,
            patient_identity_no AS idCard
        from ads.ads_ms_syndrome_process_info i
        where 1=1
        <if test="ids != null and ids.size() > 0">
            and syndrome_process_id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and syndrome_process_id = #{id}
        </if>
        <if test="symptomName != null and symptomName != ''">
            and symptom_attent_extr_json AS CHAR like CONCAT('%',  #{symptomName}, '%')
        </if>
        <if test="startDate != null and endDate != null">
            and <include refid="syndromeTimeChoose"/> between #{startDate} and #{endDate}
        </if>
        <include refid="syndromeOrgAddressCriteria"/>
        <include refid="syndromeProcessCriteria"/>
        <include refid="syndromeProcessOtherCriteria"/>
        <include refid="syndromeOrder"/>

    </select>

    <select id="loadSyndromeSimpleInfo" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoDetailVO">
        select
            <include refid="syndromeSimpleInfoMap"/> ,
            patient_name AS patientName,
            patient_identity_no AS idCard,
            patient_birth_day AS birthDate,
            patient_sex_name AS genderName,
            lv_living_addr_detail  AS livingAddrDetail,
            lv_living_addr_province_code AS livingAddrProvinceCode,
            lv_living_addr_province AS livingAddrProvinceName,

            lv_living_addr_city_code AS livingAddrCityCode,
            lv_living_addr_city AS livingAddrCityName,

            lv_living_addr_func_district_code AS livingAddrDistrictCode,
            lv_living_addr_func_district AS livingAddrDistrictName,

            lv_living_addr_street_code AS livingAddrStreetCode,
            lv_living_addr_street AS livingAddrStreet,


            lv_living_addr_street_longitude AS livingAddrLongitude,
            lv_living_addr_street_latitude AS livingAddrLatitude
        from ads.ads_ms_syndrome_process_info
        where syndrome_process_id = #{id}
    </select>

    <select id="countInfected" resultType="int">
        select
         count(*)
        from ads.ads_ms_infect_process_case_info i
        where infect_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="countSyndrome" resultType="int">
        select
         count(*)
        from ads.ads_ms_syndrome_process_info
        where syndrome_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="listSynPatientInfoByIds" resultType="com.iflytek.cdc.province.model.vo.MsPatientInfoVO">
        select
            empi_id as patient_id,
            syndrome_process_id as id,
            patient_name as patientName,
            patient_sex_name as patientSexName,
            fi_syn_patient_age as patientAge,
            fi_syn_job as job,
            li_syn_syndrome_severe_flag as li_severe_flag,
            lo_outcome_status,
            fi_syn_event_id as eventId,
            event_json,
            li_syn_syndrome_subgroup as li_subgroup
        from ads.ads_ms_syndrome_process_info i
        where syndrome_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
        <!-- 病例过滤 -->
        <include refid="syndromeOrgAddressCriteria"/>
        <include refid="syndromeProcessCriteria"/>
        <include refid="syndromeProcessOtherCriteria"/>
    </select>

    <select id="listInfPatientInfoByIds" resultType="com.iflytek.cdc.province.model.vo.MsPatientInfoVO">
        select
            infect_process_id as id,
            patient_name as patientName,
            patient_sex_name as patientSexName,
            fi_inf_patient_age as patientAge,
            empi_id as patientId,
--            fi_inf_event_id as eventId,
--            event_json,
            fi_inf_job as job
        from ads.ads_ms_infect_process_case_info i
        where infect_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
        <!-- 病例过滤 -->
        <include refid="infectedOrgAddressCriteria"/>
        <include refid="infectedProcessCriteria"/>
        <include refid="infectedProcessOtherCriteria"/>
    </select>

    <select id="listSyndromeProcessDetailInfo"
            resultType="com.iflytek.cdc.province.model.vo.MsProcessMonitorInfoVO">
        select
        i.syndrome_process_id         as id,
        i.lv_visit_day                as visitDay,
        p.focus_place_type            as focusPlace,
        i.symptom_attent_extr_json    as monitorSymptomJson
        from ads.ads_ms_syndrome_process_info i
        left join ads.ads_ms_syndrome_process_focus_place_info p
        on i.syndrome_process_id = p.syndrome_process_id
        where i.syndrome_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
        <!-- 病例过滤 -->
        <include refid="syndromeOrgAddressCriteria"/>
        <include refid="syndromeProcessCriteria"/>
        <include refid="syndromeProcessOtherCriteria"/>
    </select>

    <!--  根据人查询对应病例  -->
    <sql id="chooseTable">
        <if test="diseaseType == 'infected'">
            ads.ads_ms_infect_process_case_info
        </if>
        <if test="diseaseType == 'syndrome'">
            ads.ads_ms_syndrome_process_info
        </if>
    </sql>

    <select id="getProcessInfoByPerson"
            resultType="com.iflytek.cdc.province.model.pandemic.vo.PersonProcessInfo">
        select
        <if test="diseaseType == 'infected'">
            infect_process_id as processId,
            fi_inf_onset_time as onsetTime,
        </if>
        <if test="diseaseType == 'syndrome'">
            syndrome_process_id as processId,
            fi_syn_onset_time as onsetTime,
        </if>
        patient_name as patientName,
        etl_update_datetime as updateTime,
        empi_id as empiId
        from <include refid="chooseTable"/> i
        where 1=1 and
        <if test="diseaseCode != null and diseaseCode != ''">
            <choose>
                <when test="diseaseType == 'infected'">
                    fi_inf_infect_code = #{diseaseCode}
                </when>
                <when test="diseaseType == 'syndrome'">
                    fi_syn_syndrome_code = #{diseaseCode}
                </when>
                <otherwise>
                    1=1
                </otherwise>
            </choose>  and
        </if>
        (patient_identity_no = #{identityNo} or (patient_name = #{patientName} and patient_sex_name = #{sexName} and patient_birth_day = #{birthDay}))
    </select>

    <select id="listInfectedModelSimpleInfo"
            resultType="com.iflytek.cdc.province.model.vo.InfectedProcessModelSimpleInfo">
        select
            infect_process_id as process_id,
            patient_sex_name  as patientSexName,
            drn.province_code AS livingAddressProvinceCode,
            drn.province_name AS livingAddressProvinceName,
            drn.city_code AS livingAddressCityCode,
            drn.city_name AS livingAddressCityName,
            drn.district_code AS livingAddressDistrictCode,
            drn.district_name AS livingAddressDistrictName,
            drn.street_code AS livingAddressStreetCode,
            drn.street_name AS livingAddressStreetName,
            fi_inf_living_addr_detail_std as currentAddress,
            person_type as personType,
--            lv_school_type  as schoolType,
            co.province_code AS schoolAddressProvinceCode,
            co.province_name AS schoolAddressProvinceName,
            co.city_code AS schoolAddressCityCode,
            co.city_name AS schoolAddressCityName,
            co.district_code AS schoolAddressDistrictCode,
            co.district_name AS schoolAddressDistrictName,
            co.street_code AS schoolAddressStreetCode,
            co.street_name AS schoolAddressStreetName,
            fi_inf_company_addr_detail_std as schoolAddress,
            fi_inf_org_name as firstDiagnosisOrg,
            lv_pathogen_res_type  as firstPathogenTestResult,
            lv_pathogen_res_type  as lastPathogenTestResult,
            lo_outcome_status as outCome,
            lo_dead_reason as deadReason,
            dii.infected_type_name as diseaseType,
            dii.infected_sub_name as diseaseName,
--            fi_syn_main_diag_class  as diagnosisType,
            lv_visit_time as diagnosisTime,
            li_inf_infect_class as diagnosisStatus
        from ads.ads_ms_infect_process_case_info i
        <include refid="allRegionAndInfectJoinCondition"/>
        where infect_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="listSyndromeModelSimpleInfo"
            resultType="com.iflytek.cdc.province.model.vo.SyndromeProcessModelSimpleInfo">
        select
            syndrome_process_id as process_id,
            patient_sex_name  as patientSexName,
            lv_living_addr_province_code as livingAddressProvinceCode,
            lv_living_addr_province as livingAddressProvinceName,
            lv_living_addr_city_code as livingAddressCityCode,
            lv_living_addr_city as livingAddressCityName,
            lv_living_addr_func_district_code as livingAddressDistrictCode,
            lv_living_addr_func_district as livingAddressDistrictName,
            lv_living_addr_street_code as livingAddressStreetCode,
            lv_living_addr_street as livingAddressStreetName,
            lv_person_type as personType,
            fi_syn_school_type  as schoolType,
            lv_company_province_code  as schoolAddressProvinceCode,
            lv_company_province  as schoolAddressProvinceName,
            lv_company_city_code  as schoolAddressCityCode,
            lv_company_city  as schoolAddressCityName,
            lv_company_func_district_code  as schoolAddressDistrictCode,
            lv_company_func_district  as schoolAddressDistrictName,
            lv_company_street_code  as schoolAddressStreetCode,
            lv_company_street  as schoolAddressStreetName,
            lv_company_addr_detail_std as schoolAddress,
            fi_syn_org_name as firstDiagnosisOrg,
            fi_pathogen_res_type  as firstPathogenTestResult,
            lv_pathogen_res_type  as lastPathogenTestResult,
            lo_outcome_status as outCome,
            lo_dead_reason as deadReason,
            li_syn_syndrome_name  syndrome,
            confirm_disease as confirmedDisease,
            li_syn_syndrome_out_flag as isExcluded,
            li_syn_syndrome_severe_flag as isSevere,
            infect_type  as caseType,
            li_syn_syndrome_severe_support as severeReason
        from ads.ads_ms_syndrome_process_info amspi
        where syndrome_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="listInfectedProcessLog" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO">
        select
        	occur_time as date,
        	label_type as type,
    	    label_value as value,
            case when source_desc = '传染病识别' then 'infected' else '' end as specialMark
        from ads.ads_ms_infect_process_view
        where delete_flag = '0' and infect_process_id = #{id}
        <if test="type !=null">
            and label_type = #{type}
        </if>
        order by occur_time
    </select>

    <select id="listSyndromeProcessLog" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO">
        select
        	occur_time as date,
        	label_type as type,
    	    label_value as value,
            case when source_desc = '传染病识别' then 'infected' else '' end as specialMark
        from ads.ads_ms_syndrome_process_view
        where delete_flag = '0' and syndrome_process_id = #{id}
        <if test="type !=null">
            and label_type = #{type}
        </if>
        order by occur_time
    </select>

    <select id="getMedicalByProcessId" resultType="java.lang.String">
        select event_json
        from <include refid="chooseTable"/>
        where 1=1
        <if test="diseaseType == 'infected'">
            and infect_process_id = #{processId}
        </if>
        <if test="diseaseType == 'syndrome'">
            and syndrome_process_id = #{processId}
        </if>
        limit 1
    </select>

    <select id="getInfectedProcessInfoBy"
            resultType="com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo">
        select
        infect_process_id as id,
        patient_name as patient_name,
        patient_sex_name as patient_sex_name,
        patient_birth_day as birth_day,
        fi_inf_patient_age as patient_age,
        fi_inf_patient_age_unit as patient_age_unit,
        person_type as job,
        person_type as person_type,

        fi_inf_living_addr_detail AS living_addr_detail,
        drn.street_name AS living_street_name,
        drn.street_code AS living_street_code,
        drn.district_name AS living_district_name,
        drn.district_code AS living_district_code,
        drn.city_name AS living_city_name,
        drn.city_code AS living_city_code,
        drn.province_name AS living_province_name,
        drn.province_code AS living_province_code,
--        fi_inf_living_addr_longitude AS living_addr_longitude,
--        fi_inf_living_addr_latitude AS living_addr_latitude,

        fi_inf_company AS company,
        co.street_name AS company_street_name,
        co.street_code AS company_street_code,
        co.district_name AS company_district_name,
        co.district_code AS company_district_code,
        co.city_name AS company_city_name,
        co.city_code AS company_city_code,
        co.province_name AS company_province_name,
        co.province_code AS company_province_code,
--        fi_inf_company_longitude AS company_addr_longitude,
--        fi_inf_company_latitude AS company_addr_latitude,

        fi_inf_org_id AS org_id,
        fi_inf_org_name AS org_name,
        org.street_name AS org_street_name,
        org.street_code AS org_street_code,
        org.district_name AS org_district_name,
        org.district_code AS org_district_code,
        org.city_name AS org_city_name,
        org.city_code AS org_city_code,
        org.province_name AS org_province_name,
        org.province_code AS org_province_code,
--        fi_inf_org_longitude AS org_addr_longitude,
--        fi_inf_org_latitude AS org_addr_latitude,

        fi_inf_onset_time as onset_time,
        fi_inf_visit_time as first_visit_time,
        fi_inf_org_id as first_visit_org_id,
        fi_inf_org_name as first_visit_org_name,
        fi_inf_identify_time as diagnose_time,
        'infected' as disease_type,
        fi_inf_infect_code as disease_code,
        dii.infected_sub_name as disease_name,
        lo_outcome_status as outcome_status,
        lo_outcome_time as outcome_time,
        lo_dead_time as death_time,
        severe_flag as severe_flag,
        li_inf_infect_out_flag as out_flag,
        'infected' as process_type
        from ads.ads_ms_infect_process_case_info i
        <include refid="infectCaseInfoTableJoin"/>
        where infect_process_id in
        <foreach collection="ids" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>

    </select>

    <select id="getSyndromeProcessInfoBy"
            resultType="com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo">
        select
        syndrome_process_id as id,
        patient_name as patient_name,
        patient_sex_name as patient_sex_name,
        patient_birth_day as birth_day,
        fi_syn_patient_age as patient_age,
        fi_syn_patient_age_unit as patient_age_unit,
        fi_syn_job as job,
        fi_syn_person_type as person_type,

        fi_syn_living_addr_detail AS living_addr_detail,
        fi_syn_living_addr_province_code AS living_province_code,
        fi_syn_living_addr_province AS living_province_name,
        fi_syn_living_addr_city_code AS living_city_code,
        fi_syn_living_addr_city AS living_city_name,
        fi_syn_living_addr_district_code AS living_district_code,
        fi_syn_living_addr_district AS living_district_name,
        fi_syn_living_addr_street_code AS living_street_code,
        fi_syn_living_addr_street AS living_street_name,
        fi_syn_living_addr_longitude AS living_addr_longitude,
        fi_syn_living_addr_latitude AS living_addr_latitude,

        fi_syn_company AS company,
        fi_syn_company_province_code AS company_province_code,
        fi_syn_company_province AS company_province_name,
        fi_syn_company_city_code AS company_city_code,
        fi_syn_company_city AS company_city_name,
        fi_syn_company_district_code AS company_district_code,
        fi_syn_company_district AS company_district_name,
        fi_syn_company_street_code AS company_street_code,
        fi_syn_company_street AS company_street_name,
        fi_syn_company_longitude AS company_addr_longitude,
        fi_syn_company_latitude AS company_addr_latitude,

        fi_syn_org_id AS org_id,
        fi_syn_org_name AS org_name,
        fi_syn_org_addr_province_code AS org_province_code,
        fi_syn_org_addr_province AS org_province_name,
        fi_syn_org_addr_city_code AS org_city_code,
        fi_syn_org_addr_city AS org_city_name,
        fi_syn_org_addr_district_code AS org_district_code,
        fi_syn_org_addr_district AS org_district_name,
        fi_syn_org_addr_street_code AS org_street_code,
        fi_syn_org_addr_street AS org_street_name,
        fi_syn_org_longitude AS org_addr_longitude,
        fi_syn_org_latitude AS org_addr_latitude,

        fi_syn_onset_time as onset_time,
        fi_syn_visit_time as first_visit_time,
        fi_syn_org_id as first_visit_org_id,
        fi_syn_org_name as first_visit_org_name,
        fi_syn_identify_time as diagnose_time,
        'syndrome' as disease_type,
        fi_syn_syndrome_code as disease_code,
        fi_syn_syndrome_name as disease_name,
        lo_outcome_status as outcome_status,
        lo_outcome_time as outcome_time,
        lo_dead_time as death_time,
        severe_flag as severe_flag,
        fi_syn_syndrome_severe_time as severe_time,
        li_syn_syndrome_out_flag as out_flag,
        fi_syn_syndrome_out_time as out_time,
        'syndrome' as process_type
        from ads.ads_ms_syndrome_process_info
        where syndrome_process_id in
        <foreach collection="ids" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getInfectedProcessPathogenInfoBy"
            resultType="com.iflytek.cdc.province.model.vo.ProcessPathogenInfoVO">
        select
        sum(case when pathogen_flag = '1' then 1 else 0 end) as totalTests,
        sum(case when pathogen_flag = '1' and pathogen_name is not null then 1 else 0 end) as positiveCnt,
        sum(case when pathogen_flag = '1' and pathogen_name is null then 1 else 0 end) as negativeCnt
        from ads.ads_ms_infect_process_case_info
        where infect_process_id in
        <foreach collection="ids" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getSyndromeProcessPathogenInfoBy"
            resultType="com.iflytek.cdc.province.model.vo.ProcessPathogenInfoVO">
        select
        sum(case when pathogen_flag = '1' then 1 else 0 end) as totalTests,
        sum(case when pathogen_flag = '1' and pathogen_name is not null then 1 else 0 end) as positiveCnt,
        sum(case when pathogen_flag = '1' and pathogen_name is null then 1 else 0 end) as negativeCnt
        from ads.ads_ms_syndrome_process_info
        where syndrome_process_id in
        <foreach collection="ids" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="syndromeSimpleInfoPageListByConditions" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO">
        select
        <include refid="syndromeSimpleInfoMap"/>,
        patient_name AS patientName,
        patient_identity_no AS idCard
        from ads.ads_ms_syndrome_process_info i
        where 1=1
        <if test="ids != null and ids.size() > 0">
            and syndrome_process_id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="symptomName != null and symptomName.size() > 0">
            <foreach collection="symptomName" item="item" open="AND (" close=")" separator="OR">
                symptom_attent_extr_json LIKE CONCAT('%', #{item}, '%')
            </foreach>
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and i.fi_syn_org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and i.fi_syn_org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and i.fi_syn_org_addr_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and i.fi_syn_org_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and i.fi_syn_org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and i.fi_syn_org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and i.fi_syn_org_addr_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and i.fi_syn_org_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="fvOrgId != null and fvOrgId.size() > 0">
            and i.fi_syn_org_id in
            <foreach collection="fvOrgId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="fvOrgName != null and fvOrgName.size() > 0">
            <foreach collection="fvOrgName" item="item" open="AND (" close=")" separator="OR">
                i.fi_syn_org_name like CONCAT('%', #{fvOrgName}, '%')
            </foreach>
        </if>
        <if test="job != null and job.size() > 0">
            and i.fi_syn_job in
            <foreach collection="job" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="company != null and company.size() > 0">
            and i.fi_syn_company in
            <foreach collection="company" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="patientSexName != null and patientSexName.size() > 0">
            and i.patient_sex_name in
            <foreach collection="patientSexName" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="visitStartDate != null">
            and i.fi_syn_visit_day >= #{visitStartDate}
        </if>
        <if test="visitEndDate != null">
            and i.fi_syn_visit_day &lt;= #{visitEndDate}
        </if>
        <if test="onsetStartDate != null">
            and i.fi_syn_onset_time >= #{onsetStartDate}
        </if>
        <if test="onsetEndDate != null">
            and i.fi_syn_onset_time &lt;= #{onsetEndDate}
        </if>
        <if test="mainDiag != null and mainDiag.size() > 0">
            and i.fi_syn_main_diag in
            <foreach collection="mainDiag" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="hasExcluded != null and hasExcluded.size() > 0">
            and i.li_syn_syndrome_out_flag in
            <foreach collection="hasExcluded" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="isSevere != null and isSevere.size() > 0">
            and i.li_syn_syndrome_severe_flag in
            <foreach collection="isSevere" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="outComeStatus != null and outComeStatus.size() > 0">
            and i.lo_outcome_status in
            <foreach collection="outComeStatus" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="queryKey != null and queryKey.size() > 0">
            <foreach collection="queryKey" item="item" open="AND (" close=")" separator="OR">
                (i.syndrome_process_id like CONCAT('%', #{item}, '%') or i.fi_syn_org_name like CONCAT('%', #{item}, '%'))
            </foreach>
        </if>
        <!-- 地址选择 -->
        <if test="livingProvinceCode != null and livingProvinceCode != ''">
            and i.fi_syn_living_addr_province_code = #{livingProvinceCode}
        </if>
        <if test="livingCityCode != null and livingCityCode != ''">
            and i.fi_syn_living_addr_city_code = #{livingCityCode}
        </if>
        <if test="livingDistrictCode != null and livingDistrictCode != ''">
            and i.fi_syn_living_addr_district_code = #{livingDistrictCode}
        </if>
        <if test="companyProvinceCode != null and companyProvinceCode != ''">
            and i.fi_syn_company_province_code = #{companyProvinceCode}
        </if>
        <if test="companyCityCode != null and companyCityCode != ''">
            and i.fi_syn_company_city_code = #{companyCityCode}
        </if>
        <if test="companyDistrictCode != null and companyDistrictCode != ''">
            and i.fi_syn_company_district_code = #{companyDistrictCode}
        </if>
        <!-- 年龄筛选 -->
        <if test="ageMin != null">
            and
            case
            when i.fi_syn_patient_age_unit = '岁' or i.fi_syn_patient_age_unit is null then i.fi_syn_patient_age
            when i.fi_syn_patient_age_unit = '月' then i.fi_syn_patient_age / 12.0
            when i.fi_syn_patient_age_unit = '天' then i.fi_syn_patient_age / 365.0
            else -1
            end >= #{ageMin}
        </if>
        <if test="ageMax != null">
            and
            case
            when i.fi_syn_patient_age_unit = '岁' or i.fi_syn_patient_age_unit is null then i.fi_syn_patient_age
            when i.fi_syn_patient_age_unit = '月' then i.fi_syn_patient_age / 12.0
            when i.fi_syn_patient_age_unit = '天' then i.fi_syn_patient_age / 365.0
            else 9999
            end &lt;= #{ageMax}
        </if>
        <!-- 病原监测 -->
        <if test="firstPathogenResult != null and firstPathogenResult.size() > 0">
            and i.fi_pathogen_res_type in
            <foreach collection="firstPathogenResult" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="lastPathogenResult != null and lastPathogenResult.size() > 0">
            and i.lv_pathogen_res_type  in
            <foreach collection="lastPathogenResult" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <include refid="syndromeOrder"/>
    </select>

    <select id="listSyndromeOrgProcessStats" resultType="com.iflytek.cdc.province.model.vo.SyndromeOrgProcessStatsVO">
        SELECT 
            fi_syn_org_name AS orgName,
            fi_syn_syndrome_name AS diseaseName,
            COUNT(syndrome_process_id) AS processCnt,
            ROUND(
                COUNT(syndrome_process_id) * 100.0 / SUM(COUNT(syndrome_process_id)) OVER (), 
                2
            ) AS radio
        FROM ads.ads_ms_syndrome_process_info amspi
        where 1=1
         <include refid="syndromeAreaChooseCriteria"/>
        <if test="startDate != null and endDate != null">
            and <include refid="syndromeTimeChoose"/> between #{startDate} and #{endDate}
        </if>
        GROUP BY fi_syn_org_name, fi_syn_syndrome_name
    </select>

    <select id="getEmergingMedicalList" resultType="com.iflytek.cdc.edr.vo.dm.ProcessRecordVO">
        select
        infect_process_id as id,
        patient_name,
        fi_inf_patient_age as patient_age,
        patient_sex_name,
        fi_inf_org_addr_func_district as lvOrgAddrFuncDistrict,
        li_inf_infect_code as li_disease_code,
        li_inf_infect_name as li_disease_name,
        fi_inf_identify_time as fiIdentifyTime,
        fi_inf_dept_name as fiSyndeptName,
        lv_org_name,
        lv_visit_time,
        li_inf_infect_class as li_infect_class,
        lo_outcome_status,
        fi_inf_org_name as fv_org_name,
        fi_inf_visit_time as fv_visit_time,
        lv_medical_id,
        li_inf_identify_id as li_identify_id,
        <!-- 最新识别病例id-->
        li_inf_identify_id as li_medical_id,
        fi_inf_org_addr_province_code as provinceCode,
        fi_inf_org_addr_province as provinceName,
        fi_inf_org_addr_city_code as cityCode,
        fi_inf_org_addr_city as cityName,
        fi_inf_org_addr_func_district_code as districtCode,
        fi_inf_org_addr_func_district as districtName
        from ads.ads_ms_emerging_process_info i
        where
        1=1
        <if test="visitTimeType == 'firstVisit'">
            <if test="startDate != null">
                and fi_inf_visit_day >= #{startDate}
            </if>
            <if test="endDate != null">
                and fi_inf_visit_day &lt;= #{endDate}
            </if>
        </if>
        <if test="visitTimeType == 'latestVisit'">
            <if test="startDate != null">
                and lv_visit_day >= #{startDate}
            </if>
            <if test="endDate != null">
                and lv_visit_day &lt;= #{endDate}
            </if>
        </if>
        <if test="visitTimeType == 'onset'">
            <if test="startDate != null">
                and CAST(fi_inf_onset_time AS DATE) >= #{startDate}
            </if>
            <if test="endDate != null">
                and CAST(fi_inf_onset_time AS DATE) &lt;= #{endDate}
            </if>
        </if>
        <if test="visitTimeType == 'diagnose'">
            <if test="startDate != null">
                and lv_visit_day >= #{startDate}
            </if>
            <if test="endDate != null">
                and lv_visit_day &lt;= #{endDate}
            </if>
        </if>
        <if test="visitTimeType == 'monitorReport'">
            <if test="startDate != null">
                and fi_inf_identify_time >= #{startDate}
            </if>
            <if test="endDate != null">
                and fi_inf_identify_time &lt;= #{endDate}
            </if>
        </if>
        <include refid="infectedOrgAddressCriteria"/>
        <if test="infectCodeList != null and infectCodeList.size()>0">
            and li_inf_infect_code in
            <foreach collection="infectCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="diagnoseStatus != null and diagnoseStatus != ''">
            and li_inf_infect_class = #{diagnoseStatus}
        </if>
        <if test="outcome != null and outcome != ''">
            and lo_outcome_status = #{outcome}
        </if>
        <if test="personInfo != null and personInfo != ''">
            and (infect_process_id like CONCAT('%', #{personInfo}, '%') or patient_name like CONCAT('%', #{personInfo}, '%'))
        </if>
        <if test="keyword != null and keyword != ''">
            and ( infect_process_id like  CONCAT('%', #{keyword}, '%') or li_inf_infect_name like  CONCAT('%', #{keyword}, '%') )
        </if>
        <if test="queryKey != null and queryKey != ''">
            and ( infect_process_id like  CONCAT('%', #{queryKey}, '%')
            or li_inf_infect_name like  CONCAT('%', #{queryKey}, '%')
            or patient_name like CONCAT('%', #{queryKey}, '%'))
        </if>
        <if test="idList != null and idList.size()>0">
            and infect_process_id in
            <foreach collection="idList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="idList != null and idList.size()>0">
            and infect_process_id in
            <foreach collection="idList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="fvOrgId != null and fvOrgId != ''">
            and fi_inf_org_id = #{fvOrgId}
        </if>
        <if test="fiSyndeptName != null and fiSyndeptName != ''">
            and fi_inf_dept_name = #{fiSyndeptName}
        </if>
        <include refid="infectedOrder"/>
        <if test="limitNum != null and limitNum >0">
            limit #{limitNum}
        </if>
    </select>

    <select id="getEndemicMedicalList" resultType="com.iflytek.cdc.edr.vo.dm.ProcessRecordVO">
        select
        syndrome_process_id as id,
        fi_syn_patient_age as patient_age,
        patient_name,
        patient_sex_name,
        li_syn_syndrome_name,
        fi_syn_syndrome_name as syndromeName,
        --li_inf_infect_name as liInfectName,
        fi_syn_visit_time as fvVisitTime,
        lv_visit_time,
        fi_syn_dept_name as fiSyndeptName,
        case when li_syn_syndrome_severe_flag = '1' then '重症'
        when li_syn_syndrome_severe_flag = '0' then '非重症' end as severityLevel,
        fi_syn_org_name as fvOrgName,
        fi_syn_org_addr_func_district as lvOrgAddrFuncDistrict,
        fi_syn_identify_time as fi_syn_syndrome_identify_time,
        lv_medical_id,
        --fi_syn_syndrome_id,
        fi_syn_org_addr_province_code as provinceCode,
        fi_syn_org_addr_province as provinceName,
        fi_syn_org_addr_city_code as cityCode,
        fi_syn_org_addr_city as cityName,
        fi_syn_org_addr_func_district_code as districtCode,
        fi_syn_org_addr_func_district as districtName,
        <!-- 审核存储信息-->
        '1' as diseaseType,
        li_syn_syndrome_code as li_disease_code,
        li_syn_syndrome_name as li_disease_name,
        fi_syn_identify_time as fiIdentifyTime
        from ads.ads_ms_endemic_process_info
        where 1=1
        <if test="diseaseCodeList != null and diseaseCodeList.size() &gt; 0">
            and fi_syn_syndrome_code in
            <foreach collection="diseaseCodeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and syndrome_process_id = #{id}
        </if>
        <if test="idList != null and idList.size()>0">
            and syndrome_process_id in
            <foreach collection="idList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="idList != null and idList.size()>0">
            and syndrome_process_id in
            <foreach collection="idList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="symptomName != null and symptomName != ''">
            and symptom_attent_extr_json AS CHAR like CONCAT('%',  #{symptomName}, '%')
        </if>
        <if test="fiSyndeptName != null and fiSyndeptName != ''">
            and fi_inf_dept_name like concat('%',  #{fiSyndeptName}, '%')
        </if>
        <if test="confirmDisease != null and confirmDisease != ''">
            and confirm_disease like CONCAT('%',  #{confirmDisease}, '%')
        </if>
        <if test="personInfo != null and personInfo != ''">
            and (patient_name like CONCAT('%', #{personInfo}, '%') or
            syndrome_process_id like CONCAT('%', #{personInfo}, '%'))
        </if>
        <if test="visitTimeType == 'firstVisit'">
            <if test="startDate != null">
                and fi_syn_visit_time >= #{startDate}
            </if>
            <if test="endDate != null">
                and fi_syn_visit_time &lt;= #{endDate}
            </if>
        </if>
        <if test="visitTimeType == 'latestVisit'">
            <if test="startDate != null">
                and lv_visit_time >= #{startDate}
            </if>
            <if test="endDate != null">
                and lv_visit_time &lt;= #{endDate}
            </if>
        </if>
        <if test="visitTimeType == 'monitorReport'">
            <if test="startDate != null">
                and fi_syn_identify_time >= #{startDate}
            </if>
            <if test="endDate != null">
                and fi_syn_identify_time &lt;= #{endDate}
            </if>
        </if>
        <if test="severityLevel == '重症'">
            and li_syn_syndrome_severe_flag = '1'
        </if>
        <if test="severityLevel == '非重症'">
            and li_syn_syndrome_severe_flag = '0'
        </if>
        <if test="fiSyndeptName != null and fiSyndeptName != ''">
            and fi_inf_dept_name = #{fiSyndeptName}
        </if>
        <if test="severityLevel == '死亡'">
            and lo_outcome_status like CONCAT('%', #{severityLevel}, '%')
        </if>
        <if test="outFlag != null and outFlag != ''">
            and li_syn_syndrome_out_flag = #{outFlag}
        </if>
        <if test="fvOrgId != null and fvOrgId != ''">
            and fi_syn_org_id = #{fvOrgId}
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and fi_syn_org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and fi_syn_org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and fi_syn_org_addr_func_district_code = #{districtCode}
        </if>
        <if test="keyword != null and keyword != ''">
            and ( syndrome_process_id like  CONCAT('%', #{keyword}, '%') or li_syn_syndrome_name like  CONCAT('%', #{keyword}, '%') )
        </if>
        <if test="queryKey != null and queryKey != ''">
            and ( syndrome_process_id like  CONCAT('%', #{queryKey}, '%')
            or li_syn_syndrome_name like  CONCAT('%', #{queryKey}, '%')
            or patient_name like CONCAT('%', #{queryKey}, '%'))
        </if>
        <include refid="syndromeOrder"/>
        <if test="limitNum != null and limitNum >0">
            limit #{limitNum}
        </if>
    </select>

    <select id="getMedicalDept" resultType="java.lang.String">
         select distinct  fi_inf_dept_name from ads.ads_ms_infect_process_case_info where fi_inf_dept_name is not null and rep_card_flag = '0'
    </select>

</mapper>