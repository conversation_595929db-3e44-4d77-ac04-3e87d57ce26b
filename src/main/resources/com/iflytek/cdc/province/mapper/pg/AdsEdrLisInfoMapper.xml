<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrLisInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsEdrLisInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_edr_lis_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="specimen_category" jdbcType="VARCHAR" property="specimenCategory" />
    <result column="specimen_category_name" jdbcType="VARCHAR" property="specimenCategoryName" />
    <result column="specimen_sampling_date" jdbcType="TIMESTAMP" property="specimenSamplingDate" />
    <result column="specimen_receiving_date" jdbcType="TIMESTAMP" property="specimenReceivingDate" />
    <result column="examination_item_code" jdbcType="VARCHAR" property="examinationItemCode" />
    <result column="examination_item" jdbcType="VARCHAR" property="examinationItem" />
    <result column="examination_method_code" jdbcType="VARCHAR" property="examinationMethodCode" />
    <result column="examination_date" jdbcType="TIMESTAMP" property="examinationDate" />
    <result column="specimen_no" jdbcType="VARCHAR" property="specimenNo" />
    <result column="examination_quantification" jdbcType="VARCHAR" property="examinationQuantification" />
    <result column="examination_quantification_upper" jdbcType="VARCHAR" property="examinationQuantificationUpper" />
    <result column="examination_quantification_lower" jdbcType="VARCHAR" property="examinationQuantificationLower" />
    <result column="examination_quantification_ri" jdbcType="VARCHAR" property="examinationQuantificationRi" />
    <result column="examination_quantification_unit" jdbcType="VARCHAR" property="examinationQuantificationUnit" />
    <result column="source_examination_result_code" jdbcType="VARCHAR" property="sourceExaminationResultCode" />
    <result column="source_examination_result" jdbcType="VARCHAR" property="sourceExaminationResult" />
    <result column="examination_report_date" jdbcType="TIMESTAMP" property="examinationReportDate" />
    <result column="confirm_status_code" jdbcType="VARCHAR" property="confirmStatusCode" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="mpi_id" jdbcType="VARCHAR" property="mpiId" />
    <result column="month" jdbcType="VARCHAR" property="month" />
    <result column="create_org" jdbcType="VARCHAR" property="createOrg" />
    <result column="create_org_name" jdbcType="VARCHAR" property="createOrgName" />
    <result column="update_org" jdbcType="VARCHAR" property="updateOrg" />
    <result column="update_org_name" jdbcType="VARCHAR" property="updateOrgName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, etl_create_datetime, etl_update_datetime, event_id, empi_id, specimen_category, 
    specimen_category_name, specimen_sampling_date, specimen_receiving_date, examination_item_code, 
    examination_item, examination_method_code, examination_date, specimen_no, examination_quantification, 
    examination_quantification_upper, examination_quantification_lower, examination_quantification_ri, 
    examination_quantification_unit, source_examination_result_code, source_examination_result, 
    examination_report_date, confirm_status_code, create_user, create_time, update_user, 
    update_time, source_id, mpi_id, "month", create_org, create_org_name, update_org, 
    update_org_name
  </sql>


  <select id="getLisByEventId" parameterType="string"
          resultType="com.iflytek.cdc.province.model.vo.edrcase.AdsEdrLisInfoVO">
    select
    id,
    etl_create_datetime,
    etl_update_datetime,
    event_id,
    empi_id,
    specimen_category,
    specimen_category_name,
    specimen_sampling_date,
    specimen_receiving_date,
    examination_item_code,
    examination_item,
    examination_method_code,
    examination_date,
    specimen_no,
    examination_quantification,
    examination_quantification_upper,
    examination_quantification_lower,
    examination_quantification_ri,
    examination_quantification_unit,
    source_examination_result_code,
    source_examination_result,
    examination_report_date,
    confirm_status_code
    from ads.ads_edr_lis_info aeli
    where event_id = #{eventId}
    order by examination_date desc
  </select>
</mapper>