<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsPoIndexInfoMapper">

    <sql id="areaCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and city_code = #{cityCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="topicCriteria">
        <if test="topicKeywords != null and topicKeywords.size() != 0">
            and std_word in
            <foreach collection="topicKeywords" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>

    <select id="selectKeywordCountBy"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorKeywordVO">
        select
            std_word as keyword,
            avg(index_value) as index_value,
            count(1) as count
        from ads.ads_po_index_info
        where 1 = 1
            <if test="startDate != null and endDate != null">
                and day between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            </if>
            <if test="keyword != null and keyword != ''">
                and std_word = #{keyword,jdbcType=VARCHAR}
            </if>
            <if test="indexClass != null and indexClass != ''">
                and index_class = #{indexClass,jdbcType=VARCHAR}
            </if>
            <include refid="areaCriteria"/>
            <include refid="topicCriteria"/>
        group by std_word
    </select>

    <select id="dailyIndex" resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.AdsPoIndexDailyVO">
        select
            std_word as keyword,
            day,
            avg(index_value) as index_value,
            count(1) as count
        from ads.ads_po_index_info
        where day between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
            <if test="keyword != null and keyword != ''">
                and std_word = #{keyword,jdbcType=VARCHAR}
            </if>
            <if test="indexClass != null and indexClass != ''">
                and index_class = #{indexClass,jdbcType=VARCHAR}
            </if>
        <include refid="areaCriteria"/>
        <include refid="topicCriteria"/>
        group by std_word, day
    </select>
</mapper>