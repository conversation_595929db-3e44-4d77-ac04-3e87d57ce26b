<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcbrManualBriefRecordMapper">

    <select id="queryList" resultType="com.iflytek.cdc.province.model.brief.ManualBriefRecordVO">
        select id,
        title,
        statistics_source,
        statistics_start_time,
        statistics_end_time,
        coalesce(statistics_district_code, statistics_city_code, statistics_province_code) as statistics_region_code,
        coalesce(statistics_district_name, statistics_city_name, statistics_province_name) as statistics_region_name,
        generate_time,
        generate_file_path
        from tb_cdcbr_manual_brief_record rec
        where rec.creator_id = #{creatorId, jdbcType=VARCHAR}
        <if test="statisticsSource != null and statisticsSource != ''">
            and rec.statistics_source = #{statisticsSource, jdbcType=VARCHAR}
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and rec.statistics_province_code = #{provinceCode, jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and rec.statistics_city_code = #{cityCode, jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and rec.statistics_district_code = #{districtCode, jdbcType=VARCHAR}
        </if>
        <if test="startDate != null and endDate != null">
            <if test="dateType == 'statisticsTime'">
                and rec.statistics_start_time &lt;= #{endDate, jdbcType=TIMESTAMP} and statistics_end_time &gt;> #{startDate, jdbcType=TIMESTAMP}
            </if>
            <if test="dateType == 'generateTime'">
                and rec.generate_time between #{startDate, jdbcType=TIMESTAMP} and #{endDate, jdbcType=TIMESTAMP}
            </if>
        </if>
        order by rec.create_time desc, id desc
    </select>

</mapper>