<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMulProcessInfoMapper">

    <sql id="multichannelSimpleInfoMap">
        mul_process_id as id,
        mul_process_id as processId,
        lv_addr_belong_area_code as regionId,
        lv_addr_belong_area as regionName,
        CASE
            WHEN COALESCE(fv_living_addr_province_code, '') = COALESCE(fv_org_addr_province_code, '')
                THEN fv_living_addr_province_code
            ELSE fv_org_addr_province_code
            END AS provinceCode,
        CASE
            WHEN COALESCE(fv_living_addr_province_code, '') = COALESCE(fv_org_addr_province_code, '')
                THEN fv_living_addr_province
            ELSE fv_org_addr_province
            END AS province,
        CASE
            WHEN COALESCE(fv_living_addr_province_code, '') = COALESCE(fv_org_addr_province_code, '')
                THEN fv_living_addr_city_code
            ELSE fv_org_addr_city_code
            END AS cityCode,
        CASE
            WHEN COALESCE(fv_living_addr_province_code, '') = COALESCE(fv_org_addr_province_code, '')
                THEN fv_living_addr_city
            ELSE fv_org_addr_city
            END AS city,
        CASE
            WHEN COALESCE(fv_living_addr_province_code, '') = COALESCE(fv_org_addr_province_code, '')
                THEN fv_living_addr_func_district_code
            ELSE fv_org_addr_func_district_code
            END AS funcDistrictCode,
        CASE
            WHEN COALESCE(fv_living_addr_province_code, '') = COALESCE(fv_org_addr_province_code, '')
                THEN fv_living_addr_func_district
            ELSE fv_org_addr_func_district
            END AS funcDistrict,
        CASE
            WHEN COALESCE(fv_living_addr_province_code, '') = COALESCE(fv_org_addr_province_code, '')
                THEN fv_living_addr_street_code
            ELSE fv_org_addr_street_code
            END AS streetCode,
        CASE
            WHEN COALESCE(fv_living_addr_province_code, '') = COALESCE(fv_org_addr_province_code, '')
                THEN fv_living_addr_street
            ELSE fv_org_addr_street
            END AS street,
        fv_org_id as fvOrgId,
        fv_org_name as fvOrgName,
        fi_identify_time as firstIdentifyTime,
        li_identify_time as lastIdentifyTime,
        li_disease_code as diseaseCode,
        li_disease_name as diseaseName,
        li_infect_class as diagnoseStatus,
        fv_diag_time as identifyTime,
        fv_living_addr_detail as livingAddrStd,
        fv_company as company,
        patient_sex_name as patient_sex_name,
        fv_patient_age as patientAge,
        fv_outcome_status as outComeStatus,
        fv_visit_time as visitTime,
        CAST(fv_visit_time AS DATE) as visitDay
    </sql>

    <sql id="multichannelLivingAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and fv_living_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and fv_living_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and fv_living_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and fv_living_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and fv_living_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and fv_living_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and fv_living_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and fv_living_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>


    <sql id="multichannelCompanyAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and fv_company_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and fv_company_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and fv_company_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and fv_company_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and fv_company_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and fv_company_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and fv_company_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="multichannelOrgAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and fv_org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and fv_org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and fv_org_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and fv_org_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and fv_org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and fv_org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and fv_org_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and fv_org_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="multichannelAreaChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <include refid="multichannelLivingAddressCriteria"/>
            </when>
            <otherwise>
                <include refid="multichannelOrgAddressCriteria"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="multichannelTimeChoose">
        <choose>
            <when test="timeType == 'identifyTime'">
                 fi_identify_time
            </when>
            <otherwise>
                 fv_visit_time
            </otherwise>
        </choose>
    </sql>

    <sql id="multichannelAddress">
        lv_living_addr_detail,
        lv_living_addr_detail_std,
        lv_living_addr_longitude,
        lv_living_addr_latitude,

        lv_company,
        lv_company_addr_detail_std,
        lv_company_longitude,
        lv_company_latitude,

        lv_org_name,
        lv_org_longitude,
        lv_org_latitude,
    </sql>

    <sql id="livingAreaChoose">
        <if test="areaLevel == 1">
            2 as area_level,
            fv_living_addr_city_code as area_code,
            fv_living_addr_city as area_name

        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            3 as area_level,
            fv_living_addr_func_district_code as area_code,
            fv_living_addr_func_district as area_name
        </if>
    </sql>

    <sql id="companyAreaChoose">
        <if test="areaLevel == 1">
            2 as area_level,
            fv_company_city_code as area_code,
            fv_company_city as area_name
        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            3 as area_level,
            fv_company_func_district_code as area_code,
            fv_company_func_district as area_name
        </if>
    </sql>

    <sql id="orgAreaChoose">
        <if test="areaLevel == 1">
            2 as area_level,
            fv_org_addr_city_code as area_code,
            fv_org_addr_city as area_name
        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            3 as area_level,
            fv_org_addr_func_district_code as area_code,
            fv_org_addr_func_district as area_name
        </if>
    </sql>

    <sql id="areaChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <include refid="livingAreaChoose"/>
            </when>
            <when test="addressType == 'companyAddress'">
                <include refid="companyAreaChoose"/>
            </when>
            <otherwise>
                <include refid="orgAreaChoose"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="medCntCommonCriteria">
        <if test="ids != null and ids.size() > 0">
            and mul_process_id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and mul_process_id =  #{id}
        </if>
        <if test="processId != null and processId != ''">
            and mul_process_id = #{processId}
        </if>
        <if test="fvOrgId != null and fvOrgId != ''">
            and fv_org_id = #{fvOrgId}
        </if>
        <if test="fvOrgName != null and fvOrgName != ''">
            and fv_org_name = #{fvOrgName}
        </if>
        <include refid="multichannelAreaChoose"/>
        <if test="diseaseName != null and diseaseName != ''">
            and li_disease_name = #{diseaseName}
        </if>
    </sql>

    <select id="listMultichannelProcessInfo"
            resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO">
        select <include refid="multichannelSimpleInfoMap"/>,
            patient_name AS patientName,
            patient_identity_no AS idCard
        from ads.ads_mul_process_info
        where 1=1
        <if test="ids != null and ids.size() > 0">
            and mul_process_id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and mul_process_id = #{id}
        </if>
        <if test="fvOrgId != null and fvOrgId != ''">
            and fv_org_id = #{fvOrgId}
        </if>
        <if test="fvOrgName != null and fvOrgName != ''">
            and fv_org_name = #{fvOrgName}
        </if>
        <if test="startDate != null and endDate != null">
            and fv_visit_time between #{startDate} and #{endDate}
        </if>
        <include refid="multichannelLivingAddressCriteria"/>
        <choose>
            <when test="property != null and direction != null">
                order by ${property} ${direction}
            </when>
            <otherwise>
                order by etl_create_datetime desc
            </otherwise>
        </choose>
    </select>

    <select id="loadMultichannelSimpleInfo"
            resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoDetailVO">
        select <include refid="multichannelSimpleInfoMap"/>,
            patient_name AS patientName,
            patient_identity_no AS idCard,
            patient_birth_day  AS birthDate,
            patient_sex_name AS genderName,
            fv_living_addr_detail  AS livingAddrDetail,
            fv_living_addr_province_code AS livingAddrProvinceCode,
            fv_living_addr_province AS livingAddrProvinceName,

            fv_living_addr_city_code AS livingAddrCityCode,
            fv_living_addr_city AS livingAddrCityName,

            fv_living_addr_func_district_code AS livingAddrDistrictCode,
            fv_living_addr_func_district AS livingAddrDistrictName,

            fv_living_addr_street_code AS livingAddrStreetCode,
            fv_living_addr_street AS livingAddrStreetName,

            fv_living_addr_street_longitude AS livingAddrLongitude,
            fv_living_addr_street_latitude AS livingAddrLatitude
        from ads.ads_mul_process_info
        where mul_process_id = #{id}
    </select>

    <select id="countMultichannelProcess" resultType="java.lang.Integer">
        select count(*)
        from ads.ads_mul_process_info
        where mul_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="listMultichannelMsProcessInfoAddress"
            resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO">
        select
        <include refid="multichannelAddress"/>
        mul_process_id as id,
        mul_process_id as processId
        from ads.ads_mul_process_info
        where mul_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="item" index="index">
            #{item}
        </foreach>
    </select>

    <select id="listMultichannelPatientInfo" resultType="com.iflytek.cdc.province.model.vo.MsPatientInfoVO">
        select
            mul_process_id as id,
            mul_process_id as processId,
            patient_name as patientName,
            patient_sex_name as patientSexName,
            fv_patient_age as patientAge,
            fv_person_type as job
        from ads.ads_mul_process_info
        where mul_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="item" index="index">
            #{item}
        </foreach>
    </select>

    <select id="listMultichannelMedCntIndicator"
            resultType="com.iflytek.cdc.province.model.vo.MedCntIndicatorVO">
        select
        CAST(<include refid="multichannelTimeChoose"/> AS DATE) as stat_date,
        count(mul_process_id) as medCaseCnt
        from ads.ads_mul_process_info
        where 1= 1
        <include refid="medCntCommonCriteria"/>
        <if test="startDate != null and endDate != null">
            and <include refid="multichannelTimeChoose"/> between #{startDate} and #{endDate}
        </if>
        group by stat_date order by stat_date
    </select>

    <select id="listMultichannelOutcomeCntIndicator"
            resultType="com.iflytek.cdc.province.model.vo.MedCntIndicatorVO">
        select
        cast(case when lo_outcome_time is null then lo_dead_time else lo_outcome_time end as date) as stat_date,
        sum(case when lo_outcome_status = '治愈' then 1 else 0 end) as recoverCnt,
        sum(case when lo_outcome_status = '死亡' then 1 else 0 end) as deathCnt,
        sum(case when lo_outcome_time = null then 1 else 0 end) as existingCaseCnt,
        sum(case when lo_dead_reason = '因该病死亡' then 1 else 0 end) as diseaseDeathCnt
        from ads.ads_mul_process_info
        where 1=1
        <include refid="medCntCommonCriteria"/>
        <if test="startDate != null and endDate != null">
            and (lo_outcome_time between #{startDate} and #{endDate} or lo_dead_time between #{startDate} and #{endDate})
        </if>
        and (lo_outcome_time != null or lo_dead_time != null)
        group by stat_date order by stat_date
    </select>

    <select id="listMultichannelAreaMedCntIndicator"
            resultType="com.iflytek.cdc.province.model.vo.AreaMedCntIndicatorVO">
        select
        cast(<include refid="multichannelTimeChoose"/> AS date) as stat_date,
        count(mul_process_id) as medCaseCnt,
        <include refid="areaChoose"/>
        from ads.ads_mul_process_info
        where 1= 1
        <include refid="medCntCommonCriteria"/>
        <if test="startDate != null and endDate != null">
            and <include refid="multichannelTimeChoose"/> between #{startDate} and #{endDate}
        </if>
        group by stat_date, area_level, area_code, area_name
        order by stat_date
    </select>

    <select id="listMultichannelAreaOutcomeCntIndicator"
            resultType="com.iflytek.cdc.province.model.vo.AreaMedCntIndicatorVO">
        select
            cast(case when lo_outcome_time is null then lo_dead_time else lo_outcome_time end as date) as stat_date,
            sum(case when lo_outcome_status = '治愈' then 1 else 0 end) as recoverCnt,
            sum(case when lo_outcome_status = '死亡' then 1 else 0 end) as deathCnt,
            sum(case when lo_outcome_time = null then 1 else 0 end) as existingCaseCnt,
            sum(case when lo_dead_reason = '因该病死亡' then 1 else 0 end) as diseaseDeathCnt,
            <include refid="areaChoose"/>
        from ads.ads_mul_process_info
        where 1=1
        <include refid="medCntCommonCriteria"/>
        <if test="startDate != null and endDate != null">
            and (lo_outcome_time between #{startDate} and #{endDate} or lo_dead_time between #{startDate} and #{endDate})
        </if>
        group by stat_date, area_level, area_code, area_name
        order by stat_date
    </select>

    <select id="listMultichannelPathogenCheckResult"
            resultType="com.iflytek.cdc.province.model.vo.PathogenCheckVO">
        select
            mul_process_id     as processId,
            patient_name       as patientName,
            patient_sex_name   as sexDesc,
            fv_patient_age     as patientAge,
            pneumonia_flag     as isPneumonia,
            case when virus_positive_flag = '1' or bacteria_positive_flag = '1' then '1' else '0' end as isPositive
        from ads.ads_mul_process_info
        where mul_process_id in
        <foreach collection="sourceKeyList" separator="," index="index" item="item" close=")" open="(">
            #{item}
        </foreach>
        <if test="isPneumonia != null and isPneumonia != ''">
            and pneumonia_flag = #{isPneumonia}
        </if>
        <if test="pathogenName != null and pathogenName != ''">
            and pathogen_name like concat('%', #{pathogenName}, '%')
        </if>
    </select>

    <select id="getMultichannelProcessInfoBy"
            resultType="com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo">
        select
        mul_process_id as id,
        patient_name as patient_name,
        patient_sex_name as patient_sex_name,
        patient_birth_day as birth_day,
        fv_patient_age as patient_age,
        fv_patient_age_unit as patient_age_unit,
        fv_job as job,
        fv_person_type as person_type,

        fv_living_addr_detail AS living_addr_detail,
        fv_living_addr_province_code AS living_province_code,
        fv_living_addr_province AS living_province_name,
        fv_living_addr_city_code AS living_city_code,
        fv_living_addr_city AS living_city_name,
        fv_living_addr_district_code AS living_district_code,
        fv_living_addr_district AS living_district_name,
        fv_living_addr_street_code AS living_street_code,
        fv_living_addr_street AS living_street_name,
        fv_living_addr_longitude AS living_addr_longitude,
        fv_living_addr_latitude AS living_addr_latitude,

        fv_company AS company,
        fv_company_province_code AS company_province_code,
        fv_company_province AS company_province_name,
        fv_company_city_code AS company_city_code,
        fv_company_city AS company_city_name,
        fv_company_district_code AS company_district_code,
        fv_company_district AS company_district_name,
        fv_company_street_code AS company_street_code,
        fv_company_street AS company_street_name,
        fv_company_longitude AS company_addr_longitude,
        fv_company_latitude AS company_addr_latitude,

        fv_org_id AS org_id,
        fv_org_name AS org_name,
        fv_org_addr_province_code AS org_province_code,
        fv_org_addr_province AS org_province_name,
        fv_org_addr_city_code AS org_city_code,
        fv_org_addr_city AS org_city_name,
        fv_org_addr_district_code AS org_district_code,
        fv_org_addr_district AS org_district_name,
        fv_org_addr_street_code AS org_street_code,
        fv_org_addr_street AS org_street_name,
        fv_org_longitude AS org_addr_longitude,
        fv_org_latitude AS org_addr_latitude,

        fv_onset_time as onset_time,
        fv_visit_time as first_visit_time,
        fv_org_id as first_visit_org_id,
        fv_org_name as first_visit_org_name,
        fi_identify_time as diagnose_time,
        'multichannel' as disease_type,
        fi_disease_code as disease_code,
        fi_disease_name as disease_name,
        lo_outcome_status as outcome_status,
        lo_outcome_time as outcome_time,
        lo_dead_time as death_time,
        'multichannel' as process_type
        from ads.ads_mul_process_info
        where mul_process_id in
        <foreach collection="ids" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getMultichannelProcessPathogenInfoBy"
            resultType="com.iflytek.cdc.province.model.vo.ProcessPathogenInfoVO">
        select
        sum(case when pathogen_flag = '1' then 1 else 0 end) as totalTests,
        sum(case when pathogen_flag = '1' and pathogen_name is not null then 1 else 0 end) as positiveCnt,
        sum(case when pathogen_flag = '1' and pathogen_name is null then 1 else 0 end) as negativeCnt
        from ads.ads_mul_process_info
        where mul_process_id in
        <foreach collection="ids" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

</mapper>