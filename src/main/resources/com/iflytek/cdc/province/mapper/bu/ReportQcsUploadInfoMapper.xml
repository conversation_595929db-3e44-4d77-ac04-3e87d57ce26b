<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.ReportQcsUploadInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.bu.ReportQcsUploadInfo">
        <id column="id" property="id" />
        <result column="creator_id" property="creatorId" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater_id" property="updaterId" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="memo" property="memo" />
        <result column="main_record_id" property="mainRecordId" />
        <result column="report_card_id" property="reportCardId" />
        <result column="report_card_code" property="reportCardCode" />
        <result column="report_card_status" property="reportCardStatus" />
        <result column="report_type_name" property="reportTypeName" />
        <result column="patient_name" property="patientName" />
        <result column="parent_name" property="parentName" />
        <result column="identity_type_name" property="identityTypeName" />
        <result column="identity_no" property="identityNo" />
        <result column="sex_name" property="sexName" />
        <result column="birth_date" property="birthDate" />
        <result column="age" property="age" />
        <result column="age_unit" property="ageUnit" />
        <result column="company" property="company" />
        <result column="phone" property="phone" />
        <result column="belong_name" property="belongName" />
        <result column="living_addr_area_code" property="livingAddrAreaCode" />
        <result column="living_addr_detail" property="livingAddrDetail" />
        <result column="person_type_name" property="personTypeName" />
        <result column="case_name1" property="caseName1" />
        <result column="case_name2" property="caseName2" />
        <result column="onset_time" property="onsetTime" />
        <result column="diag_time" property="diagTime" />
        <result column="death_date" property="deathDate" />
        <result column="disease_name" property="diseaseName" />
        <result column="unrevised_disease_name" property="unrevisedDiseaseName" />
        <result column="unrevised_identify_time" property="unrevisedIdentifyTime" />
        <result column="unrevised_final_check_time" property="unrevisedFinalCheckTime" />
        <result column="report_doctor_name" property="reportDoctorName" />
        <result column="report_fill_date" property="reportFillDate" />
        <result column="report_org_addr_area_code" property="reportOrgAddrAreaCode" />
        <result column="report_org_name" property="reportOrgName" />
        <result column="report_org_type_code" property="reportOrgTypeCode" />
        <result column="upload_record_time" property="uploadRecordTime" />
        <result column="upload_doctor_name" property="uploadDoctorName" />
        <result column="upload_org_name" property="uploadOrgName" />
        <result column="district_check_time" property="districtCheckTime" />
        <result column="city_check_time" property="cityCheckTime" />
        <result column="province_check_time" property="provinceCheckTime" />
        <result column="check_status" property="checkStatus" />
        <result column="revised_identify_time" property="revisedIdentifyTime" />
        <result column="revised_final_check_time" property="revisedFinalCheckTime" />
        <result column="revised_death_time" property="revisedDeathTime" />
        <result column="revise_doctor_name" property="reviseDoctorName" />
        <result column="revise_org_name" property="reviseOrgName" />
        <result column="delete_op_time" property="deleteOpTime" />
        <result column="delete_doctor_name" property="deleteDoctorName" />
        <result column="delete_org_name" property="deleteOrgName" />
        <result column="delete_reason" property="deleteReason" />
        <result column="report_card_note" property="reportCardNote" />
        <result column="empi_id" property="empiId" />
        <result column="living_addr_province_code" property="livingAddrProvinceCode" />
        <result column="living_addr_province" property="livingAddrProvince" />
        <result column="living_addr_city_code" property="livingAddrCityCode" />
        <result column="living_addr_city" property="livingAddrCity" />
        <result column="living_addr_district_code" property="livingAddrDistrictCode" />
        <result column="living_addr_district" property="livingAddrDistrict" />
        <result column="living_addr_func_district_code" property="livingAddrFuncDistrictCode" />
        <result column="living_addr_func_district" property="livingAddrFuncDistrict" />
        <result column="living_addr_street_code" property="livingAddrStreetCode" />
        <result column="living_addr_street" property="livingAddrStreet" />
        <result column="report_org_addr_province_code" property="reportOrgAddrProvinceCode" />
        <result column="report_org_addr_province" property="reportOrgAddrProvince" />
        <result column="report_org_addr_city_code" property="reportOrgAddrCityCode" />
        <result column="report_org_addr_city" property="reportOrgAddrCity" />
        <result column="report_org_addr_district_code" property="reportOrgAddrDistrictCode" />
        <result column="report_org_addr_district" property="reportOrgAddrDistrict" />
        <result column="report_org_addr_func_district_code" property="reportOrgAddrFuncDistrictCode" />
        <result column="report_org_addr_func_district" property="reportOrgAddrFuncDistrict" />
        <result column="report_org_addr_street_code" property="reportOrgAddrStreetCode" />
        <result column="report_org_addr_street" property="reportOrgAddrStreet" />
        <result column="age_group" property="ageGroup" />
        <result column="death_flag" property="deathFlag" />
        <result column="delay_flag" property="delayFlag" />
        <result column="disease_code" property="diseaseCode" />
        <result column="infect_type" property="infectType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, memo, main_record_id, report_card_id, report_card_code, report_card_status, report_type_name, patient_name, parent_name, identity_type_name, identity_no, sex_name, birth_date, age, age_unit, company, phone, belong_name, living_addr_area_code, living_addr_detail, person_type_name, case_name1, case_name2, onset_time, diag_time, death_date, disease_name, unrevised_disease_name, unrevised_identify_time, unrevised_final_check_time, report_doctor_name, report_fill_date, report_org_addr_area_code, report_org_name, report_org_type_code, upload_record_time, upload_doctor_name, upload_org_name, district_check_time, city_check_time, province_check_time, check_status, revised_identify_time, revised_final_check_time, revised_death_time, revise_doctor_name, revise_org_name, delete_op_time, delete_doctor_name, delete_org_name, delete_reason, report_card_note, empi_id, living_addr_province_code, living_addr_province, living_addr_city_code, living_addr_city, living_addr_district_code, living_addr_district, living_addr_func_district_code, living_addr_func_district, living_addr_street_code, living_addr_street, report_org_addr_province_code, report_org_addr_province, report_org_addr_city_code, report_org_addr_city, report_org_addr_district_code, report_org_addr_district, report_org_addr_func_district_code, report_org_addr_func_district, report_org_addr_street_code, report_org_addr_street, age_group, death_flag, delay_flag, disease_code, infect_type
    </sql>
    <select id="selectByMainRecordId" resultType="com.iflytek.cdc.province.entity.bu.ReportQcsUploadInfo">
        select <include refid="Base_Column_List"/>
        from app.tb_cdcew_report_qcs_upload_info
        where main_record_id =#{mainRecordId}
    </select>

</mapper>
