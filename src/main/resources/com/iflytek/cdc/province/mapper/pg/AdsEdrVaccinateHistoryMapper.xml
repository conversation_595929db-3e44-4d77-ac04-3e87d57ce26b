<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrVaccinateHistoryMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsEdrVaccinateHistory">
    <!--@mbg.generated-->
    <!--@Table ads.ads_edr_vaccinate_history-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="vaccine_type_code" jdbcType="VARCHAR" property="vaccineTypeCode" />
    <result column="vaccine_type" jdbcType="VARCHAR" property="vaccineType" />
    <result column="vaccination_location_code" jdbcType="VARCHAR" property="vaccinationLocationCode" />
    <result column="vaccination_location" jdbcType="VARCHAR" property="vaccinationLocation" />
    <result column="vaccination_time" jdbcType="DATE" property="vaccinationTime" />
    <result column="vaccination_dose" jdbcType="NUMERIC" property="vaccinationDose" />
    <result column="vaccine_number" jdbcType="VARCHAR" property="vaccineNumber" />
    <result column="aefi_diagnosis_code" jdbcType="VARCHAR" property="aefiDiagnosisCode" />
    <result column="aefi_diagnosis" jdbcType="VARCHAR" property="aefiDiagnosis" />
    <result column="create_org" jdbcType="VARCHAR" property="createOrg" />
    <result column="create_org_name" jdbcType="VARCHAR" property="createOrgName" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_org" jdbcType="VARCHAR" property="updateOrg" />
    <result column="update_org_name" jdbcType="VARCHAR" property="updateOrgName" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="mpi_id" jdbcType="VARCHAR" property="mpiId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, etl_create_datetime, etl_update_datetime, event_id, empi_id, vaccine_type_code, 
    vaccine_type, vaccination_location_code, vaccination_location, vaccination_time, 
    vaccination_dose, vaccine_number, aefi_diagnosis_code, aefi_diagnosis, create_org, 
    create_org_name, create_user, create_time, update_org, update_org_name, update_user, 
    update_time, source_id, mpi_id
  </sql>
  <select id="getAdsEdrVaccinateHistoryList" resultType="com.iflytek.cdc.province.model.vo.edrcase.AdsEdrVaccinateHistoryVO">
    select
    event_id, empi_id, vaccine_type_code,
    vaccine_type, vaccination_location_code, vaccination_location, vaccination_time,
    vaccination_dose, vaccine_number, aefi_diagnosis_code, aefi_diagnosis, create_org,
    create_org_name, create_user, create_time
    from ads.ads_edr_vaccinate_history
    where 1=1
    <if test="empiId != null and empiId != ''">
      and empi_id=#{empiId}
    </if>
    <if test="eventIds != null and eventIds.length>0 ">
      and event_id in
      <foreach item="eventId" collection="eventIds" open="(" separator="," close=")">
        #{eventId}
      </foreach>
    </if>
    order by vaccination_time desc
  </select>
</mapper>