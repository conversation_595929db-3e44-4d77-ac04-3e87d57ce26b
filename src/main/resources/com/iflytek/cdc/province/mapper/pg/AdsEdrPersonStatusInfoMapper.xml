<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrPersonStatusInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsEdrPersonStatusInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_edr_person_status_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="create_org" jdbcType="VARCHAR" property="createOrg" />
    <result column="create_org_id" jdbcType="VARCHAR" property="createOrgId" />
    <result column="create_org_name" jdbcType="VARCHAR" property="createOrgName" />
    <result column="create_org_detail" jdbcType="VARCHAR" property="createOrgDetail" />
    <result column="create_org_detail_std" jdbcType="VARCHAR" property="createOrgDetailStd" />
    <result column="create_org_province_code" jdbcType="VARCHAR" property="createOrgProvinceCode" />
    <result column="create_org_province" jdbcType="VARCHAR" property="createOrgProvince" />
    <result column="create_org_city_code" jdbcType="VARCHAR" property="createOrgCityCode" />
    <result column="create_org_city" jdbcType="VARCHAR" property="createOrgCity" />
    <result column="create_org_district_code" jdbcType="VARCHAR" property="createOrgDistrictCode" />
    <result column="create_org_district" jdbcType="VARCHAR" property="createOrgDistrict" />
    <result column="create_org_func_district_code" jdbcType="VARCHAR" property="createOrgFuncDistrictCode" />
    <result column="create_org_func_district" jdbcType="VARCHAR" property="createOrgFuncDistrict" />
    <result column="create_org_street_code" jdbcType="VARCHAR" property="createOrgStreetCode" />
    <result column="create_org_street" jdbcType="VARCHAR" property="createOrgStreet" />
    <result column="create_org_street_longitude" jdbcType="NUMERIC" property="createOrgStreetLongitude" />
    <result column="create_org_street_latitude" jdbcType="NUMERIC" property="createOrgStreetLatitude" />
    <result column="create_org_longitude" jdbcType="NUMERIC" property="createOrgLongitude" />
    <result column="create_org_latitude" jdbcType="NUMERIC" property="createOrgLatitude" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="education_code" jdbcType="VARCHAR" property="educationCode" />
    <result column="education" jdbcType="VARCHAR" property="education" />
    <result column="workunit" jdbcType="VARCHAR" property="workunit" />
    <result column="workunit_addr_std" jdbcType="VARCHAR" property="workunitAddrStd" />
    <result column="workunit_province_code" jdbcType="VARCHAR" property="workunitProvinceCode" />
    <result column="workunit_province" jdbcType="VARCHAR" property="workunitProvince" />
    <result column="workunit_city_code" jdbcType="VARCHAR" property="workunitCityCode" />
    <result column="workunit_city" jdbcType="VARCHAR" property="workunitCity" />
    <result column="workunit_district_code" jdbcType="VARCHAR" property="workunitDistrictCode" />
    <result column="workunit_district" jdbcType="VARCHAR" property="workunitDistrict" />
    <result column="workunit_func_district_code" jdbcType="VARCHAR" property="workunitFuncDistrictCode" />
    <result column="workunit_func_district" jdbcType="VARCHAR" property="workunitFuncDistrict" />
    <result column="workunit_street_code" jdbcType="VARCHAR" property="workunitStreetCode" />
    <result column="workunit_street" jdbcType="VARCHAR" property="workunitStreet" />
    <result column="workunit_street_longitude" jdbcType="NUMERIC" property="workunitStreetLongitude" />
    <result column="workunit_street_latitude" jdbcType="NUMERIC" property="workunitStreetLatitude" />
    <result column="workunit_longitude" jdbcType="NUMERIC" property="workunitLongitude" />
    <result column="workunit_latitude" jdbcType="NUMERIC" property="workunitLatitude" />
    <result column="diagnose_state_code" jdbcType="VARCHAR" property="diagnoseStateCode" />
    <result column="diagnose_state" jdbcType="VARCHAR" property="diagnoseState" />
    <result column="risk_rating_code" jdbcType="VARCHAR" property="riskRatingCode" />
    <result column="risk_rating" jdbcType="VARCHAR" property="riskRating" />
    <result column="disease" jdbcType="VARCHAR" property="disease" />
    <result column="screening_status_code" jdbcType="VARCHAR" property="screeningStatusCode" />
    <result column="screening_status" jdbcType="VARCHAR" property="screeningStatus" />
    <result column="flow_status_code" jdbcType="VARCHAR" property="flowStatusCode" />
    <result column="flow_status" jdbcType="VARCHAR" property="flowStatus" />
    <result column="nultitude_type_code" jdbcType="VARCHAR" property="nultitudeTypeCode" />
    <result column="nultitude_type" jdbcType="VARCHAR" property="nultitudeType" />
    <result column="allergy_drug" jdbcType="VARCHAR" property="allergyDrug" />
    <result column="disease_code" jdbcType="VARCHAR" property="diseaseCode" />
    <result column="diag_std_code" jdbcType="VARCHAR" property="diagStdCode" />
    <result column="diag_std_name" jdbcType="VARCHAR" property="diagStdName" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="mpi_id" jdbcType="VARCHAR" property="mpiId" />
    <result column="update_org" jdbcType="VARCHAR" property="updateOrg" />
    <result column="update_org_name" jdbcType="VARCHAR" property="updateOrgName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, etl_create_datetime, etl_update_datetime, create_org, create_org_id, create_org_name, 
    create_org_detail, create_org_detail_std, create_org_province_code, create_org_province, 
    create_org_city_code, create_org_city, create_org_district_code, create_org_district, 
    create_org_func_district_code, create_org_func_district, create_org_street_code, 
    create_org_street, create_org_street_longitude, create_org_street_latitude, create_org_longitude, 
    create_org_latitude, event_id, empi_id, education_code, education, workunit, workunit_addr_std, 
    workunit_province_code, workunit_province, workunit_city_code, workunit_city, workunit_district_code, 
    workunit_district, workunit_func_district_code, workunit_func_district, workunit_street_code, 
    workunit_street, workunit_street_longitude, workunit_street_latitude, workunit_longitude, 
    workunit_latitude, diagnose_state_code, diagnose_state, risk_rating_code, risk_rating, 
    disease, screening_status_code, screening_status, flow_status_code, flow_status, 
    nultitude_type_code, nultitude_type, allergy_drug, disease_code, diag_std_code, diag_std_name, 
    create_user, create_time, update_user, update_time, mpi_id, update_org, update_org_name
  </sql>
  <select id="getAdsEdrProcessTagList" resultType="com.iflytek.cdc.province.model.vo.edrcase.AdsEdrProcessTagVO">
    select
    distinct
    dii.infected_sub_name,
    aepsi.event_id as id
    from
    dim.dim_infected_info dii,
    dws.dws_process_tag_relation dptr,
    dws.dws_process_tag dpt,
    ads.ads_edr_person_status_info aepsi
    where
    dii.infected_sub_id = dptr.disease_code
    and dptr.disease_type ='infectious'
    and dptr.tag_id =dpt.tag_id
    and dpt.event_id =aepsi.event_id
    and aepsi.empi_id = #{empi_id}
  </select>
  <select id="getAdsEdrPersonStatusInfoList" resultType="com.iflytek.cdc.province.model.vo.edrcase.AdsEdrPersonStatusInfoVO">
    select distinct aepsi.create_org_name ,coalesce(aecdi.diagnose_time,aeidi.serial_number) diagnose_time,aepsi.event_id
    from ads.ads_edr_person_status_info aepsi left join  ads.ads_edr_confirm_diag_info aecdi
    on aepsi.event_id =aecdi.event_id  left join ads.ads_edr_init_diag_info aeidi
    on aepsi.event_id =aeidi.event_id
    where 1=1
    <if test="empiId != null and empiId != ''">
      and aepsi.empi_id=#{empiId}
    </if>
    <if test="eventIds != null and eventIds.length>0 ">
      and aepsi.event_id in
      <foreach item="eventId" collection="eventIds" open="(" separator="," close=")">
        #{eventId}
      </foreach>
    </if>
    order by diagnose_time desc
  </select>
</mapper>