<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcdmCheckRecordMapper">

    <insert id="saveCheckRecord">
        insert into tb_cdcdm_check_record
        (id, process_id, is_checked, checker, check_time, lv_medical_id, li_identify_id, disease_process_id, li_disease_code,
        li_disease_name, li_medical_id, disease_type)
        values
        (#{id, jdbcType=VARCHAR}, #{processId, jdbcType=VARCHAR}, #{isChecked, jdbcType=VARCHAR},
        #{checker, jdbcType=VARCHAR}, #{checkTime, jdbcType=TIMESTAMP}, #{lvMedicalId, jdbcType=VARCHAR},
        #{liIdentifyId, jdbcType=VARCHAR}, #{diseaseProcessId, jdbcType=VARCHAR}, #{liDiseaseCode, jdbcType=VARCHAR},
        #{liDiseaseName, jdbcType=VARCHAR}, #{liMedicalId, jdbcType=VARCHAR}, #{diseaseType, jdbcType=VARCHAR})
        on conflict(disease_process_id)
        do update set
        is_checked          = excluded.is_checked,
        li_disease_code     = excluded.li_disease_code,
        li_disease_name     = excluded.li_disease_name,
        li_medical_id       = excluded.li_medical_id,
        disease_type        = excluded.disease_type,
        checker             = excluded.checker,
        check_time          = excluded.check_time
    </insert>

</mapper>