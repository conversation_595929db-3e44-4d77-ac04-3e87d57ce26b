<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrLifeInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsEdrLifeInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_edr_life_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="archive_id" jdbcType="VARCHAR" property="archiveId" />
    <result column="life_id" jdbcType="VARCHAR" property="lifeId" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="occur_time" jdbcType="TIMESTAMP" property="occurTime" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="delete_flag" jdbcType="VARCHAR" property="deleteFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_id" jdbcType="VARCHAR" property="updateId" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, archive_id, life_id, "label", "value", occur_time, "status", delete_flag, create_time, 
    creator_id, creator, update_time, update_id, updater, etl_create_datetime, etl_update_datetime
  </sql>

    <select id="getRecordLifeCycle" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO">
        select
        occur_time as date,
        label as type,
        value as value
        from ads.ads_edr_life_info
        where archive_id = #{id}
        order by occur_time
    </select>

</mapper>