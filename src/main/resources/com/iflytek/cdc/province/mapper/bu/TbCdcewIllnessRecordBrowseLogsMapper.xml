<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcewIllnessRecordBrowseLogsMapper">

    <sql id="Base_Column_List">
        id, archive_id, patient_id, patient_name, browse_person_id, browse_person, browse_date, status, create_time,
        creator_id, creator, permit_living_area_code, permit_company_area_code, permit_org_area_code
    </sql>

    <sql id="chooseAddress">
        and (exists(
        select 1 from tb_cdcew_region_nation n
        where l.permit_living_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and n.province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and n.city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and n.district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>)
        or
        exists(
        select 1 from tb_cdcew_region_nation n
        where l.permit_company_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and n.province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and n.city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and n.district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>)
        or exists(
        select 1 from tb_cdcew_region_nation n
        where l.permit_org_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and n.province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and n.city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and n.district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>))
    </sql>

    <sql id="criteria">
        <include refid="chooseAddress"/>
        <if test="startDate != null and endDate != null">
            and browse_date between #{startDate} and #{endDate}
        </if>
        <if test="retrievalPerson != null and retrievalPerson != ''">
            and browse_person like concat('%', #{retrievalPerson}, '%')
        </if>
        <if test="retrievalStatus != null">
            and status = #{retrievalStatus}
        </if>
        <if test="recordId != null and recordId != ''">
            and record_id = #{recordId}
        </if>
        <if test="patientInfo != null and patientInfo != ''">
            and (patient_name like concat('%', #{patientInfo}, '%') or
            patient_id like concat('%', #{patientInfo}, '%'))
        </if>
    </sql>

    <select id="getRetrievalLogs"
            resultType="com.iflytek.cdc.province.entity.bu.TbCdcewIllnessRecordBrowseLogs">
        select <include refid="Base_Column_List"/>
        from tb_cdcew_illness_record_browse_logs l
        where 1=1
        <include refid="criteria"/>
        order by browse_date desc
    </select>

    <select id="retrievalLogsStat" resultType="com.iflytek.cdc.province.model.edr.vo.RetrievalLogsStatVO">
        select
        count(id) as retrievalTotalCount,
        coalesce(sum(case when browse_date >= current_date - interval '7 days' then 1 else 0 end), 0) as recent7DaysRetrievalCount,
        coalesce(sum(case when browse_date >= current_date - interval '1 day' and browse_date &lt; current_date then 1 else 0 end), 0) as yesterdayRetrievalCount,
        coalesce(sum(case when browse_date >= current_date and browse_date &lt; current_date + interval '1 day' then 1 else 0 end), 0) aS todayRetrievalCount
        from tb_cdcew_illness_record_browse_logs l
        where 1=1
        <include refid="chooseAddress"/>
    </select>

    <select id="countRetrievalLogs" resultType="java.lang.Integer">
        select count(*)
        from tb_cdcew_illness_record_browse_logs l
        where 1=1
        <include refid="criteria"/>
    </select>

</mapper>