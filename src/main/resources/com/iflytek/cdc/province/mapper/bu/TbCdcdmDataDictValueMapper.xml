<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcdmDataDictValueMapper">
    <select id="listByDataDictCode" resultType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataDictValue">
        select
          tcddv.*
        from
          app.tb_cdcdm_data_dict tcdd
        join app.tb_cdcdm_data_dict_value tcddv on tcddv.data_dict_id = tcdd.id
        where tcdd.code = #{code}
    </select>
</mapper>