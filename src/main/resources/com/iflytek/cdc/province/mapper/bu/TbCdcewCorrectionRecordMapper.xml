<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcewCorrectionRecordMapper">

    <sql id="chooseTime">
        <if test="startDate != null and endDate != null">
            <choose>
                <when test="dateType == 'reportTime'">
                    and report_time between #{startDate} and #{endDate}
                </when>
                <otherwise>
                    and create_time between #{startDate} and #{endDate}
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id="chooseAddress">
        and (exists(
        select 1 from tb_cdcew_region_nation n
        where r.permit_living_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and n.province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and n.city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and n.district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>)
        or
        exists(
        select 1 from tb_cdcew_region_nation n
        where r.permit_company_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and n.province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and n.city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and n.district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>)
        or exists(
        select 1 from tb_cdcew_region_nation n
        where r.permit_org_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and n.province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and n.city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and n.district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>))
    </sql>

    <sql id="criteria">
        <include refid="chooseTime"/>
        <if test="diseaseType != null and diseaseType != ''">
            and r.disease_type = #{diseaseType, jdbcType=VARCHAR}
        </if>
        <if test="diseaseCode != null and diseaseCode != ''">
            and r.disease_code = #{diseaseCode, jdbcType=VARCHAR}
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            and r.disease_name = #{diseaseName, jdbcType=VARCHAR}
        </if>
        <if test="fieldName != null and fieldName != ''">
            and r.field_name = #{fieldName, jdbcType=VARCHAR}
        </if>
        <if test="fieldNameList != null and fieldNameList.size() &gt; 0">
            and r.field_name in
            <foreach collection="fieldNameList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="creator != null and creator != ''">
            and r.creator = #{creator, jdbcType=VARCHAR}
        </if>
        <if test="processInfo != null and processInfo != ''">
            and (r.process_id like concat('%', #{processInfo, jdbcType=VARCHAR}, '%')
            or r.patient_name like concat('%', #{processInfo, jdbcType=VARCHAR}, '%'))
        </if>
        <if test="reportId != null and reportId != ''">
            and r.report_id = #{reportId, jdbcType=VARCHAR}
        </if>
        <if test="queryKey != null and queryKey != ''">
            and (r.process_id like concat('%', #{queryKey, jdbcType=VARCHAR}, '%')
            or r.patient_name like concat('%', #{queryKey, jdbcType=VARCHAR}, '%'))
        </if>
        <include refid="chooseAddress"/>
    </sql>

    <insert id="saveCaseCorrection">
        insert into tb_cdcew_correction_record
        (id, permit_living_area_code, permit_company_area_code, permit_org_area_code, model_id, field_id, field_name,
        origin_value, new_value, disease_type, process_id, patient_name, report_id, report_time, create_time, creator,
        creator_id)
        values
        (#{id}, #{permitLivingAreaCode}, #{permitCompanyAreaCode}, #{permitOrgAreaCode}, #{modelId}, #{fieldId},
        #{fieldName}, #{originValue}, #{newValue}, #{diseaseType}, #{processId}, #{patientName}, #{reportId},
        #{reportTime}, #{createTime}, #{creator}, #{creatorId})
    </insert>

    <insert id="saveBatchCorrection">
        insert into tb_cdcew_correction_record
        (id, permit_living_area_code, permit_company_area_code, permit_org_area_code, model_id, field_id, field_name,
        origin_value, new_value, disease_type, process_id, patient_name, report_id, report_time, create_time, creator,
        creator_id, disease_code, disease_name)
        values
        <foreach collection="recordList" item="item" separator=",">
            (#{item.id}, #{item.permitLivingAreaCode}, #{item.permitCompanyAreaCode}, #{item.permitOrgAreaCode},
            #{item.modelId}, #{item.fieldId}, #{item.fieldName}, #{item.originValue}, #{item.newValue},
            #{item.diseaseType}, #{item.processId}, #{item.patientName}, #{item.reportId}, #{item.reportTime},
            #{item.createTime}, #{item.creator}, #{item.creatorId}, #{item.diseaseCode}, #{item.diseaseName})
        </foreach>
    </insert>

    <select id="getCorrectionFieldList" resultType="java.lang.String">
        select distinct r.field_name
        from tb_cdcew_correction_record r
        where 1=1
        <include refid="criteria"/>
        order by r.field_name
    </select>

    <select id="getCorrectionRecordList" resultType="com.iflytek.cdc.province.model.pandemic.vo.CorrectionRecordVO">
        select
        r.process_id,
        r.patient_name,
        r.report_id,
        r.report_time,
        r.field_name,
        r.origin_value,
        r.new_value,
        r.create_time,
        r.creator
        from tb_cdcew_correction_record r
        where 1=1
        <include refid="criteria"/>
        order by r.create_time desc
    </select>

    <select id="getCorrectFieldRanking" resultType="com.iflytek.cdc.province.model.pandemic.vo.CorrectFieldVO">
        select r.field_name, count(*)
        from tb_cdcew_correction_record r
        where 1=1
        <include refid="criteria"/>
        group by r.field_name
    </select>

    <select id="getCorrectFieldStat" resultType="com.iflytek.cdc.province.model.pandemic.vo.CorrectFieldVO">
        select r.field_name, r.origin_value, r.new_value, count(*)
        from tb_cdcew_correction_record r
        where 1=1
        <include refid="criteria"/>
        group by r.field_name, r.origin_value, r.new_value
    </select>

    <select id="getCorrectionRecordByReportId"
            resultType="com.iflytek.cdc.province.model.pandemic.vo.CorrectionRecordVO">
        select
        r.field_name,
        r.origin_value,
        r.new_value,
        r.create_time,
        r.creator
        from tb_cdcew_correction_record r
        where report_id = #{reportId}
        <if test="diseaseType != null and diseaseType != ''">
            and disease_type = #{diseaseType}
        </if>
        order by r.create_time desc
    </select>

</mapper>