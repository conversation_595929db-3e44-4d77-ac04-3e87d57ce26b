<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcewProcessReportCardMapper">

    <sql id="Base_Column_List">
        id, report_id, process_id, disease_type, report_time, patient_id, patient_name, sex_name, birth_day,
        permit_living_area_code, permit_company_area_code, permit_org_area_code, disease_code, disease_name,
        diagnose_status, diagnose_time, out_flag, severe_flag, check_identify_status, check_process_status,
        onset_time, visit_time, check_time, timeout_status, update_time, updater_id, updater
    </sql>

    <sql id="chooseTime">
        <choose>
            <when test="dateType == 'diagnoseTime'">
                <if test="startDate != null">
                    and r.diagnose_time &gt;= #{startDate}
                </if>
                <if test="endDate != null">
                    and r.diagnose_time &lt;= #{endDate}
                </if>
            </when>
            <when test="dateType == 'checkTime'">
                <if test="startDate != null">
                    and r.check_time &gt;= #{startDate}
                </if>
                <if test="endDate != null">
                    and r.check_time &lt;= #{endDate}
                </if>
            </when>
            <when test="dateType == 'onsetTime'">
                <if test="startDate != null">
                    and r.onset_time &gt;= #{startDate}
                </if>
                <if test="endDate != null">
                    and r.onset_time &lt;= #{endDate}
                </if>
            </when>
            <when test="dateType == 'visitTime'">
                <if test="startDate != null">
                    and r.visit_time &gt;= #{startDate}
                </if>
                <if test="endDate != null">
                    and r.visit_time &lt;= #{endDate}
                </if>
            </when>
            <otherwise>
                <if test="startDate != null">
                    and r.report_time &gt;= #{startDate}
                </if>
                <if test="endDate != null">
                    and r.report_time &lt;= #{endDate}
                </if>
            </otherwise>
        </choose>
    </sql>
    
    <sql id="permitCriteria">
        and (exists(
        select 1 from tb_cdcew_region_nation n
        where r.permit_living_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and n.province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and n.city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and n.district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>)
        or
        exists(
        select 1 from tb_cdcew_region_nation n
        where r.permit_company_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and n.province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and n.city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and n.district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>)
        or exists(
        select 1 from tb_cdcew_region_nation n
        where r.permit_org_area_code = n.region_code
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and n.province_code in
            <foreach collection="provinceCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and n.city_code in
            <foreach collection="cityCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and n.district_code in
            <foreach collection="districtCodes" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>))
    </sql>
    
    <sql id="criteria">
        <include refid="chooseTime"/>
        <include refid="permitCriteria"/>
        <if test="diseaseCodeList != null and diseaseCodeList.size() &gt; 0">
            and r.disease_code in
            <foreach collection="diseaseCodeList" separator="," close=")" open="(" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="diagnoseStatus != null and diagnoseStatus != ''">
            and r.diagnose_status = #{diagnoseStatus}
        </if>
        <if test="checkIdentifyStatusList != null and checkIdentifyStatusList.size() &gt; 0">
            and r.check_identify_status in
            <foreach collection="checkIdentifyStatusList" separator="," close=")" open="(" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="checkProcessStatus != null and checkProcessStatus != ''">
            and r.check_process_status = #{checkProcessStatus}
        </if>
        <if test="checkIntimeFlag != null and checkIntimeFlag != ''">
            and r.timeout_status = #{checkIntimeFlag}
        </if>
        <if test="outFlag != null and outFlag != ''">
            and r.out_flag = #{outFlag}
        </if>
        <if test="severeFlag != null and severeFlag != ''">
            and r.severe_flag = #{severeFlag}
        </if>
        <if test="patientName != null and patientName != ''">
            and r.patient_name like concat('%', #{patientName}, '%')
        </if>
        <if test="processId != null and processId != ''">
            and r.process_id like concat('%', #{processId}, '%')
        </if>
        <if test="diseaseType != null and diseaseType != ''">
            and r.disease_type = #{diseaseType}
        </if>
        <if test="reportId != null and reportId != ''">
            and r.report_id like concat('%', #{reportId}, '%')
        </if>
        <if test="keyword != null and keyword != ''">
            and ( r.process_id like  concat('%', #{keyword}, '%')
                 or r.report_id like  concat('%', #{keyword}, '%')
                 or r.disease_name like  concat('%', #{keyword}, '%')
            )
        </if>

        <include refid="levelCriteria"/>
    </sql>

    <sql id="levelCriteria">
        and (
        <!-- 处理 level1 相关条件 -->
        <if test="(level1DiseaseCodeList != null and level1DiseaseCodeList.size()  > 0)
                or (level1IdentifyStatusList != null and level1IdentifyStatusList.size()  > 0)">
            (<if test="level1DiseaseCodeList != null and level1DiseaseCodeList.size()  > 0">
                r.disease_code  in
                <foreach collection="level1DiseaseCodeList" separator="," close=")" open="(" item="item" index="index">
                    #{item}
                </foreach>
            </if>
            <if test="level1IdentifyStatusList != null and level1IdentifyStatusList.size()  > 0">
                and
            </if>
            <if test="level1IdentifyStatusList != null and level1IdentifyStatusList.size()  > 0">
                r.check_identify_status  in
                <foreach collection="level1IdentifyStatusList" separator="," close=")" open="(" item="item" index="index">
                    #{item}
                </foreach>
            </if>)
            <!-- 如果 level2 也有条件，使用 OR 连接 -->
            <if test="(level2DiseaseCodeList != null and level2DiseaseCodeList.size()  > 0)
                  or (level2IdentifyStatusList != null and level2IdentifyStatusList.size()  > 0)">
                or
            </if>
        </if>
        <!-- 处理 level2 相关条件 -->
        <if test="(level2DiseaseCodeList != null and level2DiseaseCodeList.size()  > 0)
                or (level2IdentifyStatusList != null and level2IdentifyStatusList.size()  > 0)">
            (<if test="level2DiseaseCodeList != null and level2DiseaseCodeList.size()  > 0">
                r.disease_code  in
                <foreach collection="level2DiseaseCodeList" separator="," close=")" open="(" item="item" index="index">
                    #{item}
                </foreach>
            </if>
            <if test="level2IdentifyStatusList != null and level2IdentifyStatusList.size()  > 0">
                and
            </if>
            <if test="level2IdentifyStatusList != null and level2IdentifyStatusList.size()  > 0">
                r.check_identify_status  in
                <foreach collection="level2IdentifyStatusList" separator="," close=")" open="(" item="item" index="index">
                    #{item}
                </foreach>
            </if>)
        </if>
        )
    </sql>

    <update id="updateReportBy">
        update tb_cdcew_process_report_card
        <set>
            <if test="checkIdentifyStatus != null and checkIdentifyStatus != ''">
                check_identify_status = #{checkIdentifyStatus},
            </if>
            <if test="checkProcessStatus != null and checkProcessStatus != ''">
                check_process_status = #{checkProcessStatus},
            </if>
            <if test="checkTime != null">
                check_time = #{checkTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updaterId != null and updaterId != ''">
                updater_id = #{updaterId},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
        </set>
        where report_id = #{reportId} and disease_type = #{diseaseType}
    </update>

    <select id="getCheckTaskList" resultType="com.iflytek.cdc.province.model.pandemic.vo.CheckTaskRecordVO">
        select <include refid="Base_Column_List"/>, date_part('year', AGE(visit_time, birth_day)) as patientAge
        from tb_cdcew_process_report_card r
        where 1=1
        <include refid="criteria"/>
    </select>

    <select id="getReportCardBy" resultType="com.iflytek.cdc.province.entity.bu.TbCdcewProcessReportCard">
        select <include refid="Base_Column_List"/>
        from tb_cdcew_process_report_card r
        where report_id = #{reportId}
        <if test="diseaseType != null and diseaseType != ''">
            and disease_type = #{diseaseType}
        </if>
    </select>

    <select id="getProcessWaitCheckReport"
            resultType="com.iflytek.cdc.province.model.pandemic.vo.CheckTaskRecordVO">
        select <include refid="Base_Column_List"/>
        from tb_cdcew_process_report_card r
        where check_process_status != 'checkedCompleted'
        <if test="processId != null and processId != ''">
            and r.process_id like concat('%', #{processId}, '%')
        </if>
        limit 1
    </select>

    <select id="getTaskRecordVOListByIdList"
            resultType="com.iflytek.cdc.province.model.pandemic.vo.CheckTaskRecordVO">
        select <include refid="Base_Column_List"/>
        from tb_cdcew_process_report_card r
        where 1=1
        <if test="diseaseType != null and diseaseType != ''">
            and disease_type = #{diseaseType}
        </if>
        <if test="idList != null and idList.size() > 0">
            and report_id in
            <foreach collection="idList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>