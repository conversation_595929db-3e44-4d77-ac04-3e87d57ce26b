<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.DimSymptomMulInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.dim.DimSymptomMulInfo">
    <!--@mbg.generated-->
    <!--@Table dim.dim_symptom_mul_info-->
    <id column="tag" jdbcType="VARCHAR" property="tag" />
    <id column="tag_value_type" jdbcType="VARCHAR" property="tagValueType" />
    <id column="tag_value" jdbcType="VARCHAR" property="tagValue" />
    <id column="data_source" jdbcType="VARCHAR" property="dataSource" />
    <id column="tag_class" jdbcType="VARCHAR" property="tagClass" />
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    tag, tag_value_type, tag_value, data_source, tag_class, id, etl_create_datetime, 
    etl_update_datetime
  </sql>

  <select id="getDistinctTagsByDataSource" resultType="java.lang.String">
    select distinct tag
    from dim.dim_symptom_mul_info d
    <where>
      <if test="dataSources != null and dataSources.size() != 0">
         data_source in
        <foreach collection="dataSources" item="item" separator="," open="(" close=")">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
    </where>
  </select>

  <select id="getTagInfosBy" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from dim.dim_symptom_mul_info d
    <where>
      <if test="dataSources != null and dataSources.size() != 0">
        data_source in
        <foreach collection="dataSources" item="item" separator="," open="(" close=")">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
      <if test="tag != null">
        and tag = #{tag,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper>