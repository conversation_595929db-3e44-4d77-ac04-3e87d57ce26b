<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsRepInfectReportInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsRepInfectReportInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_rep_infect_report_info-->
    <id column="ms_infect_report_id" jdbcType="VARCHAR" property="msInfectReportId" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="patient_id" jdbcType="VARCHAR" property="patientId" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="patient_name" jdbcType="VARCHAR" property="patientName" />
    <result column="patient_identity_type_code" jdbcType="VARCHAR" property="patientIdentityTypeCode" />
    <result column="patient_identity_type" jdbcType="VARCHAR" property="patientIdentityType" />
    <result column="patient_identity_no" jdbcType="VARCHAR" property="patientIdentityNo" />
    <result column="patient_sex_code" jdbcType="VARCHAR" property="patientSexCode" />
    <result column="patient_sex_name" jdbcType="VARCHAR" property="patientSexName" />
    <result column="patient_birth_day" jdbcType="DATE" property="patientBirthDay" />
    <result column="age" jdbcType="SMALLINT" property="age" />
    <result column="age_unit" jdbcType="VARCHAR" property="ageUnit" />
    <result column="age_group" jdbcType="VARCHAR" property="ageGroup" />
    <result column="patient_nationality_code" jdbcType="VARCHAR" property="patientNationalityCode" />
    <result column="patient_nationality_name" jdbcType="VARCHAR" property="patientNationalityName" />
    <result column="patient_ethnic_code" jdbcType="VARCHAR" property="patientEthnicCode" />
    <result column="patient_ethnic_name" jdbcType="VARCHAR" property="patientEthnicName" />
    <result column="patient_permanent_addr_type_code" jdbcType="VARCHAR" property="patientPermanentAddrTypeCode" />
    <result column="patient_permanent_addr_type_name" jdbcType="VARCHAR" property="patientPermanentAddrTypeName" />
    <result column="patient_permanent_addr_code" jdbcType="VARCHAR" property="patientPermanentAddrCode" />
    <result column="patient_permanent_addr_name" jdbcType="VARCHAR" property="patientPermanentAddrName" />
    <result column="patient_permanent_addr_detail" jdbcType="VARCHAR" property="patientPermanentAddrDetail" />
    <result column="patient_marry_code" jdbcType="VARCHAR" property="patientMarryCode" />
    <result column="patient_marry_name" jdbcType="VARCHAR" property="patientMarryName" />
    <result column="patient_educate_code" jdbcType="VARCHAR" property="patientEducateCode" />
    <result column="patient_educate_name" jdbcType="VARCHAR" property="patientEducateName" />
    <result column="patient_phone" jdbcType="VARCHAR" property="patientPhone" />
    <result column="contacts_rel_code" jdbcType="VARCHAR" property="contactsRelCode" />
    <result column="patient_contact_name" jdbcType="VARCHAR" property="patientContactName" />
    <result column="patient_contact_phone" jdbcType="VARCHAR" property="patientContactPhone" />
    <result column="living_area_code" jdbcType="VARCHAR" property="livingAreaCode" />
    <result column="living_area_name" jdbcType="VARCHAR" property="livingAreaName" />
    <result column="current_addr_type_code" jdbcType="VARCHAR" property="currentAddrTypeCode" />
    <result column="current_addr_type_name" jdbcType="VARCHAR" property="currentAddrTypeName" />
    <result column="living_addr_detail" jdbcType="VARCHAR" property="livingAddrDetail" />
    <result column="living_addr_detail_std" jdbcType="VARCHAR" property="livingAddrDetailStd" />
    <result column="living_addr_province_code" jdbcType="VARCHAR" property="livingAddrProvinceCode" />
    <result column="living_addr_province_name" jdbcType="VARCHAR" property="livingAddrProvinceName" />
    <result column="living_addr_city_code" jdbcType="VARCHAR" property="livingAddrCityCode" />
    <result column="living_addr_city_name" jdbcType="VARCHAR" property="livingAddrCityName" />
    <result column="living_addr_district_code" jdbcType="VARCHAR" property="livingAddrDistrictCode" />
    <result column="living_addr_district_name" jdbcType="VARCHAR" property="livingAddrDistrictName" />
    <result column="living_addr_func_district_code" jdbcType="VARCHAR" property="livingAddrFuncDistrictCode" />
    <result column="living_addr_func_district_name" jdbcType="VARCHAR" property="livingAddrFuncDistrictName" />
    <result column="living_addr_street_code" jdbcType="VARCHAR" property="livingAddrStreetCode" />
    <result column="living_addr_street_name" jdbcType="VARCHAR" property="livingAddrStreetName" />
    <result column="living_addr_street_longitude" jdbcType="NUMERIC" property="livingAddrStreetLongitude" />
    <result column="living_addr_street_latitude" jdbcType="NUMERIC" property="livingAddrStreetLatitude" />
    <result column="living_addr_longitude" jdbcType="NUMERIC" property="livingAddrLongitude" />
    <result column="living_addr_latitude" jdbcType="NUMERIC" property="livingAddrLatitude" />
    <result column="living_addr_poi" jdbcType="VARCHAR" property="livingAddrPoi" />
    <result column="living_addr_type" jdbcType="VARCHAR" property="livingAddrType" />
    <result column="company" jdbcType="VARCHAR" property="company" />
    <result column="company_addr_std" jdbcType="VARCHAR" property="companyAddrStd" />
    <result column="company_province_code" jdbcType="VARCHAR" property="companyProvinceCode" />
    <result column="company_province_name" jdbcType="VARCHAR" property="companyProvinceName" />
    <result column="company_city_code" jdbcType="VARCHAR" property="companyCityCode" />
    <result column="company_city_name" jdbcType="VARCHAR" property="companyCityName" />
    <result column="company_district_code" jdbcType="VARCHAR" property="companyDistrictCode" />
    <result column="company_district_name" jdbcType="VARCHAR" property="companyDistrictName" />
    <result column="company_func_district_code" jdbcType="VARCHAR" property="companyFuncDistrictCode" />
    <result column="company_func_district_name" jdbcType="VARCHAR" property="companyFuncDistrictName" />
    <result column="company_street_code" jdbcType="VARCHAR" property="companyStreetCode" />
    <result column="company_street_name" jdbcType="VARCHAR" property="companyStreetName" />
    <result column="company_street_longitude" jdbcType="NUMERIC" property="companyStreetLongitude" />
    <result column="company_street_latitude" jdbcType="NUMERIC" property="companyStreetLatitude" />
    <result column="company_longitude" jdbcType="NUMERIC" property="companyLongitude" />
    <result column="company_latitude" jdbcType="NUMERIC" property="companyLatitude" />
    <result column="nultitude_type_code" jdbcType="VARCHAR" property="nultitudeTypeCode" />
    <result column="nultitude_type_name" jdbcType="VARCHAR" property="nultitudeTypeName" />
    <result column="nultitude_type_other" jdbcType="VARCHAR" property="nultitudeTypeOther" />
    <result column="addr_belong_type_code" jdbcType="VARCHAR" property="addrBelongTypeCode" />
    <result column="addr_belong_type" jdbcType="VARCHAR" property="addrBelongType" />
    <result column="activity_type_code" jdbcType="VARCHAR" property="activityTypeCode" />
    <result column="activity_type_name" jdbcType="VARCHAR" property="activityTypeName" />
    <result column="serial_number" jdbcType="VARCHAR" property="serialNumber" />
    <result column="visit_addr_areatype2_code" jdbcType="VARCHAR" property="visitAddrAreatype2Code" />
    <result column="visit_addr_areatype2_name" jdbcType="VARCHAR" property="visitAddrAreatype2Name" />
    <result column="visit_org_code" jdbcType="VARCHAR" property="visitOrgCode" />
    <result column="visit_org_name" jdbcType="VARCHAR" property="visitOrgName" />
    <result column="dept_code" jdbcType="VARCHAR" property="deptCode" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="dept_other" jdbcType="VARCHAR" property="deptOther" />
    <result column="visit_addr_code" jdbcType="VARCHAR" property="visitAddrCode" />
    <result column="visit_addr_name" jdbcType="VARCHAR" property="visitAddrName" />
    <result column="visit_addr_detail" jdbcType="VARCHAR" property="visitAddrDetail" />
    <result column="visit_addr_detail_std" jdbcType="VARCHAR" property="visitAddrDetailStd" />
    <result column="visit_addr_province_code" jdbcType="VARCHAR" property="visitAddrProvinceCode" />
    <result column="visit_addr_province_name" jdbcType="VARCHAR" property="visitAddrProvinceName" />
    <result column="visit_addr_city_code" jdbcType="VARCHAR" property="visitAddrCityCode" />
    <result column="visit_addr_city_name" jdbcType="VARCHAR" property="visitAddrCityName" />
    <result column="visit_addr_district_code" jdbcType="VARCHAR" property="visitAddrDistrictCode" />
    <result column="visit_addr_district_name" jdbcType="VARCHAR" property="visitAddrDistrictName" />
    <result column="visit_addr_func_district_code" jdbcType="VARCHAR" property="visitAddrFuncDistrictCode" />
    <result column="visit_addr_func_district_name" jdbcType="VARCHAR" property="visitAddrFuncDistrictName" />
    <result column="visit_addr_street_code" jdbcType="VARCHAR" property="visitAddrStreetCode" />
    <result column="visit_addr_street_name" jdbcType="VARCHAR" property="visitAddrStreetName" />
    <result column="visit_addr_street_longitude" jdbcType="NUMERIC" property="visitAddrStreetLongitude" />
    <result column="visit_addr_street_latitude" jdbcType="NUMERIC" property="visitAddrStreetLatitude" />
    <result column="visit_addr_longitude" jdbcType="NUMERIC" property="visitAddrLongitude" />
    <result column="visit_addr_latitude" jdbcType="NUMERIC" property="visitAddrLatitude" />
    <result column="visit_addr_poi" jdbcType="VARCHAR" property="visitAddrPoi" />
    <result column="visit_addr_type" jdbcType="VARCHAR" property="visitAddrType" />
    <result column="cases_type_code" jdbcType="VARCHAR" property="casesTypeCode" />
    <result column="cases_type_name" jdbcType="VARCHAR" property="casesTypeName" />
    <result column="onset_datetime" jdbcType="TIMESTAMP" property="onsetDatetime" />
    <result column="clinical_other" jdbcType="VARCHAR" property="clinicalOther" />
    <result column="fever_num" jdbcType="VARCHAR" property="feverNum" />
    <result column="visit_datetime" jdbcType="TIMESTAMP" property="visitDatetime" />
    <result column="diag_datetime" jdbcType="TIMESTAMP" property="diagDatetime" />
    <result column="audit_disease_name" jdbcType="VARCHAR" property="auditDiseaseName" />
    <result column="audit_disease_code" jdbcType="VARCHAR" property="auditDiseaseCode" />
    <result column="audit_create_date" jdbcType="TIMESTAMP" property="auditCreateDate" />
    <result column="diag_code" jdbcType="VARCHAR" property="diagCode" />
    <result column="diag_name" jdbcType="VARCHAR" property="diagName" />
    <result column="diag_other" jdbcType="VARCHAR" property="diagOther" />
    <result column="diag_std_code" jdbcType="VARCHAR" property="diagStdCode" />
    <result column="diag_std_name" jdbcType="VARCHAR" property="diagStdName" />
    <result column="disease_other" jdbcType="VARCHAR" property="diseaseOther" />
    <result column="diag_state_code" jdbcType="VARCHAR" property="diagStateCode" />
    <result column="diag_state_name" jdbcType="VARCHAR" property="diagStateName" />
    <result column="test_detection_verdict_code" jdbcType="VARCHAR" property="testDetectionVerdictCode" />
    <result column="test_detection_verdict_name" jdbcType="VARCHAR" property="testDetectionVerdictName" />
    <result column="labor_diagnosis_date" jdbcType="DATE" property="laborDiagnosisDate" />
    <result column="labor_test_result_code" jdbcType="VARCHAR" property="laborTestResultCode" />
    <result column="labor_test_result_name" jdbcType="VARCHAR" property="laborTestResultName" />
    <result column="confirm_aids_datetime" jdbcType="TIMESTAMP" property="confirmAidsDatetime" />
    <result column="confirm_org_code" jdbcType="VARCHAR" property="confirmOrgCode" />
    <result column="hiv_diag_datetime" jdbcType="TIMESTAMP" property="hivDiagDatetime" />
    <result column="palsy_datetime" jdbcType="TIMESTAMP" property="palsyDatetime" />
    <result column="palsy_symptom" jdbcType="VARCHAR" property="palsySymptom" />
    <result column="iut_hos_date" jdbcType="DATE" property="iutHosDate" />
    <result column="out_hos_date" jdbcType="DATE" property="outHosDate" />
    <result column="out_hos_file_date" jdbcType="TIMESTAMP" property="outHosFileDate" />
    <result column="mgmt_status_code" jdbcType="VARCHAR" property="mgmtStatusCode" />
    <result column="mgmt_status_name" jdbcType="VARCHAR" property="mgmtStatusName" />
    <result column="currmgmt_org_code" jdbcType="VARCHAR" property="currmgmtOrgCode" />
    <result column="currmgmt_org_name" jdbcType="VARCHAR" property="currmgmtOrgName" />
    <result column="dead_datetime" jdbcType="TIMESTAMP" property="deadDatetime" />
    <result column="dead_by_this_flag" jdbcType="VARCHAR" property="deadByThisFlag" />
    <result column="direct_death_reason_code" jdbcType="VARCHAR" property="directDeathReasonCode" />
    <result column="direct_death_reason_name" jdbcType="VARCHAR" property="directDeathReasonName" />
    <result column="card_id" jdbcType="VARCHAR" property="cardId" />
    <result column="card_code" jdbcType="VARCHAR" property="cardCode" />
    <result column="card_fill_date" jdbcType="DATE" property="cardFillDate" />
    <result column="report_datetime" jdbcType="TIMESTAMP" property="reportDatetime" />
    <result column="report_org_code" jdbcType="VARCHAR" property="reportOrgCode" />
    <result column="report_org" jdbcType="VARCHAR" property="reportOrg" />
    <result column="report_zone_code" jdbcType="VARCHAR" property="reportZoneCode" />
    <result column="report_zone_name" jdbcType="VARCHAR" property="reportZoneName" />
    <result column="report_addr_detail_std" jdbcType="VARCHAR" property="reportAddrDetailStd" />
    <result column="report_addr_province_code" jdbcType="VARCHAR" property="reportAddrProvinceCode" />
    <result column="report_addr_province_name" jdbcType="VARCHAR" property="reportAddrProvinceName" />
    <result column="report_addr_city_code" jdbcType="VARCHAR" property="reportAddrCityCode" />
    <result column="report_addr_city_name" jdbcType="VARCHAR" property="reportAddrCityName" />
    <result column="report_addr_district_code" jdbcType="VARCHAR" property="reportAddrDistrictCode" />
    <result column="report_addr_district_name" jdbcType="VARCHAR" property="reportAddrDistrictName" />
    <result column="report_addr_func_district_code" jdbcType="VARCHAR" property="reportAddrFuncDistrictCode" />
    <result column="report_addr_func_district_name" jdbcType="VARCHAR" property="reportAddrFuncDistrictName" />
    <result column="report_addr_street_code" jdbcType="VARCHAR" property="reportAddrStreetCode" />
    <result column="report_addr_street_name" jdbcType="VARCHAR" property="reportAddrStreetName" />
    <result column="report_addr_street_latitude" jdbcType="NUMERIC" property="reportAddrStreetLatitude" />
    <result column="report_addr_street_longitude" jdbcType="NUMERIC" property="reportAddrStreetLongitude" />
    <result column="report_addr_latitude" jdbcType="NUMERIC" property="reportAddrLatitude" />
    <result column="report_addr_longitude" jdbcType="NUMERIC" property="reportAddrLongitude" />
    <result column="report_addr_type" jdbcType="VARCHAR" property="reportAddrType" />
    <result column="report_addr_poi" jdbcType="VARCHAR" property="reportAddrPoi" />
    <result column="fill_doc_id" jdbcType="VARCHAR" property="fillDocId" />
    <result column="fill_doc_name" jdbcType="VARCHAR" property="fillDocName" />
    <result column="notes" jdbcType="VARCHAR" property="notes" />
    <result column="infect_type_code" jdbcType="VARCHAR" property="infectTypeCode" />
    <result column="infect_type_name" jdbcType="VARCHAR" property="infectTypeName" />
    <result column="infect_parent_code" jdbcType="VARCHAR" property="infectParentCode" />
    <result column="infect_parent_name" jdbcType="VARCHAR" property="infectParentName" />
    <result column="infect_code" jdbcType="VARCHAR" property="infectCode" />
    <result column="infect_name" jdbcType="VARCHAR" property="infectName" />
    <result column="treat_type_code" jdbcType="VARCHAR" property="treatTypeCode" />
    <result column="find_way_code" jdbcType="VARCHAR" property="findWayCode" />
    <result column="find_way_name" jdbcType="VARCHAR" property="findWayName" />
    <result column="find_way_other" jdbcType="VARCHAR" property="findWayOther" />
    <result column="serverity_code" jdbcType="VARCHAR" property="serverityCode" />
    <result column="serverity_name" jdbcType="VARCHAR" property="serverityName" />
    <result column="infect_route_code" jdbcType="VARCHAR" property="infectRouteCode" />
    <result column="infect_route_name" jdbcType="VARCHAR" property="infectRouteName" />
    <result column="infect_route_other" jdbcType="VARCHAR" property="infectRouteOther" />
    <result column="contact_way_code" jdbcType="VARCHAR" property="contactWayCode" />
    <result column="contact_way_name" jdbcType="VARCHAR" property="contactWayName" />
    <result column="contact_other" jdbcType="VARCHAR" property="contactOther" />
    <result column="venereal_dis_code" jdbcType="VARCHAR" property="venerealDisCode" />
    <result column="venereal_dis_name" jdbcType="VARCHAR" property="venerealDisName" />
    <result column="inject_person_cnt" jdbcType="VARCHAR" property="injectPersonCnt" />
    <result column="nonweb_cnt" jdbcType="VARCHAR" property="nonwebCnt" />
    <result column="msm_person_cnt" jdbcType="VARCHAR" property="msmPersonCnt" />
    <result column="ct_cases_type_code" jdbcType="VARCHAR" property="ctCasesTypeCode" />
    <result column="ct_cases_type_name" jdbcType="VARCHAR" property="ctCasesTypeName" />
    <result column="lab_result_code" jdbcType="VARCHAR" property="labResultCode" />
    <result column="lab_result_name" jdbcType="VARCHAR" property="labResultName" />
    <result column="hbsag_duration_type_code" jdbcType="VARCHAR" property="hbsagDurationTypeCode" />
    <result column="hbsag_duration_type_name" jdbcType="VARCHAR" property="hbsagDurationTypeName" />
    <result column="hbsag_symptom_first_datetime" jdbcType="TIMESTAMP" property="hbsagSymptomFirstDatetime" />
    <result column="hbsag_symptom_buxiang_flag" jdbcType="VARCHAR" property="hbsagSymptomBuxiangFlag" />
    <result column="hbsag_alt_res" jdbcType="VARCHAR" property="hbsagAltRes" />
    <result column="hbcigm_code" jdbcType="VARCHAR" property="hbcigmCode" />
    <result column="hbcigm_name" jdbcType="VARCHAR" property="hbcigmName" />
    <result column="hbliver_puncture_code" jdbcType="VARCHAR" property="hbliverPunctureCode" />
    <result column="hbliver_puncture_name" jdbcType="VARCHAR" property="hbliverPunctureName" />
    <result column="hbsag_change_code" jdbcType="VARCHAR" property="hbsagChangeCode" />
    <result column="hbsag_change_name" jdbcType="VARCHAR" property="hbsagChangeName" />
    <result column="same_symptom_flag" jdbcType="VARCHAR" property="sameSymptomFlag" />
    <result column="covid_severe_type_code" jdbcType="VARCHAR" property="covidSevereTypeCode" />
    <result column="covid_severe_type_name" jdbcType="VARCHAR" property="covidSevereTypeName" />
    <result column="is_baby_apnea_code" jdbcType="VARCHAR" property="isBabyApneaCode" />
    <result column="is_baby_apnea_name" jdbcType="VARCHAR" property="isBabyApneaName" />
    <result column="is_baby_shock_code" jdbcType="VARCHAR" property="isBabyShockCode" />
    <result column="is_baby_shock_name" jdbcType="VARCHAR" property="isBabyShockName" />
    <result column="input_type_code" jdbcType="VARCHAR" property="inputTypeCode" />
    <result column="input_type_name" jdbcType="VARCHAR" property="inputTypeName" />
    <result column="input_area_code" jdbcType="VARCHAR" property="inputAreaCode" />
    <result column="input_area_name" jdbcType="VARCHAR" property="inputAreaName" />
    <result column="place_other" jdbcType="VARCHAR" property="placeOther" />
    <result column="valid_flag" jdbcType="VARCHAR" property="validFlag" />
    <result column="card_status" jdbcType="VARCHAR" property="cardStatus" />
    <result column="valid_time_district" jdbcType="TIMESTAMP" property="validTimeDistrict" />
    <result column="valid_time_city" jdbcType="TIMESTAMP" property="validTimeCity" />
    <result column="valid_time_province" jdbcType="TIMESTAMP" property="validTimeProvince" />
    <result column="valid_time" jdbcType="TIMESTAMP" property="validTime" />
    <result column="dead_valid_time" jdbcType="TIMESTAMP" property="deadValidTime" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="do_datetime" jdbcType="TIMESTAMP" property="doDatetime" />
    <result column="last_modify_user" jdbcType="VARCHAR" property="lastModifyUser" />
    <result column="delete_user_id" jdbcType="VARCHAR" property="deleteUserId" />
    <result column="delete_user_name" jdbcType="VARCHAR" property="deleteUserName" />
    <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
    <result column="deleting_type_code" jdbcType="VARCHAR" property="deletingTypeCode" />
    <result column="deleting_reason_details" jdbcType="VARCHAR" property="deletingReasonDetails" />
    <result column="report_tag" jdbcType="VARCHAR" property="reportTag" />
    <result column="card_delete_flag" jdbcType="SMALLINT" property="cardDeleteFlag" />
    <result column="card_delete_remark" jdbcType="VARCHAR" property="cardDeleteRemark" />
    <result column="card_exclude_flag" jdbcType="SMALLINT" property="cardExcludeFlag" />
    <result column="card_exclude_remark" jdbcType="VARCHAR" property="cardExcludeRemark" />
    <result column="life_id" jdbcType="VARCHAR" property="lifeId" />
    <result column="archive_id" jdbcType="VARCHAR" property="archiveId" />
    <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
    <result column="data_source_name" jdbcType="VARCHAR" property="dataSourceName" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ms_infect_report_id, etl_create_datetime, etl_update_datetime, event_id, patient_id,
    empi_id, patient_name, patient_identity_type_code, patient_identity_type, patient_identity_no,
    patient_sex_code, patient_sex_name, patient_birth_day, age, age_unit, age_group,
    patient_nationality_code, patient_nationality_name, patient_ethnic_code, patient_ethnic_name,
    patient_permanent_addr_type_code, patient_permanent_addr_type_name, patient_permanent_addr_code,
    patient_permanent_addr_name, patient_permanent_addr_detail, patient_marry_code, patient_marry_name,
    patient_educate_code, patient_educate_name, patient_phone, contacts_rel_code, patient_contact_name,
    patient_contact_phone, living_area_code, living_area_name, current_addr_type_code,
    current_addr_type_name, living_addr_detail, living_addr_detail_std, living_addr_province_code,
    living_addr_province_name, living_addr_city_code, living_addr_city_name, living_addr_district_code,
    living_addr_district_name, living_addr_func_district_code, living_addr_func_district_name,
    living_addr_street_code, living_addr_street_name, living_addr_street_longitude, living_addr_street_latitude,
    living_addr_longitude, living_addr_latitude, living_addr_poi, living_addr_type, company,
    company_addr_std, company_province_code, company_province_name, company_city_code,
    company_city_name, company_district_code, company_district_name, company_func_district_code,
    company_func_district_name, company_street_code, company_street_name, company_street_longitude,
    company_street_latitude, company_longitude, company_latitude, nultitude_type_code,
    nultitude_type_name, nultitude_type_other, addr_belong_type_code, addr_belong_type,
    activity_type_code, activity_type_name, serial_number, visit_addr_areatype2_code,
    visit_addr_areatype2_name, visit_org_code, visit_org_name, dept_code, dept_name,
    dept_other, visit_addr_code, visit_addr_name, visit_addr_detail, visit_addr_detail_std,
    visit_addr_province_code, visit_addr_province_name, visit_addr_city_code, visit_addr_city_name,
    visit_addr_district_code, visit_addr_district_name, visit_addr_func_district_code,
    visit_addr_func_district_name, visit_addr_street_code, visit_addr_street_name, visit_addr_street_longitude,
    visit_addr_street_latitude, visit_addr_longitude, visit_addr_latitude, visit_addr_poi,
    visit_addr_type, cases_type_code, cases_type_name, onset_datetime, clinical_other,
    fever_num, visit_datetime, diag_datetime, audit_disease_name, audit_disease_code,
    audit_create_date, diag_code, diag_name, diag_other, diag_std_code, diag_std_name,
    disease_other, diag_state_code, diag_state_name, test_detection_verdict_code, test_detection_verdict_name,
    labor_diagnosis_date, labor_test_result_code, labor_test_result_name, confirm_aids_datetime,
    confirm_org_code, hiv_diag_datetime, palsy_datetime, palsy_symptom, iut_hos_date,
    out_hos_date, out_hos_file_date, mgmt_status_code, mgmt_status_name, currmgmt_org_code,
    currmgmt_org_name, dead_datetime, dead_by_this_flag, direct_death_reason_code, direct_death_reason_name,
    card_id, card_code, card_fill_date, report_datetime, report_org_code, report_org,
    report_zone_code, report_zone_name, report_addr_detail_std, report_addr_province_code,
    report_addr_province_name, report_addr_city_code, report_addr_city_name, report_addr_district_code,
    report_addr_district_name, report_addr_func_district_code, report_addr_func_district_name,
    report_addr_street_code, report_addr_street_name, report_addr_street_latitude, report_addr_street_longitude,
    report_addr_latitude, report_addr_longitude, report_addr_type, report_addr_poi, fill_doc_id,
    fill_doc_name, notes, infect_type_code, infect_type_name, infect_parent_code, infect_parent_name,
    infect_code, infect_name, treat_type_code, find_way_code, find_way_name, find_way_other,
    serverity_code, serverity_name, infect_route_code, infect_route_name, infect_route_other,
    contact_way_code, contact_way_name, contact_other, venereal_dis_code, venereal_dis_name,
    inject_person_cnt, nonweb_cnt, msm_person_cnt, ct_cases_type_code, ct_cases_type_name,
    lab_result_code, lab_result_name, hbsag_duration_type_code, hbsag_duration_type_name,
    hbsag_symptom_first_datetime, hbsag_symptom_buxiang_flag, hbsag_alt_res, hbcigm_code,
    hbcigm_name, hbliver_puncture_code, hbliver_puncture_name, hbsag_change_code, hbsag_change_name,
    same_symptom_flag, covid_severe_type_code, covid_severe_type_name, is_baby_apnea_code,
    is_baby_apnea_name, is_baby_shock_code, is_baby_shock_name, input_type_code, input_type_name,
    input_area_code, input_area_name, place_other, valid_flag, card_status, valid_time_district,
    valid_time_city, valid_time_province, valid_time, dead_valid_time, operator_id, do_datetime,
    last_modify_user, delete_user_id, delete_user_name, delete_time, deleting_type_code,
    deleting_reason_details, report_tag, card_delete_flag, card_delete_remark, card_exclude_flag,
    card_exclude_remark, life_id, archive_id, data_source, data_source_name, source_id
  </sql>

  <select id="findReportByempiIdList" parameterType="string"
          resultType="com.iflytek.cdc.province.model.vo.edrcase.AdsRepInfectReportInfoVO">
    select
    card_code,
    null as reportClass,
    card_status,
    patient_name,
    patient_contact_name,
    patient_identity_type,
    patient_identity_no,
    company,
    patient_phone,
    addr_belong_type,
    living_addr_detail_std,
    living_addr_detail,
    nultitude_type_name,
    cases_type_name,
    null as casesCategory2,
    onset_datetime,
    diag_datetime,
    diag_code,
    diag_name,
    dead_datetime,
    infect_name,
    infect_code,
    audit_disease_name,
    fill_doc_id,
    card_fill_date,
    fill_doc_name,
    valid_time_district,
    valid_time_city,
    valid_time_province,
    valid_flag,
    audit_create_date,
    delete_time,
    delete_user_name,
    deleting_reason_details,
    notes,
    TO_CHAR(etl_create_datetime, 'YYYY-MM-DD HH24:MI:SS') as etlCreateDatetime,
    TO_CHAR(etl_update_datetime, 'YYYY-MM-DD HH24:MI:SS') as etlUpdateDatetime,
    empi_id,
    patient_sex_name,
    patient_birth_day,
    age,
    age_unit,
    living_addr_province_code,
    living_addr_province_name,
    living_addr_city_code,
    living_addr_city_name,
    living_addr_district_name,
    report_addr_province_code,
    report_addr_province_name,
    report_addr_city_code,
    report_addr_city_name,
    report_addr_district_code,
    report_addr_district_name,
    life_id,
    archive_id,
    null as revisedPreviousDiagnoseTime,
    null as revisedPreviousCheckTime,
    null as unitType,
    null as recordUserCompany,
    null as revisedFinalCheckTime,
    dead_valid_time ,
    null as revisedUser,
    null as revisedUserCompany,
    null as creatorId,
    null as creator,
    CASE source_id
    WHEN '11' THEN '大疫情网'
    WHEN '110' THEN '临床'
    ELSE ''
    end as sourceName
    from ads.ads_rep_infect_report_info
    where empi_id = #{empiId}
  </select>
</mapper>