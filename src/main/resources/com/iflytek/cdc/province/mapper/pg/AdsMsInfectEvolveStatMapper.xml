<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMsInfectEvolveStatMapper">

    <sql id="chooseAddress">
        <if test="provinceCode != null and provinceCode != ''">
            and org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and org_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and org_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and org_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="whereCriteria">
        <if test="diseaseTypeCode != null and diseaseTypeCode != ''">
            and infect_type = #{diseaseTypeCode}
        </if>
        <if test="diseaseCodeList != null and diseaseCodeList.size() &gt; 0">
            and infect_code in
            <foreach collection="diseaseCodeList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <include refid="chooseAddress"/>
    </sql>

    <sql id="chooseTable">
        <choose>
            <when test="dateDimType == 'day'">
                ads.ads_ms_infect_evolve_ot_d
            </when>
            <when test="dateDimType == 'week'">
                ads.ads_ms_infect_evolve_ot_w
            </when>
            <when test="dateDimType == 'meadow'">
                ads.ads_ms_infect_evolve_ot_td
            </when>
            <when test="dateDimType == 'month' or dateDimType == 'halfYear'">
                ads.ads_ms_infect_evolve_ot_m
            </when>
            <when test="dateDimType == 'quarter'">
                ads.ads_ms_infect_evolve_ot_q
            </when>
            <otherwise>
                ads.ads_ms_infect_evolve_ot_y
            </otherwise>
        </choose>
    </sql>

    <select id="getSyndromeInfectDayDiagnose"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.SyndromeRelateInfectedProcessVO">
        select
        syndrome_code,
        syndrome_name,
        infect_code,
        infect_name,
        process_new_cnt
        from
        <include refid="chooseTable"/>
        where day between #{startDate} and #{endDate}
        <include refid="chooseAddress"/>
        <if test="syndromeCodeList != null and syndromeCodeList.size() &gt; 0">
            and syndrome_code in
            <foreach collection="syndromeCodeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getSyndromeInfectWeekDiagnose"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.SyndromeRelateInfectedProcessVO">
        select
        syndrome_code,
        syndrome_name,
        infect_code,
        infect_name,
        process_new_cnt
        from
        <include refid="chooseTable"/>
        where monday between #{startDate} and #{endDate}
        <include refid="chooseAddress"/>
        <if test="syndromeCodeList != null and syndromeCodeList.size() &gt; 0">
            and syndrome_code in
            <foreach collection="syndromeCodeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getSyndromeInfectMeadowDiagnose"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.SyndromeRelateInfectedProcessVO">
        select
        syndrome_code,
        syndrome_name,
        infect_code,
        infect_name,
        process_new_cnt
        from
        <include refid="chooseTable"/>
        where 1=1
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month} and ten_day = #{item.meadow})
        </foreach>
        <include refid="chooseAddress"/>
        <if test="syndromeCodeList != null and syndromeCodeList.size() &gt; 0">
            and syndrome_code in
            <foreach collection="syndromeCodeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getSyndromeInfectMonthDiagnose"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.SyndromeRelateInfectedProcessVO">
        select
        syndrome_code,
        syndrome_name,
        infect_code,
        infect_name,
        process_new_cnt
        from
        <include refid="chooseTable"/>
        where 1 = 1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month})
        </foreach>
        <include refid="chooseAddress"/>
        <if test="syndromeCodeList != null and syndromeCodeList.size() &gt; 0">
            and syndrome_code in
            <foreach collection="syndromeCodeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getSyndromeInfectQuarterDiagnose"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.SyndromeRelateInfectedProcessVO">
        select
        syndrome_code,
        syndrome_name,
        infect_code,
        infect_name,
        process_new_cnt
        from
        <include refid="chooseTable"/>
        where 1 = 1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and quarter = #{item.quarter})
        </foreach>
        <include refid="chooseAddress"/>
        <if test="syndromeCodeList != null and syndromeCodeList.size() &gt; 0">
            and syndrome_code in
            <foreach collection="syndromeCodeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getSyndromeInfectYearDiagnose"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.SyndromeRelateInfectedProcessVO">
        select
        syndrome_code,
        syndrome_name,
        infect_code,
        infect_name,
        process_new_cnt
        from
        <include refid="chooseTable"/>
        where year in
        <foreach collection="dateDims" open="(" close=")" separator="," item="item">
            #{item.year}
        </foreach>
        <include refid="chooseAddress"/>
        <if test="syndromeCodeList != null and syndromeCodeList.size() &gt; 0">
            and syndrome_code in
            <foreach collection="syndromeCodeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <sql id="flowStat">
        syndrome_code as sourceCode,
        syndrome_name as source,
        infect_code as targetCode,
        infect_name as target,
        COALESCE(sum(process_new_cnt) , 0) as value
    </sql>

    <select id="getMonitorProcessFlowDayStat"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorProcessFlowVO">
        select
        <include refid="flowStat"/>
        from
        <include refid="chooseTable"/>
        where day between #{startDate} and #{endDate}
        <include refid="chooseAddress"/>
        <if test="syndromeCodeList != null and syndromeCodeList.size() &gt; 0">
            and syndrome_code in
            <foreach collection="syndromeCodeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by syndrome_code, syndrome_name, infect_code, infect_name
    </select>

    <select id="getMonitorProcessFlowWeekStat"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorProcessFlowVO">
        select
        <include refid="flowStat"/>
        from
        <include refid="chooseTable"/>
        where monday between #{startDate} and #{endDate}
        <include refid="chooseAddress"/>
        <if test="syndromeCodeList != null and syndromeCodeList.size() &gt; 0">
            and syndrome_code in
            <foreach collection="syndromeCodeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by syndrome_code, syndrome_name, infect_code, infect_name
    </select>

    <select id="getMonitorProcessFlowMeadowStat"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorProcessFlowVO">
        select
        <include refid="flowStat"/>
        from
        <include refid="chooseTable"/>
        where 1=1
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month} and ten_day = #{item.meadow})
        </foreach>
        <include refid="chooseAddress"/>
        <if test="syndromeCodeList != null and syndromeCodeList.size() &gt; 0">
            and syndrome_code in
            <foreach collection="syndromeCodeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by syndrome_code, syndrome_name, infect_code, infect_name
    </select>

    <select id="getMonitorProcessFlowMonthStat"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorProcessFlowVO">
        select
        <include refid="flowStat"/>
        from
        <include refid="chooseTable"/>
        where 1 = 1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and month = #{item.month})
        </foreach>
        <include refid="chooseAddress"/>
        <if test="syndromeCodeList != null and syndromeCodeList.size() &gt; 0">
            and syndrome_code in
            <foreach collection="syndromeCodeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by syndrome_code, syndrome_name, infect_code, infect_name
    </select>

    <select id="getMonitorProcessFlowQuarterStat"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorProcessFlowVO">
        select
        <include refid="flowStat"/>
        from
        <include refid="chooseTable"/>
        where 1 = 1 and
        <foreach collection="dateDims" open="(" close=")" separator="or" item="item">
            (year = #{item.year} and quarter = #{item.quarter})
        </foreach>
        <include refid="chooseAddress"/>
        <if test="syndromeCodeList != null and syndromeCodeList.size() &gt; 0">
            and syndrome_code in
            <foreach collection="syndromeCodeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by syndrome_code, syndrome_name, infect_code, infect_name
    </select>

    <select id="getMonitorProcessFlowYearStat"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorProcessFlowVO">
        select
        <include refid="flowStat"/>
        from
        <include refid="chooseTable"/>
        where year in
        <foreach collection="dateDims" open="(" close=")" separator="," item="item">
            #{item.year}
        </foreach>
        <include refid="chooseAddress"/>
        <if test="syndromeCodeList != null and syndromeCodeList.size() &gt; 0">
            and syndrome_code in
            <foreach collection="syndromeCodeList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by syndrome_code, syndrome_name, infect_code, infect_name
    </select>

</mapper>