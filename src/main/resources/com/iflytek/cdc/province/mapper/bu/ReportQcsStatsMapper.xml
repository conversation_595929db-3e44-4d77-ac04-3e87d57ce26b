<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.ReportQcsStatsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.bu.ReportQcsStats">
        <id column="id" property="id" />
        <result column="creator_id" property="creatorId" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater_id" property="updaterId" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="memo" property="memo" />
        <result column="main_record_id" property="mainRecordId" />
        <result column="report_org_name" property="reportOrgName" />
        <result column="report_org_addr_province_code" property="reportOrgAddrProvinceCode" />
        <result column="report_org_addr_province" property="reportOrgAddrProvince" />
        <result column="report_org_addr_city_code" property="reportOrgAddrCityCode" />
        <result column="report_org_addr_city" property="reportOrgAddrCity" />
        <result column="report_org_addr_district_code" property="reportOrgAddrDistrictCode" />
        <result column="report_org_addr_district" property="reportOrgAddrDistrict" />
        <result column="report_org_addr_func_district_code" property="reportOrgAddrFuncDistrictCode" />
        <result column="report_org_addr_func_district" property="reportOrgAddrFuncDistrict" />
        <result column="report_org_addr_street_code" property="reportOrgAddrStreetCode" />
        <result column="report_org_addr_street" property="reportOrgAddrStreet" />
        <result column="identify_cnt" property="identifyCnt" />
        <result column="report_cnt" property="reportCnt" />
        <result column="leak_cnt" property="leakCnt" />
        <result column="delay_cnt" property="delayCnt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, memo, main_record_id, report_org_name, report_org_addr_province_code, report_org_addr_province, report_org_addr_city_code, report_org_addr_city, report_org_addr_district_code, report_org_addr_district, report_org_addr_func_district_code, report_org_addr_func_district, report_org_addr_street_code, report_org_addr_street, identify_cnt, report_cnt, leak_cnt, delay_cnt
    </sql>
    <insert id="mergeInto">
        INSERT INTO tb_cdcew_report_qcs_stats
        ( id, creator_id, creator, create_time, updater_id, updater, update_time, delete_flag, memo, main_record_id, report_org_name, report_org_addr_province_code, report_org_addr_province, report_org_addr_city_code, report_org_addr_city, report_org_addr_district_code, report_org_addr_district, report_org_addr_func_district_code, report_org_addr_func_district, report_org_addr_street_code, report_org_addr_street, identify_cnt, report_cnt, leak_cnt, delay_cnt)
        VALUES
        <foreach collection="list" item="item" separator=",">
            ( #{item.id}, #{item.creatorId}, #{item.creator}, #{item.createTime}, #{item.updaterId}, #{item.updater}, #{item.updateTime}, #{item.deleteFlag}, #{item.memo}, #{item.mainRecordId}, #{item.reportOrgName}, #{item.reportOrgAddrProvinceCode}, #{item.reportOrgAddrProvince}, #{item.reportOrgAddrCityCode}, #{item.reportOrgAddrCity}, #{item.reportOrgAddrDistrictCode}, #{item.reportOrgAddrDistrict}, #{item.reportOrgAddrFuncDistrictCode}, #{item.reportOrgAddrFuncDistrict}, #{item.reportOrgAddrStreetCode}, #{item.reportOrgAddrStreet}, #{item.identifyCnt}, #{item.reportCnt}, #{item.leakCnt}, #{item.delayCnt})
        </foreach>

    </insert>
    <delete id="deleteByMainRecordId">
        delete from app.tb_cdcew_report_qcs_stats where main_record_id=#{mainRecordId}
    </delete>

</mapper>
