<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMsSyndromeProcessMapper">

    <sql id="syndromeCaseInfoTableJoin">
        <include refid="regionJoinCondition"/>
        <include refid="syndromeJoinCondition"/>
    </sql>

    <sql id="regionJoinCondition">
        join dim.dim_region_nation drn
        on
        <choose>
            <when test="addressType == 'livingAddress'">
                i.living_region_code = drn.region_code
            </when>
            <when test="addressType == 'companyAddress'">
                i.company_region_code = drn.region_code
            </when>
            <otherwise>
                i.org_region_code = drn.region_code
            </otherwise>
        </choose>
    </sql>

    <sql id="syndromeJoinCondition">
        join dim.dim_syndrome_info dii
        on dii.disease_code = i.fi_syn_syndrome_code
    </sql>

    <sql id="regionChoose">
        <if test="provinceCodeList != null and provinceCodeList.size() > 0">
            and drn.province_code in
            <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodeList != null and cityCodeList.size() > 0">
            and drn.city_code in
            <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodeList != null and districtCodeList.size() > 0">
            and drn.district_code in
            <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodeList != null and streetCodeList.size() > 0">
            and drn.street_code  in
            <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and drn.province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and drn.city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and drn.district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size() > 0">
            and drn.street_code  in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="areaMultiChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and living_addr_province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and living_addr_city_code  in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and living_addr_func_district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and living_addr_street_code  in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and living_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and living_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and living_addr_func_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and org_addr_province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and org_addr_city_code in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and org_addr_func_district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and org_addr_street_code in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and org_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and org_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and org_addr_func_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and org_addr_street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="timeTypeWhere">
        <choose>
            <when test="timeType == 'onsetTime'">
                coalesce(i.fi_syn_onset_day, i.fi_syn_visit_day)
            </when>
            <otherwise>
                i.fi_syn_identify_day
            </otherwise>
        </choose>
    </sql>

    <sql id="dayDb">
        <choose>
            <when test="timeType == 'onsetTime'">
                ads.ads_ms_syndrome_process_ot_d
            </when>
            <otherwise>
                ads.ads_ms_syndrome_process_it_d
            </otherwise>
        </choose>
    </sql>
    <sql id="monthDb">
        <choose>

            <when test="timeType == 'onsetTime'">
                ads.ads_ms_syndrome_process_ot_m
            </when>
            <otherwise>
                ads.ads_ms_syndrome_process_it_m
            </otherwise>
        </choose>
    </sql>
    <sql id="quarterDb">
        <choose>

            <when test="timeType == 'onsetTime'">
                ads.ads_ms_syndrome_process_ot_q
            </when>
            <otherwise>
                ads.ads_ms_syndrome_process_it_q
            </otherwise>
        </choose>
    </sql>
    <sql id="tenDaysDb">
        <choose>
            <when test="timeType == 'onsetTime'">
                ads.ads_ms_syndrome_process_ot_td
            </when>
            <otherwise>
                ads.ads_ms_syndrome_process_it_td
            </otherwise>
        </choose>
    </sql>
    <sql id="weekDb">
        <choose>
            <when test="timeType == 'onsetTime'">
                ads.ads_ms_syndrome_process_ot_w
            </when>
            <otherwise>
                ads.ads_ms_syndrome_process_it_w
            </otherwise>
        </choose>
    </sql>
    <sql id="yearDb">
        <choose>

            <when test="timeType == 'onsetTime'">
                ads.ads_ms_syndrome_process_ot_y
            </when>
            <otherwise>
                ads.ads_ms_syndrome_process_it_y
            </otherwise>
        </choose>
    </sql>

    <sql id="whereDistribution">
        <if test="syndromeCode != null and syndromeCode != ''">
            and syndrome_code = #{syndromeCode}
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            and syndrome_name = #{diseaseName}
        </if>
        <if test = "filterCodes != null and filterCodes.size() > 0">
            and syndrome_code not in
            <foreach collection="filterCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
         <choose>
            <when test="addressType == 'livingAddress'">
                <if test="provinceCode != null and provinceCode != ''">
                    and living_addr_province_code = #{provinceCode}
                </if>
                <if test="cityCode != null and cityCode != ''">
                    and living_addr_city_code = #{cityCode}
                </if>
                <if test="districtCode != null and districtCode != ''">
                    and living_addr_func_district_code = #{districtCode}
                </if>
                <if test="streetCode != null and streetCode != ''">
                    and living_addr_street_code = #{streetCode}
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and living_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and living_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and living_addr_func_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="provinceCode != null and provinceCode != ''">
                    and org_addr_province_code = #{provinceCode}
                </if>
                <if test="cityCode != null and cityCode != ''">
                    and org_addr_city_code = #{cityCode}
                </if>
                <if test="districtCode != null and districtCode != ''">
                    and org_addr_func_district_code = #{districtCode}
                </if>
                <if test="streetCode != null and streetCode != ''">
                    and org_addr_street_code = #{streetCode}
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and org_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and org_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and org_addr_func_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and org_addr_street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="whereDistributionAreaMultiChoose">
        <if test="syndromeCode != null and syndromeCode != ''">
            and dii.disease_code = #{syndromeCode}
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            and dii.disease_name = #{diseaseName}
        </if>
        <if test = "filterCodes != null and filterCodes.size() > 0">
            and dii.disease_code not in
            <foreach collection="filterCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <include refid="regionChoose"/>
    </sql>

    <select id="findDistribution" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <choose>
            <when test="dateDimType == 'month'">
                <include refid="monthDimSelectColumns"/>
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterDimSelectColumns"/>
            </when>
            <when test="dateDimType == 'halfYear'">
                d.half_year_desc AS description
            </when>
            <otherwise>
                <include refid="yearDimSelectColumns"/>
            </otherwise>
        </choose>,
        <include refid="sumCnt"/>

        FROM ads.ads_ms_syndrome_process_case_info i
        <include refid="syndromeCaseInfoTableJoin"/>
        <include refid="joinDayDim"/>

        WHERE 1=1
        AND i.rep_card_flag = '0'
        AND <include refid="timeTypeWhere"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)

        <include refid="whereDistributionAreaMultiChoose"/>

        <choose>
            <when test="dateDimType == 'month'">
                <include refid="monthDimGroupOrder"/>
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterDimGroupOrder"/>
            </when>
            <when test="dateDimType == 'halfYear'">
                GROUP BY d.half_year_desc
                ORDER BY d.half_year_desc
            </when>
            <otherwise>
                <include refid="yearDimGroupOrder"/>
            </otherwise>
        </choose>
    </select>

    <sql id="sumCommonCnt">
        COALESCE(sum(process_new_cnt),0)  as processNewCnt ,
        COALESCE(sum(process_new_cnt_last),0) as processNewCntLast ,
        COALESCE(sum(process_new_cnt_last_y),0)  as processNewCntLastY,
        COALESCE(sum(process_new_cnt_avg_s),0)  as processNewCntAvgS,
        COALESCE(sum(process_new_cnt_pre),0)   as processNewCntPre,

        COALESCE(sum(process_dead_cnt),0)  as processDeadCnt,
        COALESCE(sum(process_dead_cnt_avg_s),0)  as  processDeadCntAvgS,
        COALESCE(sum(process_dead_cnt_last),0)  as  processDeadCntLast,
        COALESCE(sum(process_dead_cnt_last_y),0)  as processDeadCntLastY,

        COALESCE(sum(process_now_cnt),0) as processNowCnt,
        COALESCE(sum(process_now_cnt_avg_s) , 0) as processNowCntAvgS,
        COALESCE(sum(process_now_cnt_last), 0)  as  processNowCntLast,
        COALESCE(sum(process_now_cnt_last_y), 0) as processNowCntLastY,

        COALESCE(sum(process_cure_cnt),0) as processCureCnt,
        COALESCE(sum(process_cure_cnt_avg_s) , 0) as processCureCntAvgS,
        COALESCE(sum(process_cure_cnt_last), 0)  as  processCureCntLast,
        COALESCE(sum(process_cure_cnt_last_y), 0) as processCureCntLastY,

        COALESCE(sum(process_severe_cnt),0)  as processSevereCnt,
        COALESCE(sum(process_severe_cnt_avg_s),0)  as  processSevereCntAvgS,
        COALESCE(sum(process_severe_cnt_last),0)   as  processSevereCntLast,
        COALESCE(sum(process_severe_cnt_last_y),0)  as processSevereCntLastY,

        COALESCE(sum(process_virus_cnt),0) as processVirusCnt,
        COALESCE(sum(process_virus_cnt_last),0) as processVirusCntLast,
        COALESCE(sum(process_virus_cnt_last_y),0) as processVirusCntLastY,

        COALESCE(sum(process_virus_positive_cnt),0) as processVirusPositiveCnt,
        COALESCE(sum(process_virus_positive_cnt_last),0) as processVirusPositiveCntLast,
        COALESCE(sum(process_virus_positive_cnt_last_y),0) as processVirusPositiveCntLastY,

        COALESCE(sum(process_bacteria_cnt),0) as processBacteriaCnt,
        COALESCE(sum(process_bacteria_cnt_last),0) as processBacteriaCntLast,
        COALESCE(sum(process_bacteria_cnt_last_y),0) as processBacteriaCntLastY,
        
        COALESCE(sum(process_bacteria_positive_cnt),0) as processBacteriaPositiveCnt,
        COALESCE(sum(process_bacteria_positive_cnt_last),0) as processBacteriaPositiveCntLast,
        COALESCE(sum(process_bacteria_positive_cnt_last_y),0) as processBacteriaPositiveCntLastY,

        COALESCE(sum(process_bacteria_virus_cnt),0) as processBacteriaVirusCnt,
        COALESCE(sum(process_bacteria_virus_cnt_last),0) as processBacteriaVirusCntLast,
        COALESCE(sum(process_bacteria_virus_cnt_last_y),0) as processBacteriaVirusCntLastY
    </sql>

    <sql id="selectGroupArea">
        <if test="areaLevel == 1">
            drn.city_code  areaCode,
            drn.city_name  areaName,
            drn.city_name  description,
            2 as areaLevel,
        </if>
        <if test="areaLevel == 2">
            drn.district_code  areaCode,
            drn.district_name  areaName,
            drn.district_name  description,
            3 as areaLevel,
        </if>
        <if test="areaLevel == 3">
            drn.street_code  areaCode,
            drn.street_name  areaName,
            drn.street_name  description,
            4 as areaLevel,
        </if>
    </sql>

    <sql id="whereGroupArea">
        <if test="areaLevel == 1">
            drn.city_code,
            drn.city_name
        </if>
        <if test="areaLevel == 2">
            drn.district_code,
            drn.district_name
        </if>
        <if test="areaLevel == 3">
            drn.street_code,
            drn.street_name
        </if>
    </sql>

    <select id="groupArea" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="selectGroupArea"/>
        <include refid="sumCnt"/>
        FROM
        ads.ads_ms_syndrome_process_case_info i
        <include refid="syndromeCaseInfoTableJoin"/>
        WHERE 1=1
        AND rep_card_flag ='0'
        AND <include refid="timeTypeWhere"/> BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>

        GROUP BY
        <include refid="whereGroupArea"/>
    </select>

    <select id="groupAddrType" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        <if test="'orgAddress' == addressType ">
            org_addr_type  description,
        </if>
        <if test="'livingAddress' == addressType ">
            living_addr_type  description,
        </if>
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between cast(#{startDate} AS DATE) and cast(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose" />
        group by
        <if test="'orgAddress' == addressType ">
            org_addr_type
        </if>
        <if test="'livingAddress' == addressType ">
            living_addr_type
        </if>
    </select>

    <select id="groupAge" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="selectAgeGroup"/>,
        <include refid="sumCnt"/>,
        value_sort as order
        FROM
        <include refid="fromWithAgeGroup"/>
        <include refid="syndromeCaseInfoTableJoin"/>
        WHERE 1=1
        AND i.rep_card_flag ='0'
        AND <include refid="timeTypeWhere"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        CASE
        WHEN ag.value_code IS NOT NULL THEN ag.value_name
        ELSE '不详'
        END,
        value_sort
        ORDER BY
        value_sort
    </select>
    <select id="groupSex" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        patient_sex_name,
        patient_sex_name as description,
        <include refid="sumCnt"/>
        FROM
        ads.ads_ms_syndrome_process_case_info i
        <include refid="syndromeCaseInfoTableJoin"/>
        WHERE 1=1
        AND rep_card_flag = '0'
        AND <include refid="timeTypeWhere"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by patient_sex_name  order by patient_sex_name
    </select>
    <select id="groupJob" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        fi_syn_person_type as patientJob,
        fi_syn_person_type as description,
        <include refid="sumCnt"/>
        from
        ads.ads_ms_syndrome_process_case_info i
        <include refid="syndromeCaseInfoTableJoin"/>
        WHERE 1=1
        AND rep_card_flag ='0'
        AND <include refid="timeTypeWhere"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose" />
        group by fi_syn_person_type order by processNewCnt desc  limit 5
    </select>

    <select id="overAll" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <choose>
            <when test="dateDimType == 'day'">
                <include refid="timeTypeWhere"/> AS statDate
            </when>
            <when test="dateDimType == 'week'">
                <include refid="weekDimSelectColumns"/>
            </when>
            <when test="dateDimType == 'meadow'">
                <include refid="tenDaysDimSelectColumns"/>
            </when>
            <when test="dateDimType == 'month'">
                <include refid="monthDimSelectColumns"/>
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterDimSelectColumns"/>
            </when>
            <when test="dateDimType == 'halfYear'">
                <include refid="monthDimSelectColumns"/>
            </when>
            <otherwise>
                <include refid="yearDimSelectColumns"/>
            </otherwise>
        </choose>,
        <include refid="sumCnt"/>

        FROM ads.ads_ms_syndrome_process_case_info i
        <include refid="joinDayDim"/>
        <include refid="syndromeCaseInfoTableJoin"/>

        WHERE 1=1
        AND i.rep_card_flag = '0'
        AND <include refid="timeTypeWhere"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)

        <include refid="whereDistributionAreaMultiChoose"/>

        <choose>
            <when test="dateDimType == 'day'">
                GROUP BY <include refid="timeTypeWhere"/>
                ORDER BY <include refid="timeTypeWhere"/>
            </when>
            <when test="dateDimType == 'week'">
                <include refid="weekDimGroupOrder"/>
            </when>
            <when test="dateDimType == 'meadow'">
                <include refid="tenDaysDimGroupOrder"/>
            </when>
            <when test="dateDimType == 'month'">
                <include refid="monthDimGroupOrder"/>
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterDimGroupOrder"/>
            </when>
            <when test="dateDimType == 'halfYear'">
                <include refid="monthDimGroupOrder"/>
            </when>
            <otherwise>
                <include refid="yearDimGroupOrder"/>
            </otherwise>
        </choose>
    </select>

    <select id="areaChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <choose>
            <when test="dateDimType == 'day'">
                <include refid="timeTypeWhere"/> AS statDate,
            </when>
            <when test="dateDimType == 'week'">
                <include refid="weekDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'meadow'">
                <include refid="tenDaysDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'month'">
                <include refid="monthDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'halfYear'">
                <include refid="monthDimSelectColumns"/>,
            </when>
            <otherwise>
                <include refid="yearDimSelectColumns"/>,
            </otherwise>
        </choose>

        <include refid="selectGroupArea"/>
        <include refid="sumCnt"/>

        FROM ads.ads_ms_syndrome_process_case_info i
        <include refid="joinDayDim"/>

        <include refid="syndromeCaseInfoTableJoin"/>

        WHERE 1=1
        AND i.rep_card_flag = '0'
        AND <include refid="timeTypeWhere"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>

        <choose>
            <when test="dateDimType == 'day'">
                GROUP BY <include refid="timeTypeWhere"/>,
                <include refid="whereGroupArea"/>
                ORDER BY <include refid="timeTypeWhere"/>
                <if test="'${whereGroupArea}' != null and '${whereGroupArea}' != ''">
                    , areaCode
                </if>
                , processNewCnt DESC
            </when>

            <when test="dateDimType == 'week'">
                GROUP BY <include refid="weekCommonGroupBy"/>,
                <include refid="whereGroupArea"/>
                ORDER BY year, week, processNewCnt DESC
            </when>

            <when test="dateDimType == 'meadow'">
                GROUP BY <include refid="tenDayCommonGroupBy"/>,
                <include refid="whereGroupArea"/>
                ORDER BY year, month, tenDay, processNewCnt DESC
            </when>

            <when test="dateDimType == 'month'">
                GROUP BY <include refid="monthCommonGroupBy"/>,
                <include refid="whereGroupArea"/>
                ORDER BY year, month, processNewCnt DESC
            </when>

            <when test="dateDimType == 'quarter'">
                GROUP BY <include refid="quarterCommonGroupBy"/>,
                <include refid="whereGroupArea"/>
                ORDER BY year, quarter, processNewCnt DESC
            </when>

            <when test="dateDimType == 'halfYear'">
                GROUP BY <include refid="monthCommonGroupBy"/>,
                <include refid="whereGroupArea"/>
                ORDER BY year, month, processNewCnt DESC
            </when>

            <otherwise>
                GROUP BY year
                <if test="'${whereGroupArea}' != null and '${whereGroupArea}' != ''">
                    , <include refid="whereGroupArea"/>
                </if>
                ORDER BY year, processNewCnt DESC
            </otherwise>
        </choose>
    </select>

    <select id="sexChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <choose>
            <when test="dateDimType == 'day'">
                <include refid="timeTypeWhere"/> AS statDate,
            </when>
            <when test="dateDimType == 'week'">
                <include refid="weekDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'meadow'">
                <include refid="tenDaysDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'month'">
                <include refid="monthDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'halfYear'">
                <include refid="monthDimSelectColumns"/>,
            </when>
            <otherwise>
                <include refid="yearDimSelectColumns"/>,
            </otherwise>
        </choose>

        patient_sex_name,
        patient_sex_name AS description,

        <include refid="sumCnt"/>

        FROM ads.ads_ms_syndrome_process_case_info i
        <include refid="joinDayDim"/>
        <include refid="syndromeCaseInfoTableJoin"/>

        WHERE 1=1
        AND i.rep_card_flag = '0'
        AND <include refid="timeTypeWhere"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>

        <choose>
            <when test="dateDimType == 'day'">
                GROUP BY <include refid="timeTypeWhere"/>, patient_sex_name
            </when>
            <when test="dateDimType == 'week'">
                GROUP BY <include refid="weekCommonGroupBy"/>, patient_sex_name
            </when>
            <when test="dateDimType == 'meadow'">
                GROUP BY <include refid="tenDayCommonGroupBy"/>, patient_sex_name
            </when>
            <when test="dateDimType == 'month'">
                GROUP BY <include refid="monthCommonGroupBy"/>, patient_sex_name
            </when>
            <when test="dateDimType == 'quarter'">
                GROUP BY <include refid="quarterCommonGroupBy"/>, patient_sex_name
            </when>
            <when test="dateDimType == 'halfYear'">
                GROUP BY <include refid="monthCommonGroupBy"/>, patient_sex_name
            </when>
            <otherwise>
                GROUP BY year, patient_sex_name
            </otherwise>
        </choose>

        <choose>
            <when test="dateDimType == 'day'">
                ORDER BY statDate, processNewCnt DESC
            </when>
            <when test="dateDimType == 'week'">
                ORDER BY year, week, processNewCnt DESC
            </when>
            <when test="dateDimType == 'meadow'">
                ORDER BY year, month, tenDay, processNewCnt DESC
            </when>
            <when test="dateDimType == 'month'">
                ORDER BY year, month, processNewCnt DESC
            </when>
            <when test="dateDimType == 'quarter'">
                ORDER BY year, quarter, processNewCnt DESC
            </when>
            <when test="dateDimType == 'halfYear'">
                ORDER BY year, month, processNewCnt DESC
            </when>
            <otherwise>
                ORDER BY year, processNewCnt DESC
            </otherwise>
        </choose>
    </select>

    <select id="ageChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <choose>
            <when test="dateDimType == 'day'">
                <include refid="timeTypeWhere"/> AS statDate,
            </when>
            <when test="dateDimType == 'week'">
                <include refid="weekDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'meadow'">
                <include refid="tenDaysDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'month'">
                <include refid="monthDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'halfYear'">
                <include refid="monthDimSelectColumns"/>,
            </when>
            <otherwise>
                <include refid="yearDimSelectColumns"/>,
            </otherwise>
        </choose>

        <include refid="selectAgeGroup"/>,
        ag.value_down AS ageOrder,

        <include refid="sumCnt"/>
        FROM
        <include refid="fromWithAgeGroup"/>
        <include refid="joinDayDim"/>

        <include refid="syndromeCaseInfoTableJoin"/>

        WHERE 1=1
        AND i.rep_card_flag = '0'
        AND <include refid="timeTypeWhere"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>

        <choose>
            <when test="dateDimType == 'day'">
                GROUP BY <include refid="timeTypeWhere"/>,
                ag.value_code, ag.value_name, ag.value_down
            </when>
            <when test="dateDimType == 'week'">
                GROUP BY <include refid="weekCommonGroupBy"/>,
                ag.value_code, ag.value_name, ag.value_down
            </when>
            <when test="dateDimType == 'meadow'">
                GROUP BY <include refid="tenDayCommonGroupBy"/>,
                ag.value_code, ag.value_name, ag.value_down
            </when>
            <when test="dateDimType == 'month'">
                GROUP BY <include refid="monthCommonGroupBy"/>,
                ag.value_code, ag.value_name, ag.value_down
            </when>
            <when test="dateDimType == 'quarter'">
                GROUP BY <include refid="quarterCommonGroupBy"/>,
                ag.value_code, ag.value_name, ag.value_down
            </when>
            <when test="dateDimType == 'halfYear'">
                GROUP BY <include refid="monthCommonGroupBy"/>,
                ag.value_code, ag.value_name, ag.value_down
            </when>
            <otherwise>
                GROUP BY year,
                ag.value_code, ag.value_name, ag.value_down
            </otherwise>
        </choose>

        <choose>
            <when test="dateDimType == 'day'">
                ORDER BY statDate, ageOrder, processNewCnt DESC
            </when>
            <when test="dateDimType == 'week'">
                ORDER BY year, week, ageOrder, processNewCnt DESC
            </when>
            <when test="dateDimType == 'meadow'">
                ORDER BY year, month, tenDay, ageOrder, processNewCnt DESC
            </when>
            <when test="dateDimType == 'month'">
                ORDER BY year, month, ageOrder, processNewCnt DESC
            </when>
            <when test="dateDimType == 'quarter'">
                ORDER BY year, quarter, ageOrder, processNewCnt DESC
            </when>
            <when test="dateDimType == 'halfYear'">
                ORDER BY year, month, ageOrder, processNewCnt DESC
            </when>
            <otherwise>
                ORDER BY year, ageOrder, processNewCnt DESC
            </otherwise>
        </choose>
    </select>

    <sql id="processStatQuery">
        COALESCE(sum(syndrome_identify_cnt), 0) as identifyCnt,
        COALESCE(count(syndrome_process_id), 0) as processNewCnt,
        COALESCE(sum(case when lo_outcome_status = '死亡' then 1 else 0 end) , 0) as processDeadCnt,
        COALESCE(sum(case when i.end_time is null  AND i.syndrome_process_id IS NOT null then 1 else 0 end) , 0) as processNowCnt,
        COALESCE(sum(case when lo_outcome_status='治愈' or lo_outcome_status='治疗成功' or lo_outcome_status='好转' then 1 else 0 end), 0) as processCureCnt,
        COALESCE(sum(case when severe_flag = '1' then 1 else 0 end), 0) as processSevereCnt,
        COALESCE(sum(case when virus_flag='1' then 1 else 0 end), 0) as processVirusCnt,
        COALESCE(sum(case when virus_positive_flag='1' then 1 else 0 end), 0) as processVirusPositiveCnt,
        COALESCE(sum(case when bacteria_flag='1' then 1 else 0 end), 0) as processBacteriaCnt,
        COALESCE(sum(case when bacteria_positive_flag='1' then 1 else 0 end), 0) as processBacteriaPositiveCnt,
        COALESCE(sum(case when bacteria_virus_flag='1' then 1 else 0 end), 0) as processBacteriaVirusCnt,
        COALESCE(sum(case when lv_visit_type_code = '4' then 1 else 0 end), 0) as processDischargedCasesCnt
    </sql>

    <select id="processStat" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        COALESCE(cur.identifyCnt,0) identifyCnt,
        COALESCE(lastD.identifyCnt,0) identifyCntLast,
        COALESCE(lastY.identifyCnt,0) identifyCntLastY,

        COALESCE(cur.processNewCnt,0) processNewCnt,
        COALESCE(lastD.processNewCnt,0) processNewCntLast,
        COALESCE(lastY.processNewCnt,0) processNewCntLastY,

        COALESCE(cur.processDeadCnt,0) processDeadCnt,
        COALESCE(lastD.processDeadCnt,0) processDeadCntLast,
        COALESCE(lastY.processDeadCnt,0) processDeadCntLastY,

        COALESCE(cur.processNowCnt,0) processNowCnt,
        COALESCE(lastD.processNowCnt,0) processNowCntLast,
        COALESCE(lastY.processNowCnt,0) processNowCntLastY,

        COALESCE(cur.processCureCnt,0) processCureCnt,
        COALESCE(lastD.processCureCnt,0) processCureCntLast,
        COALESCE(lastY.processCureCnt,0) processCureCntLastY,

        COALESCE(cur.processSevereCnt,0) processSevereCnt,
        COALESCE(lastD.processSevereCnt,0) processSevereCntLast,
        COALESCE(lastY.processSevereCnt,0) processSevereCntLastY,

        COALESCE(cur.processVirusCnt,0) processVirusCnt,
        COALESCE(lastD.processVirusCnt,0) processVirusCntLast,
        COALESCE(lastY.processVirusCnt,0) processVirusCntLastY,

        COALESCE(cur.processVirusPositiveCnt,0) processVirusPositiveCnt,
        COALESCE(lastD.processVirusPositiveCnt,0) processVirusPositiveCntLast,
        COALESCE(lastY.processVirusPositiveCnt,0) processVirusPositiveCntLastY,

        COALESCE(cur.processBacteriaCnt,0) processBacteriaCnt,
        COALESCE(lastD.processBacteriaCnt,0) processBacteriaCntLast,
        COALESCE(lastY.processBacteriaCnt,0) processBacteriaCntLastY,

        COALESCE(cur.processBacteriaPositiveCnt,0) processBacteriaPositiveCnt,
        COALESCE(lastD.processBacteriaPositiveCnt,0) processBacteriaPositiveCntLast,
        COALESCE(lastY.processBacteriaPositiveCnt,0) processBacteriaPositiveCntLastY,

        COALESCE(cur.processBacteriaVirusCnt,0) processBacteriaVirusCnt,
        COALESCE(lastD.processBacteriaVirusCnt,0) processBacteriaVirusCntLast,
        COALESCE(lastY.processBacteriaVirusCnt,0) processBacteriaVirusCntLastY,

        COALESCE(cur.processDischargedCasesCnt,0) processDischargedCasesCnt,
        COALESCE(lastD.processDischargedCasesCnt,0) processDischargedCasesCntLast,
        COALESCE(lastY.processDischargedCasesCnt,0) processDischargedCasesCntLastY
        from
        (
            select
            <include refid="processStatQuery"/>
            from
            ads.ads_ms_syndrome_process_case_info i
            <include refid="syndromeCaseInfoTableJoin"/>
            where
            i.rep_card_flag ='0'
            and<include refid="timeTypeWhere"/> between #{startDate} and #{endDate}
            <include refid="statCriteria" />
        ) cur
        full join
        (
            select
            <include refid="processStatQuery"/>
            from
            ads.ads_ms_syndrome_process_case_info i
            <include refid="syndromeCaseInfoTableJoin"/>
            where
            i.rep_card_flag ='0'
            and<include refid="timeTypeWhere"/> between #{lastStartDate} and #{lastEndDate}
            <include refid="statCriteria" />
        ) lastD
        on 1=1
        full join
        (
            select
            <include refid="processStatQuery"/>
            from
            ads.ads_ms_syndrome_process_case_info i
            <include refid="syndromeCaseInfoTableJoin"/>
            where
            i.rep_card_flag ='0'
            and<include refid="timeTypeWhere"/> between #{lastYStartDate} and #{lastYEndDate}
            <include refid="statCriteria" />
        ) lastY
        on 1=1
    </select>

    <sql id="statSql">
        sum(identify_cnt) as identifyCnt,
        sum(identify_cnt_avg_s) as identifyCntAvgS,
        sum(identify_cnt_last_y) as identifyCntLastY,

        sum(process_new_cnt) as processNewCnt,
        sum(process_new_cnt_avg_s) as processNewCntAvgS,
        sum(process_new_cnt_last_y) as processNewCntLastY,

        sum(process_dead_cnt) as processDeadCnt,
        sum(process_dead_cnt_avg_s) as processDeadCntAvgS,
        sum(process_dead_cnt_last_y) as processDeadCntLastY,

        sum(process_now_cnt) as processNowCnt,
        sum(process_now_cnt_avg_s) as processNowCntAvgS,
        sum(process_now_cnt_last_y) as processNowCntLastY,

        sum(process_cure_cnt) as processCureCnt,
        sum(process_cure_cnt_avg_s) as processCureCntAvgS,
        sum(process_cure_cnt_last_y) as processCureCntLastY,

        sum(process_severe_cnt) as processSevereCnt,
        sum(process_severe_cnt_avg_s) as processSevereCntAvgS,
        sum(process_severe_cnt_last_y) as processSevereCntLastY,
        
        sum(process_virus_cnt) as processVirusCnt,
        sum(process_virus_cnt_last_y) as processVirusCntLastY,
        
        sum(process_virus_positive_cnt) as processVirusPositiveCnt,
        sum(process_virus_positive_cnt_last_y) as processVirusPositiveCntLastY,
        
        sum(process_bacteria_cnt) as processBacteriaCnt,
        sum(process_bacteria_cnt_last_y) as processBacteriaCntLastY,
        
        sum(process_bacteria_positive_cnt) as processBacteriaPositiveCnt,
        sum(process_bacteria_positive_cnt_last_y) as processBacteriaPositiveCntLastY,
        
        sum(process_bacteria_virus_cnt) as processBacteriaVirusCnt,
        sum(process_bacteria_virus_cnt_last_y) as processBacteriaVirusCntLastY
    </sql>

    <sql id="statCriteria">
        <include refid="regionChoose" />
        <if test="syndromeCode != null and syndromeCode != ''">
            and i.fi_syn_syndrome_code = #{syndromeCode}
        </if>
    </sql>

    <sql id="timeTrendStatDateChoose">
        <choose>
            <when test="dateDimType == 'year'">
                ddd.year as statDate,
            </when>
            <when test="dateDimType == 'halfYear'">
                ddd.half_year_desc as statDate,
            </when>
            <when test="dateDimType == 'quarter'">
                ddd.quarter_desc as statDate,
            </when>
            <when test="dateDimType == 'month'">
                ddd.month_desc as statDate,
            </when>
            <when test="dateDimType == 'meadow'">
                ddd.ten_days_desc as statDate,
            </when>
            <when test="dateDimType == 'week'">
                ddd.week_desc as statDate,
            </when>
            <otherwise>
                ddd.date_day as statDate,
            </otherwise>
        </choose>
    </sql>

    <select id="timeTrendStat" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
            COALESCE(cur.statDate, lastY.statDate) as statDate,
            COALESCE(cur.identifyCnt, 0) as identifyCnt,
            COALESCE(lastY.identifyCnt, 0) as identifyCntLastY,
            COALESCE(cur.processNewCnt, 0) as processNewCnt,
            COALESCE(lastY.processNewCnt, 0) as processNewCntLastY,
            COALESCE(cur.processDeadCnt, 0) as processDeadCnt,
            COALESCE(lastY.processDeadCnt, 0) as processDeadCntLastY,
            COALESCE(cur.processNowCnt, 0) as processNowCnt,
            COALESCE(lastY.processNowCnt, 0) as processNowCntLastY,
            COALESCE(cur.processCureCnt, 0) as processCureCnt,
            COALESCE(lastY.processCureCnt, 0) as processCureCntLastY,
            COALESCE(cur.processSevereCnt, 0) as processSevereCnt,
            COALESCE(lastY.processSevereCnt, 0) as processSevereCntLastY,
            COALESCE(cur.processVirusCnt, 0) as processVirusCnt,
            COALESCE(lastY.processVirusCnt, 0) as processVirusCntLastY,
            COALESCE(cur.processVirusPositiveCnt, 0) as processVirusPositiveCnt,
            COALESCE(lastY.processVirusPositiveCnt, 0) as processVirusPositiveCntLastY,
            COALESCE(cur.processBacteriaCnt, 0) as processBacteriaCnt,
            COALESCE(lastY.processBacteriaCnt, 0) as processBacteriaCntLastY,
            COALESCE(cur.processBacteriaPositiveCnt, 0) as processBacteriaPositiveCnt,
            COALESCE(lastY.processBacteriaPositiveCnt, 0) as processBacteriaPositiveCntLastY,
            COALESCE(cur.processBacteriaVirusCnt, 0) as processBacteriaVirusCnt,
            COALESCE(lastY.processBacteriaVirusCnt, 0) as processBacteriaVirusCntLastY
        from
        (
            select
            ddd.year,
            <include refid="timeTrendStatDateChoose" />
            <include refid="processStatQuery" />
            from
            dim.dim_date_day ddd
            left join ads.ads_ms_syndrome_process_case_info i
            on
            ddd.date_day =
            <choose>
                <when test="timeType == 'onsetTime'">
                    coalesce(i.fi_syn_onset_day, i.fi_syn_visit_day)
                </when>
                <otherwise>
                    i.fi_syn_identify_day
                </otherwise>
            </choose>
            and i.rep_card_flag ='0'
            <if test="syndromeCode != null and syndromeCode != ''">
                and i.fi_syn_syndrome_code = #{syndromeCode}
            </if>
            left <include refid="regionJoinCondition"/>
            <include refid="regionChoose" />
            left <include refid="syndromeJoinCondition"/>
            where
            ddd.date_day between #{startDate} and #{endDate}
            group by statDate,ddd.year
        )cur
        full join
        (
            select
            ddd.year,
            <include refid="timeTrendStatDateChoose" />
            <include refid="processStatQuery" />
            from
            dim.dim_date_day ddd
            left join ads.ads_ms_syndrome_process_case_info i
            on
            ddd.date_day =
            <choose>
                <when test="timeType == 'onsetTime'">
                    coalesce(i.fi_syn_onset_day, i.fi_syn_visit_day)
                </when>
                <otherwise>
                    i.fi_syn_identify_day
                </otherwise>
            </choose>
            and i.rep_card_flag ='0'
            <if test="syndromeCode != null and syndromeCode != ''">
                and i.fi_syn_syndrome_code = #{syndromeCode}
            </if>
            left <include refid="regionJoinCondition"/>
            <include refid="regionChoose" />
            left <include refid="syndromeJoinCondition"/>
            where
            ddd.date_day between #{lastYStartDate} and #{lastYEndDate}
            group by statDate,ddd.year
        ) lastY
        <choose>
            <when test="dateDimType == 'day'">
                ON EXTRACT(MONTH FROM cur.statDate) = EXTRACT(MONTH FROM lastY.statDate)
                AND EXTRACT(DAY FROM cur.statDate) = EXTRACT(DAY FROM lastY.statDate)
            </when>
            <when test="dateDimType == 'year'">
                ON cur.year-1 =lastY.year
            </when>
            <otherwise>
                ON SUBSTRING(cur.statDate, 5) = SUBSTRING(lastY.statDate, 5) AND cur.year - 1 = lastY.YEAR
            </otherwise>
        </choose>
        order by statDate
    </select>



    <select id="groupIdentifyClass" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        identify_class,
        identify_class as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between cast(#{startDate} AS DATE) and cast(#{endDate} AS DATE)
        and identify_class is not null
        <include refid="whereDistributionAreaMultiChoose" />
        group by identify_class order by identify_class
    </select>

    <select id="groupOutcomeStatus" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        outcome_status,
        outcome_status as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between cast(#{startDate} AS DATE) and cast(#{endDate} AS DATE)
        and outcome_status is not null
        <include refid="whereDistributionAreaMultiChoose" />
        group by outcome_status order by outcome_status
    </select>

    <select id="groupSymptomFirst" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        symptom_first,
        symptom_first as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between cast(#{startDate} AS DATE) and cast(#{endDate} AS DATE)
        and symptom_first is not null
        <include refid="whereDistributionAreaMultiChoose" />
        group by symptom_first order by processNewCnt desc
    </select>

    <select id="groupHistoryBefore" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        history_before,
        history_before as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between cast(#{startDate} AS DATE) and cast(#{endDate} AS DATE)
        and history_before is not null
        <include refid="whereDistributionAreaMultiChoose" />
        group by history_before order by processNewCnt desc
    </select>

    <select id="groupPathogenResNominal" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        pathogen_res_nominal,
        pathogen_res_nominal as description,
        <include refid="sumCommonCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between cast(#{startDate} AS DATE) and cast(#{endDate} AS DATE)
        and pathogen_res_nominal is not null
        <include refid="whereDistributionAreaMultiChoose" />
        group by pathogen_res_nominal order by pathogen_res_nominal
    </select>

    <select id="groupDiseaseName" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        dii.disease_code as descriptionCode,
        dii.disease_name as description,
        COALESCE(count(syndrome_process_id), 0)  as processNewCnt ,
        COALESCE(sum(case when lo_outcome_status = '死亡' then 1 else 0 end), 0)  as processDeadCnt,
        COALESCE(sum(case when end_time is null then 1 else 0 end), 0) as processNowCnt,
        COALESCE(sum(case when lo_outcome_status='治愈' or lo_outcome_status='治疗成功' or lo_outcome_status='好转' then 1 else 0 end), 0) as processCureCnt,
        COALESCE(sum(case when severe_flag = '1' then 1 else 0 end), 0)  as processSevereCnt,
        COALESCE(sum(case when virus_flag='1' then 1 else 0 end), 0) as processVirusCnt,
        COALESCE(sum(case when virus_positive_flag='1' then 1 else 0 end), 0) as processVirusPositiveCnt,
        COALESCE(sum(case when bacteria_flag='1' then 1 else 0 end), 0) as processBacteriaCnt,
        COALESCE(sum(case when bacteria_positive_flag='1' then 1 else 0 end), 0) as processBacteriaPositiveCnt,
        COALESCE(sum(case when bacteria_virus_flag='1' then 1 else 0 end), 0) as processBacteriaVirusCnt
        from ads.ads_ms_syndrome_process_case_info i
        <include refid="syndromeCaseInfoTableJoin"/>
        where <include refid="timeTypeWhere"/>
        between cast(#{startDate} AS DATE) and cast(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose" />
        group by dii.disease_code, dii.disease_name order by dii.disease_code, dii.disease_name
    </select>

    <select id="groupAreaCurrentLevel" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        <include refid="selectGroupAreaCurrentLevel"/>
        <include refid="sumCnt"/>
        from
        ads.ads_ms_syndrome_process_case_info i
        <include refid="syndromeCaseInfoTableJoin"/>
        where 1=1
        and i.rep_card_flag ='0'
        AND <include refid="timeTypeWhere"/> BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose" />
        group by
        <include refid="whereGroupAreaCurrentLevel"/>
    </select>
    <sql id="selectGroupAreaCurrentLevel">
        <if test="areaLevel == 1">
            drn.province_code  areaCode,
                drn.province_name  areaName,
                drn.province_name  description,
                1 as areaLevel,
        </if>
        <if test="areaLevel == 2">
            drn.city_code  areaCode,
                drn.city_name  areaName,
                drn.city_name  description,
                2 as areaLevel,
        </if>
        <if test="areaLevel == 3">
            drn.district_code  areaCode,
                drn.district_name  areaName,
                drn.district_name  description,
                3 as areaLevel,
        </if>
        <if test="areaLevel == 4">
            drn.street_code  areaCode,
                drn.street_name  areaName,
                drn.street_name  description,
                4 as areaLevel,
        </if>
    </sql>

    <sql id="whereGroupAreaCurrentLevel">
        <if test="areaLevel == 1">
            drn.province_code,
            drn.province_name
        </if>
        <if test="areaLevel == 2">
            drn.city_code,
            drn.city_name
        </if>
        <if test="areaLevel == 3">
            drn.district_code,
            drn.district_name
        </if>
        <if test="areaLevel == 4">
            drn.street_code,
                drn.street_name
        </if>
    </sql>

    <select id="groupAreaDripDetail" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        (
        <if test="areaLevel &lt;= 1">
            <include refid="provinceQuery"/>
            union all
        </if>
        <if test="areaLevel &lt;= 2">
            <include refid="cityQuery"/>
            union all
        </if>
        <if test="areaLevel &lt;= 3">
            <include refid="districtQuery"/>
            union all
        </if>
        <if test="areaLevel &lt;= 4">
            <include refid="streetQuery"/>
        </if>
        )
        order by
        provinceCode DESC,
        cityCode DESC,
        districtCode DESC,
        streetCode DESC,
        areaLevel
    </select>
    <!-- Org Address - Province Level -->
    <sql id="provinceQuery">
        select
        drn.province_code as areaCode,
        drn.province_name as areaName,
        drn.province_name as description,
        drn.province_code as provinceCode,
        drn.province_name as provinceName,
        null as cityCode,
        null as cityName,
        null as districtCode,
        null as districtName,
        null as streetCode,
        null as streetName,
        1 as areaLevel,
        <include refid="sumCnt"/>
        from
        ads.ads_ms_syndrome_process_case_info i
        <include refid="regionJoinCondition"/>
        where 1=1
        AND rep_card_flag ='0'
        AND <include refid="timeTypeWhere"/> BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by drn.province_code, drn.province_name
    </sql>

    <!-- Org Address - City Level -->
    <sql id="cityQuery">
        select
        drn.city_code as areaCode,
        drn.city_name as areaName,
        drn.city_name as description,
        drn.province_code as provinceCode,
        drn.province_name as provinceName,
        drn.city_code as cityCode,
        drn.city_name as cityName,
        null as districtCode,
        null as districtName,
        null as streetCode,
        null as streetName,
        2 as areaLevel,
        <include refid="sumCnt"/>
        from
        ads.ads_ms_syndrome_process_case_info i
        <include refid="regionJoinCondition"/>
        where 1=1
        AND rep_card_flag ='0'
        AND <include refid="timeTypeWhere"/> BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by drn.province_code, drn.province_name, drn.city_code, drn.city_name
    </sql>

    <!-- Org Address - District Level -->
    <sql id="districtQuery">
        select
        drn.district_code as areaCode,
        drn.district_name as areaName,
        drn.district_name as description,
        drn.province_code as provinceCode,
        drn.province_name as provinceName,
        drn.city_code as cityCode,
        drn.city_name as cityName,
        drn.district_code as districtCode,
        drn.district_name as districtName,
        null as streetCode,
        null as streetName,
        3 as areaLevel,
        <include refid="sumCnt"/>
        from
        ads.ads_ms_syndrome_process_case_info i
        <include refid="regionJoinCondition"/>
        where 1=1
        AND rep_card_flag ='0'
        AND <include refid="timeTypeWhere"/> BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by
        drn.province_code, drn.province_name, drn.city_code, drn.city_name, drn.district_code, drn.district_name
    </sql>

    <!-- Org Address - Street Level -->
    <sql id="streetQuery">
        select
        drn.street_code as areaCode,
        drn.street_name as areaName,
        drn.street_name as description,
        drn.province_code as provinceCode,
        drn.province_name as provinceName,
        drn.city_code as cityCode,
        drn.city_name as cityName,
        drn.district_code as districtCode,
        drn.district_name as districtName,
        drn.street_code as streetCode,
        drn.street_name as streetName,
        4 as areaLevel,
        <include refid="sumCnt"/>
        from
        ads.ads_ms_syndrome_process_case_info i
        <include refid="regionJoinCondition"/>
        where 1=1
        AND rep_card_flag ='0'
        AND <include refid="timeTypeWhere"/> BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by drn.province_code, drn.province_name,
        drn.city_code, drn.city_name,
        drn.district_code, drn.district_name,
        drn.street_code, drn.street_name
    </sql>

    <select id="groupDiseaseCodeAndName" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        syndrome_code as descriptionCode,
        syndrome_name as description,
        COALESCE(sum(process_new_cnt),0)  as processNewCnt
        from
        <include refid="dayDb"/>
        where 1=1 and day between cast(#{startDate} AS DATE) and cast(#{endDate} AS DATE)
        <include refid="whereDistribution" />
        group by syndrome_code, syndrome_name order by processNewCnt desc
    </select>

    <select id="qOQTrendChart" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <choose>
            <when test="dateDimType == 'day'">
                <include refid="timeTypeWhere"/> AS statDate,
            </when>
            <when test="dateDimType == 'week'">
                <include refid="weekDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'meadow'">
                <include refid="tenDaysDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'month'">
                <include refid="monthDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterDimSelectColumns"/>,
            </when>
            <otherwise>
                <include refid="yearDimSelectColumns"/>,
            </otherwise>
        </choose>
        <include refid="sumCnt"/>
        FROM ads.ads_ms_syndrome_process_case_info i
        <include refid="joinDayDim"/>
        <include refid="syndromeCaseInfoTableJoin"/>
        where <include refid="timeTypeWhere"/> between cast(#{startDate} AS DATE) and cast(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose" />
        <choose>
            <when test="dateDimType == 'day'">
                GROUP BY <include refid="timeTypeWhere"/>
                ORDER BY <include refid="timeTypeWhere"/>
            </when>
            <when test="dateDimType == 'week'">
                <include refid="weekDimGroupOrder"/>
            </when>
            <when test="dateDimType == 'meadow'">
                <include refid="tenDaysDimGroupOrder"/>
            </when>
            <when test="dateDimType == 'month'">
                <include refid="monthDimGroupOrder"/>
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterDimGroupOrder"/>
            </when>
            <otherwise>
                <include refid="yearDimGroupOrder"/>
            </otherwise>
        </choose>
    </select>

    <!-- 月度维表选择列 -->
    <sql id="monthDimSelectColumns">
        d.month_desc AS statDate,
        d.month_desc AS description,
    d.year,
    d.month
    </sql>

    <!-- 月度维表分组与排序 -->
    <sql id="monthDimGroupOrder">
        GROUP BY
        d.year, d.month, d.month_desc
    ORDER BY
        d.year DESC,
        d.month DESC
    </sql>

    <sql id="quarterDimSelectColumns">
        d.quarter_desc AS statDate,
        d.quarter_desc AS description,
    d.year,
    d.quarter
    </sql>

    <sql id="quarterDimGroupOrder">
        GROUP BY
        d.year, d.quarter, d.quarter_desc
    ORDER BY
        d.year DESC,
        d.quarter DESC
    </sql>

    <sql id="tenDaysDimSelectColumns">
        d.ten_days_desc AS statDate,
        d.ten_days_desc AS description,
    d.year,
    d.month,
    d.ten_days as tenDay
    </sql>

    <sql id="tenDaysDimGroupOrder">
        GROUP BY
        d.year, d.month, d.ten_days, d.ten_days_desc
    ORDER BY
        d.year,
        d.month,
        d.ten_days
    </sql>

    <sql id="joinDayDim">
        JOIN dim.dim_date_day d
        ON
        <include refid="timeTypeWhere"/>
        BETWEEN d.start_time AND d.end_time
    </sql>

    <sql id="weekDimSelectColumns">
        d.week_desc AS statDate,
        d.week_desc AS description,
    d.year,
    d.week
    </sql>

    <sql id="weekDimGroupOrder">
        GROUP BY
        d.year, d.week, d.week_desc
    ORDER BY
        d.year,
        d.week
    </sql>

    <sql id="yearDimSelectColumns">
        CONCAT(d.year, '年') AS statDate,
        CONCAT(d.year, '年') AS description,
    d.year
    </sql>

    <sql id="yearDimGroupOrder">
        GROUP BY
        d.year
    ORDER BY
        d.year DESC
    </sql>

    <sql id="sumCnt">
        count(distinct i.syndrome_process_id) as processNewCnt ,
        sum(case when i.lo_outcome_status = '死亡' then 1 else 0 end) as processDeadCnt,
        sum(case when i.end_time is null then 1 else 0 end) as processNowCnt,
        sum(case when i.lo_dead_this_flag = '1' then 1 else 0 end) as processDeadIllnessCnt,
        sum(case when i.lo_outcome_status='治愈' or lo_outcome_status='治疗成功' or lo_outcome_status='好转' then 1 else 0 end) as processCureCnt
    </sql>

    <sql id="selectAgeGroup">
        CASE
        WHEN ag.value_code IS NOT NULL THEN ag.value_name
        ELSE '不详'
    END AS patient_age_group,
    CASE
        WHEN ag.value_code IS NOT NULL THEN ag.value_name
        ELSE '不详'
    END AS description
    </sql>

    <sql id="fromWithAgeGroup">
        ads.ads_ms_syndrome_process_case_info i
    LEFT JOIN dim.dim_age_group ag
        ON i.fi_syn_patient_age &gt;= ag.value_down
        AND i.fi_syn_patient_age &lt; ag.value_up
    </sql>

    <!-- 季度分组 -->
    <sql id="quarterCommonGroupBy">
         year, quarter, quarter_desc
    </sql>

    <!-- 周维度分组字段 -->
    <sql id="weekCommonGroupBy">
        year, week, week_desc
    </sql>

    <!-- 月维度 GroupBy 字段 -->
    <sql id="monthCommonGroupBy">
       year, month, month_desc
    </sql>

    <sql id="tenDayCommonGroupBy">
        year, month, ten_days, ten_days_desc
    </sql>

</mapper>