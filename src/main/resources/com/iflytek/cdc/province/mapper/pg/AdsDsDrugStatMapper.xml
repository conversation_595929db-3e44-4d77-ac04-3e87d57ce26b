<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsDsDrugStatMapper">

    <sql id="areaFilter">
        <if test="provinceCode != null and provinceCode != ''">
            and pharmacy_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and pharmacy_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and pharmacy_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and pharmacy_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and pharmacy_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and pharmacy_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and pharmacy_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and pharmacy_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="topicCriteria">
        <if test="topicDrugNames != null and topicDrugNames.size() != 0">
            and drug_name in (
            <foreach collection="topicDrugNames" item="item" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
    </sql>

  <select id="selectDrugSalesBy" resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.DrugSalesVO">
      select drug_id,
             drug_name,
             sum(drug_cnt)        as sales_volume,
             sum(drug_cnt_last_y) as ly_sales_volume,
             case when sum(drug_cnt_last_y) > 0 then ((sum(drug_cnt)*1.0 - sum(drug_cnt_last_y)) / sum(drug_cnt_last_y)) else 0 end as growthRate
      from ads.ads_ds_drug_stat
      where 1 = 1
      <if test="startDate != null and endDate != null">
          and day between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
      </if>
      <if test="drugType != null and drugType != ''">
          and drug_class = #{drugType,jdbcType=VARCHAR}
      </if>
      <include refid="areaFilter"/>
      <include refid="topicCriteria"/>

      group by drug_id, drug_name
      order by 5 desc, 3 desc
  </select>

  <select id="drugDailySales" resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.DrugDailySalesVO">
      select drug_name,
             day,
             sum(drug_cnt)        as sales_volume,
             sum(drug_cnt_last_y) as ly_sales_volume,
             case when sum(drug_cnt_last_y) > 0 then ((sum(drug_cnt)*1.0 - sum(drug_cnt_last_y)) / sum(drug_cnt_last_y)) else 0 end as growthRate
      from ads.ads_ds_drug_stat
      where day between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}
      <if test="keyword != null and keyword != ''">
          and drug_name = #{keyword,jdbcType=VARCHAR}
      </if>
      <include refid="areaFilter"/>
      <include refid="topicCriteria"/>
      group by drug_name, day
      order by day
    </select>
</mapper>