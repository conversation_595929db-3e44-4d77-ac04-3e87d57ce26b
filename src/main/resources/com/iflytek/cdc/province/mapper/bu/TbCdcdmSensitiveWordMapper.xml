<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcdmSensitiveWordMapper">

    <select id="getAllWords" resultType="string">
        select distinct tag_value
        from app.tb_cdcdm_sensitive_word
        where delete_flag = '0'
    </select>
</mapper>
