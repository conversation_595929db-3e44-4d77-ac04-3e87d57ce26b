<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcdmDataAttrMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.bu.TbCdcdmDataAttr">
    <!--@mbg.generated-->
    <!--@Table tb_cdcdm_data_attr-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="attr_count" jdbcType="INTEGER" property="attrCount" />
    <result column="attr_value_count" jdbcType="INTEGER" property="attrValueCount" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="data_url" jdbcType="VARCHAR" property="dataUrl" />
    <result column="data_dict_code" jdbcType="VARCHAR" property="dataDictCode" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", attr_count, attr_value_count, "type", data_url, data_dict_code, note, 
    is_deleted, create_time, creator, update_time, updater
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcdm_data_attr
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from tb_cdcdm_data_attr
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataAttr">
    <!--@mbg.generated-->
    insert into tb_cdcdm_data_attr (id, "name", attr_count,
      attr_value_count, "type", data_url, 
      data_dict_code, note, is_deleted, 
      create_time, creator, update_time, 
      updater)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{attrCount,jdbcType=INTEGER}, 
      #{attrValueCount,jdbcType=INTEGER}, #{type,jdbcType=VARCHAR}, #{dataUrl,jdbcType=VARCHAR}, 
      #{dataDictCode,jdbcType=VARCHAR}, #{note,jdbcType=VARCHAR}, #{isDeleted,jdbcType=SMALLINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataAttr">
    <!--@mbg.generated-->
    insert into tb_cdcdm_data_attr
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        id,
      </if>
      <if test="name != null and name != ''">
        "name",
      </if>
      <if test="attrCount != null">
        attr_count,
      </if>
      <if test="attrValueCount != null">
        attr_value_count,
      </if>
      <if test="type != null and type != ''">
        "type",
      </if>
      <if test="dataUrl != null and dataUrl != ''">
        data_url,
      </if>
      <if test="dataDictCode != null and dataDictCode != ''">
        data_dict_code,
      </if>
      <if test="note != null and note != ''">
        note,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null and creator != ''">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null and updater != ''">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null and name != ''">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="attrCount != null">
        #{attrCount,jdbcType=INTEGER},
      </if>
      <if test="attrValueCount != null">
        #{attrValueCount,jdbcType=INTEGER},
      </if>
      <if test="type != null and type != ''">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="dataUrl != null and dataUrl != ''">
        #{dataUrl,jdbcType=VARCHAR},
      </if>
      <if test="dataDictCode != null and dataDictCode != ''">
        #{dataDictCode,jdbcType=VARCHAR},
      </if>
      <if test="note != null and note != ''">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null and updater != ''">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataAttr">
    <!--@mbg.generated-->
    update tb_cdcdm_data_attr
    <set>
      <if test="name != null and name != ''">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="attrCount != null">
        attr_count = #{attrCount,jdbcType=INTEGER},
      </if>
      <if test="attrValueCount != null">
        attr_value_count = #{attrValueCount,jdbcType=INTEGER},
      </if>
      <if test="type != null and type != ''">
        "type" = #{type,jdbcType=VARCHAR},
      </if>
      <if test="dataUrl != null and dataUrl != ''">
        data_url = #{dataUrl,jdbcType=VARCHAR},
      </if>
      <if test="dataDictCode != null and dataDictCode != ''">
        data_dict_code = #{dataDictCode,jdbcType=VARCHAR},
      </if>
      <if test="note != null and note != ''">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null and updater != ''">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataAttr">
    <!--@mbg.generated-->
    update tb_cdcdm_data_attr
    set "name" = #{name,jdbcType=VARCHAR},
      attr_count = #{attrCount,jdbcType=INTEGER},
      attr_value_count = #{attrValueCount,jdbcType=INTEGER},
      "type" = #{type,jdbcType=VARCHAR},
      data_url = #{dataUrl,jdbcType=VARCHAR},
      data_dict_code = #{dataDictCode,jdbcType=VARCHAR},
      note = #{note,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>