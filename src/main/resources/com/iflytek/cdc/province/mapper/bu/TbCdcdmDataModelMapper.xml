<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcdmDataModelMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.bu.TbCdcdmDataModel">
        <!--@mbg.generated-->
        <!--@Table tb_cdcdm_data_model-->
        <id column="model_id" jdbcType="VARCHAR" property="modelId"/>
        <result column="model_name" jdbcType="VARCHAR" property="modelName"/>
        <result column="model_type" jdbcType="VARCHAR" property="modelType"/>
        <result column="is_built_in" jdbcType="SMALLINT" property="isBuiltIn"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        model_id,
        model_name,
        model_type,
        is_built_in,
        note
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from tb_cdcdm_data_model
        where model_id = #{modelId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete
        from tb_cdcdm_data_model
        where model_id = #{modelId,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataModel">
        <!--@mbg.generated-->
        insert into tb_cdcdm_data_model (model_id, model_name, model_type,
                                                is_built_in, note)
        values (#{modelId,jdbcType=VARCHAR}, #{modelName,jdbcType=VARCHAR}, #{modelType,jdbcType=VARCHAR},
                #{isBuiltIn,jdbcType=SMALLINT}, #{note,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataModel">
        <!--@mbg.generated-->
        insert into tb_cdcdm_data_model
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="modelId != null and modelId != ''">
                model_id,
            </if>
            <if test="modelName != null and modelName != ''">
                model_name,
            </if>
            <if test="modelType != null and modelType != ''">
                model_type,
            </if>
            <if test="isBuiltIn != null">
                is_built_in,
            </if>
            <if test="note != null and note != ''">
                note,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="modelId != null and modelId != ''">
                #{modelId,jdbcType=VARCHAR},
            </if>
            <if test="modelName != null and modelName != ''">
                #{modelName,jdbcType=VARCHAR},
            </if>
            <if test="modelType != null and modelType != ''">
                #{modelType,jdbcType=VARCHAR},
            </if>
            <if test="isBuiltIn != null">
                #{isBuiltIn,jdbcType=SMALLINT},
            </if>
            <if test="note != null and note != ''">
                #{note,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataModel">
        <!--@mbg.generated-->
        update tb_cdcdm_data_model
        <set>
            <if test="modelName != null and modelName != ''">
                model_name = #{modelName,jdbcType=VARCHAR},
            </if>
            <if test="modelType != null and modelType != ''">
                model_type = #{modelType,jdbcType=VARCHAR},
            </if>
            <if test="isBuiltIn != null">
                is_built_in = #{isBuiltIn,jdbcType=SMALLINT},
            </if>
            <if test="note != null and note != ''">
                note = #{note,jdbcType=VARCHAR},
            </if>
        </set>
        where model_id = #{modelId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataModel">
        <!--@mbg.generated-->
        update tb_cdcdm_data_model
        set model_name  = #{modelName,jdbcType=VARCHAR},
            model_type  = #{modelType,jdbcType=VARCHAR},
            is_built_in = #{isBuiltIn,jdbcType=SMALLINT},
            note        = #{note,jdbcType=VARCHAR}
        where model_id = #{modelId,jdbcType=VARCHAR}
    </update>

    <select id="getModelLatestVersion" resultType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelVersion">
        select *
        from tb_cdcdm_data_model_version
        where is_deleted != 1
        <if test="status != null and status != ''">
            and status = #{status, jdbcType=VARCHAR}
        </if>
        <if test="modelId != null and modelId != ''">
            and model_id = #{modelId, jdbcType=VARCHAR}
        </if>
        order by create_time desc
        limit 1
    </select>

    <select id="getFormListByModelVersionId" resultType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelForm">
        select *
        from tb_cdcdm_data_model_form mf
        where mf.is_deleted = 0 and mf.model_version_id = #{modelVersionId,jdbcType=VARCHAR}
        order by mf.order_flag desc
    </select>
</mapper>