<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcewDataFavoritesDetailMapper">

    <sql id="Base_Column_List">
        d.id, d.data_favorites_id, d.data_type, d.data_record_id, d.data_model_id, d.status, d.data_query_url,
        d.data_query_param, d.collect_time
    </sql>

    <insert id="batchInsertCollection">
        insert into tb_cdcew_data_favorites_detail
        (id, data_favorites_id, data_type, data_record_id, data_model_id, status, data_query_url,
        data_query_param, collect_time)
        values
        <foreach collection="details" item="item" separator=",">
            (#{item.id, jdbcType=VARCHAR}, #{item.dataFavoritesId, jdbcType=VARCHAR}, #{item.dataType, jdbcType=VARCHAR},
            #{item.dataRecordId, jdbcType=VARCHAR}, #{item.dataModelId, jdbcType=VARCHAR}, #{item.status, jdbcType=INTEGER},
            #{item.dataQueryUrl, jdbcType=VARCHAR}, #{item.dataQueryParam, jdbcType=VARCHAR}, #{item.collectTime, jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <update id="updateCollectionStatus">
        update tb_cdcew_data_favorites_detail
        set status = 0
        where data_favorites_id = #{favoritesId, jdbcType=VARCHAR}
        <if test="dataRecordIds != null and dataRecordIds.size() &gt; 0">
            and data_record_id in
            <foreach collection="dataRecordIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="getFavoritesDetails" resultType="com.iflytek.cdc.province.entity.bu.TbCdcewDataFavoritesDetail">
        select <include refid="Base_Column_List"/>, f.model_version_id
        from tb_cdcew_data_favorites_detail d
        left join tb_cdcew_data_favorites f
        on d.data_favorites_id = f.id
        where
        f.delete_flag = '0' and d.status = 1
        and f.id = #{favoritesId, jdbcType=VARCHAR}
        <if test="startDate != null and endDate != null">
            and d.collect_time between #{startDate, jdbcType=TIMESTAMP} and #{endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="dataType != null and dataType != ''">
            and d.data_type like concat('%', #{dataType, jdbcType=VARCHAR}, '%')
        </if>
        order by collect_time desc
    </select>

</mapper>