<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.iflytek.cdc.province.mapper.pg.MonitorWarningAnalysisMapper">

     <!--
          传染病病程表 ads_ms_infect_process_case_info
          报卡信息表   ads_rep_infect_report_info
      -->
    <!-- 传染病病例表区域选择 -->
    <sql id="caseRegionChoose">
        <if test="provinceCode != null and provinceCode != ''">
            and drn.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and drn.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and drn.district_code = #{districtCode}
        </if>
        <if test="provinceCodeList != null and provinceCodeList.size() > 0">
            and drn.province_code in
            <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodeList != null and cityCodeList.size() > 0">
            and drn.city_code in
            <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodeList != null and districtCodeList.size() > 0">
            and drn.district_code in
            <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and drn.province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and drn.city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and drn.district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <!-- 病例时间范围选择 -->
    <sql id="caseTimeRangeChoose">
        <if test="startDate != null and endDate != null">
            and fi_inf_identify_time between #{startDate} and #{endDate}
        </if>
        <if test="diagStartTime != null and diagEndTime != null">
            and fi_inf_diag_time between #{diagStartTime} and #{diagEndTime}
        </if>
        <if test="repStartTime != null and repEndTime != null">
            and li_inf_identify_day between #{repStartTime} and #{repEndTime}
        </if>
        <if test="taskPushStartTime != null and taskPushEndTime != null">
            and li_inf_check_time between #{taskPushStartTime} and #{taskPushEndTime}
        </if>
        <if test="supReportStartTime != null and supReportEndTime != null">
            and li_inf_identify_day between #{supReportStartTime} and #{supReportEndTime}
        </if>
    </sql>

    <!-- 诊断 -->
    <sql id="diagnoseJoinCondition">
        left join ads.ads_disease_process_diagnose dpd
        on dpd.process_id = i.infect_process_id
    </sql>

    <sql id="infectProcessTable">
        ads.ads_ms_infect_process_case_info i
        left join dim.dim_region_nation drn
        on i.org_region_code = drn.region_code
        left join dim.dim_infected_info dii
        on dii.id = i.fi_inf_infect_code
    </sql>

    <sql id="caseCriteria">
        <include refid="caseRegionChoose"/>
        <include refid="caseTimeRangeChoose"/>
        <if test="infectClass != null and infectClass != ''">
            and infected_name = #{infectClass}
        </if>
        <if test="orgId != null and orgId != ''">
            and fi_inf_org_id = #{orgId}
        </if>
        <if test="diagTypeName != null and diagTypeName != ''">
            and li_inf_infect_class = #{diagTypeName}
        </if>
        <if test="deptName != null and deptName != ''">
            and fi_inf_dept_name = #{deptName}
        </if>
        <if test="visitTypeName != null and visitTypeName != ''">
            and fi_inf_visit_type_name = #{visitTypeName}
        </if>
        <if test="checkedIdList != null and checkedIdList.size() > 0">
            and infect_process_id in
            <foreach collection="checkedIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="queryKey != null and queryKey != ''">
            and (
                patient_name like concat('%', #{queryKey,jdbcType=VARCHAR}, '%')
                or fi_inf_org_name like concat('%', #{queryKey,jdbcType=VARCHAR}, '%')
                or infect_process_id like concat('%', #{queryKey,jdbcType=VARCHAR}, '%')
            )
        </if>
    </sql>

    <sql id="reportTable">
        ads.ads_rep_infect_report_info r
        left join dim.dim_infected_info dii
        on dii.id = r.infect_code
    </sql>

    <!-- 报卡信息表区域选择 -->
    <sql id="reportRegionChoose">
        <if test="reportProvinceCode != null and reportProvinceCode != ''">
            and drn.province_code = #{reportProvinceCode}
        </if>
        <if test="reportCityCode != null and reportCityCode != ''">
            and drn.city_code = #{reportCityCode}
        </if>
        <if test="reportDistrictCode != null and reportDistrictCode != ''">
            and drn.district_code = #{reportDistrictCode}
        </if>

        <if test="patientProvinceCode != null and patientProvinceCode != ''">
            and drn.province_code = #{patientProvinceCode}
        </if>
        <if test="patientCityCode != null and patientCityCode != ''">
            and drn.city_code = #{patientCityCode}
        </if>
        <if test="patientDistrictCode != null and patientDistrictCode != ''">
            and drn.district_code = #{patientDistrictCode}
        </if>
    </sql>

    <!-- 报卡信息表条件 -->
    <sql id="reportCriteria">
        <include refid="reportRegionChoose"/>
        <if test="orgId != null and orgId != ''">
            and report_org_code = #{orgId}
        </if>
        <if test="startDate != null and endDate != null">
            and r.etl_create_datetime between #{startDate} and #{endDate}
        </if>
        <if test="diagStartTime != null and diagEndTime != null">
            and diag_datetime between #{diagStartTime} and #{diagEndTime}
        </if>
        <if test="repStartTime != null and repEndTime != null">
            and report_datetime between #{repStartTime} and #{repEndTime}
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            and infect_name = #{diseaseName}
        </if>
        <if test="infectClass != null and infectClass != ''">
            and infect_name = #{infectClass}
        </if>
        <if test="infectCode != null and infectCode != ''">
            and infect_code = #{infectCode}
        </if>
        <if test="casesCategory != null and casesCategory != ''">
            and cases_type_name = #{casesCategory}
        </if>
        <if test="clinicalSeverity != null and clinicalSeverity != ''">
            and covid_severe_type_name = #{clinicalSeverity}
        </if>
        <if test="personType != null and personType != ''">
            and nultitude_type_name = #{personType}
        </if>
        <if test="queryKey != null and queryKey != ''">
            and patient_name like concat('%', #{queryKey,jdbcType=VARCHAR}, '%')
        </if>
    </sql>



    <select id="threeCatePeopleView" resultType="com.iflytek.cdc.province.model.vo.ThreeCatePeopleViewVO">
        select
            count(1) as reportCard,
            COALESCE(sum(case when NULLIF(confirm_aids_datetime, null) is null then 0 else 1 end), 0) as labPositive,
            COALESCE(sum(case when diag_state_name = '确诊' then 1 else 0 end), 0) as diagnosed
        from
            <include refid="reportTable" />
        where
            1=1
            <include refid="reportCriteria"/>
    </select>


    <select id="threeCatePeopleTimeTrend" resultType="com.iflytek.cdc.province.model.vo.ThreeCatePeopleViewVO">
        select
            count(1) as reportCard,
            COALESCE(sum(case when NULLIF(confirm_aids_datetime, null) is null then 0 else 1 end), 0) as labPositive,
            COALESCE(sum(case when diag_state_name = '确诊' then 1 else 0 end), 0) as diagnosed,
            CAST(report_datetime AS DATE) as statDate
        from
            <include refid="reportTable" />
        where
            1=1
            <include refid="reportCriteria"/>
        group by CAST(report_datetime AS DATE)
        order by CAST(report_datetime AS DATE)
    </select>

    <select id="vennView" resultType="com.iflytek.cdc.province.model.vo.ThreeCatePeopleViewVO">
        select
            count(1) as allThree,
            coalesce(sum(case when (report_datetime is not null and confirm_aids_datetime is null and diag_state_name <![CDATA[<>]]> '确诊') then 1 else 0 end), 0) as onlyReport,
            coalesce(sum(case when (confirm_aids_datetime is not null and report_datetime is null and diag_state_name <![CDATA[<>]]> '确诊') then 1 else 0 end), 0) as onlyLabPositive,
            coalesce(sum(case when (diag_state_name = '确诊' and confirm_aids_datetime is null and report_datetime is null) then 1 else 0 end), 0) as onlyDiagnosed,
            coalesce(sum(case when (report_datetime is not null and diag_state_name = '确诊' and confirm_aids_datetime is null) then 1 else 0 end), 0) as reportAndDiagnosed,
            coalesce(sum(case when (report_datetime is not null and confirm_aids_datetime is not null and diag_state_name <![CDATA[<>]]> '确诊') then 1 else 0 end), 0) as reportAndLab,
            coalesce(sum(case when (confirm_aids_datetime is not null and diag_state_name = '确诊' and report_datetime is null) then 1 else 0 end), 0) as labAndDiagnosed
        from
            <include refid="reportTable" />
        where
            1=1
            <include refid="reportCriteria"/>
    </select>


    <select id="reportOverall" resultType="com.iflytek.cdc.province.model.vo.ReportOverallVO">
        select
            count(1) as report,
            count(1) as shouldReport,
            coalesce(sum(case when report_datetime is not null then 1 else 0 end), 0) as hasReported,
            coalesce(sum(case when report_datetime is null then 1 else 0 end), 0) as notReport,
            coalesce(sum(case when (diag_state_name = '确诊' and report_datetime is null) then 1 else 0 end), 0) as diagnosedNotReport,
            coalesce(sum(case when (confirm_aids_datetime is not null and report_datetime is null) then 1 else 0 end), 0) as labPositiveNotReport
        from
            <include refid="reportTable" />
        where
            1=1
            <include refid="reportCriteria"/>
    </select>


    <select id="selectDiagnoseProcess" resultType="com.iflytek.cdc.province.model.vo.DiagnoseProcessVO">
        select
            infect_process_id as processId,
            fi_inf_org_name as orgName,
            concat(coalesce(drn.city_name, ''), '-', coalesce(drn.district_name, '')) as orgAreaName,
            patient_name as patientName,
            fi_inf_dept_name as deptName,
            fi_inf_visit_type_name as visitTypeName,
            i.li_inf_infect_class as diagType,
            CAST(fi_inf_diag_time as DATE) as diagTime,
            dii.infected_name as infectClass,
            dii.infected_code as infectCode,
            '-' as checkResult,
            '-' as excludeReason,
            case when DATE_PART('day', i.etl_create_datetime - fi_inf_identify_time) = 0 then 1 else 0 end as hasReportInDay,
            li_inf_identify_day as recentlyReportTime,
            li_org_name as recentlyReportOrg,
            '-' as supReportCode
        from
            <include refid="infectProcessTable"/>
            <include refid="diagnoseJoinCondition"/>
        where
            1=1
            <include refid="caseCriteria"/>
    </select>

    <select id="selectPatientDetailByProcessId" resultType="com.iflytek.cdc.province.model.vo.PatientVisitDetailVO">
        select
            '-' as patientId,
            patient_name as patientName,
            patient_sex_name as patientSex,
            CAST(patient_birth_day as DATE) as patientBirthDay,
            infected_type_name as infectClass,
            lv_visit_time as admissionTime,
            lv_discharge_time as leaveTime
        from
            <include refid="infectProcessTable"/>
        <where>
            infect_process_id = #{processId}
        </where>
    </select>

    <select id="selectReportProcess" resultType="com.iflytek.cdc.province.model.vo.ReportProcessVO">
        select
            ms_infect_report_id as processId,
            card_id as reportCardId,
            concat(coalesce(visit_addr_city_name, ''), '-', coalesce(visit_addr_district_name, '')) as reportOrgArea,
            concat(coalesce(living_addr_city_name,''), '-', coalesce(living_addr_district_name, '')) as patientArea,
            patient_name as patientName,
            CAST(diag_datetime as DATE) as diagDate,
            infect_name as diseaseName,
            infect_code as infectCode,
            cases_type_name as casesCategory,
            covid_severe_type_name as clinicalSeverity,
            nultitude_type_name as personType
        from
            <include refid="reportTable"/>
        where
            1=1
            <include refid="reportCriteria"/>
    </select>

    <select id="selectReportProcessByProcessId" resultType="com.iflytek.cdc.province.model.vo.ReportProcessVO">
        select
            ms_infect_report_id as processId,
            card_id as reportCardId,
            concat(coalesce(visit_addr_city_name, ''), '-', coalesce(visit_addr_district_name, '')) as reportOrgArea,
            concat(coalesce(living_addr_city_name,''), '-', coalesce(living_addr_district_name, '')) as patientArea,
            patient_name as patientName,
            CAST(diag_datetime as DATE) as diagDate,
            infect_name as diseaseName,
            infect_code as infectCode,
            cases_type_name as casesCategory,
            covid_severe_type_name as clinicalSeverity,
            nultitude_type_name as personType,
            patient_sex_name as sexName,
            patient_contact_name as patientContactName,
            patient_contact_phone as patientContactPhone,
            living_addr_detail as livingAddrDetail,
            input_type_name as inputTypeName,
            dead_datetime as deadDatetime,
            card_fill_date as cardFillDate,
            report_addr_poi as reportAddrPoi,
            valid_time_province as validTimeProvince,
            audit_create_date as auditCreateDate,
            delete_user_id as deleteUserId,
            notes,
            card_status as cardStatus,
            patient_identity_type as patientIdentityType,
            age_unit as ageUnit,
            addr_belong_type as addrBelongType,
            nultitude_type_name as nultitudeTypeName,
            onset_datetime as onsetDatetime,
            infect_parent_name as infectParentName,
            valid_time as validTime,
            report_zone_code as reportZoneCode,
            do_datetime as doDatetime,
            valid_time_district as validTimeDistrict,
            valid_flag as validFlag,
            dead_valid_time as deadValidTime,
            '-' as auditUserOrg,
            '-' as delUserOrg
        from
            <include refid="reportTable"/>
        where ms_infect_report_id = #{processId}
    </select>



    <select id="investigatedCaseStat" resultType="com.iflytek.cdc.province.model.vo.InvestigatedCaseVO">
        select
            coalesce(sum(case when li_inf_identify_time is not null then 1 else 0 end), 0) as report,
            coalesce(sum(case when li_inf_check_time is null then 1 else 0 end), 0) as tobeInvest,
            coalesce(sum(case when fi_inf_out_time is not null then 1 else 0 end), 0) as excluded,
            0 as supReport
        from
            <include refid="infectProcessTable"/>
        where
            1=1
            <include refid="caseCriteria"/>
    </select>

    <select id="selectInvestigatedCase" resultType="com.iflytek.cdc.province.model.vo.InvestigatedCaseVO">
        select
            infect_process_id as processId,
            infect_process_id as reportcardId,
            concat(coalesce(drn.city_name, ''), '-', coalesce(drn.district_name, '')) as area,
            fi_inf_org_name as orgName,
            patient_name as investPatient,
            dii.infected_name as diseaseName,
            fi_inf_infect_code as infectCode,
            '-' as checkResult,
            '-' as excludeReason,
            to_char(li_inf_check_time, 'YYYY-MM-DD HH24:MI:SS') as taskPushTime,
            to_char(fi_inf_out_time, 'YYYY-MM-DD HH24:MI:SS') as supReportTime
        from
            <include refid="infectProcessTable"/>
        where
            1=1
            <include refid="caseCriteria"/>
    </select>

    <select id="investigatedTaskStat" resultType="com.iflytek.cdc.province.model.vo.InvestigatedTaskVO">
        select
            coalesce(count(r.ms_infect_report_id),0)                                                            as total,
            coalesce(sum(case when r.card_exclude_flag = '0' then 1 else 0 end),0)                            as waitingCount,
            coalesce(sum(case when r.card_exclude_flag = '1' then 1 else 0 end),0)                            as completeCount,
            0                                                                                                 as identifyIntimeCnt,
            coalesce(sum(case when dii.infected_type_name = '甲类传染病' then 1 else 0 end),0)                         as casesA,
            coalesce(sum(case when dii.infected_type_name = '乙类传染病' then 1 else 0 end),0)                         as casesB,
            coalesce(sum(case when dii.infected_type_name = '丙类传染病' then 1 else 0 end),0)                         as casesC
        from
            <include refid="reportTable"/>
        where
            1=1
            <include refid="reportCriteria"/>
    </select>

    <select id="taskStatAreaGroup" resultType="com.iflytek.cdc.province.model.vo.InvestigatedTaskVO">
        select
            infect_process_id as taskId,
            concat(coalesce(drn.city_name, ''), '-', coalesce(drn.district_name, '')) as areaName,
            dii.infected_name as infectClass,
            dii.infected_code as infectCode,
            coalesce(sum(case when li_inf_check_time is null then 1 else 0 end), 0) as total,
            coalesce(sum(case when li_inf_check_time is null and li_inf_check_time is null then 1 else 0 end), 0) as waitingCount,
            coalesce(sum(case when li_inf_check_time is null and li_inf_check_time is not null then 1 else 0 end), 0) as completeCount,
            coalesce(sum(case when li_inf_check_time is null and dii.infected_type_name = '甲类传染病' then 1 else 0 end), 0) as casesA,
            coalesce(sum(case when li_inf_check_time is null and dii.infected_type_name = '乙类传染病' then 1 else 0 end), 0) as casesB,
            coalesce(sum(case when li_inf_check_time is null and dii.infected_type_name = '丙类传染病' then 1 else 0 end), 0) as casesC
        from
            <include refid="infectProcessTable"/>
        where
            1=1
            <include refid="caseCriteria"/>
        group by infect_process_id, drn.city_name, drn.district_name, dii.infected_name, dii.infected_code
    </select>


    <select id="checkTaskStat" resultType="com.iflytek.cdc.province.model.vo.InvestTaskStatVO">
        select
            infect_process_id as taskId,
            drn.district_name as areaName,
            dii.infected_name as infectClass,
            dii.infected_code as infectCode,
            0 as identifyIntimeCnt,
            coalesce(sum(case when li_inf_check_time is null then 1 else 0 end), 0) as checkTaskTotal,
            coalesce(sum(case when li_inf_check_time is null and li_inf_check_time is null then 1 else 0 end), 0) as waitingCount,
            coalesce(sum(case when li_inf_check_time is null and li_inf_check_time is not null then 1 else 0 end), 0) as completeCount,
            coalesce(sum(case when li_inf_check_time is null and dii.infected_type_name = '甲类传染病' then 1 else 0 end), 0) as checkTaskCaseA,
            coalesce(sum(case when li_inf_check_time is null and dii.infected_type_name = '乙类传染病' then 1 else 0 end), 0) as checkTaskCaseB,
            coalesce(sum(case when li_inf_check_time is null and dii.infected_type_name = '丙类传染病' then 1 else 0 end), 0) as checkTaskCaseC
        from
            <include refid="infectProcessTable"/>
        where
            1=1
            <include refid="caseCriteria"/>
        group by infect_process_id, drn.city_name, drn.district_name, dii.infected_name, dii.infected_code
    </select>

    <select id="investTaskStatInfect" resultType="com.iflytek.cdc.province.model.vo.InvestTaskStatVO">
        select
            drn.district_name as areaName,
            coalesce(sum(case when li_inf_check_time is null then 1 else 0 end), 0) as investTaskInfect
        from
            <include refid="infectProcessTable"/>
        where
            1=1
            <include refid="caseCriteria"/>
        group by drn.district_name
    </select>


    <select id="investTaskStatReport" resultType="com.iflytek.cdc.province.model.vo.InvestTaskStatVO">
        select
            report_zone_name as areaName,
            coalesce(sum(case when card_exclude_flag = '0' then 1 else 0 end), 0) as investTaskReport
        from
            <include refid="reportTable"/>
        where
            1=1
            <include refid="reportCriteria"/>
        group by report_zone_name
    </select>

</mapper>