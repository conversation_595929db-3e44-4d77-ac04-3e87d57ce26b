<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrInitDiagInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsEdrInitDiagInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_edr_init_diag_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="initial_diagnosis_code" jdbcType="VARCHAR" property="initialDiagnosisCode" />
    <result column="initial_diagnosis" jdbcType="VARCHAR" property="initialDiagnosis" />
    <result column="serial_number" jdbcType="TIMESTAMP" property="serialNumber" />
    <result column="current_addr_code" jdbcType="VARCHAR" property="currentAddrCode" />
    <result column="current_addr" jdbcType="VARCHAR" property="currentAddr" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="mpi_id" jdbcType="VARCHAR" property="mpiId" />
    <result column="create_org" jdbcType="VARCHAR" property="createOrg" />
    <result column="create_org_name" jdbcType="VARCHAR" property="createOrgName" />
    <result column="update_org" jdbcType="VARCHAR" property="updateOrg" />
    <result column="update_org_name" jdbcType="VARCHAR" property="updateOrgName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, etl_create_datetime, etl_update_datetime, event_id, empi_id, org_code, org_id, 
    org_name, initial_diagnosis_code, initial_diagnosis, serial_number, current_addr_code, 
    current_addr, create_user, create_time, update_user, update_time, mpi_id, create_org, 
    create_org_name, update_org, update_org_name
  </sql>

  <select id="getDiagInfoByEventId" parameterType="string"
          resultType="com.iflytek.cdc.province.model.vo.edrcase.AdsEdrInitDiagInfoVO">
    select
    id,
    etl_create_datetime,
    etl_update_datetime,
    event_id,
    empi_id,
    org_code,
    org_id,
    org_name,
    initial_diagnosis_code,
    initial_diagnosis,
    serial_number,
    current_addr_code,
    current_addr
    from ads.ads_edr_init_diag_info
    where event_id = #{eventId}
    order by serial_number desc
  </select>
</mapper>