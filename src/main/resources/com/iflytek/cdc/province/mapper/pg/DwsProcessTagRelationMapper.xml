<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.DwsProcessTagRelationMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.DwsProcessTagRelation">
    <!--@mbg.generated-->
    <!--@Table dws.dws_process_tag_relation-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="process_id" jdbcType="VARCHAR" property="processId" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="disease_type" jdbcType="VARCHAR" property="diseaseType" />
    <result column="disease_code" jdbcType="VARCHAR" property="diseaseCode" />
    <result column="tag_id" jdbcType="VARCHAR" property="tagId" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, process_id, empi_id, disease_type, disease_code, tag_id, etl_create_datetime, 
    etl_update_datetime
  </sql>
</mapper>