<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMsLifeInfoMapper">

    <select id="getPatientList" resultType="com.iflytek.cdc.province.model.vo.PatientListVO">
        select
        life_id                 as lifeId,
        lv_patient_id           as patientId,
        patient_name            as patientName,
        patient_sex_name        as sexName,
        patient_identity_no     as identityNo,
        patient_birth_day       as birthDay,
        lv_job                  as job,
        lv_living_addr_detail   as livingAddress
        from ads.ads_ms_life_info
        where 1=1
        <if test="patientId != null and patientId != ''">
            and lv_patient_id like concat('%', #{patientId}, '%')
        </if>
        <if test="patientName != null and patientName != ''">
            and patient_name like concat('%', #{patientName}, '%')
        </if>
        <if test="sexName != null and sexName != ''">
            and patient_sex_name = #{sexName}
        </if>
        <if test="identityNo != null and identityNo != ''">
            and patient_identity_no like concat('%', #{identityNo}, '%')
        </if>
        <if test="birthDay != null">
            and patient_birth_day = #{birthDay}
        </if>
        order by etl_update_datetime desc
    </select>

    <select id="getPatientListByRecordIds" resultType="com.iflytek.cdc.province.model.vo.PatientListVO">
        select
        life_id                 as lifeId,
        empi_id           as patientId,
        patient_name            as patientName,
        patient_sex_name        as sexName,
        patient_identity_no     as identityNo,
        patient_birth_day       as birthDay,
        lv_job                  as job,
        lv_living_addr_detail   as livingAddress
        from ads.ads_ms_life_info
        where empi_id in
        <foreach collection="recordIds" index="index" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

</mapper>