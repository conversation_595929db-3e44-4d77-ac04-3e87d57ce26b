<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMsInfectProcessStatMapper">

    <sql id="infectCaseInfoTableJoin">
        <include refid="regionJoinCondition"/>
        <include refid="infectJoinCondition"/>
    </sql>

    <sql id="regionJoinCondition">
        join dim.dim_region_nation drn
        on
        <choose>
            <when test="addressType == 'livingAddress'">
                i.living_region_code = drn.region_code
            </when>
            <when test="addressType == 'companyAddress'">
                i.company_region_code = drn.region_code
            </when>
            <otherwise>
                i.org_region_code = drn.region_code
            </otherwise>
        </choose>
    </sql>

    <sql id="infectJoinCondition">
        join dim.dim_infected_info dii
        on dii.id = i.fi_inf_infect_code
    </sql>

    <sql id="regionChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and drn.province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and drn.city_code in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and drn.district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and drn.street_code  in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and drn.province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and drn.city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and drn.district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and drn.province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and drn.city_code in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and drn.district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and drn.street_code in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and drn.province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and drn.city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and drn.district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and drn.street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="processAreaChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and fi_inf_living_addr_province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and fi_inf_living_addr_city_code  in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and fi_inf_living_addr_district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and fi_inf_living_addr_street_code  in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and fi_inf_living_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and fi_inf_living_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and fi_inf_living_addr_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and fi_inf_org_addr_province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and fi_inf_org_addr_city_code in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and fi_inf_org_addr_district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and fi_inf_org_addr_street_code in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and fi_inf_org_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and fi_inf_org_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and fi_inf_org_addr_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and fi_inf_org_addr_street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="areaChooseDeprecated">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="provinceCode != null and provinceCode != ''">
                    and fi_inf_living_addr_province_code = #{provinceCode}
                </if>
                <if test="cityCode != null and cityCode != ''">
                    and fi_inf_living_addr_city_code = #{cityCode}
                </if>
                <if test="districtCode != null and districtCode != ''">
                    and fi_inf_living_addr_func_district_code = #{districtCode}
                </if>
                <if test="streetCode != null and streetCode != ''">
                    and fi_inf_living_addr_street_code = #{streetCode}
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and fi_inf_living_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and fi_inf_living_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and fi_inf_living_addr_func_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and fi_inf_living_addr_street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="provinceCode != null and provinceCode != ''">
                    and fi_inf_org_addr_province_code = #{provinceCode}
                </if>
                <if test="cityCode != null and cityCode != ''">
                    and fi_inf_org_addr_city_code = #{cityCode}
                </if>
                <if test="districtCode != null and districtCode != ''">
                    and fi_inf_org_addr_func_district_code = #{districtCode}
                </if>
                <if test="streetCode != null and streetCode != ''">
                    and fi_inf_org_addr_street_code = #{streetCode}
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and fi_inf_org_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and fi_inf_org_addr_province_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and fi_inf_org_addr_func_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and fi_inf_org_addr_street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="areaMultiChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and fi_inf_living_addr_province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and fi_inf_living_addr_city_code  in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and fi_inf_living_addr_func_district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and fi_inf_living_addr_street_code  in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and fi_inf_living_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and fi_inf_living_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and fi_inf_living_addr_func_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                    and fi_inf_org_addr_province_code in
                    <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodeList != null and cityCodeList.size() > 0">
                    and fi_inf_org_addr_city_code in
                    <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodeList != null and districtCodeList.size() > 0">
                    and fi_inf_org_addr_func_district_code in
                    <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodeList != null and streetCodeList.size() > 0">
                    and fi_inf_org_addr_street_code in
                    <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="provinceCodes != null and provinceCodes.size()>0">
                    and fi_inf_org_addr_province_code in
                    <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="cityCodes != null and cityCodes.size()>0">
                    and fi_inf_org_addr_city_code in
                    <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="districtCodes != null and districtCodes.size()>0">
                    and fi_inf_org_addr_func_district_code in
                    <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="streetCodes != null and streetCodes.size()>0">
                    and fi_inf_org_addr_street_code in
                    <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="statSql">
        sum(identify_cnt) as identifyCnt,
        sum(identify_cnt_avg_s) as identifyCntAvgS,
        sum(identify_cnt_last_y) as identifyCntLastY,

        sum(process_new_cnt) as processNewCnt,
        sum(process_new_cnt_avg_s) as processNewCntAvgS,
        sum(process_new_cnt_last_y) as processNewCntLastY,

        sum(process_dead_cnt) as processDeadCnt,
        sum(process_dead_cnt_avg_s) as processDeadCntAvgS,
        sum(process_dead_cnt_last_y) as processDeadCntLastY,

        sum(process_now_cnt) as processNowCnt,
        sum(process_now_cnt_avg_s) as processNowCntAvgS,
        sum(process_now_cnt_last_y) as processNowCntLastY,

        sum(process_dead_illness_cnt) as processDeadIllnessCnt,
        sum(process_dead_illness_cnt_avg_s) as processDeadIllnessCntAvgS,
        sum(process_dead_illness_cnt_last_y) as processDeadIllnessCntLastY,

        sum(process_cure_cnt) as processCureCnt,
        sum(process_cure_cnt_avg_s) as processCureCntAvgS,
        sum(process_cure_cnt_last_y) as processCureCntLastY,

        sum(process_severe_cnt) as processSevereCnt,
        sum(process_severe_cnt_avg_s) as processSevereCntAvgS,
        sum(process_severe_cnt_last_y) as processSevereCntLastY
    </sql>

    <sql id="dayDb">
        <choose>
            <when test="timeType == 'approveTime'">
                ads.ads_ms_infect_process_ct_d d
            </when>
            <when test="timeType == 'onsetTime' or timeType == 'visitTime' or timeType == 'firVisitTime'">
                ads.ads_ms_infect_process_ot_d d
            </when>
            <when test="timeType == 'deathTime'">
                ads.ads_ms_infect_process_dt_d d
            </when>
            <otherwise>
                ads.ads_ms_infect_process_it_d d
            </otherwise>
        </choose>
    </sql>

    <sql id="weekDb">
        <choose>
            <when test="timeType == 'approveTime'">
                ads.ads_ms_infect_process_ct_w
            </when>
            <when test="timeType == 'onsetTime'">
                ads.ads_ms_infect_process_ot_w
            </when>
            <when test="timeType == 'deathTime'">
                ads.ads_ms_infect_process_dt_w
            </when>
            <otherwise>
                ads.ads_ms_infect_process_it_w
            </otherwise>
        </choose>
    </sql>

    <sql id="tenDaysDb">
        <choose>
            <when test="timeType == 'approveTime'">
                ads.ads_ms_infect_process_ct_td
            </when>
            <when test="timeType == 'onsetTime'">
                ads.ads_ms_infect_process_ot_td
            </when>
            <when test="timeType == 'deathTime'">
                ads.ads_ms_infect_process_dt_td
            </when>
            <otherwise>
                ads.ads_ms_infect_process_it_td
            </otherwise>
        </choose>
    </sql>

    <sql id="monthDb">
        <choose>
            <when test="timeType == 'approveTime'">
                ads.ads_ms_infect_process_ct_m
            </when>
            <when test="timeType == 'onsetTime'">
                ads.ads_ms_infect_process_ot_m
            </when>
            <when test="timeType == 'deathTime'">
                ads.ads_ms_infect_process_dt_m
            </when>
            <otherwise>
                ads.ads_ms_infect_process_it_m
            </otherwise>
        </choose>
    </sql>

    <sql id="quarterDb">
        <choose>
            <when test="timeType == 'approveTime'">
                ads.ads_ms_infect_process_ct_q
            </when>
            <when test="timeType == 'onsetTime'">
                ads.ads_ms_infect_process_ot_q
            </when>
            <when test="timeType == 'deathTime'">
                ads.ads_ms_infect_process_dt_q
            </when>
            <otherwise>
                ads.ads_ms_infect_process_it_q
            </otherwise>
        </choose>
    </sql>

    <sql id="yearDb">
        <choose>
            <when test="timeType == 'approveTime'">
                ads.ads_ms_infect_process_ct_y
            </when>
            <when test="timeType == 'onsetTime'">
                ads.ads_ms_infect_process_ot_y
            </when>
            <when test="timeType == 'deathTime'">
                ads.ads_ms_infect_process_dt_y
            </when>
            <otherwise>
                ads.ads_ms_infect_process_it_y
            </otherwise>
        </choose>
    </sql>

    <sql id="sumNotifiableCount">
        COALESCE(COUNT(DISTINCT infect_process_id), 0) AS notifiableDiseaseNewCnt,
        COALESCE(COUNT(DISTINCT CASE WHEN dii.infected_type_name IN ('甲类传染病', '乙类传染病') THEN infect_process_id END), 0) AS classAAndBNewCnt,
        COALESCE(COUNT(DISTINCT CASE WHEN dii.infected_type_name = '甲类传染病' THEN infect_process_id END), 0) AS classANewCnt,
        COALESCE(COUNT(DISTINCT CASE WHEN dii.infected_type_name = '乙类传染病' THEN infect_process_id END), 0) AS classBNewCnt,
        COALESCE(COUNT(DISTINCT CASE WHEN dii.infected_type_name = '丙类传染病' THEN infect_process_id END), 0) AS classCNewCnt,
        COALESCE(COUNT(DISTINCT CASE WHEN dii.infected_type_name = '其他法定管理及重点监测传染病' THEN infect_process_id END), 0) AS otherNewCnt,
        COALESCE(SUM(CASE WHEN lo_outcome_status = '死亡' THEN 1 ELSE 0 END), 0) AS notifiableDiseaseDeadCnt,
        COALESCE(SUM(CASE WHEN dii.infected_type_name IN ('甲类传染病', '乙类传染病') AND lo_outcome_status = '死亡' THEN 1 ELSE 0 END), 0) AS classAAndBDeadCnt,
        COALESCE(SUM(CASE WHEN dii.infected_type_name = '甲类传染病' AND lo_outcome_status = '死亡' THEN 1 ELSE 0 END), 0) AS classADeadCnt,
        COALESCE(SUM(CASE WHEN dii.infected_type_name = '乙类传染病' AND lo_outcome_status = '死亡' THEN 1 ELSE 0 END), 0) AS classBDeadCnt,
        COALESCE(SUM(CASE WHEN dii.infected_type_name = '丙类传染病' AND lo_outcome_status = '死亡' THEN 1 ELSE 0 END), 0) AS classCDeadCnt,
        COALESCE(SUM(CASE WHEN dii.infected_type_name = '其他法定管理及重点监测传染病' AND lo_outcome_status = '死亡' THEN 1 ELSE 0 END), 0) AS otherDeadCnt
    </sql>

    <sql id="statCriteria">
        <include refid="areaMultiChoose" />
        <if test="infectCode != '' and infectCode != null">
            and infect_code = #{infectCode}
        </if>
        <if test="infectType != '' and infectType != null">
            and infect_type = #{infectType}
        </if>
        <if test="infectCodeList != null and infectCodeList.size() > 0">
            and infect_code in
            <foreach collection="infectCodeList " item= "item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="infectTypes != null and infectTypes.size() > 0">
            and infect_type in
            <foreach collection="infectTypes" item= "item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="infectClass != '' and infectClass != null">
            and infect_class = #{infectClass}
        </if>
        <if test = "filterCodes != null and filterCodes.size() > 0">
            and infect_code not in
            <foreach collection="filterCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="infectTransmitType != '' and infectTransmitType != null">
            and infect_transmit_type = #{infectTransmitType}
        </if>
    </sql>

    <select id="processStat" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
            COALESCE(sum(infect_identify_cnt), 0) as identifyCnt,
            COALESCE(count(distinct infect_process_id), 0) as processNewCnt,
            COALESCE(sum(case when lo_outcome_status = '死亡' then 1 else 0 end) , 0) as processDeadCnt,
            COALESCE(sum(case when end_time is null then 1 else 0 end) , 0) as processNowCnt,
            COALESCE(sum(case when lo_dead_this_flag = '1' then 1 else 0 end) , 0) as processDeadIllnessCnt,
            COALESCE(sum(case when lo_outcome_status='治愈' or lo_outcome_status='治疗成功' or lo_outcome_status='好转' then 1 else 0 end) , 0) as processCureCnt,
            COALESCE(sum(case when severe_flag = '1' then 1 else 0 end), 0) as processSevereCnt
        from ads.ads_ms_infect_process_case_info i
        <include refid="regionJoinCondition"/>
        where
        i.rep_card_flag = '0' and
        <choose>
            <when test="timeType == 'approveTime'">
                li_inf_check_time
            </when>
            <when test="timeType == 'onsetTime' or timeType == 'visitTime' or timeType == 'firVisitTime'">
                coalesce( fi_inf_onset_time, fi_inf_visit_time )
            </when>
            <when test="timeType == 'deathTime'">
                lo_dead_time
            </when>
            <otherwise>
                fi_inf_identify_time
            </otherwise>
        </choose> between #{startDate} and #{endDate}
        <include refid="regionChoose" />

    </select>

    <sql id="dayCntStatByDetailTableQuery">
        <include refid="dynamicDayField"/> as statDate,
        COALESCE(sum(infect_identify_cnt), 0) as identifyCnt,
        COALESCE(count(distinct infect_process_id), 0) as processNewCnt,
        COALESCE(sum(case when lo_outcome_status = '死亡' then 1 else 0 end) , 0) as processDeadCnt,
        COALESCE(sum(case when end_time is null then 1 else 0 end) , 0) as processNowCnt,
        COALESCE(sum(case when lo_dead_this_flag = '1' then 1 else 0 end) , 0) as processDeadIllnessCnt,
        COALESCE(sum(case when lo_outcome_status='治愈' or lo_outcome_status='治疗成功' or lo_outcome_status='好转' then 1 else 0 end) , 0) as processCureCnt,
        COALESCE(sum(case when severe_flag = '1' then 1 else 0 end), 0) as processSevereCnt
    </sql>

    <select id="dayCntStatByDetailTable" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">

        select
            COALESCE(cur.statDate, lastYData.statDate) as statDate,
            COALESCE(cur.identifyCnt, 0) as identifyCnt,
            COALESCE(lastYData.identifyCnt, 0) as identifyCntLastY,
            COALESCE(cur.processNewCnt, 0) as processNewCnt,
            COALESCE(lastYData.processNewCnt, 0) as processNewCntLastY,
            COALESCE(cur.processDeadCnt, 0) as processDeadCnt,
            COALESCE(lastYData.processDeadCnt, 0) as processDeadCntLastY,
            COALESCE(cur.processNowCnt, 0) as processNowCnt,
            COALESCE(lastYData.processNowCnt, 0) as processNowCntLastY,
            COALESCE(cur.processDeadIllnessCnt, 0) as processDeadIllnessCnt,
            COALESCE(lastYData.processDeadIllnessCnt, 0) as processDeadIllnessCntLastY,
            COALESCE(cur.processCureCnt, 0) as processCureCnt,
            COALESCE(lastYData.processCureCnt, 0) as processCureCntLastY,
            COALESCE(cur.processSevereCnt, 0) as processSevereCnt,
            COALESCE(lastYData.processSevereCnt, 0) as processSevereCntLastY
        from
        (
            select
            <include refid="dayCntStatByDetailTableQuery"/>
            from
                ads.ads_ms_infect_process_case_info i
                <include refid="infectCaseInfoTableJoin"/>
            where
                rep_card_flag ='0'
            <if test="startDate != null and endDate != null">
                <include refid="timeTypeCriteria" />
            </if>
            <include refid="dimensionTableAndAreaCriteria" />
            group by statDate
        ) cur
        full join
        (
            select
            <include refid="dayCntStatByDetailTableQuery"/>
            from <include refid="infectProcessTable"/>
            where
            rep_card_flag ='0'
            <if test="lastYStartDate != null and lastYEndDate != null">
                <include refid="timeTypeLastYCriteria" />
            </if>
            <include refid="dimensionTableAndAreaCriteria" />
            group by statDate
        ) lastYData
        ON EXTRACT(MONTH FROM cur.statDate) = EXTRACT(MONTH FROM lastYData.statDate)
        AND EXTRACT(DAY FROM cur.statDate) = EXTRACT(DAY FROM lastYData.statDate)
        order by statDate
    </select>

    <sql id="whereDistribution">
        <if test="infectType != null and infectType != ''">
            and fi_inf_infect_type = #{infectType}
        </if>
        <if test="infectTransmitType != null and infectTransmitType != ''">
            and fi_inf_infect_transmit_type = #{infectTransmitType}
        </if>
        <if test="infectCode != null and infectCode != ''">
            and fi_inf_infect_code = #{infectCode}
        </if>
        <if test = "filterCodes != null and filterCodes.size() > 0">
            and fi_inf_infect_code not in
            <foreach collection="filterCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            and fi_inf_infect_name = #{diseaseName}
        </if>
        <include refid="areaChooseDeprecated"/>
    </sql>

    <sql id="whereDistributionAreaMultiChoose">
        <if test="infectType != null and infectType != ''">
            AND dii.infected_type_name = #{infectType}
        </if>
        <if test="infectTransmitType != null and infectTransmitType != ''">
            AND dii.transmission_type_name = #{infectTransmitType}
        </if>
        <if test="infectCode != null and infectCode != ''">
            AND dii.id = #{infectCode}
        </if>
        <if test="filterCodes != null and filterCodes.size() > 0">
            AND dii.id NOT IN
            <foreach collection="filterCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            AND dii.infected_sub_name = #{diseaseName}
        </if>
        <include refid="regionChoose"/>
    </sql>

    <select id="findDistribution" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <choose>
            <when test="dateDimType == 'month'">
                <include refid="monthDimSelectColumns"/>
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterDimSelectColumns"/>
            </when>
            <when test="dateDimType == 'halfYear'">
                d.half_year_desc AS description
            </when>
            <otherwise>
                <include refid="yearDimSelectColumns"/>
            </otherwise>
        </choose>,
        <include refid="sumCnt"/>

        FROM ads.ads_ms_infect_process_case_info i
        <include refid="infectCaseInfoTableJoin"/>
        <include refid="joinDayDim"/>

        WHERE 1=1
        AND i.rep_card_flag = '0'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)

        <include refid="whereDistributionAreaMultiChoose"/>

        <choose>
            <when test="dateDimType == 'month'">
                <include refid="monthDimGroupOrder"/>
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterDimGroupOrder"/>
            </when>
            <when test="dateDimType == 'halfYear'">
                GROUP BY d.half_year_desc
                ORDER BY d.half_year_desc
            </when>
            <otherwise>
                <include refid="yearDimGroupOrder"/>
            </otherwise>
        </choose>
    </select>

    <select id="notifiableInfectTypeStat" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsInfectTypeStatVO">
        select
        coalesce(cur.notifiableDiseaseNewCnt, 0) notifiableDiseaseNewCnt,
        coalesce(lastData.notifiableDiseaseNewCnt, 0) notifiableDiseaseNewCntLast,
        coalesce(lastYData.notifiableDiseaseNewCnt, 0) notifiableDiseaseNewCntLastY,
        coalesce(cur.classAAndBNewCnt, 0) classAAndBNewCnt,
        coalesce(lastData.classAAndBNewCnt, 0) classAAndBNewCntLast,
        coalesce(lastYData.classAAndBNewCnt, 0) classAAndBNewCntLastY,
        coalesce(cur.classANewCnt, 0) classANewCnt,
        coalesce(lastData.classANewCnt, 0) classANewCntLast,
        coalesce(lastYData.classANewCnt, 0) classANewCntLastY,
        coalesce(cur.classBNewCnt, 0) classBNewCnt,
        coalesce(lastData.classBNewCnt, 0) classBNewCntLast,
        coalesce(lastYData.classBNewCnt, 0) classBNewCntLastY,
        coalesce(cur.classCNewCnt, 0) classCNewCnt,
        coalesce(lastData.classCNewCnt, 0) classCNewCntLast,
        coalesce(lastYData.classCNewCnt, 0) classCNewCntLastY,
        coalesce(cur.otherNewCnt, 0) otherNewCnt,
        coalesce(lastData.otherNewCnt, 0) otherNewCntLast,
        coalesce(lastYData.otherNewCnt, 0) otherNewCntLastY,

        coalesce(cur.notifiableDiseaseDeadCnt, 0) notifiableDiseaseDeadCnt,
        coalesce(lastData.notifiableDiseaseDeadCnt, 0) notifiableDiseaseDeadCntLast,
        coalesce(lastYData.notifiableDiseaseDeadCnt, 0) notifiableDiseaseDeadCntLastY,
        coalesce(cur.classAAndBDeadCnt, 0) classAAndBDeadCnt,
        coalesce(lastData.classAAndBDeadCnt, 0) classAAndBDeadCntLast,
        coalesce(lastYData.classAAndBDeadCnt, 0) classAAndBDeadCntLastY,
        coalesce(cur.classADeadCnt, 0) classADeadCnt,
        coalesce(lastData.classADeadCnt, 0) classADeadCntLast,
        coalesce(lastYData.classADeadCnt, 0) classADeadCntLastY,
        coalesce(cur.classBDeadCnt, 0) classBDeadCnt,
        coalesce(lastData.classBDeadCnt, 0) classBDeadCntLast,
        coalesce(lastYData.classBDeadCnt, 0) classBDeadCntLastY,
        coalesce(cur.classCDeadCnt, 0) classCDeadCnt,
        coalesce(lastData.classCDeadCnt, 0) classCDeadCntLast,
        coalesce(lastYData.classCDeadCnt, 0) classCDeadCntLastY,
        coalesce(cur.otherDeadCnt, 0) otherDeadCnt,
        coalesce(lastData.otherDeadCnt, 0) otherDeadCntLast,
        coalesce(lastYData.otherDeadCnt, 0) otherDeadCntLastY
        from
        (
        select
        <include refid="sumNotifiableCount" />
        from <include refid="infectProcessTable" />
        where
        rep_card_flag ='0' and
        dii.infect_class_name = #{infectClass}
        <if test="startDate != null and endDate != null">
            <include refid="timeTypeCriteria" />
        </if>
        <if test = "filterCodes != null and filterCodes.size() > 0">
            and fi_inf_infect_code not in
            <foreach collection="filterCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <include refid="regionChoose" />
        ) cur
        full join
        (
        select
        <include refid="sumNotifiableCount" />
        from <include refid="infectProcessTable" />
        where
        rep_card_flag ='0' and
        dii.infect_class_name = #{infectClass}
        <if test="lastStartDate != null and lastEndDate != null">
            <include refid="timeTypeLastCriteria" />
        </if>
        <if test = "filterCodes != null and filterCodes.size() > 0">
            and fi_inf_infect_code not in
            <foreach collection="filterCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <include refid="regionChoose" />
        ) lastData
        on 1=1
        full join
        (
        select
        <include refid="sumNotifiableCount" />
        from <include refid="infectProcessTable" />
        where
        rep_card_flag ='0' and
        dii.infect_class_name = #{infectClass}
        <if test="lastYStartDate != null and lastYEndDate != null">
            <include refid="timeTypeLastYCriteria" />
        </if>
        <if test = "filterCodes != null and filterCodes.size() > 0">
            and fi_inf_infect_code not in
            <foreach collection="filterCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <include refid="regionChoose" />
        ) lastYData
        on 1=1
    </select>


    <select id="groupInfectNewCnt" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsInfectNewCntVO">
        select
            infectCode,
            infectName,
            processNewCnt,
            processNewCntLastY,
            case
                when processNewCntLastY = 0
                then 1
                else round((CAST(processNewCnt AS DECIMAL(10,4)) - CAST(processNewCntLastY AS DECIMAL(10,4))) / CAST(processNewCntLastY AS DECIMAL(10,4)), 4)
            end processNewCntYearGrowth
        from
        (
        select
        coalesce(cur.infectCode, lastYData.infectCode) infectCode,
        coalesce(cur.infectName, lastYData.infectName) infectName,
        coalesce(cur.processNewCnt, 0) processNewCnt,
        coalesce(lastYData.processNewCnt, 0) processNewCntLastY
        from
        (
        select
        dii.id as infectCode,
        dii.infected_sub_name as infectName,
        COALESCE(COUNT(DISTINCT infect_process_id), 0) as processNewCnt
        from <include refid="infectProcessTable" />
        where
        rep_card_flag ='0'
        <if test="startDate != null and endDate != null">
            <include refid="timeTypeCriteria" />
        </if>
        <include refid="dimensionTableAndAreaCriteria" />
        group by dii.id, dii.infected_sub_name
        ) cur
        full join
        (
        select
        dii.id as infectCode,
        dii.infected_sub_name as infectName,
        COALESCE(COUNT(DISTINCT infect_process_id), 0) as processNewCnt
        from <include refid="infectProcessTable" />
        where
        rep_card_flag ='0'
        <if test="lastYStartDate != null and lastYEndDate != null">
            <include refid="timeTypeLastYCriteria" />
        </if>
        <include refid="dimensionTableAndAreaCriteria" />
        group by dii.id, dii.infected_sub_name
        ) lastYData
        on cur.infectCode = lastYData.infectCode
        ) tab
        where processNewCntLastY > 0
        order by processNewCntYearGrowth desc
    </select>

    <select id="groupInfectNewCntDecline" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsInfectNewCntVO">
        select
            infectCode,
            infectName,
            processNewCnt,
            processNewCntLastY,
            case
            when processNewCntLastY = 0
            then 1
            else round((CAST(processNewCnt AS DECIMAL(10,4)) - CAST(processNewCntLastY AS DECIMAL(10,4))) / CAST(processNewCntLastY AS DECIMAL(10,4)), 4)
            end as processNewCntYearGrowth
        from
        (
            select
            coalesce(cur.infectCode, lastYData.infectCode) infectCode,
            coalesce(cur.infectName, lastYData.infectName) infectName,
            coalesce(cur.processNewCnt, 0) processNewCnt,
            coalesce(lastYData.processNewCnt, 0) processNewCntLastY
            from
            (
            select
            dii.id as infectCode,
            dii.infected_sub_name as infectName,
            COALESCE(COUNT(DISTINCT infect_process_id), 0) as processNewCnt
            from <include refid="infectProcessTable" />
            where
            rep_card_flag ='0'
            <if test="startDate != null and endDate != null">
                <include refid="timeTypeCriteria" />
            </if>
            <include refid="dimensionTableAndAreaCriteria" />
            group by dii.id, dii.infected_sub_name
            ) cur
            full join
            (
            select
            dii.id as infectCode,
            dii.infected_sub_name as infectName,
            COALESCE(COUNT(DISTINCT infect_process_id), 0) as processNewCnt
            from <include refid="infectProcessTable" />
            where
            rep_card_flag ='0'
            <if test="lastYStartDate != null and lastYEndDate != null">
                <include refid="timeTypeLastYCriteria" />
            </if>
            <include refid="dimensionTableAndAreaCriteria" />
            group by dii.id, dii.infected_sub_name
            ) lastYData
            on cur.infectCode = lastYData.infectCode
            ) tab
            where processNewCntLastY > 0
            order by processNewCntYearGrowth asc
    </select>

    <sql id="leftJoinRegion">
        left <include refid="regionJoinCondition" />
        <include refid="regionChoose" />
    </sql>

    <select id="groupInfectCnt" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsInfectCntVO">
       select
            coalesce(cur.infectCode, lastD.infectCode, lastY.infectCode) as infectCode,
            coalesce(cur.infectName, lastD.infectName, lastY.infectName) as infectName,
            coalesce(cur.infectClass, lastD.infectClass, lastY.infectClass) as infectClass,
            coalesce(cur.infectType, lastD.infectType, lastY.infectType) as infectType,
            coalesce(cur.infectTransmitType, lastD.infectTransmitType, lastY.infectTransmitType) as infectTransmitType,
            coalesce(cur.levelType, lastD.levelType, lastY.levelType) as levelType,
            coalesce(cur.manageInfectId, lastD.manageInfectId, lastY.manageInfectId) as manageInfectId,
            coalesce(cur.orderFlag, lastD.orderFlag, lastY.orderFlag) as orderFlag,
            COALESCE(cur.processNewCnt, 0) as processNewCnt,
            COALESCE(lastD.processNewCnt, 0) as processNewCntLast,
            COALESCE(lastY.processNewCnt, 0) as processNewCntLastY,
            COALESCE(cur.processDeadCnt, 0) as processDeadCnt,
            COALESCE(lastD.processDeadCnt, 0) as processDeadCntLast,
            COALESCE(lastY.processDeadCnt, 0) as processDeadCntLastY,
            COALESCE(cur.processDeadIllnessCnt, 0) as processDeadIllnessCnt,
            COALESCE(lastD.processDeadIllnessCnt, 0) as processDeadIllnessCntLast,
            COALESCE(lastY.processDeadIllnessCnt, 0) as processDeadIllnessCntLastY,
            COALESCE(cur.processCureCnt, 0) as processCureCnt,
            COALESCE(lastD.processCureCnt, 0) as processCureCntLast,
            COALESCE(lastY.processCureCnt, 0) as processCureCntLastY,
            COALESCE(cur.processSevereCnt, 0) as processSevereCnt,
            COALESCE(lastD.processSevereCnt, 0) as processSevereCntLast,
            COALESCE(lastY.processSevereCnt, 0) as processSevereCntLastY,
            COALESCE(cur.processNowCnt, 0) as processNowCnt,
            COALESCE(lastD.processNowCnt, 0) as processNowCntLast,
            COALESCE(lastY.processNowCnt, 0) as processNowCntLastY
        from
        (
            select
            dii.id as infectCode,
            dii.infected_sub_name as infectName,
            dii.infect_class_name as infectClass,
            dii.infected_type_name as infectType,
            dii.transmit_type as infectTransmitType,
            dii.level_type as levelType,
            dii.infected_id as manageInfectId,
            dii.order_flag as orderFlag,
            <include refid="nowCntCurrent" />
            <include refid="infectCntTotalQuery" />
            from
            dim.dim_infected_info dii
            left join ads.ads_ms_infect_process_case_info i
            on dii.id = i.fi_inf_infect_code
            and i.rep_card_flag ='0'
            <if test="startDate != null and endDate != null">
                <include refid="timeTypeCriteria" />
            </if>
            <include refid="leftJoinRegion" />
            where
            <include refid="groupInfectCntWhere"/>
        )cur
        full join
        (
            select
            dii.id as infectCode,
            dii.infected_sub_name as infectName,
            dii.infect_class_name as infectClass,
            dii.infected_type_name as infectType,
            dii.transmit_type as infectTransmitType,
            dii.level_type as levelType,
            dii.infected_id as manageInfectId,
            dii.order_flag as orderFlag,
            <include refid="nowCntLast" />
            <include refid="infectCntTotalQuery" />
            from
            dim.dim_infected_info dii
            left join ads.ads_ms_infect_process_case_info i
            on dii.id = i.fi_inf_infect_code
            and i.rep_card_flag ='0'
            <if test="lastStartDate != null and lastEndDate != null">
                <include refid="timeTypeLastCriteria" />
            </if>
            <include refid="leftJoinRegion" />
            where
            <include refid="groupInfectCntWhere"/>
        ) lastD
        on cur.infectCode = lastD.infectCode
        full join
        (
            select
            dii.id as infectCode,
            dii.infected_sub_name as infectName,
            dii.infect_class_name as infectClass,
            dii.infected_type_name as infectType,
            dii.transmit_type as infectTransmitType,
            dii.level_type as levelType,
            dii.infected_id as manageInfectId,
            dii.order_flag as orderFlag,
            <include refid="nowCntLastY" />
            <include refid="infectCntTotalQuery" />
            from
            dim.dim_infected_info dii
            left join ads.ads_ms_infect_process_case_info i
            on dii.id = i.fi_inf_infect_code
            and i.rep_card_flag ='0'
            <if test="lastYStartDate != null and lastYEndDate != null">
                <include refid="timeTypeLastYCriteria" />
            </if>
            <include refid="leftJoinRegion" />
            where
            <include refid="groupInfectCntWhere"/>
        )lastY
        on COALESCE(cur.infectCode, lastD.infectCode) = lastY.infectCode
        order by
            case
            when cur.infectType = '甲类传染病' then 1
            when cur.infectType = '乙类传染病' then 2
            when cur.infectType = '丙类传染病' then 3
            when cur.infectType = '其他法定管理及重点监测传染病' then 4
            else 0
            end,
            cur.infectCode
    </select>

    <sql id="groupInfectCntWhere">
        dii.delete_flag ='0' and
        dii.infect_class_name = #{infectClass}
        <include refid="statSubtypeCriteria" />
        group by dii.id, dii.infected_sub_name,dii.infect_class_name, dii.infected_type_name, dii.transmit_type,dii.level_type,dii.infected_id
    </sql>

    <sql id="statSubtypeCriteria">
        <if test="infectCode != '' and infectCode != null">
            and dii.id = #{infectCode}
        </if>
        <if test="infectType != '' and infectType != null">
            and dii.infected_type_name = #{infectType}
        </if>
        <if test="infectCodeList != null and infectCodeList.size() > 0">
            and dii.id in
            <foreach collection="infectCodeList " item= "item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="infectTypes != null and infectTypes.size() > 0">
            and dii.infected_type_name in
            <foreach collection="infectTypes" item= "item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="infectClass != '' and infectClass != null">
            and dii.infect_class_name = #{infectClass}
        </if>
        <if test = "filterCodes != null and filterCodes.size() > 0">
            and dii.id not in
            <foreach collection="filterCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="infectTransmitType != '' and infectTransmitType != null">
            and dii.transmit_type = #{infectTransmitType}
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            and infected_sub_name = #{diseaseName}
        </if>
    </sql>

    <sql id="infectCntTotalQuery">
        COALESCE(COUNT(DISTINCT CASE WHEN drn.region_code is not null THEN infect_process_id END), 0) as processNewCnt,
        COALESCE(sum(case when lo_outcome_status = '死亡' and drn.region_code is not null then 1 else 0 end), 0) as processDeadCnt,
        COALESCE(sum(case when lo_dead_this_flag = '1' and drn.region_code is not null then 1 else 0 end), 0) as processDeadIllnessCnt,
        COALESCE(sum(case when (lo_outcome_status='治愈' or lo_outcome_status='治疗成功' or lo_outcome_status='好转') and drn.region_code is not null then 1 else 0 end), 0) as processCureCnt,
        COALESCE(sum(case when severe_flag = '1' and drn.region_code is not null then 1 else 0 end), 0) as processSevereCnt
    </sql>

    <select id="infectCntTotal" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsInfectCntVO">
        select
            COALESCE(cur.processNewCnt, 0) as processNewCnt,
            COALESCE(lastD.processNewCnt, 0) as processNewCntLast,
            COALESCE(lastY.processNewCnt, 0) as processNewCntLastY,
            COALESCE(cur.processDeadCnt, 0) as processDeadCnt,
            COALESCE(lastD.processDeadCnt, 0) as processDeadCntLast,
            COALESCE(lastY.processDeadCnt, 0) as processDeadCntLastY,
            COALESCE(cur.processDeadIllnessCnt, 0) as processDeadIllnessCnt,
            COALESCE(lastD.processDeadIllnessCnt, 0) as processDeadIllnessCntLast,
            COALESCE(lastY.processDeadIllnessCnt, 0) as processDeadIllnessCntLastY,
            COALESCE(cur.processCureCnt, 0) as processCureCnt,
            COALESCE(lastD.processCureCnt, 0) as processCureCntLast,
            COALESCE(lastY.processCureCnt, 0) as processCureCntLastY,
            COALESCE(cur.processSevereCnt, 0) as processSevereCnt,
            COALESCE(lastD.processSevereCnt, 0) as processSevereCntLast,
            COALESCE(lastY.processSevereCnt, 0) as processSevereCntLastY,
            COALESCE(cur.processNowCnt, 0) as processNowCnt,
            COALESCE(lastD.processNowCnt, 0) as processNowCntLast,
            COALESCE(lastY.processNowCnt, 0) as processNowCntLastY
        from
        (
            select
            <include refid="nowCntCurrent" />
            <include refid="infectCntTotalQuery" />
            from <include refid="infectProcessTable" />
            where
            rep_card_flag ='0'
            <if test="startDate != null and endDate != null">
                <include refid="timeTypeCriteria" />
            </if>
            <include refid="dimensionTableAndAreaCriteria" />
        ) cur
        full join
        (
            select
            <include refid="nowCntLast" />
            <include refid="infectCntTotalQuery" />
            from <include refid="infectProcessTable" />
            where
            rep_card_flag ='0'
            <if test="lastStartDate != null and lastEndDate != null">
                <include refid="timeTypeLastCriteria" />
            </if>
            <include refid="dimensionTableAndAreaCriteria" />
        ) lastD
        on 1=1
        full join
        (
            select
            <include refid="nowCntLastY" />
            <include refid="infectCntTotalQuery" />
            from <include refid="infectProcessTable" />
            where
            rep_card_flag ='0'
        <if test="lastYStartDate != null and lastYEndDate != null">
                <include refid="timeTypeLastYCriteria" />
            </if>
            <include refid="dimensionTableAndAreaCriteria" />
        ) lastY
        on 1=1
    </select>

    <sql id="nowCntCurrent">
        <choose>
            <when test="timeType == 'approveTime'">
                COALESCE(sum(CASE WHEN i.li_inf_check_day = #{startDate} and end_time is null and drn.region_code is not null then 1 else 0 end),0)+ COALESCE(COUNT(DISTINCT CASE WHEN drn.region_code is not null THEN infect_process_id END), 0) as processNowCnt,
            </when>
            <when test="timeType == 'onsetTime' or timeType == 'visitTime' or timeType == 'firVisitTime'">
                COALESCE(sum(CASE WHEN end_time is null and  coalesce(i.fi_inf_onset_day, i.fi_inf_visit_day) = #{startDate} and drn.region_code is not null  then 1 else 0 end),0)+ COALESCE(COUNT(DISTINCT CASE WHEN drn.region_code is not null THEN infect_process_id END), 0) as processNowCnt,
            </when>
            <when test="timeType == 'deathTime'">
                COALESCE(sum(CASE WHEN i.lo_dead_day = #{startDate} and end_time is null and drn.region_code is not null then 1 else 0 end),0)+ COALESCE(COUNT(DISTINCT CASE WHEN drn.region_code is not null THEN infect_process_id END), 0) as processNowCnt,
            </when>
            <otherwise>
                COALESCE(sum(CASE WHEN i.fi_inf_identify_day = #{startDate} and end_time is null and drn.region_code is not null then 1 else 0 end),0)+ COALESCE(COUNT(DISTINCT CASE WHEN drn.region_code is not null THEN infect_process_id END), 0) as processNowCnt,
            </otherwise>
        </choose>
    </sql>

    <sql id="nowCntLast">
        <choose>
            <when test="timeType == 'approveTime'">
                COALESCE(sum(CASE WHEN i.li_inf_check_day = #{lastStartDate} and end_time is null and drn.region_code is not null then 1 else 0 end),0)+ COALESCE(COUNT(DISTINCT CASE WHEN drn.region_code is not null THEN infect_process_id END), 0) as processNowCnt,
            </when>
            <when test="timeType == 'onsetTime' or timeType == 'visitTime' or timeType == 'firVisitTime'">
                COALESCE(sum(CASE WHEN  end_time is null and  coalesce(i.fi_inf_onset_day, i.fi_inf_visit_day) = #{lastStartDate} and drn.region_code is not null  then 1 else 0 end),0)+ COALESCE(COUNT(DISTINCT CASE WHEN drn.region_code is not null THEN infect_process_id END), 0) as processNowCnt,
            </when>
            <when test="timeType == 'deathTime'">
                COALESCE(sum(CASE WHEN i.lo_dead_day = #{lastStartDate} and end_time is null and drn.region_code is not null then 1 else 0 end),0)+ COALESCE(COUNT(DISTINCT CASE WHEN drn.region_code is not null THEN infect_process_id END), 0) as processNowCnt,
            </when>
            <otherwise>
                COALESCE(sum(CASE WHEN i.fi_inf_identify_day = #{lastStartDate} and end_time is null and drn.region_code is not null then 1 else 0 end),0)+ COALESCE(COUNT(DISTINCT CASE WHEN drn.region_code is not null THEN infect_process_id END), 0) as processNowCnt,
            </otherwise>
        </choose>
    </sql>

    <sql id="nowCntLastY">
        <choose>
            <when test="timeType == 'approveTime'">
                COALESCE(sum(CASE WHEN i.li_inf_check_day = #{lastYStartDate} and end_time is null and drn.region_code is not null then 1 else 0 end),0)+ COALESCE(COUNT(DISTINCT CASE WHEN drn.region_code is not null THEN infect_process_id END), 0) as processNowCnt,
            </when>
            <when test="timeType == 'onsetTime' or timeType == 'visitTime' or timeType == 'firVisitTime'">
                COALESCE(sum(CASE WHEN end_time is null and  coalesce(i.fi_inf_onset_day, i.fi_inf_visit_day) = #{lastYStartDate} and drn.region_code is not null  then 1 else 0 end),0)+ COALESCE(COUNT(DISTINCT CASE WHEN drn.region_code is not null THEN infect_process_id END), 0) as processNowCnt,
            </when>
            <when test="timeType == 'deathTime'">
                COALESCE(sum(CASE WHEN i.lo_dead_day = #{lastYStartDate} and end_time is null and drn.region_code is not null then 1 else 0 end),0)+ COALESCE(COUNT(DISTINCT CASE WHEN drn.region_code is not null THEN infect_process_id END), 0) as processNowCnt,
            </when>
            <otherwise>
                COALESCE(sum(CASE WHEN i.fi_inf_identify_day = #{lastYStartDate} and end_time is null and drn.region_code is not null then 1 else 0 end),0)+ COALESCE(COUNT(DISTINCT CASE WHEN drn.region_code is not null THEN infect_process_id END), 0) as processNowCnt,
            </otherwise>
        </choose>
    </sql>


    <select id="groupInfectTransmitTypeCnt" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsInfectTransmitTypeCntVO">
        select
            COALESCE(cur.transmitType, lastD.transmitType, lastY.transmitType) as transmitType,
            COALESCE(cur.processNewCnt, 0) as processNewCnt,
            COALESCE(lastD.processNewCnt, 0) as processNewCntLast,
            COALESCE(lastY.processNewCnt, 0) as processNewCntLastY,
            COALESCE(cur.processDeadCnt, 0) as processDeadCnt,
            COALESCE(lastD.processDeadCnt, 0) as processDeadCntLast,
            COALESCE(lastY.processDeadCnt, 0) as processDeadCntLastY
        from
        (
        select
        dii.transmit_type as transmitType,
        COALESCE(COUNT(DISTINCT infect_process_id), 0) as processNewCnt,
        COALESCE(sum(case when lo_outcome_status = '死亡' then 1 else 0 end), 0) as processDeadCnt
        from  <include refid="infectProcessTable"/>
        where
        rep_card_flag ='0'
        <if test="startDate != null and endDate != null">
            <include refid="timeTypeCriteria" />
        </if>
        <include refid="dimensionTableAndAreaCriteria" />
        group by transmitType
        ) cur
        full join
        (
        select
        dii.transmit_type as transmitType,
        COALESCE(COUNT(DISTINCT infect_process_id), 0) as processNewCnt,
        COALESCE(sum(case when lo_outcome_status = '死亡' then 1 else 0 end), 0) as processDeadCnt
        from  <include refid="infectProcessTable"/>
        where
        rep_card_flag ='0'
        <if test="lastStartDate != null and lastEndDate != null">
            <include refid="timeTypeLastCriteria" />
        </if>
        <include refid="dimensionTableAndAreaCriteria" />
        group by transmitType
        ) lastD
        on cur.transmitType = lastD.transmitType
        full join
        (
        select
        dii.transmit_type as transmitType,
        COALESCE(COUNT(DISTINCT infect_process_id), 0) as processNewCnt,
        COALESCE(sum(case when lo_outcome_status = '死亡' then 1 else 0 end), 0) as processDeadCnt
        from  <include refid="infectProcessTable"/>
        where
        rep_card_flag ='0'
        <if test="lastYStartDate != null and lastYEndDate != null">
            <include refid="timeTypeLastYCriteria" />
        </if>
        <include refid="dimensionTableAndAreaCriteria" />
        group by transmitType
        ) lastY
        on COALESCE(cur.transmitType, lastD.transmitType) = lastY.transmitType
    </select>

    <sql id="sumCnt">
        count(distinct i.infect_process_id) as processNewCnt ,
        sum(case when i.lo_outcome_status = '死亡' then 1 else 0 end) as processDeadCnt,
        sum(case when i.end_time is null then 1 else 0 end) as processNowCnt,
        sum(case when i.lo_dead_this_flag = '1' then 1 else 0 end) as processDeadIllnessCnt,
        sum(case when i.lo_outcome_status='治愈' or lo_outcome_status='治疗成功' or lo_outcome_status='好转' then 1 else 0 end) as processCureCnt
    </sql>

    <sql id="selectGroupArea">
        <if test="areaLevel == 1">
            drn.city_code  areaCode,
            drn.city_name  areaName,
            drn.city_name  description,
                2 as areaLevel,
        </if>
        <if test="areaLevel == 2">
            drn.district_code  areaCode,
            drn.district_name  areaName,
            drn.district_name  description,
                3 as areaLevel,
        </if>
        <if test="areaLevel == 3">
            drn.street_code  areaCode,
            drn.street_name  areaName,
            drn.street_name  description,
                4 as areaLevel,
        </if>
    </sql>

    <sql id="whereGroupArea">
        <if test="areaLevel == 1">
            drn.city_code,
                drn.city_name
        </if>
        <if test="areaLevel == 2">
            drn.district_code,
                drn.district_name
        </if>
        <if test="areaLevel == 3">
            drn.street_code,
                drn.street_name
        </if>
    </sql>

    <select id="groupArea" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="selectGroupArea"/>
        <include refid="sumCnt"/>
        FROM
        ads.ads_ms_infect_process_case_info i
        <include refid="infectCaseInfoTableJoin"/>
        WHERE 1=1
        AND rep_card_flag ='0'
        AND <include refid="dynamicDayField"/> BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>

        GROUP BY
        <include refid="whereGroupArea"/>
    </select>

    <select id="groupAddrType" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <choose>
            <when test="addressType == 'orgAddress'">
                fi_inf_org_addr_type AS description
            </when>
            <when test="addressType == 'livingAddress'">
                fi_inf_living_addr_type AS description
            </when>
            <otherwise>
                NULL AS description
            </otherwise>
        </choose>,
        <include refid="sumCnt"/>
        FROM
        ads.ads_ms_infect_process_case_info
        WHERE 1=1
        AND rep_card_flag ='0'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>

        GROUP BY
        <choose>
            <when test="addressType == 'orgAddress'">
                fi_inf_org_addr_type
            </when>
            <when test="addressType == 'livingAddress'">
                fi_inf_living_addr_type
            </when>
        </choose>
    </select>


    <select id="groupAge" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <include refid="selectAgeGroup"/>,
        <include refid="sumCnt"/>,
        value_sort as order
        FROM
        <include refid="fromWithAgeGroup"/>
        <include refid="infectCaseInfoTableJoin"/>
        WHERE 1=1
        AND i.rep_card_flag ='0'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        GROUP BY
        CASE
        WHEN ag.value_code IS NOT NULL THEN ag.value_name
        ELSE '不详'
        END,
        value_sort
        ORDER BY
        value_sort
    </select>

    <select id="groupSex" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        patient_sex_name,
        patient_sex_name AS description,
        <include refid="sumCnt"/>
        FROM
        ads.ads_ms_infect_process_case_info i
        <include refid="infectCaseInfoTableJoin"/>
         WHERE 1=1
        AND rep_card_flag = '0'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by patient_sex_name  order by patient_sex_name
    </select>

    <select id="groupJob" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        person_type as patientJob,
        person_type as description,
        <include refid="sumCnt"/>
        from
        ads.ads_ms_infect_process_case_info i
        <include refid="infectCaseInfoTableJoin"/>
        WHERE 1=1
        AND rep_card_flag ='0'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose" />
        group by person_type order by processNewCnt desc
    </select>

    <select id="listNewCntPre" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
            CAST(day AS DATE)  as statDate,
            sum(process_new_cnt_pre)  as processNewCnt
        from
        <include refid="dayDb" />
        where day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="statCriteria" />
        group by day
        order by day
    </select>

    <select id="overAll" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <choose>
            <when test="dateDimType == 'day'">
                <include refid="dynamicDayField"/> AS statDate
            </when>
            <when test="dateDimType == 'week'">
                <include refid="weekDimSelectColumns"/>
            </when>
            <when test="dateDimType == 'meadow'">
                <include refid="tenDaysDimSelectColumns"/>
            </when>
            <when test="dateDimType == 'month'">
                <include refid="monthDimSelectColumns"/>
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterDimSelectColumns"/>
            </when>
            <when test="dateDimType == 'halfYear'">
                <include refid="monthDimSelectColumns"/>
            </when>
            <otherwise>
                <include refid="yearDimSelectColumns"/>
            </otherwise>
        </choose>,
        <include refid="sumCnt"/>

        FROM ads.ads_ms_infect_process_case_info i
        <include refid="joinDayDim"/>

        <include refid="infectCaseInfoTableJoin"/>

        WHERE 1=1
        AND i.rep_card_flag = '0'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)

        <include refid="whereDistributionAreaMultiChoose"/>

        <choose>
            <when test="dateDimType == 'day'">
                GROUP BY <include refid="dynamicDayField"/>
                ORDER BY <include refid="dynamicDayField"/>
            </when>
            <when test="dateDimType == 'week'">
                <include refid="weekDimGroupOrder"/>
            </when>
            <when test="dateDimType == 'meadow'">
                <include refid="tenDaysDimGroupOrder"/>
            </when>
            <when test="dateDimType == 'month'">
                <include refid="monthDimGroupOrder"/>
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterDimGroupOrder"/>
            </when>
            <when test="dateDimType == 'halfYear'">
                <include refid="monthDimGroupOrder"/>
            </when>
            <otherwise>
                <include refid="yearDimGroupOrder"/>
            </otherwise>
        </choose>
    </select>

    <select id="areaChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <choose>
            <when test="dateDimType == 'day'">
                <include refid="dynamicDayField"/> AS statDate,
            </when>
            <when test="dateDimType == 'week'">
                <include refid="weekDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'meadow'">
                <include refid="tenDaysDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'month'">
                <include refid="monthDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'halfYear'">
                <include refid="monthDimSelectColumns"/>,
            </when>
            <otherwise>
                <include refid="yearDimSelectColumns"/>,
            </otherwise>
        </choose>

        <include refid="selectGroupArea"/>
        <include refid="sumCnt"/>

        FROM ads.ads_ms_infect_process_case_info i
        <include refid="joinDayDim"/>
        <include refid="infectCaseInfoTableJoin"/>

        WHERE 1=1
        AND i.rep_card_flag = '0'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>

        <choose>
            <when test="dateDimType == 'day'">
                GROUP BY <include refid="dynamicDayField"/>,
                <include refid="whereGroupArea"/>
                ORDER BY <include refid="dynamicDayField"/>
                <if test="'${whereGroupArea}' != null and '${whereGroupArea}' != ''">
                    , areaCode
                </if>
                , processNewCnt DESC
            </when>

            <when test="dateDimType == 'week'">
                GROUP BY <include refid="weekCommonGroupBy"/>,
                <include refid="whereGroupArea"/>
                ORDER BY year, week, processNewCnt DESC
            </when>

            <when test="dateDimType == 'meadow'">
                GROUP BY <include refid="tenDayCommonGroupBy"/>,
                <include refid="whereGroupArea"/>
                ORDER BY year, month, tenDay, processNewCnt DESC
            </when>

            <when test="dateDimType == 'month'">
                GROUP BY <include refid="monthCommonGroupBy"/>,
                <include refid="whereGroupArea"/>
                ORDER BY year, month, processNewCnt DESC
            </when>

            <when test="dateDimType == 'quarter'">
                GROUP BY <include refid="quarterCommonGroupBy"/>,
                <include refid="whereGroupArea"/>
                ORDER BY year, quarter, processNewCnt DESC
            </when>

            <when test="dateDimType == 'halfYear'">
                GROUP BY <include refid="monthCommonGroupBy"/>,
                <include refid="whereGroupArea"/>
                ORDER BY year, month, processNewCnt DESC
            </when>

            <otherwise>
                GROUP BY year
                <if test="'${whereGroupArea}' != null and '${whereGroupArea}' != ''">
                    , <include refid="whereGroupArea"/>
                </if>
                ORDER BY year, processNewCnt DESC
            </otherwise>
        </choose>
    </select>


    <select id="sexChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <choose>
            <when test="dateDimType == 'day'">
                <include refid="dynamicDayField"/> AS statDate,
            </when>
            <when test="dateDimType == 'week'">
                <include refid="weekDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'meadow'">
                <include refid="tenDaysDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'month'">
                <include refid="monthDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'halfYear'">
                <include refid="monthDimSelectColumns"/>
            </when>
            <otherwise>
                <include refid="yearDimSelectColumns"/>,
            </otherwise>
        </choose>

        patient_sex_name,
        patient_sex_name AS description,

        <include refid="sumCnt"/>

        FROM ads.ads_ms_infect_process_case_info i
        <include refid="joinDayDim"/>
        <include refid="infectCaseInfoTableJoin"/>

        WHERE 1=1
        AND i.rep_card_flag = '0'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>

        <choose>
            <when test="dateDimType == 'day'">
                GROUP BY <include refid="dynamicDayField"/>, patient_sex_name
            </when>
            <when test="dateDimType == 'week'">
                GROUP BY <include refid="weekCommonGroupBy"/>, patient_sex_name
            </when>
            <when test="dateDimType == 'meadow'">
                GROUP BY <include refid="tenDayCommonGroupBy"/>, patient_sex_name
            </when>
            <when test="dateDimType == 'month'">
                GROUP BY <include refid="monthCommonGroupBy"/>, patient_sex_name
            </when>
            <when test="dateDimType == 'quarter'">
                GROUP BY <include refid="quarterCommonGroupBy"/>, patient_sex_name
            </when>
            <when test="dateDimType == 'halfYear'">
                GROUP BY <include refid="monthCommonGroupBy"/>, patient_sex_name
            </when>
            <otherwise>
                GROUP BY year, patient_sex_name
            </otherwise>
        </choose>

        <choose>
            <when test="dateDimType == 'day'">
                ORDER BY statDate, processNewCnt DESC
            </when>
            <when test="dateDimType == 'week'">
                ORDER BY year, week, processNewCnt DESC
            </when>
            <when test="dateDimType == 'meadow'">
                ORDER BY year, month, tenDay, processNewCnt DESC
            </when>
            <when test="dateDimType == 'month'">
                ORDER BY year, month, processNewCnt DESC
            </when>
            <when test="dateDimType == 'quarter'">
                ORDER BY year, quarter, processNewCnt DESC
            </when>
            <when test="dateDimType == 'halfYear'">
                ORDER BY year, month, processNewCnt DESC
            </when>
            <otherwise>
                ORDER BY year, processNewCnt DESC
            </otherwise>
        </choose>
    </select>

    <select id="ageChange" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        SELECT
        <choose>
            <when test="dateDimType == 'day'">
                <include refid="dynamicDayField"/> AS statDate,
            </when>
            <when test="dateDimType == 'week'">
                <include refid="weekDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'meadow'">
                <include refid="tenDaysDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'month'">
                <include refid="monthDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'quarter'">
                <include refid="quarterDimSelectColumns"/>,
            </when>
            <when test="dateDimType == 'halfYear'">
                <include refid="monthDimSelectColumns"/>
            </when>
            <otherwise>
                <include refid="yearDimSelectColumns"/>,
            </otherwise>
        </choose>

        <include refid="selectAgeGroup"/>,
        ag.value_down AS ageOrder,

        <include refid="sumCnt"/>

        FROM
        <include refid="fromWithAgeGroup"/>
        <include refid="joinDayDim"/>

        <include refid="infectCaseInfoTableJoin"/>

        WHERE 1=1
        AND i.rep_card_flag = '0'
        AND <include refid="dynamicDayField"/>
        BETWEEN CAST(#{startDate} AS DATE)
        AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>

        <choose>
            <when test="dateDimType == 'day'">
                GROUP BY <include refid="dynamicDayField"/>,
                ag.value_code, ag.value_name, ag.value_down
            </when>
            <when test="dateDimType == 'week'">
                GROUP BY <include refid="weekCommonGroupBy"/>,
                ag.value_code, ag.value_name, ag.value_down
            </when>
            <when test="dateDimType == 'meadow'">
                GROUP BY <include refid="tenDayCommonGroupBy"/>,
                ag.value_code, ag.value_name, ag.value_down
            </when>
            <when test="dateDimType == 'month'">
                GROUP BY <include refid="monthCommonGroupBy"/>,
                ag.value_code, ag.value_name, ag.value_down
            </when>
            <when test="dateDimType == 'quarter'">
                GROUP BY <include refid="quarterCommonGroupBy"/>,
                ag.value_code, ag.value_name, ag.value_down
            </when>
            <when test="dateDimType == 'halfYear'">
                GROUP BY <include refid="monthCommonGroupBy"/>,
                ag.value_code, ag.value_name, ag.value_down
            </when>
            <otherwise>
                GROUP BY year,
                ag.value_code, ag.value_name, ag.value_down
            </otherwise>
        </choose>

        <choose>
            <when test="dateDimType == 'day'">
                ORDER BY statDate, ageOrder, processNewCnt DESC
            </when>
            <when test="dateDimType == 'week'">
                ORDER BY year, week, ageOrder, processNewCnt DESC
            </when>
            <when test="dateDimType == 'meadow'">
                ORDER BY year, month, tenDay, ageOrder, processNewCnt DESC
            </when>
            <when test="dateDimType == 'month'">
                ORDER BY year, month, ageOrder, processNewCnt DESC
            </when>
            <when test="dateDimType == 'quarter'">
                ORDER BY year, quarter, ageOrder, processNewCnt DESC
            </when>
            <when test="dateDimType == 'halfYear'">
                ORDER BY year, month, ageOrder, processNewCnt DESC
            </when>
            <otherwise>
                ORDER BY year, ageOrder, processNewCnt DESC
            </otherwise>
        </choose>
    </select>

    <select id="groupIdentifyClass" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        i.li_inf_infect_class as description,
        COALESCE(COUNT(DISTINCT i.infect_process_id), 0) as processNewCnt
        from
        ads.ads_ms_infect_process_case_info i
        <include refid="infectCaseInfoTableJoin"/>
        where
        i.rep_card_flag ='0'
        <if test="startDate != null and endDate != null">
            <include refid="timeTypeCriteria" />
        </if>
        and i.li_inf_infect_class is not null  and i.li_inf_infect_class <![CDATA[<>]]> ''
        <include refid="dimensionTableAndAreaCriteria" />
        group by i.li_inf_infect_class order by i.li_inf_infect_class
    </select>

    <select id="groupOutcomeStatus" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        i.lo_outcome_status as description,
        COALESCE(COUNT(DISTINCT i.infect_process_id), 0) as processNewCnt
        from
        ads.ads_ms_infect_process_case_info i
        <include refid="infectCaseInfoTableJoin"/>
        where
        i.rep_card_flag ='0'
        <if test="startDate != null and endDate != null">
            <include refid="timeTypeCriteria" />
        </if>
        and i.lo_outcome_status is not null and i.lo_outcome_status <![CDATA[<>]]> ''
        <include refid="dimensionTableAndAreaCriteria" />
        group by i.lo_outcome_status order by i.lo_outcome_status
    </select>

    <select id="groupSymptomFirst" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        i.fi_syn_symptom_first as description,
        COALESCE(COUNT(DISTINCT i.infect_process_id), 0) as processNewCnt
        from
        ads.ads_ms_infect_process_case_info i
        <include refid="infectCaseInfoTableJoin"/>
        where
        i.rep_card_flag ='0'
        <if test="startDate != null and endDate != null">
            <include refid="timeTypeCriteria" />
        </if>
        and i.fi_syn_symptom_first is not null and i.fi_syn_symptom_first <![CDATA[<>]]> ''
        <include refid="dimensionTableAndAreaCriteria" />
        group by i.fi_syn_symptom_first order by processNewCnt desc
    </select>

    <select id="groupHistoryBefore" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        i.disease_before as description,
        COALESCE(COUNT(DISTINCT i.infect_process_id), 0) as processNewCnt
        from
        ads.ads_ms_infect_process_case_info i
        <include refid="infectCaseInfoTableJoin"/>
        where
        i.rep_card_flag ='0'
        <if test="startDate != null and endDate != null">
            <include refid="timeTypeCriteria" />
        </if>
        and i.disease_before is not null and i.disease_before <![CDATA[<>]]> ''
        <include refid="dimensionTableAndAreaCriteria" />
        group by i.disease_before order by processNewCnt desc
    </select>

    <select id="groupPathogenResNominal" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        i.pathogen_res_class as description,
        COALESCE(COUNT(DISTINCT i.infect_process_id), 0) as processNewCnt
        from
        ads.ads_ms_infect_process_case_info i
        <include refid="infectCaseInfoTableJoin"/>
        where
        i.rep_card_flag ='0'
        <if test="startDate != null and endDate != null">
            <include refid="timeTypeCriteria" />
        </if>
        and i.pathogen_res_class is not null and i.pathogen_res_class <![CDATA[<>]]> ''
        <include refid="whereDistributionAreaMultiChoose" />
        group by i.pathogen_res_class order by pathogen_res_class
    </select>

    <select id="groupDiseaseName" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        infect_code as descriptionCode,
        infect_name as description,
        <include refid="sumCnt"/>
        from
        <include refid="dayDb"/>
        where 1=1 and day between CAST(#{startDate} AS DATE) and CAST(#{endDate} AS DATE)
        <include refid="whereDistribution" />
        group by infect_code, infect_name order by infect_code, infect_name
    </select>

    <select id="groupAreaCurrentLevel" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        <include refid="selectGroupAreaCurrentLevel"/>
        <include refid="sumCnt"/>
        from
        ads.ads_ms_infect_process_case_info i
        <include refid="infectCaseInfoTableJoin"/>
        where 1=1
        and i.rep_card_flag ='0'
        AND <include refid="dynamicDayField"/> BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose" />
        group by
        <include refid="whereGroupAreaCurrentLevel"/>
    </select>

    <sql id="selectGroupAreaCurrentLevel">
        <if test="areaLevel == 1">
            drn.province_code  areaCode,
            drn.province_name  areaName,
            drn.province_name  description,
            1 as areaLevel,
        </if>
        <if test="areaLevel == 2">
            drn.city_code  areaCode,
            drn.city_name  areaName,
            drn.city_name  description,
            2 as areaLevel,
        </if>
        <if test="areaLevel == 3">
            drn.district_code  areaCode,
            drn.district_name  areaName,
            drn.district_name  description,
            3 as areaLevel,
        </if>
    </sql>

    <sql id="whereGroupAreaCurrentLevel">
        <if test="areaLevel == 1">
            drn.province_code,
            drn.province_name
        </if>
        <if test="areaLevel == 2">
            drn.city_code,
            drn.city_name
        </if>
        <if test="areaLevel == 3">
            drn.district_code,
            drn.district_name
        </if>
    </sql>

    <select id="groupAreaDripDetail" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        (
        <if test="areaLevel &lt;= 1">
            <include refid="provinceQuery"/>
            union all
        </if>
        <if test="areaLevel &lt;= 2">
            <include refid="cityQuery"/>
            union all
        </if>
        <if test="areaLevel &lt;= 3">
            <include refid="districtQuery"/>
            union all
        </if>
        <if test="areaLevel &lt;= 4">
            <include refid="streetQuery"/>
        </if>
        )
        ORDER BY
        provinceCode DESC,
        cityCode DESC,
        districtCode DESC,
        streetCode DESC,
        areaLevel
    </select>
    <!-- Org Address - Province Level -->
    <sql id="provinceQuery">
        select
        drn.province_code as areaCode,
        drn.province_name as areaName,
        drn.province_name as description,
        drn.province_code as provinceCode,
        drn.province_name as provinceName,
        null as cityCode,
        null as cityName,
        null as districtCode,
        null as districtName,
        null as streetCode,
        null as streetName,
        1 as areaLevel,
        <include refid="sumCnt"/>
        from
        ads.ads_ms_infect_process_case_info i
        <include refid="regionJoinCondition"/>
        where 1=1
        AND rep_card_flag ='0'
        AND <include refid="dynamicDayField"/> BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by drn.province_code, drn.province_name
    </sql>

    <!-- Org Address - City Level -->
    <sql id="cityQuery">
        select
        drn.city_code as areaCode,
        drn.city_name as areaName,
        drn.city_name as description,
        drn.province_code as provinceCode,
        drn.province_name as provinceName,
        drn.city_code as cityCode,
        drn.city_name as cityName,
        null as districtCode,
        null as districtName,
        null as streetCode,
        null as streetName,
        2 as areaLevel,
        <include refid="sumCnt"/>
        from
        ads.ads_ms_infect_process_case_info i
        <include refid="regionJoinCondition"/>
        where 1=1
        AND rep_card_flag ='0'
        AND <include refid="dynamicDayField"/> BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by drn.province_code, drn.province_name, drn.city_code, drn.city_name
    </sql>

    <!-- Org Address - District Level -->
    <sql id="districtQuery">
        select
        drn.district_code as areaCode,
        drn.district_name as areaName,
        drn.district_name as description,
        drn.province_code as provinceCode,
        drn.province_name as provinceName,
        drn.city_code as cityCode,
        drn.city_name as cityName,
        drn.district_code as districtCode,
        drn.district_name as districtName,
        null as streetCode,
        null as streetName,
        3 as areaLevel,
        <include refid="sumCnt"/>
        from
        ads.ads_ms_infect_process_case_info i
        <include refid="regionJoinCondition"/>
        where 1=1
        AND rep_card_flag ='0'
        AND <include refid="dynamicDayField"/> BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by
        drn.province_code, drn.province_name, drn.city_code, drn.city_name, drn.district_code, drn.district_name
    </sql>

    <!-- Org Address - Street Level -->
    <sql id="streetQuery">
        select
        drn.street_code as areaCode,
        drn.street_name as areaName,
        drn.street_name as description,
        drn.province_code as provinceCode,
        drn.province_name as provinceName,
        drn.city_code as cityCode,
        drn.city_name as cityName,
        drn.district_code as districtCode,
        drn.district_name as districtName,
        drn.street_code as streetCode,
        drn.street_name as streetName,
        4 as areaLevel,
        <include refid="sumCnt"/>
        from
        ads.ads_ms_infect_process_case_info i
        <include refid="regionJoinCondition"/>
        where 1=1
        AND rep_card_flag ='0'
        AND <include refid="dynamicDayField"/> BETWEEN CAST(#{startDate} AS DATE) AND CAST(#{endDate} AS DATE)
        <include refid="whereDistributionAreaMultiChoose"/>
        group by drn.province_code, drn.province_name,
        drn.city_code, drn.city_name,
        drn.district_code, drn.district_name,
        drn.street_code, drn.street_name
    </sql>

    <select id="groupInfectSexCnt" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsInfectCntVO">
        select
            infect_code as infectCode,
            infect_name as infectName,
            infect_class as infectClass,
            infect_type as infectType,
            patient_sex_name,
            infect_transmit_type,
            sum(process_new_cnt) as processNewCnt,
            sum(process_new_cnt_last) as processNewCntLast,
            sum(process_new_cnt_last_y) as processNewCntLastY,
            sum(process_dead_cnt) as processDeadCnt,
            sum(process_dead_cnt_last) as processDeadCntLast,
            sum(process_dead_cnt_last_y) as processDeadCntLastY,
            sum(process_now_cnt) as processNowCnt,
            sum(process_now_cnt_last) as processNowCntLast,
            sum(process_now_cnt_last_y) as processNowCntLastY,
            sum(process_dead_illness_cnt) as processDeadIllnessCnt,
            sum(process_dead_illness_cnt_last) as processDeadIllnessCntLast,
            sum(process_dead_illness_cnt_last_y) as processDeadIllnessCntLastY,
            sum(process_cure_cnt) as processCureCnt,
            sum(process_cure_cnt_last) as processCureCntLast,
            sum(process_cure_cnt_last_y) as processCureCntLastY,
            sum(process_severe_cnt) as processSevereCnt,
            sum(process_severe_cnt_last) as processSevereCntLast,
            sum(process_severe_cnt_last_y) as processSevereCntLastY
        from
            <include refid="dayDb"/>
            where
            infect_class = #{infectClass}
            <if test="startDate != null and endDate != null">
                and day between #{startDate} and #{endDate}
            </if>
            <include refid="statCriteria" />
        group by infect_code, infect_name,infect_class, infect_type, patient_sex_name, infect_transmit_type
        order by
            case
                when infect_type = '甲类传染病' then 1
                when infect_type = '乙类传染病' then 2
                when infect_type = '丙类传染病' then 3
                when infect_type = '其他法定管理及重点监测传染病' then 4
                else 0
            end, infect_code
    </select>

    <select id="groupInfectAgeCnt" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsInfectCntVO">
        select
            infect_code as infectCode,
            infect_name as infectName,
            infect_class as infectClass,
            infect_type as infectType,
            patient_age_group,
            infect_transmit_type,
            sum(process_new_cnt) as processNewCnt,
            sum(process_new_cnt_last) as processNewCntLast,
            sum(process_new_cnt_last_y) as processNewCntLastY,
            sum(process_dead_cnt) as processDeadCnt,
            sum(process_dead_cnt_last) as processDeadCntLast,
            sum(process_dead_cnt_last_y) as processDeadCntLastY,
            sum(process_now_cnt) as processNowCnt,
            sum(process_now_cnt_last) as processNowCntLast,
            sum(process_now_cnt_last_y) as processNowCntLastY,
            sum(process_dead_illness_cnt) as processDeadIllnessCnt,
            sum(process_dead_illness_cnt_last) as processDeadIllnessCntLast,
            sum(process_dead_illness_cnt_last_y) as processDeadIllnessCntLastY,
            sum(process_cure_cnt) as processCureCnt,
            sum(process_cure_cnt_last) as processCureCntLast,
            sum(process_cure_cnt_last_y) as processCureCntLastY,
            sum(process_severe_cnt) as processSevereCnt,
            sum(process_severe_cnt_last) as processSevereCntLast,
            sum(process_severe_cnt_last_y) as processSevereCntLastY
        from
            <include refid="dayDb"/>
            where
            infect_class = #{infectClass}
            <if test="startDate != null and endDate != null">
                and day between #{startDate} and #{endDate}
            </if>
            <include refid="statCriteria" />
        group by infect_code, infect_name,infect_class, infect_type, patient_age_group, infect_transmit_type
        order by
            case
                when infect_type = '甲类传染病' then 1
                when infect_type = '乙类传染病' then 2
                when infect_type = '丙类传染病' then 3
                when infect_type = '其他法定管理及重点监测传染病' then 4
                else 0
            end, infect_code
    </select>


    <select id="groupInfectDiseaseCnt" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsInfectCntVO">
        select
        dii.id as infectCode,
        dii.infected_sub_name as infectName,
        dii.infect_class_name as infectClass,
        dii.infected_type_name as infectType,

        COALESCE(COUNT(DISTINCT infect_process_id), 0) as processNewCnt,
        COALESCE(sum(case when lo_dead_this_flag = '1' then 1 else 0 end), 0) as processDeadIllnessCnt
        from <include refid="infectProcessTable" />
        where rep_card_flag ='0'
        <if test="startDate != null and endDate != null">
            <include refid="timeTypeCriteria" />
        </if>
        <include refid="dimensionTableAndAreaCriteria" />
        group by infectCode, infectName,infectClass, infectType
    </select>


    <!-- 统计指标 -->
    <sql id="statFlagChoose">
        <choose>
            <when test="statFlag == 'newCnt' or statFlag == 'newRate'">
                coalesce(sum(process_new_cnt), 0) as processNewCnt
            </when>
            <when test="statFlag == 'deathRate'">
                coalesce(sum(process_dead_illness_cnt), 0) as processDeadIllnessCnt
            </when>
        </choose>
    </sql>

    <!-- 排序字段 -->
    <sql id="orderChoose">
        <choose>
            <when test="statFlag == 'newCnt' or statFlag == 'newRate'">
                processNewCnt asc
            </when>
            <when test="statFlag == 'deathRate'">
                processDeadIllnessCnt asc
            </when>
        </choose>
    </sql>

    <sql id="whereGroupAreaSupLevel">
        <if test="'orgAddress' == addressType ">
            <if test="areaLevel == 1">
                org_addr_city_code,
                org_addr_city
            </if>
            <if test="areaLevel == 2">
                org_addr_func_district_code,
                org_addr_func_district
            </if>
            <if test="areaLevel == 3">
                org_addr_street_code,
                org_addr_street
            </if>
        </if>

        <if test="'livingAddress' == addressType ">
            <if test="areaLevel == 1">
                living_addr_city_code  ,
                living_addr_city
            </if>
            <if test="areaLevel == 2">
                living_addr_func_district_code  ,
                living_addr_func_district
            </if>
            <if test="areaLevel == 3">
                living_addr_street_code,
                living_addr_street
            </if>
        </if>
    </sql>

    <sql id="selectAreaSupLevel">
        <if test="'orgAddress' == addressType ">
            <if test="areaLevel == 1">
                org_addr_city_code areaCode,
                org_addr_city areaName,
                org_addr_city description,
            </if>
            <if test="areaLevel == 2">
                org_addr_func_district_code areaCode,
                org_addr_func_district areaName,
                org_addr_func_district description,
            </if>
            <if test="areaLevel == 3">
                org_addr_street_code areaCode,
                org_addr_street areaName,
                org_addr_street description,
            </if>
        </if>

        <if test="'livingAddress' == addressType ">
            <if test="areaLevel == 1">
                living_addr_city_code areaCode,
                living_addr_city areaName,
                living_addr_city description,
            </if>
            <if test="areaLevel == 2">
                living_addr_func_district_code areaCode,
                living_addr_func_district areaName,
                living_addr_func_district description,
            </if>
            <if test="areaLevel == 3">
                living_addr_street_code areaCode,
                living_addr_street areaName,
                living_addr_street description,
            </if>
        </if>
    </sql>

    <select id="groupInfectAreaCnt" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
            <include refid="chooseStat"/>
            <include refid="chooseDescription"/>
        from <include refid="infectProcessTable"/>
        where rep_card_flag ='0'
        <if test="(statFlag == 'newCnt' or statFlag == 'newRate') and (startDate != null and endDate != null)">
            and i.fi_inf_onset_time between #{startDate} and #{endDate}
        </if>
        <if test="(statFlag == 'deathRate') and (startDate != null and endDate != null)">
            and i.lo_dead_time between #{startDate} and #{endDate}
        </if>
        <include refid="regionChoose"/>
        and
            dii.infect_class_name = #{infectClass}
        group by
            <if test="areaLevel == 1">
                drn.city_name,
                drn.city_code
            </if>
            <if test="areaLevel == 2">
                drn.district_name,
                drn.district_code
            </if>
            <if test="areaLevel == 3">
                drn.street_name,
                drn.street_code
            </if>
        order by
        <choose>
            <when test="statFlag == 'deathRate'">
                processDeadIllnessCnt
            </when>
            <otherwise>
                processNewCnt
            </otherwise>
        </choose>
        desc
    </select>


    <sql id="chooseStat">
        <choose>
            <when test="statFlag == 'deathRate'">
                count(1) as processDeadIllnessCnt,
            </when>
            <otherwise>
                count(1) as processNewCnt,
            </otherwise>
        </choose>
    </sql>

    <sql id="chooseDescription">
        <if test="areaLevel == 1">
            drn.city_name as description,
            drn.city_code as areaCode
        </if>
        <if test="areaLevel == 2">
            drn.district_name as description,
            drn.district_code as areaCode
        </if>
        <if test="areaLevel == 3">
            drn.street_name as description,
            drn.street_code as areaCode
        </if>
    </sql>

    <select id="groupInfectAreaOrgCnt" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
            <include refid="chooseStat"/>
        i.fi_inf_org_name as description
        from  <include refid="infectProcessTable"/>
        where rep_card_flag ='0'
            <if test="(statFlag == 'newCnt' or statFlag == 'newRate') and (startDate != null and endDate != null)">
                and i.fi_inf_onset_time between #{startDate} and #{endDate}
            </if>
            <if test="(statFlag == 'deathRate') and (startDate != null and endDate != null)">
                and i.lo_dead_time between #{startDate} and #{endDate}
            </if>
            <include refid="regionChoose"/>
        and dii.infect_class_name = #{infectClass}
        group by
        i.fi_inf_org_name
    </select>


    <select id="groupDeath" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO">
        select
        lo_dead_day as description,
        lo_dead_day as statDate,
        count(1) as processDeadIllnessCnt
        from
        ads.ads_ms_infect_process_case_info amipi
        where rep_card_flag = '0'
        <if test="startDate != null and endDate != null">
            and amipi.lo_dead_day between #{startDate} and #{endDate}
        </if>
        <include refid="regionChoose"/>
        group by
        amipi.lo_dead_day
    </select>

    <sql id="dynamicDayField">
        <choose>
            <when test="timeType == 'approveTime'">
                i.li_inf_check_day
            </when>
            <when test="timeType == 'onsetTime' or timeType == 'visitTime' or timeType == 'firVisitTime'">
                COALESCE( i.fi_inf_onset_day, i.fi_inf_visit_day )
            </when>
            <when test="timeType == 'deathTime'">
                i.lo_dead_day
            </when>
            <otherwise>
                i.fi_inf_identify_day
            </otherwise>
        </choose>
    </sql>

    <sql id="tenDayCommonGroupBy">
        year, month, ten_days, ten_days_desc
    </sql>

    <sql id="selectAgeGroup">
        CASE
        WHEN ag.value_code IS NOT NULL THEN ag.value_name
        ELSE '不详'
    END AS patient_age_group,
    CASE
        WHEN ag.value_code IS NOT NULL THEN ag.value_name
        ELSE '不详'
    END AS description
    </sql>

    <sql id="fromWithAgeGroup">
        ads.ads_ms_infect_process_case_info i
    LEFT JOIN dim.dim_age_group ag
        ON i.fi_inf_patient_age &gt;= ag.value_down
        AND i.fi_inf_patient_age &lt; ag.value_up
    </sql>

    <!-- 季度分组 -->
    <sql id="quarterCommonGroupBy">
         year, quarter, quarter_desc
    </sql>

    <!-- 周维度分组字段 -->
    <sql id="weekCommonGroupBy">
        year, week, week_desc
    </sql>

    <!-- 月维度 GroupBy 字段 -->
    <sql id="monthCommonGroupBy">
       year, month, month_desc
    </sql>



    <sql id="dimensionTableAndAreaCriteria">
        <include refid="regionChoose"/>
        <include refid="statSubtypeCriteria"/>
    </sql>

    <sql id="timeTypeCriteria">
        <choose>
            <when test="timeType == 'approveTime'">
                <if test="startDate != null">
                    and i.li_inf_check_day &gt;= CAST(#{startDate} AS DATE)
                </if>
                <if test="endDate != null">
                    and i.li_inf_check_day &lt;= CAST(#{endDate} AS DATE)
                </if>
            </when>
            <when test="timeType == 'onsetTime' or timeType == 'visitTime' or timeType == 'firVisitTime'">
                <if test="startDate != null">
                    and coalesce(i.fi_inf_onset_day, i.fi_inf_visit_day) &gt;= CAST(#{startDate} AS DATE)
                </if>
                <if test="endDate != null">
                    and coalesce(i.fi_inf_onset_day, i.fi_inf_visit_day) &lt;= CAST(#{endDate} AS DATE)
                </if>
            </when>
            <when test="timeType == 'deathTime'">
                <if test="startDate != null">
                    and i.lo_dead_day &gt;= CAST(#{startDate} AS DATE)
                </if>
                <if test="endDate != null">
                    and i.lo_dead_day &lt;= CAST(#{endDate} AS DATE)
                </if>
            </when>
            <otherwise>
                <if test="startDate != null">
                    and i.fi_inf_identify_day &gt;= CAST(#{startDate} AS DATE)
                </if>
                <if test="endDate != null">
                    and i.fi_inf_identify_day &lt;= CAST(#{endDate} AS DATE)
                </if>
            </otherwise>
        </choose>
    </sql>

    <sql id="timeTypeLastCriteria">
        <choose>
            <when test="timeType == 'approveTime'">
                and i.li_inf_check_day between #{lastStartDate} and #{lastEndDate}
            </when>
            <when test="timeType == 'onsetTime' or timeType == 'visitTime' or timeType == 'firVisitTime'">
                and coalesce(i.fi_inf_onset_day, i.fi_inf_visit_day) between #{lastStartDate} and #{lastEndDate}
            </when>
            <when test="timeType == 'deathTime'">
                and i.lo_dead_day between #{lastStartDate} and #{lastEndDate}
            </when>
            <otherwise>
                and i.fi_inf_identify_day between #{lastStartDate} and #{lastEndDate}
            </otherwise>
        </choose>
    </sql>

    <sql id="timeTypeLastYCriteria">
        <choose>
            <when test="timeType == 'approveTime'">
                and i.li_inf_check_day between #{lastYStartDate} and #{lastYEndDate}
            </when>
            <when test="timeType == 'onsetTime' or timeType == 'visitTime' or timeType == 'firVisitTime'">
                and coalesce(i.fi_inf_onset_day, i.fi_inf_visit_day) between #{lastYStartDate} and #{lastYEndDate}
            </when>
            <when test="timeType == 'deathTime'">
                and i.lo_dead_day between #{lastYStartDate} and #{lastYEndDate}
            </when>
            <otherwise>
                and i.fi_inf_identify_day between #{lastYStartDate} and #{lastYEndDate}
            </otherwise>
        </choose>
    </sql>

    <!-- 月度维表选择列 -->
    <sql id="monthDimSelectColumns">
        d.month_desc AS statDate,
        d.month_desc AS description,
    d.year,
    d.month
    </sql>

    <!-- 月度维表分组与排序 -->
    <sql id="monthDimGroupOrder">
        GROUP BY
        d.year, d.month, d.month_desc
    ORDER BY
        d.year DESC,
        d.month DESC
    </sql>

    <sql id="quarterDimSelectColumns">
        d.quarter_desc AS statDate,
        d.quarter_desc AS description,
    d.year,
    d.quarter
    </sql>

    <sql id="quarterDimGroupOrder">
        GROUP BY
        d.year, d.quarter, d.quarter_desc
    ORDER BY
        d.year DESC,
        d.quarter DESC
    </sql>

    <sql id="tenDaysDimSelectColumns">
        d.ten_days_desc AS statDate,
        d.ten_days_desc AS description,
    d.year,
    d.month,
    d.ten_days as tenDay
    </sql>

    <sql id="tenDaysDimGroupOrder">
        GROUP BY
        d.year, d.month, d.ten_days, d.ten_days_desc
    ORDER BY
        d.year,
        d.month,
        d.ten_days
    </sql>

    <sql id="joinDayDim">
        JOIN dim.dim_date_day d
        ON
        <include refid="dynamicDayField"/>
        BETWEEN d.start_time AND d.end_time
    </sql>


    <sql id="weekDimSelectColumns">
        d.week_desc AS statDate,
        d.week_desc AS description,
    d.year,
    d.week
    </sql>

    <sql id="weekDimGroupOrder">
        GROUP BY
        d.year, d.week, d.week_desc
    ORDER BY
        d.year,
        d.week
    </sql>

    <sql id="yearDimSelectColumns">
        CONCAT(d.year, '年') AS statDate,
        CONCAT(d.year, '年') AS description,
    d.year
    </sql>

    <sql id="yearDimGroupOrder">
        GROUP BY
        d.year
    ORDER BY
        d.year DESC
    </sql>

    <sql id="infectProcessTable">
        ads.ads_ms_infect_process_case_info i
        <include refid="infectCaseInfoTableJoin"/>
    </sql>


</mapper>