<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.OrgCommonMapper">

    <select id="getAllOrgInfo" resultType="com.iflytek.cdc.province.model.vo.ValueDomainVO">
        select
        org_id as value,
        org_name as label
        from dim.dim_organization_info
        where org_name not like '%测试%'
        and org_name not like '%导入%'
        and org_name not like '%巡检%'
    </select>

    <select id="getAllOrgList" resultType="com.iflytek.cdc.province.model.vo.OrgInfoVO">
        select org_id, org_name, org_address, org_type_code, org_type_name, source_type
        from dim.dim_organization_info
        where org_name not like '%测试%'
        and org_name not like '%导入%'
        and org_name not like '%巡检%'
        <if test="orgParams.areaList != null and orgParams.areaList.size() > 0">
            and
            <foreach collection="orgParams.areaList" index="index" item="area" open="(" separator=" or " close=")">
                1=1
                <if test="area.provinceCode != null and area.provinceCode != ''">
                    and province_code = #{area.provinceCode}
                </if>
                <if test="area.cityCode != null and area.cityCode != ''">
                    and city_code = #{area.cityCode}
                </if>
                <if test="area.districtCode != null and area.districtCode != ''">
                    and district_code = #{area.districtCode}
                </if>
            </foreach>
        </if>
        <if test="sourceType != null and sourceType.size() > 0">
            <foreach collection="sourceType" index="index" item="item" open=" and (" separator=" or " close=")">
                source_type = #{item}
            </foreach>
        </if>
        <if test="orgParams.orgName != null and orgParams.orgName != ''">
            AND org_name like concat('%', #{orgParams.orgName, jdbcType=VARCHAR}, '%')
        </if>
    </select>

    <select id="getOrgListBy" resultType="com.iflytek.cdc.province.model.vo.TreeNode">
        select
        org_id as value,
        org_name as label
        from dim.dim_organization_info
        where org_name not like '%测试%'
        and org_name not like '%导入%'
        and org_name not like '%巡检%'
        <if test="name != null and name != ''">
            and org_name like concat('%', #{name}, '%')
        </if>
        order by org_id
    </select>

</mapper>