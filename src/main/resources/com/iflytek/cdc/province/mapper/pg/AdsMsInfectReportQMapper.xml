<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMsInfectReportQMapper">

    <sql id="dayDb">
        <choose>
            <when test="timeType == 'approveTime' || timeType == 'checkTime'">
                ads.ads_ms_infect_report_q_ct_d
            </when>
            <when test="timeType == 'identifyTime' || timeType == 'reportTime'">
                ads.ads_ms_infect_report_q_it_d
            </when>
            <otherwise>
                ads.ads_ms_infect_report_q_ot_d
            </otherwise>
        </choose>
    </sql>

    <sql id="whereEval">
        <if test="infectClass != null and infectClass != ''">
            and infect_class_name = #{infectClass}
        </if>
        <if test="infectType != null and infectType != ''">
            and infect_type = #{infectType}
        </if>
        <if test="infectCode != null and infectCode != ''">
            and infect_code = #{infectCode}
        </if>
        <if test="orgId != null and orgId != ''">
            and org_id = #{orgId}
        </if>
        <include refid="addressCriteria"/>
    </sql>

    <sql id="addressMultiChooseCriteria">
        <if test="addressType == 'livingAddress'">
            <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                and living_addr_province_code in
                <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodeList != null and cityCodeList.size() > 0">
                and living_addr_city_code  in
                <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodeList != null and districtCodeList.size() > 0">
                and living_addr_func_district_code in
                <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="streetCodeList != null and streetCodeList.size() > 0">
                and living_addr_street_code  in
                <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="provinceCodes != null and provinceCodes.size()>0">
                and living_addr_province_code in
                <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodes != null and cityCodes.size()>0">
                and living_addr_city_code in
                <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodes != null and districtCodes.size()>0">
                and living_addr_func_district_code in
                <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="addressType == 'orgAddress'">
            <if test="provinceCodeList != null and provinceCodeList.size() > 0">
                and org_addr_province_code in
                <foreach collection="provinceCodeList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodeList != null and cityCodeList.size() > 0">
                and org_addr_city_code in
                <foreach collection="cityCodeList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodeList != null and districtCodeList.size() > 0">
                and org_addr_func_district_code in
                <foreach collection="districtCodeList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="streetCodeList != null and streetCodeList.size() > 0">
                and org_addr_street_code in
                <foreach collection="streetCodeList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="provinceCodes != null and provinceCodes.size()>0">
                and org_addr_province_code in
                <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodes != null and cityCodes.size()>0">
                and org_addr_city_code in
                <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodes != null and districtCodes.size()>0">
                and org_addr_func_district_code in
                <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </if>
    </sql>

    <sql id="addressCriteria">
        <if test="addressType == 'livingAddress'">
            <if test="provinceCode != null and provinceCode != ''">
                and living_addr_province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode != ''">
                and living_addr_city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode != ''">
                and living_addr_func_district_code = #{districtCode}
            </if>
            <if test="streetCode != null and streetCode != ''">
                and living_addr_street_code = #{streetCode}
            </if>
            <if test="provinceCodes != null and provinceCodes.size()>0">
                and living_addr_province_code in
                <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodes != null and cityCodes.size()>0">
                and living_addr_city_code in
                <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodes != null and districtCodes.size()>0">
                and living_addr_func_district_code in
                <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="addressType == 'orgAddress'">
            <if test="provinceCode != null and provinceCode != ''">
                and org_addr_province_code = #{provinceCode}
            </if>
            <if test="cityCode != null and cityCode != ''">
                and org_addr_city_code = #{cityCode}
            </if>
            <if test="districtCode != null and districtCode != ''">
                and org_addr_func_district_code = #{districtCode}
            </if>
            <if test="streetCode != null and streetCode != ''">
                and org_addr_street_code = #{streetCode}
            </if>
            <if test="provinceCodes != null and provinceCodes.size()>0">
                and org_addr_province_code in
                <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodes != null and cityCodes.size()>0">
                and org_addr_city_code in
                <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodes != null and districtCodes.size()>0">
                and org_addr_func_district_code in
                <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </if>
    </sql>

    <select id="dayEvalStat" resultType="com.iflytek.cdc.edr.vo.adsMsQualityEval.QualityEvalRespVO">
        select
        d.infect_class_name, a.infect_type, a.infect_code, a.infect_name,
        coalesce(count(a.infect_process_id),0)                                                          processNewCnt,
        coalesce(sum(case when a.check_status in ('1','3') then 1 else 0 end),0)                        needCheckCnt,
        coalesce(sum(case when a.check_status in ('1','3') then 1 else 0 end),0)                        checkCnt,
        coalesce(sum(case when a.identify_intime_flag =  '1' then 1 else 0 end),0)                      identifyIntimeCnt,
        coalesce(sum(case when a.identify_intime_flag = '2' then 1 else 0 end),0)                       identifyDelayCnt,
        coalesce(sum(case when a.check_intime_flag = '1' then 1 else 0 end),0)                          checkIntimeCnt,
        coalesce(sum(case when a.check_intime_flag = '2' then 1 else 0 end),0)                          checkDelayCnt,
        coalesce(sum(case when a.identify_way='1'and a.check_time is not null then 1 else 0 end), 0)    identifyTrueCnt,
        coalesce(sum(case when a.leak_flag='1' then 1 else 0 end),0)                                    leakCnt,
        coalesce(sum(case when a.checked_flag = '0' then 1 else 0 end) ,0)                              falseIdentifyCnt,
        coalesce(sum(case when a.full_flag = '1' then 1 else 0 end),0)                                  fullCnt,
        coalesce(sum(case when a.conform_flag = '1' then 1 else 0 end),0)                               conformCnt
        from ads.ads_ms_infect_report_info a
        join dim.dim_infected_info as d
        on a.infect_code = d.infected_sub_id
        where a.infect_code is not null and
        <choose>
            <when test="timeType == 'approveTime' || timeType == 'checkTime'">
                cast(a.check_time AS DATE)
            </when>
            <when test="timeType == 'identifyTime' || timeType == 'reportTime'">
                cast(a.identify_time AS DATE)
            </when>
            <otherwise>
                cast(a.onset_time AS DATE)
            </otherwise>
        </choose> between cast(#{startDate} AS DATE) and cast(#{endDate} AS DATE)
        <include refid="whereEval"/>
        group by d.infect_class_name, a.infect_type, a.infect_code, a.infect_name
        order by
            case
                when a.infect_type = '甲类传染病' then 1
                when a.infect_type = '乙类传染病' then 2
                when a.infect_type = '丙类传染病' then 3
                when a.infect_type = '其他传染病' then 4
                else 0
            end, a.infect_code
    </select>

</mapper>
