<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrPersonInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsEdrPersonInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_edr_person_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="create_org" jdbcType="VARCHAR" property="createOrg" />
    <result column="create_org_name" jdbcType="VARCHAR" property="createOrgName" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="patient_name" jdbcType="VARCHAR" property="patientName" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="birth_date" jdbcType="DATE" property="birthDate" />
    <result column="gender_code" jdbcType="VARCHAR" property="genderCode" />
    <result column="gender" jdbcType="VARCHAR" property="gender" />
    <result column="former_name" jdbcType="VARCHAR" property="formerName" />
    <result column="nationality_code" jdbcType="VARCHAR" property="nationalityCode" />
    <result column="nationality" jdbcType="VARCHAR" property="nationality" />
    <result column="nation_code" jdbcType="VARCHAR" property="nationCode" />
    <result column="nation" jdbcType="VARCHAR" property="nation" />
    <result column="marital_status_code" jdbcType="VARCHAR" property="maritalStatusCode" />
    <result column="marital_status" jdbcType="VARCHAR" property="maritalStatus" />
    <result column="permanent_addr_code" jdbcType="VARCHAR" property="permanentAddrCode" />
    <result column="permanent_addr" jdbcType="VARCHAR" property="permanentAddr" />
    <result column="permanent_addr_detail" jdbcType="VARCHAR" property="permanentAddrDetail" />
    <result column="temp_id_card" jdbcType="VARCHAR" property="tempIdCard" />
    <result column="contacts_id_card" jdbcType="VARCHAR" property="contactsIdCard" />
    <result column="contacts_name" jdbcType="VARCHAR" property="contactsName" />
    <result column="contacts_tel" jdbcType="VARCHAR" property="contactsTel" />
    <result column="current_addr_code" jdbcType="VARCHAR" property="currentAddrCode" />
    <result column="current_addr" jdbcType="VARCHAR" property="currentAddr" />
    <result column="current_addr_detail" jdbcType="VARCHAR" property="currentAddrDetail" />
    <result column="current_addr_detail_std" jdbcType="VARCHAR" property="currentAddrDetailStd" />
    <result column="current_addr_province_code" jdbcType="VARCHAR" property="currentAddrProvinceCode" />
    <result column="current_addr_province" jdbcType="VARCHAR" property="currentAddrProvince" />
    <result column="current_addr_city_code" jdbcType="VARCHAR" property="currentAddrCityCode" />
    <result column="current_addr_city" jdbcType="VARCHAR" property="currentAddrCity" />
    <result column="current_addr_district_code" jdbcType="VARCHAR" property="currentAddrDistrictCode" />
    <result column="current_addr_district" jdbcType="VARCHAR" property="currentAddrDistrict" />
    <result column="current_addr_func_district_code" jdbcType="VARCHAR" property="currentAddrFuncDistrictCode" />
    <result column="current_addr_func_district" jdbcType="VARCHAR" property="currentAddrFuncDistrict" />
    <result column="current_addr_street_code" jdbcType="VARCHAR" property="currentAddrStreetCode" />
    <result column="current_addr_street" jdbcType="VARCHAR" property="currentAddrStreet" />
    <result column="current_addr_street_longitude" jdbcType="NUMERIC" property="currentAddrStreetLongitude" />
    <result column="current_addr_street_latitude" jdbcType="NUMERIC" property="currentAddrStreetLatitude" />
    <result column="current_addr_longitude" jdbcType="NUMERIC" property="currentAddrLongitude" />
    <result column="current_addr_latitude" jdbcType="NUMERIC" property="currentAddrLatitude" />
    <result column="current_addr_poi" jdbcType="VARCHAR" property="currentAddrPoi" />
    <result column="current_addr_type" jdbcType="VARCHAR" property="currentAddrType" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="mpi_id" jdbcType="VARCHAR" property="mpiId" />
    <result column="update_org" jdbcType="VARCHAR" property="updateOrg" />
    <result column="update_org_name" jdbcType="VARCHAR" property="updateOrgName" />
    <result column="create_org_id" jdbcType="VARCHAR" property="createOrgId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, etl_create_datetime, etl_update_datetime, create_org, create_org_name, empi_id, 
    patient_name, id_card, birth_date, gender_code, gender, former_name, nationality_code, 
    nationality, nation_code, nation, marital_status_code, marital_status, permanent_addr_code, 
    permanent_addr, permanent_addr_detail, temp_id_card, contacts_id_card, contacts_name, 
    contacts_tel, current_addr_code, current_addr, current_addr_detail, current_addr_detail_std, 
    current_addr_province_code, current_addr_province, current_addr_city_code, current_addr_city, 
    current_addr_district_code, current_addr_district, current_addr_func_district_code, 
    current_addr_func_district, current_addr_street_code, current_addr_street, current_addr_street_longitude, 
    current_addr_street_latitude, current_addr_longitude, current_addr_latitude, current_addr_poi, 
    current_addr_type, create_user, create_time, update_user, update_time, source_id, 
    mpi_id, update_org, update_org_name, create_org_id
  </sql>

  <select id="getAdsEdrPersonInfo" resultType="com.iflytek.cdc.province.model.vo.edrcase.AdsEdrPersonInfoVO">
    select
    empi_id,
    patient_name,
    id_card,
    birth_date,
    gender,
    contacts_tel,
    current_addr,
    EXTRACT(YEAR FROM AGE(CURRENT_DATE, birth_date)) AS age
    from ads.ads_edr_person_info
    where empi_id = #{empi_id}
  </select>
</mapper>