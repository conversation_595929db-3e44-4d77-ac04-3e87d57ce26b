<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.SyndromeVerifyRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.bu.SyndromeVerifyRecord">
        <id column="id" property="id" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_time" property="createTime" />
        <result column="creator_id" property="creatorId" />
        <result column="creator" property="creator" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, delete_flag, create_time, creator_id, creator
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM app.tb_cdcew_syndrome_verify_record
        WHERE id = #{id} AND delete_flag != '1'
    </select>

    <!-- 根据ID列表查询 -->
    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM app.tb_cdcew_syndrome_verify_record
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND delete_flag != '1'
    </select>

    <!-- 插入单条记录 -->
    <insert id="insert" parameterType="com.iflytek.cdc.province.entity.bu.SyndromeVerifyRecord">
        INSERT INTO app.tb_cdcew_syndrome_verify_record
        (id, delete_flag, create_time, creator_id, creator)
        VALUES
        (#{id}, #{deleteFlag}, #{createTime}, #{creatorId}, #{creator})
        ON CONFLICT (id) DO UPDATE SET
            delete_flag = #{deleteFlag},
            create_time = #{createTime},
            creator_id = #{creatorId},
            creator = #{creator}
    </insert>

    <!-- 根据ID更新 -->
    <update id="updateById" parameterType="com.iflytek.cdc.province.entity.bu.SyndromeVerifyRecord">
        UPDATE app.tb_cdcew_syndrome_verify_record
        SET delete_flag = #{deleteFlag},
            create_time = #{createTime},
            creator_id = #{creatorId},
            creator = #{creator}
        WHERE id = #{id}
    </update>

</mapper> 