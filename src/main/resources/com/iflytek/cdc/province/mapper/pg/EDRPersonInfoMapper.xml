<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.EDRPersonInfoMapper">

    <select id="getCommonRecordLogById" resultType="com.iflytek.cdc.province.model.edr.vo.CommonRecordLogVO">
        select
        archive_id,
        patient_id,
        patient_name,
        life_id
        from ads.ads_ms_edr_person_info
        where archive_id = #{id}
        limit 1
    </select>

</mapper>