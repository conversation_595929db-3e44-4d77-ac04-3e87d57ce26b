<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsDiIndataStatTotalMapper">

    <sql id="areaFilter">
        <choose>
            <when test="param.areaLevel == 1">
                and area_type = 'province'
                <if test="param.provinceCode != null and param.provinceCode != ''">
                    and area_code = #{param.provinceCode,jdbcType=VARCHAR}
                </if>
                <if test="param.provinceCodes != null and param.provinceCodes.size() > 0">
                    and area_code in
                    <foreach collection="param.provinceCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <when test="param.areaLevel == 2">
                and area_type = 'city'
                <if test="param.cityCode != null and param.cityCode != ''">
                    and area_code = #{param.cityCode,jdbcType=VARCHAR}
                </if>
                <if test="param.cityCodes != null and param.cityCodes.size() > 0">
                    and area_code in
                    <foreach collection="param.cityCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <when test="param.areaLevel == 3">
                and area_type = 'district'
                <if test="param.districtCode != null and param.districtCode != ''">
                    and area_code = #{param.districtCode,jdbcType=VARCHAR}
                </if>
                <if test="param.districtCodes != null and param.districtCodes.size() > 0">
                    and area_code in
                    <foreach collection="param.districtCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
            <when test="param.areaLevel == 4">
                and area_type = 'street'
                <if test="param.streetCode != null and param.streetCode != ''">
                    and area_code = #{param.streetCode,jdbcType=VARCHAR}
                </if>
                <if test="param.streetCodes != null and param.streetCodes.size() > 0">
                    and area_code in
                    <foreach collection="param.streetCodes" open="(" separator="," close=")" item="item">
                        #{item}
                    </foreach>
                </if>
            </when>
        </choose>
    </sql>

    <select id="getTotalPatientCount" resultType="com.iflytek.cdc.province.entity.ads.AdsDiIndataStatTotal">
        select day, org_type, area_code, global_patient_cnt, global_patient_cnt_total
        from ads.ads_di_indata_stat_total
        where org_type = #{param.orgType}
        <if test="statDate != null">
            and day = #{statDate}
        </if>
        <include refid="areaFilter"/>
        order by day desc
        limit 1
    </select>

    <select id="getLastStatDate" resultType="java.util.Date">
        select max(day)
        from ads.ads_di_indata_stat_total
        where 1=1
        <include refid="areaFilter"/>
    </select>

    <select id="patientStat" resultType="com.iflytek.cdc.edr.vo.DiIndataStatVO">
        select
        day                                   as statDate,
        COALESCE(sum(global_patient_cnt), 0)  as globalPatientCnt
        from ads.ads_di_indata_stat_total m
        where org_type = #{param.orgType}
        <if test="param.startDate != null and param.endDate != null">
            and day between #{param.startDate} and #{param.endDate}
        </if>
        <include refid="areaFilter" />
        group by statDate
    </select>

</mapper>