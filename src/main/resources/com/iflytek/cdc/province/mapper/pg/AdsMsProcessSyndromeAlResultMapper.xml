<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsMsProcessSyndromeAlResultMapper">

    <select id="listByEventIds" resultType="com.iflytek.cdc.province.model.vo.ProcessSyndromeResultVO">
        SELECT
            identify_time,
            syndrome_code,
            syndrome_name,
            in_flag,
            in_support,
            out_flag,
            out_support,
            severe_flag,
            severe_support,
            subgroup,
            subgroup_support
        from ads.ads_ms_process_syndrome_al_result
        where event_id  in
        <foreach collection="eventIds" open="(" separator="," close=")" item="item">
            #{eventId}
        </foreach>
    </select>


</mapper>