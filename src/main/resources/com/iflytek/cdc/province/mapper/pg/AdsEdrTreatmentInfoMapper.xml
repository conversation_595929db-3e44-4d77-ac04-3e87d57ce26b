<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrTreatmentInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsEdrTreatmentInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_edr_treatment_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="registration_no" jdbcType="VARCHAR" property="registrationNo" />
    <result column="treatment_type_code" jdbcType="VARCHAR" property="treatmentTypeCode" />
    <result column="treatment_type" jdbcType="VARCHAR" property="treatmentType" />
    <result column="treatment_category_code" jdbcType="VARCHAR" property="treatmentCategoryCode" />
    <result column="treatment_category" jdbcType="VARCHAR" property="treatmentCategory" />
    <result column="treatment_shceme_code" jdbcType="VARCHAR" property="treatmentShcemeCode" />
    <result column="treatment_shceme" jdbcType="VARCHAR" property="treatmentShceme" />
    <result column="begin_treatment_date" jdbcType="TIMESTAMP" property="beginTreatmentDate" />
    <result column="stop_treatment_date" jdbcType="TIMESTAMP" property="stopTreatmentDate" />
    <result column="end_treatment_reason_code" jdbcType="VARCHAR" property="endTreatmentReasonCode" />
    <result column="end_treatment_reason" jdbcType="VARCHAR" property="endTreatmentReason" />
    <result column="non_treatment_reason_code" jdbcType="VARCHAR" property="nonTreatmentReasonCode" />
    <result column="non_treatment_reason" jdbcType="VARCHAR" property="nonTreatmentReason" />
    <result column="recovery_days" jdbcType="INTEGER" property="recoveryDays" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
    <result column="mpi_id" jdbcType="VARCHAR" property="mpiId" />
    <result column="create_org" jdbcType="VARCHAR" property="createOrg" />
    <result column="create_org_name" jdbcType="VARCHAR" property="createOrgName" />
    <result column="update_org" jdbcType="VARCHAR" property="updateOrg" />
    <result column="update_org_name" jdbcType="VARCHAR" property="updateOrgName" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, etl_create_datetime, etl_update_datetime, event_id, empi_id, registration_no, 
    treatment_type_code, treatment_type, treatment_category_code, treatment_category, 
    treatment_shceme_code, treatment_shceme, begin_treatment_date, stop_treatment_date, 
    end_treatment_reason_code, end_treatment_reason, non_treatment_reason_code, non_treatment_reason, 
    recovery_days, create_user, create_time, update_user, update_time, mpi_id, create_org, 
    create_org_name, update_org, update_org_name, source_id
  </sql>
</mapper>