<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.DimStreetInfoMapper">

    <sql id="Base_Column_List">
        street_id, province_code, province_name, city_code, city_name, district_code, district_name, street_code, street_name,
        street_longitude, street_latitude, street_source, memo, create_datetime, update_datetime, etl_create_datetime,
        etl_update_datetime, standard_district_code, standard_district_name, delete_flag, is_name_changed, original_street_name,
        alias_street_name

    </sql>

    <select id="listAll" resultType="com.iflytek.cdc.province.entity.dim.DimStreetInfo">
        select <include refid="Base_Column_List"/>
        from dim.dim_street_info
        where delete_flag = '0'
    </select>
</mapper>