<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcdmDataModelVersionMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelVersion">
    <!--@mbg.generated-->
    <!--@Table tb_cdcdm_data_model_version-->
    <id column="model_version_id" jdbcType="VARCHAR" property="modelVersionId" />
    <result column="model_id" jdbcType="VARCHAR" property="modelId" />
    <result column="model_label" jdbcType="VARCHAR" property="modelLabel" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="is_enable" jdbcType="SMALLINT" property="isEnable" />
    <result column="is_deleted" jdbcType="SMALLINT" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    model_version_id, model_id, model_label, version, "status", note, is_enable, is_deleted, 
    create_time, creator, update_time, updater
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tb_cdcdm_data_model_version
    where model_version_id = #{modelVersionId,jdbcType=VARCHAR}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from tb_cdcdm_data_model_version
    where model_version_id = #{modelVersionId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelVersion">
    <!--@mbg.generated-->
    insert into tb_cdcdm_data_model_version (model_version_id, model_id, model_label,
      version, "status", note, 
      is_enable, is_deleted, create_time, 
      creator, update_time, updater
      )
    values (#{modelVersionId,jdbcType=VARCHAR}, #{modelId,jdbcType=VARCHAR}, #{modelLabel,jdbcType=VARCHAR}, 
      #{version,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{note,jdbcType=VARCHAR}, 
      #{isEnable,jdbcType=SMALLINT}, #{isDeleted,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelVersion">
    <!--@mbg.generated-->
    insert into tb_cdcdm_data_model_version
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="modelVersionId != null and modelVersionId != ''">
        model_version_id,
      </if>
      <if test="modelId != null and modelId != ''">
        model_id,
      </if>
      <if test="modelLabel != null and modelLabel != ''">
        model_label,
      </if>
      <if test="version != null and version != ''">
        version,
      </if>
      <if test="status != null and status != ''">
        "status",
      </if>
      <if test="note != null and note != ''">
        note,
      </if>
      <if test="isEnable != null">
        is_enable,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="creator != null and creator != ''">
        creator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updater != null and updater != ''">
        updater,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="modelVersionId != null and modelVersionId != ''">
        #{modelVersionId,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null and modelId != ''">
        #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="modelLabel != null and modelLabel != ''">
        #{modelLabel,jdbcType=VARCHAR},
      </if>
      <if test="version != null and version != ''">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="note != null and note != ''">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null and updater != ''">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelVersion">
    <!--@mbg.generated-->
    update tb_cdcdm_data_model_version
    <set>
      <if test="modelId != null and modelId != ''">
        model_id = #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="modelLabel != null and modelLabel != ''">
        model_label = #{modelLabel,jdbcType=VARCHAR},
      </if>
      <if test="version != null and version != ''">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''">
        "status" = #{status,jdbcType=VARCHAR},
      </if>
      <if test="note != null and note != ''">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        is_enable = #{isEnable,jdbcType=SMALLINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null and creator != ''">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null and updater != ''">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where model_version_id = #{modelVersionId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelVersion">
    <!--@mbg.generated-->
    update tb_cdcdm_data_model_version
    set model_id = #{modelId,jdbcType=VARCHAR},
      model_label = #{modelLabel,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      "status" = #{status,jdbcType=VARCHAR},
      note = #{note,jdbcType=VARCHAR},
      is_enable = #{isEnable,jdbcType=SMALLINT},
      is_deleted = #{isDeleted,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      updater = #{updater,jdbcType=VARCHAR}
    where model_version_id = #{modelVersionId,jdbcType=VARCHAR}
  </update>

  <select id="getModelIdByModelVersionId" resultType="java.lang.String">
    select model_id
    from tb_cdcdm_data_model_version
    where model_version_id = #{modelVersionId,jdbcType=VARCHAR} and status = '已发布'
  </select>
</mapper>