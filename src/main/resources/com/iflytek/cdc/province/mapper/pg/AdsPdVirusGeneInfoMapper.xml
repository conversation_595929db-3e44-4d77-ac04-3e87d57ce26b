<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsPdVirusGeneInfoMapper">

    <sql id="orgArea">
        <if test="provinceCode != null and provinceCode != ''">
            and org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and org_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and org_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and org_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and org_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="livingArea">
        <if test="provinceCode != null and provinceCode != ''">
            and living_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and living_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and living_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and living_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and living_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and living_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and living_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and living_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="chooseArea">
        <choose>
            <when test="addressType == 'orgAddress'">
                <include refid="orgArea"/>
            </when>
            <when test="addressType == 'livingAddress'">
                <include refid="livingArea"/>
            </when>
            <otherwise>
                <include refid="livingArea"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="dayCriteria">
        <if test="startDate != null and endDate != null">
            and day between #{startDate} and #{endDate}
        </if>
    </sql>

    <sql id="whereCriteria">
        <include refid="chooseArea"/>
        <if test="processType != null and processType != ''">
            and process_type = #{processType}
        </if>
        <if test="labIdList != null and labIdList.size() &gt; 0">
            and sample_org_id in
            <foreach collection="labIdList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startDate != null and endDate != null">
            and sample_time between #{startDate} and #{endDate}
        </if>
        <if test="sampleStartDate != null and sampleEndDate != null">
            and sample_time between #{sampleStartDate} and #{sampleEndDate}
        </if>
    </sql>

    <select id="xxx"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.VirusAntigenResultVO">
        select
            virus_sub_name as type,
            refer_pathogen as referAntigenName,
            test_org_name as labName,
            COALESCE(sum(similar_cnt), 0) as similarStrainCount,
            COALESCE(sum(low_cnt), 0) as lowResponseStrainCount,
            COALESCE(sum(sample_cnt), 0) as totalCount
        from ads.ads_pd_virus_gene_info
        where 1=1
            <include refid="whereCriteria"/>
        group by virus_sub_name, refer_pathogen, test_org_name
        order by virus_sub_name, refer_pathogen, test_org_name
    </select>

    <select id="getResistanceAnalysisResult"
            resultType="com.iflytek.cdc.province.model.multichannelMonitor.vo.VirusResistanceResultVO">
        select
            pd_virus_gene_id as id,
            sample_org_id as labId,
            sample_org_name as labName,
            sample_time as sampleTime,
            virus_name as strainName,
            virus_sub_name as type,
            evolve_history as dynasticHistory,
            ha_titer as HATiter,
            test_method as detectedMethod,
            null as oseltamivir,
            null as zanamivir,
            null as peramivir,
            null as laninamivir,
            test_ast_res as sensitivity,
            mutate_position as mutation
        from ads.ads_pd_virus_gene_info
        where 1=1
            <include refid="whereCriteria"/>
        order by sample_time desc nulls last
    </select>
</mapper>
