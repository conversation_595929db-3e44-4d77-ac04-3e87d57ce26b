<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrDeadInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsEdrDeadInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_edr_dead_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="death_reason_type_code" jdbcType="VARCHAR" property="deathReasonTypeCode" />
    <result column="death_reason_detail_code" jdbcType="VARCHAR" property="deathReasonDetailCode" />
    <result column="death_info_source_code" jdbcType="VARCHAR" property="deathInfoSourceCode" />
    <result column="direct_cause_code" jdbcType="VARCHAR" property="directCauseCode" />
    <result column="dead_date" jdbcType="TIMESTAMP" property="deadDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="mpi_id" jdbcType="VARCHAR" property="mpiId" />
    <result column="create_org" jdbcType="VARCHAR" property="createOrg" />
    <result column="create_org_name" jdbcType="VARCHAR" property="createOrgName" />
    <result column="update_org" jdbcType="VARCHAR" property="updateOrg" />
    <result column="update_org_name" jdbcType="VARCHAR" property="updateOrgName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, etl_create_datetime, etl_update_datetime, event_id, empi_id, death_reason_type_code, 
    death_reason_detail_code, death_info_source_code, direct_cause_code, dead_date, create_user, 
    create_time, update_user, update_time, source_id, mpi_id, create_org, create_org_name, 
    update_org, update_org_name
  </sql>

  <select id="getAdsEdrDeadInfo" resultType="com.iflytek.cdc.province.model.vo.edrcase.AdsEdrDeadInfoVO">
    select
    event_id, empi_id, death_reason_type_code,
    death_reason_detail_code, death_info_source_code, direct_cause_code, dead_date
    from ads.ads_edr_dead_info
    where 1=1
    <if test="empiId != null and empiId != ''">
      and empi_id=#{empiId}
    </if>
    <if test="eventIds != null and eventIds.length>0 ">
      and event_id in
      <foreach item="eventId" collection="eventIds" open="(" separator="," close=")">
        #{eventId}
      </foreach>
    </if>
  </select>
</mapper>