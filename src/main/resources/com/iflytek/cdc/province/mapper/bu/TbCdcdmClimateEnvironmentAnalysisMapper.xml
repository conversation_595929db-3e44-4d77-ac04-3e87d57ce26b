<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.TbCdcdmClimateEnvironmentAnalysisMapper">


    <select id="queryClimateEnvironmentAnalysis"
            resultType="com.iflytek.cdc.province.model.vo.ClimateEnvironmentAnalysisVo">
        select
            "year" || '年' || to_char("month", 'FM00') || '月' as statDate,
            temperature,
            humidity,
            air_pressure_avg,
            precipitation
        from
            tb_cdcdm_climate_environment_analysis
        where 1=1
        and
        <foreach collection="dateDims" item="item" separator=" or " open="(" close=")">
            ("year" = #{item.year} and "month" = #{item.month})
        </foreach>
        <choose>
            <when test="provinceCode != null and provinceCode != ''">
                and province_code = #{provinceCode}
            </when>
            <otherwise>
                and province_code is null
            </otherwise>
        </choose>
        <choose>
            <when test="cityCode != null and cityCode != ''">
                and city_code = #{cityCode}
            </when>
            <otherwise>
                and city_code is null
            </otherwise>
        </choose>
        <choose>
            <when test="districtCode != null and districtCode != ''">
                and district_code = #{districtCode}
            </when>
            <otherwise>
                and district_code is null
            </otherwise>
        </choose>
        <choose>
            <when test="streetCode != null and streetCode != ''">
                and street_code = #{streetCode}
            </when>
            <otherwise>
                and street_code is null
            </otherwise>
        </choose>
    </select>
</mapper>