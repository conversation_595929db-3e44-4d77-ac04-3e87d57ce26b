<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsFsSuggestWordMapper">

    <select id="getSuggestWords" resultType="com.iflytek.cdc.province.model.vo.SuggestWordVO">
        select tag, tag_value
        from ads.ads_fs_suggest_word
        where LOWER(tag_value) like LOWER(CONCAT('%', #{text,jdbcType=VARCHAR}, '%'))
        order by value_length, tag_order, id desc
    </select>
</mapper>
