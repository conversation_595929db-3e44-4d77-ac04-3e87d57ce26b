<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrTreatmentDetailMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsEdrTreatmentDetail">
    <!--@mbg.generated-->
    <!--@Table ads.ads_edr_treatment_detail-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="drug_code" jdbcType="VARCHAR" property="drugCode" />
    <result column="drug" jdbcType="VARCHAR" property="drug" />
    <result column="drug_dosage_code" jdbcType="VARCHAR" property="drugDosageCode" />
    <result column="drug_dosage" jdbcType="VARCHAR" property="drugDosage" />
    <result column="drug_dosage_route_code" jdbcType="VARCHAR" property="drugDosageRouteCode" />
    <result column="drug_dosage_route" jdbcType="VARCHAR" property="drugDosageRoute" />
    <result column="drug_end_date" jdbcType="TIMESTAMP" property="drugEndDate" />
    <result column="drug_begin_date" jdbcType="TIMESTAMP" property="drugBeginDate" />
    <result column="drug_frequency" jdbcType="INTEGER" property="drugFrequency" />
    <result column="drug_dose_code" jdbcType="VARCHAR" property="drugDoseCode" />
    <result column="drug_given_quantity" jdbcType="VARCHAR" property="drugGivenQuantity" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="mpi_id" jdbcType="VARCHAR" property="mpiId" />
    <result column="create_org" jdbcType="VARCHAR" property="createOrg" />
    <result column="create_org_name" jdbcType="VARCHAR" property="createOrgName" />
    <result column="update_org" jdbcType="VARCHAR" property="updateOrg" />
    <result column="update_org_name" jdbcType="VARCHAR" property="updateOrgName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, etl_create_datetime, etl_update_datetime, event_id, empi_id, drug_code, drug, 
    drug_dosage_code, drug_dosage, drug_dosage_route_code, drug_dosage_route, drug_end_date, 
    drug_begin_date, drug_frequency, drug_dose_code, drug_given_quantity, create_user, 
    create_time, update_user, update_time, source_id, mpi_id, create_org, create_org_name, 
    update_org, update_org_name
  </sql>

  <select id="getTreatmentDetaiByEventId" parameterType="string"
          resultType="com.iflytek.cdc.province.model.vo.edrcase.AdsEdrTreatmentDetailVO">
    select
    ti.id,
    ti.event_id,
    ti.empi_id,
    ti.registration_no,
    ti.treatment_type_code,
    ti.treatment_type,
    ti.treatment_category_code,
    ti.treatment_category,
    ti.treatment_shceme_code,
    ti.treatment_shceme,
    ti.begin_treatment_date,
    ti.stop_treatment_date,
    ti.end_treatment_reason_code,
    ti.end_treatment_reason,
    ti.non_treatment_reason_code,
    ti.non_treatment_reason,
    td.id as detailId,
    td.drug_code,
    td.drug,
    td.drug_dosage_code,
    td.drug_dosage,
    td.drug_dosage_route_code,
    td.drug_dosage_route,
    td.drug_end_date,
    td.drug_begin_date,
    td.drug_frequency,
    td.drug_dose_code,
    td.drug_given_quantity
    from ads.ads_edr_treatment_info  ti
    left join ads.ads_edr_treatment_detail td
    on ti.id  = td.ms_treatment_info_id
    where ti.event_id = #{eventId}
    order by ti.create_time desc
  </select>
</mapper>