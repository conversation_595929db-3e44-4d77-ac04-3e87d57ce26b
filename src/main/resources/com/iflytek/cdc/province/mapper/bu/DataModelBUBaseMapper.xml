<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.bu.DataModelBUBaseMapper">

    <select id="selectDataBySql" resultType="map">
        ${customSql}
    </select>

    <select id="selectDataDynamical" resultType="map">
        SELECT
        <foreach item="column" collection="fieldColumns" separator=",">
            ${column}
        </foreach>
        FROM ${table}
        <where>
            <foreach index="key" item="value" collection="filterParam.param" separator=" AND ">
                <choose>
                    <when test="value instanceof java.util.Collection">
                        #{key} IN
                        <foreach item="item" collection="value" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </when>
                    <otherwise>
                        #{key} = #{value}
                    </otherwise>
                </choose>
            </foreach>
        </where>
    </select>

</mapper>