<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.EndemicProcessInfoMapper">

    <select id="listMedCntIndicator" resultType="com.iflytek.cdc.province.model.vo.MedCntIndicatorVO">
        select
        CAST(<include refid="endemicTimeChoose"/> AS DATE) as stat_date,
        sum(case when lo_outcome_status = '治愈' then 1 else 0 end) as recoverCnt ,
        sum(case when lo_outcome_status = '死亡' then 1 else 0 end) as deathCnt,
        sum(case when end_flag = '0' then 1 else 0 end) as existingCaseCnt ,
        sum(case when lo_dead_reason = '因该病死亡' then 1 else 0 end) as diseaseDeathCnt,
        count(syndrome_process_id) as medCaseCnt
        from ads.ads_ms_endemic_process_info i
        where 1= 1
        <include refid="endemicMedCntCriteria"/>
        <!-- 病例过滤 -->
        <include refid="endemicOrgAddressCriteria"/>
        <include refid="endemicProcessCriteria"/>
        <include refid="endemicProcessOtherCriteria"/>
        <if test="startDate != null and endDate != null">
            and <include refid="endemicTimeChoose"/> between #{startDate} and #{endDate}
        </if>
        and <include refid="endemicTimeChoose"/> is not null
        group by stat_date order by stat_date
    </select>

    <select id="listOutcomeCntIndicator" resultType="com.iflytek.cdc.province.model.vo.MedCntIndicatorVO">
        select
        CAST(lo_outcome_time AS DATE) as stat_date,
        sum(case when lo_outcome_status = '治愈' then 1 else 0 end) as recoverCnt ,
        sum(case when lo_outcome_status = '死亡' then 1 else 0 end) as deathCnt,
        sum(case when end_flag = '0' then 1 else 0 end) as existingCaseCnt ,
        sum(case when lo_dead_reason = '因该病死亡' then 1 else 0 end) as diseaseDeathCnt
        from ads.ads_ms_endemic_process_info i
        where lo_outcome_time is not null
        <include refid="endemicMedCntCriteria"/>
        <!-- 病例过滤 -->
        <include refid="endemicOrgAddressCriteria"/>
        <include refid="endemicProcessCriteria"/>
        <include refid="endemicProcessOtherCriteria"/>
        <if test="startDate != null and endDate != null">
            and lo_outcome_time between #{startDate} and #{endDate}
        </if>
        group by stat_date order by stat_date
    </select>

    <select id="listAreaMedCntIndicator" resultType="com.iflytek.cdc.province.model.vo.AreaMedCntIndicatorVO">
        select
        CAST(<include refid="endemicTimeChoose"/> AS DATE) as stat_date,
        count(syndrome_process_id) as medCaseCnt,
        <include refid="endemicAreaChoose"/>
        from ads.ads_ms_endemic_process_info i
        where 1= 1
        <include refid="endemicMedCntCriteria"/>
        <if test="startDate != null and endDate != null">
            and <include refid="endemicTimeChoose"/> between #{startDate} and #{endDate}
        </if>
        group by stat_date, areaLevel, areaCode, areaName
        order by stat_date
    </select>

    <select id="listAreaOutcomeCntIndicator"
            resultType="com.iflytek.cdc.province.model.vo.AreaMedCntIndicatorVO">
        select
        CAST(lo_outcome_time AS DATE) as stat_date,
        sum(case when lo_outcome_status = '治愈' then 1 else 0 end) as recoverCnt ,
        sum(case when lo_outcome_status = '死亡' then 1 else 0 end) as deathCnt,
        sum(case when end_flag = '0' then 1 else 0 end) as existingCaseCnt ,
        sum(case when lo_dead_reason = '因该病死亡' then 1 else 0 end) as diseaseDeathCnt,
        <include refid="endemicAreaChoose"/>
        from ads.ads_ms_endemic_process_info i
        where 1= 1
        <include refid="endemicMedCntCriteria"/>
        <if test="startDate != null and endDate != null">
            and lo_outcome_time between #{startDate} and #{endDate}
        </if>
        group by stat_date, areaLevel, areaCode, areaName
        order by stat_date
    </select>

    <select id="listAddressByIds" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO">
        select
        syndrome_process_id as id,
        <include refid="endemicAddressMap"/>
        from ads.ads_ms_endemic_process_info i
        where syndrome_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
        <!-- 病例过滤 -->
        <include refid="endemicOrgAddressCriteria"/>
        <include refid="endemicProcessCriteria"/>
        <include refid="endemicProcessOtherCriteria"/>
    </select>

    <select id="listSimpleInfo" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO">
        select
        <include refid="endemicSimpleInfoMap"/>,
        patient_name AS patientName,
        patient_identity_no AS idCard
        from ads.ads_ms_endemic_process_info i
        where 1=1
        <if test="ids != null and ids.size() > 0">
            and syndrome_process_id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and syndrome_process_id = #{id}
        </if>
        <if test="symptomName != null and symptomName != ''">
            and symptom_attent_extr_json LIKE CONCAT('%', #{symptomName}, '%')
        </if>
        <if test="startDate != null and endDate != null">
            and <include refid="endemicTimeChoose"/> between #{startDate} and #{endDate}
        </if>
        <include refid="endemicOrgAddressCriteria"/>
        <include refid="endemicProcessCriteria"/>
        <include refid="endemicProcessOtherCriteria"/>
        <include refid="endemicOrder"/>
    </select>

    <select id="loadProcessSimpleById"
            resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoDetailVO">
        select
        <include refid="endemicSimpleInfoMap"/> ,
        patient_name AS patientName,
        patient_identity_no AS idCard,
        patient_birth_day AS birthDate,
        patient_sex_name AS genderName,
        lv_living_addr_detail  AS livingAddrDetail,
        lv_living_addr_province_code AS livingAddrProvinceCode,
        lv_living_addr_province AS livingAddrProvinceName,

        lv_living_addr_city_code AS livingAddrCityCode,
        lv_living_addr_city AS livingAddrCityName,

        lv_living_addr_func_district_code AS livingAddrDistrictCode,
        lv_living_addr_func_district AS livingAddrDistrictName,

        lv_living_addr_street_code AS livingAddrStreetCode,
        lv_living_addr_street AS livingAddrStreet,


        lv_living_addr_street_longitude AS livingAddrLongitude,
        lv_living_addr_street_latitude AS livingAddrLatitude
        from ads.ads_ms_endemic_process_info
        where syndrome_process_id = #{id}
    </select>

    <select id="listPatientInfoByProcessIds" resultType="com.iflytek.cdc.province.model.vo.MsPatientInfoVO">
        select
        empi_id as patient_id,
        syndrome_process_id as id,
        patient_name as patientName,
        patient_sex_name as patientSexName,
        fi_syn_patient_age as patientAge,
        fi_syn_job as job,
        li_syn_syndrome_severe_flag as li_severe_flag,
        lo_outcome_status,
        fi_syn_event_id as eventId,
        event_json,
        li_syn_syndrome_subgroup as li_subgroup
        from ads.ads_ms_endemic_process_info i
        where syndrome_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
        <!-- 病例过滤 -->
        <include refid="endemicOrgAddressCriteria"/>
        <include refid="endemicProcessCriteria"/>
        <include refid="endemicProcessOtherCriteria"/>
    </select>

    <select id="countEmerging" resultType="java.lang.Integer">
        select
        count(*)
        from ads.ads_ms_endemic_process_info
        where syndrome_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="listProcessModelSimpleInfo"
            resultType="com.iflytek.cdc.province.model.vo.EndemicProcessModelSimpleInfo">
        select
        syndrome_process_id as process_id,
        patient_sex_name  as patientSexName,
        lv_living_addr_province_code as livingAddressProvinceCode,
        lv_living_addr_province as livingAddressProvinceName,
        lv_living_addr_city_code as livingAddressCityCode,
        lv_living_addr_city as livingAddressCityName,
        lv_living_addr_func_district_code as livingAddressDistrictCode,
        lv_living_addr_func_district as livingAddressDistrictName,
        lv_living_addr_street_code as livingAddressStreetCode,
        lv_living_addr_street as livingAddressStreetName,
        lv_person_type as personType,
        fi_syn_school_type  as schoolType,
        lv_company_province_code  as schoolAddressProvinceCode,
        lv_company_province  as schoolAddressProvinceName,
        lv_company_city_code  as schoolAddressCityCode,
        lv_company_city  as schoolAddressCityName,
        lv_company_func_district_code  as schoolAddressDistrictCode,
        lv_company_func_district  as schoolAddressDistrictName,
        lv_company_street_code  as schoolAddressStreetCode,
        lv_company_street  as schoolAddressStreetName,
        lv_company_addr_detail_std as schoolAddress,
        fi_syn_org_name as firstDiagnosisOrg,
        fi_pathogen_res_type  as firstPathogenTestResult,
        lv_pathogen_res_type  as lastPathogenTestResult,
        lo_outcome_status as outCome,
        lo_dead_reason as deadReason,
        li_syn_syndrome_name  syndrome,
        confirm_disease as confirmedDisease,
        li_syn_syndrome_out_flag as isExcluded,
        li_syn_syndrome_severe_flag as isSevere,
        infect_type  as caseType,
        li_syn_syndrome_severe_support as severeReason
        from ads.ads_ms_endemic_process_info amspi
        where syndrome_process_id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="listProcessLog" resultType="com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO">
        select
        occur_time as date,
        label_type as type,
        label_value as value,
        case when source_desc = '传染病识别' then 'infected' else '' end as specialMark
        from ads.ads_ms_syndrome_process_view
        where delete_flag = '0' and syndrome_process_id = #{id}
        <if test="type !=null">
            and label_type = #{type}
        </if>
        order by occur_time
    </select>

    <select id="getProcessInfoBy"
            resultType="com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo">
        select
        syndrome_process_id as id,
        patient_name as patient_name,
        patient_sex_name as patient_sex_name,
        patient_birth_day as birth_day,
        fi_syn_patient_age as patient_age,
        fi_syn_patient_age_unit as patient_age_unit,
        fi_syn_job as job,
        fi_syn_person_type as person_type,

        fi_syn_living_addr_detail AS living_addr_detail,
        fi_syn_living_addr_province_code AS living_province_code,
        fi_syn_living_addr_province AS living_province_name,
        fi_syn_living_addr_city_code AS living_city_code,
        fi_syn_living_addr_city AS living_city_name,
        fi_syn_living_addr_district_code AS living_district_code,
        fi_syn_living_addr_district AS living_district_name,
        fi_syn_living_addr_street_code AS living_street_code,
        fi_syn_living_addr_street AS living_street_name,
        fi_syn_living_addr_longitude AS living_addr_longitude,
        fi_syn_living_addr_latitude AS living_addr_latitude,

        fi_syn_company AS company,
        fi_syn_company_province_code AS company_province_code,
        fi_syn_company_province AS company_province_name,
        fi_syn_company_city_code AS company_city_code,
        fi_syn_company_city AS company_city_name,
        fi_syn_company_district_code AS company_district_code,
        fi_syn_company_district AS company_district_name,
        fi_syn_company_street_code AS company_street_code,
        fi_syn_company_street AS company_street_name,
        fi_syn_company_longitude AS company_addr_longitude,
        fi_syn_company_latitude AS company_addr_latitude,

        fi_syn_org_id AS org_id,
        fi_syn_org_name AS org_name,
        fi_syn_org_addr_province_code AS org_province_code,
        fi_syn_org_addr_province AS org_province_name,
        fi_syn_org_addr_city_code AS org_city_code,
        fi_syn_org_addr_city AS org_city_name,
        fi_syn_org_addr_district_code AS org_district_code,
        fi_syn_org_addr_district AS org_district_name,
        fi_syn_org_addr_street_code AS org_street_code,
        fi_syn_org_addr_street AS org_street_name,
        fi_syn_org_longitude AS org_addr_longitude,
        fi_syn_org_latitude AS org_addr_latitude,

        fi_syn_onset_time as onset_time,
        fi_syn_visit_time as first_visit_time,
        fi_syn_org_id as first_visit_org_id,
        fi_syn_org_name as first_visit_org_name,
        fi_syn_identify_time as diagnose_time,
        'syndrome' as disease_type,
        fi_syn_syndrome_code as disease_code,
        fi_syn_syndrome_name as disease_name,
        lo_outcome_status as outcome_status,
        lo_outcome_time as outcome_time,
        lo_dead_time as death_time,
        severe_flag as severe_flag,
        fi_syn_syndrome_severe_time as severe_time,
        li_syn_syndrome_out_flag as out_flag,
        fi_syn_syndrome_out_time as out_time,
        'syndrome' as process_type
        from ads.ads_ms_endemic_process_info
        where syndrome_process_id in
        <foreach collection="ids" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getProcessPathogenInfoBy" resultType="com.iflytek.cdc.province.model.vo.ProcessPathogenInfoVO">
        select
        sum(case when pathogen_flag = '1' then 1 else 0 end) as totalTests,
        sum(case when pathogen_flag = '1' and pathogen_name is not null then 1 else 0 end) as positiveCnt,
        sum(case when pathogen_flag = '1' and pathogen_name is null then 1 else 0 end) as negativeCnt
        from ads.ads_ms_endemic_process_info
        where syndrome_process_id in
        <foreach collection="ids" separator="," item="item" index="index" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <sql id="endemicTimeChoose">
        <choose>
            <when test="timeType == 'approveTime'">
                fi_syn_check_time
            </when>
            <when test="timeType == 'identifyTime'">
                fi_syn_identify_time
            </when>
            <otherwise>
                fi_syn_visit_day
            </otherwise>
        </choose>
    </sql>

    <sql id="endemicMedCntCriteria">
        <if test="ids != null and ids.size() > 0">
            and syndrome_process_id in
            <foreach collection="ids" open="(" separator="," close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="id != null and id != ''">
            and syndrome_process_id = #{id}
        </if>
        <if test="fvOrgId != null and fvOrgId != ''">
            and fi_syn_org_id = #{fvOrgId}
        </if>
        <if test="fvOrgName != null and fvOrgName != ''">
            and fi_syn_org_name = #{fvOrgName}
        </if>
        <include refid="endemicAreaChooseCriteria"/>
    </sql>

    <sql id="endemicOrgAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and i.fi_syn_org_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and i.fi_syn_org_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and i.fi_syn_org_addr_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and i.fi_syn_org_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and i.fi_syn_org_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and i.fi_syn_org_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and i.fi_syn_org_addr_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="streetCodes != null and streetCodes.size()>0">
            and i.fi_syn_org_addr_street_code in
            <foreach collection="streetCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="endemicProcessCriteria">
        <if test="fvOrgId != null and fvOrgId != ''">
            and i.fi_syn_org_id = #{fvOrgId}
        </if>
        <if test="fvOrgName != null and fvOrgName != ''">
            and i.fi_syn_org_name like CONCAT('%', #{fvOrgName}, '%')
        </if>
        <if test="job != null and job != ''">
            and i.fi_syn_job = #{job}
        </if>
        <if test="company != null and company != ''">
            and i.fi_syn_company = #{company}
        </if>
        <if test="patientSexName != null and patientSexName != ''">
            and i.patient_sex_name = #{patientSexName}
        </if>
        <if test="visitStartDate != null">
            and i.fi_syn_visit_day >= #{visitStartDate}
        </if>
        <if test="visitEndDate != null">
            and i.fi_syn_visit_day &lt;= #{visitEndDate}
        </if>
        <if test="onsetStartDate != null">
            and i.fi_syn_onset_time >= #{onsetStartDate}
        </if>
        <if test="onsetEndDate != null">
            and i.fi_syn_onset_time &lt;= #{onsetEndDate}
        </if>
        <if test="mainDiag != null and mainDiag != ''">
            and i.fi_syn_main_diag = #{mainDiag}
        </if>
        <if test="hasExcluded != null and hasExcluded != ''">
            and i.li_syn_syndrome_out_flag = #{hasExcluded}
        </if>
        <if test="isSevere != null and isSevere != ''">
            and i.li_syn_syndrome_severe_flag = #{isSevere}
        </if>
        <if test="outComeStatus != null and outComeStatus != ''">
            and i.lo_outcome_status = #{outComeStatus}
        </if>
        <if test="queryKey != null and queryKey !=''">
            and (i.syndrome_process_id like CONCAT('%', #{queryKey}, '%') or i.fi_syn_org_name like CONCAT('%', #{queryKey}, '%'))
        </if>
    </sql>

    <sql id="endemicProcessOtherCriteria">
        <!-- 地址选择 -->
        <if test="livingProvinceCode != null and livingProvinceCode != ''">
            and i.fi_syn_living_addr_province_code = #{livingProvinceCode}
        </if>
        <if test="livingCityCode != null and livingCityCode != ''">
            and i.fi_syn_living_addr_city_code = #{livingCityCode}
        </if>
        <if test="livingDistrictCode != null and livingDistrictCode != ''">
            and i.fi_syn_living_addr_district_code = #{livingDistrictCode}
        </if>
        <if test="companyProvinceCode != null and companyProvinceCode != ''">
            and i.fi_syn_company_province_code = #{companyProvinceCode}
        </if>
        <if test="companyCityCode != null and companyCityCode != ''">
            and i.fi_syn_company_city_code = #{companyCityCode}
        </if>
        <if test="companyDistrictCode != null and companyDistrictCode != ''">
            and i.fi_syn_company_district_code = #{companyDistrictCode}
        </if>
        <!-- 年龄筛选 -->
        <if test="ageMin != null">
            and
            case
            when i.fi_syn_patient_age_unit = '岁' or i.fi_syn_patient_age_unit is null then i.fi_syn_patient_age
            when i.fi_syn_patient_age_unit = '月' then i.fi_syn_patient_age / 12.0
            when i.fi_syn_patient_age_unit = '天' then i.fi_syn_patient_age / 365.0
            else -1
            end >= #{ageMin}
        </if>
        <if test="ageMax != null">
            and
            case
            when i.fi_syn_patient_age_unit = '岁' or i.fi_syn_patient_age_unit is null then i.fi_syn_patient_age
            when i.fi_syn_patient_age_unit = '月' then i.fi_syn_patient_age / 12.0
            when i.fi_syn_patient_age_unit = '天' then i.fi_syn_patient_age / 365.0
            else 9999
            end &lt;= #{ageMax}
        </if>
        <!-- 病原监测 -->
        <if test="firstPathogenResult != null and firstPathogenResult != ''">
            and i.fi_pathogen_res_type = #{firstPathogenResult}
        </if>
        <if test="lastPathogenResult != null and lastPathogenResult != ''">
            and i.lv_pathogen_res_type = #{lastPathogenResult}
        </if>
    </sql>

    <sql id="endemicAreaChoose">
        <choose>
            <when test="addressType == 'livingAddress'">
                <include refid="endemicLivingAreaChoose"/>
            </when>
            <otherwise>
                <include refid="endemicOrgAreaChoose"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="endemicLivingAreaChoose">
        <if test="areaLevel == 1">
            2 as areaLevel,
            fi_syn_living_addr_city_code as areaCode,
            fi_syn_living_addr_city as areaName

        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            3 as areaLevel,
            fi_syn_living_addr_func_district_code as areaCode,
            fi_syn_living_addr_func_district as areaName
        </if>
    </sql>

    <sql id="endemicOrgAreaChoose">
        <if test="areaLevel == 1">
            2 as areaLevel,
            fi_syn_org_addr_city_code as areaCode,
            fi_syn_org_addr_city as areaName

        </if>
        <if test="areaLevel == 2 or areaLevel == 3">
            3 as areaLevel,
            fi_syn_org_addr_func_district_code as areaCode,
            fi_syn_org_addr_func_district as areaName
        </if>
    </sql>

    <sql id="endemicAddressMap">
        fi_syn_living_addr_detail as fv_living_addr_detail ,
        fi_syn_living_addr_detail_std as fv_living_addr_detail_std,
        fi_syn_living_addr_street_longitude as fv_living_addr_street_longitude ,
        fi_syn_living_addr_street_latitude as  fv_living_addr_street_latitude ,
        fi_syn_company  as fv_company ,
        fi_syn_company_addr_detail_std as fv_company_addr_detail_std ,
        fi_syn_company_longitude  as fv_company_longitude ,
        fi_syn_company_latitude  as fv_company_latitude ,
        fi_syn_org_name  as fv_org_name ,
        fi_syn_org_longitude  as fv_org_longitude ,
        fi_syn_org_latitude  as fv_org_latitude ,
        lv_living_addr_detail ,
        lv_living_addr_detail_std,
        lv_living_addr_longitude ,
        lv_living_addr_latitude ,
        lv_company ,
        lv_company_addr_detail_std ,
        lv_company_longitude ,
        lv_company_latitude ,
        lv_org_name ,
        lv_org_longitude ,
        lv_org_latitude
    </sql>

    <sql id="endemicSimpleInfoMap">
        event_json,
        empi_id as patient_id,
        syndrome_process_id as id,
        li_syn_syndrome_out_flag as hasExcluded,
        li_syn_syndrome_severe_flag as severe,
        lo_outcome_status as outComeStatus,
        lo_outcome_time as outComeTime,
        fi_syn_onset_time as onsetTime,
        fi_syn_visit_time  as visitTime,
        fi_syn_visit_day as visitDay,
        fi_syn_syndrome_out_time as excludedTime,
        fi_syn_syndrome_severe_time as severeTime,
        lv_living_addr_detail_std as livingAddrStd,
        fi_syn_company as company,
        patient_sex_name as patientSexName,
        fi_syn_patient_age  as patientAge,
        li_syn_syndrome_name as diseaseName,
        li_syn_syndrome_code as diseaseCode,
        fi_syn_identify_time as identifyTime,
        lv_symptom_attent_json as symptomAttentExtrJson,
        fi_syn_job as job,
        fi_inf_infect_json as infectJson,
        fi_syn_org_name as fvOrgName,
        fi_syn_main_diag as mainDiag,
        fi_pathogen_res_type,
        lv_pathogen_res_type,
        fi_syn_visit_time as fiVisitTime,
        fi_syn_org_name   as fiOrgName,
        lv_visit_time     as lvVisitTime,
        lv_org_name       as lv_org_name,
        lo_dead_reason as deathReason,
        lo_dead_time as deathTime
    </sql>

    <sql id="endemicOrder">
        order by
        <choose>
            <when test="property == 'firstIdentifyTime'">
                fi_syn_identify_time
            </when>
            <when test="property == 'onsetTime'">
                fi_syn_onset_time
            </when>
            <when test="property == 'fiIdentifyTime'">
                fi_syn_identify_time
            </when>
            <when test="property == 'patientAge'">
                fi_syn_patient_age
            </when>
            <when test="property == 'mainDiag'">
                fi_syn_main_diag_std_code
            </when>
            <when test="property == 'livingAddrStd'">
                lv_living_addr_detail_std
            </when>
            <when test="property == 'company'">
                fi_syn_company
            </when>
            <when test="property == 'visitOrg'">
                fi_syn_org_id
            </when>
            <when test="property == 'cityCode'">
                <choose>
                    <when test="addressType == 'livingAddress'">
                        fi_syn_living_addr_city_code
                    </when>
                    <otherwise>
                        fi_syn_org_addr_city_code
                    </otherwise>
                </choose>
            </when>
            <when test="property == 'districtCode'">
                <choose>
                    <when test="addressType == 'livingAddress'">
                        fi_syn_living_addr_func_district_code
                    </when>
                    <otherwise>
                        fi_syn_org_addr_func_district_code
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                fi_syn_visit_time
            </otherwise>
        </choose>
        <if test="direction == 'desc' or direction == 'asc'">
            ${direction}
        </if>
    </sql>

    <sql id="endemicAreaChooseCriteria">
        <choose>
            <when test="addressType == 'livingAddress'">
                <include refid="endemicLivingAddressCriteria"/>
            </when>
            <otherwise>
                <include refid="endemicOrgAddressCriteria"/>
            </otherwise>
        </choose>
    </sql>

    <sql id="endemicLivingAddressCriteria">
        <if test="provinceCode != null and provinceCode != ''">
            and fi_syn_living_addr_province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and fi_syn_living_addr_city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and fi_syn_living_addr_func_district_code = #{districtCode}
        </if>
        <if test="streetCode != null and streetCode != ''">
            and fi_syn_living_addr_street_code = #{streetCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size()>0">
            and fi_syn_living_addr_province_code in
            <foreach collection="provinceCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size()>0">
            and fi_syn_living_addr_city_code in
            <foreach collection="cityCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size()>0">
            and fi_syn_living_addr_func_district_code in
            <foreach collection="districtCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </sql>


</mapper>