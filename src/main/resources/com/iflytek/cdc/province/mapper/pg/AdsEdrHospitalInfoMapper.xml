<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.province.mapper.pg.AdsEdrHospitalInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.province.entity.ads.AdsEdrHospitalInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_edr_hospital_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="empi_id" jdbcType="VARCHAR" property="empiId" />
    <result column="admission_date" jdbcType="TIMESTAMP" property="admissionDate" />
    <result column="admission_diagnosis_code" jdbcType="VARCHAR" property="admissionDiagnosisCode" />
    <result column="admission_diagnosis" jdbcType="VARCHAR" property="admissionDiagnosis" />
    <result column="discharge_date" jdbcType="TIMESTAMP" property="dischargeDate" />
    <result column="discharge_diagnosis_code" jdbcType="VARCHAR" property="dischargeDiagnosisCode" />
    <result column="discharge_diagnosis" jdbcType="VARCHAR" property="dischargeDiagnosis" />
    <result column="disease_progression_code" jdbcType="VARCHAR" property="diseaseProgressionCode" />
    <result column="disease_progression" jdbcType="VARCHAR" property="diseaseProgression" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="mpi_id" jdbcType="VARCHAR" property="mpiId" />
    <result column="create_org" jdbcType="VARCHAR" property="createOrg" />
    <result column="create_org_name" jdbcType="VARCHAR" property="createOrgName" />
    <result column="update_org" jdbcType="VARCHAR" property="updateOrg" />
    <result column="update_org_name" jdbcType="VARCHAR" property="updateOrgName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, etl_create_datetime, etl_update_datetime, event_id, empi_id, admission_date, 
    admission_diagnosis_code, admission_diagnosis, discharge_date, discharge_diagnosis_code, 
    discharge_diagnosis, disease_progression_code, disease_progression, create_user, 
    create_time, update_user, update_time, source_id, mpi_id, create_org, create_org_name, 
    update_org, update_org_name
  </sql>

  <select id="getHospitalByEventId" parameterType="string"
          resultType="com.iflytek.cdc.province.model.vo.edrcase.AdsEdrHospitalInfoVO">
    select
    id,
    etl_create_datetime,
    etl_update_datetime,
    event_id,
    empi_id,
    admission_date,
    admission_diagnosis_code,
    admission_diagnosis,
    discharge_date,
    discharge_diagnosis_code,
    discharge_diagnosis,
    disease_progression_code,
    disease_progression
    from ads.ads_edr_hospital_info
    where event_id = #{eventId}
    order by admission_date desc
  </select>
</mapper>