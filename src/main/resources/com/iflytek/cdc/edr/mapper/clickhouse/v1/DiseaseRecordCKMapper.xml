<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.clickhouse.v1.DiseaseRecordCKMapper">

    <select id="getInspectionDetail" resultType="com.iflytek.cdc.edr.dto.inspection.InspectionDetail">
        SELECT
        CASE WHEN data_src = '2' THEN '门诊' WHEN data_src = '3' THEN '住院' END AS visit_type,
        formatDateTime(toDateTime(visit_time), '%Y-%m-%d') as visit_time,
        business_id,
        patient_name,
        sex_name AS sex,
        concat(age, age_unit) AS age,
        inspect_item_code,
        formatDateTime(toDateTime(result_datetime), '%Y-%m-%d') as result_datetime,
        inspect_item_name,
        result,
        exam_result_unit,
        refrange AS ref_range,
        abnormal,
        exam_item_name,
        inspect_id,
        specimen_name,
        formatDateTime(toDateTime(submi_datetime), '%Y-%m-%d') AS submit_datetime,
        dept_name,
        CASE WHEN is_exist_report_card = '1' THEN '是' ELSE '否' END AS is_exist_report_card
        FROM app_ch_inspection_report t
        WHERE t.result_datetime >= #{startDate}::timestamp
        AND t.result_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
        <if test="reportOrgCode != null">
            AND org_id = #{reportOrgCode}
        </if>
        <if test="patientName != null and patientName != ''">
            AND patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND SUBSTR(source_id, 1, 3) = #{sourceType}
        </if>
        <if test="patientSourceCode != null and patientSourceCode != ''">
            AND data_src = #{patientSourceCode}
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="abnormal != null and abnormal != ''">
            AND abnormal = #{abnormal}
        </if>
        <if test="inspectItemList != null and inspectItemList.size() > 0">
            <foreach collection="inspectItemList" index="index" item="insItem" open=" AND (" separator=" OR " close=")">
                inspect_item_name = #{insItem}
            </foreach>
        </if>
        ORDER BY coalesce(t.visit_time, toDateTime('1900-01-01')::timestamp)
        LIMIT ${pageSize} OFFSET (${pageIndex} - 1) * ${pageSize}
    </select>

    <select id="getInspectionDetailByLis" resultType="com.iflytek.cdc.edr.vo.LisInfoVO">
        SELECT t.inspect_id as hisInspectId,
        t.org_name as orgName,
        case when t.data_src = '2' THEN '门诊' WHEN data_src = '3' THEN '住院' END AS visitTypeName,
        t.data_src as visitTypeCode,
        formatDateTime(toDateTime(t.visit_time), '%Y-%m-%d') as visitTime,
        t.business_id as serialNo,
        t.patient_name as patientName,
        t.sex_name AS patientSexDesc,
        concat(t.age, t.age_unit) AS patientAge,
        formatDateTime(toDateTime(t.result_datetime), '%Y-%m-%d') as detectTime,
        t.inspect_item_code as itemCode,
        t.inspect_item_name as itemName,
        t.refrange AS referenceValue,
        t.abnormal as itemResVal,
        CONCAT(t.result, t.exam_result_unit) as itemResNum,
        t.disease_name as diseaseName,
        t.is_exist_report_card as isExistReportCard,
        t.recent_report_card_datetime as reportCardDatetime,
        t.event_id as medicalId,
        t.exam_item_name as inspectName,
        t.report_card_type AS reportCardType,
        t.report_card_type AS reportCardTypeName
        FROM ads_ch_inspection_report t
        WHERE result_datetime >= #{startDate}::timestamp
        AND result_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
        <choose>
            <when test="reportOrgCode != null and reportOrgCode != '' ">
                AND org_id = #{reportOrgCode}
            </when>
            <otherwise>
                and org_id in (
                    select org_id
                    from dim_organization_info o
                    where o.delete_flag = 'N'
                    <if test="provinceCode!= null and provinceCode != ''">
                        and o.province_code = #{provinceCode}
                    </if>
                    <if test="cityCode!= null and cityCode != ''">
                        and o.city_code = #{cityCode}
                    </if>
                    <if test="districtCode!= null and districtCode != ''">
                        and o.district_code = #{districtCode}
                    </if>
                    <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
                        and o.province_code in
                        <foreach item="item" index="index" collection="provinceCodes"
                                 open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="cityCodes != null and cityCodes.size() &gt; 0">
                        and o.city_code in
                        <foreach item="item" index="index" collection="cityCodes"
                                 open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="districtCodes != null and districtCodes.size() &gt; 0">
                        and o.district_code in
                        <foreach item="item" index="index" collection="districtCodes"
                                 open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                )
            </otherwise>
        </choose>
        <if test="patientName != null and patientName != ''">
            AND patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND SUBSTR(source_id, 1, 3) = #{sourceType}
        </if>
        <if test="patientSourceCode != null and patientSourceCode != ''">
            AND data_src = #{patientSourceCode}
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
            <if test="isExistReportCard == 1 and reportCardTypeList != null and reportCardTypeList.size() > 0">
                <foreach collection="reportCardTypeList" item="reportCardType" index="index" open=" AND (" separator=" OR " close=")">
                    report_card_type LIKE CONCAT('%', #{reportCardType}, '%')
                </foreach>
            </if>
        </if>
        <if test="abnormal != null and abnormal != ''">
            AND abnormal = #{abnormal}
        </if>
        <if test="inspectItemList != null and inspectItemList.size() > 0">
            <foreach collection="inspectItemList" index="index" item="insItem" open=" AND (" separator=" OR " close=")">
                inspect_item_name = #{insItem}
            </foreach>
        </if>
        <if test="diseaseCode != null and diseaseCode.size() &gt; 0">
            <foreach collection="diseaseCode" index="index" item="disease" open=" AND (" separator=" OR " close=")">
                disease_code = #{disease}
            </foreach>
        </if>
        <if test="customQueryParamList != null and customQueryParamList.size() > 0">
            AND (
            <trim prefixOverrides="AND|OR">
                <foreach collection="customQueryParamList" item="queryParam" index="index">
                    <choose>
                        <when test="queryParam.logicalType == 'and'"> AND </when>
                        <when test="queryParam.logicalType == 'or'"> OR </when>
                    </choose>
                    <choose>
                        <when test="queryParam.paramName == 'age'">
                            <choose>
                                <when test="queryParam.compareType == 'eq'"> age = #{queryParam.paramValue} </when>
                                <when test="queryParam.compareType == 'ne'"> (age != #{queryParam.paramValue} OR age IS NULL) </when>
                                <when test="queryParam.compareType == 'gt'"> (CASE WHEN match(age, '^[0-9\.]+$') THEN toInt32(age) END) <![CDATA[ > ]]> toInt32(#{queryParam.paramValue}) </when>
                                <when test="queryParam.compareType == 'lt'"> (CASE WHEN match(age, '^[0-9\.]+$') THEN toInt32(age) END) <![CDATA[ < ]]> toInt32(#{queryParam.paramValue}) </when>
                            </choose>
                            <if test="queryParam.ageUnit != null and queryParam.ageUnit != ''">
                                AND age_unit = #{queryParam.ageUnit}
                            </if>
                        </when>
                        <when test="queryParam.paramName == 'item'">
                            <choose>
                                <when test="queryParam.compareType == 'eq'"> inspect_item_name = #{queryParam.paramValue} </when>
                                <when test="queryParam.compareType == 'ne'"> (inspect_item_name != #{queryParam.paramValue} OR inspect_item_name IS NULL) </when>
                                <when test="queryParam.compareType == 'co'"> inspect_item_name LIKE CONCAT('%', #{queryParam.paramValue} ,'%') </when>
                                <when test="queryParam.compareType == 'nc'"> (inspect_item_name NOT LIKE CONCAT('%', #{queryParam.paramValue}, '%') OR inspect_item_name IS NULL) </when>
                            </choose>
                            <if test="queryParam.resultValue != null and queryParam.resultValue != ''">
                                <choose>
                                    <when test="queryParam.resultCompareType == 'eq'"> AND result = #{queryParam.resultValue} </when>
                                    <when test="queryParam.resultCompareType == 'ne'"> AND (result != #{queryParam.resultValue} OR result IS NULL) </when>
                                    <when test="queryParam.resultCompareType == 'gt'"> AND (CASE WHEN match(result, '^[0-9\.]+$') THEN toFloat64(result) END) <![CDATA[ > ]]> toFloat64(#{queryParam.resultValue}) </when>
                                    <when test="queryParam.resultCompareType == 'lt'"> AND (CASE WHEN match(result, '^[0-9\.]+$') THEN toFloat64(result) END) <![CDATA[ < ]]> toFloat64(#{queryParam.resultValue}) </when>
                                    <when test="queryParam.resultCompareType == 'co'"> AND result LIKE CONCAT('%', #{queryParam.resultValue} ,'%') </when>
                                    <when test="queryParam.resultCompareType == 'nc'"> AND (result NOT LIKE CONCAT('%', #{queryParam.resultValue}, '%') OR result IS NULL) </when>
                                </choose>
                            </if>
                        </when>
                        <when test="queryParam.paramName == 'disease'">
                            <choose>
                                <when test="queryParam.compareType == 'eq'"> disease_code = #{queryParam.paramValue} </when>
                                <when test="queryParam.compareType == 'ne'"> (disease_code != #{queryParam.paramValue} OR disease_code IS NULL)</when>
                                <when test="queryParam.compareType == 'co'"> disease_code LIKE CONCAT(#{queryParam.paramValue}, '%') </when>
                                <when test="queryParam.compareType == 'nc'"> (disease_code NOT LIKE CONCAT(#{queryParam.paramValue}, '%') OR disease_code IS NULL)</when>
                            </choose>
                        </when>
                    </choose>
                </foreach>
            </trim>
            )
        </if>
        ORDER BY coalesce(t.visit_time, toDateTime('1900-01-01')::timestamp) DESC
    </select>

</mapper>