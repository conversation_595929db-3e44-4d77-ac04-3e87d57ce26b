<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.AdsMsRepcardBasicDMapper">

  <select id="getList" resultType="com.iflytek.cdc.edr.dto.repcard.ReportCardStatisticData">
    with  tmp as(
    select
    t.org_id orgId,
    COALESCE(sum(coalesce(t.reported_card_cnt,'0')::int),0) as reportedCardCnt ,
    COALESCE(sum(coalesce(t.wait_card_cnt, '0')::int),0) as waitCardCnt ,
    COALESCE(sum(coalesce(t.audit_card_cnt, '0')::int),0) as auditCardCnt ,
    COALESCE(sum(coalesce(t.nosubmitted_card_cnt, '0')::int),0) as nosubmittedCardCnt ,
    COALESCE(sum(coalesce(t.overrule_card_cnt, '0')::int),0) as overruleCardCnt,
    COALESCE(sum(coalesce(t.exclude_card_cnt, '0')::int),0) as excludeCardCnt
    from   ads.v_ms_repcard_basic_d t
    where 1= 1
    <if test="startDate != null and endDate != null">
      and t.day between #{startDate} and #{endDate}
    </if>
    <if test="cardType!= null and cardType != ''">
      and t.repcard_class = #{cardType}
    </if>
    <if test="patientType!= null and patientType != ''">
      and t.visit_type_code = #{patientType}
    </if>
    <if test="infectedCodeList != null and infectedCodeList.size() &gt; 0">
      and disease_code in
      <foreach item="item" index="index" collection="infectedCodeList"
               open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
<!--    <if test="infectedCode!= null and infectedCode != ''">-->
<!--      and t.disease_code = #{infectedCode}-->
<!--    </if>-->
<!--    <if test="infectedTypeCode!= null and infectedTypeCode != ''">-->
<!--      and t.infect_type_code = #{infectedTypeCode}-->
<!--    </if>-->
    <if test="orgTypeCode!= null and orgTypeCode != ''">
      and t.source_type_code = #{orgTypeCode}
    </if>
    <if test="provinceCode!= null and provinceCode != ''">
      and t.province_code = #{provinceCode}
    </if>
    <if test="cityCode!= null and cityCode != ''">
      and t.city_code = #{cityCode}
    </if>
    <if test="districtCode!= null and districtCode != ''">
      and t.district_code = #{districtCode}
    </if>
    <if test="orgId!= null and orgId != ''">
      and t.org_id = #{orgId}
    </if>
    <if test="orgIds != null and orgIds.size() &gt; 0">
      and t.org_id in
      <foreach item="item" index="index" collection="orgIds"
               open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
        group by t.org_id
      )  select
    o.org_id orgId,
    o.org_Name orgName,
    COALESCE(tmp.reportedCardCnt,0) as reportedCardCnt ,
    COALESCE(tmp.waitCardCnt,0) as waitCardCnt ,
    COALESCE(tmp.auditCardCnt,0) as auditCardCnt ,
    COALESCE(tmp.nosubmittedCardCnt,0) as nosubmittedCardCnt ,
    COALESCE(tmp.overruleCardCnt,0) as overruleCardCnt,
    COALESCE(tmp.excludeCardCnt,0) as excludeCardCnt
    from  dim.dim_organization_info  o  left join tmp  on o.org_id = tmp.orgId
    where o.is_online = '1'
    <if test="orgId!= null and orgId != ''">
      and o.org_id = #{orgId}
    </if>
    <if test="orgTypeCode!= null and orgTypeCode != ''">
      and o.source_type = #{orgTypeCode}
    </if>
    <if test="orgIds != null and orgIds.size() &gt; 0">
      and o.org_id in
      <foreach item="item" index="index" collection="orgIds"
               open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    order by reportedCardCnt desc,waitCardCnt desc,auditCardCnt desc,nosubmittedCardCnt desc,overruleCardCnt desc,excludeCardCnt desc,o.district_code,o.org_name


  </select>
  <select id="getTotal" resultType="com.iflytek.cdc.edr.dto.repcard.ReportCardStatistic">
    select
    COALESCE(sum(coalesce(reported_card_cnt,'0')::int),0) as reportedCardCnt ,
    COALESCE(sum(coalesce(wait_card_cnt, '0')::int),0) as waitCardCnt ,
    COALESCE(sum(coalesce(audit_card_cnt, '0')::int),0) as auditCardCnt ,
    COALESCE(sum(coalesce(nosubmitted_card_cnt, '0')::int),0) as nosubmittedCardCnt ,
    COALESCE(sum(coalesce(overrule_card_cnt, '0')::int),0) as overruleCardCnt,
    COALESCE(sum(coalesce(exclude_card_cnt, '0')::int),0) as excludeCardCnt
    from  ads.v_ms_repcard_basic_d
    where 1=1
    <if test="startDate != null and endDate != null">
      and day between #{startDate} and #{endDate}
    </if>
    <if test="cardType!= null and cardType != ''">
      and repcard_class = #{cardType}
    </if>
    <if test="patientType!= null and patientType != ''">
      and visit_type_code = #{patientType}
    </if>
    <if test="infectedCodeList != null and infectedCodeList.size() &gt; 0">
      and disease_code in
      <foreach item="item" index="index" collection="infectedCodeList"
               open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
<!--    <if test="infectedCode!= null and infectedCode != ''">-->
<!--      and disease_code = #{infectedCode}-->
<!--    </if>-->
<!--    <if test="infectedTypeCode!= null and infectedTypeCode != ''">-->
<!--      and infect_type_code = #{infectedTypeCode}-->
<!--    </if>-->
    <if test="orgTypeCode!= null and orgTypeCode != ''">
      and source_type_code = #{orgTypeCode}
    </if>
    <if test="provinceCode!= null and provinceCode != ''">
      and province_code = #{provinceCode}
    </if>
    <if test="cityCode!= null and cityCode != ''">
      and city_code = #{cityCode}
    </if>
    <if test="districtCode!= null and districtCode != ''">
      and district_code = #{districtCode}
    </if>
    <if test="orgId!= null and orgId != ''">
      and org_id = #{orgId}
    </if>
    <if test="orgIds != null and orgIds.size() &gt; 0">
      and org_id in
      <foreach item="item" index="index" collection="orgIds"
               open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="getStatTotal" resultType="com.iflytek.cdc.edr.dto.repcard.ReportCardStatistic">
    select
    COALESCE(sum(coalesce(card_cnt,'0')::int),0) as totalReportCard ,
    COALESCE(sum(coalesce(report_card_cnt, '0')::int),0) as shouldCommitReportCard ,
    COALESCE(sum(coalesce(actual_card_cnt, '0')::int),0) as realCommitReportCard ,
    COALESCE(sum(coalesce(timely_card_cnt, '0')::int),0) as inTimeReportCard ,
    COALESCE(sum(coalesce(omit_card_cnt, '0')::int),0) as omissionReportCard
    from  ads.ads_ms_repcard_basic_d
    where 1=1
    <if test="startDate != null and endDate != null">
      and day between #{startDate} and #{endDate}
    </if>
    <if test="cardType!= null and cardType != ''">
      and repcard_class = #{cardType}
    </if>
    <if test="patientType!= null and patientType != ''">
      and visit_type_code = #{patientType}
    </if>
    <if test="infectedCodeList != null and infectedCodeList.size() &gt; 0">
      and disease_code in
      <foreach item="item" index="index" collection="infectedCodeList"
               open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
<!--    <if test="infectedCode!= null and infectedCode != ''">-->
<!--      and disease_code = #{infectedCode}-->
<!--    </if>-->
<!--    <if test="infectedTypeCode!= null and infectedTypeCode != ''">-->
<!--      and infect_type_code = #{infectedTypeCode}-->
<!--    </if>-->
    <if test="orgTypeCode!= null and orgTypeCode != ''">
      and source_type_code = #{orgTypeCode}
    </if>
    <if test="provinceCode!= null and provinceCode != ''">
      and province_code = #{provinceCode}
    </if>
    <if test="cityCode!= null and cityCode != ''">
      and city_code = #{cityCode}
    </if>
    <if test="districtCode!= null and districtCode != ''">
      and district_code = #{districtCode}
    </if>
    <if test="orgId!= null and orgId != ''">
      and org_id = #{orgId}
    </if>
    <if test="orgIds != null and orgIds.size() &gt; 0">
      and org_id in
      <foreach item="item" index="index" collection="orgIds"
               open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>
</mapper>