<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v1.VCdcrpEpidemicReportMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.edr.entity.VCdcrpEpidemicReport">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="situation_description" jdbcType="VARCHAR" property="situationDescription"/>
        <result column="infected_number" jdbcType="INTEGER" property="infectedNumber"/>
        <result column="dead_number" jdbcType="INTEGER" property="deadNumber"/>
        <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updator" jdbcType="VARCHAR" property="updator"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="clinical_symptoms" jdbcType="VARCHAR" property="clinicalSymptoms"/>
        <result column="epidemic_report_theme" jdbcType="VARCHAR" property="epidemicReportTheme"/>
        <result column="epidemic_report_number" jdbcType="VARCHAR" property="epidemicReportNumber"/>
        <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
        <result column="org_code" jdbcType="VARCHAR" property="orgCode"/>
        <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
        <result column="area_province_code" jdbcType="VARCHAR" property="areaProvinceCode"/>
        <result column="area_province_name" jdbcType="VARCHAR" property="areaProvinceName"/>
        <result column="area_city_code" jdbcType="VARCHAR" property="areaCityCode"/>
        <result column="area_city_name" jdbcType="VARCHAR" property="areaCityName"/>
        <result column="area_county_code" jdbcType="VARCHAR" property="areaCountyCode"/>
        <result column="area_county_name" jdbcType="VARCHAR" property="areaCountyName"/>
        <result column="mapping_id" jdbcType="VARCHAR" property="mappingId"/>
        <result column="report_business_id" jdbcType="VARCHAR" property="reportBusinessId"/>
        <result column="mapping_deleted" jdbcType="VARCHAR" property="mappingDeleted"/>
        <result column="report_source" jdbcType="VARCHAR" property="reportSource"/>
        <result column="report_doctor" jdbcType="VARCHAR" property="reportDoctor"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="medical_id" jdbcType="VARCHAR" property="medicalId"/>
        <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime"/>
        <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime"/>
        <result column="patient_name" jdbcType="VARCHAR" property="patientName"/>
        <result column="sex" jdbcType="VARCHAR" property="sex"/>
        <result column="age" jdbcType="VARCHAR" property="age"/>
        <result column="diagnose_info" jdbcType="VARCHAR" property="diagnoseInfo"/>
        <result column="exam_info" jdbcType="VARCHAR" property="examInfo"/>
        <result column="lis_info" jdbcType="VARCHAR" property="lisInfo"/>
        <result column="clte_info" jdbcType="VARCHAR" property="clteInfo"/>
    </resultMap>
    <select id="selectEventList" resultType="com.iflytek.cdc.edr.dto.coordination.ReportEventListVo">
        select id as id,report_doctor as doctorName,create_datetime as registerDatetime,*
        from
        (select id,org_name,org_id,epidemic_report_theme
        ,epidemic_report_number,clinical_symptoms,infected_Number
        ,dead_Number,create_datetime,situation_description
        ,area_province_code,area_city_code,area_county_code,area_province_name,area_city_name,area_county_name,report_doctor,status
        from
        ads.v_cdcrp_epidemic_report where is_deleted = '0' group by id,org_name,org_id,epidemic_report_theme
        ,epidemic_report_number,clinical_symptoms,infected_Number,dead_Number,create_datetime,situation_description
        ,area_province_code,area_city_code,area_county_code,area_province_name,area_city_name,area_county_name,report_doctor,status)
        as
        theme
        where 1=1
        <if test="regionInfoList!=null">
            and
            <foreach close=")" collection="regionInfoList" index="index" item="item" open="(  " separator=" or ">
                (1=1
                <if test="item.provinceCode != null and item.provinceCode != '' ">
                    and theme.area_province_code = #{item.provinceCode}
                </if>
                <if test="item.cityCode != null and item.cityCode != '' ">
                    and theme.area_city_code = #{item.cityCode}
                </if>
                <if test="item.districtCode != null and item.districtCode != '' ">
                    and theme.area_county_code = #{item.districtCode}
                </if>
                )
            </foreach>
        </if>
        <if test="epidemicReportTheme != null and epidemicReportTheme != ''">
            and theme.epidemic_report_theme like concat('%',#{epidemicReportTheme},'%')
        </if>
        <if test="orgId != null and orgId != ''">
            and theme.org_id = #{orgId}
        </if>
        <if test="startTime != null and endTime != null">
            and theme.create_datetime between #{startTime} and #{endTime}
        </if>
        order by theme.create_datetime desc
    </select>
    <select id="selectDetailList" resultType="com.iflytek.cdc.edr.dto.coordination.ReportDetailVo">
        select id ,patient_name as patientName,sex as sexName,age as ageDesc ,diagnose_info as localDiagnosticName,
        exam_info as checkInfo,lis_info as inspectionInfo ,clte_info as microbeInfo,status
        from ads.v_cdcrp_epidemic_report
        where is_deleted = '0'
        <if test="id != null">
            and id = #{id}
        </if>
        order by create_datetime desc ,id
    </select>

</mapper>