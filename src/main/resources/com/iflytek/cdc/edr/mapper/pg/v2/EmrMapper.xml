<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.EmrMapper">
    <select id="getEmrList" resultType="com.iflytek.cdc.edr.demo.EmrInfo">
        SELECT patient_name,
               sex_name,
               patient_id_number,
               event_datetime,
               action_in_chief,
               current_addr_detail,
               company_name,
               diagnose_name
          FROM dwd.dwd_ch_medical_record t
        <where>
            <if test="patientName != null and patientName != ''">
                t.patient_name = #{patientName}
            </if>
            <if test="sexCode != null and sexCode != ''">
                AND t.sex_code = #{sexCode}
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                AND t.patient_id_number = #{patientIdNumber}
            </if>
            <if test="startTime != null">
                AND t.event_datetime >= #{startTime}::TIMESTAMP
            </if>
            <if test="endTime != null">
                AND t.event_datetime &lt;= #{endTime}::TIMESTAMP
            </if>
            <if test="currentAddrDetail != null and currentAddrDetail != ''">
                AND t.current_addr_detail LIKE concat('%', #{currentAddrDetail, jdbcType=VARCHAR},'%')
            </if>
            <if test="actionInChief != null and actionInChief != ''">
                AND t.action_in_chief LIKE concat('%', #{actionInChief, jdbcType=VARCHAR},'%')
            </if>
            <if test="companyName != null and companyName != ''">
                AND t.company_name LIKE concat('%', #{companyName, jdbcType=VARCHAR},'%')
            </if>
        </where>
        <if test="topN > 0">
            LIMIT ${topN}
        </if>
    </select>

    <select id="getNewEmrList" resultType="com.iflytek.cdc.edr.demo.EmrInfo">
        SELECT patient_name,
        patient_sex_desc as sex_name,
        identity_number as patient_id_number,
        event_datetime,
        action_in_chief,
        now_addr as current_addr_detail,
        company_name,
        diagnose_type_name as diagnose_name
        FROM ads.ads_outpat_medical_records t
        <where>
            <if test="patientName != null and patientName != ''">
                t.patient_name = #{patientName}
            </if>
            <if test="sexCode != null and sexCode != ''">
                AND t.patient_sex_code = #{sexCode}
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                AND t.identity_number = #{patientIdNumber}
            </if>
            <if test="startTime != null">
                AND t.event_datetime >= #{startTime}::TIMESTAMP
            </if>
            <if test="endTime != null">
                AND t.event_datetime &lt;= #{endTime}::TIMESTAMP
            </if>
            <if test="currentAddrDetail != null and currentAddrDetail != ''">
                AND t.now_addr LIKE concat('%', #{currentAddrDetail, jdbcType=VARCHAR},'%')
            </if>
            <if test="actionInChief != null and actionInChief != ''">
                AND t.action_in_chief LIKE concat('%', #{actionInChief, jdbcType=VARCHAR},'%')
            </if>
            <if test="companyName != null and companyName != ''">
                AND t.company_name LIKE concat('%', #{companyName, jdbcType=VARCHAR},'%')
            </if>
        </where>
        <if test="topN > 0">
            LIMIT ${topN}
        </if>
    </select>
</mapper>