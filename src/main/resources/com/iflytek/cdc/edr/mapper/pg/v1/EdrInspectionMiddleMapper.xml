<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v1.EdrInspectionMiddleMapper">

    <resultMap type="com.iflytek.cdc.edr.entity.outpatient.EdrInspectionMiddle" id="EdrInspectionMiddleResult">
        <result property="id" column="id"/>
        <result property="eventId" column="event_id"/>
        <result property="businessId" column="business_id"/>
        <result property="hisInspectId" column="his_inspect_id"/>
        <result property="examItemName" column="exam_item_name"/>
        <result property="sentDeptName" column="sent_dept_name"/>
        <result property="sentDeptCode" column="sent_dept_code"/>
        <result property="specimenAcceptDatetime" column="specimen_accept_datetime"/>
        <result property="inspectId" column="inspect_id"/>
        <result property="inspectName" column="inspect_name"/>
        <result property="result" column="result"/>
        <result property="examResultUnit" column="exam_result_unit"/>
        <result property="refrange" column="refrange"/>
        <result property="abnormal" column="abnormal"/>
    </resultMap>

    <sql id="selectEdrInspectionMiddleVo">
        select t0.id,
               t0.event_id,
               t0.business_id,
               t0.inspect_id                 his_inspect_id,
               t0.exam_item_name,
               t0.report_dept_name           sent_dept_name,
               t0.report_dept_code           sent_dept_code,
               t0.specimen_sampling_datetime specimen_accept_datetime, -- 采样日期
               t0.inspect_id,
               t0.inspect_item_name          inspect_name,
               t0.result,
               t0.exam_result_unit,
               t0.refrange,
               t0.abnormal
        from ads.ads_ch_inspection_report t0
    </sql>
    <select id="selectEdrInspectionMiddleByEventId" parameterType="String" resultMap="EdrInspectionMiddleResult">
        <include refid="selectEdrInspectionMiddleVo"/>
        where t0.event_id = #{eventId}
        order by t0.specimen_sampling_datetime DESC
    </select>

    <select id="selectEdrInspectionMiddleList" parameterType="com.iflytek.cdc.edr.entity.outpatient.EdrInspectionMiddle"
            resultMap="EdrInspectionMiddleResult">
        <include refid="selectEdrInspectionMiddleVo"/>
        <where>
            <if test="eventId != null  and eventId != ''">and t1.event_id = #{eventId}</if>
            <if test="businessId != null  and businessId != ''">and business_id = #{businessId}</if>
            <if test="hisInspectId != null  and hisInspectId != ''">and his_inspect_id = #{hisInspectId}</if>
            <if test="examItemName != null  and examItemName != ''">and exam_item_name like concat('%', #{examItemName},
                '%')
            </if>
            <if test="sentDeptName != null  and sentDeptName != ''">and sent_dept_name like concat('%', #{sentDeptName},
                '%')
            </if>
            <if test="sentDeptCode != null  and sentDeptCode != ''">and sent_dept_code = #{sentDeptCode}</if>
            <if test="specimenAcceptDatetime != null ">and specimen_accept_datetime = #{specimenAcceptDatetime}</if>
            <if test="inspectId != null  and inspectId != ''">and inspect_id = #{inspectId}</if>
            <if test="inspectName != null  and inspectName != ''">and inspect_name like concat('%', #{inspectName},
                '%')
            </if>
            <if test="result != null  and result != ''">and result = #{result}</if>
            <if test="examResultUnit != null  and examResultUnit != ''">and exam_result_unit = #{examResultUnit}</if>
            <if test="refrange != null  and refrange != ''">and refrange = #{refrange}</if>
            <if test="abnormal != null  and abnormal != ''">and abnormal = #{abnormal}</if>
        </where>
    </select>

    <select id="selectEdrInspectionMiddleById" parameterType="String" resultMap="EdrInspectionMiddleResult">
        <include refid="selectEdrInspectionMiddleVo"/>
        where id = #{id}
    </select>
    <select id="selectEdrInspectionMiddleByEventIds"
            resultType="com.iflytek.cdc.edr.entity.outpatient.EdrInspectionMiddle">
        <include refid="selectEdrInspectionMiddleVo"/>
        where t0.event_id in
        <foreach collection="eventIds" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

</mapper>
