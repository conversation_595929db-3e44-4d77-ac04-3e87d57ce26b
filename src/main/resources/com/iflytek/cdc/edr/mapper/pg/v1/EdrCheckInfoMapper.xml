<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v1.EdrCheckInfoMapper">
    <resultMap type="com.iflytek.cdc.edr.entity.outpatient.EdrCheckInfo" id="EdrCheckInfoResult">
        <result property="id"    column="id"    />
        <result property="eventId"    column="event_id"    />
        <result property="checkName"    column="check_name"    />
        <result property="applyDeptCode"    column="apply_dept_code"    />
        <result property="applyDeptName"    column="apply_dept_name"    />
        <result property="checkDatetime"    column="check_datetime"    />
        <result property="examConclusion"    column="exam_conclusion"    />
        <result property="checkSituation"    column="check_situation"    />
        <result property="option"    column="option"    />
        <result property="hisCheckId"    column="his_check_id"  />
    </resultMap>

    <sql id="selectEdrCheckInfoVo">
        select
            t0.id,
            t0.event_id ,
            t0.check_name ,
            t0.report_dept_code apply_dept_code,
            t0.report_dept_name apply_dept_name,
            t0.check_datetime,
            t0.exam_conclusion,
            t0.check_situation,
            t0.option,
            t0.his_check_id
        from dwd.dwd_ch_check_report t0
    </sql>

    <select id="selectEdrCheckInfoByEventId" parameterType="String" resultMap="EdrCheckInfoResult">
        <include refid="selectEdrCheckInfoVo"/>
        where t0.event_id = #{eventId}
        order by t0.check_datetime DESC
    </select>

    <select id="selectEdrCheckInfoList" parameterType="com.iflytek.cdc.edr.entity.outpatient.EdrCheckInfo" resultMap="EdrCheckInfoResult">
        <include refid="selectEdrCheckInfoVo"/>
        <where>
            <if test="eventId != null  and eventId != ''"> and t0.event_id = #{eventId}</if>
            <if test="checkName != null  and checkName != ''"> and check_name like concat('%', #{checkName, jdbcType=VARCHAR}, '%')</if>
            <if test="applyDeptCode != null  and applyDeptCode != ''"> and apply_dept_code = #{applyDeptCode}</if>
            <if test="applyDeptName != null  and applyDeptName != ''"> and apply_dept_name like concat('%', #{applyDeptName, jdbcType=VARCHAR}, '%')</if>
            <if test="checkDatetime != null "> and check_datetime = #{checkDatetime}</if>
            <if test="examConclusion != null  and examConclusion != ''"> and exam_conclusion = #{examConclusion}</if>
            <if test="checkSituation != null  and checkSituation != ''"> and check_situation = #{checkSituation}</if>
        </where>
    </select>

    <select id="selectEdrCheckInfoById" parameterType="String" resultMap="EdrCheckInfoResult">
        <include refid="selectEdrCheckInfoVo"/>
        where id = #{id}
    </select>
    <select id="selectEdrCheckInfoByEventIds" resultType="com.iflytek.cdc.edr.entity.outpatient.EdrCheckInfo">
        <include refid="selectEdrCheckInfoVo"/>
        where t0.event_id in
        <foreach collection="eventIds" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

</mapper>
