<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v1.MedicalHistoryLogMapper">

    <resultMap type="com.iflytek.cdc.edr.entity.hismedicallog.MedicalHistoryLog" id="MedicalHistoryLogResult">
        <result property="id"    column="id"    />
        <result property="eventId"    column="event_id"    />
        <result property="patientId"    column="patient_id"    />
        <result property="patientName"    column="patient_name"    />
        <result property="sexName"    column="sex_name"    />
        <result property="orgId"    column="org_id"    />
        <result property="orgCode"    column="org_code"    />
        <result property="orgName"    column="org_name"    />
        <result property="deptCode"    column="dept_code"    />
        <result property="visitDocName"    column="visit_doc_name"    />
        <result property="diagnoseName"    column="diagnose_name"    />
        <result property="visitDatetime"    column="visit_datetime"    />
        <result property="createDatetime"    column="create_datetime"    />
        <result property="dataSrc"    column="data_src"    />
        <result property="sourceType"    column="source_type"    />
        <result property="birthday"    column="birthday"    />
        <result property="diagType"    column="diag_type"    />
        <result property="diagCode"    column="diag_code"    />
        <result property="mainDiagFlag"    column="main_diag_flag"    />
        <result property="deptName"    column="dept_name"    />
    </resultMap>

    <sql id="selectMedicalHistoryLogVo">
        select id, event_id, patient_id, patient_name,
               sex_name, org_id, org_code,
               org_name, dept_code,
               visit_doc_name, diagnose_name,
               visit_datetime, create_datetime,
               data_src, source_type, birthday,
               null diag_type,
               null diag_code,
               null main_diag_flag,
               dept_name from dwd.dwd_ch_medical_history_log
    </sql>

    <select id="selectMedicalHistoryLogList" parameterType="String" resultMap="MedicalHistoryLogResult">
        <include refid="selectMedicalHistoryLogVo"/>
         where   event_id = #{eventId}
         order by create_datetime
    </select>

    <select id="selectMedicalHistoryLogListByEventIds" parameterType="String" resultMap="MedicalHistoryLogResult">
        <include refid="selectMedicalHistoryLogVo"/>
        where event_id in
        <foreach close=")" collection="eventIdLists" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        order by create_datetime
    </select>

    <select id="selectMedicalHistoryLogById" parameterType="String" resultMap="MedicalHistoryLogResult">
        <include refid="selectMedicalHistoryLogVo"/>
        where id = #{id}
        order by create_datetime
    </select>
</mapper>
