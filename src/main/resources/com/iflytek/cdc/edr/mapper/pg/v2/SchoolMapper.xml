<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.SchoolMapper">
    <select id="getHealthRecord" resultType="com.iflytek.cdc.edr.entity.school.HealthRecordBo">
        SELECT doi.org_id,
               doi.org_name,
               doi.org_address,
               doi.org_type_code,
               doi.org_type_name,
               dspi.person_id,
               dspi.identity_no AS patient_id_number,
               dspi.person_name AS patient_name,
               dssr.person_age  AS age,
               dspi.sex_name,
               dspi.telephone,
               dspi.living_address,
               dspi.admin_name,
               dspi.admin_telephone,
               dspi.person_type_name,
               dspi.class_name,
               dspi.habitation,
               dssr.record_type_code,
               dssr.record_type_name,
               dssr.record_datetime,
               dssr.is_health_code,
               dssr.is_health_name,
               dssr.symptom_name_append,
               dssr.temperature,
               dssr.health_code_status_code,
               dssr.health_code_status_name,
               dssr.trip_card_status_code,
               dssr.trip_card_status_name,
               dssr.risk_status,
               dssr.risk_journey_status,
               dssr.risk_touch_status,
               dssr.risk_leave_status
        FROM dwd.dwd_sc_symptom_record dssr
                 INNER JOIN dim.dim_organization_info doi ON doi.org_id = dssr.org_id
                 INNER JOIN dim.dim_sc_person_info dspi ON dspi.person_id = dssr.person_id
        WHERE dssr.id = #{eventId} LIMIT 1
    </select>

    <select id="getHousematesList" resultType="com.iflytek.cdc.edr.entity.school.Housemates">
        SELECT guardian_name AS patient_name,
               identity_type_name,
               identity_no   AS patient_id_number,
               telephone,
               is_cohabit
        FROM dim.dim_sc_guardians_housemates
        WHERE user_id = #{personId}
    </select>

    <select id="getCheckRecord" resultType="com.iflytek.cdc.edr.entity.school.CheckRecordBo">
        SELECT doi.org_id,
               doi.org_name,
               doi.org_address,
               doi.org_type_code,
               doi.org_type_name,
               dspi.identity_no AS      patient_id_number,
               dspi.person_id,
               dspi.person_name AS      patient_name,
               dssr.person_age  AS      age,
               dspi.sex_name,
               dspi.telephone,
               dspi.living_address,
               dspi.admin_name,
               dspi.admin_telephone,
               dspi.person_type_name,
               dspi.class_name,
               dspi.habitation,
               dssr.record_type_code    check_type,
               dssr.record_type_name,
               dssr.is_health_code,
               dssr.record_datetime     check_datetime,
               ''                       examiner,
               dssr.is_absent,
               dssr.absent_reason_name  absent_reason,
               dssr.symptom_name_append symptom_name,
               dssr.other_symptom_desc  other_symptom,
               dssr.accident_date,
               dssr.is_outpatient,
               dssr.outpatient_date,
               dssr.outpatient_hospital,
               dssr.diagnose,
               dssr.risk_status,
               dssr.risk_journey_status,
               dssr.risk_touch_status,
               dssr.risk_leave_status
        FROM dwd.dwd_sc_symptom_record dssr
                 INNER JOIN dim.dim_organization_info doi ON doi.org_id = dssr.org_id
                 INNER JOIN dim.dim_sc_person_info dspi ON dspi.person_id = dssr.person_id
        WHERE id = #{eventId} LIMIT 1
    </select>

    <select id="getCheckRecordsByIds" resultType="com.iflytek.cdc.edr.entity.school.CheckRecordBo">
        SELECT doi.org_id,
        doi.org_name,
        doi.org_address,
        doi.org_type_code,
        doi.org_type_name,
        dspi.identity_no AS patient_id_number,
        dspi.person_id,
        dspi.person_name AS patient_name,
        dssr.person_age AS age,
        dspi.sex_name,
        dspi.telephone,
        dspi.living_address,
        dspi.admin_name,
        dspi.admin_telephone,
        dspi.person_type_name,
        dspi.class_name,
        dspi.habitation,
        dssr.record_type_code check_type,
        dssr.record_type_name,
        dssr.is_health_code,
        dssr.record_datetime check_datetime,
        '' examiner,
        dssr.is_absent,
        dssr.absent_reason_name absent_reason,
        dssr.symptom_name_append symptom_name,
        dssr.other_symptom_desc other_symptom,
        dssr.accident_date,
        dssr.is_outpatient,
        dssr.outpatient_date,
        dssr.outpatient_hospital,
        dssr.diagnose,
        dssr.risk_status,
        dssr.risk_journey_status,
        dssr.risk_touch_status,
        dssr.risk_leave_status
        FROM dwd.dwd_sc_symptom_record dssr
        INNER JOIN dim.dim_organization_info doi ON doi.org_id = dssr.org_id
        INNER JOIN dim.dim_sc_person_info dspi ON dspi.person_id = dssr.person_id
        WHERE dssr.id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>

    </select>
    <select id="getEventInfosByIds" resultType="com.iflytek.cdc.edr.dto.event.EventInfoDto">
        SELECT doi.org_type_code,
               doi.org_type_name,
               doi.org_id,
               doi.org_name,
               doi.org_address,
               ds.record_datetime as eventDatetime,
               ds.id as recordId,
               ds.is_health_name as healthStatus
        FROM dwd.dwd_sc_symptom_record ds
                 INNER JOIN dim.dim_organization_info doi ON doi.org_id = ds.org_id
        WHERE ds.id in --AND der.event_datetime >= CURRENT_DATE - INTERVAL '30 days'
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

</mapper>