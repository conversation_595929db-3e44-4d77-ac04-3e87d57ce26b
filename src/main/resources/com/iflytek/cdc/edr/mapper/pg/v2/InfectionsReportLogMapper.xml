<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.InfectionsReportLogMapper">
    <sql id="outPatientReportLog">
        aomr.serial_no outpatient_id,
        aomr.patient_name,
        aomr.patient_sex_desc as sex_name,
        case
        when aomr.patient_age_unit is not null and aomr.patient_age_unit<![CDATA[<>]]> '' and aomr.patient_age is not null and aomr.patient_age<![CDATA[<>]]> ''
        then CONCAT(aomr.patient_age, aomr.patient_age_unit)
        when aomr.patient_age is not null and aomr.patient_age<![CDATA[<>]]> ''
        then CONCAT(aomr.patient_age, '岁')
        else '' end as age,
        aomr.identity_number as patient_id_number,
        aomr.job occupation,
        aomr.dept_name,
        doi.source_type as source_type,
        case when doi.source_type = '110' then '等级医院' when doi.source_type = '120' then '基层医疗' end sourceTypeName,
        doi.org_name as orgName,
        doi.org_id as orgId,
        aomr.doctor_name AS doctor_name,
        aomr.diagnose_name as diagnose_name,
        aomr.disease_name,
        CAST(aomr.visit_time AS DATE) visit_date,
        CAST(aomr.attk_date_time AS DATE) start_sick_date,
        case when aomr.first_dig_flag = '0' then '复诊' else '初诊' end first_diag_flag,
        aomr.now_addr living_address,
        -- todo 将medical_id作为event_id
        aomr.medical_id as event_id,
        aomr.medical_id as medical_id
    </sql>
    <sql id="inHospitalReportLog">
        air.inhospital_no                             in_hospital_id,
        air.patient_name,
        air.patient_sex_desc                          sex_name,
        case
        when air.patient_age_unit is not null and air.patient_age_unit<![CDATA[<>]]> '' and air.patient_age is not null and air.patient_age<![CDATA[<>]]> ''
        then CONCAT(air.patient_age, air.patient_age_unit)
        when air.patient_age is not null and air.patient_age<![CDATA[<>]]> ''
        then CONCAT(air.patient_age, '岁')
        else '' end as age,
        air.identity_no                               patient_id_number,
        air.job                                       occupation,
        air.current_dept_name                         dept_name,
        air.director_doc_name                         doctor_name,
        CAST(air.admiss_time AS DATE)        in_hospital_date,
        air.init_diagnose_name                        in_hospital_diag,
        CAST(air.discharge_datetime AS DATE) out_hospital_date,
        air.discharge_diagnose_desc                   out_hospital_diag,
        air.outcome_name,
        air.outpatient_no as outpatient_id,
        air.residential_address                       living_address,
        air.medical_id                                event_id,
        air.medical_id                                medicalId,
        doi.source_type                               source_type,
        case when doi.source_type = '110' then '等级医院' when doi.source_type = '120' then '基层医疗' end sourceTypeName,
        doi.org_name as orgName
    </sql>

    <select id="getOutpatientReportLog" resultType="com.iflytek.cdc.edr.dto.infections.OutpatientReportLog">
        SELECT dcmr.his_medical_id outpatient_id,
               dcmr.patient_name,
               dcmr.sex_name,
               CONCAT(dcmr.age, age_unit_name) AS age,
               dcmr.patient_id_number,
               dcmr.job occupation,
               dcmr.dept_name,
               dcmr.visit_doc_name AS doctor_name,
               dcmr.diagnose_name,
               CAST(dcmr.event_datetime AS DATE) visit_date,
               CAST(dcmr.start_sick_datetime AS DATE) start_sick_date,
               dcmr.is_first_diag first_diag_flag,
               dcmr.current_addr_detail living_address,
               dcmr.event_id
          FROM dwd.dwd_ch_medical_record dcmr
          JOIN dim.dim_organization_info doi ON doi.org_id = dcmr.org_id
         WHERE dcmr.event_datetime >= #{startDate}::timestamp
           AND dcmr.event_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
           AND doi.source_type in ('110', '120')
        <if test="provinceCode != null">
           AND doi.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
           AND doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null">
           AND doi.district_code = #{districtCode}
        </if>
        <if test="sourceType != null and sourceType != ''">
           AND doi.source_type = #{sourceType}
        </if>
        <if test="reportOrgCode != null">
           AND doi.org_id = #{reportOrgCode}
        </if>
        <if test="patientName != null and patientName != ''">
           AND dcmr.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
           AND dcmr.diagnose_name LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isInfectious != null">
           AND dcmr.is_infectious = #{isInfectious}
        </if>
        <if test="infectedName != null">
           AND dcmr.diagnose_name LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="firstDiagFlag != null and firstDiagFlag != ''">
            AND is_first_diag = #{firstDiagFlag}
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
         ORDER BY dcmr.event_datetime DESC
        LIMIT ${pageSize} OFFSET (${pageIndex} - 1) * ${pageSize}
    </select>

    <select id="getInHospitalReportLog" resultType="com.iflytek.cdc.edr.dto.infections.InHospitalReportLog">
        SELECT dcir.inhospital_id in_hospital_id,
               dcir.patient_name,
               dcir.sex_desc sex_name,
               dcir.age,
               dcir.id_card_no patient_id_number,
               dcir.job occupation,
               dcir.dept_name,
               dcir.doctor_name,
               CAST(dcir.in_hos_date AS DATE) in_hospital_date,
               dcir.in_diag in_hospital_diag,
               CAST(dcir.out_hos_date AS DATE) out_hospital_date,
               dcir.out_diag out_hospital_diag,
               dcir.outcome_name,
               dcir.outpatient_id,
               dcir.residential_address living_address,
               dcir.event_id
          FROM dwd.dwd_ch_inhospital_record dcir
          JOIN dim.dim_organization_info doi ON doi.org_id = dcir.org_id
         WHERE dcir.in_hos_date >= #{startDate}::timestamp
           AND dcir.in_hos_date &lt; #{endDate}::timestamp + INTERVAL '1 day'
           AND doi.source_type in ('110', '120')
        <if test="provinceCode != null">
           AND doi.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
           AND doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null">
           AND doi.district_code = #{districtCode}
        </if>
        <if test="sourceType != null and sourceType != ''">
           AND doi.source_type = #{sourceType}
        </if>
        <if test="reportOrgCode != null">
           AND doi.org_id = #{reportOrgCode}
        </if>
        <if test="patientName != null and patientName != ''">
           AND dcir.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
           AND (dcir.in_diag LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%') OR dcir.out_diag LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%'))
        </if>
        <if test="isInfectious != null">
           AND dcir.is_infectious = #{isInfectious}
        </if>
        <if test="infectedName != null">
           AND (dcir.in_diag LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%') OR dcir.out_diag LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%'))
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
         ORDER BY dcir.in_hos_date DESC
        LIMIT ${pageSize} OFFSET (${pageIndex} - 1) * ${pageSize}
    </select>

    <select id="getAbnormalMedicalList"
            resultType="com.iflytek.cdc.edr.entity.hismedicallog.DwdChMedicalSuspectRecord">
        select sr.event_id as eventId, sr.his_medical_id as hisMedicalId, sr.org_id as orgId, doi.org_name as orgName,
               sr.patient_name as patientName, sr.sex_desc as sexDesc, sr.in_diagnose as inDiagnose,
               sr.out_diagnose as outDiagnose, sr.age as age, sr.age_unit_name as ageUnitName,
               sr.id_card_no as idCardNo, sr.global_person_id as globalPersonId,
               sr.full_date as fullDate, sr.visit_datetime as visitDatetime,
               sr.event_datetime as eventDatetime, sr.doctor_id as doctorId,
               sr.doctor_name as doctorName, sr.dept_name as deptName,
               sr.dept_code as deptCode, sr.is_submit_report_card as isSubmitReportCard,
               case when doi.district_name = '' then doi.city_name else doi.district_name end as regionName,
               sr.data_src as dataSrc, doi.source_type as sourceType,
               doi.source_type_name as sourceTypeName, sr.living_addr_detail as livingAddressDetail,
               case when sr.data_src = '3' then sr.in_diagnose else sr.diagnose_name_list end as diagnoseName
        from dwd.dwd_ch_medical_suspect_record sr
        left join dim.dim_organization_info doi
        on sr.org_id = doi.org_id
        where sr.event_datetime >= #{startDate}::timestamp
        and sr.event_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
        <if test="dataSrc != null and dataSrc != ''">
            and sr.data_src = #{dataSrc, jdbcType=VARCHAR}
        </if>
        <if test="sourceType != null and sourceType != ''">
            and doi.source_type = #{sourceType, jdbcType=VARCHAR}
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and doi.province_code = #{provinceCode, jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and doi.city_code = #{cityCode, jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and doi.district_code = #{districtCode, jdbcType=VARCHAR}
        </if>
        <if test="reportOrgCode != null and reportOrgCode != ''">
            and sr.org_id = #{reportOrgCode, jdbcType=VARCHAR}
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
            <choose>
                <when test="dataSrc == 3">
                    and sr.in_diagnose like concat('%', #{diagnoseName, jdbcType=VARCHAR}, '%')
                </when>
                <when test="dataSrc == 2">
                    and sr.diagnose_name_list like concat('%', #{diagnoseName, jdbcType=VARCHAR}, '%')
                </when>
                <when test="dataSrc == '' or dataSrc == null">
                    and (sr.in_diagnose like concat('%', #{diagnoseName, jdbcType=VARCHAR}, '%') or sr.diagnose_name_list like concat('%', #{diagnoseName, jdbcType=VARCHAR}, '%'))
                </when>
            </choose>
        </if>
        <if test="patientName != null and patientName != ''">
            and sr.patient_name like concat('%', #{patientName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="isReported.size() &gt; 0">
            and sr.is_submit_report_card in
            <foreach item="item" index="index" collection="isReported"
                     open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="getSuspectRecordList"
            resultType="com.iflytek.cdc.edr.dto.suspect.DwdChMedicalSuspectRecordDto">
        select sr.* from dwd.dwd_ch_medical_suspect_record sr
        left join dim.dim_organization_info doi
        on sr.org_id = doi.org_id
        where 1=1
        <if test="minFullDate != null">
            and sr.full_date >= #{minFullDate,jdbcType=DATE}
        </if>
        <if test="maxFullDate != null">
            and sr.full_date &lt;= #{maxFullDate,jdbcType=DATE}
        </if>
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and doi.province_code in
            <foreach close=")" collection="provinceCodes" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and doi.city_code in
            <foreach close=")" collection="cityCodes" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and doi.district_code in
            <foreach close=")" collection="districtCodes" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="getSuspectDetailByEventIds"
            resultType="com.iflytek.cdc.edr.dto.suspect.DwdChMedicalSuspectDetailDto">
        select * from dwd.dwd_ch_medical_suspect_detail where
        event_id in
        <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="getAllOutpatientReportLog" resultType="com.iflytek.cdc.edr.dto.infections.OutpatientReportLog">
        SELECT dcmr.his_medical_id outpatient_id,
        dcmr.patient_name,
        dcmr.sex_name,
        CONCAT(dcmr.age, age_unit_name) AS age,
        dcmr.patient_id_number,
        dcmr.job occupation,
        dcmr.dept_name,
        dcmr.visit_doc_name AS doctor_name,
        dcmr.diagnose_name,
        CAST(dcmr.event_datetime AS DATE) visit_date,
        CAST(dcmr.start_sick_datetime AS DATE) start_sick_date,
        dcmr.is_first_diag first_diag_flag,
        dcmr.current_addr_detail living_address,
        dcmr.event_id
        FROM dwd.dwd_ch_medical_record dcmr
        JOIN dim.dim_organization_info doi ON doi.org_id = dcmr.org_id
        WHERE dcmr.event_datetime >= #{startDate}::timestamp
        AND dcmr.event_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
        AND doi.source_type in ('110', '120')
        <if test="provinceCode != null">
            AND doi.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
            AND doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null">
            AND doi.district_code = #{districtCode}
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND doi.source_type = #{sourceType}
        </if>
        <if test="reportOrgCode != null">
            AND doi.org_id = #{reportOrgCode}
        </if>
        <if test="patientName != null and patientName != ''">
            AND dcmr.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
            AND dcmr.diagnose_name LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isInfectious != null">
            AND dcmr.is_infectious = #{isInfectious}
        </if>
        <if test="infectedName != null">
            AND dcmr.diagnose_name LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="firstDiagFlag != null and firstDiagFlag != ''">
            AND is_first_diag = #{firstDiagFlag}
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
        ORDER BY dcmr.event_datetime DESC
    </select>
    <select id="getAllInHospitalReportLog" resultType="com.iflytek.cdc.edr.dto.infections.InHospitalReportLog">
        SELECT dcir.inhospital_id in_hospital_id,
        dcir.patient_name,
        dcir.sex_desc sex_name,
        dcir.age,
        dcir.id_card_no patient_id_number,
        dcir.job occupation,
        dcir.dept_name,
        dcir.doctor_name,
        CAST(dcir.in_hos_date AS DATE) in_hospital_date,
        dcir.in_diag in_hospital_diag,
        CAST(dcir.out_hos_date AS DATE) out_hospital_date,
        dcir.out_diag out_hospital_diag,
        dcir.outcome_name,
        dcir.outpatient_id,
        dcir.residential_address living_address,
        dcir.event_id
        FROM dwd.dwd_ch_inhospital_record dcir
        JOIN dim.dim_organization_info doi ON doi.org_id = dcir.org_id
        WHERE dcir.in_hos_date >= #{startDate}::timestamp
        AND dcir.in_hos_date &lt; #{endDate}::timestamp + INTERVAL '1 day'
        AND doi.source_type in ('110', '120')
        <if test="provinceCode != null">
            AND doi.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
            AND doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null">
            AND doi.district_code = #{districtCode}
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND doi.source_type = #{sourceType}
        </if>
        <if test="reportOrgCode != null">
            AND doi.org_id = #{reportOrgCode}
        </if>
        <if test="patientName != null and patientName != ''">
            AND dcir.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
            AND (dcir.in_diag LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%') OR dcir.out_diag LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%'))
        </if>
        <if test="isInfectious != null">
            AND dcir.is_infectious = #{isInfectious}
        </if>
        <if test="infectedName != null">
            AND (dcir.in_diag LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%') OR dcir.out_diag LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%'))
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
        ORDER BY dcir.in_hos_date DESC
    </select>

    <select id="selectOutpatientByMedicalId" resultType="com.iflytek.cdc.edr.dto.infections.OutpatientReportLog">
        SELECT
            <include refid="outPatientReportLog"/>
        FROM ads.ads_outpat_medical_records aomr
        JOIN dim.dim_organization_info doi ON doi.org_id = aomr.org_id
        where aomr.medical_id = #{medicalId}
    </select>

    <select id="listOutpatientByMedicalIds" resultType="com.iflytek.cdc.edr.dto.infections.OutpatientReportLog">
        SELECT
        <include refid="outPatientReportLog"/>
        FROM ads.ads_outpat_medical_records aomr
        JOIN dim.dim_organization_info doi ON doi.org_id = aomr.org_id
        where aomr.medical_id in
        <foreach collection="medicalIds" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getNewOutpatientReportLog" resultType="com.iflytek.cdc.edr.dto.infections.OutpatientReportLog">
        SELECT
            <include refid="outPatientReportLog"/>
        FROM ads.ads_outpat_medical_records aomr
        JOIN dim.dim_organization_info doi ON doi.org_id = aomr.org_id
        WHERE aomr.event_datetime >= #{startDate}::timestamp
        AND aomr.event_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
        AND doi.source_type in ('110', '120')
        <if test="provinceCode != null">
            AND doi.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
            AND doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null">
            AND doi.district_code = #{districtCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size() > 0">
            and doi.province_code in
            <foreach collection="provinceCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() > 0">
            and doi.city_code in
            <foreach collection="cityCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() > 0">
            and doi.district_code in
            <foreach collection="districtCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="provinceCodes != null and provinceCodes.size() > 0">
            and doi.province_code in
            <foreach collection="provinceCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() > 0">
            and doi.city_code in
            <foreach collection="cityCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() > 0">
            and doi.district_code in
            <foreach collection="districtCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND doi.source_type = #{sourceType}
        </if>
        <if test="orgList != null and orgList.size() > 0">
            AND doi.org_id in
            <foreach item="item" index="index" collection="orgList" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="patientName != null and patientName != ''">
            AND aomr.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
            AND aomr.diagnose_name LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isInfectious != null">
            AND aomr.is_infectious = #{isInfectious}
        </if>
        <if test="infectedName != null">
            AND aomr.diagnose_name LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND aomr.is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="firstDiagFlag != null and firstDiagFlag != ''">
            AND aomr.first_dig_flag = #{firstDiagFlag}
        </if>
        <if test="deptName != null and deptName != ''">
            AND aomr.dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
        <if test="occupation != null and occupation != ''">
            AND aomr.job LIKE concat('%', #{occupation, jdbcType=VARCHAR}, '%')
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            AND aomr.disease_name LIKE concat('%', #{diseaseName, jdbcType=VARCHAR}, '%')
        </if>
        order by CAST(aomr.visit_time AS DATE) DESC,
        doi.org_name,
        aomr.serial_no asc
    </select>

    <select id="getNewAllOutpatientReportLog"
            resultType="com.iflytek.cdc.edr.dto.infections.OutpatientReportLog">
        SELECT aomr.serial_no outpatient_id,
        aomr.patient_name,
        aomr.patient_sex_desc as sexName,
        case
        when aomr.patient_age_unit is not null and aomr.patient_age_unit<![CDATA[<>]]> '' and aomr.patient_age is not null and aomr.patient_age<![CDATA[<>]]> ''
        then CONCAT(aomr.patient_age, aomr.patient_age_unit)
        when aomr.patient_age is not null and aomr.patient_age<![CDATA[<>]]> ''
        then CONCAT(aomr.patient_age, '岁')
        else '' end as age,
        aomr.identity_number as patientIdNumber,
        aomr.job occupation,
        aomr.dept_name,
        aomr.doctor_name AS doctor_name,
        aomr.diagnose_name,
        CAST(aomr.event_datetime AS DATE) visit_date,
        CAST(aomr.attk_date_time AS DATE) start_sick_date,
        case when aomr.first_dig_flag = '1' then '初诊' else '复诊' end first_diag_flag,
        aomr.now_addr living_address,
        aomr.event_id,
        case when doi.source_type = '110' then '等级医院' when doi.source_type = '120' then '基层医疗' end sourceTypeName,
        doi.org_name as orgName
        FROM ads.ads_outpat_medical_records aomr
        JOIN dim.dim_organization_info doi ON doi.org_id = aomr.org_id
        WHERE aomr.event_datetime >= #{startDate}::timestamp
        AND aomr.event_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
        AND doi.source_type in ('110', '120')
        <if test="provinceCode != null">
            AND doi.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
            AND doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null">
            AND doi.district_code = #{districtCode}
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND doi.source_type = #{sourceType}
        </if>
        <if test="orgList != null and orgList.size() > 0">
            AND doi.org_id in
            <foreach item="item" index="index" collection="orgList" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="patientName != null and patientName != ''">
            AND aomr.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
            AND aomr.diagnose_name LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isInfectious != null">
            AND aomr.is_infectious = #{isInfectious}
        </if>
        <if test="infectedName != null">
            AND aomr.diagnose_name LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND aomr.is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="firstDiagFlag != null and firstDiagFlag != ''">
            AND aomr.first_dig_flag = #{firstDiagFlag}
        </if>
        <if test="deptName != null and deptName != ''">
            AND aomr.dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
        ORDER BY aomr.event_datetime DESC
    </select>

    <select id="selectInHospitalByEventId" resultType="com.iflytek.cdc.edr.dto.infections.InHospitalReportLog">
        SELECT <include refid="inHospitalReportLog"/>
        FROM ads.ads_inpat_record air
        JOIN dim.dim_organization_info doi ON doi.org_id = air.org_id
        where air.medical_id = #{eventId}
    </select>

    <select id="listInHospitalByEventIds" resultType="com.iflytek.cdc.edr.dto.infections.InHospitalReportLog">
        SELECT <include refid="inHospitalReportLog"/>
        FROM ads.ads_inpat_record air
        JOIN dim.dim_organization_info doi ON doi.org_id = air.org_id
        where air.medical_id in
        <foreach collection="eventIds" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getNewInHospitalReportLog" resultType="com.iflytek.cdc.edr.dto.infections.InHospitalReportLog">
        SELECT <include refid="inHospitalReportLog"/>
        FROM ads.ads_inpat_record air
        JOIN dim.dim_organization_info doi ON doi.org_id = air.org_id
        WHERE air.admiss_time >= #{startDate}::timestamp
        AND air.admiss_time &lt; #{endDate}::timestamp + INTERVAL '1 day'
        AND doi.source_type in ('110', '120')
        <if test="diseaseName != null and diseaseName != ''">
            AND EXISTS (
                SELECT 1
                FROM dws.dws_infected_suspect_mapping_result r
                WHERE air.medical_id = r.medical_id
                AND r.tag_name LIKE CONCAT('%', #{diseaseName, jdbcType=VARCHAR}, '%')
            )
        </if>
        <if test="provinceCode != null">
            AND doi.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
            AND doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null">
            AND doi.district_code = #{districtCode}
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND doi.source_type = #{sourceType}
        </if>
        <if test="orgList != null and orgList.size() > 0">
            AND doi.org_id in
            <foreach item="item" index="index" collection="orgList" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="patientName != null and patientName != ''">
            AND air.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <!-- 入院诊断 新表中为初步诊断 -->
        <if test="diagnoseName != null and diagnoseName != ''">
            AND (air.init_diagnose_name LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%') OR air.out_ward_notes LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%'))
        </if>
        <if test="isInfectious != null">
            AND air.is_infectious = #{isInfectious}
        </if>
        <if test="infectedName != null">
            AND (air.init_diagnose_name LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%') OR air.out_ward_notes LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%'))
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND air.is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="deptName != null and deptName != ''">
            AND air.current_dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
        <if test="occupation != null and occupation != ''">
            AND air.job LIKE concat('%', #{occupation, jdbcType=VARCHAR}, '%')
        </if>
        order by CAST(air.admiss_time AS DATE) DESC,
        count(air.medical_id) over (partition by doi.org_name, CAST(air.admiss_time AS DATE)) desc,
        doi.org_name,
        air.inhospital_no asc
    </select>

    <select id="getNewAllInHospitalReportLog"
            resultType="com.iflytek.cdc.edr.dto.infections.InHospitalReportLog">
        SELECT air.inhospital_no in_hospital_id,
        air.patient_name,
        air.patient_sex_desc sex_name,
        case
        when air.patient_age_unit is not null and air.patient_age_unit<![CDATA[<>]]> '' and air.patient_age is not null and air.patient_age<![CDATA[<>]]> ''
        then CONCAT(air.patient_age, air.patient_age_unit)
        when air.patient_age is not null and air.patient_age<![CDATA[<>]]> ''
        then CONCAT(air.patient_age, '岁')
        else '' end as age,
        air.identity_no patient_id_number,
        air.job occupation,
        air.current_dept_name as dept_name,
        air.director_doc_name as doctor_name,
        CAST(air.admiss_time AS DATE) in_hospital_date,
        <!-- 入院诊断 新表中为初步诊断-->
        air.init_diagnose_name in_hospital_diag,
        CAST(air.discharge_datetime AS DATE) out_hospital_date,
        air.discharge_diagnose_desc out_hospital_diag,
        air.outcome_name,
        air.outpatient_no as outpatient_id,
        air.residential_address living_address,
        case when doi.source_type = '110' then '等级医院' when doi.source_type = '120' then '基层医疗' end sourceTypeName,
        doi.org_name as orgName,
        air.event_id, air.medical_id
        FROM ads.ads_inpat_record air
        JOIN dim.dim_organization_info doi ON doi.org_id = air.org_id
        WHERE air.admiss_time >= #{startDate}::timestamp
        AND air.admiss_time &lt; #{endDate}::timestamp + INTERVAL '1 day'
        AND doi.source_type in ('110', '120')
        <if test="diseaseName != null and diseaseName != ''">
            AND exists (
                select 1 from dws.dws_infected_suspect_detail   r
                where air.medical_id = r.medical_id
                    AND r.infected_sub_name LIKE concat('%', #{diseaseName, jdbcType=VARCHAR}, '%')
            )
        </if>

        <if test="provinceCode != null">
            AND doi.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
            AND doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null">
            AND doi.district_code = #{districtCode}
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND doi.source_type = #{sourceType}
        </if>
        <if test="orgList != null and orgList.size() > 0">
            AND doi.org_id in
            <foreach item="item" index="index" collection="orgList" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="patientName != null and patientName != ''">
            AND air.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <!-- 入院诊断 新表中为初步诊断 -->
        <if test="diagnoseName != null and diagnoseName != ''">
            AND (air.init_diagnose_name LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%') OR air.out_ward_notes LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%'))
        </if>
        <if test="isInfectious != null">
            AND air.is_infectious = #{isInfectious}
        </if>
        <if test="infectedName != null">
            AND (air.init_diagnose_name LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%') OR air.out_ward_notes LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%'))
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND air.is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="deptName != null and deptName != ''">
            AND air.current_dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
        <if test="occupation != null and occupation != ''">
            AND air.job LIKE concat('%', #{occupation, jdbcType=VARCHAR}, '%')
        </if>
        ORDER BY air.admiss_time DESC
    </select>

    <select id="getNewAbnormalMedicalList"
            resultType="com.iflytek.cdc.edr.dto.suspect.MedicalSuspectRecordDto">
        -- todo 将medical_id作为event_id
        select sr.medical_id                      as                eventId,
               sr.his_medical_id                  as                hisMedicalId,
               sr.org_id                          as                orgId,
               doi.org_name                       as                orgName,
               sr.patient_name                    as                patientName,
               sr.sex_desc                        as                sexDesc,
               sr.in_diagnose                     as                inDiagnose,
               sr.out_diagnose                    as                outDiagnose,
               sr.age                             as                age,
               sr.age_unit_name                   as                ageUnitName,
               sr.id_card_no                      as                idCardNo,
               sr.global_person_id                as                globalPersonId,
               sr.full_date                       as                fullDate,
               sr.visit_datetime                  as                visitDatetime,
               sr.event_datetime                  as                eventDatetime,
               sr.doctor_id                       as                doctorId,
               sr.doctor_name                     as                doctorName,
               sr.dept_name                       as                deptName,
               sr.dept_code                       as                deptCode,
               sr.is_submit_report_card           as                isSubmitReportCard,
               case
                   when doi.district_name = '' then doi.city_name
                   else doi.district_name end     as                regionName,
               sr.data_src                        as                dataSrc,
               doi.source_type                    as                sourceType,
               case
                   when doi.source_type = '110' then '等级医院'
                   when doi.source_type = '120' then '基层医疗' end sourceTypeName,
               sr.living_addr_detail              as                livingAddressDetail,
               case
                   when sr.data_src = '4' then sr.in_diagnose
                   else sr.diagnose_name_list end as                diagnoseName
        from ads.ads_output_medical_suspect_record sr
                 left join dim.dim_organization_info doi on sr.org_id = doi.org_id
        where CAST(sr.event_datetime AS DATE) >= CAST(#{startDate} AS DATE)
          and CAST(sr.event_datetime AS DATE) &lt;= CAST(#{endDate} AS DATE)
          and exists (
                select 1 from ads.v_infected_suspect_mapping_result m
                 where sr.medical_id = m.medical_id
                <if test="caseTypeList != null and caseTypeList.size() != 0">
                    and m.case_type_code in
                    <foreach collection="caseTypeList" item="item" separator="," open="(" close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </if>
                <if test="infectedCode != null and infectedCode.size() &gt; 0">
                    and m.tag_code  in
                    <foreach item="item" collection="infectedCode"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="submitReportTypeList != null and submitReportTypeList.size() != 0">
                    and m.case_type_code in
                    <foreach collection="submitReportTypeList" item="item" separator="," open="(" close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </if>
                <if test="isReported != null and isReported.size() != 0">
                    and m.is_submit_report_card in
                    <foreach collection="isReported" item="item" separator="," open="(" close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </if>
                <if test="diseaseName != null and diseaseName != ''">
                    AND m.tag_name LIKE concat('%', #{diseaseName, jdbcType=VARCHAR}, '%')
                </if>
                )
        <if test="dataSrc != null and dataSrc != ''">
            and sr.data_src = #{dataSrc, jdbcType=VARCHAR}
        </if>
        <if test="sourceType != null and sourceType != ''">
            and doi.source_type = #{sourceType, jdbcType=VARCHAR}
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and doi.province_code = #{provinceCode, jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and doi.city_code = #{cityCode, jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and doi.district_code = #{districtCode, jdbcType=VARCHAR}
        </if>
        <if test="provinceCodes != null and provinceCodes.size() > 0">
            and doi.province_code in
            <foreach collection="provinceCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() > 0">
            and doi.city_code in
            <foreach collection="cityCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() > 0">
            and doi.district_code in
            <foreach collection="districtCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="provinceCodes != null and provinceCodes.size() > 0">
            and doi.province_code in
            <foreach collection="provinceCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() > 0">
            and doi.city_code in
            <foreach collection="cityCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() > 0">
            and doi.district_code in
            <foreach collection="districtCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="orgList != null and orgList.size() > 0">
            AND sr.org_id in
            <foreach item="item" index="index" collection="orgList" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
            <choose>
                <when test="dataSrc == 4">
                    and sr.in_diagnose like concat('%', #{diagnoseName, jdbcType=VARCHAR}, '%')
                </when>
                <when test="dataSrc == 2">
                    and sr.diagnose_name_list like concat('%', #{diagnoseName, jdbcType=VARCHAR}, '%')
                </when>
                <when test="dataSrc == '' or dataSrc == null">
                    and (sr.in_diagnose like concat('%', #{diagnoseName, jdbcType=VARCHAR}, '%') or
                         sr.diagnose_name_list like concat('%', #{diagnoseName, jdbcType=VARCHAR}, '%'))
                </when>
            </choose>
        </if>
        <if test="patientName != null and patientName != ''">
            and sr.patient_name like concat('%', #{patientName, jdbcType=VARCHAR}, '%')
        </if>
        order by CAST(sr.event_datetime AS DATE) DESC,
                 count(sr.medical_id) over (partition by doi.org_name, CAST(sr.event_datetime AS DATE)) desc,
                 doi.org_name,
                 sr.his_medical_id asc
    </select>

    <select id="getNewSuspectRecordList"
            resultType="com.iflytek.cdc.edr.dto.suspect.DwdChMedicalSuspectRecordDto">
        select sr.* from ads.ads_output_medical_suspect_record sr
        left join dim.dim_organization_info doi
        on sr.org_id = doi.org_id
        where 1=1
        <if test="minFullDate != null">
            and sr.full_date >= #{minFullDate,jdbcType=DATE}
        </if>
        <if test="maxFullDate != null">
            and sr.full_date &lt;= #{maxFullDate,jdbcType=DATE}
        </if>
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and doi.province_code in
            <foreach close=")" collection="provinceCodes" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and doi.city_code in
            <foreach close=")" collection="cityCodes" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and doi.district_code in
            <foreach close=")" collection="districtCodes" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <select id="getNewSuspectDetailByEventIds"
            resultType="com.iflytek.cdc.edr.dto.suspect.DwdChMedicalSuspectDetailDto">
        select * from ads.ads_output_medical_suspect_detail where
        medical_id in
        <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getSuspectMappingResultByMedicalIds" resultType="com.iflytek.cdc.edr.dto.suspect.SuspectMappingResult">
        select * from ads.v_infected_suspect_mapping_result m
        where m.medical_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="getNewAllInHospitalReportLogCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM ads.ads_inpat_record air
        JOIN dim.dim_organization_info doi ON doi.org_id = air.org_id
        WHERE air.admiss_time >= #{startDate}::timestamp
        AND air.admiss_time &lt; #{endDate}::timestamp + INTERVAL '1 day'
        AND doi.source_type in ('110', '120')
        AND exists (
            select 1 from dws.dws_infected_suspect_mapping_result r
            where air.medical_id = r.medical_id
            <if test="diseaseName != null and diseaseName != ''">
                AND r.tag_name LIKE concat('%', #{diseaseName, jdbcType=VARCHAR}, '%')
            </if>
        )
        <if test="provinceCode != null">
            AND doi.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
            AND doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null">
            AND doi.district_code = #{districtCode}
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND doi.source_type = #{sourceType}
        </if>
        <if test="orgList != null and orgList.size() > 0">
            AND doi.org_id in
            <foreach item="item" index="index" collection="orgList" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="patientName != null and patientName != ''">
            AND air.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <!-- 入院诊断 新表中为初步诊断 -->
        <if test="diagnoseName != null and diagnoseName != ''">
            AND (air.init_diagnose_name LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%') OR air.out_ward_notes LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%'))
        </if>
        <if test="isInfectious != null">
            AND air.is_infectious = #{isInfectious}
        </if>
        <if test="infectedName != null">
            AND (air.init_diagnose_name LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%') OR air.out_ward_notes LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%'))
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND air.is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="deptName != null and deptName != ''">
            AND air.current_dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
        <if test="occupation != null and occupation != ''">
            AND air.job LIKE concat('%', #{occupation, jdbcType=VARCHAR}, '%')
        </if>
    </select>

    <select id="getInfectedSuspectMappingResultByMedicalIds"
            resultType="com.iflytek.cdc.edr.dto.suspect.SuspectMappingResult">
        select * from dws.dws_infected_suspect_mapping_result m
        where m.medical_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getOriginOutpatientMedicalList" resultType="com.iflytek.cdc.edr.vo.dm.OutpatientOriginMedicalVO">
        SELECT
        <include refid="outPatientReportLog"/>
        FROM ads.ads_outpat_medical_records aomr
        JOIN dim.dim_organization_info doi ON doi.org_id = aomr.org_id
        WHERE 1=1 and
        <include refid="whereSqlGetOriginOutpatientMedicalList"/>
        order by CAST(aomr.visit_time AS DATE) DESC
    </select>

    <sql id="whereSqlGetOriginOutpatientMedicalList">
        aomr.event_datetime >= #{startDate}::timestamp
        AND aomr.event_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
        AND doi.source_type in ('110', '120')
        <if test="provinceCodes != null and provinceCodes.size() > 0">
            and doi.province_code in
            <foreach collection="provinceCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() > 0">
            and doi.city_code in
            <foreach collection="cityCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() > 0">
            and doi.district_code in
            <foreach collection="districtCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND doi.source_type = #{sourceType}
        </if>
        <if test="patientName != null and patientName != ''">
            AND aomr.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
            AND aomr.diagnose_name LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%')
        </if>
        <if test="deptName != null and deptName != ''">
            AND aomr.dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
    </sql>

    <select id="getOriginInpatientMedicalList" resultType="com.iflytek.cdc.edr.vo.dm.InpatientOriginMedicalVO">
        SELECT <include refid="inHospitalReportLog"/>
        FROM ads.ads_inpat_record air
        JOIN dim.dim_organization_info doi ON doi.org_id = air.org_id
        WHERE 1=1
        <include refid="whereSqlGetOriginInpatientMedicalList"/>

        order by CAST(air.admiss_time AS DATE) DESC
    </select>
    <sql id="whereSqlGetOriginInpatientMedicalList">
        and air.admiss_time >= #{startDate}::timestamp
        AND air.admiss_time &lt; #{endDate}::timestamp + INTERVAL '1 day'
        AND doi.source_type in ('110', '120')
        <if test="provinceCodes != null and provinceCodes.size() > 0">
            and doi.province_code in
            <foreach collection="provinceCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() > 0">
            and doi.city_code in
            <foreach collection="cityCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() > 0">
            and doi.district_code in
            <foreach collection="districtCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND doi.source_type = #{sourceType}
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
            AND (air.init_diagnose_name LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%') OR air.out_ward_notes LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%'))
        </if>
        <if test="patientName != null and patientName != ''">
            AND air.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="deptName != null and deptName != ''">
            AND air.current_dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
    </sql>
    <select id="countOriginOutpatientMedicalList" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM ads.ads_outpat_medical_records aomr
        JOIN dim.dim_organization_info doi ON doi.org_id = aomr.org_id
        WHERE 1=1 and
        <include refid="whereSqlGetOriginOutpatientMedicalList"/>
    </select>
    <select id="countOriginInpatientMedicalList" resultType="java.lang.Integer">
        SELECT count(1)
        FROM ads.ads_inpat_record air
        JOIN dim.dim_organization_info doi ON doi.org_id = air.org_id
        WHERE 1=1
        <include refid="whereSqlGetOriginInpatientMedicalList"/>
    </select>

</mapper>