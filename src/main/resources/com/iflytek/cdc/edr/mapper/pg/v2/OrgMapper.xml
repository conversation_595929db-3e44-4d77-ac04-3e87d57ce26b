<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.OrgMapper">
    <select id="getOrgList" resultType="com.iflytek.cdc.edr.dto.org.OrgInfoDto">
        select org_id, org_name, org_address, org_type_code, org_type_name, source_type
          from dim.dim_organization_info
         where org_name not like '%测试%'
           and org_name not like '%导入%'
           and org_name not like '%巡检%'
        <foreach collection="orgParams.areaList" index="index" item="area" open=" and (" separator=" or " close=")">
            <if test='area.regionType == "1"'>
                city_code = '340100'
            </if>
            <if test="area.cityCode != null">
                city_code = #{area.cityCode}
            </if>
            <if test="area.districtCode != null">
                AND district_code = #{area.districtCode}
            </if>
        </foreach>
        <foreach collection="sourceTypeList" index="index" item="sourceTypeCode" open=" and (" separator=" or " close=")">
            source_type = #{sourceTypeCode}
        </foreach>
        <if test="orgParams.orgName != null and orgParams.orgName != ''">
            AND org_name like concat('%', #{orgParams.orgName, jdbcType=VARCHAR}, '%')
        </if>
    </select>

    <select id="getPharmacyList" resultType="com.iflytek.cdc.edr.dto.HierarchyData">
        SELECT pharmacy_name AS label, pharmacy_id AS value
        FROM dim.dim_ds_pharmacy_info
        WHERE pharmacy_name NOT LIKE '%测试%'
        AND pharmacy_name LIKE concat('%', #{orgName}::text, '%')
        <if test="provinceCode != null and provinceCode != ''">
           AND province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
           AND city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
           AND district_code = #{districtCode}
        </if>
        <if test="provinceCode == null and cityCode == null and districtCode == null">
           AND city_code = #{cityCode}
        </if>
    </select>

    <select id="getSchoolList" resultType="com.iflytek.cdc.edr.dto.HierarchyData">
        SELECT org_name AS label, org_id AS value
        FROM dim.dim_sc_school_info
        WHERE org_name LIKE concat('%', #{orgName}::text, '%')
        AND org_name NOT LIKE '%测试%'
        AND org_name NOT LIKE '%导入%'
        AND org_name NOT LIKE '%巡检%'
        <if test="provinceCode != null and provinceCode != ''">
           AND province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
           AND city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
           AND district_code = #{districtCode}
        </if>
        <if test="provinceCode == null and cityCode == null and districtCode == null">
           AND city_code = #{cityCode}
        </if>
    </select>

</mapper>