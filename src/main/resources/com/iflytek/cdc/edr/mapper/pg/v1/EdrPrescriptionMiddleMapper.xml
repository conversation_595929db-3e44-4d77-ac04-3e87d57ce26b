<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v1.EdrPrescriptionMiddleMapper">
    <resultMap type="com.iflytek.cdc.edr.entity.outpatient.EdrPrescriptionMiddle" id="EdrPrescriptionMiddleResult">
        <result property="id"    column="id"    />
        <result property="eventId"    column="event_id"    />
        <result property="outpatientId"    column="outpatient_id"    />
        <result property="prescriptionListTypeCode"    column="prescription_list_type_code"    />
        <result property="decoctMethod"    column="decoct_method"    />
        <result property="tcmrxDs"    column="tcmrx_ds"    />
        <result property="tcmNum"    column="tcm_num"    />
        <result property="treat"    column="treat"    />
        <result property="treatDetail"    column="treat_detail"    />
        <result property="applyDeptCode"    column="apply_dept_code"    />
        <result property="applyDeptName"    column="apply_dept_name"    />
        <result property="applyDocName"    column="apply_doc_name"    />
        <result property="drugName"    column="drug_name"    />
        <result property="drugSpec"    column="drug_spec"    />
        <result property="onceDosage"    column="once_dosage"    />
        <result property="drugUsewayName"    column="drug_useway_name"    />
        <result property="drugFrequencyName"    column="drug_frequency_name"    />
        <result property="totalNum"    column="total_num"    />
        <result property="useDays"    column="use_days"    />
    </resultMap>

    <sql id="selectEdrPrescriptionMiddleVo">
        select
            t0.id,
            t0.event_id,
            t0.outpatient_id,
            t0.prescription_list_type_code,
            t0.decoct_method,
            t0.tcmrx_ds,
            t0.tcm_num,
            t0.treat,
            t0.treat_detail,
            t0.apply_dept_code,
            t0.apply_dept_name,
            t0.apply_doc_name,
            t0.drug_name ,
            t0.drug_spec ,
            CONCAT(cast(t0.once_dosage as VARCHAR ), t0.once_dosage_unit_code)  once_dosage ,-- t1.once_dosage 单次使用量
            t0.drug_useway_name ,-- 用法
            t0.drug_frequency_name ,-- 频次
            cast(t0.total_num as VARCHAR )  total_num,  -- 总量
            t0.use_days  -- 天数
        from dwd.dwd_ch_prescription t0

    </sql>

    <select id="selectEdrPrescriptionMiddleByEventId" parameterType="String" resultMap="EdrPrescriptionMiddleResult">
        <include refid="selectEdrPrescriptionMiddleVo"/>
        <where>
         t0.event_id = #{eventId}
        </where>
    </select>

    <select id="selectEdrPrescriptionMiddleById" parameterType="String" resultMap="EdrPrescriptionMiddleResult">
        <include refid="selectEdrPrescriptionMiddleVo"/>
        where id = #{id}
    </select>
</mapper>