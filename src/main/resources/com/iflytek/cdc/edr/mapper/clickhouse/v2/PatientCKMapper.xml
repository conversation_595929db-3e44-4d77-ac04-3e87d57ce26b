<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.clickhouse.v2.PatientCKMapper">

    <select id="getNewPatientInfoByPerson" resultType="com.iflytek.cdc.edr.entity.person.DimMdmPersonInfo">
        SELECT dmpi.global_person_id,
        dmpi.patient_id_number,
        dmpi.identity_type_name,
        dmpi.patient_name,
        dmpi.sex_name,
        dmpi.birthday birthday,
        dmpi.marital_code,
        dmpi.marital_name,
        dmpi.telephone,
        dmpi.household_addr_detail,
        dmpi.living_address,
        dmpi.company_name,
        dmpi.contact_name,
        dmpi.contact_telephone,
        dmpi.std_living_place_name,
        formatDateTime(dmpi.create_datetime, '%Y-%m-%d %H:%M:%S') AS create_datetime,
        formatDateTime(dmpi.update_datetime, '%Y-%m-%d %H:%M:%S') AS update_datetime
        FROM dim_mdm_person_info dmpi
        WHERE dmpi.update_datetime IS NOT NULL
        <foreach collection="areaList" index="index" item="area" open=" AND (" separator=" OR " close=")">
            <if test='area.regionType == "1"'>
                dmpi.std_living_city_code = '340100'
            </if>
            <if test="area.cityCode != null">
                dmpi.std_living_city_code = #{area.cityCode}
            </if>
            <if test="area.districtCode != null">
                AND dmpi.std_living_place_code = #{area.districtCode}
            </if>
        </foreach>
        <if test="personInfo != null">
            AND composite_key LIKE concat('%', #{personInfo}, '%')
        </if>
        <if test="startTime != null">
            AND dmpi.create_datetime >= #{startTime}
        </if>
        <if test="endTime != null">
            AND dmpi.create_datetime &lt;= #{endTime}
        </if>
        ORDER BY update_datetime DESC
        LIMIT ${pageSize} OFFSET (${pageIndex} - 1) * ${pageSize}
    </select>

<!--    <select id="getNewPatientInfoByEvent" resultType="com.iflytek.cdc.edr.entity.person.DimMdmPersonInfo">-->
<!--        SELECT aer.global_person_id,-->
<!--        aer.identity_no as patient_id_number,-->
<!--        aer.identity_type_name,-->
<!--        aer.patient_name,-->
<!--        dmpi.sex_name,-->
<!--        dmpi.birthday birthday,-->
<!--        dmpi.marital_code,-->
<!--        dmpi.marital_name,-->
<!--        dmpi.telephone,-->
<!--        dmpi.household_addr_detail,-->
<!--        dmpi.living_address,-->
<!--        dmpi.company_name,-->
<!--        dmpi.contact_name,-->
<!--        dmpi.contact_telephone,-->
<!--        dmpi.std_living_place_name,-->
<!--        formatDateTime(dmpi.create_datetime, '%Y-%m-%d %H:%M:%S') AS create_datetime,-->
<!--        formatDateTime(MAX(aer.event_datetime), '%Y-%m-%d %H:%M:%S') AS update_datetime,-->
<!--        aer.symptom_tag_list-->
<!--        FROM wuhu.ads_event_record aer left join wuhu.dim_mdm_person_info dmpi on aer.global_person_id = dmpi.global_person_id-->
<!--        <where>-->
<!--            <foreach collection="areaList" index="index" item="area" open="(" separator=" OR " close=")">-->
<!--                <if test='area.cityCode != null'>-->
<!--                    aer.std_living_city_code = #{area.cityCode}-->
<!--                </if>-->
<!--                <if test="area.districtCode != null">-->
<!--                    AND aer.std_living_district_code = #{area.districtCode}-->
<!--                </if>-->
<!--            </foreach>-->
<!--            <if test="personInfo != null">-->
<!--                AND dmpi.composite_key LIKE concat('%', #{personInfo}, '%')-->
<!--            </if>-->
<!--            <if test="startTime!= null">-->
<!--                AND aer.event_datetime >= #{startTime}::TIMESTAMP-->
<!--            </if>-->
<!--            <if test="endTime!= null">-->
<!--                AND aer.event_datetime &lt;= #{endTime}::TIMESTAMP-->
<!--            </if>-->
<!--            <if test="symptomList != null and symptomList.size() > 0">-->
<!--                and-->
<!--                <foreach collection="symptomList" item="item" index="index" open="(" close=")" separator="and">-->
<!--                    aer.symptom_tag_list LIKE concat('%', #{item}::text, '%')-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="infectionName != null">-->
<!--                AND aer.primary_diagnose_name LIKE concat('%', #{infectionName}, '%')-->
<!--            </if>-->
<!--        </where>-->
<!--        GROUP BY aer.global_person_id,-->
<!--        aer.identity_no,-->
<!--        aer.identity_type_name,-->
<!--        aer.patient_name,-->
<!--        dmpi.sex_name,-->
<!--        dmpi.birthday,-->
<!--        dmpi.marital_code,-->
<!--        dmpi.marital_name,-->
<!--        dmpi.telephone,-->
<!--        dmpi.household_addr_detail,-->
<!--        dmpi.living_address,-->
<!--        dmpi.company_name,-->
<!--        dmpi.contact_name,-->
<!--        dmpi.contact_telephone,-->
<!--        dmpi.std_living_place_name,-->
<!--        dmpi.create_datetime,-->
<!--        aer.symptom_tag_list-->
<!--        ORDER BY max(aer.event_datetime) DESC-->
<!--        LIMIT ${pageSize} OFFSET (${pageIndex} - 1) * ${pageSize}-->
<!--    </select>-->

    <select id="getNewPatientInfoByEvent" resultType="com.iflytek.cdc.edr.entity.person.DimMdmPersonInfo">
        SELECT
            agpi.global_person_id as global_person_id,
            agpi.patient_id_number as patient_id_number,
            agpi.identity_type_name as identity_type_name,
            agpi.patient_name as patient_name,
            agpi.symptom_tag_list as symptom_tag_list,
            dmpi.sex_name,
            dmpi.birthday birthday,
            dmpi.marital_code,
            dmpi.marital_name,
            dmpi.telephone,
            dmpi.household_addr_detail,
            dmpi.living_address,
            dmpi.company_name,
            dmpi.contact_name,
            dmpi.contact_telephone,
            dmpi.std_living_place_name,
            formatDateTime(dmpi.create_datetime, '%Y-%m-%d %H:%M:%S') AS create_datetime,
            formatDateTime(agpi.event_datetime ,'%Y-%m-%d %H:%M:%S') AS update_datetime
        FROM
            default.dim_mdm_person_info dmpi
        right join (
            SELECT
                aer.global_person_id as global_person_id ,
                aer.identity_no as patient_id_number,
                aer.identity_type_name,
                aer.patient_name,
                aer.symptom_tag_list,
                MAX(aer.event_datetime) as event_datetime
            FROM
                default.ads_event_record aer
            <where>
                <foreach collection="areaList" index="index" item="area" open="(" separator=" OR " close=")">
                    <if test='area.cityCode != null'>
                        aer.std_living_city_code = #{area.cityCode}
                    </if>
                    <if test="area.districtCode != null">
                        AND aer.std_living_district_code = #{area.districtCode}
                    </if>
                </foreach>
                <if test="startTime!= null">
                    AND aer.event_datetime >= #{startTime}::TIMESTAMP
                </if>
                <if test="endTime!= null">
                    AND aer.event_datetime &lt;= #{endTime}::TIMESTAMP
                </if>
                <if test="symptomList != null and symptomList.size() > 0">
                    and
                    <foreach collection="symptomList" item="item" index="index" open="(" close=")" separator="and">
                        aer.symptom_tag_list LIKE concat('%', #{item}::text, '%')
                    </foreach>
                </if>
                <if test="infectionName != null">
                    AND aer.primary_diagnose_name LIKE concat('%', #{infectionName}, '%')
                </if>
            </where>
            group by
                aer.global_person_id as global_person_id ,
                aer.identity_no as patient_id_number,
                aer.identity_type_name,
                aer.patient_name,
                aer.symptom_tag_list
        ) agpi on agpi.global_person_id = dmpi .global_person_id
        where 1=1
        <if test="personInfo != null">
            AND dmpi.composite_key LIKE concat('%', #{personInfo}, '%')
        </if>
        ORDER BY agpi.event_datetime DESC
        LIMIT ${pageSize} OFFSET (${pageIndex} - 1) * ${pageSize}
    </select>

</mapper>