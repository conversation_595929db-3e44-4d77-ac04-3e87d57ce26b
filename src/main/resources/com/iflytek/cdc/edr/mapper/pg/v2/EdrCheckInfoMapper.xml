<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.EdrCheckInfoMapper">
    <resultMap type="com.iflytek.cdc.edr.entity.outpatient.EdrCheckInfo" id="EdrCheckInfoResult">
        <result property="id"    column="id"    />
        <result property="eventId"    column="event_id"    />
        <result property="checkName"    column="check_name"    />
        <result property="applyDeptCode"    column="apply_dept_code"    />
        <result property="applyDeptName"    column="apply_dept_name"    />
        <result property="checkDatetime"    column="check_datetime"    />
        <result property="examConclusion"    column="exam_conclusion"    />
        <result property="checkSituation"    column="check_situation"    />
        <result property="option"    column="option"    />
        <result property="hisCheckId"    column="his_check_id"  />
    </resultMap>

    <sql id="selectEdrCheckInfoVo">
        select
            t0.id,
            t0.event_id ,
            t0.check_name ,
            t0.report_dept_code apply_dept_code,
            t0.report_dept_name apply_dept_name,
            t0.check_datetime,
            t0.exam_conclusion,
            t0.check_situation,
            t0.option,
            t0.his_check_id
        from dwd.dwd_ch_check_report t0
    </sql>

    <sql id="selectNewEdrCheckInfoVo">
        -- todo : medical_id, event_id
        select t0.id,
               t0.medical_id     as event_id,
               t0.exam_item_name as check_name,
               t0.exec_dept_code    apply_dept_code,
               t0.exec_dept_name    apply_dept_name,
               t0.exam_datetime  as check_datetime,
               t0.exam_concluse  as exam_conclusion,
               t0.exam_view      as check_situation,
               t0.exam_concluse as option,
            t0.examine_report_no as his_check_id,
            t0.reporter_name ,
            t0.reporter_id,
            t0.exam_class_name,
            t0.exam_part_name
        from ads.ads_mt_ris_info t0
    </sql>

    <select id="selectEdrCheckInfoByEventId" parameterType="String" resultMap="EdrCheckInfoResult">
        <include refid="selectEdrCheckInfoVo"/>
        where t0.event_id = #{eventId}
    </select>

    <select id="selectEdrCheckInfoList" parameterType="com.iflytek.cdc.edr.entity.outpatient.EdrCheckInfo" resultMap="EdrCheckInfoResult">
        <include refid="selectEdrCheckInfoVo"/>
        <where>
            <if test="eventId != null  and eventId != ''"> and t0.event_id = #{eventId}</if>
            <if test="checkName != null  and checkName != ''"> and check_name like concat('%', #{checkName}, '%')</if>
            <if test="applyDeptCode != null  and applyDeptCode != ''"> and apply_dept_code = #{applyDeptCode}</if>
            <if test="applyDeptName != null  and applyDeptName != ''"> and apply_dept_name like concat('%', #{applyDeptName}, '%')</if>
            <if test="checkDatetime != null "> and check_datetime = #{checkDatetime}</if>
            <if test="examConclusion != null  and examConclusion != ''"> and exam_conclusion = #{examConclusion}</if>
            <if test="checkSituation != null  and checkSituation != ''"> and check_situation = #{checkSituation}</if>
        </where>
    </select>

    <select id="selectEdrCheckInfoById" parameterType="String" resultMap="EdrCheckInfoResult">
        <include refid="selectEdrCheckInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectEdrCheckInfoByEventIds" resultType="com.iflytek.cdc.edr.entity.outpatient.EdrCheckInfo">
        <include refid="selectEdrCheckInfoVo"/>
        where t0.event_id in
        <foreach collection="eventIds" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectNewEdrCheckInfoById" resultType="com.iflytek.cdc.edr.entity.outpatient.EdrCheckInfo">
        <include refid="selectNewEdrCheckInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectNewEdrCheckInfoByEventId" resultType="com.iflytek.cdc.edr.entity.outpatient.EdrCheckInfo">
        -- todo : medical_id, event_id
        <include refid="selectNewEdrCheckInfoVo"/>
        where t0.medical_id = #{eventId}
        order by t0.exam_datetime DESC
    </select>

    <select id="selectNewEdrCheckInfoByEventIds" resultType="com.iflytek.cdc.edr.entity.outpatient.EdrCheckInfo">
        -- todo : medical_id, event_id
        <include refid="selectNewEdrCheckInfoVo"/>
        where t0.medical_id in
        <foreach collection="eventIds" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectNewEdrCheckInfoList" resultType="com.iflytek.cdc.edr.entity.outpatient.EdrCheckInfo">
        -- todo : medical_id, event_id
        <include refid="selectNewEdrCheckInfoVo"/>
        <where>
            <if test="eventId != null  and eventId != ''"> and t0.medical_id = #{eventId}</if>
            <if test="checkName != null  and checkName != ''"> and exam_class_name like concat('%', #{checkName}, '%')</if>
            <if test="applyDeptCode != null  and applyDeptCode != ''"> and apply_dept_code = #{applyDeptCode}</if>
            <if test="applyDeptName != null  and applyDeptName != ''"> and apply_dept_name like concat('%', #{applyDeptName}, '%')</if>
            <if test="checkDatetime != null "> and exam_datetime = #{checkDatetime}</if>
            <if test="examConclusion != null  and examConclusion != ''"> and exam_concluse = #{examConclusion}</if>
            <if test="checkSituation != null  and checkSituation != ''"> and exam_view = #{checkSituation}</if>
        </where>
    </select>


</mapper>
