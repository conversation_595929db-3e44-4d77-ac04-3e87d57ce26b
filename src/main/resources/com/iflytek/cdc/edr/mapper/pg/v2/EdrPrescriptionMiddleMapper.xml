<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.EdrPrescriptionMiddleMapper">
    <resultMap type="com.iflytek.cdc.edr.entity.outpatient.EdrPrescriptionMiddle" id="EdrPrescriptionMiddleResult">
        <result property="id"    column="id"    />
        <result property="eventId"    column="event_id"    />
        <result property="outpatientId"    column="outpatient_id"    />
        <result property="prescriptionListTypeCode"    column="prescription_list_type_code"    />
        <result property="decoctMethod"    column="decoct_method"    />
        <result property="tcmrxDs"    column="tcmrx_ds"    />
        <result property="tcmNum"    column="tcm_num"    />
        <result property="treat"    column="treat"    />
        <result property="treatDetail"    column="treat_detail"    />
        <result property="applyDeptCode"    column="apply_dept_code"    />
        <result property="applyDeptName"    column="apply_dept_name"    />
        <result property="applyDocName"    column="apply_doc_name"    />
        <result property="drugName"    column="drug_name"    />
        <result property="drugSpec"    column="drug_spec"    />
        <result property="onceDosage"    column="once_dosage"    />
        <result property="drugUsewayName"    column="drug_useway_name"    />
        <result property="drugFrequencyName"    column="drug_frequency_name"    />
        <result property="totalNum"    column="total_num"    />
        <result property="useDays"    column="use_days"    />
    </resultMap>

    <sql id="selectEdrPrescriptionMiddleVo">
        select
            t0.id,
            t0.event_id,
            t0.outpatient_id,
            t0.prescription_list_type_code,
            t0.decoct_method,
            t0.tcmrx_ds,
            t0.tcm_num,
            t0.treat,
            t0.treat_detail,
            t0.apply_dept_code,
            t0.apply_dept_name,
            t0.apply_doc_name,
            t0.drug_name ,
            t0.drug_spec ,
            CONCAT(cast(t0.once_dosage as VARCHAR ), t0.once_dosage_unit_code)  once_dosage ,--t1.once_dosage 单次使用量
            t0.drug_useway_name ,-- 用法
            t0.drug_frequency_name ,-- 频次
            cast(t0.total_num as VARCHAR )  total_num,  -- 总量
            t0.use_days  -- 天数
        from dwd.dwd_ch_prescription t0

    </sql>

    <sql id="selectNewEdrPrescriptionMiddleVo">
        select t0.id,
               t0.event_id,
               t0.serial_no as outpatient_id,
               t0.prescription_class_code as prescription_list_type_code,
               t0.cm_piece_boil_desc as decoct_method,
               t0.cm_piece_prescription_desc as tcmrx_ds,
               t0.cm_piece_num as tcm_num,
               t0.herbal_treat as treat,
               t0.herbal_treat_detail as treat_detail,
               t0.prescribe_dept_code as apply_dept_code,
               t0.prescribe_dept_name as apply_dept_name,
               t0.prescribe_doc_name as apply_doc_name,
               t0.drug_name , -- 药物通用名称
               coalesce(t0.drug_spec_name,t0.drug_spec_code) as drug_spec , -- 药物规格代码
               CONCAT(cast(t0.drug_use_dose as VARCHAR ), t0.drug_use_dose_unit) once_dosage ,--t1.once_dosage 单次使用量
               t0.usage_name as drug_useway_name ,-- 用法
               t0.freq_name as drug_frequency_name ,-- 频次
               CONCAT(cast(t0.drug_use_total_dose as VARCHAR ), t0.drug_use_dose_unit) total_num, -- 总量
               t0.medication_days as use_days, -- 天数
               t0.prescription_type_code -- 处方类型代码
            from ads.ads_outpat_pre_info t0
    </sql>

    <select id="selectEdrPrescriptionMiddleByEventId" parameterType="String" resultMap="EdrPrescriptionMiddleResult">
        <include refid="selectEdrPrescriptionMiddleVo"/>
        <where>
         t0.event_id = #{eventId}
        </where>
    </select>

    <select id="selectEdrPrescriptionMiddleById" parameterType="String" resultMap="EdrPrescriptionMiddleResult">
        <include refid="selectEdrPrescriptionMiddleVo"/>
        where id = #{id}
    </select>

    <select id="selectNewEdrPrescriptionMiddleByEventId"
            resultType="com.iflytek.cdc.edr.entity.outpatient.EdrPrescriptionMiddle">
        <include refid="selectNewEdrPrescriptionMiddleVo"/>
        <where>
            -- todo 这里的eventId实际上为medicalId
            t0.medical_id = #{eventId}
        </where>
    </select>

</mapper>