<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.AdsMtRisInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.edr.entity.AdsMtRisInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_mt_ris_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="global_person_id" jdbcType="VARCHAR" property="globalPersonId" />
    <result column="examine_report_no" jdbcType="VARCHAR" property="examineReportNo" />
    <result column="report_title" jdbcType="VARCHAR" property="reportTitle" />
    <result column="report_type_code" jdbcType="VARCHAR" property="reportTypeCode" />
    <result column="report_type_name" jdbcType="VARCHAR" property="reportTypeName" />
    <result column="tech_inside_no" jdbcType="VARCHAR" property="techInsideNo" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="apply_org_id" jdbcType="VARCHAR" property="applyOrgId" />
    <result column="apply_org_name" jdbcType="VARCHAR" property="applyOrgName" />
    <result column="apply_dept_code" jdbcType="VARCHAR" property="applyDeptCode" />
    <result column="apply_dept_name" jdbcType="VARCHAR" property="applyDeptName" />
    <result column="apply_doc_code" jdbcType="VARCHAR" property="applyDocCode" />
    <result column="apply_doc_name" jdbcType="VARCHAR" property="applyDocName" />
    <result column="exam_report_desc" jdbcType="VARCHAR" property="examReportDesc" />
    <result column="exam_org_id" jdbcType="VARCHAR" property="examOrgId" />
    <result column="exam_org_name" jdbcType="VARCHAR" property="examOrgName" />
    <result column="exec_dept_code" jdbcType="VARCHAR" property="execDeptCode" />
    <result column="exec_dept_name" jdbcType="VARCHAR" property="execDeptName" />
    <result column="exec_receive_time" jdbcType="TIMESTAMP" property="execReceiveTime" />
    <result column="executor_code" jdbcType="VARCHAR" property="executorCode" />
    <result column="executor" jdbcType="VARCHAR" property="executor" />
    <result column="exam_class_code" jdbcType="VARCHAR" property="examClassCode" />
    <result column="exam_class_name" jdbcType="VARCHAR" property="examClassName" />
    <result column="exam_item_code" jdbcType="VARCHAR" property="examItemCode" />
    <result column="exam_item_name" jdbcType="VARCHAR" property="examItemName" />
    <result column="instrument_code" jdbcType="VARCHAR" property="instrumentCode" />
    <result column="instrument" jdbcType="VARCHAR" property="instrument" />
    <result column="exam_datetime" jdbcType="TIMESTAMP" property="examDatetime" />
    <result column="exam_view" jdbcType="VARCHAR" property="examView" />
    <result column="exam_concluse" jdbcType="VARCHAR" property="examConcluse" />
    <result column="report_time" jdbcType="TIMESTAMP" property="reportTime" />
    <result column="reporter_id" jdbcType="VARCHAR" property="reporterId" />
    <result column="reporter_name" jdbcType="VARCHAR" property="reporterName" />
    <result column="report_audit_time" jdbcType="TIMESTAMP" property="reportAuditTime" />
    <result column="report_auditor_code" jdbcType="VARCHAR" property="reportAuditorCode" />
    <result column="report_auditor_name" jdbcType="VARCHAR" property="reportAuditorName" />
    <result column="report_call_path" jdbcType="VARCHAR" property="reportCallPath" />
    <result column="report_adnexa_type" jdbcType="VARCHAR" property="reportAdnexaType" />
    <result column="report_adnexa_name" jdbcType="VARCHAR" property="reportAdnexaName" />
    <result column="report_adnexa_path" jdbcType="VARCHAR" property="reportAdnexaPath" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="person_info_id" jdbcType="VARCHAR" property="personInfoId" />
    <result column="patient_name" jdbcType="VARCHAR" property="patientName" />
    <result column="patient_sex_code" jdbcType="VARCHAR" property="patientSexCode" />
    <result column="patient_sex_desc" jdbcType="VARCHAR" property="patientSexDesc" />
    <result column="patient_birthday" jdbcType="DATE" property="patientBirthday" />
    <result column="identity_type_code" jdbcType="VARCHAR" property="identityTypeCode" />
    <result column="identity_type_name" jdbcType="VARCHAR" property="identityTypeName" />
    <result column="identity_no" jdbcType="VARCHAR" property="identityNo" />
    <result column="patient_age" jdbcType="VARCHAR" property="patientAge" />
    <result column="patient_age_unit" jdbcType="VARCHAR" property="patientAgeUnit" />
    <result column="health_card_id" jdbcType="VARCHAR" property="healthCardId" />
    <result column="patient_source" jdbcType="VARCHAR" property="patientSource" />
    <result column="visit_type_code" jdbcType="VARCHAR" property="visitTypeCode" />
    <result column="frg_medical_unit" jdbcType="VARCHAR" property="frgMedicalUnit" />
    <result column="ward_code" jdbcType="VARCHAR" property="wardCode" />
    <result column="ward_name" jdbcType="VARCHAR" property="wardName" />
    <result column="dept_room" jdbcType="VARCHAR" property="deptRoom" />
    <result column="bedno" jdbcType="VARCHAR" property="bedno" />
    <result column="exam_part_code" jdbcType="VARCHAR" property="examPartCode" />
    <result column="exam_part_name" jdbcType="VARCHAR" property="examPartName" />
    <result column="dicom_studyuids" jdbcType="VARCHAR" property="dicomStudyuids" />
    <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
    <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime" />
    <result column="updator_id" jdbcType="VARCHAR" property="updatorId" />
    <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime" />
    <result column="enabled" jdbcType="VARCHAR" property="enabled" />
    <result column="deleted" jdbcType="VARCHAR" property="deleted" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="medical_id" jdbcType="VARCHAR" property="medicalId" />
    <result column="visit_time" jdbcType="TIMESTAMP" property="visitTime" />
    <result column="visit_type_name" jdbcType="VARCHAR" property="visitTypeName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, global_person_id, examine_report_no, report_title, report_type_code, report_type_name, 
    tech_inside_no, order_id, org_id, org_code, org_name, apply_org_id, apply_org_name, 
    apply_dept_code, apply_dept_name, apply_doc_code, apply_doc_name, exam_report_desc, 
    exam_org_id, exam_org_name, exec_dept_code, exec_dept_name, exec_receive_time, executor_code, 
    executor, exam_class_code, exam_class_name, exam_item_code, exam_item_name, instrument_code, 
    instrument, exam_datetime, exam_view, exam_concluse, report_time, reporter_id, reporter_name, 
    report_audit_time, report_auditor_code, report_auditor_name, report_call_path, report_adnexa_type, 
    report_adnexa_name, report_adnexa_path, serial_no, person_info_id, patient_name, 
    patient_sex_code, patient_sex_desc, patient_birthday, identity_type_code, identity_type_name, 
    identity_no, patient_age, patient_age_unit, health_card_id, patient_source, visit_type_code, 
    frg_medical_unit, ward_code, ward_name, dept_room, bedno, exam_part_code, exam_part_name, 
    dicom_studyuids, creator_id, create_datetime, updator_id, update_datetime, enabled, 
    deleted, source_id, event_id, etl_create_datetime, etl_update_datetime, medical_id, 
    visit_time, visit_type_name
  </sql>

  <select id="getRisInfoByPatient" resultMap="BaseResultMap">
    select distinct on (event_id, examine_report_no) <include refid="Base_Column_List"/>
    from ads.ads_mt_ris_info t
    <where>
      <if test="param.startTime != null and param.startTime != ''">
        and t.visit_time >= #{param.startTime}::timestamp
      </if>
      <if test="param.endTime != null and param.endTime != ''">
        and t.visit_time  <![CDATA[<=]]>  #{param.endTime}::timestamp
      </if>
      <if test="param.patientName">
        and t.patient_name = #{param.patientName,jdbcType=VARCHAR}
      </if>
      <if test="param.idCardNo != null">
        and t.identity_no = #{param.idCardNo,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
  <select id="getRisInfoList" resultType="com.iflytek.cdc.edr.vo.RisInfoVO">
      select t1.org_name as orgName,
      t1.visit_type_name as visitTypeName,
      CASE WHEN t1.visit_type_code = '4' THEN '3' ELSE t1.visit_type_code END as visitTypeCode,
      t1.visit_time as visitTime,
      t1.serial_no as serialNo,
      t1.patient_name as patientName,
      t1.patient_sex_desc as patientSexDesc,
      concat(t1.patient_age, t1.patient_age_unit) as patientAge,
      t1.exam_datetime as examDateTime,
      t1.examine_report_no as examineReportNo,
      t1.exam_item_name as itemName,
      t1.exam_concluse as examConcluse,
      t1.disease_name as diseaseName,
      t1.is_exist_report_card as isExistReportCard,
      t1.recent_report_card_datetime as reportCardDatetime,
      t1.medical_id as medicalId,
      t1.report_card_type AS reportCardType,
      t1.report_card_type AS reportCardTypeName
      from ads.ads_mt_ris_info t1 LEFT JOIN dim.dim_organization_info t2 on t1.org_id = t2.org_id
      WHERE t1.report_time >= #{startDate}::timestamp
      AND t1.report_time &lt; #{endDate}::timestamp + INTERVAL '1 day'
      <if test="orgList != null and orgList.size() &gt; 0">
          <foreach collection="orgList" index="index" item="orgId" open=" AND (" separator=" OR " close=")">
              t1.org_id = #{orgId}
          </foreach>
      </if>
      <if test="provinceCode != null and provinceCode != ''">
          AND t2.province_code = #{provinceCode}
      </if>
      <if test="cityCode != null and cityCode != ''">
          AND t2.city_code = #{cityCode}
      </if>
      <if test="districtCode != null and districtCode != ''">
          AND t2.district_code = #{districtCode}
      </if>
      <if test="provinceCodes != null and provinceCodes.size() > 0">
          and t2.province_code in
          <foreach collection="provinceCodes" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="cityCodes != null and cityCodes.size() > 0">
          and t2.city_code in
          <foreach collection="cityCodes" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="districtCodes != null and districtCodes.size() > 0">
          and t2.district_code in
          <foreach collection="districtCodes" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="patientName != null and patientName != ''">
          AND t1.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
      </if>
      <if test="sourceType != null and sourceType != ''">
          AND SUBSTR(t1.source_id, 1, 3) = #{sourceType}
      </if>
      <if test="patientSourceCode != null and patientSourceCode != ''">
          AND t1.visit_type_code = #{patientSourceCode}
      </if>
      <if test="isExistReportCard != null and isExistReportCard != ''">
          AND t1.is_exist_report_card = #{isExistReportCard}
          <if test="isExistReportCard == 1 and reportCardTypeList != null and reportCardTypeList.size() > 0">
              <foreach collection="reportCardTypeList" item="reportCardType" index="index" open=" AND (" separator=" OR " close=")">
                  t1.report_card_type LIKE CONCAT('%', #{reportCardType}, '%')
              </foreach>
          </if>
      </if>
      <if test="itemName != null and itemName != ''">
          AND t1.exam_item_name  LIKE concat('%', #{itemName, jdbcType=VARCHAR} ,'%')
      </if>
      <if test="diseaseCode != null and diseaseCode.size() &gt; 0">
          <foreach collection="diseaseCode" index="index" item="disease" open=" AND (" separator=" OR " close=")">
              t1.disease_code like concat(#{disease}, '%')
          </foreach>
      </if>
      <if test="customQueryParamList != null and customQueryParamList.size() > 0">
          AND (
          <trim prefixOverrides="AND|OR">
              <foreach collection="customQueryParamList" item="queryParam" index="index">
                  <choose>
                      <when test="queryParam.logicalType == 'and'"> AND </when>
                      <when test="queryParam.logicalType == 'or'"> OR </when>
                  </choose>
                  <choose>
                      <when test="queryParam.paramName == 'age'">
                          <choose>
                              <when test="queryParam.compareType == 'eq'"> t1.patient_age = #{queryParam.paramValue} </when>
                              <when test="queryParam.compareType == 'ne'"> (t1.patient_age != #{queryParam.paramValue} OR t1.patient_age IS NULL) </when>
                              <when test="queryParam.compareType == 'gt'"> (CASE WHEN t1.patient_age ~ '^[0-9\.]+$' THEN t1.patient_age::INTEGER END) <![CDATA[ > ]]> #{queryParam.paramValue}::INTEGER </when>
                              <when test="queryParam.compareType == 'lt'"> (CASE WHEN t1.patient_age ~ '^[0-9\.]+$' THEN t1.patient_age::INTEGER END) <![CDATA[ < ]]> #{queryParam.paramValue}::INTEGER </when>
                          </choose>
                          <if test="queryParam.ageUnit != null and queryParam.ageUnit != ''">
                              AND t1.patient_age_unit = #{queryParam.ageUnit}
                          </if>
                      </when>
                      <when test="queryParam.paramName == 'disease'">
                          <choose>
                              <when test="queryParam.compareType == 'eq'"> t1.disease_code = #{queryParam.paramValue} </when>
                              <when test="queryParam.compareType == 'ne'"> (t1.disease_code != #{queryParam.paramValue} OR t1.disease_code IS NULL) </when>
                              <when test="queryParam.compareType == 'co'"> t1.disease_code LIKE CONCAT(#{queryParam.paramValue}, '%') </when>
                              <when test="queryParam.compareType == 'nc'"> (t1.disease_code NOT LIKE CONCAT(#{queryParam.paramValue}, '%') OR t1.disease_code IS NULL) </when>
                          </choose>
                      </when>
                      <when test="queryParam.paramName == 'item'">
                          <choose>
                              <when test="queryParam.compareType == 'eq'"> t1.exam_item_name = #{queryParam.paramValue} </when>
                              <when test="queryParam.compareType == 'ne'"> (t1.exam_item_name != #{queryParam.paramValue} OR t1.exam_item_name IS NULL) </when>
                              <when test="queryParam.compareType == 'co'"> t1.exam_item_name LIKE CONCAT('%', #{queryParam.paramValue} ,'%') </when>
                              <when test="queryParam.compareType == 'nc'"> (t1.exam_item_name NOT LIKE CONCAT('%', #{queryParam.paramValue}, '%') OR t1.exam_item_name IS NULL) </when>
                          </choose>
                      </when>
                      <when test="queryParam.paramName == 'conclusion'">
                          <choose>
                              <when test="queryParam.compareType == 'eq'"> t1.exam_concluse = #{queryParam.paramValue} </when>
                              <when test="queryParam.compareType == 'ne'"> (t1.exam_concluse != #{queryParam.paramValue} OR t1.exam_concluse IS NULL) </when>
                              <when test="queryParam.compareType == 'co'"> t1.exam_concluse LIKE CONCAT('%', #{queryParam.paramValue} ,'%') </when>
                              <when test="queryParam.compareType == 'nc'"> (t1.exam_concluse NOT LIKE CONCAT('%', #{queryParam.paramValue}, '%') OR t1.exam_concluse IS NULL) </when>
                          </choose>
                      </when>
                  </choose>
              </foreach>
          </trim>
          )
      </if>
      ORDER BY coalesce(t1.create_datetime, '1900-01-01'::timestamp) DESC
  </select>
    <select id="getExamClassDict" resultType="com.iflytek.cdc.edr.dto.DictDto">
        SELECT dict_value_code, dict_value_name
        FROM dim.dim_dict
        WHERE  dict_code = '404129'
    </select>
    <select id="getExamPartDict" resultType="com.iflytek.cdc.edr.dto.DictDto">
        SELECT dict_value_code, dict_value_name
        FROM dim.dim_dict
        WHERE dict_code = '404119'
    </select>
    <select id="getRisInfoByMedicalId" resultType="com.iflytek.cdc.edr.entity.AdsMtRisInfo">
        select * from ads.ads_mt_ris_info where medical_id = #{medicalId}
    </select>
    <select id="getRisInfoListCount" resultType="java.lang.Integer">
        select count(1)
        from ads.ads_mt_ris_info t1 LEFT JOIN dim.dim_organization_info t2 on t1.org_id = t2.org_id
        WHERE t1.report_time >= #{startDate}::timestamp
        AND t1.report_time &lt; #{endDate}::timestamp + INTERVAL '1 day'
        <if test="orgList != null and orgList.size() &gt; 0">
            <foreach collection="orgList" index="index" item="orgId" open=" AND (" separator=" OR " close=")">
                t1.org_id = #{orgId}
            </foreach>
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            AND t2.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND t2.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            AND t2.district_code = #{districtCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size() > 0">
            and t2.province_code in
            <foreach collection="provinceCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() > 0">
            and t2.city_code in
            <foreach collection="cityCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() > 0">
            and t2.district_code in
            <foreach collection="districtCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="patientName != null and patientName != ''">
            AND t1.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND SUBSTR(t1.source_id, 1, 3) = #{sourceType}
        </if>
        <if test="patientSourceCode != null and patientSourceCode != ''">
            AND t1.visit_type_code = #{patientSourceCode}
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND t1.is_exist_report_card = #{isExistReportCard}
            <if test="isExistReportCard == 1 and reportCardTypeList != null and reportCardTypeList.size() > 0">
                <foreach collection="reportCardTypeList" item="reportCardType" index="index" open=" AND (" separator=" OR " close=")">
                    t1.report_card_type LIKE CONCAT('%', #{reportCardType}, '%')
                </foreach>
            </if>
        </if>
        <if test="itemName != null and itemName != ''">
            AND t1.exam_item_name  LIKE concat('%', #{itemName, jdbcType=VARCHAR} ,'%')
        </if>
        <if test="diseaseCode != null and diseaseCode.size() &gt; 0">
            <foreach collection="diseaseCode" index="index" item="disease" open=" AND (" separator=" OR " close=")">
                t1.disease_code = #{disease}
            </foreach>
        </if>
        <if test="customQueryParamList != null and customQueryParamList.size() > 0">
            AND (
            <trim prefixOverrides="AND|OR">
                <foreach collection="customQueryParamList" item="queryParam" index="index">
                    <choose>
                        <when test="queryParam.logicalType == 'and'"> AND </when>
                        <when test="queryParam.logicalType == 'or'"> OR </when>
                    </choose>
                    <choose>
                        <when test="queryParam.paramName == 'age'">
                            <choose>
                                <when test="queryParam.compareType == 'eq'"> t1.patient_age = #{queryParam.paramValue} </when>
                                <when test="queryParam.compareType == 'ne'"> (t1.patient_age != #{queryParam.paramValue} OR t1.patient_age IS NULL) </when>
                                <when test="queryParam.compareType == 'gt'"> (CASE WHEN t1.patient_age ~ '^[0-9\.]+$' THEN t1.patient_age::INTEGER END) <![CDATA[ > ]]> #{queryParam.paramValue}::INTEGER </when>
                                <when test="queryParam.compareType == 'lt'"> (CASE WHEN t1.patient_age ~ '^[0-9\.]+$' THEN t1.patient_age::INTEGER END) <![CDATA[ < ]]> #{queryParam.paramValue}::INTEGER </when>
                            </choose>
                            <if test="queryParam.ageUnit != null and queryParam.ageUnit != ''">
                                AND t1.patient_age_unit = #{queryParam.ageUnit}
                            </if>
                        </when>
                        <when test="queryParam.paramName == 'disease'">
                            <choose>
                                <when test="queryParam.compareType == 'eq'"> t1.disease_code = #{queryParam.paramValue} </when>
                                <when test="queryParam.compareType == 'ne'"> (t1.disease_code != #{queryParam.paramValue} OR t1.disease_code IS NULL) </when>
                                <when test="queryParam.compareType == 'co'"> t1.disease_code LIKE CONCAT(#{queryParam.paramValue}, '%') </when>
                                <when test="queryParam.compareType == 'nc'"> (t1.disease_code NOT LIKE CONCAT(#{queryParam.paramValue}, '%') OR t1.disease_code IS NULL) </when>
                            </choose>
                        </when>
                        <when test="queryParam.paramName == 'item'">
                            <choose>
                                <when test="queryParam.compareType == 'eq'"> t1.exam_item_name = #{queryParam.paramValue} </when>
                                <when test="queryParam.compareType == 'ne'"> (t1.exam_item_name != #{queryParam.paramValue} OR t1.exam_item_name IS NULL) </when>
                                <when test="queryParam.compareType == 'co'"> t1.exam_item_name LIKE CONCAT('%', #{queryParam.paramValue} ,'%') </when>
                                <when test="queryParam.compareType == 'nc'"> (t1.exam_item_name NOT LIKE CONCAT('%', #{queryParam.paramValue}, '%') OR t1.exam_item_name IS NULL) </when>
                            </choose>
                        </when>
                        <when test="queryParam.paramName == 'conclusion'">
                            <choose>
                                <when test="queryParam.compareType == 'eq'"> t1.exam_concluse = #{queryParam.paramValue} </when>
                                <when test="queryParam.compareType == 'ne'"> (t1.exam_concluse != #{queryParam.paramValue} OR t1.exam_concluse IS NULL) </when>
                                <when test="queryParam.compareType == 'co'"> t1.exam_concluse LIKE CONCAT('%', #{queryParam.paramValue} ,'%') </when>
                                <when test="queryParam.compareType == 'nc'"> (t1.exam_concluse NOT LIKE CONCAT('%', #{queryParam.paramValue}, '%') OR t1.exam_concluse IS NULL) </when>
                            </choose>
                        </when>
                    </choose>
                </foreach>
            </trim>
            )
        </if>
    </select>

</mapper>