<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.DwdChMedicalRecordMapper">


    <select id="findByMedicalId" resultType="com.iflytek.cdc.edr.dto.epi.MedicalInfoResult">
        select t1.medical_id,
               t1.event_id,
               t1.his_medical_id,
               t1.patient_id,
               t1.insurance_card,
               t1.patient_id_number,
               t1.patient_name,
               t1.sex_code,
               t1.sex_name,
               t1.age,
               t1.age_unit_name,
               t1.telephone,
               t1.company_name,
               t1.job,
               t1.current_addr_detail,
               t1.org_id,
               t1.org_code,
               t1.org_name,
               t1.dept_id,
               t1.dept_code,
               t1.dept_name,
               t1.visit_doc_id,
               t1.visit_doc_name,
               t1.visit_doc_telephone,
               t1.action_in_chief,
               t1.medical_history_now,
               t1.medical_history,
               t1.personal_history,
               t1.marital_birth_history,
               t1.menstrual_history,
               t1.family_history,
               t1.allergy_history,
               t1.vaccinate_history,
               t1.infecttion_desc,
               t1.tcm_observe_desc,
               t1.tongue_picture,
               t1.dbp,
               t1.mbp,
               t1.sbp,
               t1.fbs,
               t1.two_hpbg,
               t1.temperature,
               t1.breath_rate,
               t1.blood_oxygen,
               t1.heart_rate,
               t1.weight,
               t1.height,
               t1.pulse_rate,
               t1.exam_desc,
               t1.pos_aux_examin,
               t1.diagnose_type,
               t1.diagnose_code,
               t1.diagnose_name,
               t1.medical_name,
               t1.medical_type_code,
               t1.medical_type_name,
               t1.medical_content,
               t1.visit_datetime,
               t1.start_sick_datetime,
               t1.event_datetime,
               t1.etl_create_datetime,
               t1.etl_update_datetime,
               t1.source_id,
               t1.data_src,
               t1.global_person_id,
               t1.medical_create_datetime,
               t1.medical_update_datetime,
               t1.uap_user_id,
               t2.province_code,
               t2.province_name,
               t2.city_code,
               t2.city_name,
               t2.district_code,
               t2.district_name,
               t2.township_hospital_id,
               t2.township_hospital_name,
               t3.symptom_tag_list,
               t3.onset_time_list,
               t3.cause_list,
               t3.examine_list,
               t3.diagnose_name,
               t3.main_suit_symptom_tag_list,
               t3.physical_sign_list,
               t3.epidemiology_history_list
        from dwd.dwd_ch_medical_record t1
                 join dim.dim_medical_organization_info t2 on t1.org_id = t2.org_id
                 left join dwd.dwd_syndrome_medical_symptom_tag t3 on t1.medical_id = t3.medical_id
        where t1.medical_id = #{medicalId,jdbcType=VARCHAR}
    </select>
    <select id="findByNameAndIdCardNo" resultType="com.iflytek.cdc.edr.dto.epi.MedicalInfoResult">
        select t1.medical_id,
               t1.event_id,
               t1.his_medical_id,
               t1.patient_id,
               t1.insurance_card,
               t1.patient_id_number,
               t1.patient_name,
               t1.sex_code,
               t1.sex_name,
               t1.age,
               t1.age_unit_name,
               t1.telephone,
               t1.company_name,
               t1.job,
               t1.current_addr_detail,
               t1.org_id,
               t1.org_code,
               t1.org_name,
               t1.dept_id,
               t1.dept_code,
               t1.dept_name,
               t1.visit_doc_id,
               t1.visit_doc_name,
               t1.visit_doc_telephone,
               t1.action_in_chief,
               t1.medical_history_now,
               t1.medical_history,
               t1.personal_history,
               t1.marital_birth_history,
               t1.menstrual_history,
               t1.family_history,
               t1.allergy_history,
               t1.vaccinate_history,
               t1.infecttion_desc,
               t1.tcm_observe_desc,
               t1.tongue_picture,
               t1.dbp,
               t1.mbp,
               t1.sbp,
               t1.fbs,
               t1.two_hpbg,
               t1.temperature,
               t1.breath_rate,
               t1.blood_oxygen,
               t1.heart_rate,
               t1.weight,
               t1.height,
               t1.pulse_rate,
               t1.exam_desc,
               t1.pos_aux_examin,
               t1.diagnose_type,
               t1.diagnose_code,
               t1.diagnose_name,
               t1.medical_name,
               t1.medical_type_code,
               t1.medical_type_name,
               t1.medical_content,
               t1.visit_datetime,
               t1.start_sick_datetime,
               t1.event_datetime,
               t1.etl_create_datetime,
               t1.etl_update_datetime,
               t1.source_id,
               t1.data_src,
               t1.global_person_id,
               t1.medical_create_datetime,
               t1.medical_update_datetime,
               t1.uap_user_id,
               t2.province_code,
               t2.province_name,
               t2.city_code,
               t2.city_name,
               t2.district_code,
               t2.district_name,
               t2.township_hospital_id,
               t2.township_hospital_name,
               t3.symptom_tag_list,
               t3.onset_time_list,
               t3.cause_list,
               t3.examine_list,
               t3.diagnose_name,
               t3.main_suit_symptom_tag_list,
               t3.physical_sign_list,
               t3.epidemiology_history_list
        from dwd.dwd_ch_medical_record t1
                 join dim.dim_medical_organization_info t2 on t1.org_id = t2.org_id
                 left join dwd.dwd_syndrome_medical_symptom_tag t3 on t1.medical_id = t3.medical_id
        where t1.patient_name = #{patientName,jdbcType=VARCHAR}
          and t1.patient_id_number = #{idCardNo,jdbcType=VARCHAR}
        order by t1.medical_create_datetime desc limit 1
    </select>
    <select id="findByCaseSearchDto" resultType="com.iflytek.cdc.edr.dto.epi.CaseDTO">
        select
        t1.medical_id as sourceKey,
        t1.patient_name as name,
        t1.patient_id as patientId,
        t1.age as age,
        t1.patient_id_number as idCardNum,
        t1.telephone as phone,
        t1.diagnose_name as diag,
        t1.medical_create_datetime as fullDate,
        t1.visit_datetime as outpatientTime,
        t1.company_name as company,
        mi.address_province_code as provinceCode,
        mi.address_province_name as provinceName,
        mi.address_city_code as cityCode,
        mi.address_city_name as cityName,
        mi.address_district_code as districtCode,
        mi.address_district_name as districtName,
        t1.current_addr_detail as address,
        t1.start_sick_datetime as onsetDate,
        t1.diagnose_create_datetime as diagnosisDateTime,
        t1.visit_doc_id as visitDocId,
        t1.visit_doc_name as visitDocName,
        t1.visit_doc_telephone as visitDocPhone,
        t1.org_id as visitOrgId,
        t1.org_name as visitOrgName,
        t2.org_longitude as orgLongitude,
        t2.org_latitude as orgLatitude,
        t1.exam_desc as checkupOther,
        t1.action_in_chief as mainSuit,
        t1.medical_history_now as illnessHistory,
        t1.medical_history as previousHistory,
        t1.pos_aux_examin as auxExam,
        t3.symptom_tag_list as symptomName,
        t1.org_name as source,
        t1.source_id as sourceId,
        t1.data_src as dataSrc
        from dwd.dwd_ch_medical_record t1
        join dim.dim_medical_organization_info t2 on t1.org_id = t2.org_id
        left join dim.dim_mdm_person_info dp on t1.global_person_id = dp.global_person_id
        left join dim.dim_mr_address_standard mi on dp.std_living_address_code = mi.address_area_code
        left join dwd.dwd_syndrome_medical_symptom_tag t3 on t1.medical_id = t3.medical_id
        where 1=1
        <if test="name != null">
            and t1.patient_name = #{name,jdbcType=VARCHAR}
        </if>
        <if test="idCardNum != null">
            and t1.patient_id_number = #{idCardNum,jdbcType=VARCHAR}
        </if>
        <if test="startTime != null">
            and t1.visit_datetime <![CDATA[>=]]> #{startTime,jdbcType=DATE}
        </if>
        <if test="endTime != null">
            and t1.visit_datetime <![CDATA[<=]]> #{endTime,jdbcType=DATE}
        </if>
        <if test="searchKey != null">
            and t3.symptom_tag_list like concat('%',#{searchKey,jdbcType=VARCHAR}::varchar,'%')
        </if>
        <if test="sourceId != null">
            and t1.org_id = #{sourceId,jdbcType=VARCHAR}
        </if>
    </select>


    <select id="findNewByMedicalId" resultType="com.iflytek.cdc.edr.dto.epi.MedicalInfoResult">
        select t1.medical_id,
               t1.event_id,
               t1.serial_no as his_medical_id,
               t1.person_info_id as patient_id,
               t1.insurance_card,
               t1.identity_number as patient_id_number,
               t1.patient_name,
               t1.patient_sex_code as sex_code,
               t1.patient_sex_desc as sex_name,
               t1.patient_age as age,
               t1.patient_age_unit as age_unit_name,
               t1.phone_no as telephone,
               t1.company_name,
               t1.job,
               t1.now_addr as current_addr_detail,
               t1.org_id,
               t1.org_code,
               t1.org_name,
               -- t1.dept_id,
               t1.dept_code,
               t1.dept_name,
               t1.doctor_code as visit_doc_id,
               t1.doctor_name as visit_doc_name,
               <!--todo 无就诊医师手机号字段-->
               -- t1.visit_doc_telephone,
               t1.action_in_chief,
               t1.medical_history_now,
               t1.medical_history,
               t1.personal_history,
               t1.marital_birth_history,
               t1.menstrual_history,
               t1.family_history,
               t1.allergy_history_desc as allergy_history,
               t1.vaccinate_history,
               t1.infecttion_desc,
               t1.tcm_four_diag_observe as tcm_observe_desc,
               t1.tonguepicture as tongue_picture,
               t1.diastolic_pressure as dbp,
               t1.mean_pressure as mbp,
               t1.systolic_pressure as sbp,
               t1.fasting_glucose as fbs,
               t1.postprandial_glucose as two_hpbg,
               t1.temperature,
               t1.breath_freq as breath_rate,
               t1.blood_oxygen,
               t1.heart_rate,
               t1.weight,
               t1.height,
               t1.pulse as pulse_rate,
               t1.checkup_other as exam_desc,
               t1.positive_sign_ds as pos_aux_examin,
               -- t1.diagnose_type,
               t1.diagnose_type_code as diagnose_code,
               t1.diagnose_type_name as diagnose_name,
               --t1.medical_name,
               t1.mr_type_code as medical_type_code,
               t1.mr_type_desc as medical_type_name,
               t1.medical_content,
               t1.visit_time as visit_datetime,
               t1.attk_date_time as start_sick_datetime,
               t1.event_datetime,
               t1.etl_create_datetime,
               t1.etl_update_datetime,
               t1.source_id,
               t1.visit_type_code as data_src,
               t1.global_person_id,
               t1.create_datetime as medical_create_datetime,
               t1.update_datetime as medical_update_datetime,
               <!-- 删除该字段 -->
               -- t1.uap_user_id,
               t2.province_code,
               t2.province_name,
               t2.city_code,
               t2.city_name,
               t2.district_code,
               t2.district_name,
               t2.township_hospital_id,
               t2.township_hospital_name,
               t3.symptom_tag_list,
               t3.onset_time_list,
               t3.cause_list,
               t3.examine_list,
               t3.diagnose_name,
               t3.main_suit_symptom_tag_list,
               t3.physical_sign_list,
               t3.epidemiology_history_list
        from ads.ads_outpat_medical_records t1
                 join dim.dim_medical_organization_info t2 on t1.org_id = t2.org_id
                 left join ads.v_syndrome_medical_symptom_tag t3 on t1.medical_id = t3.medical_id
        where t1.medical_id = #{medicalId,jdbcType=VARCHAR}
    </select>

    <select id="findNewByNameAndIdCardNo" resultType="com.iflytek.cdc.edr.dto.epi.MedicalInfoResult">
        select t1.medical_id,
        t1.event_id,
        t1.serial_no as his_medical_id,
        t1.person_info_id as patient_id,
        t1.insurance_card,
        t1.identity_number as patient_id_number,
        t1.patient_name,
        t1.patient_sex_code as sex_code,
        t1.patient_sex_desc as sex_name,
        t1.patient_age as age,
        t1.patient_age_unit as age_unit_name,
        t1.phone_no as telephone,
        t1.company_name,
        t1.job,
        t1.now_addr as current_addr_detail,
        t1.org_id,
        t1.org_code,
        t1.org_name,
        -- t1.dept_id,
        t1.dept_code,
        t1.dept_name,
        t1.doctor_code as visit_doc_id,
        t1.doctor_name as visit_doc_name,
        <!--todo 无就诊医师手机号字段-->
        -- t1.visit_doc_telephone,
        t1.action_in_chief,
        t1.medical_history_now,
        t1.medical_history,
        t1.personal_history,
        t1.marital_birth_history,
        t1.menstrual_history,
        t1.family_history,
        t1.allergy_history_desc as allergy_history,
        t1.vaccinate_history,
        t1.infecttion_desc,
        t1.tcm_four_diag_observe as tcm_observe_desc,
        t1.tonguepicture as tongue_picture,
        t1.diastolic_pressure as dbp,
        t1.mean_pressure as mbp,
        t1.systolic_pressure as sbp,
        t1.fasting_glucose as fbs,
        t1.postprandial_glucose as two_hpbg,
        t1.temperature,
        t1.breath_freq as breath_rate,
        t1.blood_oxygen,
        t1.heart_rate,
        t1.weight,
        t1.height,
        t1.pulse as pulse_rate,
        aomr.checkup_other as exam_desc,
        aomr.positive_sign_ds as pos_aux_examin,
        -- t1.diagnose_type,
        t1.diagnose_type_code as diagnose_code,
        t1.diagnose_type_name as diagnose_name,
        <!-- 删除该字段 -->
        -- t1.medical_name,
        t1.mr_type_code as medical_type_code,
        t1.mr_type_desc as medical_type_name,
        t1.medical_content,
        t1.visit_time as visit_datetime,
        t1.attk_date_time as start_sick_datetime,
        t1.event_datetime,
        t1.etl_create_datetime,
        t1.etl_update_datetime,
        t1.source_id,
        t1.visit_type_code as data_src,
        t1.global_person_id,
        t1.create_datetime as medical_create_datetime,
        t1.update_datetime as medical_update_datetime,
        <!-- 删除该字段-->
        -- t1.uap_user_id,
        t2.province_code,
        t2.province_name,
        t2.city_code,
        t2.city_name,
        t2.district_code,
        t2.district_name,
        t2.township_hospital_id,
        t2.township_hospital_name,
        t3.symptom_tag_list,
        t3.onset_time_list,
        t3.cause_list,
        t3.examine_list,
        t3.diagnose_name,
        t3.main_suit_symptom_tag_list,
        t3.physical_sign_list,
        t3.epidemiology_history_list
        from ads.ads_outpat_medical_records t1
        join dim.dim_medical_organization_info t2 on t1.org_id = t2.org_id
        left join ads.v_syndrome_medical_symptom_tag t3 on t1.medical_id = t3.medical_id
        where t1.patient_name = #{patientName,jdbcType=VARCHAR}
          and t1.patient_id_number = #{idCardNo,jdbcType=VARCHAR}
        order by t1.medical_create_datetime desc limit 1
    </select>

    <select id="findNewByCaseSearchDto" resultType="com.iflytek.cdc.edr.dto.epi.CaseDTO">
        select
        t1.medical_id as sourceKey,
        t1.patient_name as name,
        t1.person_info_id as patientId,
        t1.patient_age as age,
        t1.patient_sex_code as sexCode,
        t1.patient_sex_desc as sexName,
        t1.identity_number as idCardNum,
        t1.phone_no as phone,
        t1.diagnose_name as diag,
        t1.create_datetime as fullDate,
        t1.visit_time as outpatientTime,
        t1.company_name as company,
        mi.address_province_code as provinceCode,
        mi.address_province_name as provinceName,
        mi.address_city_code as cityCode,
        mi.address_city_name as cityName,
        mi.address_district_code as districtCode,
        mi.address_district_name as districtName,
        t1.now_addr as address,
        t1.attk_date_time as onsetDate,
        t1.diagnose_create_time as diagnosisDateTime,
        t1.doctor_code as visitDocId,
        t1.doctor_name as visitDocName,
        '' as visitDocPhone,
        t1.org_id as visitOrgId,
        t1.org_name as visitOrgName,
        t2.org_longitude as orgLongitude,
        t2.org_latitude as orgLatitude,
        t1.checkup_other as checkupOther,
        t1.action_in_chief as mainSuit,
        t1.medical_history_now as illnessHistory,
        t1.medical_history as previousHistory,
        t1.positive_sign_ds as auxExam,
        t3.symptom_tag_list as symptomName,
        t1.org_name as source,
        t1.source_id as sourceId,
        t1.visit_type_code as dataSrc
        from ads.ads_outpat_medical_records t1
        join dim.dim_medical_organization_info t2 on t1.org_id = t2.org_id
        left join dim.dim_mdm_person_info dp on t1.global_person_id = dp.global_person_id
        left join dim.dim_mr_address_standard mi on dp.std_living_address_code = mi.address_area_code
        left join ads.v_syndrome_medical_symptom_tag t3 on t1.medical_id = t3.medical_id
        where 1=1
        <if test="name != null and name != ''">
            and t1.patient_name = #{name,jdbcType=VARCHAR}
        </if>
        <if test="idCardNum != null and idCardNum != ''">
            and t1.identity_number = #{idCardNum,jdbcType=VARCHAR}
        </if>
        <if test="startTime != null">
            and t1.visit_time <![CDATA[>=]]> #{startTime,jdbcType=DATE}
        </if>
        <if test="endTime != null">
            and t1.visit_time <![CDATA[<=]]> #{endTime,jdbcType=DATE}
        </if>
        <if test="searchKey != null and searchKey != ''">
            and t3.symptom_tag_list like concat('%',#{searchKey,jdbcType=VARCHAR}::varchar,'%')
        </if>
        <if test="sourceId != null and sourceId != ''">
            and t1.org_id = #{sourceId,jdbcType=VARCHAR}
        </if>
        <if test="attackStartTime != null">
            and t1.attk_date_time <![CDATA[>=]]> #{attackStartTime,jdbcType=DATE}
        </if>
        <if test="attackEndTime != null">
            and t1.attk_date_time <![CDATA[<=]]> #{attackEndTime,jdbcType=DATE}
        </if>
        <if test="attackAddress != null and attackAddress != ''">
            and t1.now_addr like concat('%',#{attackAddress,jdbcType=VARCHAR}::varchar,'%')
        </if>
        <if test="sign != null and sign != ''">
            and t1.checkup_other like concat('%',#{sign,jdbcType=VARCHAR}::varchar,'%')
        </if>
    </select>

</mapper>
