<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.clickhouse.v2.AdsOutpatMedicalRecordsCKMapper">

<!--    <select id="getNewOutpatientReportLog" resultType="com.iflytek.cdc.edr.dto.infections.OutpatientReportLog">-->
<!--        SELECT aomr.serial_no outpatient_id,-->
<!--        aomr.patient_name,-->
<!--        aomr.patient_sex_desc as sex_name,-->
<!--        case-->
<!--        when aomr.patient_age_unit is not null and aomr.patient_age_unit != '' and aomr.patient_age is not null and aomr.patient_age != ''-->
<!--        then concat(aomr.patient_age, aomr.patient_age_unit)-->
<!--        when aomr.patient_age is not null and aomr.patient_age != ''-->
<!--        then concat(aomr.patient_age, '岁')-->
<!--        else '' end as age,-->
<!--        aomr.identity_number as patient_id_number,-->
<!--        aomr.job occupation,-->
<!--        aomr.dept_name,-->
<!--        case when doi.source_type = '110' then '等级医院' when doi.source_type = '120' then '基层医疗' end sourceTypeName,-->
<!--        doi.org_name as orgName,-->
<!--        aomr.doctor_name AS doctor_name,-->
<!--        aomr.diagnose_name as diagnose_name,-->
<!--        formatDateTime(toDateTime(aomr.visit_time), '%Y-%m-%d') visit_date,-->
<!--        formatDateTime(toDateTime(aomr.attk_date_time), '%Y-%m-%d') start_sick_date,-->
<!--        case when aomr.first_dig_flag = '0' then '复诊' else '初诊' end first_diag_flag,-->
<!--        aomr.now_addr living_address,-->
<!--        &#45;&#45; todo 将medical_id作为event_id-->
<!--        aomr.medical_id as event_id-->
<!--        FROM wuhu.ads_outpat_medical_records aomr-->
<!--        JOIN wuhu.dim_organization_info doi ON doi.org_id = aomr.org_id-->
<!--        WHERE aomr.event_datetime >=  #{startDate}::timestamp-->
<!--        AND aomr.event_datetime &lt;=  #{endDate}::timestamp + INTERVAL '1 day'-->
<!--        AND doi.source_type in ('110', '120')-->
<!--        <if test="provinceCode != null">-->
<!--            AND doi.province_code = #{provinceCode}-->
<!--        </if>-->
<!--        <if test="cityCode != null">-->
<!--            AND doi.city_code = #{cityCode}-->
<!--        </if>-->
<!--        <if test="districtCode != null">-->
<!--            AND doi.district_code = #{districtCode}-->
<!--        </if>-->
<!--        <if test="sourceType != null and sourceType != ''">-->
<!--            AND doi.source_type = #{sourceType}-->
<!--        </if>-->
<!--        <if test="orgList != null and orgList.size() > 0">-->
<!--            AND doi.org_id in-->
<!--            <foreach item="item" index="index" collection="orgList" open="(" separator="," close=")">-->
<!--                #{item,jdbcType=VARCHAR}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="patientName != null and patientName != ''">-->
<!--            AND aomr.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')-->
<!--        </if>-->
<!--        <if test="diagnoseName != null and diagnoseName != ''">-->
<!--            AND aomr.diagnose_name LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%')-->
<!--        </if>-->
<!--        <if test="isInfectious != null">-->
<!--            AND aomr.is_infectious = #{isInfectious}-->
<!--        </if>-->
<!--        <if test="infectedName != null">-->
<!--            AND aomr.diagnose_name LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%')-->
<!--        </if>-->
<!--        <if test="isExistReportCard != null and isExistReportCard != ''">-->
<!--            AND aomr.is_exist_report_card = #{isExistReportCard}-->
<!--        </if>-->
<!--        <if test="firstDiagFlag != null and firstDiagFlag != ''">-->
<!--            AND aomr.first_dig_flag = #{firstDiagFlag}-->
<!--        </if>-->
<!--        <if test="deptName != null and deptName != ''">-->
<!--            AND aomr.dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')-->
<!--        </if>-->
<!--        order by formatDateTime(toDateTime(aomr.visit_time), '%Y-%m-%d') DESC,-->
<!--        doi.org_name,-->
<!--        aomr.serial_no asc-->
<!--    </select>-->

    <sql id="outPatientReportLog" >
        aomr.serial_no outpatient_id,
        aomr.patient_name,
        aomr.patient_sex_desc as sex_name,
        case
        when aomr.patient_age_unit is not null and aomr.patient_age_unit != '' and aomr.patient_age is not null and aomr.patient_age != ''
        then concat(aomr.patient_age, aomr.patient_age_unit)
        when aomr.patient_age is not null and aomr.patient_age != ''
        then concat(aomr.patient_age, '岁')
        else '' end as age,
        aomr.identity_number as patient_id_number,
        aomr.job occupation,
        aomr.dept_name,
        aomr.source_id as source_type,
        case when aomr.source_id = '110' then '等级医院' when aomr.source_id = '120' then '基层医疗' end sourceTypeName,
        aomr.org_name as orgName,
        aomr.org_id as orgId,
        aomr.doctor_name AS doctor_name,
        aomr.diagnose_name as diagnose_name,
        aomr.disease_name,
        formatDateTime(toDateTime(aomr.visit_time), '%Y-%m-%d') visit_date,
        formatDateTime(toDateTime(aomr.attk_date_time), '%Y-%m-%d') start_sick_date,
        case when aomr.first_dig_flag = '0' then '复诊' else '初诊' end first_diag_flag,
        aomr.now_addr living_address,
        -- todo 将medical_id作为event_id
        aomr.medical_id as event_id,
        aomr.medical_id as medical_id

    </sql>

    <select id="selectByMedicalId" resultType="com.iflytek.cdc.edr.dto.infections.OutpatientReportLog">
        SELECT
            <include refid="outPatientReportLog"/>
        FROM ads_outpat_medical_records aomr
        where aomr.medical_id = #{medicalId}
        limit 1
    </select>


    <select id="getNewOutpatientReportLog" resultType="com.iflytek.cdc.edr.dto.infections.OutpatientReportLog">
        SELECT
        <include refid="outPatientReportLog"/>
        FROM ads_outpat_medical_records aomr
        WHERE aomr.event_datetime >=  #{startDate}::timestamp
        AND aomr.event_datetime &lt;=  #{endDate}::timestamp + INTERVAL '1 day'
        and aomr.org_id in (
            select doi.org_id
            from dim_organization_info doi
            where doi.source_type in ('110', '120')
            <if test="provinceCode != null">
                AND doi.province_code = #{provinceCode}
            </if>
            <if test="cityCode != null">
                AND doi.city_code = #{cityCode}
            </if>
            <if test="districtCode != null">
                AND doi.district_code = #{districtCode}
            </if>
            <if test="sourceType != null and sourceType != ''">
                AND doi.source_type = #{sourceType}
            </if>
            <if test="provinceCodes != null and provinceCodes.size() > 0">
                and doi.province_code in
                <foreach collection="provinceCodes" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodes != null and cityCodes.size() > 0">
                and doi.city_code in
                <foreach collection="cityCodes" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodes != null and districtCodes.size() > 0">
                and doi.district_code in
                <foreach collection="districtCodes" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="orgList != null and orgList.size() > 0">
                AND doi.org_id in
                <foreach item="item" index="index" collection="orgList" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        )
        <if test="patientName != null and patientName != ''">
            AND aomr.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
            AND aomr.diagnose_name LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isInfectious != null">
            AND aomr.is_infectious = #{isInfectious}
        </if>
        <if test="infectedName != null">
            AND aomr.diagnose_name LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND aomr.is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="firstDiagFlag != null and firstDiagFlag != ''">
            AND aomr.first_dig_flag = #{firstDiagFlag}
        </if>
        <if test="deptName != null and deptName != ''">
            AND aomr.dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
        <if test="occupation != null and occupation != ''">
            AND aomr.job LIKE concat('%', #{occupation, jdbcType=VARCHAR}, '%')
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            AND aomr.disease_name LIKE concat('%', #{diseaseName, jdbcType=VARCHAR}, '%')
        </if>
        order by formatDateTime(toDateTime(aomr.visit_time), '%Y-%m-%d') DESC,
        aomr.org_name,
        aomr.serial_no asc
    </select>

</mapper>