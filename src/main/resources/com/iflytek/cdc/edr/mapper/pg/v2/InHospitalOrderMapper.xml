<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.InHospitalOrderMapper">

 <select id="selectInHospitalOrderByEventId"
         resultType="com.iflytek.cdc.edr.entity.inhospital.InHospitalOrder">
     select t0.id,
            t0.drug_name,                                                                   -- 药物通用名称
            t0.spec               as                                    drug_spec,          -- 药物规格代码
            CONCAT(cast(t0.single_dose as VARCHAR), t0.single_dose_unit_name) once_dosage,-- 单次使用量
            t0.usage_name         as                                    drug_useway_name,-- 用法
            t0.drug_use_freq_name as                                    drug_frequency_name,-- 频次
            CONCAT(cast(t0.total_amt as VARCHAR), t0.total_amt_unit)          total_num,          -- 总量
            t0.medc_days          as                                    use_days,           -- 天数
            t0.order_type_code
     from ads.ads_inpat_order t0
     where t0.event_id = #{eventId}


 </select>
</mapper>