<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.VAdsMsRepcardBasicMapper">

  <select id="listCardTypesByMedicalId" resultType="string">
      select
        distinct repcard_class
      from ads.v_ads_ms_repcard_basic where medical_id = #{medicalId}
  </select>
  </mapper>