<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.AdsOrgSyndromeSetVisualInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.edr.entity.ads.AdsOrgSyndromeSetVisualInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_org_syndrome_set_visual_info-->
    <id column="monitor_set_id" jdbcType="VARCHAR" property="monitorSetId" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="syndrome_sub_code" jdbcType="VARCHAR" property="syndromeSubCode" />
    <result column="syndrome_sub_name" jdbcType="VARCHAR" property="syndromeSubName" />
    <result column="day" jdbcType="DATE" property="day" />
    <result column="suspect_trend_day" jdbcType="VARCHAR" property="suspectTrendDay" />
    <result column="age_distribute" jdbcType="VARCHAR" property="ageDistribute" />
    <result column="risk_diagnose_distribute" jdbcType="VARCHAR" property="riskDiagnoseDistribute" />
    <result column="risk_diagnose_rate" jdbcType="VARCHAR" property="riskDiagnoseRate" />
    <result column="visit_distribute" jdbcType="VARCHAR" property="visitDistribute" />
    <result column="addr_gather" jdbcType="VARCHAR" property="addrGather" />
    <result column="age_group_gather" jdbcType="VARCHAR" property="ageGroupGather" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="living_addr_distribute" jdbcType="VARCHAR" property="livingAddrDistribute" />
    <result column="window_days_num" jdbcType="INTEGER" property="windowDaysNum" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    monitor_set_id, org_id, org_name, syndrome_sub_code, syndrome_sub_name, "day", suspect_trend_day, 
    age_distribute, risk_diagnose_distribute, risk_diagnose_rate, visit_distribute, addr_gather, 
    age_group_gather, etl_create_datetime, etl_update_datetime, living_addr_distribute, 
    window_days_num
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from ads.ads_org_syndrome_set_visual_info
    where monitor_set_id = #{monitorSetId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from ads.ads_org_syndrome_set_visual_info
    where monitor_set_id = #{monitorSetId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.edr.entity.ads.AdsOrgSyndromeSetVisualInfo">
    <!--@mbg.generated-->
    insert into ads.ads_org_syndrome_set_visual_info (monitor_set_id, org_id, org_name, 
      syndrome_sub_code, syndrome_sub_name, "day", 
      suspect_trend_day, age_distribute, risk_diagnose_distribute, 
      risk_diagnose_rate, visit_distribute, addr_gather, 
      age_group_gather, etl_create_datetime, etl_update_datetime, 
      living_addr_distribute, window_days_num)
    values (#{monitorSetId,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, 
      #{syndromeSubCode,jdbcType=VARCHAR}, #{syndromeSubName,jdbcType=VARCHAR}, #{day,jdbcType=DATE}, 
      #{suspectTrendDay,jdbcType=VARCHAR}, #{ageDistribute,jdbcType=VARCHAR}, #{riskDiagnoseDistribute,jdbcType=VARCHAR}, 
      #{riskDiagnoseRate,jdbcType=VARCHAR}, #{visitDistribute,jdbcType=VARCHAR}, #{addrGather,jdbcType=VARCHAR}, 
      #{ageGroupGather,jdbcType=VARCHAR}, #{etlCreateDatetime,jdbcType=TIMESTAMP}, #{etlUpdateDatetime,jdbcType=TIMESTAMP}, 
      #{livingAddrDistribute,jdbcType=VARCHAR}, #{windowDaysNum,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.edr.entity.ads.AdsOrgSyndromeSetVisualInfo">
    <!--@mbg.generated-->
    insert into ads.ads_org_syndrome_set_visual_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="monitorSetId != null and monitorSetId != ''">
        monitor_set_id,
      </if>
      <if test="orgId != null and orgId != ''">
        org_id,
      </if>
      <if test="orgName != null and orgName != ''">
        org_name,
      </if>
      <if test="syndromeSubCode != null and syndromeSubCode != ''">
        syndrome_sub_code,
      </if>
      <if test="syndromeSubName != null and syndromeSubName != ''">
        syndrome_sub_name,
      </if>
      <if test="day != null">
        "day",
      </if>
      <if test="suspectTrendDay != null">
        suspect_trend_day,
      </if>
      <if test="ageDistribute != null">
        age_distribute,
      </if>
      <if test="riskDiagnoseDistribute != null">
        risk_diagnose_distribute,
      </if>
      <if test="riskDiagnoseRate != null">
        risk_diagnose_rate,
      </if>
      <if test="visitDistribute != null">
        visit_distribute,
      </if>
      <if test="addrGather != null">
        addr_gather,
      </if>
      <if test="ageGroupGather != null">
        age_group_gather,
      </if>
      <if test="etlCreateDatetime != null">
        etl_create_datetime,
      </if>
      <if test="etlUpdateDatetime != null">
        etl_update_datetime,
      </if>
      <if test="livingAddrDistribute != null">
        living_addr_distribute,
      </if>
      <if test="windowDaysNum != null">
        window_days_num,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="monitorSetId != null and monitorSetId != ''">
        #{monitorSetId,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null and orgId != ''">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null and orgName != ''">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="syndromeSubCode != null and syndromeSubCode != ''">
        #{syndromeSubCode,jdbcType=VARCHAR},
      </if>
      <if test="syndromeSubName != null and syndromeSubName != ''">
        #{syndromeSubName,jdbcType=VARCHAR},
      </if>
      <if test="day != null">
        #{day,jdbcType=DATE},
      </if>
      <if test="suspectTrendDay != null">
        #{suspectTrendDay,jdbcType=VARCHAR},
      </if>
      <if test="ageDistribute != null">
        #{ageDistribute,jdbcType=VARCHAR},
      </if>
      <if test="riskDiagnoseDistribute != null">
        #{riskDiagnoseDistribute,jdbcType=VARCHAR},
      </if>
      <if test="riskDiagnoseRate != null">
        #{riskDiagnoseRate,jdbcType=VARCHAR},
      </if>
      <if test="visitDistribute != null">
        #{visitDistribute,jdbcType=VARCHAR},
      </if>
      <if test="addrGather != null">
        #{addrGather,jdbcType=VARCHAR},
      </if>
      <if test="ageGroupGather != null">
        #{ageGroupGather,jdbcType=VARCHAR},
      </if>
      <if test="etlCreateDatetime != null">
        #{etlCreateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="etlUpdateDatetime != null">
        #{etlUpdateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="livingAddrDistribute != null">
        #{livingAddrDistribute,jdbcType=VARCHAR},
      </if>
      <if test="windowDaysNum != null">
        #{windowDaysNum,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.edr.entity.ads.AdsOrgSyndromeSetVisualInfo">
    <!--@mbg.generated-->
    update ads.ads_org_syndrome_set_visual_info
    <set>
      <if test="orgId != null and orgId != ''">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null and orgName != ''">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="syndromeSubCode != null and syndromeSubCode != ''">
        syndrome_sub_code = #{syndromeSubCode,jdbcType=VARCHAR},
      </if>
      <if test="syndromeSubName != null and syndromeSubName != ''">
        syndrome_sub_name = #{syndromeSubName,jdbcType=VARCHAR},
      </if>
      <if test="day != null">
        "day" = #{day,jdbcType=DATE},
      </if>
      <if test="suspectTrendDay != null">
        suspect_trend_day = #{suspectTrendDay,jdbcType=VARCHAR},
      </if>
      <if test="ageDistribute != null">
        age_distribute = #{ageDistribute,jdbcType=VARCHAR},
      </if>
      <if test="riskDiagnoseDistribute != null">
        risk_diagnose_distribute = #{riskDiagnoseDistribute,jdbcType=VARCHAR},
      </if>
      <if test="riskDiagnoseRate != null">
        risk_diagnose_rate = #{riskDiagnoseRate,jdbcType=VARCHAR},
      </if>
      <if test="visitDistribute != null">
        visit_distribute = #{visitDistribute,jdbcType=VARCHAR},
      </if>
      <if test="addrGather != null">
        addr_gather = #{addrGather,jdbcType=VARCHAR},
      </if>
      <if test="ageGroupGather != null">
        age_group_gather = #{ageGroupGather,jdbcType=VARCHAR},
      </if>
      <if test="etlCreateDatetime != null">
        etl_create_datetime = #{etlCreateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="etlUpdateDatetime != null">
        etl_update_datetime = #{etlUpdateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="livingAddrDistribute != null">
        living_addr_distribute = #{livingAddrDistribute,jdbcType=VARCHAR},
      </if>
      <if test="windowDaysNum != null">
        window_days_num = #{windowDaysNum,jdbcType=INTEGER},
      </if>
    </set>
    where monitor_set_id = #{monitorSetId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.edr.entity.ads.AdsOrgSyndromeSetVisualInfo">
    <!--@mbg.generated-->
    update ads.ads_org_syndrome_set_visual_info
    set org_id = #{orgId,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      syndrome_sub_code = #{syndromeSubCode,jdbcType=VARCHAR},
      syndrome_sub_name = #{syndromeSubName,jdbcType=VARCHAR},
      "day" = #{day,jdbcType=DATE},
      suspect_trend_day = #{suspectTrendDay,jdbcType=VARCHAR},
      age_distribute = #{ageDistribute,jdbcType=VARCHAR},
      risk_diagnose_distribute = #{riskDiagnoseDistribute,jdbcType=VARCHAR},
      risk_diagnose_rate = #{riskDiagnoseRate,jdbcType=VARCHAR},
      visit_distribute = #{visitDistribute,jdbcType=VARCHAR},
      addr_gather = #{addrGather,jdbcType=VARCHAR},
      age_group_gather = #{ageGroupGather,jdbcType=VARCHAR},
      etl_create_datetime = #{etlCreateDatetime,jdbcType=TIMESTAMP},
      etl_update_datetime = #{etlUpdateDatetime,jdbcType=TIMESTAMP},
      living_addr_distribute = #{livingAddrDistribute,jdbcType=VARCHAR},
      window_days_num = #{windowDaysNum,jdbcType=INTEGER}
    where monitor_set_id = #{monitorSetId,jdbcType=VARCHAR}
  </update>

  <select id="getByMonitorSetId" resultType="com.iflytek.cdc.edr.entity.app.AppOrgSyndromeSetVisualInfo">
    select *
    from ads.ads_org_syndrome_set_visual_info
    where 1=1
    <if test="monitorSetId != null and monitorSetId !=''">
      and monitor_set_id=#{monitorSetId}
    </if>

    <if test="maxFullDate != null">
      and day = CAST(#{maxFullDate} AS DATE)
    </if>
    <if test="orgId != '' and orgId != null">
      and org_id=#{orgId}
    </if>
    <if test="syndromeSubCode != '' and syndromeSubCode !=null">
      and syndrome_sub_code=#{syndromeSubCode}
    </if>
    limit 1

  </select>

  <select id="getStreetDataByMonitorSetId"
          resultType="com.iflytek.cdc.edr.entity.app.AppOrgSyndromeSetVisualInfo">
    select *
    from ads.ads_street_syndrome_set_visual_info
    where 1=1
    <if test="monitorSetId != null and monitorSetId !=''">
      and monitor_set_id=#{monitorSetId}
    </if>

    <if test="maxFullDate != null ">
      and day = CAST(#{maxFullDate} AS DATE)
    </if>
    <if test="streetCode != '' and streetCode != null">
      and street_code=#{streetCode}
    </if>
    <if test="syndromeSubCode != '' and syndromeSubCode !=null">
      and syndrome_sub_code=#{syndromeSubCode}
    </if>
    limit 1

  </select>


  <select id="medicalDistributionList" resultType="com.iflytek.cdc.edr.dto.syndrome.AppSyndromeMedicalDetailDTO">
    select * from ads.ads_syndrome_medical_detail t
    where  1=1
    and t.syndrome_sub_code=#{syndromeSubCode}
    and t.visit_day between CAST(#{minFullDate} AS DATE) and CAST(#{maxFullDate} AS DATE)
    <if test="orgId != '' and orgId != null">
      and t.org_id_attent=#{orgId}
    </if>
    <if test="streetCode != '' and streetCode != null">
      and t.street_code=#{streetCode}
    </if>
    <if test="ageGroup != '' and ageGroup != null">
      and t.age_group=#{ageGroup}
    </if>
    <if test="medicalType == 'justModelFlag' ">
      and t.just_model_flag='1'
    </if>
    <if test="medicalType == 'abnormalTimeFlag' ">
      and t.abnormal_time_flag='1'
    </if>
    <if test="medicalType == 'enhanceConditionFlag' ">
      and t.enhance_condition_flag='1'
    </if>
    <if test="riskDiagnoseRiskLevel != '' and riskDiagnoseRiskLevel != null">
      and t.risk_diagnose_risk_level=#{riskDiagnoseRiskLevel}
    </if>
    <if test="riskDiagnose != '' and riskDiagnose != null">
      and t.risk_diagnose=#{riskDiagnose}
    </if>
    order by t.visit_time desc
  </select>

</mapper>