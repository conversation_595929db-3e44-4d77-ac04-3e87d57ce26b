<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.VCdcrpSymptomReportMapper">

    <select id="selectByParam" resultType="com.iflytek.cdc.edr.dto.symptomRegister.SymptomRegisterInfo">
        select patient_name as patientName,
        sex_name as sexName,
        concat(exact_age,COALESCE(age_unit,'')) as ageDesc,
        clinical_diagnosis as diagnose,
        patient_source as patientType,
        create_time as registerTime,
        org_name as orgName,
        org_id as orgId,
        report_business_id as visitNo,
        dept_name as deptName,
        dept_code as deptCode,
        telephone as phone,
        identity_no as idCard,
        is_isolation as isIsolated,
        contact_history_des as contactHistory,
        people_going as patientDestination,
        symptoms as symptomDesc,
        id as id
        from ads.v_cdcrp_symptom_report
        where is_delete = '0'
        <if test="diagnose != null">
            and clinical_diagnosis like concat('%',#{diagnose},'%')
        </if>
        <if test="patientType != null">
            and patient_source_code = #{patientType}
        </if>
        <if test="startTime != null and endTime != null">
            and create_time between #{startTime} and #{endTime}
        </if>
        <if test="orderByTime == '1'.toString() ">
            order by create_time desc
        </if>
        <if test="orderByTime == '0'.toString() ">
            order by create_time asc
        </if>
    </select>
</mapper>