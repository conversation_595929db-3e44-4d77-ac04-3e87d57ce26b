<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v1.AdsMtLisInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.edr.entity.AdsMtLisInfo">
    <!--@mbg.generated-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="lab_test_report_no" jdbcType="VARCHAR" property="labTestReportNo" />
    <result column="lab_test_report_title" jdbcType="VARCHAR" property="labTestReportTitle" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="apply_org_id" jdbcType="VARCHAR" property="applyOrgId" />
    <result column="apply_org_name" jdbcType="VARCHAR" property="applyOrgName" />
    <result column="specimen_collect_time" jdbcType="TIMESTAMP" property="specimenCollectTime" />
    <result column="specimen_part_code" jdbcType="VARCHAR" property="specimenPartCode" />
    <result column="specimen_part_name" jdbcType="VARCHAR" property="specimenPartName" />
    <result column="specimen_type_code" jdbcType="VARCHAR" property="specimenTypeCode" />
    <result column="specimen_type_name" jdbcType="VARCHAR" property="specimenTypeName" />
    <result column="specimen_code" jdbcType="VARCHAR" property="specimenCode" />
    <result column="specimen_name" jdbcType="VARCHAR" property="specimenName" />
    <result column="sample_no" jdbcType="VARCHAR" property="sampleNo" />
    <result column="barcode_no" jdbcType="VARCHAR" property="barcodeNo" />
    <result column="exam_org_id" jdbcType="VARCHAR" property="examOrgId" />
    <result column="exam_org_name" jdbcType="VARCHAR" property="examOrgName" />
    <result column="exec_dept_code" jdbcType="VARCHAR" property="execDeptCode" />
    <result column="exec_dept_name" jdbcType="VARCHAR" property="execDeptName" />
    <result column="exam_type_code" jdbcType="VARCHAR" property="examTypeCode" />
    <result column="exam_type_name" jdbcType="VARCHAR" property="examTypeName" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="lab_item_class_code" jdbcType="VARCHAR" property="labItemClassCode" />
    <result column="lab_item_class_name" jdbcType="VARCHAR" property="labItemClassName" />
    <result column="test_detail_code" jdbcType="VARCHAR" property="testDetailCode" />
    <result column="test_detail_desc" jdbcType="VARCHAR" property="testDetailDesc" />
    <result column="test_result_code" jdbcType="VARCHAR" property="testResultCode" />
    <result column="item_result_name" jdbcType="VARCHAR" property="itemResultName" />
    <result column="item_unit" jdbcType="VARCHAR" property="itemUnit" />
    <result column="item_res_flag" jdbcType="VARCHAR" property="itemResFlag" />
    <result column="reference_value" jdbcType="VARCHAR" property="referenceValue" />
    <result column="reference_value_min" jdbcType="VARCHAR" property="referenceValueMin" />
    <result column="reference_value_max" jdbcType="VARCHAR" property="referenceValueMax" />
    <result column="item_res_num" jdbcType="VARCHAR" property="itemResNum" />
    <result column="item_res_num_unit" jdbcType="VARCHAR" property="itemResNumUnit" />
    <result column="item_res_val" jdbcType="VARCHAR" property="itemResVal" />
    <result column="detect_time" jdbcType="TIMESTAMP" property="detectTime" />
    <result column="reporter_id" jdbcType="VARCHAR" property="reporterId" />
    <result column="reporter_name" jdbcType="VARCHAR" property="reporterName" />
    <result column="report_time" jdbcType="TIMESTAMP" property="reportTime" />
    <result column="report_type_code" jdbcType="VARCHAR" property="reportTypeCode" />
    <result column="report_type_name" jdbcType="VARCHAR" property="reportTypeName" />
    <result column="report_auditor_id" jdbcType="VARCHAR" property="reportAuditorId" />
    <result column="report_auditor_name" jdbcType="VARCHAR" property="reportAuditorName" />
    <result column="report_audit_time" jdbcType="TIMESTAMP" property="reportAuditTime" />
    <result column="person_info_id" jdbcType="VARCHAR" property="personInfoId" />
    <result column="patient_name" jdbcType="VARCHAR" property="patientName" />
    <result column="patient_age" jdbcType="VARCHAR" property="patientAge" />
    <result column="patient_age_unit" jdbcType="VARCHAR" property="patientAgeUnit" />
    <result column="patient_sex_code" jdbcType="VARCHAR" property="patientSexCode" />
    <result column="patient_sex_desc" jdbcType="VARCHAR" property="patientSexDesc" />
    <result column="visit_type_code" jdbcType="VARCHAR" property="visitTypeCode" />
    <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
    <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime" />
    <result column="updator_id" jdbcType="VARCHAR" property="updatorId" />
    <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime" />
    <result column="enabled" jdbcType="VARCHAR" property="enabled" />
    <result column="deleted" jdbcType="VARCHAR" property="deleted" />
    <result column="is_exist_report_card" jdbcType="VARCHAR" property="isExistReportCard" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="medical_id" jdbcType="VARCHAR" property="medicalId" />
    <result column="visit_time" jdbcType="TIMESTAMP" property="visitTime" />
    <result column="visit_type_name" jdbcType="VARCHAR" property="visitTypeName" />
    <result column="identity_type_code" jdbcType="VARCHAR" property="identityTypeCode" />
    <result column="identity_type_name" jdbcType="VARCHAR" property="identityTypeName" />
    <result column="identity_no" jdbcType="VARCHAR" property="identityNo" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, event_id, serial_no, lab_test_report_no, lab_test_report_title, org_id, org_code,
    org_name, apply_org_id, apply_org_name, specimen_collect_time, specimen_part_code,
    specimen_part_name, specimen_type_code, specimen_type_name, specimen_code, specimen_name,
    sample_no, barcode_no, exam_org_id, exam_org_name, exec_dept_code, exec_dept_name,
    exam_type_code, exam_type_name, item_code, item_name, lab_item_class_code, lab_item_class_name,
    test_detail_code, test_detail_desc, test_result_code, item_result_name, item_unit,
    item_res_flag, reference_value, reference_value_min, reference_value_max, item_res_num,
    item_res_num_unit, item_res_val, detect_time, reporter_id, reporter_name, report_time,
    report_type_code, report_type_name, report_auditor_id, report_auditor_name, report_audit_time,
    person_info_id, patient_name, patient_age, patient_age_unit, patient_sex_code, patient_sex_desc,
    visit_type_code, creator_id, create_datetime, updator_id, update_datetime, enabled,
    deleted, is_exist_report_card, etl_create_datetime, etl_update_datetime, medical_id,
    visit_time, visit_type_name, identity_type_code, identity_type_name, identity_no,
    source_id
  </sql>

  <sql id="InspectionReportColumnsSql">
             id,
             event_id,
             event_id                   as medical_id,
             visit_time,
             data_src                   as visit_type_code,
             business_id                as serial_no,
             inspect_id                 as lab_test_report_no,
             org_id,
             org_name,
             patient_id                 as person_info_id,
             patient_name,
             '01'                         as identity_type_code,
             '居民身份证'                 as identity_type_name,
             patient_id_number          as identity_no,
             age                        as patient_age,
             age_unit                   as patient_age_unit,
             sex_code                   as patient_sex_code,
             sex_name                   as patient_sex_desc,
             specimen_sampling_datetime as specimen_collect_time,
             result_datetime,
             dept_code,
             dept_name,
             report_dept_code,
             report_dept_name,
             project_cat_code             as lab_item_class_code,
             project_cat_name             as lab_item_class_name,
             exam_item_code,
             exam_item_name,
             specimen_class_code          as specimen_type_code,
             specimen_class_name          as specimen_type_name,
             specimen_code,
             specimen_name,
             exam_item_code               as item_code,
             exam_item_name               as item_name,
             inspect_item_code            as test_detail_code,
             inspect_item_name            as test_detail_desc,
             result                       as item_result_name,
             exam_result_unit	          as item_res_num_unit,
             abnormal                     as item_res_val,
             refrange                     as reference_value,
             is_exist_report_card,
             source_create_datetime       as create_datetime,
             source_update_datetime       as update_datetime,
             etl_create_datetime,
             etl_update_datetime
  </sql>

  <select id="selectInspectionReportByPatient" resultMap="BaseResultMap">
      select distinct on (event_id, coalesce(inspect_id, id))
        <include refid="InspectionReportColumnsSql"/>
      from ads.ads_ch_inspection_report t
      <where>
          <if test="param.startTime != null and param.startTime != ''">
              and t.result_datetime >= #{param.startTime}::timestamp
          </if>
          <if test="param.endTime != null and param.endTime != ''">
              and t.result_datetime  <![CDATA[<=]]> #{param.endTime}::timestamp
          </if>
          <if test="param.patientName">
              and t.patient_name = #{param.patientName,jdbcType=VARCHAR}
          </if>
          <if test="param.idCardNo != null">
              and t.patient_id_number = #{param.idCardNo,jdbcType=VARCHAR}
          </if>
      </where>
  </select>

  <select id="selectInspectionReportByIds" resultMap="BaseResultMap">
    select
    <include refid="InspectionReportColumnsSql"/>
      from ads.ads_ch_inspection_report t
      <where>
      <if test="ids != null and ids.size() != 0">
        t.id in
        <foreach collection="ids" item="item" separator="," open="(" close=")">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
    </where>
  </select>

  <select id="selectInspectionReportByEventIdReportNo" resultMap="BaseResultMap">
      select
    <include refid="InspectionReportColumnsSql"/>
      from ads.ads_ch_inspection_report t
      <where>
      <if test="eventId != null and eventId != ''">
        and t.event_id = #{eventId,jdbcType=VARCHAR}
      </if>
      <if test="labTestReportNo != null and labTestReportNo != ''">
        and t.inspect_id = #{labTestReportNo,jdbcType=VARCHAR}
      </if>
    </where>

  </select>

  <select id="selectInspectionReportByEventIds" resultMap="BaseResultMap">
      select
    <include refid="InspectionReportColumnsSql"/>
      from ads.ads_ch_inspection_report t
      <where>
      <if test="eventIds != null and eventIds.size() != 0">
        t.event_id in
        <foreach collection="eventIds" item="item" separator="," open="(" close=")">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
    </where>
  </select>
</mapper>