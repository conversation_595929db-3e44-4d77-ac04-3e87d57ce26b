<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.DimAgeGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.edr.entity.dw.DimAgeGroup">
        <id column="id" property="id" />
        <result column="sort" property="sort" />
        <result column="age_group" property="ageGroup" />
        <result column="min_age" property="minAge" />
        <result column="max_age" property="maxAge" />
        <result column="age_group_desc" property="ageGroupDesc" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sort, age_group, min_age, max_age, age_group_desc
    </sql>
    <select id="findAll" resultType="com.iflytek.cdc.edr.entity.dw.DimAgeGroup">
                select *  from dim.dim_age_group order by sort
    </select>

</mapper>
