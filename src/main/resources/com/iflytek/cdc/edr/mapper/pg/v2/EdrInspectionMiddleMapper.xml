<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.EdrInspectionMiddleMapper">

    <resultMap type="com.iflytek.cdc.edr.entity.outpatient.EdrInspectionMiddle" id="EdrInspectionMiddleResult">
        <result property="id" column="id"/>
        <result property="eventId" column="event_id"/>
        <result property="businessId" column="business_id"/>
        <result property="hisInspectId" column="his_inspect_id"/>
        <result property="examItemName" column="exam_item_name"/>
        <result property="sentDeptName" column="sent_dept_name"/>
        <result property="sentDeptCode" column="sent_dept_code"/>
        <result property="specimenAcceptDatetime" column="specimen_accept_datetime"/>
        <result property="inspectId" column="inspect_id"/>
        <result property="inspectName" column="inspect_name"/>
        <result property="result" column="result"/>
        <result property="examResultUnit" column="exam_result_unit"/>
        <result property="refrange" column="refrange"/>
        <result property="abnormal" column="abnormal"/>
    </resultMap>

    <sql id="selectEdrInspectionMiddleVo">
        select t0.id,
               t0.event_id,
               t0.business_id,
               t0.inspect_id                 his_inspect_id,
               t0.exam_item_name,
               t0.report_dept_name           sent_dept_name,
               t0.report_dept_code           sent_dept_code,
               t0.specimen_sampling_datetime specimen_accept_datetime, -- 采样日期
               t0.inspect_id,
               t0.inspect_item_name          inspect_name,
               t0.result,
               t0.exam_result_unit,
               t0.refrange,
               t0.abnormal
        from ads.ads_ch_inspection_report t0
    </sql>

    <sql id="selectNewEdrInspectionMiddleVo">
        select t0.id,
               t0.medical_id as event_id,
               t0.serial_no as business_id,
               t0.lab_test_report_no         his_inspect_id,
               t0.item_name as exam_item_name,
               t0.exec_dept_name           sent_dept_name,
               t0.exec_dept_code           sent_dept_code,
               t0.specimen_collect_time  specimen_accept_datetime, -- 采样日期
               t0.lab_test_report_no as inspect_id,
               t0.test_detail_desc          inspect_name,
               t0.item_res_num as result,
               t0.item_unit as exam_result_unit,
               t0.reference_value as refrange,
               t0.item_res_val as abnormal
        from ads.ads_mt_lis_info t0
    </sql>

    <select id="selectEdrInspectionMiddleByEventId" parameterType="String" resultMap="EdrInspectionMiddleResult">
        <include refid="selectEdrInspectionMiddleVo"/>
        where t0.event_id = #{eventId}
    </select>

    <select id="selectEdrInspectionMiddleList" parameterType="com.iflytek.cdc.edr.entity.outpatient.EdrInspectionMiddle"
            resultMap="EdrInspectionMiddleResult">
        <include refid="selectEdrInspectionMiddleVo"/>
        <where>
            <if test="eventId != null  and eventId != ''">and t1.event_id = #{eventId}</if>
            <if test="businessId != null  and businessId != ''">and business_id = #{businessId}</if>
            <if test="hisInspectId != null  and hisInspectId != ''">and his_inspect_id = #{hisInspectId}</if>
            <if test="examItemName != null  and examItemName != ''">and exam_item_name like concat('%', #{examItemName},
                '%')
            </if>
            <if test="sentDeptName != null  and sentDeptName != ''">and sent_dept_name like concat('%', #{sentDeptName},
                '%')
            </if>
            <if test="sentDeptCode != null  and sentDeptCode != ''">and sent_dept_code = #{sentDeptCode}</if>
            <if test="specimenAcceptDatetime != null ">and specimen_accept_datetime = #{specimenAcceptDatetime}</if>
            <if test="inspectId != null  and inspectId != ''">and inspect_id = #{inspectId}</if>
            <if test="inspectName != null  and inspectName != ''">and inspect_name like concat('%', #{inspectName},
                '%')
            </if>
            <if test="result != null  and result != ''">and result = #{result}</if>
            <if test="examResultUnit != null  and examResultUnit != ''">and exam_result_unit = #{examResultUnit}</if>
            <if test="refrange != null  and refrange != ''">and refrange = #{refrange}</if>
            <if test="abnormal != null  and abnormal != ''">and abnormal = #{abnormal}</if>
        </where>
    </select>

    <select id="selectEdrInspectionMiddleById" parameterType="String" resultMap="EdrInspectionMiddleResult">
        <include refid="selectEdrInspectionMiddleVo"/>
        where id = #{id}
    </select>
    <select id="selectEdrInspectionMiddleByEventIds"
            resultType="com.iflytek.cdc.edr.entity.outpatient.EdrInspectionMiddle">
        <include refid="selectEdrInspectionMiddleVo"/>
        where t0.event_id in
        <foreach collection="eventIds" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectNewEdrInspectionMiddleById" resultType="com.iflytek.cdc.edr.entity.outpatient.EdrInspectionMiddle">
        <include refid="selectNewEdrInspectionMiddleVo"/>
        where id = #{id}
    </select>

    <select id="selectNewEdrInspectionMiddleList" resultType="com.iflytek.cdc.edr.entity.outpatient.EdrInspectionMiddle">
        <include refid="selectNewEdrInspectionMiddleVo"/>
        <where>
            -- todo 后续需要处理medical_id和event_id
            <if test="eventId != null  and eventId != ''">and t0.medical_id = #{eventId}</if>
            <if test="businessId != null  and businessId != ''">and serial_no = #{businessId}</if>
            <if test="hisInspectId != null  and hisInspectId != ''">and lab_test_report_no = #{hisInspectId}</if>
            <if test="examItemName != null  and examItemName != ''">and item_name like concat('%', #{examItemName},
                '%')
            </if>
            <if test="sentDeptName != null  and sentDeptName != ''">and exec_dept_name like concat('%', #{sentDeptName},
                '%')
            </if>
            <if test="sentDeptCode != null  and sentDeptCode != ''">and exec_dept_code = #{sentDeptCode}</if>
            <if test="specimenAcceptDatetime != null ">and specimen_collect_time = #{specimenAcceptDatetime}</if>
            <if test="inspectId != null  and inspectId != ''">and lab_test_report_no = #{inspectId}</if>
            <if test="inspectName != null  and inspectName != ''">and item_name like concat('%', #{inspectName},
                '%')
            </if>
            <if test="result != null  and result != ''">and item_result_name = #{result}</if>
            <if test="examResultUnit != null  and examResultUnit != ''">and item_unit = #{examResultUnit}</if>
            <if test="refrange != null  and refrange != ''">and reference_value = #{refrange}</if>
            <if test="abnormal != null  and abnormal != ''">and item_res_val = #{abnormal}</if>
        </where>
    </select>

    <select id="selectNewEdrInspectionMiddleByEventId" resultType="com.iflytek.cdc.edr.entity.outpatient.EdrInspectionMiddle">
        <include refid="selectNewEdrInspectionMiddleVo"/>
        -- todo 后续需要处理medical_id和event_id
        where t0.medical_id = #{eventId}
        order by t0.specimen_collect_time DESC
    </select>

    <select id="selectNewEdrInspectionMiddleByEventIds" resultType="com.iflytek.cdc.edr.entity.outpatient.EdrInspectionMiddle">
        <include refid="selectNewEdrInspectionMiddleVo"/>
        -- todo 后续需要处理medical_id和event_id
        where t0.medical_id in
        <foreach collection="eventIds" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

</mapper>
