<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.clickhouse.v2.AdsMtLisInfoCKMapper">

  <select id="getLisInfoList" resultType="com.iflytek.cdc.edr.vo.LisInfoVO">
      select t1.lab_test_report_no as hisInspectId,
      t1.org_name as orgName,
      t1.visit_type_name as visitTypeName,
      CASE WHEN t1.visit_type_code = '4' THEN '3' ELSE t1.visit_type_code END as visitTypeCode,
      t1.visit_time as visitTime,
      t1.serial_no as serialNo,
      t1.patient_name as patientName,
      t1.patient_sex_desc as patientSexDesc,
      CONCAT(t1.patient_age, t1.patient_age_unit) as patientAge,
      t1.detect_time as detectTime,
      t1.item_code as itemCode,
      t1.item_name as itemName,
      CONCAT(t1.reference_value, t1.item_unit) as referenceValue,
      t1.item_res_val as itemResVal,
      CONCAT(t1.item_res_num, item_res_num_unit) as itemResNum,
      t1.disease_name as diseaseName,
      t1.is_exist_report_card as isExistReportCard,
      t1.recent_report_card_datetime as reportCardDatetime,
      t1.medical_id as medicalId,
      t1.test_detail_desc as inspectName,
      t1.report_card_type AS reportCardType,
      t1.report_card_type AS reportCardTypeName
      from ads_mt_lis_info t1
      LEFT JOIN dim_organization_info t2 on t1.org_id = t2.org_id
      WHERE t1.report_time >= #{startDate}::timestamp
      AND t1.report_time &lt; #{endDate}::timestamp + INTERVAL '1 day'
      <if test="orgList != null and orgList.size() &gt; 0">
          <foreach collection="orgList" index="index" item="orgId" open=" AND (" separator=" OR " close=")">
              t1.org_id = #{orgId}
          </foreach>
      </if>
      <if test="provinceCode != null and provinceCode != ''">
          AND t2.province_code = #{provinceCode}
      </if>
      <if test="cityCode != null and cityCode != ''">
          AND t2.city_code = #{cityCode}
      </if>
      <if test="districtCode != null and districtCode != ''">
          AND t2.district_code = #{districtCode}
      </if>
      <if test="provinceCodes != null and provinceCodes.size() > 0">
          and t2.province_code in
          <foreach collection="provinceCodes" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="cityCodes != null and cityCodes.size() > 0">
          and t2.city_code in
          <foreach collection="cityCodes" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="districtCodes != null and districtCodes.size() > 0">
          and t2.district_code in
          <foreach collection="districtCodes" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="patientName != null and patientName != ''">
          AND t1.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
      </if>
      <if test="sourceType != null and sourceType != ''">
          AND SUBSTR(t1.source_id, 1, 3) = #{sourceType}
      </if>
      <if test="patientSourceCode != null and patientSourceCode != ''">
          AND t1.visit_type_code = #{patientSourceCode}
      </if>
      <if test="isExistReportCard != null and isExistReportCard != ''">
          AND t1.is_exist_report_card = #{isExistReportCard}
          <if test="isExistReportCard == 1 and reportCardTypeList != null and reportCardTypeList.size() > 0">
              <foreach collection="reportCardTypeList" item="reportCardType" index="index" open=" AND (" separator=" OR " close=")">
                  t1.report_card_type LIKE CONCAT('%', #{reportCardType}, '%')
              </foreach>
          </if>
      </if>
      <if test="abnormal != null and abnormal != ''">
          AND t1.item_res_val = #{abnormal}
      </if>
      <if test="inspectItemList != null and inspectItemList.size() &gt; 0">
          <foreach collection="inspectItemList" index="index" item="insItem" open=" AND (" separator=" OR " close=")">
              t1.item_name = #{insItem}
          </foreach>
      </if>
      <if test="diseaseCode != null and diseaseCode.size() &gt; 0">
          <foreach collection="diseaseCode" index="index" item="disease" open=" AND (" separator=" OR " close=")">
              t1.disease_code = #{disease}
          </foreach>
      </if>
      <if test="customQueryParamList != null and customQueryParamList.size() > 0">
          AND (
          <trim prefixOverrides="AND|OR">
              <foreach collection="customQueryParamList" item="queryParam" index="index">
                  <choose>
                      <when test="queryParam.logicalType == 'and'"> AND </when>
                      <when test="queryParam.logicalType == 'or'"> OR </when>
                  </choose>
                  <choose>
                      <when test="queryParam.paramName == 'age'">
                          <choose>
                              <when test="queryParam.compareType == 'eq'"> t1.patient_age = #{queryParam.paramValue} </when>
                              <when test="queryParam.compareType == 'ne'"> (t1.patient_age != #{queryParam.paramValue} OR t1.patient_age IS NULL) </when>
                              <when test="queryParam.compareType == 'gt'"> (CASE WHEN match(t1.patient_age, '^[0-9\.]+$') THEN toInt32(t1.patient_age) END) <![CDATA[ > ]]> toInt32(#{queryParam.paramValue}) </when>
                              <when test="queryParam.compareType == 'lt'"> (CASE WHEN match(t1.patient_age, '^[0-9\.]+$') THEN toInt32(t1.patient_age) END) <![CDATA[ < ]]> toInt32(#{queryParam.paramValue}) </when>
                          </choose>
                          <if test="queryParam.ageUnit != null and queryParam.ageUnit != ''">
                              AND t1.patient_age_unit = #{queryParam.ageUnit}
                          </if>
                      </when>
                      <when test="queryParam.paramName == 'item'">
                          <choose>
                              <when test="queryParam.compareType == 'eq'"> t1.item_name = #{queryParam.paramValue} </when>
                              <when test="queryParam.compareType == 'ne'"> (t1.item_name != #{queryParam.paramValue} OR t1.item_name IS NULL) </when>
                              <when test="queryParam.compareType == 'co'"> t1.item_name LIKE CONCAT('%', #{queryParam.paramValue} ,'%') </when>
                              <when test="queryParam.compareType == 'nc'"> (t1.item_name NOT LIKE CONCAT('%', #{queryParam.paramValue}, '%') OR t1.item_name IS NULL) </when>
                          </choose>
                          <if test="queryParam.resultValue != null and queryParam.resultValue != ''">
                              <choose>
                                  <when test="queryParam.resultCompareType == 'eq'"> AND t1.item_res_num = #{queryParam.resultValue} </when>
                                  <when test="queryParam.resultCompareType == 'ne'"> AND (t1.item_res_num != #{queryParam.resultValue} OR t1.item_res_num IS NULL) </when>
                                  <when test="queryParam.resultCompareType == 'gt'"> AND (CASE WHEN match(t1.item_res_num, '^[0-9\.]+$') THEN toFloat64(t1.item_res_num) END) <![CDATA[ > ]]> toFloat64(#{queryParam.resultValue}) </when>
                                  <when test="queryParam.resultCompareType == 'lt'"> AND (CASE WHEN match(t1.item_res_num, '^[0-9\.]+$') THEN toFloat64(t1.item_res_num) END) <![CDATA[ < ]]> toFloat64(#{queryParam.resultValue}) </when>
                                  <when test="queryParam.resultCompareType == 'co'"> AND t1.item_res_num LIKE CONCAT('%', #{queryParam.resultValue} ,'%') </when>
                                  <when test="queryParam.resultCompareType == 'nc'"> AND (t1.item_res_num NOT LIKE CONCAT('%', #{queryParam.resultValue}, '%') OR t1.item_res_num IS NULL) </when>
                              </choose>
                          </if>
                      </when>
                      <when test="queryParam.paramName == 'disease'">
                          <choose>
                              <when test="queryParam.compareType == 'eq'"> t1.disease_code = #{queryParam.paramValue} </when>
                              <when test="queryParam.compareType == 'ne'"> (t1.disease_code != #{queryParam.paramValue} OR t1.disease_code IS NULL) </when>
                              <when test="queryParam.compareType == 'co'"> t1.disease_code LIKE CONCAT(#{queryParam.paramValue}, '%') </when>
                              <when test="queryParam.compareType == 'nc'"> (t1.disease_code NOT LIKE CONCAT(#{queryParam.paramValue}, '%') OR t1.disease_code IS NULL) </when>
                          </choose>
                      </when>
                  </choose>
              </foreach>
          </trim>
          )
      </if>
      ORDER BY coalesce(t1.create_datetime, toDateTime('1900-01-01')::timestamp) DESC
  </select>

</mapper>