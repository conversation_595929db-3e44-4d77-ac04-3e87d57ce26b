<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v1.MedicalDescMapper">

    <select id="getMedicalDescList" resultType="com.iflytek.cdc.edr.entity.outpatient.MedicalDesc">
        SELECT der.event_id,
               dcmr.patient_id_number,
               dcmr.patient_name,
               dcmr.sex_name,
               CONCAT(dcmr.age, dcmr.age_unit_name) AS age,
               dcmr.telephone,
               der.business_id,
               der.org_id,
               der.org_name,
               dcmr.dept_code,
               dcmr.dept_name,
               dcmr.visit_doc_id as visit_doctor_id,
               dcmr.visit_doc_name as visit_doctor_name,
               dcmr.action_in_chief,
               dcmr.medical_history_now,
               dcmr.medical_history,
               dcmr.personal_history,
               dcmr.family_history,
               dcmr.dbp,
               dcmr.sbp,
               CASE
                   WHEN dbp IS NOT NULL OR sbp IS NOT NULL THEN CONCAT(dbp, '/', sbp, 'mmHg')
                   ELSE '' END AS blood_pressure,
               CASE WHEN fbs IS NOT NULL THEN CONCAT(fbs, 'mmol/L') ELSE '' END AS fbs,
               CASE WHEN temperature IS NOT NULL THEN CONCAT(temperature, '℃') ELSE '' END AS temperature,
               CASE WHEN breath_rate IS NOT NULL THEN CONCAT(breath_rate, '次/分') ELSE '' END AS breath_rate,
               CASE WHEN blood_oxygen IS NOT NULL THEN CONCAT(blood_oxygen, '%') ELSE '' END AS blood_oxygen,
               CASE WHEN heart_rate IS NOT NULL THEN CONCAT(heart_rate, '次/分') ELSE '' END AS heart_rate,
               CASE WHEN weight IS NOT NULL THEN CONCAT(weight, 'kg') ELSE '' END AS weight,
               CASE WHEN height IS NOT NULL THEN CONCAT(height, 'cm') ELSE '' END AS height,
               CASE WHEN pulse_rate IS NOT NULL THEN CONCAT(pulse_rate, '次/分') ELSE '' END AS pulse_rate,
               dcmr.exam_desc,
               dcmr.pos_aux_examin,
               der.diagnose_name_list as diagnose_name,
               der.visit_datetime,
               der.data_src,
               dcmr.global_person_id
          FROM dwd.dwd_ch_medical_record dcmr
               JOIN dwd.dwd_event_record der ON der.event_id = dcmr.event_id
         WHERE der.global_person_id = #{globalPersonId}
         ORDER BY der.visit_datetime desc
    </select>

    <select id="getMedicalDescByEventId" resultType="com.iflytek.cdc.edr.dto.outpatient.MedicalRecord">
        SELECT dcmr.event_id,
               dcmr.patient_id_number,
               dcmr.patient_name,
               dcmr.sex_name,
               CONCAT(dcmr.age, dcmr.age_unit_name) AS age,
               dcmr.telephone,
               dcmr.his_medical_id as business_id,
               dcmr.org_id,
               dcmr.org_name,
               dcmr.dept_code,
               dcmr.dept_name,
               dcmr.visit_doc_id as visit_doctor_id,
               dcmr.visit_doc_name as visit_doctor_name,
               dcmr.action_in_chief,
               dcmr.medical_history_now,
               dcmr.medical_history,
               dcmr.personal_history,
               dcmr.family_history,
               dcmr.dbp,
               dcmr.sbp,
               CASE
                   WHEN dbp IS NOT NULL OR sbp IS NOT NULL THEN CONCAT(dbp, '/', sbp, 'mmHg')
                   ELSE '' END AS blood_pressure,
               CASE WHEN fbs IS NOT NULL THEN CONCAT(fbs, 'mmol/L') ELSE '' END AS fbs,
               CASE WHEN temperature IS NOT NULL THEN CONCAT(temperature, '℃') ELSE '' END AS temperature,
               CASE WHEN breath_rate IS NOT NULL THEN CONCAT(breath_rate, '次/分') ELSE '' END AS breath_rate,
               CASE WHEN blood_oxygen IS NOT NULL THEN CONCAT(blood_oxygen, '%') ELSE '' END AS blood_oxygen,
               CASE WHEN heart_rate IS NOT NULL THEN CONCAT(heart_rate, '次/分') ELSE '' END AS heart_rate,
        CASE WHEN weight IS NOT NULL THEN CONCAT(weight, 'kg') ELSE '' END AS weight,
        CASE WHEN height IS NOT NULL THEN CONCAT(height, 'cm') ELSE '' END AS height,
        CASE WHEN pulse_rate IS NOT NULL THEN CONCAT(pulse_rate, '次/分') ELSE '' END AS pulse_rate,
        dcmr.exam_desc,
        dcmr.pos_aux_examin,
        dcmr.diagnose_name,
        dcmr.visit_datetime,
        dcmr.data_src,
        dcmr.global_person_id
        FROM dwd.dwd_ch_medical_record dcmr
        WHERE dcmr.event_id = #{eventId}
        LIMIT 1
    </select>
    <select id="getMedicalDescByEventIds" resultType="com.iflytek.cdc.edr.dto.outpatient.MedicalRecord">
        SELECT dcmr.event_id,
               dcmr.patient_id_number,
               dcmr.patient_name,
               dcmr.sex_name,
               CONCAT(dcmr.age, dcmr.age_unit_name) AS age,
               dcmr.telephone,
               dcmr.his_medical_id as business_id,
               dcmr.org_id,
               dcmr.org_name,
               dcmr.dept_code,
               dcmr.dept_name,
               dcmr.visit_doc_id as visit_doctor_id,
               dcmr.visit_doc_name as visit_doctor_name,
               dcmr.action_in_chief,
               dcmr.medical_history_now,
               dcmr.medical_history,
               dcmr.personal_history,
               dcmr.family_history,
               dcmr.dbp,
               dcmr.sbp,
               CASE
                   WHEN dbp IS NOT NULL OR sbp IS NOT NULL THEN CONCAT(dbp, '/', sbp, 'mmHg')
                   ELSE '' END AS blood_pressure,
               CASE WHEN fbs IS NOT NULL THEN CONCAT(fbs, 'mmol/L') ELSE '' END AS fbs,
               CASE WHEN temperature IS NOT NULL THEN CONCAT(temperature, '℃') ELSE '' END AS temperature,
               CASE WHEN breath_rate IS NOT NULL THEN CONCAT(breath_rate, '次/分') ELSE '' END AS breath_rate,
               CASE WHEN blood_oxygen IS NOT NULL THEN CONCAT(blood_oxygen, '%') ELSE '' END AS blood_oxygen,
               CASE WHEN heart_rate IS NOT NULL THEN CONCAT(heart_rate, '次/分') ELSE '' END AS heart_rate,
               CASE WHEN weight IS NOT NULL THEN CONCAT(weight, 'kg') ELSE '' END AS weight,
               CASE WHEN height IS NOT NULL THEN CONCAT(height, 'cm') ELSE '' END AS height,
               CASE WHEN pulse_rate IS NOT NULL THEN CONCAT(pulse_rate, '次/分') ELSE '' END AS pulse_rate,
               dcmr.exam_desc,
               dcmr.pos_aux_examin,
               dcmr.diagnose_name,
               dcmr.visit_datetime,
               dcmr.data_src,
               dcmr.global_person_id
        FROM dwd.dwd_ch_medical_record dcmr
        WHERE dcmr.event_id in
        <foreach collection="eventIds" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getInhospitableMedicalByEventId" resultType="com.iflytek.cdc.edr.dto.outpatient.MedicalRecord">

    </select>
</mapper>