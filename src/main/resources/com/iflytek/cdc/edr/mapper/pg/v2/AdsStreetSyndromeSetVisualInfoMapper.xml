<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.AdsStreetSyndromeSetVisualInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.edr.entity.ads.AdsStreetSyndromeSetVisualInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_street_syndrome_set_visual_info-->
    <id column="monitor_set_id" jdbcType="VARCHAR" property="monitorSetId" />
    <result column="street_code" jdbcType="VARCHAR" property="streetCode" />
    <result column="street_name" jdbcType="VARCHAR" property="streetName" />
    <result column="street_province" jdbcType="VARCHAR" property="streetProvince" />
    <result column="street_city" jdbcType="VARCHAR" property="streetCity" />
    <result column="street_district" jdbcType="VARCHAR" property="streetDistrict" />
    <result column="syndrome_sub_code" jdbcType="VARCHAR" property="syndromeSubCode" />
    <result column="syndrome_sub_name" jdbcType="VARCHAR" property="syndromeSubName" />
    <result column="day" jdbcType="DATE" property="day" />
    <result column="suspect_trend_day" jdbcType="VARCHAR" property="suspectTrendDay" />
    <result column="age_distribute" jdbcType="VARCHAR" property="ageDistribute" />
    <result column="risk_diagnose_distribute" jdbcType="VARCHAR" property="riskDiagnoseDistribute" />
    <result column="risk_diagnose_rate" jdbcType="VARCHAR" property="riskDiagnoseRate" />
    <result column="visit_distribute" jdbcType="VARCHAR" property="visitDistribute" />
    <result column="addr_gather" jdbcType="VARCHAR" property="addrGather" />
    <result column="age_group_gather" jdbcType="VARCHAR" property="ageGroupGather" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="living_addr_distribute" jdbcType="VARCHAR" property="livingAddrDistribute" />
    <result column="window_days_num" jdbcType="INTEGER" property="windowDaysNum" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    monitor_set_id, street_code, street_name, street_province, street_city, street_district, 
    syndrome_sub_code, syndrome_sub_name, "day", suspect_trend_day, age_distribute, risk_diagnose_distribute, 
    risk_diagnose_rate, visit_distribute, addr_gather, age_group_gather, etl_create_datetime, 
    etl_update_datetime, living_addr_distribute, window_days_num
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from ads.ads_street_syndrome_set_visual_info
    where monitor_set_id = #{monitorSetId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from ads.ads_street_syndrome_set_visual_info
    where monitor_set_id = #{monitorSetId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.edr.entity.ads.AdsStreetSyndromeSetVisualInfo">
    <!--@mbg.generated-->
    insert into ads.ads_street_syndrome_set_visual_info (monitor_set_id, street_code, street_name, 
      street_province, street_city, street_district, 
      syndrome_sub_code, syndrome_sub_name, "day", 
      suspect_trend_day, age_distribute, risk_diagnose_distribute, 
      risk_diagnose_rate, visit_distribute, addr_gather, 
      age_group_gather, etl_create_datetime, etl_update_datetime, 
      living_addr_distribute, window_days_num)
    values (#{monitorSetId,jdbcType=VARCHAR}, #{streetCode,jdbcType=VARCHAR}, #{streetName,jdbcType=VARCHAR}, 
      #{streetProvince,jdbcType=VARCHAR}, #{streetCity,jdbcType=VARCHAR}, #{streetDistrict,jdbcType=VARCHAR}, 
      #{syndromeSubCode,jdbcType=VARCHAR}, #{syndromeSubName,jdbcType=VARCHAR}, #{day,jdbcType=DATE}, 
      #{suspectTrendDay,jdbcType=VARCHAR}, #{ageDistribute,jdbcType=VARCHAR}, #{riskDiagnoseDistribute,jdbcType=VARCHAR}, 
      #{riskDiagnoseRate,jdbcType=VARCHAR}, #{visitDistribute,jdbcType=VARCHAR}, #{addrGather,jdbcType=VARCHAR}, 
      #{ageGroupGather,jdbcType=VARCHAR}, #{etlCreateDatetime,jdbcType=TIMESTAMP}, #{etlUpdateDatetime,jdbcType=TIMESTAMP}, 
      #{livingAddrDistribute,jdbcType=VARCHAR}, #{windowDaysNum,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.edr.entity.ads.AdsStreetSyndromeSetVisualInfo">
    <!--@mbg.generated-->
    insert into ads.ads_street_syndrome_set_visual_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="monitorSetId != null and monitorSetId != ''">
        monitor_set_id,
      </if>
      <if test="streetCode != null and streetCode != ''">
        street_code,
      </if>
      <if test="streetName != null and streetName != ''">
        street_name,
      </if>
      <if test="streetProvince != null and streetProvince != ''">
        street_province,
      </if>
      <if test="streetCity != null and streetCity != ''">
        street_city,
      </if>
      <if test="streetDistrict != null and streetDistrict != ''">
        street_district,
      </if>
      <if test="syndromeSubCode != null and syndromeSubCode != ''">
        syndrome_sub_code,
      </if>
      <if test="syndromeSubName != null and syndromeSubName != ''">
        syndrome_sub_name,
      </if>
      <if test="day != null">
        "day",
      </if>
      <if test="suspectTrendDay != null">
        suspect_trend_day,
      </if>
      <if test="ageDistribute != null">
        age_distribute,
      </if>
      <if test="riskDiagnoseDistribute != null">
        risk_diagnose_distribute,
      </if>
      <if test="riskDiagnoseRate != null">
        risk_diagnose_rate,
      </if>
      <if test="visitDistribute != null">
        visit_distribute,
      </if>
      <if test="addrGather != null">
        addr_gather,
      </if>
      <if test="ageGroupGather != null">
        age_group_gather,
      </if>
      <if test="etlCreateDatetime != null">
        etl_create_datetime,
      </if>
      <if test="etlUpdateDatetime != null">
        etl_update_datetime,
      </if>
      <if test="livingAddrDistribute != null">
        living_addr_distribute,
      </if>
      <if test="windowDaysNum != null">
        window_days_num,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="monitorSetId != null and monitorSetId != ''">
        #{monitorSetId,jdbcType=VARCHAR},
      </if>
      <if test="streetCode != null and streetCode != ''">
        #{streetCode,jdbcType=VARCHAR},
      </if>
      <if test="streetName != null and streetName != ''">
        #{streetName,jdbcType=VARCHAR},
      </if>
      <if test="streetProvince != null and streetProvince != ''">
        #{streetProvince,jdbcType=VARCHAR},
      </if>
      <if test="streetCity != null and streetCity != ''">
        #{streetCity,jdbcType=VARCHAR},
      </if>
      <if test="streetDistrict != null and streetDistrict != ''">
        #{streetDistrict,jdbcType=VARCHAR},
      </if>
      <if test="syndromeSubCode != null and syndromeSubCode != ''">
        #{syndromeSubCode,jdbcType=VARCHAR},
      </if>
      <if test="syndromeSubName != null and syndromeSubName != ''">
        #{syndromeSubName,jdbcType=VARCHAR},
      </if>
      <if test="day != null">
        #{day,jdbcType=DATE},
      </if>
      <if test="suspectTrendDay != null">
        #{suspectTrendDay,jdbcType=VARCHAR},
      </if>
      <if test="ageDistribute != null">
        #{ageDistribute,jdbcType=VARCHAR},
      </if>
      <if test="riskDiagnoseDistribute != null">
        #{riskDiagnoseDistribute,jdbcType=VARCHAR},
      </if>
      <if test="riskDiagnoseRate != null">
        #{riskDiagnoseRate,jdbcType=VARCHAR},
      </if>
      <if test="visitDistribute != null">
        #{visitDistribute,jdbcType=VARCHAR},
      </if>
      <if test="addrGather != null">
        #{addrGather,jdbcType=VARCHAR},
      </if>
      <if test="ageGroupGather != null">
        #{ageGroupGather,jdbcType=VARCHAR},
      </if>
      <if test="etlCreateDatetime != null">
        #{etlCreateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="etlUpdateDatetime != null">
        #{etlUpdateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="livingAddrDistribute != null">
        #{livingAddrDistribute,jdbcType=VARCHAR},
      </if>
      <if test="windowDaysNum != null">
        #{windowDaysNum,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.edr.entity.ads.AdsStreetSyndromeSetVisualInfo">
    <!--@mbg.generated-->
    update ads.ads_street_syndrome_set_visual_info
    <set>
      <if test="streetCode != null and streetCode != ''">
        street_code = #{streetCode,jdbcType=VARCHAR},
      </if>
      <if test="streetName != null and streetName != ''">
        street_name = #{streetName,jdbcType=VARCHAR},
      </if>
      <if test="streetProvince != null and streetProvince != ''">
        street_province = #{streetProvince,jdbcType=VARCHAR},
      </if>
      <if test="streetCity != null and streetCity != ''">
        street_city = #{streetCity,jdbcType=VARCHAR},
      </if>
      <if test="streetDistrict != null and streetDistrict != ''">
        street_district = #{streetDistrict,jdbcType=VARCHAR},
      </if>
      <if test="syndromeSubCode != null and syndromeSubCode != ''">
        syndrome_sub_code = #{syndromeSubCode,jdbcType=VARCHAR},
      </if>
      <if test="syndromeSubName != null and syndromeSubName != ''">
        syndrome_sub_name = #{syndromeSubName,jdbcType=VARCHAR},
      </if>
      <if test="day != null">
        "day" = #{day,jdbcType=DATE},
      </if>
      <if test="suspectTrendDay != null">
        suspect_trend_day = #{suspectTrendDay,jdbcType=VARCHAR},
      </if>
      <if test="ageDistribute != null">
        age_distribute = #{ageDistribute,jdbcType=VARCHAR},
      </if>
      <if test="riskDiagnoseDistribute != null">
        risk_diagnose_distribute = #{riskDiagnoseDistribute,jdbcType=VARCHAR},
      </if>
      <if test="riskDiagnoseRate != null">
        risk_diagnose_rate = #{riskDiagnoseRate,jdbcType=VARCHAR},
      </if>
      <if test="visitDistribute != null">
        visit_distribute = #{visitDistribute,jdbcType=VARCHAR},
      </if>
      <if test="addrGather != null">
        addr_gather = #{addrGather,jdbcType=VARCHAR},
      </if>
      <if test="ageGroupGather != null">
        age_group_gather = #{ageGroupGather,jdbcType=VARCHAR},
      </if>
      <if test="etlCreateDatetime != null">
        etl_create_datetime = #{etlCreateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="etlUpdateDatetime != null">
        etl_update_datetime = #{etlUpdateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="livingAddrDistribute != null">
        living_addr_distribute = #{livingAddrDistribute,jdbcType=VARCHAR},
      </if>
      <if test="windowDaysNum != null">
        window_days_num = #{windowDaysNum,jdbcType=INTEGER},
      </if>
    </set>
    where monitor_set_id = #{monitorSetId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.edr.entity.ads.AdsStreetSyndromeSetVisualInfo">
    <!--@mbg.generated-->
    update ads.ads_street_syndrome_set_visual_info
    set street_code = #{streetCode,jdbcType=VARCHAR},
      street_name = #{streetName,jdbcType=VARCHAR},
      street_province = #{streetProvince,jdbcType=VARCHAR},
      street_city = #{streetCity,jdbcType=VARCHAR},
      street_district = #{streetDistrict,jdbcType=VARCHAR},
      syndrome_sub_code = #{syndromeSubCode,jdbcType=VARCHAR},
      syndrome_sub_name = #{syndromeSubName,jdbcType=VARCHAR},
      "day" = #{day,jdbcType=DATE},
      suspect_trend_day = #{suspectTrendDay,jdbcType=VARCHAR},
      age_distribute = #{ageDistribute,jdbcType=VARCHAR},
      risk_diagnose_distribute = #{riskDiagnoseDistribute,jdbcType=VARCHAR},
      risk_diagnose_rate = #{riskDiagnoseRate,jdbcType=VARCHAR},
      visit_distribute = #{visitDistribute,jdbcType=VARCHAR},
      addr_gather = #{addrGather,jdbcType=VARCHAR},
      age_group_gather = #{ageGroupGather,jdbcType=VARCHAR},
      etl_create_datetime = #{etlCreateDatetime,jdbcType=TIMESTAMP},
      etl_update_datetime = #{etlUpdateDatetime,jdbcType=TIMESTAMP},
      living_addr_distribute = #{livingAddrDistribute,jdbcType=VARCHAR},
      window_days_num = #{windowDaysNum,jdbcType=INTEGER}
    where monitor_set_id = #{monitorSetId,jdbcType=VARCHAR}
  </update>
</mapper>