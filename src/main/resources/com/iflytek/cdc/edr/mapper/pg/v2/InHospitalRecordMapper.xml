<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.InHospitalRecordMapper">

    <resultMap type="com.iflytek.cdc.edr.entity.inhospital.InHospitalRecord" id="InHospitalRecordResult">
        <result property="eventId"    column="event_id"    />
        <result property="inHospitalId"    column="inhospital_id"    />
        <result property="patientName"    column="patient_name"    />
        <result property="sexDesc"    column="sex_desc"    />
        <result property="age"    column="age"    />
        <result property="idCardNo"    column="id_card_no"    />
        <result property="phone"    column="phone"    />
        <result property="standerOrgCode"    column="stander_org_code"    />
        <result property="patientType"    column="patient_type"    />
        <result property="doctorId"    column="doctor_id"    />
        <result property="doctorName"    column="doctor_name"    />
        <result property="orgId"    column="org_id"/>
        <result property="orgName"    column="org_name"/>
        <result property="deptCode"    column="dept_code"    />
        <result property="deptName"    column="dept_name"    />
        <result property="outpatientDept"    column="outpatient_dept"    />
        <result property="outpatientDoctor"    column="outpatient_doctor"    />
        <result property="inHosChannel"    column="in_hos_channel"    />
        <result property="inDays"    column="in_days"    />
        <result property="inHosDate"    column="in_hos_date"    />
        <result property="inDeptCode"    column="in_dept_code"    />
        <result property="inDeptName"    column="in_dept_name"    />
        <result property="outHosDate"    column="out_hos_date"    />
        <result property="outDeptCode"    column="out_dept_code"    />
        <result property="outDeptName"    column="out_dept_name"    />
        <result property="nowDeptCode"    column="now_dept_code"    />
        <result property="nowDeptName"    column="now_dept_name"    />
        <result property="bedNo"    column="bed_no"    />
        <result property="zlDoctor"    column="zl_doctor"    />
        <result property="mzDoctor"    column="mz_doctor"    />
        <result property="outcomeName"    column="outcome_name"    />
    </resultMap>

    <sql id="selectInHospitalRecordVo">
        select event_id,
               inhospital_id,
               patient_name,
               sex_desc,
               age,
               id_card_no,
               phone,
               stander_org_code,
               patient_type,
               doctor_id,
               doctor_name,
               org_id,
               org_name,
               dept_code,
               dept_name,
               outpatient_dept,
               outpatient_doctor,
               in_hos_channel,
               in_days,
               to_char(in_hos_date, 'YYYY-MM-DD HH24:MI:SS') as in_hos_date,
               in_dept_code,
               in_dept_name,
               to_char(out_hos_date, 'YYYY-MM-DD HH24:MI:SS') as out_hos_date,
               out_dept_code,
               out_dept_name,
               now_dept_code,
               now_dept_name,
               bed_no,
               zl_doctor,
               mz_doctor,
               outcome_name
          from dwd.dwd_ch_inhospital_record
    </sql>

    <sql id="selectNewInHospitalRecordVo">
        select medical_id as event_id,
               inhospital_no as inhospital_id,
               patient_name,
               patient_sex_desc as sex_desc,
               concat(patient_age, patient_age_unit) as age,
               identity_no as id_card_no,
               telephone as phone,
               org_name as stander_org_code,
               <!-- 就诊类别（原表为住院）， 现暂取病人来源 -->
               patient_source as patient_type,
               <!-- 取主任医生编号和姓名 -->
               director_doc_id as doctor_id,
               director_doc_name as doctor_name,
               org_id,
               org_name,
               <!--取当前的科室名称以及编码 -->
               current_dept_code as dept_code,
               current_dept_name as dept_name,
               outpat_dept_name as outpatient_dept,
               outpatient_doc_name as outpatient_doctor,
               (case
                    when admiss_source = '1' then '门诊'
                    when admiss_source = '2' then '急诊'
                    when admiss_source = '3' then '转入'
                    when admiss_source = '9' then '其他'
                    else '其他' end
                   ) as in_hos_channel, --  1 门诊 2 急诊 3 转入 9 其他
               date_part('day', discharge_datetime::timestamp -admiss_time::timestamp) as in_days,
               to_char(admiss_time, 'YYYY-MM-DD HH24:MI:SS') as in_hos_date,
               admiss_dept_code as in_dept_code,
               admiss_dept_name as in_dept_name,
               to_char(discharge_datetime, 'YYYY-MM-DD HH24:MI:SS') as out_hos_date,
               discharge_dept_code as out_dept_code,
               discharge_dept_name as out_dept_name,
               current_dept_code as now_dept_code,
               current_dept_name as now_dept_name,
               current_bed_no as bed_no,
               attending_doc_name as zl_doctor,
               outpatient_doc_name as mz_doctor,
               outcome_name
        from ads.ads_inpat_record
    </sql>

    <select id="selectInHospitalRecordList" parameterType="com.iflytek.cdc.edr.entity.inhospital.InHospitalRecord" resultMap="InHospitalRecordResult">
        <include refid="selectInHospitalRecordVo"/>
        <where>
            <if test="patientName != null  and patientName != ''"> and patient_name like concat('%', #{patientName}, '%')</if>
            <if test="sexDesc != null  and sexDesc != ''"> and sex_desc = #{sexDesc}</if>
            <if test="age != null  and age != ''"> and age = #{age}</if>
            <if test="idCardNo != null "> and id_card_no = #{idCardNo}</if>
            <if test="standerOrgCode != null  and standerOrgCode != ''"> and stander_org_code = #{standerOrgCode}</if>
            <if test="patientType != null  and patientType != ''"> and patient_type = #{patientType}</if>
            <if test="doctorId != null  and doctorId != ''"> and doctor_id = #{doctorId}</if>
            <if test="doctorName != null  and doctorName != ''"> and doctor_name like concat('%', #{doctorName}, '%')</if>
            <if test="deptCode != null  and deptCode != ''"> and dept_code = #{deptCode}</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="outpatientDept != null  and outpatientDept != ''"> and outpatient_dept = #{outpatientDept}</if>
            <if test="outpatientDoctor != null  and outpatientDoctor != ''"> and outpatient_doctor = #{outpatientDoctor}</if>
            <if test="inHosChannel != null  and inHosChannel != ''"> and in_hos_channel = #{inHosChannel}</if>
            <if test="inDays != null  and inDays != ''"> and in_days = #{inDays}</if>
            <if test="inHosDate != null  and inHosDate != ''"> and in_hos_date = #{inHosDate}</if>
            <if test="inDeptCode != null  and inDeptCode != ''"> and in_dept_code = #{inDeptCode}</if>
            <if test="inDeptName != null  and inDeptName != ''"> and in_dept_name like concat('%', #{inDeptName}, '%')</if>
            <if test="outHosDate != null  and outHosDate != ''"> and out_hos_date = #{outHosDate}</if>
            <if test="outDeptCode != null  and outDeptCode != ''"> and out_dept_code = #{outDeptCode}</if>
            <if test="outDeptName != null  and outDeptName != ''"> and out_dept_name like concat('%', #{outDeptName}, '%')</if>
            <if test="nowDeptCode != null  and nowDeptCode != ''"> and now_dept_code = #{nowDeptCode}</if>
            <if test="nowDeptName != null  and nowDeptName != ''"> and now_dept_name like concat('%', #{nowDeptName}, '%')</if>
            <if test="bedNo != null  and bedNo != ''"> and bed_no = #{bedNo}</if>
            <if test="zlDoctor != null  and zlDoctor != ''"> and zl_doctor = #{zlDoctor}</if>
            <if test="mzDoctor != null  and mzDoctor != ''"> and mz_doctor = #{mzDoctor}</if>
            <if test="outcomeName != null  and outcomeName != ''"> and outcome_name like concat('%', #{outcomeName}, '%')</if>
        </where>
    </select>

    <select id="selectInHospitalRecordById" parameterType="String" resultMap="InHospitalRecordResult">
        <include refid="selectInHospitalRecordVo"/>
        where event_id = #{eventId}
    </select>

    <select id="selectNewInHospitalRecordById" resultType="com.iflytek.cdc.edr.entity.inhospital.InHospitalRecord">
        <include refid="selectNewInHospitalRecordVo"/>
        where medical_id = #{eventId}
    </select>


</mapper>
