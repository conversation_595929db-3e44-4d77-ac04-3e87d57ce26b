<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.MultiChannelPreventionMapper">

    <select id="getStatResult" resultType="com.iflytek.cdc.edr.dto.preventionControl.TopDataResultDto">
        select
        count(distinct alstr.sample_address_id) as collectionPointCount,
        count(*) as totalCount,
        sum(case when alstr.item_res_val = '阳性' then 1 else 0 end) as positiveCount
        from ads.ads_lab_sample_test_result alstr
        where alstr.sample_time between #{startTime} and #{endTime}
        and alstr.tests_item_name = #{typeName, jdbcType=VARCHAR}
        <if test="provinceCode != null  and provinceCode != ''">
            and alstr.sample_province_code = #{provinceCode, jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null  and cityCode != ''">
            and alstr.sample_city_code = #{cityCode, jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null  and districtCode != ''">
            and alstr.sample_district_code = #{districtCode, jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getNucleicAcidInfo" resultType="com.iflytek.cdc.edr.dto.preventionControl.NucleicAcidTestInfo">
        select
        alstr.id,
        alstr.patient_name,
        alstr.patient_sex_name,
        alstr.patient_age,
        alstr.patient_age_unit,
        alstr.identity_type_name,
        alstr.identity_no,
        alstr.item_res_val as status,
        alstr.sample_time,
        alstr.sample_address,
        alstr.sample_type_name,
        alstr.sample_bar_code as tubeCode,
        alstr.tube_type,
        alstr.send_time
        from ads.ads_lab_sample_test_result alstr
        where alstr.sample_time between #{startTime} and #{endTime}
        and alstr.tests_item_name = #{typeName, jdbcType=VARCHAR}
        <if test="provinceCode != null  and provinceCode != ''">
            and alstr.sample_province_code = #{provinceCode, jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null  and cityCode != ''">
            and alstr.sample_city_code = #{cityCode, jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null  and districtCode != ''">
            and alstr.sample_district_code = #{districtCode, jdbcType=VARCHAR}
        </if>
        <if test="patientName != null  and patientName != ''">
            and alstr.patient_name like concat('%', #{patientName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="tubeCode != null  and tubeCode != ''">
            and alstr.sample_bar_code  like concat('%', #{tubeCode, jdbcType=VARCHAR}, '%')
        </if>
        <if test="testTubeType != null  and testTubeType != ''">
            and alstr.tube_type = #{testTubeType, jdbcType=VARCHAR}
        </if>
        <if test="specimenType != null  and specimenType != ''">
            and alstr.sample_type_name = #{specimenType, jdbcType=VARCHAR}
        </if>
        <if test="status != null  and status != ''">
            and alstr.item_res_val = #{status, jdbcType=VARCHAR}
        </if>
        order by alstr.sample_time
    </select>

    <select id="getAntigenInfo" resultType="com.iflytek.cdc.edr.dto.preventionControl.AntigenTestInfo">
        select
        alstr.id,
        alstr.patient_name,
        alstr.identity_no,
        alstr.sample_address,
        alstr.test_method as detectionMode,
        alstr.sample_time as detectionTime,
        alstr.item_res_val as detectionResult,
        alstr.inspection_purpose as detectionReason,
        alstr.report_method as reportingMode,
        alstr.report_time as reportingTime
        from ads.ads_lab_sample_test_result alstr
        where alstr.sample_time between #{startTime} and #{endTime}
        and alstr.tests_item_name = #{typeName, jdbcType=VARCHAR}
        <if test="provinceCode != null  and provinceCode != ''">
            and alstr.sample_province_code = #{provinceCode, jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null  and cityCode != ''">
            and alstr.sample_city_code = #{cityCode, jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null  and districtCode != ''">
            and alstr.sample_district_code = #{districtCode, jdbcType=VARCHAR}
        </if>
        <if test="patientName != null  and patientName != ''">
            and alstr.patient_name like concat('%', #{patientName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="detectionMode != null  and detectionMode != ''">
            and alstr.test_method = #{detectionMode, jdbcType=VARCHAR}
        </if>
        <if test="reportingMode != null  and reportingMode != ''">
            and alstr.report_method = #{reportingMode, jdbcType=VARCHAR}
        </if>
        <if test="detectionResult != null  and detectionResult != ''">
            and alstr.item_res_val = #{detectionResult, jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getDetailInfo" resultType="com.iflytek.cdc.edr.entity.multichannel.AdsLabSampleTestResult">
        select *
        from ads.ads_lab_sample_test_result
        where id = #{id}
    </select>

    <select id="getDetailInfoByParams" resultType="com.iflytek.cdc.edr.entity.multichannel.AdsLabSampleTestResult">
        SELECT
            alstr.id,
            alstr.global_person_id,
            alstr.patient_name,
            alstr.patient_sex_name,
            alstr.patient_age,
            alstr.patient_phone,
            alstr.identity_no,
            alstr.item_res_val,
            alstr.tests_item_name,
            alstr.lab_name,
            alstr.sample_bar_code,
            alstr.sample_type_code,
            alstr.sample_type_name,
            alstr.tube_type,
            alstr.sample_time,
            alstr.sample_address,
            alstr.send_time,
            alstr.report_time,
            alstr.batch_num
        FROM
            ads.ads_lab_sample_test_result alstr
        WHERE
            alstr.enabled = '1' AND alstr.deleted = '0'
        <if test="name != null and name != ''">
            AND alstr.patient_name = #{name}
        </if>
        <if test="phone != null and phone != ''">
            AND alstr.patient_phone = #{phone}
        </if>
        <if test="idNo != null and idNo != ''">
            AND alstr.identity_no = #{idNo}
        </if>
        <if test="startSampleTime != null">
            AND alstr.sample_time <![CDATA[>=]]> #{startSampleTime}
        </if>
        <if test="endSampleTime != null">
            AND alstr.sample_time <![CDATA[<=]]> #{endSampleTime}
        </if>
        <if test="testItemType != null and testItemType==1 ">
            AND alstr.tests_item_name = '新冠核酸检测'
        </if>
        <if test="testItemType != null and testItemType==2 ">
            AND alstr.tests_item_name = '新冠抗原检测'
        </if>
        <if test="itemResValType != null and itemResValType==1 ">
            AND alstr.item_res_val = '阴性'
        </if>
        <if test="itemResValType != null and itemResValType==2 ">
            AND alstr.item_res_val = '阳性'
        </if>
        ORDER BY alstr.sample_time DESC
    </select>

    <select id="getTubeTypeList" resultType="java.lang.String">
        select
        distinct alstr.tube_type
        from ads.ads_lab_sample_test_result alstr
        where alstr.sample_time between #{startTime} and #{endTime}
        and alstr.tests_item_name = #{typeName, jdbcType=VARCHAR}
        and alstr.tube_type is not null
        <if test="provinceCode != null  and provinceCode != ''">
            and alstr.sample_province_code = #{provinceCode, jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null  and cityCode != ''">
            and alstr.sample_city_code = #{cityCode, jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null  and districtCode != ''">
            and alstr.sample_district_code = #{districtCode, jdbcType=VARCHAR}
        </if>
        <if test="patientName != null  and patientName != ''">
            and alstr.patient_name like concat('%', #{patientName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="tubeCode != null  and tubeCode != ''">
            and alstr.sample_bar_code  like concat('%', #{tubeCode, jdbcType=VARCHAR}, '%')
        </if>
        <if test="testTubeType != null  and testTubeType != ''">
            and alstr.tube_type = #{testTubeType, jdbcType=VARCHAR}
        </if>
        <if test="specimenType != null  and specimenType != ''">
            and alstr.sample_type_name = #{specimenType, jdbcType=VARCHAR}
        </if>
        <if test="status != null  and status != ''">
            and alstr.item_res_val = #{status, jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getSpecimenTypeList" resultType="java.lang.String">
        select
        distinct alstr.sample_type_name
        from ads.ads_lab_sample_test_result alstr
        where alstr.sample_time between #{startTime} and #{endTime}
        and alstr.tests_item_name = #{typeName, jdbcType=VARCHAR}
        and alstr.sample_type_name is not null
        <if test="provinceCode != null  and provinceCode != ''">
            and alstr.sample_province_code = #{provinceCode, jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null  and cityCode != ''">
            and alstr.sample_city_code = #{cityCode, jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null  and districtCode != ''">
            and alstr.sample_district_code = #{districtCode, jdbcType=VARCHAR}
        </if>
        <if test="patientName != null  and patientName != ''">
            and alstr.patient_name like concat('%', #{patientName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="tubeCode != null  and tubeCode != ''">
            and alstr.sample_bar_code  like concat('%', #{tubeCode, jdbcType=VARCHAR}, '%')
        </if>
        <if test="testTubeType != null  and testTubeType != ''">
            and alstr.tube_type = #{testTubeType, jdbcType=VARCHAR}
        </if>
        <if test="specimenType != null  and specimenType != ''">
            and alstr.sample_type_name = #{specimenType, jdbcType=VARCHAR}
        </if>
        <if test="status != null  and status != ''">
            and alstr.item_res_val = #{status, jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getStatusList" resultType="java.lang.String">
        select
        distinct alstr.item_res_val
        from ads.ads_lab_sample_test_result alstr
        where alstr.sample_time between #{startTime} and #{endTime}
        and alstr.tests_item_name = #{typeName, jdbcType=VARCHAR}
        and alstr.item_res_val is not null
        <if test="provinceCode != null  and provinceCode != ''">
            and alstr.sample_province_code = #{provinceCode, jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null  and cityCode != ''">
            and alstr.sample_city_code = #{cityCode, jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null  and districtCode != ''">
            and alstr.sample_district_code = #{districtCode, jdbcType=VARCHAR}
        </if>
        <if test="patientName != null  and patientName != ''">
            and alstr.patient_name like concat('%', #{patientName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="tubeCode != null  and tubeCode != ''">
            and alstr.sample_bar_code  like concat('%', #{tubeCode, jdbcType=VARCHAR}, '%')
        </if>
        <if test="testTubeType != null  and testTubeType != ''">
            and alstr.tube_type = #{testTubeType, jdbcType=VARCHAR}
        </if>
        <if test="specimenType != null  and specimenType != ''">
            and alstr.sample_type_name = #{specimenType, jdbcType=VARCHAR}
        </if>
        <if test="status != null  and status != ''">
            and alstr.item_res_val = #{status, jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getDetectionModeList" resultType="java.lang.String">
        select
        distinct alstr.test_method
        from ads.ads_lab_sample_test_result alstr
        where alstr.sample_time between #{startTime} and #{endTime}
        and alstr.tests_item_name = #{typeName, jdbcType=VARCHAR}
        and alstr.test_method is not null
        <if test="provinceCode != null  and provinceCode != ''">
            and alstr.sample_province_code = #{provinceCode, jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null  and cityCode != ''">
            and alstr.sample_city_code = #{cityCode, jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null  and districtCode != ''">
            and alstr.sample_district_code = #{districtCode, jdbcType=VARCHAR}
        </if>
        <if test="patientName != null  and patientName != ''">
            and alstr.patient_name like concat('%', #{patientName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="detectionMode != null  and detectionMode != ''">
            and alstr.test_method = #{detectionMode, jdbcType=VARCHAR}
        </if>
        <if test="reportingMode != null  and reportingMode != ''">
            and alstr.report_method = #{reportingMode, jdbcType=VARCHAR}
        </if>
        <if test="detectionResult != null  and detectionResult != ''">
            and alstr.item_res_val = #{detectionResult, jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getReportingTypeList" resultType="java.lang.String">
        select
        distinct alstr.report_method
        from ads.ads_lab_sample_test_result alstr
        where alstr.sample_time between #{startTime} and #{endTime}
        and alstr.tests_item_name = #{typeName, jdbcType=VARCHAR}
        and alstr.report_method is not null
        <if test="provinceCode != null  and provinceCode != ''">
            and alstr.sample_province_code = #{provinceCode, jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null  and cityCode != ''">
            and alstr.sample_city_code = #{cityCode, jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null  and districtCode != ''">
            and alstr.sample_district_code = #{districtCode, jdbcType=VARCHAR}
        </if>
        <if test="patientName != null  and patientName != ''">
            and alstr.patient_name like concat('%', #{patientName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="detectionMode != null  and detectionMode != ''">
            and alstr.test_method = #{detectionMode, jdbcType=VARCHAR}
        </if>
        <if test="reportingMode != null  and reportingMode != ''">
            and alstr.report_method = #{reportingMode, jdbcType=VARCHAR}
        </if>
        <if test="detectionResult != null  and detectionResult != ''">
            and alstr.item_res_val = #{detectionResult, jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getDetectionResultList" resultType="java.lang.String">
        select
        distinct alstr.item_res_val
        from ads.ads_lab_sample_test_result alstr
        where alstr.sample_time between #{startTime} and #{endTime}
        and alstr.tests_item_name = #{typeName, jdbcType=VARCHAR}
        and alstr.item_res_val is not null
        <if test="provinceCode != null  and provinceCode != ''">
            and alstr.sample_province_code = #{provinceCode, jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null  and cityCode != ''">
            and alstr.sample_city_code = #{cityCode, jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null  and districtCode != ''">
            and alstr.sample_district_code = #{districtCode, jdbcType=VARCHAR}
        </if>
        <if test="patientName != null  and patientName != ''">
            and alstr.patient_name like concat('%', #{patientName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="detectionMode != null  and detectionMode != ''">
            and alstr.test_method = #{detectionMode, jdbcType=VARCHAR}
        </if>
        <if test="reportingMode != null  and reportingMode != ''">
            and alstr.report_method = #{reportingMode, jdbcType=VARCHAR}
        </if>
        <if test="detectionResult != null  and detectionResult != ''">
            and alstr.item_res_val = #{detectionResult, jdbcType=VARCHAR}
        </if>
    </select>

</mapper>