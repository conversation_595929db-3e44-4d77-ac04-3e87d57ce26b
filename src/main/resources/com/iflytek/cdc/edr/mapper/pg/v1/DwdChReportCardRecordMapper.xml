<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v1.DwdChReportCardRecordMapper">
    <sql id="statisticSumSql">
        sum(case when d.status in('2', '3', '4', '5', '6', '7', '8') then 1 else 0 end) as totalReportCard,
        sum(case when d.status in('2','3','4','5','6') then 1 else 0 end) as shouldCommitReportCard,
        sum(case when d.status = '2' then 1 else 0 end) as realCommitReportCard,
        sum(case when d.status in ('3', '4', '5', '6') then 1 else 0 end) as notCommitReportCard,
        sum(case when d.timely_status_fbk = '1' and d.status = '2' then 1 else 0 end) as inTimeReportCard,
        sum(case when d.timely_status_fbk = '0' and d.status in ('3','4','5','6') then 1 else 0 end) as omissionReportCard,
        sum(case when d.timely_status_fbk = '0' and d.status = '2' then 1 else 0 end) as lateReportCard,
        sum(case when d.status = '2' then 1 else 0 end) as reportedCardCnt,
        sum(case when d.status = '6' then 1 else 0 end) as waitCardCnt,
        sum(case when d.status = '3' then 1 else 0 end) as auditCardCnt,
        sum(case when d.status = '4' then 1 else 0 end) as noSubmittedCardCnt,
        sum(case when d.status = '5' then 1 else 0 end) as overruleCardCnt,
        sum(case when d.status in ('7','8') then 1 else 0 end) as excludeCardCnt
    </sql>

    <sql id="statisticSql">
        COALESCE(totalReportCard, 0) as totalReportCard,
        COALESCE(shouldCommitReportCard, 0) as shouldCommitReportCard,
        COALESCE(realCommitReportCard, 0) as realCommitReportCard,
        COALESCE(notCommitReportCard, 0) as notCommitReportCard,
        COALESCE(inTimeReportCard, 0) as inTimeReportCard,
        COALESCE(omissionReportCard, 0) as omissionReportCard,
        COALESCE(lateReportCard, 0) as lateReportCard,
        COALESCE(reportedCardCnt, 0) as reportedCardCnt,
        COALESCE(waitCardCnt, 0) as waitCardCnt,
        COALESCE(auditCardCnt, 0) as auditCardCnt,
        COALESCE(noSubmittedCardCnt, 0) as noSubmittedCardCnt,
        COALESCE(overruleCardCnt, 0) as overruleCardCnt,
        COALESCE(excludeCardCnt, 0) as excludeCardCnt,
        case when shouldCommitReportCard != 0 then round(reportedCardCnt::numeric / shouldCommitReportCard::numeric, 4) else 0 end as reportedRate,
        case when shouldCommitReportCard != 0 then round(notCommitReportCard::numeric / shouldCommitReportCard::numeric, 4) else 0 end as noReportedRate,
        case when realCommitReportCard != 0 then round(inTimeReportCard::numeric / realCommitReportCard::numeric, 4) else 0 end as inTimeRate,
        case when shouldCommitReportCard != 0 then round(omissionReportCard::numeric / shouldCommitReportCard::numeric, 4) else 0 end as omissionRate,
        case when realCommitReportCard != 0 then round(lateReportCard::numeric / realCommitReportCard::numeric, 4) else 0 end as lateRate,
        case when totalReportCard != 0 then round(noSubmittedCardCnt::numeric / totalReportCard::numeric, 4) else 0 end as noSubmittedRate,
        case when totalReportCard != 0 then round(excludeCardCnt::numeric / totalReportCard::numeric, 4) else 0 end as excludeRate
    </sql>

    <sql id="fromAreaLevel">
        <choose>
            <when test="areaLevel == 1">
                o.city_code as orgId, o.city_name as orgName,
                null as levelCode, null as levelName,
                null as departmentCode, null as departmentName,
                o.city_code as code, '2' as areaLevel
            </when>
            <when test="areaLevel == 2">
                o.district_code as orgId, o.district_name as orgName,
                null as levelCode, null as levelName,
                null as departmentCode, null as departmentName,
                o.district_code as code, '3' as areaLevel
            </when>
            <when test="areaLevel == 3">
                o.org_id as orgId, o.org_name as orgName,
                o.org_id as levelCode, o.org_name as levelName,
                null as departmentCode, null as departmentName,
                o.org_id as code, '4' as areaLevel
            </when>
            <when test="areaLevel == 4">
                d.dept_code as orgId, d.dept_name as orgName,
                o.org_id as levelCode, o.org_name as levelName,
                d.dept_code as departmentCode, d.dept_name as departmentName,
                d.dept_code as code, '5' as areaLevel
            </when>
            <otherwise>
                d.report_doctor_id as orgId, max(d.report_doctor_name) as orgName,
                o.org_id as levelCode, o.org_name as levelName,
                d.dept_code as departmentCode, d.dept_name as departmentName,
                d.report_doctor_id as code, null as areaLevel
            </otherwise>
        </choose>
    </sql>

    <sql id="areaCodeCriteriaAndGroup">
        <if test="areaCode != null and areaCode != ''">
            <choose>
                <when test="areaLevel == 1"> and o.province_code = #{areaCode} </when>
                <when test="areaLevel == 2"> and o.city_code = #{areaCode} </when>
                <when test="areaLevel == 3"> and o.district_code = #{areaCode} </when>
            </choose>
        </if>
        <choose>
            <when test="areaLevel == 1"> group by o.city_code, o.city_name </when>
            <when test="areaLevel == 2"> group by o.district_code, o.district_name </when>
            <when test="areaLevel == 3"> group by o.org_id, o.org_name </when>
            <when test="areaLevel == 4"> group by o.org_id, o.org_name, d.dept_code, d.dept_name </when>
            <otherwise> group by o.org_id, o.org_name, d.dept_code, d.dept_name, d.report_doctor_id </otherwise>
        </choose>
    </sql>

    <sql id="areaCodeCriteria">
        <if test="areaCode != null and areaCode != ''">
            <choose>
                <when test="areaLevel == 1"> and o.province_code = #{areaCode} </when>
                <when test="areaLevel == 2"> and o.city_code = #{areaCode} </when>
                <when test="areaLevel == 3"> and o.district_code = #{areaCode} </when>
            </choose>
        </if>
    </sql>

    <sql id="reportCardSql">
        d.id as id,
        d.event_id as medicalId,
        d.event_id as eventId,
        d.status as status,
        d.patient_name as patientName,
        d.sex_name as sexName,
        d.exact_age as age,
        d.age_unit_name as ageUnit,
        d.living_addr_province as livingAddrProvinceName,
        d.living_addr_city as livingAddrCityName,
        d.living_addr_county as livingAddrCountyName,
        d.living_addr_detail as livingAddrDetail,
        d.first_onset_date as firstOnsetDate,
        d.infected_sub_Name as infectedSubName,
        d.diagnose_datetime as diagnoseTime,
        d.create_time as createDatetime,
        d.fill_date as fillDatetime,
        d.upload_date_time as uploadDateTime,
        d.report_org_name as reportOrgName,
        d.org_name as orgName,
        d.org_id as orgId,
        d.report_doctor_name as reportDoctorName,
        d.dept_name as reportDeptName,
        d.org_name as orgName,
        d.report_source_name as reportSourceName,
        d.patient_source_code as visitTypeCode,
        d.patient_source as visitTypeName,
        d.company as companyName,
        d.career_type_code as careerTypeCode,
        d.career_type_name as careerTypeName,
        d.person_type_code AS personTypeCode,
        d.person_type_name AS personTypeName,
        d.other_person_type_name AS otherPersonTypeName
    </sql>

    <sql id="orgIdChoose">
        <choose>
            <when test="areaLevel == 1"> and o.province_code = #{orgId} </when>
            <when test="areaLevel == 2"> and o.city_code = #{orgId} </when>
            <when test="areaLevel == 3"> and o.district_code = #{orgId} </when>
            <when test="areaLevel == 4"> and o.org_id = #{orgId} </when>
            <when test="areaLevel == 5"> and o.org_id = #{levelCode} and d.dept_code = #{orgId} </when>
            <otherwise> and o.org_id = #{levelCode} and d.dept_code = #{departmentCode} and d.report_doctor_id = #{orgId} </otherwise>
        </choose>
    </sql>

    <sql id="reportCardCriteria">
        and o.delete_flag = 'N' and o.is_rootorg = '0'
        <if test="status != null and status.size() &gt; 0">
            and d.status in
            <foreach item="item" index="index" collection="status"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startDate != null and endDate != null">
            <choose>
                <when test="dateType == '1'.toString()">
                    and d.create_time between #{startDate} and #{endDate}
                </when>
                <otherwise>
                    and d.diagnose_datetime between #{startDate} and #{endDate}
                </otherwise>
            </choose>
        </if>
        <if test="infectedCode != null and infectedCode.size() &gt; 0">
            and d.infected_sub_code in
            <foreach item="item" index="index" collection="infectedCode"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="patientName != null">
            and d.PATIENT_NAME like concat('%',#{patientName},'%')
        </if>
        <if test="companyNames != null and companyNames.size() &gt; 0">-->
            and d.company in
            <foreach collection="companyNames" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="careerTypeCode != null and careerTypeCode != ''">
            and d.person_type_code = #{careerTypeCode}
        </if>
        <if test="orgId!= null and orgId != ''">
            <include refid="orgIdChoose" />
        </if>
        <if test="orgIds != null and orgIds.size() &gt; 0">
            and o.org_id in
            <foreach item="item" index="index" collection="orgIds"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="provinceCode != null">
            and o.province_code = #{provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null">
            and o.city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null">
            and o.district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
                and o.province_code in
                <foreach item="item" index="index" collection="provinceCodes"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodes != null and cityCodes.size() &gt; 0">
                and o.city_code in
                <foreach item="item" index="index" collection="cityCodes"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodes != null and districtCodes.size() &gt; 0">
                and o.district_code in
                <foreach item="item" index="index" collection="districtCodes"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        <if test="orgTypeCode!= null and orgTypeCode != ''">
            and o.source_type = #{orgTypeCode}
        </if>
        <if test="visitTypeCode != null and visitTypeCode != '9'.toString()">
            and d.patient_source_code = #{visitTypeCode,jdbcType=VARCHAR}
        </if>
        <if test="visitTypeCode == '9'.toString()">
            and (d.patient_source_code is null or d.patient_source_code not in ('2','3'))
        </if>
        <if test="statType ==  '1'.toString()">
            and d.status in('2','3','4','5','6','7','8')
        </if>
        <if test="statType ==  '2'.toString()">
            and d.status in('2','3','4','5','6')
        </if>
        <if test="statType ==  '3'.toString()">
            and d.status = '2'
        </if>
        <if test="statType ==  '4'.toString()">
            and d.timely_status_fbk = '1'
            and d.status = '2'
        </if>
        <if test="statType ==  '5'.toString()">
            and d.timely_status_fbk = '0'
            and d.status in ('3','4','5','6')
        </if>
        <if test="statType ==  '6'.toString()">
            and d.timely_status_fbk = '0'
            and d.status = '2'
        </if>
        <if test="statType ==  '7'.toString()">
            and d.status = '4'
        </if>
        <if test="statType ==  '8'.toString()">
            and d.status in ('7', '8')
        </if>
        -- 未上报报卡数
        <if test="statType == '9'.toString()">
            and d.status in ('3','4','5','6')
        </if>
    </sql>

    <select id="selectReportCardByIds" resultType="com.iflytek.cdc.edr.dto.repcard.ReportCardRecord">
        select
        <include refid="reportCardSql" />
        from dwd.dwd_ch_report_card_record d
        left join dim.dim_organization_info o on d.org_id = o.org_id
        where d.delete_flag = '0'
        and d.id in
        <foreach collection="ids" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="listByMedicalIds" parameterType="com.iflytek.cdc.edr.dto.repcard.ReportCardQueryParam"
            resultType="com.iflytek.cdc.edr.entity.repcard.VInfectionReport">
        select
        *
        from dwd.dwd_ch_report_card_record d
        left join dim.dim_organization_info o on d.org_id = o.org_id
        where d.event_id in
        <foreach collection="medicalIds" open="(" separator="," close=")" item="medicalId">
            #{medicalId}
        </foreach>
        and d.delete_flag = '0'
        <include refid="reportCardCriteria" />
        order by d.create_time desc
    </select>

    <select id="countReportCard" resultType="long">
        select
         count(*)
        from dwd.dwd_ch_report_card_record d
        left join dim.dim_organization_info o on d.org_id = o.org_id
        where d.delete_flag = '0'
        <include refid="reportCardCriteria" />
    </select>

    <select id="listReportCard" resultType="com.iflytek.cdc.edr.dto.repcard.ReportCardRecord">
        select
        <include refid="reportCardSql" />
        from dwd.dwd_ch_report_card_record d
        left join dim.dim_organization_info o on d.org_id = o.org_id
        where d.delete_flag = '0'
        <include refid="reportCardCriteria" />
        order by d.create_time desc
    </select>

    <select id="getTotalReportCardStatistic" parameterType="com.iflytek.cdc.edr.dto.repcard.ReportCardQueryParam"
            resultType="com.iflytek.cdc.edr.dto.repcard.ReportCardStatistic">
        select <include refid="statisticSql" />,
            <choose>
                <when test="areaLevel == 1"> '1' as areaLevel </when>
                <when test="areaLevel == 2"> '2' as areaLevel </when>
                <when test="areaLevel == 3"> '3' as areaLevel </when>
                <when test="areaLevel == 4"> '4' as areaLevel </when>
                <when test="areaLevel == 5"> '5' as areaLevel </when>
                <otherwise> null as areaLevel </otherwise>
            </choose>
        from (
            select <include refid="statisticSumSql" />
            from dwd.dwd_ch_report_card_record d
            join dim.dim_organization_info o on d.org_id = o.org_id
            where d.delete_flag = '0'
            and o.delete_flag = 'N'
            and o.is_rootorg = '0'
            <if test="sourceTypes != null and sourceTypes.size() &gt; 0">
                and o.source_type in
                <foreach item="item" index="index" collection="sourceTypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="startDate != null and endDate != null">
                <choose>
                    <when test="dateType == '1'.toString()">
                        and d.create_time between #{startDate} and #{endDate}
                    </when>
                    <otherwise>
                        and d.diagnose_datetime between #{startDate} and #{endDate}
                    </otherwise>
                </choose>
            </if>
            <if test="orgId!= null and orgId != ''">
                <include refid="orgIdChoose" />
            </if>
            <if test="orgIds != null and orgIds.size() &gt; 0">
                and o.org_id in
                <foreach item="item" index="index" collection="orgIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="provinceCode!= null and provinceCode != ''">
                and o.province_code = #{provinceCode}
            </if>
            <if test="cityCode!= null and cityCode != ''">
                and o.city_code = #{cityCode}
            </if>
            <if test="districtCode!= null and districtCode != ''">
                and o.district_code = #{districtCode}
            </if>
            <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
                and o.province_code in
                <foreach item="item" index="index" collection="provinceCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodes != null and cityCodes.size() &gt; 0">
                and o.city_code in
                <foreach item="item" index="index" collection="cityCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodes != null and districtCodes.size() &gt; 0">
                and o.district_code in
                <foreach item="item" index="index" collection="districtCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="orgTypeCode!= null and orgTypeCode != ''">
                and o.source_type = #{orgTypeCode}
            </if>
            <if test="status != null and status.size>0">
                and d.status in
                <foreach item="item" index="index" collection="status" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="infectedCode != null and infectedCode.size() &gt; 0">
                and d.infected_sub_code in
                <foreach item="item" index="index" collection="infectedCode" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="infectedTypeCode!= null and infectedTypeCode != ''">
                and d.infected_type_code = #{infectedTypeCode}
            </if>
            <if test="visitTypeCode!= null and visitTypeCode != ''">
                and d.patient_source_code = #{visitTypeCode}
            </if>
            <include refid="areaCodeCriteria" />
        ) tab
    </select>

    <select id="getOrgReportCardStatistic" parameterType="com.iflytek.cdc.edr.dto.repcard.ReportCardQueryParam"
            resultType="com.iflytek.cdc.edr.dto.repcard.ReportCardStatistic">
        select <include refid="statisticSql"/>, orgId, orgName, areaLevel, levelCode, levelName, departmentCode, departmentName
        from (
            select <include refid="statisticSumSql" />, <include refid="fromAreaLevel" />
            from dim.dim_organization_info o
            left join dwd.dwd_ch_report_card_record d on d.org_id = o.org_id
            and d.delete_flag = '0'
            <if test="startDate != null and endDate != null">
                <choose>
                    <when test="dateType == '1'.toString()">
                        and d.create_time between #{startDate} and #{endDate}
                    </when>
                    <otherwise>
                        and d.diagnose_datetime between #{startDate} and #{endDate}
                    </otherwise>
                </choose>
            </if>
            <if test="status != null and status.size>0">
                and d.status in
                <foreach item="item" index="index" collection="status" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="infectedCode != null and infectedCode.size() &gt; 0">
                and d.infected_sub_code in
                <foreach item="item" index="index" collection="infectedCode" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="infectedTypeCode!= null and infectedTypeCode != ''">
                and d.infected_type_code = #{infectedTypeCode}
            </if>
            <if test="visitTypeCode!= null and visitTypeCode != ''">
                and d.patient_source_code = #{visitTypeCode}
            </if>
            where o.delete_flag = 'N' and o.is_rootorg = '0'
            <if test="sourceTypes != null and sourceTypes.size() &gt; 0">
                and o.source_type in
                <foreach item="item" index="index" collection="sourceTypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="orgId!= null and orgId != ''">
                <include refid="orgIdChoose" />
            </if>
            <if test="orgIds != null and orgIds.size() &gt; 0">
                and o.org_id in
                <foreach item="item" index="index" collection="orgIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="provinceCode!= null and provinceCode != ''">
                and o.province_code = #{provinceCode}
            </if>
            <if test="cityCode!= null and cityCode != ''">
                and o.city_code = #{cityCode}
            </if>
            <if test="districtCode!= null and districtCode != ''">
                and o.district_code = #{districtCode}
            </if>
            <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
                and o.province_code in
                <foreach item="item" index="index" collection="provinceCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodes != null and cityCodes.size() &gt; 0">
                and o.city_code in
                <foreach item="item" index="index" collection="cityCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="districtCodes != null and districtCodes.size() &gt; 0">
                and o.district_code in
                <foreach item="item" index="index" collection="districtCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="orgTypeCode!= null and orgTypeCode != ''">
                and o.source_type = #{orgTypeCode}
            </if>
            <include refid="areaCodeCriteriaAndGroup" />
        ) tab
        <choose>
            <when test="property != null and (direction != null and (direction == 'asc' or direction == 'desc'))">
                order by ${property} ${direction}, orgId
            </when>
            <otherwise>
                order by reportedCardCnt desc,waitCardCnt desc,auditCardCnt desc,noSubmittedCardCnt desc,overruleCardCnt desc,excludeCardCnt desc,tab.code,orgName
            </otherwise>
        </choose>
    </select>

    <select id="listCompanyNameData" resultType="com.iflytek.cdc.edr.dto.HierarchyData">
        select distinct d.company as label, d.company as value
        from dwd.dwd_ch_report_card_record d
        where d.delete_flag = '0' and char_length(d.company) > 0
        <if test="keyword != null and keyword != ''">
            AND d.company LIKE CONCAT('%', #{keyword}, '%')
        </if>
        order by d.company asc
    </select>

    <select id="listCareerTypeData" resultType="com.iflytek.cdc.edr.dto.HierarchyData">
        select distinct d.person_type_name as label, d.person_type_code as value
        from dwd.dwd_ch_report_card_record d
        where d.delete_flag = '0' and char_length(d.person_type_code) > 0 and char_length(d.person_type_name) > 0
        order by d.person_type_code asc
    </select>

</mapper>
