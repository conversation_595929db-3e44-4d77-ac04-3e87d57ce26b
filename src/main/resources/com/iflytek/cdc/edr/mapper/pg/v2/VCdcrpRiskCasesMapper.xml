<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.VCdcrpRiskCasesMapper">

  <select id="selectList" resultType="com.iflytek.cdc.edr.dto.client.IntelligentWarningListVO">
      select medical_id as eventId,
      patient_type_name as dataSrc,
      patient_type_code as dataSrcCode,
      dept_name as deptName,
      patient_name as patientName,
      sex_name as sexName,
      exact_age as age,
      age_unit_name as ageDesc ,
      diagnose_name as localDiagnosticName,
      visit_datetime as eventDateTime,
      infectious_report_code as infectiousReportFlag,
      infectious_diag_code as infectiousDiagIdentical,
      is_checked as isChecked,
      not_need_report_reason_name as unreportReason,
      doctor_uap_id as operatorId,
      doctor_name as operatorName,
      need_report as isNeedReport,
      region_id as regionId,
      region_name as regionName,
      org_id as orgId,
      org_name as orgName,
      updator as updator,
      updator_name as updatorName,
      not_need_report_reason_text as unreportReasonMsg,
      not_need_report_reason_code as unreportReasonCode,
      infectious_report_name as infectiousReportName,
      infectious_diag_name as infectiousDiagName
      from ads.v_cdcrp_risk_cases
      where delete_flag = '0'
      <if test="patientNameOrPinYin != null and patientNameOrPinYin != ''">
          and (patient_name like concat('%',#{patientNameOrPinYin},'%') or
          patient_pinyin like concat('%',#{patientNameOrPinYin},'%'))
      </if>
      <if test="localDiagnosticNameOrPinYin != null and localDiagnosticNameOrPinYin != ''">
          and (diagnose_name like concat('%',#{localDiagnosticNameOrPinYin},'%') or
          local_diagnostic_pinyin like concat('%',#{localDiagnosticNameOrPinYin},'%'))
      </if>
      <if test="eventBeginDateTime != null and eventEndDateTime != null">
          and visit_datetime between #{eventBeginDateTime} and #{eventEndDateTime}
      </if>
      <if test="isChecked != null">
          and is_checked = #{isChecked}
      </if>
      <if test="orgIds != null and orgIds.size>0">
          and org_id in
          <foreach item="item" index="index" collection="orgIds" open="(" separator="," close=")">
              #{item,jdbcType=VARCHAR}
          </foreach>
      </if>
      <if test="regionIds != null and regionIds.size>0">
          and region_id in
          <foreach item="item" index="index" collection="regionIds" open="(" separator="," close=")">
              #{item,jdbcType=VARCHAR}
          </foreach>
      </if>
      order by visit_datetime desc
  </select>

    <select id="getCount" resultType="com.iflytek.cdc.edr.dto.client.IntelligentCountVO">
        select count(*)                                                             as totalCount,
               (select count(*) from ads.v_cdcrp_risk_cases where is_checked = '0' and delete_flag = '0') as unCheckedCount,
               (select count(*) from ads.v_cdcrp_risk_cases where is_checked = '1' and delete_flag = '0') as checkedCount
        from ads.v_cdcrp_risk_cases
        where delete_flag = '0'
    </select>
</mapper>