<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v1.AppOrgSyndromeSetVisualInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.edr.entity.app.AppOrgSyndromeSetVisualInfo">
        <id column="monitor_set_id" property="monitorSetId" />
        <result column="org_id" property="orgId" />
        <result column="org_name" property="orgName" />
        <result column="syndrome_sub_code" property="syndromeSubCode" />
        <result column="syndrome_sub_name" property="syndromeSubName" />
        <result column="day" property="day" />
        <result column="suspect_trend_day" property="suspectTrendDay" />
        <result column="age_distribute" property="ageDistribute" />
        <result column="risk_diagnose_distribute" property="riskDiagnoseDistribute" />
        <result column="risk_diagnose_rate" property="riskDiagnoseRate" />
        <result column="visit_distribute" property="visitDistribute" />
        <result column="addr_gather" property="addrGather" />
        <result column="age_group_gather" property="ageGroupGather" />
        <result column="etl_create_datetime" property="etlCreateDateTime" />
        <result column="etl_update_datetime" property="etlUpdateDateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        monitor_set_id, org_id, org_name, syndrome_sub_code, syndrome_sub_name, day, suspect_trend_day, age_distribute,
        risk_diagnose_distribute, risk_diagnose_rate, visit_distribute, addr_gather, age_group_gather, etl_create_datetime, etl_update_datetime
    </sql>
    <select id="getByMonitorSetId" resultType="com.iflytek.cdc.edr.entity.app.AppOrgSyndromeSetVisualInfo">
        select *
        from ads.ads_org_syndrome_set_visual_info
        where 1=1
        <if test="monitorSetId != null and monitorSetId !=''">
            and monitor_set_id=#{monitorSetId}
        </if>

        <if test="maxFullDate != null">
           and day = CAST(#{maxFullDate} AS DATE)
        </if>
        <if test="orgId != '' and orgId != null">
            and org_id=#{orgId}
        </if>
        <if test="syndromeSubCode != '' and syndromeSubCode !=null">
            and syndrome_sub_code=#{syndromeSubCode}
        </if>
        limit 1

    </select>
    <select id="getStreetDataByMonitorSetId"
            resultType="com.iflytek.cdc.edr.entity.app.AppOrgSyndromeSetVisualInfo">
        select *
        from ads.ads_street_syndrome_set_visual_info
        where 1=1
        <if test="monitorSetId != null and monitorSetId !=''">
            and monitor_set_id=#{monitorSetId}
        </if>

        <if test="maxFullDate != null ">
            and day = CAST(#{maxFullDate} AS DATE)
        </if>
        <if test="streetCode != '' and streetCode != null">
            and street_code=#{streetCode}
        </if>
        <if test="syndromeSubCode != '' and syndromeSubCode !=null">
            and syndrome_sub_code=#{syndromeSubCode}
        </if>
        limit 1

    </select>


    <select id="medicalDistributionList" resultType="com.iflytek.cdc.edr.dto.syndrome.AppSyndromeMedicalDetailDTO">
        select * from ads.ads_syndrome_medical_detail t
        where  1=1
        and t.syndrome_sub_code=#{syndromeSubCode}
        and t.visit_day between CAST(#{minFullDate} AS DATE) and CAST(#{maxFullDate} AS DATE)
        <if test="orgId != '' and orgId != null">
            and t.org_id_attent=#{orgId}
        </if>
        <if test="streetCode != '' and streetCode != null">
            and t.street_code=#{streetCode}
        </if>
        <if test="ageGroup != '' and ageGroup != null">
            and t.age_group=#{ageGroup}
        </if>
        <if test="medicalType == 'justModelFlag' ">
            and t.just_model_flag='1'
        </if>
        <if test="medicalType == 'abnormalTimeFlag' ">
            and t.abnormal_time_flag='1'
        </if>
        <if test="medicalType == 'enhanceConditionFlag' ">
            and t.enhance_condition_flag='1'
        </if>
        <if test="riskDiagnoseRiskLevel != '' and riskDiagnoseRiskLevel != null">
            and t.risk_diagnose_risk_level=#{riskDiagnoseRiskLevel}
        </if>
        <if test="riskDiagnose != '' and riskDiagnose != null">
            and t.risk_diagnose=#{riskDiagnose}
        </if>
        order by t.visit_time desc
    </select>

</mapper>
