<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.DimOrganizationInfoMapper">

    <select id="findAll" resultType="com.iflytek.cdc.edr.dto.org.OrgInfoDto">
        select
        org_id,org_name,
        CASE WHEN higher_org_id is null THEN '' ELSE higher_org_id END AS higher_org_id,
        org_type_code,
        province_code,province_name,city_code,city_name,district_code,district_name
        from dim.dim_organization_info
        where delete_flag = '0'
        <if test="sourceTypes != null and sourceTypes.size() &gt; 0">
            and (org_type_code = '100'
            or source_type in
            <foreach item="item" index="index" collection="sourceTypes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>)
        </if>
        <if test="provinceCodes !=null and provinceCodes.size() > 0">
            and province_code  in
            <foreach item="item" index="index" collection="provinceCodes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes !=null and cityCodes.size() > 0">
            and city_code  in
            <foreach item="item" index="index" collection="cityCodes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes !=null and districtCodes.size() > 0">
            and district_code  in
            <foreach item="item" index="index" collection="districtCodes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findById" resultType="com.iflytek.cdc.edr.entity.org.DimOrganizationInfo">
        select *
        from dim.dim_organization_info
        where org_id = #{orgId}
    </select>
</mapper>