<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v1.TbCdcewUserConfigMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.edr.entity.TbCdcewUserConfig">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="uap_user_id" jdbcType="VARCHAR" property="uapUserId"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="update_time" jdbcType="DATE" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , uap_user_id, "status", update_time
    </sql>

    <insert id="upsert">
        insert into "tb_cdcew_user_desensitization" (id, uap_user_id, "status",
                                                     update_time)
        values (#{id,jdbcType=VARCHAR}, #{uapUserId,jdbcType=VARCHAR}, #{status,jdbcType=SMALLINT},
                #{updateTime}) on conflict(uap_user_id) do
        update
            set
                "status" = excluded.status,
            update_time = excluded.update_time
    </insert>
    <select id="getDesensitization" resultType="com.iflytek.cdc.edr.entity.TbCdcewUserConfig">
        select *
        from tb_cdcew_user_config
        where uap_user_id = #{loginUserId} and config_type = #{configType}
    </select>


</mapper>