<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.AdsSyndromeMappingResMapper">



    <!-- 通用查询结果列 -->

    <select id="findList" resultType="com.iflytek.cdc.edr.entity.ads.AdsSyndromeMappingRes">
        select
        org_id,
        medical_id,
        org_longitude,
        org_latitude,
        living_address_longitude,
        living_address_latitude,
        company_address_longitude,
        company_address_latitude
        from
        ads.v_syndrome_mapping_res
        <where>
            <if test="startDate != null and endDate != null">
                and  CAST(full_date AS DATE) between  #{startDate} and  #{endDate}
            </if>
            <if test="regionInfoList!=null and regionInfoList.size() &gt; 0">
                and
                <foreach close=")" collection="regionInfoList" index="index" item="item" open="(  " separator=" or ">
                    (1=1
                    <if test="item.provinceCode != null and item.provinceCode != '' ">
                        and PROVINCE_CODE = #{item.provinceCode}
                    </if>
                    <if test="item.cityCode != null and item.cityCode != '' ">
                        and CITY_CODE = #{item.cityCode}
                    </if>
                    <if test="item.districtCode != null and item.districtCode !='' ">
                        and DISTRICT_CODE = #{item.districtCode}
                    </if>)
                </foreach>
            </if>
            <if test="symptom != null and symptom.size() &gt; 0">
                and parent_syndrome_code in
                <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
