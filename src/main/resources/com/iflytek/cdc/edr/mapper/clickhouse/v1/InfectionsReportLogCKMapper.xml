<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.clickhouse.v1.InfectionsReportLogCKMapper">
    <sql id="outpatientReport">
        dcmr.his_medical_id outpatient_id,
        dcmr.patient_name,
        dcmr.sex_name,
        CONCAT(dcmr.age, age_unit_name) AS age,
        dcmr.patient_id_number,
        dcmr.job occupation,
        dcmr.dept_name,
        dcmr.visit_doc_name AS doctor_name,
        dcmr.diagnose_name,
        doi.source_type,
        case when doi.source_type = '110' then '等级医院' when doi.source_type = '120' then '基层医疗' end sourceTypeName,
        doi.org_name as orgName,
        formatDateTime(toDateTime(dcmr.event_datetime), '%Y-%m-%d') AS visit_date,
        formatDateTime(toDateTime(dcmr.start_sick_datetime), '%Y-%m-%d') AS start_sick_date,
        case when dcmr.is_first_diag = '0' then '复诊' else '初诊' end first_diag_flag,
        dcmr.current_addr_detail living_address,
        dcmr.event_id,
        dcmr.medical_id
    </sql>
    <select id="selectByMedicalId" resultType="com.iflytek.cdc.edr.dto.infections.OutpatientReportLog">
        SELECT
        <include refid="outpatientReport"/>
        FROM dwd_ch_medical_record dcmr
        JOIN dim_organization_info doi ON doi.org_id = dcmr.org_id
        WHERE dcmr.medical_id = #{medicalId}
        limit 1
    </select>

    <select id="getOutpatientReportLog" resultType="com.iflytek.cdc.edr.dto.infections.OutpatientReportLog">
        SELECT dcmr.his_medical_id outpatient_id,
               dcmr.patient_name,
               dcmr.sex_name,
               CONCAT(dcmr.age, age_unit_name) AS age,
               dcmr.patient_id_number,
               dcmr.job occupation,
               dcmr.dept_name,
               dcmr.visit_doc_name AS doctor_name,
               dcmr.diagnose_name,
               dcmr.disease_name,
               case when doi.source_type = '110' then '等级医院' when doi.source_type = '120' then '基层医疗' end sourceTypeName,
               doi.org_name as orgName,
               formatDateTime(toDateTime(dcmr.event_datetime), '%Y-%m-%d') AS visit_date,
               formatDateTime(toDateTime(dcmr.start_sick_datetime), '%Y-%m-%d') AS start_sick_date,
               case when dcmr.is_first_diag = '0' then '复诊' else '初诊' end first_diag_flag,
               dcmr.current_addr_detail living_address,
               dcmr.event_id,
               dcmr.medical_id
          FROM dwd_ch_medical_record dcmr
          JOIN dim_organization_info doi ON doi.org_id = dcmr.org_id
         WHERE dcmr.event_datetime >= #{startDate}::timestamp
           AND dcmr.event_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
           AND doi.source_type in ('110', '120')
        <if test="provinceCodes != null and provinceCodes.size() > 0">
            and doi.province_code in
            <foreach collection="provinceCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() > 0">
            and doi.city_code in
            <foreach collection="cityCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() > 0">
            and doi.district_code in
            <foreach collection="districtCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="provinceCode != null">
           AND doi.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
           AND doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null">
           AND doi.district_code = #{districtCode}
        </if>
        <if test="sourceType != null and sourceType != ''">
           AND doi.source_type = #{sourceType}
        </if>
        <if test="orgList != null and orgList.size() > 0">
            AND doi.org_id in
            <foreach item="item" index="index" collection="orgList" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="patientName != null and patientName != ''">
           AND dcmr.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
           AND dcmr.diagnose_name LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isInfectious != null">
           AND dcmr.is_infectious = #{isInfectious}
        </if>
        <if test="infectedName != null">
           AND dcmr.diagnose_name LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="firstDiagFlag != null and firstDiagFlag != ''">
            AND is_first_diag = #{firstDiagFlag}
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
        <if test="occupation != null and occupation != ''">
            AND dcmr.job LIKE concat('%', #{occupation, jdbcType=VARCHAR}, '%')
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            AND dcmr.disease_name LIKE concat('%', #{diseaseName, jdbcType=VARCHAR}, '%')
        </if>
        order by formatDateTime(toDateTime(dcmr.visit_datetime), '%Y-%m-%d') DESC,
        doi.org_name,
        dcmr.his_medical_id asc

    </select>

    <select id="getAllOutpatientReportLog" resultType="com.iflytek.cdc.edr.dto.infections.OutpatientReportLog">
        SELECT dcmr.his_medical_id outpatient_id,
        dcmr.patient_name,
        dcmr.sex_name,
        CONCAT(dcmr.age, age_unit_name) AS age,
        dcmr.patient_id_number,
        dcmr.job occupation,
        dcmr.dept_name,
        dcmr.visit_doc_name AS doctor_name,
        dcmr.diagnose_name,
        dcmr.disease_name,
        case when doi.source_type = '110' then '等级医院' when doi.source_type = '120' then '基层医疗' end sourceTypeName,
        doi.org_name as orgName,
        formatDateTime(toDateTime(dcmr.event_datetime), '%Y-%m-%d') AS visit_date,
        formatDateTime(toDateTime(dcmr.start_sick_datetime), '%Y-%m-%d') AS start_sick_date,
        case when dcmr.is_first_diag = '0' then '复诊' else '初诊' end first_diag_flag,
        dcmr.current_addr_detail living_address,
        dcmr.event_id
        FROM dwd_ch_medical_record dcmr
        JOIN dim_organization_info doi ON doi.org_id = dcmr.org_id
        WHERE dcmr.event_datetime >= #{startDate}::timestamp
        AND dcmr.event_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
        AND doi.source_type in ('110', '120')
        <if test="provinceCode != null">
            AND doi.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
            AND doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null">
            AND doi.district_code = #{districtCode}
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND doi.source_type = #{sourceType}
        </if>
        <if test="orgList != null and orgList.size() > 0">
            AND doi.org_id in
            <foreach item="item" index="index" collection="orgList" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="patientName != null and patientName != ''">
            AND dcmr.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
            AND dcmr.diagnose_name LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isInfectious != null">
            AND dcmr.is_infectious = #{isInfectious}
        </if>
        <if test="infectedName != null">
            AND dcmr.diagnose_name LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="firstDiagFlag != null and firstDiagFlag != ''">
            AND is_first_diag = #{firstDiagFlag}
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
        <if test="occupation != null and occupation != ''">
            AND dcmr.job LIKE concat('%', #{occupation, jdbcType=VARCHAR}, '%')
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            AND dcmr.disease_name LIKE concat('%', #{diseaseName, jdbcType=VARCHAR}, '%')
        </if>
        order by formatDateTime(toDateTime(dcmr.visit_datetime), '%Y-%m-%d') DESC,
        doi.org_name,
        dcmr.his_medical_id asc
    </select>

</mapper>