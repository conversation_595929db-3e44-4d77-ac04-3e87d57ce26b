<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v1.ExternalMapper">

    <select id="getMainSuitData" resultType="com.iflytek.cdc.edr.dto.external.DwdChMedicalRecord">
        SELECT *
        FROM dwd.dwd_ch_medical_record
        WHERE org_id = #{orgId}
          AND CAST(event_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getDiagnoseDetail" resultType="com.iflytek.cdc.edr.dto.external.DwdChMedicalDiagnoseResult">
        SELECT *
        FROM dwd.dwd_ch_medical_diagnose_result
        WHERE org_id = #{orgId}
          AND CAST(diagnose_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getPrescription" resultType="com.iflytek.cdc.edr.dto.external.DwdChPrescription">
        SELECT *
        FROM dwd.dwd_ch_prescription
        WHERE org_id = #{orgId}
          AND CAST(apply_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getMedicalRecord" resultType="com.iflytek.cdc.edr.dto.external.OdsDjyyJkV303EmrBc">
        select *
        from ods.ods_djyy_jk_v303_emr_bc a
                 left join dim.dim_medical_organization_info o
                           on a.stander_org_code = o.org_code
        where o.org_id = #{orgId}
          and CAST(a.record_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getDoctorAdviceInformation" resultType="com.iflytek.cdc.edr.dto.external.OdsDjyyJkV107HisYz">
        select *
        from ods.ods_djyy_jk_v107_his_yz a
                 left join dim.dim_medical_organization_info o
                           on a.stander_org_code = o.org_code
        where o.org_id = #{orgId}
          and CAST(a.order_date AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getGeneralSituation" resultType="com.iflytek.cdc.edr.dto.external.OdsDjyyJkV107HisTz">
        select *
        from ods.ods_djyy_jk_v107_his_tz a
                 left join dim.dim_medical_organization_info o
                           on a.stander_org_code = o.org_code
        where o.org_id = #{orgId}
          and CAST(a.create_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getCheckDetail" resultType="com.iflytek.cdc.edr.dto.external.DwdChCheckReport">
        SELECT *
        FROM dwd.dwd_ch_check_report
        WHERE org_id = #{orgId}
          AND CAST(check_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getExamineDetail" resultType="com.iflytek.cdc.edr.dto.external.DwdChInspectionReport">
        SELECT *
        FROM dwd.dwd_ch_inspection_report
        WHERE org_id = #{orgId}
          AND CAST(result_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getReportCardRecord" resultType="com.iflytek.cdc.edr.dto.external.DwdChReportCardRecord">
        SELECT *
        FROM dwd.dwd_ch_report_card_record
        WHERE org_id = #{orgId}
          AND CAST(create_time AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getInHospitalRecord" resultType="com.iflytek.cdc.edr.dto.external.DwdChInhospitalRecord">
        SELECT *
        FROM dwd.dwd_ch_inhospital_record
        WHERE org_id = #{orgId}
          AND CAST(in_hos_date AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getPatientInformation" resultType="com.iflytek.cdc.edr.dto.external.DimMdmPersonInfo">

    </select>
    <select id="getSymptomInformation" resultType="com.iflytek.cdc.edr.dto.external.DwdSyndromeMedicalSymptomTag">
        SELECT *
        FROM dwd.dwd_syndrome_medical_symptom_tag
        WHERE org_id = #{orgId}
          AND CAST(event_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getReportCardRecordByEventIds"
            resultType="com.iflytek.cdc.edr.dto.external.DwdChReportCardRecord">
        select *
        FROM dwd.dwd_ch_report_card_record
        where event_id in
        <foreach collection="eventIds" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="findBySymptomAndDate" resultType="com.iflytek.cdc.edr.dto.external.DwdChMedicalRecord">
        select
        *
        from dwd.dwd_ch_medical_record
        where medical_id in
        (select medical_id
        from dwd.dwd_syndrome_medical_symptom_tag s
        where event_datetime <![CDATA[>=]]> #{startDate,jdbcType=TIMESTAMP}
        and event_datetime <![CDATA[<=]]> #{endDate,jdbcType=TIMESTAMP}
        and symptom_tag_list like concat('%',#{symptom,jdbcType=VARCHAR}::varchar,'%'))
    </select>

    <select id="getSyndromeMedicalBy" resultType="com.iflytek.cdc.edr.dto.external.MedicalMappingResultDTO">
        select
        t1.medical_id,
        t1.global_person_id,
        t1.patient_id,
        t1.syndrome_code as diseaseCode,
        t3.std_living_addr_area as livingAddress,
        t4.address_longitude as livingAddressLongitude,
        t4.address_latitude as livingAddressLatitude
        from dwd.dwd_syndrome_mapping_res t1
        left join dim.dim_organization_info t2 on t1.org_id = t2.org_id
        left join dim.dim_patient_best_record t3 on t1.global_person_id = t3.global_patient_id
        left join dim.dim_address_standard t4 on t3.std_living_addr_area_code = t4.address_area_code
        where t1.full_date = #{fullDate} and t1.syndrome_code = #{diseaseCode}
        <if test="orgIds != null and orgIds.size() > 0">
            and t1.org_id in
            <foreach item="item" index="index" collection="orgIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getInfectedMedicalBy" resultType="com.iflytek.cdc.edr.dto.external.MedicalMappingResultDTO">
        select
        r.id as medicalId,
        r.global_person_id,
        r.patient_name,
        r.patient_id,
        r.infected_sub_code as diseaseCode,
        t3.std_living_addr_area as livingAddress,
        t4.address_longitude as livingAddressLongitude,
        t4.address_latitude as livingAddressLatitude
        from dw."dwd_ch_report_card_record" r
        join dim.dim_organization_info o on r.org_id = o.org_id
        left join dim.dim_infected_info dii on r.infected_sub_code = dii.infected_sub_code
        join dim.dim_patient_best_record t3 on r.global_person_id = t3.global_patient_id
        join dim.dim_address_standard t4 on t3.std_living_addr_area_code = t4.address_area_code
        where r.infected_sub_code is not null and r.fill_date = #{fullDate} and r.infected_sub_code = #{diseaseCode}
        <if test="orgIds != null and orgIds.size() > 0">
            and r.org_id in
            <foreach item="item" index="index" collection="orgIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        union
        select
        r.medical_id,
        r.global_person_id,
        r.patient_name,
        null as patientId,
        dd.infected_sub_code as diseaseCode,
        t3.std_living_addr_area as livingAddress,
        t4.address_longitude as livingAddressLongitude,
        t4.address_latitude as livingAddressLatitude
        from dwd.dwd_ch_medical_suspect_record r
        join dwd.dwd_ch_medical_suspect_detail dd on r.event_id = dd.event_id
        left join dim.dim_infected_info dii on dd.infected_sub_code = dii.infected_sub_code
        join dim.dim_patient_best_record t3 on r.global_person_id = t3.global_patient_id
        join dim.dim_address_standard t4 on t3.std_living_addr_area_code = t4.address_area_code
        where dd.infected_sub_code is not null and r.full_date = #{fullDate} and dd.infected_sub_code = #{diseaseCode}
        <if test="orgIds != null and orgIds.size() > 0">
            and r.org_id in
            <foreach item="item" index="index" collection="orgIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getSymptomMedicalBy" resultType="com.iflytek.cdc.edr.dto.external.MedicalMappingResultDTO">
        select
        s.record_id as medicalId,
        s.global_person_id,
        s.person_name as patientName,
        s.person_id as patientId,
        s.symptom_code as diseaseCode,
        t3.std_living_addr_area as livingAddress,
        t4.address_longitude as livingAddressLongitude,
        t4.address_latitude as livingAddressLatitude
        from dwd.dwd_symp_mapping s
        join dim.dim_patient_best_record t3 on s.global_person_id = t3.global_patient_id
        join dim.dim_address_standard t4 on t3.std_living_addr_area_code = t4.address_area_code
        where s.full_date = #{fullDate} and s.symptom_code = #{diseaseCode}
        <if test="orgIds != null and orgIds.size() > 0">
            and s.org_id in
            <foreach item="item" index="index" collection="orgIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getPoisonMedicalBy" resultType="com.iflytek.cdc.edr.dto.external.MedicalMappingResultDTO">
        select
        t1.medical_id,
        t1.patient_name,
        t1.global_person_id,
        t1.tag_code as diseaseCode,
        t3.std_living_addr_area as livingAddress,
        t4.address_longitude as livingAddressLongitude,
        t4.address_latitude as livingAddressLatitude
        from dwd.dwd_ch_medical_mapping_result t1
        left join dim.dim_organization_info t2 on t1.org_id = t2.org_id
        left join dim.dim_patient_best_record t3 on t1.global_person_id = t3.global_patient_id
        left join dim.dim_address_standard t4 on t3.std_living_addr_area_code = t4.address_area_code
        where t1.full_date = #{fullDate} and t1.tag_code = #{diseaseCode}
        <if test="orgIds != null and orgIds.size() > 0">
            and t2.stat_org_id in
            <foreach item="item" index="index" collection="orgIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>