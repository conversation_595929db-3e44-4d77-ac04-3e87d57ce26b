<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v1.DiseaseRecordMapper">

    <select id="getSummary" resultType="com.iflytek.cdc.edr.dto.outpatient.EdrSummaryDto">
        SELECT CAST(full_date AS DATE) AS stat_date,
               health_files_total::BIGINT AS disease_record_total,
               infection_patients_total::BIGINT AS infection_patients_total
        FROM dw.dws_edr_stat_indicator
        ORDER BY full_date DESC LIMIT 1
    </select>

    <select id="loadByMedicalId" resultType="com.iflytek.cdc.edr.dto.event.EventInfoDto">
        SELECT
        der.event_id as medicalId,
        der.event_id as eventId,
        der.data_src AS data_src_code,
        doi.org_type_code,
        doi.org_type_name,
        doi.org_id,
        doi.org_name,
        doi.org_address,
        der.drug_prescription_tag,
        der.health_status,
        der.event_datetime,
        der.global_person_id
        FROM dwd.dwd_event_record der
        INNER JOIN dim.dim_organization_info doi ON doi.org_id = der.org_id
        WHERE der.event_id = #{medicalId}
        --AND der.event_datetime >= CURRENT_DATE - INTERVAL '30 days'
        ORDER BY der.event_datetime DESC
        limit 1
    </select>

    <select id="listByMedicalIds" resultType="com.iflytek.cdc.edr.dto.event.EventInfoDto">
        SELECT
        der.event_id as medicalId,
        der.event_id as eventId,
        der.data_src AS data_src_code,
        doi.org_type_code,
        doi.org_type_name,
        doi.org_id,
        doi.org_name,
        doi.org_address,
        der.drug_prescription_tag,
        der.health_status,
        der.event_datetime,
        der.global_person_id
        FROM dwd.dwd_event_record der
        INNER JOIN dim.dim_organization_info doi ON doi.org_id = der.org_id
        WHERE der.event_id in
        <foreach collection="medicalIds" open="(" separator="," close=")" item="medicalId">
            #{medicalId}
        </foreach>
        --AND der.event_datetime >= CURRENT_DATE - INTERVAL '30 days'
        ORDER BY der.event_datetime DESC
    </select>

    <select id="listByGlobalPersonIds" resultType="com.iflytek.cdc.edr.dto.event.EventInfoDto">
        SELECT
        der.event_id as medicalId,
        der.event_id as eventId,
        der.data_src AS data_src_code,
        doi.org_type_code,
        doi.org_type_name,
        doi.org_id,
        doi.org_name,
        doi.org_address,
        der.drug_prescription_tag,
        der.health_status,
        der.event_datetime,
        der.global_person_id
        FROM dwd.dwd_event_record der
        INNER JOIN dim.dim_organization_info doi ON doi.org_id = der.org_id
        WHERE der.global_person_id in
        <foreach collection="globalPersonIds" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        --AND der.event_datetime >= CURRENT_DATE - INTERVAL '30 days'
        ORDER BY der.event_datetime DESC
    </select>

    <select id="getEventList" resultType="com.iflytek.cdc.edr.dto.event.EventInfoDto">
        SELECT der.event_id,
               der.event_id as medical_id,
               der.data_src AS data_src_code,
               doi.org_type_code,
               doi.org_type_name,
               doi.org_id,
               doi.org_name,
               doi.org_address,
               der.drug_prescription_tag,
               der.health_status,
               der.event_datetime
        FROM dwd.dwd_event_record der
                 INNER JOIN dim.dim_organization_info doi ON doi.org_id = der.org_id
        WHERE der.global_person_id = #{globalPersonId}
              --AND der.event_datetime >= CURRENT_DATE - INTERVAL '30 days'
        ORDER BY der.event_datetime DESC
    </select>

    <select id="getInspectionItems" resultType="com.iflytek.cdc.edr.dto.inspection.InspectionItem">
        SELECT '' as inspect_item_code, inspect_item_name, is_infected
        FROM dim.dim_ch_inspection_item
        <if test="inspectItemName != null and inspectItemName != ''">
            WHERE inspect_item_name like concat('%', #{inspectItemName, jdbcType=VARCHAR}, '%')
        </if>

    </select>

    <select id="getInspectionDetail" resultType="com.iflytek.cdc.edr.dto.inspection.InspectionDetail">
        SELECT CASE data_src WHEN '2' THEN '门诊' WHEN '3' THEN '住院' END AS visit_type,
        CAST(visit_time AS DATE) as visit_time,
        business_id,
        patient_name,
        sex_name AS sex,
        concat(age, age_unit) AS age,
        inspect_item_code,
        CAST(result_datetime AS DATE) as result_datetime,
        inspect_item_name,
        result,
        exam_result_unit,
        refrange AS ref_range,
        abnormal,
        exam_item_name,
        inspect_id,
        specimen_name,
        CAST(submi_datetime AS DATE) AS submit_datetime,
        dept_name,
        CASE is_exist_report_card WHEN '1' THEN '是' ELSE '否' END AS is_exist_report_card
        FROM ads.ads_ch_inspection_report t
        WHERE result_datetime >= #{startDate}::timestamp
        AND result_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
        <if test="reportOrgCode != null">
            AND org_id = #{reportOrgCode}
        </if>
        <if test="patientName != null and patientName != ''">
            AND patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND SUBSTR(source_id, 1, 3) = #{sourceType}
        </if>
        <if test="patientSourceCode != null and patientSourceCode != ''">
            AND data_src = #{patientSourceCode}
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="abnormal != null and abnormal != ''">
            AND abnormal = #{abnormal}
        </if>
        <foreach collection="inspectItemList" index="index" item="insItem" open=" AND (" separator=" OR " close=")">
            inspect_item_name = #{insItem}
        </foreach>
        ORDER BY coalesce(t.visit_time, '1900-01-01'::timestamp) DESC
        LIMIT ${pageSize} OFFSET (${pageIndex} - 1) * ${pageSize}
    </select>

    <select id="getDicts" resultType="com.iflytek.cdc.edr.dto.DictDto">
        SELECT dict_value_code, dict_value_name
        FROM dim.dim_dict
        WHERE dict_type = #{dictType}
        AND dict_code = #{dictCode}
        <if test="dictValue != null and dictValue != ''">
            AND dict_value_name like concat('%', #{dictValue, jdbcType=VARCHAR}, '%')
        </if>
    </select>
    <select id="getAllInspectionDetail" resultType="com.iflytek.cdc.edr.dto.inspection.InspectionDetail">
        SELECT CASE data_src WHEN '2' THEN '门诊' WHEN '3' THEN '住院' END AS visit_type,
        CAST(visit_time AS DATE) as visit_time,
        business_id,
        patient_name,
        sex_name AS sex,
        concat(age, age_unit) AS age,
        inspect_item_code,
        CAST(result_datetime AS DATE) as result_datetime,
        inspect_item_name,
        result,
        exam_result_unit,
        refrange AS ref_range,
        abnormal,
        exam_item_name,
        inspect_id,
        specimen_name,
        CAST(submi_datetime AS DATE) AS submit_datetime,
        dept_name,
        CASE is_exist_report_card WHEN '1' THEN '是' ELSE '否' END AS is_exist_report_card
        FROM ads.ads_ch_inspection_report t
        WHERE result_datetime >= #{startDate}::timestamp
        AND result_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
        <if test="reportOrgCode != null">
            AND org_id = #{reportOrgCode}
        </if>
        <if test="patientName != null and patientName != ''">
            AND patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND SUBSTR(source_id, 1, 3) = #{sourceType}
        </if>
        <if test="patientSourceCode != null and patientSourceCode != ''">
            AND data_src = #{patientSourceCode}
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="abnormal != null and abnormal != ''">
            AND abnormal = #{abnormal}
        </if>
        <foreach collection="inspectItemList" index="index" item="insItem" open=" AND (" separator=" OR " close=")">
            inspect_item_name = #{insItem}
        </foreach>
        ORDER BY coalesce(t.visit_time, '1900-01-01'::timestamp) DESC
    </select>
    <select id="getEventListByMedicalIds" resultType="com.iflytek.cdc.edr.dto.event.EventInfoDto">
        SELECT
            der.event_id as medicalId,
            der.event_id as eventId,
            der.data_src AS data_src_code,
            doi.org_type_code,
            doi.org_type_name,
            doi.org_id,
            doi.org_name,
            doi.org_address,
            der.drug_prescription_tag,
            der.health_status,
            der.event_datetime,
            der.global_person_id
        FROM dwd.dwd_event_record der
                 INNER JOIN dim.dim_organization_info doi ON doi.org_id = der.org_id
                 inner join dwd.dwd_ch_medical_record r on der.event_id = r.event_id
        WHERE r.medical_id in
        <foreach close=")" collection="medicalIds" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        ORDER BY der.event_datetime DESC
    </select>
    <select id="getInspectionDetailByLis" resultType="com.iflytek.cdc.edr.vo.LisInfoVO">
        SELECT t.inspect_id as hisInspectId,
        t.org_name as orgName,
        CASE t.data_src WHEN '2' THEN '门诊' WHEN '3' THEN '住院' END AS visitTypeName,
        t.data_src as visitTypeCode,
        CAST(t.visit_time AS DATE) as visitTime,
        t.business_id as serialNo,
        t.patient_name as patientName,
        t.sex_name as patientSexDesc,
        concat(t.age, t.age_unit) as patientAge,
        CAST(t.result_datetime AS DATE) as detectTime,
        t.inspect_item_code as itemCode,
        t.inspect_item_name as itemName,
        t.refrange AS referenceValue,
        t.abnormal as itemResVal,
        CONCAT(t.result, t.exam_result_unit) as itemResNum,
        t.disease_name as diseaseName,
        t.is_exist_report_card as isExistReportCard,
        t.recent_report_card_datetime as reportCardDatetime,
        t.event_id as medicalId,
        t.exam_item_name as inspectName,
        t.report_card_type AS reportCardType,
        t.report_card_type AS reportCardTypeName
        FROM ads.ads_ch_inspection_report t
        left JOIN dim.dim_organization_info doi ON doi.org_id = t.org_id
        WHERE t.result_datetime >= #{startDate}::timestamp
        AND t.result_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
        <if test="orgList != null and orgList.size() &gt; 0">
            <foreach collection="orgList" index="index" item="orgId" open=" AND (" separator=" OR " close=")">
                t.org_id = #{orgId}
            </foreach>
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and doi.province_code = #{provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and doi.city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and doi.district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and doi.province_code in
            <foreach item="item" index="index" collection="provinceCodes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and doi.city_code in
            <foreach item="item" index="index" collection="cityCodes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and doi.district_code in
            <foreach item="item" index="index" collection="districtCodes"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="patientName != null and patientName != ''">
            AND t.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND SUBSTR(t.source_id, 1, 3) = #{sourceType}
        </if>
        <if test="patientSourceCode != null and patientSourceCode != ''">
            AND t.data_src = #{patientSourceCode}
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND t.is_exist_report_card = #{isExistReportCard}
            <if test="isExistReportCard == 1 and reportCardTypeList != null and reportCardTypeList.size() > 0">
                <foreach collection="reportCardTypeList" item="reportCardType" index="index" open=" AND (" separator=" OR " close=")">
                    t.report_card_type LIKE CONCAT('%', #{reportCardType}, '%')
                </foreach>
            </if>
        </if>
        <if test="abnormal != null and abnormal != ''">
            AND t.abnormal = #{abnormal}
        </if>
        <if test="inspectItemList != null and inspectItemList.size() &gt; 0">
            <foreach collection="inspectItemList" index="index" item="insItem" open=" AND (" separator=" OR " close=")">
                t.inspect_item_name = #{insItem}
            </foreach>
        </if>
        <if test="diseaseCode != null and diseaseCode.size() &gt; 0">
            <foreach collection="diseaseCode" index="index" item="disease" open=" AND (" separator=" OR " close=")">
                t.disease_code = #{disease}
            </foreach>
        </if>
        <if test="customQueryParamList != null and customQueryParamList.size() > 0">
            AND (
            <trim prefixOverrides="AND|OR">
                <foreach collection="customQueryParamList" item="queryParam" index="index">
                    <choose>
                        <when test="queryParam.logicalType == 'and'"> AND </when>
                        <when test="queryParam.logicalType == 'or'"> OR </when>
                    </choose>
                    <choose>
                        <when test="queryParam.paramName == 'age'">
                            <choose>
                                <when test="queryParam.compareType == 'eq'"> t.age = #{queryParam.paramValue} </when>
                                <when test="queryParam.compareType == 'ne'"> (t.age != #{queryParam.paramValue} OR t.age IS NULL) </when>
                                <when test="queryParam.compareType == 'gt'"> (CASE WHEN t.age ~ '^[0-9\.]+$' THEN t.age::INTEGER END) <![CDATA[ > ]]> #{queryParam.paramValue}::INTEGER </when>
                                <when test="queryParam.compareType == 'lt'"> (CASE WHEN t.age ~ '^[0-9\.]+$' THEN t.age::INTEGER END) <![CDATA[ < ]]> #{queryParam.paramValue}::INTEGER </when>
                            </choose>
                            <if test="queryParam.ageUnit != null and queryParam.ageUnit != ''">
                                AND t.age_unit = #{queryParam.ageUnit}
                            </if>
                        </when>
                        <when test="queryParam.paramName == 'item'">
                            <choose>
                                <when test="queryParam.compareType == 'eq'"> t.inspect_item_name = #{queryParam.paramValue} </when>
                                <when test="queryParam.compareType == 'ne'"> (t.inspect_item_name != #{queryParam.paramValue} OR t.inspect_item_name IS NULL) </when>
                                <when test="queryParam.compareType == 'co'"> t.inspect_item_name LIKE CONCAT('%', #{queryParam.paramValue} ,'%') </when>
                                <when test="queryParam.compareType == 'nc'"> (t.inspect_item_name NOT LIKE CONCAT('%', #{queryParam.paramValue}, '%') OR t.inspect_item_name IS NULL) </when>
                            </choose>
                            <if test="queryParam.resultValue != null and queryParam.resultValue != ''">
                                <choose>
                                    <when test="queryParam.resultCompareType == 'eq'"> AND t.result = #{queryParam.resultValue} </when>
                                    <when test="queryParam.resultCompareType == 'ne'"> AND (t.result != #{queryParam.resultValue} OR t.result IS NULL) </when>
                                    <when test="queryParam.resultCompareType == 'gt'"> AND (CASE WHEN t.result ~ '^[0-9\.]+$' THEN t.result::NUMERIC END) <![CDATA[ > ]]> #{queryParam.resultValue}::NUMERIC </when>
                                    <when test="queryParam.resultCompareType == 'lt'"> AND (CASE WHEN t.result ~ '^[0-9\.]+$' THEN t.result::NUMERIC END) <![CDATA[ < ]]> #{queryParam.resultValue}::NUMERIC </when>
                                    <when test="queryParam.resultCompareType == 'co'"> AND t.result LIKE CONCAT('%', #{queryParam.resultValue} ,'%') </when>
                                    <when test="queryParam.resultCompareType == 'nc'"> AND (t.result NOT LIKE CONCAT('%', #{queryParam.resultValue}, '%') OR t.result IS NULL)</when>
                                </choose>
                            </if>
                        </when>
                        <when test="queryParam.paramName == 'disease'">
                            <choose>
                                <when test="queryParam.compareType == 'eq'"> t.disease_code = #{queryParam.paramValue} </when>
                                <when test="queryParam.compareType == 'ne'"> (t.disease_code != #{queryParam.paramValue} OR t.disease_code IS NULL)</when>
                                <when test="queryParam.compareType == 'co'"> t.disease_code LIKE CONCAT(#{queryParam.paramValue}, '%') </when>
                                <when test="queryParam.compareType == 'nc'"> (t.disease_code NOT LIKE CONCAT(#{queryParam.paramValue}, '%') OR t.disease_code IS NULL) </when>
                            </choose>
                        </when>
                    </choose>
                </foreach>
            </trim>
            )
        </if>
        ORDER BY coalesce(t.visit_time, '1900-01-01'::timestamp) DESC
    </select>

</mapper>