<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v1.InfectionsReportLogMapper">
    <sql id="outpatientReportLog">
        dcmr.his_medical_id outpatient_id,
        dcmr.patient_name,
        dcmr.sex_name,
        concat(dcmr.age, dcmr.age_unit_name) AS age,
        dcmr.patient_id_number,
        dcmr.job occupation,
        dcmr.dept_name,
        dcmr.visit_doc_name AS doctor_name,
        dcmr.diagnose_name,
        dcmr.disease_name,
        doi.source_type as source_type,
        case when doi.source_type = '110' then '等级医院' when doi.source_type = '120' then '基层医疗' end sourceTypeName,
        doi.org_name as orgName,
        CAST(dcmr.event_datetime AS DATE) visit_date,
        CAST(dcmr.start_sick_datetime AS DATE) start_sick_date,
        case when dcmr.is_first_diag = '0' then '复诊' else '初诊' end first_diag_flag,
        dcmr.current_addr_detail living_address,
        dcmr.event_id,
        dcmr.medical_id
    </sql>

    <sql id="inHospitalReportLog">
        dcir.inhospital_id in_hospital_id,
        dcir.patient_name,
        dcir.sex_desc sex_name,
        dcir.age,
        dcir.id_card_no patient_id_number,
        dcir.job occupation,
        dcir.dept_name,
        dcir.doctor_name,
        CAST(dcir.in_hos_date AS DATE) in_hospital_date,
        dcir.in_diag in_hospital_diag,
        CAST(dcir.out_hos_date AS DATE) out_hospital_date,
        dcir.out_diag out_hospital_diag,
        dcir.outcome_name,
        dcir.outpatient_id,
        dcir.residential_address living_address,
        dcir.event_id,
        doi.source_type as source_type,
        case when doi.source_type = '110' then '等级医院' when doi.source_type = '120' then '基层医疗' end sourceTypeName,
        doi.org_name as orgName,
        doi.org_id as orgId
    </sql>

    <select id="selectOutpatientByMedicalId" resultType="com.iflytek.cdc.edr.dto.infections.OutpatientReportLog">
        SELECT
        <include refid="outpatientReportLog"/>
        FROM dwd.dwd_ch_medical_record dcmr
        JOIN dim.dim_organization_info doi ON doi.org_id = dcmr.org_id
        where dcmr.event_id = #{medicalId}
    </select>

    <select id="listOutpatientByMedicalIds" resultType="com.iflytek.cdc.edr.dto.infections.OutpatientReportLog">
        SELECT
        <include refid="outpatientReportLog"/>
        FROM dwd.dwd_ch_medical_record dcmr
        JOIN dim.dim_organization_info doi ON doi.org_id = dcmr.org_id
        where dcmr.event_id in
        <foreach collection="medicalIds" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getOutpatientReportLog" resultType="com.iflytek.cdc.edr.dto.infections.OutpatientReportLog">
        SELECT
            <include refid="outpatientReportLog"/>
        FROM dwd.dwd_ch_medical_record dcmr
          JOIN dim.dim_organization_info doi ON doi.org_id = dcmr.org_id
         WHERE dcmr.event_datetime >= #{startDate}::timestamp
           AND dcmr.event_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
           AND doi.source_type in ('110', '120')
        <if test="provinceCode != null">
           AND doi.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
           AND doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null">
           AND doi.district_code = #{districtCode}
        </if>
        <if test="provinceCodes != null and provinceCodes.size() > 0">
            and doi.province_code in
            <foreach collection="provinceCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() > 0">
            and doi.city_code in
            <foreach collection="cityCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() > 0">
            and doi.district_code in
            <foreach collection="districtCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="sourceType != null and sourceType != ''">
           AND doi.source_type = #{sourceType}
        </if>
        <if test="orgList != null and orgList.size() > 0">
            AND doi.org_id in
            <foreach item="item" index="index" collection="orgList" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="patientName != null and patientName != ''">
           AND dcmr.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
           AND dcmr.diagnose_name LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isInfectious != null">
           AND dcmr.is_infectious = #{isInfectious}
        </if>
        <if test="infectedName != null">
           AND dcmr.diagnose_name LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="firstDiagFlag != null and firstDiagFlag != ''">
            AND is_first_diag = #{firstDiagFlag}
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
        <if test="occupation != null and occupation != ''">
            AND dcmr.job LIKE concat('%', #{occupation, jdbcType=VARCHAR}, '%')
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            AND dcmr.disease_name LIKE concat('%', #{diseaseName, jdbcType=VARCHAR}, '%')
        </if>
        order by CAST(dcmr.visit_datetime AS DATE) DESC,
        count(dcmr.medical_id) over (partition by doi.org_name, CAST(dcmr.visit_datetime AS DATE)) desc,
        doi.org_name,
        dcmr.his_medical_id asc

    </select>

    <select id="selectInHospitalByEventId" resultType="com.iflytek.cdc.edr.dto.infections.InHospitalReportLog">
        SELECT <include refid="inHospitalReportLog"/>
        FROM dwd.dwd_ch_inhospital_record dcir
        JOIN dim.dim_organization_info doi ON doi.org_id = dcir.org_id
        WHERE dcir.event_id = #{eventId}
    </select>

    <select id="listInHospitalByEventIds" resultType="com.iflytek.cdc.edr.dto.infections.InHospitalReportLog">
        SELECT <include refid="inHospitalReportLog"/>
        FROM dwd.dwd_ch_inhospital_record dcir
        JOIN dim.dim_organization_info doi ON doi.org_id = dcir.org_id
        WHERE dcir.event_id in
        <foreach collection="eventIds" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getInHospitalReportLog" resultType="com.iflytek.cdc.edr.dto.infections.InHospitalReportLog">
        SELECT dcir.inhospital_id in_hospital_id,
               dcir.patient_name,
               dcir.sex_desc sex_name,
               dcir.age,
               dcir.id_card_no patient_id_number,
               dcir.job occupation,
               dcir.dept_name,
               dcir.doctor_name,
               CAST(dcir.in_hos_date AS DATE) in_hospital_date,
               dcir.in_diag in_hospital_diag,
               CAST(dcir.out_hos_date AS DATE) out_hospital_date,
               dcir.out_diag out_hospital_diag,
               dcir.outcome_name,
               dcir.outpatient_id,
               dcir.residential_address living_address,
               dcir.event_id,
               case when doi.source_type = '110' then '等级医院' when doi.source_type = '120' then '基层医疗' end sourceTypeName,
               doi.org_name as orgName
          FROM dwd.dwd_ch_inhospital_record dcir
          JOIN dim.dim_organization_info doi ON doi.org_id = dcir.org_id
         WHERE dcir.in_hos_date >= #{startDate}::timestamp
           AND dcir.in_hos_date &lt; #{endDate}::timestamp + INTERVAL '1 day'
           AND doi.source_type in ('110', '120')
        <if test="diseaseName != null and diseaseName != ''">
           AND exists (
                SELECT 1
                FROM dw.dws_infected_suspect_mapping_result r
                WHERE dcir.event_id = r.event_id
                AND r.tag_name LIKE CONCAT('%', #{diseaseName, jdbcType=VARCHAR}, '%')
           )
        </if>
        <if test="provinceCode != null">
           AND doi.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
           AND doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null">
           AND doi.district_code = #{districtCode}
        </if>
        <if test="sourceType != null and sourceType != ''">
           AND doi.source_type = #{sourceType}
        </if>
        <if test="orgList != null and orgList.size() > 0">
            AND doi.org_id in
            <foreach item="item" index="index" collection="orgList" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="patientName != null and patientName != ''">
           AND dcir.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
           AND (dcir.in_diag LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%') OR dcir.out_diag LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%'))
        </if>
        <if test="isInfectious != null">
           AND dcir.is_infectious = #{isInfectious}
        </if>
        <if test="infectedName != null">
           AND (dcir.in_diag LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%') OR dcir.out_diag LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%'))
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
        <if test="occupation != null and occupation != ''">
            AND dcir.job LIKE concat('%', #{occupation, jdbcType=VARCHAR}, '%')
        </if>
        order by CAST(dcir.in_hos_date AS DATE) DESC,
        count(dcir.event_id) over (partition by doi.org_name, CAST(dcir.in_hos_date AS DATE)) desc,
        doi.org_name,
        dcir.inhospital_id asc
    </select>

    <select id="getAbnormalMedicalList"
            resultType="com.iflytek.cdc.edr.dto.suspect.MedicalSuspectRecordDto">
        select sr.event_id as eventId, sr.his_medical_id as hisMedicalId, sr.org_id as orgId, doi.org_name as orgName,
               sr.patient_name as patientName, sr.sex_desc as sexDesc, sr.in_diagnose as inDiagnose,
               sr.out_diagnose as outDiagnose, sr.age as age, sr.age_unit_name as ageUnitName,
               sr.id_card_no as idCardNo, sr.global_person_id as globalPersonId,
               sr.full_date as fullDate, sr.visit_datetime as visitDatetime,
               sr.event_datetime as eventDatetime, sr.doctor_id as doctorId,
               sr.doctor_name as doctorName, sr.dept_name as deptName,
               sr.dept_code as deptCode, sr.is_submit_report_card as isSubmitReportCard,
               case when doi.district_name = '' then doi.city_name else doi.district_name end as regionName,
               sr.data_src as dataSrc, doi.source_type as sourceType,
               case when doi.source_type = '110' then '等级医院' when doi.source_type = '120' then '基层医疗' end sourceTypeName,
               sr.living_addr_detail as livingAddressDetail,
               case when sr.data_src = '3' then sr.in_diagnose else sr.diagnose_name_list end as diagnoseName
        from dwd.dwd_ch_medical_suspect_record sr
--         left join (select event_id, array_to_string(array_agg(std_diagnose_name), ',') as stdDiagnoseName
--                    from dwd.dwd_ch_medical_suspect_detail where std_diagnose_name != '' group by event_id) sd
--         on sr.event_id = sd.event_id
        left join dim.dim_organization_info doi
        on sr.org_id = doi.org_id
        where sr.event_datetime >= #{startDate}::timestamp
        and sr.event_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
        AND exists (
            select 1 from dwd.dwd_ch_medical_suspect_detail d
            where sr.event_id = d.event_id
            <if test="infectedCode != null and infectedCode.size() &gt; 0">
                and d.infected_sub_code  in
                <foreach item="item" collection="infectedCode" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="diseaseName != null and diseaseName != ''">
                AND d.infected_sub_name LIKE concat('%', #{diseaseName, jdbcType=VARCHAR}, '%')
            </if>
        )
        <if test="dataSrc != null and dataSrc != ''">
            and sr.data_src = #{dataSrc, jdbcType=VARCHAR}
        </if>
        <if test="sourceType != null and sourceType != ''">
            and doi.source_type = #{sourceType, jdbcType=VARCHAR}
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and doi.province_code = #{provinceCode, jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and doi.city_code = #{cityCode, jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and doi.district_code = #{districtCode, jdbcType=VARCHAR}
        </if>
        <if test="provinceCodes != null and provinceCodes.size() > 0">
            and doi.province_code in
            <foreach collection="provinceCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() > 0">
            and doi.city_code in
            <foreach collection="cityCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() > 0">
            and doi.district_code in
            <foreach collection="districtCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="provinceCodes != null and provinceCodes.size() > 0">
            and doi.province_code in
            <foreach collection="provinceCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() > 0">
            and doi.city_code in
            <foreach collection="cityCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() > 0">
            and doi.district_code in
            <foreach collection="districtCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="orgList != null and orgList.size() > 0">
            AND doi.org_id in
            <foreach item="item" index="index" collection="orgList" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
            <choose>
                <when test="dataSrc == 3">
                    and sr.in_diagnose like concat('%', #{diagnoseName, jdbcType=VARCHAR}, '%')
                </when>
                <when test="dataSrc == 2">
                    and sr.diagnose_name_list like concat('%', #{diagnoseName, jdbcType=VARCHAR}, '%')
                </when>
                <when test="dataSrc == '' or dataSrc == null">
                    and (sr.in_diagnose like concat('%', #{diagnoseName, jdbcType=VARCHAR}, '%') or sr.diagnose_name_list like concat('%', #{diagnoseName, jdbcType=VARCHAR}, '%'))
                </when>
            </choose>
        </if>
        <if test="patientName != null and patientName != ''">
            and sr.patient_name like concat('%', #{patientName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="isReported.size() &gt; 0">
            and sr.is_submit_report_card in
            <foreach item="item" index="index" collection="isReported"
                     open="(" separator="," close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        order by CAST(sr.event_datetime AS DATE) DESC,
        count(sr.medical_id) over (partition by doi.org_name, CAST(sr.event_datetime AS DATE)) desc,
        doi.org_name,
        sr.his_medical_id asc
    </select>
    <select id="getSuspectRecordList"
            resultType="com.iflytek.cdc.edr.dto.suspect.DwdChMedicalSuspectRecordDto">
        select sr.* from dwd.dwd_ch_medical_suspect_record sr
        left join dim.dim_organization_info doi
        on sr.org_id = doi.org_id
        where 1=1
        <if test="minFullDate != null">
            and sr.full_date >= #{minFullDate,jdbcType=DATE}
        </if>
        <if test="maxFullDate != null">
            and sr.full_date &lt;= #{maxFullDate,jdbcType=DATE}
        </if>
        <if test="provinceCodes != null and provinceCodes.size() &gt; 0">
            and doi.province_code in
            <foreach close=")" collection="provinceCodes" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="cityCodes != null and cityCodes.size() &gt; 0">
            and doi.city_code in
            <foreach close=")" collection="cityCodes" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="districtCodes != null and districtCodes.size() &gt; 0">
            and doi.district_code in
            <foreach close=")" collection="districtCodes" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <select id="getSuspectDetailByEventIds"
            resultType="com.iflytek.cdc.edr.dto.suspect.DwdChMedicalSuspectDetailDto">
        select * from dwd.dwd_ch_medical_suspect_detail where
        event_id in
        <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="getAllOutpatientReportLog" resultType="com.iflytek.cdc.edr.dto.infections.OutpatientReportLog">
        SELECT dcmr.his_medical_id outpatient_id,
        dcmr.patient_name,
        dcmr.sex_name,
        CONCAT(dcmr.age, age_unit_name) AS age,
        dcmr.patient_id_number,
        dcmr.job occupation,
        dcmr.dept_name,
        case when doi.source_type = '110' then '等级医院' when doi.source_type = '120' then '基层医疗' end sourceTypeName,
        doi.org_name,
        dcmr.visit_doc_name AS doctor_name,
        dcmr.diagnose_name,
        dcmr.disease_name,
        CAST(dcmr.event_datetime AS DATE) visit_date,
        CAST(dcmr.start_sick_datetime AS DATE) start_sick_date,
        case when dcmr.is_first_diag = '0' then '复诊' else '初诊' end first_diag_flag,
        dcmr.current_addr_detail living_address,
        dcmr.event_id
        FROM dwd.dwd_ch_medical_record dcmr
        JOIN dim.dim_organization_info doi ON doi.org_id = dcmr.org_id
        WHERE dcmr.event_datetime >= #{startDate}::timestamp
        AND dcmr.event_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
        AND doi.source_type in ('110', '120')
        <if test="provinceCode != null">
            AND doi.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
            AND doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null">
            AND doi.district_code = #{districtCode}
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND doi.source_type = #{sourceType}
        </if>
        <if test="orgList != null and orgList.size() > 0">
            AND doi.org_id in
            <foreach item="item" index="index" collection="orgList" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="patientName != null and patientName != ''">
            AND dcmr.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
            AND dcmr.diagnose_name LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isInfectious != null">
            AND dcmr.is_infectious = #{isInfectious}
        </if>
        <if test="infectedName != null">
            AND dcmr.diagnose_name LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%')
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="firstDiagFlag != null and firstDiagFlag != ''">
            AND is_first_diag = #{firstDiagFlag}
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
        <if test="occupation != null and occupation != ''">
            AND dcmr.job LIKE concat('%', #{occupation, jdbcType=VARCHAR}, '%')
        </if>
        <if test="diseaseName != null and diseaseName != ''">
            AND dcmr.disease_name LIKE concat('%', #{diseaseName, jdbcType=VARCHAR}, '%')
        </if>
        order by CAST(dcmr.visit_datetime AS DATE) DESC,
        count(dcmr.medical_id) over (partition by doi.org_name, CAST(dcmr.visit_datetime AS DATE)) desc,
        doi.org_name,
        dcmr.his_medical_id asc
    </select>

    <select id="getAllInHospitalReportLog" resultType="com.iflytek.cdc.edr.dto.infections.InHospitalReportLog">
        SELECT dcir.inhospital_id in_hospital_id,
        dcir.patient_name,
        dcir.sex_desc sex_name,
        dcir.age,
        dcir.id_card_no patient_id_number,
        dcir.job occupation,
        dcir.dept_name,
        dcir.doctor_name,
        CAST(dcir.in_hos_date AS DATE) in_hospital_date,
        dcir.in_diag in_hospital_diag,
        CAST(dcir.out_hos_date AS DATE) out_hospital_date,
        dcir.out_diag out_hospital_diag,
        dcir.outcome_name,
        dcir.outpatient_id,
        case when doi.source_type = '110' then '等级医院' when doi.source_type = '120' then '基层医疗' end sourceTypeName,
        doi.org_name,
        dcir.residential_address living_address,
        dcir.event_id
        FROM dwd.dwd_ch_inhospital_record dcir
        JOIN dim.dim_organization_info doi ON doi.org_id = dcir.org_id
        WHERE dcir.in_hos_date >= #{startDate}::timestamp
        AND dcir.in_hos_date &lt; #{endDate}::timestamp + INTERVAL '1 day'
        AND doi.source_type in ('110', '120')
        <if test="diseaseName != null and diseaseName != ''">
            AND exists (
                select 1 from dw.dws_infected_suspect_detail r
                where dcir.event_id = r.event_id
                    AND r.infected_sub_name LIKE concat('%', #{diseaseName, jdbcType=VARCHAR}, '%')
            )
        </if>

        <if test="provinceCode != null">
            AND doi.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
            AND doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null">
            AND doi.district_code = #{districtCode}
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND doi.source_type = #{sourceType}
        </if>
        <if test="orgList != null and orgList.size() > 0">
            AND doi.org_id in
            <foreach item="item" index="index" collection="orgList" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="patientName != null and patientName != ''">
            AND dcir.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
            AND (dcir.in_diag LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%') OR dcir.out_diag LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%'))
        </if>
        <if test="isInfectious != null">
            AND dcir.is_infectious = #{isInfectious}
        </if>
        <if test="infectedName != null">
            AND (dcir.in_diag LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%') OR dcir.out_diag LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%'))
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
        <if test="occupation != null and occupation != ''">
            AND dcir.job LIKE concat('%', #{occupation, jdbcType=VARCHAR}, '%')
        </if>
        ORDER BY dcir.in_hos_date DESC
    </select>
    <select id="getAllInHospitalReportLogCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM dwd.dwd_ch_inhospital_record dcir
        JOIN dim.dim_organization_info doi ON doi.org_id = dcir.org_id
        WHERE dcir.in_hos_date >= #{startDate}::timestamp
        AND dcir.in_hos_date &lt; #{endDate}::timestamp + INTERVAL '1 day'
        AND doi.source_type in ('110', '120')
        AND exists (
            select 1 from dw.dws_infected_suspect_mapping_result r
            where dcir.event_id = r.event_id
            <if test="diseaseName != null and diseaseName != ''">
                AND r.tag_name LIKE concat('%', #{diseaseName, jdbcType=VARCHAR}, '%')
            </if>
        )
        <if test="provinceCode != null">
            AND doi.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
            AND doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null">
            AND doi.district_code = #{districtCode}
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND doi.source_type = #{sourceType}
        </if>
        <if test="orgList != null and orgList.size() > 0">
            AND doi.org_id in
            <foreach item="item" index="index" collection="orgList" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="patientName != null and patientName != ''">
            AND dcir.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="diagnoseName != null and diagnoseName != ''">
            AND (dcir.in_diag LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%') OR dcir.out_diag LIKE concat('%', #{diagnoseName, jdbcType=VARCHAR},'%'))
        </if>
        <if test="isInfectious != null">
            AND dcir.is_infectious = #{isInfectious}
        </if>
        <if test="infectedName != null">
            AND (dcir.in_diag LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%') OR dcir.out_diag LIKE concat('%', #{infectedName, jdbcType=VARCHAR},'%'))
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name LIKE concat('%', #{deptName, jdbcType=VARCHAR},'%')
        </if>
        <if test="occupation != null and occupation != ''">
            AND dcir.job LIKE concat('%', #{occupation, jdbcType=VARCHAR}, '%')
        </if>
    </select>

    <select id="getInfectedSuspectMappingResultByEventIds" resultType="com.iflytek.cdc.edr.dto.suspect.SuspectMappingResult">
        select *, '1' as case_type_code, '传染病报告卡' as case_type_name
        from dw.dws_infected_suspect_mapping_result m
        where m.event_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>