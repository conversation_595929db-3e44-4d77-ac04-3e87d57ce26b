<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.VAdsMedSuspectDiseaseStatsMapper">


    <select id="distributionAndStats"
            resultType="com.iflytek.cdc.edr.dto.infections.InfectedMonitorStatRespDTO">
        with tmp as(
        select
        coalesce(sum(disease_med_cnt),0)  medCnt,
        coalesce(sum(last_disease_med_cnt),0) lastYearMedCnt,
        max(rule_id) rule_id,
        infected_type_code  infectedCode,
        infected_type_name  infectedName
        from
        ads.v_ads_med_suspect_disease_stats t
        <where>
            <if test="startDate != null and endDate != null">
                and  CAST(full_date AS DATE) between  #{startDate} and  #{endDate}
            </if>
            <if test="regionInfoList!=null and regionInfoList.size() &gt; 0">
                and
                <foreach close=")" collection="regionInfoList" index="index" item="item" open="(  " separator=" or ">
                    (1=1
                    <if test="item.provinceCode != null and item.provinceCode != '' ">
                        and PROVINCE_CODE = #{item.provinceCode}
                    </if>
                    <if test="item.cityCode != null and item.cityCode != '' ">
                        and CITY_CODE = #{item.cityCode}
                    </if>
                    <if test="item.districtCode != null and item.districtCode !='' ">
                        and DISTRICT_CODE = #{item.districtCode}
                    </if>)
                </foreach>
            </if>
            <if test="symptom != null and symptom.size() &gt; 0">
                and show_disease_code in
                <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>

        group by
        infected_type_code ,
        infected_type_name
        )
        select
        coalesce(tmp.medCnt,0) medCnt,
        coalesce(tmp.lastYearMedCnt,0) lastYearMedCnt,
        tmp.rule_id,
        mr.infected_type_code infectedCode,
        mr.infected_type_name infectedName  from
        (select infected_type_code,infected_type_name from  dim.dim_infected_info i
        where 1=1
        <if test="symptom != null and symptom.size() &gt; 0">
            and i.mdm_infected_code in
            <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by  infected_type_code,infected_type_name) mr
        left join tmp  on tmp.infectedCode=mr.infected_type_code
        order by
        infectedCode
    </select>
    <select id="timeTrend" resultType="com.iflytek.cdc.edr.dto.infections.InfectedMonitorStatRespDTO">
        with tmp as (
        select
        full_date,
        coalesce(sum(disease_med_cnt),0)  medCnt,
        max(rule_id) rule_id,
        infected_type_code  infectedCode,
        infected_type_name  infectedName,
        to_char(full_date, 'MM-DD') short_full_date
        from
        ads.v_ads_med_suspect_disease_stats
        <where>
            <if test="startDate != null and endDate != null">
                and  CAST(full_date AS DATE) between #{startDate} and  #{endDate}
            </if>
            <if test="regionInfoList!=null and regionInfoList.size() &gt; 0">
                and
                <foreach close=")" collection="regionInfoList" index="index" item="item" open="(  " separator=" or ">
                    (1=1
                    <if test="item.provinceCode != null and item.provinceCode != '' ">
                        and PROVINCE_CODE = #{item.provinceCode}
                    </if>
                    <if test="item.cityCode != null and item.cityCode != '' ">
                        and CITY_CODE = #{item.cityCode}
                    </if>
                    <if test="item.districtCode != null and item.districtCode !='' ">
                        and DISTRICT_CODE = #{item.districtCode}
                    </if>)
                </foreach>
            </if>
            <if test="symptom != null and symptom.size() &gt; 0">
                and show_disease_code in
                <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        group by
        full_date,
        infected_type_code ,
        infected_type_name
        order by infected_type_code,full_date
        )
        select
        tmp.full_date,
        to_char(tmp.full_date, 'MM-DD') short_full_date,
        coalesce(tmp.medCnt,0) medCnt,
        tmp.rule_id,
        mr.infected_type_code infectedCode,
        mr.infected_type_name infectedName  from
        (select infected_type_code,infected_type_name from  dim.dim_infected_info i
        where 1=1
        <if test="symptom != null and symptom.size() &gt; 0">
            and i.mdm_infected_code in
            <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        group by  infected_type_code,infected_type_name) mr
        left join tmp  on tmp.infectedCode=mr.infected_type_code
        order by
        infectedCode,
        full_date
    </select>
    <select id="abnormalStats" resultType="com.iflytek.cdc.edr.dto.infections.InfectedMonitorStatRespDTO">
        select
        coalesce(sum(disease_med_cnt),0)  medCnt,
        coalesce(sum(last_disease_med_cnt),0) lastYearMedCnt,
        show_disease_code   infected_code,
        show_disease_name   infected_name,
        full_date,
        max(is_abnormal) is_abnormal,
        max(rule_id) rule_id,
        to_char(full_date, 'MM-DD') short_full_date
        from
        ads.v_ads_med_suspect_disease_stats
        <where>
            <if test="startDate != null and endDate != null">
                and  CAST(full_date AS DATE) between  #{startDate} and  #{endDate}
            </if>
            <if test="isAbnormal != null and isAbnormal != ''">
                and  is_abnormal =#{isAbnormal}
            </if>
            <if test="regionInfoList!=null and regionInfoList.size() &gt; 0">
                and
                <foreach close=")" collection="regionInfoList" index="index" item="item" open="(  " separator=" or ">
                    (1=1
                    <if test="item.provinceCode != null and item.provinceCode != '' ">
                        and PROVINCE_CODE = #{item.provinceCode}
                    </if>
                    <if test="item.cityCode != null and item.cityCode != '' ">
                        and CITY_CODE = #{item.cityCode}
                    </if>
                    <if test="item.districtCode != null and item.districtCode !='' ">
                        and DISTRICT_CODE = #{item.districtCode}
                    </if>)
                </foreach>
            </if>
            <if test="symptom != null and symptom.size() &gt; 0">
                and show_disease_code in
                <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        group by
        show_disease_code ,
        show_disease_name, full_date   order by show_disease_code

    </select>
    <select id="getOrgCount"
            resultType="com.iflytek.cdc.edr.dto.infections.InfectedMonitorStatGeographyTopRespDTO">
        select
        coalesce(count(distinct org_id),0)     orgCnt
        from
        ads.v_infected_suspect_mapping_res

        <where>
            <if test="startDate != null and endDate != null">
                and  CAST(full_date AS DATE) between  #{startDate} and  #{endDate}
            </if>
            <if test="regionInfoList!=null and regionInfoList.size() &gt; 0">
                and
                <foreach close=")" collection="regionInfoList" index="index" item="item" open="(  " separator=" or ">
                    (1=1
                    <if test="item.provinceCode != null and item.provinceCode != '' ">
                        and PROVINCE_CODE = #{item.provinceCode}
                    </if>
                    <if test="item.cityCode != null and item.cityCode != '' ">
                        and CITY_CODE = #{item.cityCode}
                    </if>
                    <if test="item.districtCode != null and item.districtCode !='' ">
                        and DISTRICT_CODE = #{item.districtCode}
                    </if>)
                </foreach>
            </if>
            <if test="symptom != null and symptom.size() &gt; 0">
                and show_disease_code in
                <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getDistrictCount"
            resultType="com.iflytek.cdc.edr.dto.infections.InfectedMonitorStatGeographyRespDTO">
        select
        district_code,
        district_name,
        count(1)  medCnt
        from
        ads.v_infected_suspect_mapping_res
        <where>
            <if test="startDate != null and endDate != null">
                and  CAST(full_date AS DATE) between  #{startDate} and  #{endDate}
            </if>
            <if test="regionInfoList!=null and regionInfoList.size() &gt; 0">
                and
                <foreach close=")" collection="regionInfoList" index="index" item="item" open="(  " separator=" or ">
                    (1=1
                    <if test="item.provinceCode != null and item.provinceCode != '' ">
                        and PROVINCE_CODE = #{item.provinceCode}
                    </if>
                    <if test="item.cityCode != null and item.cityCode != '' ">
                        and CITY_CODE = #{item.cityCode}
                    </if>
                    <if test="item.districtCode != null and item.districtCode !='' ">
                        and DISTRICT_CODE = #{item.districtCode}
                    </if>)
                </foreach>
            </if>
            <if test="symptom != null and symptom.size() &gt; 0">
                and show_disease_code in
                <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        group by
        district_code,
        district_name  order by medCnt desc
    </select>
    <select id="findInfectedList" resultType="com.iflytek.cdc.edr.entity.ads.AdsSyndromeMappingRes">
        select
        org_id,
        medical_id,
        org_longitude,
        org_latitude,
        living_address_longitude,
        living_address_latitude,
        company_address_longitude,
        company_address_latitude
        from
        ads.v_infected_suspect_mapping_res
        <where>
            <if test="startDate != null and endDate != null">
                and  CAST(full_date AS DATE) between  #{startDate} and  #{endDate}
            </if>
            <if test="regionInfoList!=null and regionInfoList.size() &gt; 0">
                and
                <foreach close=")" collection="regionInfoList" index="index" item="item" open="(  " separator=" or ">
                    (1=1
                    <if test="item.provinceCode != null and item.provinceCode != '' ">
                        and PROVINCE_CODE = #{item.provinceCode}
                    </if>
                    <if test="item.cityCode != null and item.cityCode != '' ">
                        and CITY_CODE = #{item.cityCode}
                    </if>
                    <if test="item.districtCode != null and item.districtCode !='' ">
                        and DISTRICT_CODE = #{item.districtCode}
                    </if>)
                </foreach>
            </if>
            <if test="symptom != null and symptom.size() &gt; 0">
                and show_disease_code in
                <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>

    </select>


    <select id="timeAreaStatistics" resultType="com.iflytek.cdc.edr.dto.infections.TimeTrendDistrictRespDTO">
        select
        max(city_code) city_code,
        max(city_name) city_name,
        district_code,
        district_name,
        full_date,
        to_char(full_date, 'MM-DD') short_full_date,
        coalesce (sum(disease_med_cnt),0) medCnt,
        coalesce (sum(avg_med_cnt::numeric(11,1)),0) avgMedCnt,
        coalesce (sum(reference_value::numeric(11,1)),0) referenceValue,
        max(rule_id) rule_id ,
        max(is_abnormal) isAbnormal ,
        coalesce (sum(last_disease_med_cnt),0) lastYearMedCnt
        from
        ads.v_ads_med_suspect_disease_stats
        <where>
            <if test="startDate != null and endDate != null">
                and  CAST(full_date AS DATE) between  #{startDate} and  #{endDate}
            </if>
            <if test="regionInfoList!=null and regionInfoList.size() &gt; 0">
                and
                <foreach close=")" collection="regionInfoList" index="index" item="item" open="(  " separator=" or ">
                    (1=1
                    <if test="item.provinceCode != null and item.provinceCode != '' ">
                        and PROVINCE_CODE = #{item.provinceCode}
                    </if>
                    <if test="item.cityCode != null and item.cityCode != '' ">
                        and CITY_CODE = #{item.cityCode}
                    </if>
                    <if test="item.districtCode != null and item.districtCode !='' ">
                        and DISTRICT_CODE = #{item.districtCode}
                    </if>)
                </foreach>
            </if>
            <if test="infectedCodeList != null and infectedCodeList.size() &gt; 0">
                and (
                infected_code in
                <foreach close=")" collection="infectedCodeList" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
                or show_disease_code in
                <foreach  collection="infectedCodeList" index="index" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
                )
            </if>
        </where>
        group by
        district_code,
        district_name,full_date order by district_code

    </select>


    <select id="casesTimeTrend" resultType="com.iflytek.cdc.edr.dto.infections.CasesTimeTrendRespDTO">
        select
        full_date,
        coalesce(sum(disease_med_cnt), 0) med_cnt,
        coalesce(sum(last_disease_med_cnt), 0) last_year_med_cnt,
        coalesce(sum(reference_value::numeric(11,1)), 0) as referenceValue,
        to_char(full_date, 'MM-DD') short_full_date
        from
        ads.v_ads_med_suspect_disease_stats
        <where>
            <if test="startDate != null and endDate != null">
                and  CAST(full_date AS DATE) between  #{startDate} and  #{endDate}
            </if>
            <if test="regionInfoList!=null and regionInfoList.size() &gt; 0">
                and
                <foreach close=")" collection="regionInfoList" index="index" item="item" open="(  " separator=" or ">
                    (1=1
                    <if test="item.provinceCode != null and item.provinceCode != '' ">
                        and PROVINCE_CODE = #{item.provinceCode}
                    </if>
                    <if test="item.cityCode != null and item.cityCode != '' ">
                        and CITY_CODE = #{item.cityCode}
                    </if>
                    <if test="item.districtCode != null and item.districtCode !='' ">
                        and DISTRICT_CODE = #{item.districtCode}
                    </if>)
                </foreach>
            </if>
            <if test="infectedCodeList != null and infectedCodeList.size() &gt; 0">
                and (
                infected_code in
                <foreach close=")" collection="infectedCodeList" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
                or show_disease_code in
                <foreach  collection="infectedCodeList" index="index" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
                )
            </if>
        </where>
        group by full_date
        order by full_date
    </select>
</mapper>
