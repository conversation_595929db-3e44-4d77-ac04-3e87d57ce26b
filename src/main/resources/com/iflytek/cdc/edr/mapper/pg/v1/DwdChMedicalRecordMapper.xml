<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v1.DwdChMedicalRecordMapper">

    <select id="findOutpatEventIdsByMedicalIds" resultType="com.iflytek.cdc.edr.dto.epi.MedicalInfoResult">
        select event_id, medical_id
        from dwd.dwd_ch_medical_record d
        <where>
            <if test="medicalIds != null and medicalIds.size() != 0">
                d.medical_id in
                <foreach collection="medicalIds" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findByMedicalId" resultType="com.iflytek.cdc.edr.dto.epi.MedicalInfoResult">
        select t1.medical_id,
               t1.event_id,
               t1.his_medical_id,
               t1.patient_id,
               t1.insurance_card,
               t1.patient_id_number,
               t1.patient_name,
               t1.sex_code,
               t1.sex_name,
               t1.age,
               t1.age_unit_name,
               t1.telephone,
               t1.company_name,
               t1.job,
               t1.current_addr_detail,
               t1.org_id,
               t1.org_code,
               t1.org_name,
               t1.dept_id,
               t1.dept_code,
               t1.dept_name,
               t1.visit_doc_id,
               t1.visit_doc_name,
               t1.visit_doc_telephone,
               t1.action_in_chief,
               t1.medical_history_now,
               t1.medical_history,
               t1.personal_history,
               t1.marital_birth_history,
               t1.menstrual_history,
               t1.family_history,
               t1.allergy_history,
               t1.vaccinate_history,
               t1.infecttion_desc,
               t1.tcm_observe_desc,
               t1.tongue_picture,
               t1.dbp,
               t1.mbp,
               t1.sbp,
               t1.fbs,
               t1.two_hpbg,
               t1.temperature,
               t1.breath_rate,
               t1.blood_oxygen,
               t1.heart_rate,
               t1.weight,
               t1.height,
               t1.pulse_rate,
               t1.exam_desc,
               t1.pos_aux_examin,
               t1.diagnose_type,
               t1.diagnose_code,
               t1.diagnose_name,
               t1.medical_name,
               t1.medical_type_code,
               t1.medical_type_name,
               t1.medical_content,
               t1.visit_datetime,
               t1.start_sick_datetime,
               t1.event_datetime,
               t1.etl_create_datetime,
               t1.etl_update_datetime,
               t1.source_id,
               t1.data_src,
               t1.global_person_id,
               t1.medical_create_datetime,
               t1.medical_update_datetime,
               t1.uap_user_id,
               t2.province_code,
               t2.province_name,
               t2.city_code,
               t2.city_name,
               t2.district_code,
               t2.district_name,
               t2.township_hospital_id,
               t2.township_hospital_name,
               t3.symptom_tag_list,
               t3.onset_time_list,
               t3.cause_list,
               t3.examine_list,
               t3.diagnose_name,
               t3.main_suit_symptom_tag_list,
               t3.physical_sign_list,
               t3.epidemiology_history_list
        from dwd.dwd_ch_medical_record t1
                 join dim.dim_medical_organization_info t2 on t1.org_id = t2.org_id
                 left join dwd.dwd_syndrome_medical_symptom_tag t3 on t1.medical_id = t3.medical_id
        where t1.medical_id = #{medicalId,jdbcType=VARCHAR}
    </select>
    <select id="findByNameAndIdCardNo" resultType="com.iflytek.cdc.edr.dto.epi.MedicalInfoResult">
        select t1.medical_id,
               t1.event_id,
               t1.his_medical_id,
               t1.patient_id,
               t1.insurance_card,
               t1.patient_id_number,
               t1.patient_name,
               t1.sex_code,
               t1.sex_name,
               t1.age,
               t1.age_unit_name,
               t1.telephone,
               t1.company_name,
               t1.job,
               t1.current_addr_detail,
               t1.org_id,
               t1.org_code,
               t1.org_name,
               t1.dept_id,
               t1.dept_code,
               t1.dept_name,
               t1.visit_doc_id,
               t1.visit_doc_name,
               t1.visit_doc_telephone,
               t1.action_in_chief,
               t1.medical_history_now,
               t1.medical_history,
               t1.personal_history,
               t1.marital_birth_history,
               t1.menstrual_history,
               t1.family_history,
               t1.allergy_history,
               t1.vaccinate_history,
               t1.infecttion_desc,
               t1.tcm_observe_desc,
               t1.tongue_picture,
               t1.dbp,
               t1.mbp,
               t1.sbp,
               t1.fbs,
               t1.two_hpbg,
               t1.temperature,
               t1.breath_rate,
               t1.blood_oxygen,
               t1.heart_rate,
               t1.weight,
               t1.height,
               t1.pulse_rate,
               t1.exam_desc,
               t1.pos_aux_examin,
               t1.diagnose_type,
               t1.diagnose_code,
               t1.diagnose_name,
               t1.medical_name,
               t1.medical_type_code,
               t1.medical_type_name,
               t1.medical_content,
               t1.visit_datetime,
               t1.start_sick_datetime,
               t1.event_datetime,
               t1.etl_create_datetime,
               t1.etl_update_datetime,
               t1.source_id,
               t1.data_src,
               t1.global_person_id,
               t1.medical_create_datetime,
               t1.medical_update_datetime,
               t1.uap_user_id,
               t2.province_code,
               t2.province_name,
               t2.city_code,
               t2.city_name,
               t2.district_code,
               t2.district_name,
               t2.township_hospital_id,
               t2.township_hospital_name,
               t3.symptom_tag_list,
               t3.onset_time_list,
               t3.cause_list,
               t3.examine_list,
               t3.diagnose_name,
               t3.main_suit_symptom_tag_list,
               t3.physical_sign_list,
               t3.epidemiology_history_list
        from dwd.dwd_ch_medical_record t1
                 join dim.dim_medical_organization_info t2 on t1.org_id = t2.org_id
                 left join dwd.dwd_syndrome_medical_symptom_tag t3 on t1.medical_id = t3.medical_id
        where t1.patient_name = #{patientName,jdbcType=VARCHAR}
          and t1.patient_id_number = #{idCardNo,jdbcType=VARCHAR}
        order by t1.medical_create_datetime desc limit 1
    </select>
    <select id="findByCaseSearchDto" resultType="com.iflytek.cdc.edr.dto.epi.CaseDTO">
        select
        t1.medical_id as sourceKey,
        t1.patient_name as name,
        t1.patient_id as patientId,
        t1.age as age,
        t1.sex_code as sexCode,
        t1.sex_name as sexName,
        t1.patient_id_number as idCardNum,
        t1.telephone as phone,
        t1.diagnose_name as diag,
        t1.medical_create_datetime as fullDate,
        t1.visit_datetime as outpatientTime,
        t1.company_name as company,
        mi.address_province_code as provinceCode,
        mi.address_province_name as provinceName,
        mi.address_city_code as cityCode,
        mi.address_city_name as cityName,
        mi.address_district_code as districtCode,
        mi.address_district_name as districtName,
        t1.current_addr_detail as address,
        t1.start_sick_datetime as onsetDate,
        t1.diagnose_create_datetime as diagnosisDateTime,
        t1.visit_doc_id as visitDocId,
        t1.visit_doc_name as visitDocName,
        t1.visit_doc_telephone as visitDocPhone,
        t1.org_id as visitOrgId,
        t1.org_name as visitOrgName,
        t2.org_longitude as orgLongitude,
        t2.org_latitude as orgLatitude,
        t1.exam_desc as checkupOther,
        t1.action_in_chief as mainSuit,
        t1.medical_history_now as illnessHistory,
        t1.medical_history as previousHistory,
        t1.pos_aux_examin as auxExam,
        t3.symptom_tag_list as symptomName,
        t1.org_name as source,
        t1.source_id as sourceId,
        t1.data_src as dataSrc
        from dwd.dwd_ch_medical_record t1
        join dim.dim_medical_organization_info t2 on t1.org_id = t2.org_id
        left join dim.dim_patient_best_record dp on t1.global_person_id = dp.global_patient_id
        left join dim.dim_mr_address_standard mi on dp.std_living_addr_area_code = mi.address_area_code
        left join dwd.dwd_syndrome_medical_symptom_tag t3 on t1.medical_id = t3.medical_id
        where 1=1
        <if test="name != null and name != ''">
            and t1.patient_name = #{name,jdbcType=VARCHAR}
        </if>
        <if test="idCardNum != null and idCardNum != ''">
            and t1.patient_id_number = #{idCardNum,jdbcType=VARCHAR}
        </if>
        <if test="startTime != null">
            and t1.visit_datetime <![CDATA[>=]]> #{startTime,jdbcType=DATE}
        </if>
        <if test="endTime != null">
            and t1.visit_datetime <![CDATA[<=]]> #{endTime,jdbcType=DATE}
        </if>
        <if test="searchKey != null and searchKey != ''">
            and t3.symptom_tag_list like concat('%',#{searchKey,jdbcType=VARCHAR}::varchar,'%')
        </if>
        <if test="sourceId != null and sourceId != ''">
            and t1.org_id = #{sourceId,jdbcType=VARCHAR}
        </if>
        <if test="attackStartTime != null">
            and t1.start_sick_datetime <![CDATA[>=]]> #{attackStartTime,jdbcType=DATE}
        </if>
        <if test="attackEndTime != null">
            and t1.start_sick_datetime <![CDATA[<=]]> #{attackEndTime,jdbcType=DATE}
        </if>
        <if test="attackAddress != null and attackAddress != ''">
            and t1.current_addr_detail like concat('%',#{attackAddress,jdbcType=VARCHAR}::varchar,'%')
        </if>
        <if test="sign != null and sign != ''">
            and t1.exam_desc like concat('%',#{sign,jdbcType=VARCHAR}::varchar,'%')
        </if>
    </select>

</mapper>
