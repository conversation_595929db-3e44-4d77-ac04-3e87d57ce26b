<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.PharmacyMapper">
    <select id="getPurchaseMedicine" resultType="com.iflytek.cdc.edr.entity.pharmacy.PurchaseMedicine">
        SELECT ph.pharmacy_id AS org_id,
               ph.pharmacy_name org_name,
               ph.pharmacy_address org_address,
               ph.check_code,
               ph.director_name,
               ph.director_telephone,
               ddpi.register_idcardno register_id_number,
               ddpi.register_name,
               ddpi.register_phone,
               ddpi.update_datetime register_time,
               ph.pharmacy_name,
               ddpi.identity_no patient_id_number,
               ddpi.name patient_name,
               ddpi.purchaser_sex sex_name,
               ddpi.purchaser_age age,
               ddpi.telephone,
               ddpi.living_address,
               ddpi.update_datetime purchase_time,
               ddpi.prescription_file prescription_url
          FROM dwd.dwd_ds_purchaser_info ddpi
          INNER JOIN dim.dim_ds_pharmacy_info ph ON ph.pharmacy_id = ddpi.pharmacy_id
         WHERE event_id = #{eventId}
         LIMIT 1
    </select>

    <select id="getMedicineList" resultType="com.iflytek.cdc.edr.entity.pharmacy.Medicine">
        SELECT approval_number,
               drug_name,
               drug_use_type_name drug_use_name,
               CONCAT(symptom_desc, '|', other_symptoms) AS symptom_desc,
               drugs_factory,
               batch_number,
               amount,
               unit,
               prescription_flag,
               drugs_type,
               prescription_reviewer
          FROM dwd.dwd_ds_medicine_equipment_info
         WHERE event_id = #{eventId};
    </select>
</mapper>