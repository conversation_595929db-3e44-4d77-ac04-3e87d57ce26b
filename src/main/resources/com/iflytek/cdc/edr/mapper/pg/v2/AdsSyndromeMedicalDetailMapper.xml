<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.AdsSyndromeMedicalDetailMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.edr.entity.ads.AdsSyndromeMedicalDetail">
    <!--@mbg.generated-->
    <!--@Table ads.ads_syndrome_medical_detail-->
    <id column="syndrome_medical_id" jdbcType="VARCHAR" property="syndromeMedicalId" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="syndrome_sub_code" jdbcType="VARCHAR" property="syndromeSubCode" />
    <result column="visit_day" jdbcType="DATE" property="visitDay" />
    <result column="medical_id" jdbcType="VARCHAR" property="medicalId" />
    <result column="patient_name" jdbcType="VARCHAR" property="patientName" />
    <result column="patient_sex_name" jdbcType="VARCHAR" property="patientSexName" />
    <result column="patient_age" jdbcType="VARCHAR" property="patientAge" />
    <result column="age_group" jdbcType="VARCHAR" property="ageGroup" />
    <result column="living_addr_detail" jdbcType="VARCHAR" property="livingAddrDetail" />
    <result column="main_suit" jdbcType="VARCHAR" property="mainSuit" />
    <result column="symptom_list" jdbcType="VARCHAR" property="symptomList" />
    <result column="diagnose_list" jdbcType="VARCHAR" property="diagnoseList" />
    <result column="company" jdbcType="VARCHAR" property="company" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="visit_time" jdbcType="TIMESTAMP" property="visitTime" />
    <result column="enhance_condition_list" jdbcType="VARCHAR" property="enhanceConditionList" />
    <result column="doctor_name" jdbcType="VARCHAR" property="doctorName" />
    <result column="medical_history_now" jdbcType="VARCHAR" property="medicalHistoryNow" />
    <result column="medical_history_before" jdbcType="VARCHAR" property="medicalHistoryBefore" />
    <result column="checkup" jdbcType="VARCHAR" property="checkup" />
    <result column="assisted_exam" jdbcType="VARCHAR" property="assistedExam" />
    <result column="in_table_time" jdbcType="TIMESTAMP" property="inTableTime" />
    <result column="main_suit_symptom_list" jdbcType="VARCHAR" property="mainSuitSymptomList" />
    <result column="syndrome_name" jdbcType="VARCHAR" property="syndromeName" />
    <result column="eat_cold_flag" jdbcType="VARCHAR" property="eatColdFlag" />
    <result column="eat_unclean_flag" jdbcType="VARCHAR" property="eatUncleanFlag" />
    <result column="eat_orther_food_flag" jdbcType="VARCHAR" property="eatOrtherFoodFlag" />
    <result column="drink_flag" jdbcType="VARCHAR" property="drinkFlag" />
    <result column="drug_flag" jdbcType="VARCHAR" property="drugFlag" />
    <result column="job" jdbcType="VARCHAR" property="job" />
    <result column="just_model_flag" jdbcType="VARCHAR" property="justModelFlag" />
    <result column="visit_type_name" jdbcType="VARCHAR" property="visitTypeName" />
    <result column="dept_code" jdbcType="VARCHAR" property="deptCode" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="identity_no" jdbcType="VARCHAR" property="identityNo" />
    <result column="abnormal_time_flag" jdbcType="VARCHAR" property="abnormalTimeFlag" />
    <result column="enhance_condition_flag" jdbcType="VARCHAR" property="enhanceConditionFlag" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="street_code" jdbcType="VARCHAR" property="streetCode" />
    <result column="street_name" jdbcType="VARCHAR" property="streetName" />
    <result column="org_longitude" jdbcType="NUMERIC" property="orgLongitude" />
    <result column="org_latitude" jdbcType="NUMERIC" property="orgLatitude" />
    <result column="living_addr_longitude" jdbcType="NUMERIC" property="livingAddrLongitude" />
    <result column="living_addr_latitude" jdbcType="NUMERIC" property="livingAddrLatitude" />
    <result column="street_longitude" jdbcType="NUMERIC" property="streetLongitude" />
    <result column="street_latitude" jdbcType="NUMERIC" property="streetLatitude" />
    <result column="company_longitude" jdbcType="NUMERIC" property="companyLongitude" />
    <result column="company_latitude" jdbcType="NUMERIC" property="companyLatitude" />
    <result column="source_type" jdbcType="VARCHAR" property="sourceType" />
    <result column="org_id_attent" jdbcType="VARCHAR" property="orgIdAttent" />
    <result column="org_name_attent" jdbcType="VARCHAR" property="orgNameAttent" />
    <result column="doctor_code" jdbcType="VARCHAR" property="doctorCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    syndrome_medical_id, org_id, syndrome_sub_code, visit_day, medical_id, patient_name, 
    patient_sex_name, patient_age, age_group, living_addr_detail, main_suit, symptom_list, 
    diagnose_list, company, org_name, visit_time, enhance_condition_list, doctor_name, 
    medical_history_now, medical_history_before, checkup, assisted_exam, in_table_time, 
    main_suit_symptom_list, syndrome_name, eat_cold_flag, eat_unclean_flag, eat_orther_food_flag, 
    drink_flag, drug_flag, job, just_model_flag, visit_type_name, dept_code, dept_name, 
    phone, identity_no, abnormal_time_flag, enhance_condition_flag, etl_create_datetime, 
    etl_update_datetime, street_code, street_name, org_longitude, org_latitude, living_addr_longitude, 
    living_addr_latitude, street_longitude, street_latitude, company_longitude, company_latitude, 
    source_type, org_id_attent, org_name_attent, doctor_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from ads.ads_syndrome_medical_detail
    where syndrome_medical_id = #{syndromeMedicalId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from ads.ads_syndrome_medical_detail
    where syndrome_medical_id = #{syndromeMedicalId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.iflytek.cdc.edr.entity.ads.AdsSyndromeMedicalDetail">
    <!--@mbg.generated-->
    insert into ads.ads_syndrome_medical_detail (syndrome_medical_id, org_id, syndrome_sub_code, 
      visit_day, medical_id, patient_name, 
      patient_sex_name, patient_age, age_group, 
      living_addr_detail, main_suit, symptom_list, 
      diagnose_list, company, org_name, 
      visit_time, enhance_condition_list, doctor_name, 
      medical_history_now, medical_history_before, 
      checkup, assisted_exam, in_table_time, 
      main_suit_symptom_list, syndrome_name, eat_cold_flag, 
      eat_unclean_flag, eat_orther_food_flag, drink_flag, 
      drug_flag, job, just_model_flag, 
      visit_type_name, dept_code, dept_name, 
      phone, identity_no, abnormal_time_flag, 
      enhance_condition_flag, etl_create_datetime, 
      etl_update_datetime, street_code, street_name, 
      org_longitude, org_latitude, living_addr_longitude, 
      living_addr_latitude, street_longitude, street_latitude, 
      company_longitude, company_latitude, source_type, 
      org_id_attent, org_name_attent, doctor_code
      )
    values (#{syndromeMedicalId,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR}, #{syndromeSubCode,jdbcType=VARCHAR}, 
      #{visitDay,jdbcType=DATE}, #{medicalId,jdbcType=VARCHAR}, #{patientName,jdbcType=VARCHAR}, 
      #{patientSexName,jdbcType=VARCHAR}, #{patientAge,jdbcType=VARCHAR}, #{ageGroup,jdbcType=VARCHAR}, 
      #{livingAddrDetail,jdbcType=VARCHAR}, #{mainSuit,jdbcType=VARCHAR}, #{symptomList,jdbcType=VARCHAR}, 
      #{diagnoseList,jdbcType=VARCHAR}, #{company,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, 
      #{visitTime,jdbcType=TIMESTAMP}, #{enhanceConditionList,jdbcType=VARCHAR}, #{doctorName,jdbcType=VARCHAR}, 
      #{medicalHistoryNow,jdbcType=VARCHAR}, #{medicalHistoryBefore,jdbcType=VARCHAR}, 
      #{checkup,jdbcType=VARCHAR}, #{assistedExam,jdbcType=VARCHAR}, #{inTableTime,jdbcType=TIMESTAMP}, 
      #{mainSuitSymptomList,jdbcType=VARCHAR}, #{syndromeName,jdbcType=VARCHAR}, #{eatColdFlag,jdbcType=VARCHAR}, 
      #{eatUncleanFlag,jdbcType=VARCHAR}, #{eatOrtherFoodFlag,jdbcType=VARCHAR}, #{drinkFlag,jdbcType=VARCHAR}, 
      #{drugFlag,jdbcType=VARCHAR}, #{job,jdbcType=VARCHAR}, #{justModelFlag,jdbcType=VARCHAR}, 
      #{visitTypeName,jdbcType=VARCHAR}, #{deptCode,jdbcType=VARCHAR}, #{deptName,jdbcType=VARCHAR}, 
      #{phone,jdbcType=VARCHAR}, #{identityNo,jdbcType=VARCHAR}, #{abnormalTimeFlag,jdbcType=VARCHAR}, 
      #{enhanceConditionFlag,jdbcType=VARCHAR}, #{etlCreateDatetime,jdbcType=TIMESTAMP}, 
      #{etlUpdateDatetime,jdbcType=TIMESTAMP}, #{streetCode,jdbcType=VARCHAR}, #{streetName,jdbcType=VARCHAR}, 
      #{orgLongitude,jdbcType=NUMERIC}, #{orgLatitude,jdbcType=NUMERIC}, #{livingAddrLongitude,jdbcType=NUMERIC}, 
      #{livingAddrLatitude,jdbcType=NUMERIC}, #{streetLongitude,jdbcType=NUMERIC}, #{streetLatitude,jdbcType=NUMERIC}, 
      #{companyLongitude,jdbcType=NUMERIC}, #{companyLatitude,jdbcType=NUMERIC}, #{sourceType,jdbcType=VARCHAR}, 
      #{orgIdAttent,jdbcType=VARCHAR}, #{orgNameAttent,jdbcType=VARCHAR}, #{doctorCode,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.iflytek.cdc.edr.entity.ads.AdsSyndromeMedicalDetail">
    <!--@mbg.generated-->
    insert into ads.ads_syndrome_medical_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="syndromeMedicalId != null and syndromeMedicalId != ''">
        syndrome_medical_id,
      </if>
      <if test="orgId != null and orgId != ''">
        org_id,
      </if>
      <if test="syndromeSubCode != null and syndromeSubCode != ''">
        syndrome_sub_code,
      </if>
      <if test="visitDay != null">
        visit_day,
      </if>
      <if test="medicalId != null and medicalId != ''">
        medical_id,
      </if>
      <if test="patientName != null and patientName != ''">
        patient_name,
      </if>
      <if test="patientSexName != null and patientSexName != ''">
        patient_sex_name,
      </if>
      <if test="patientAge != null and patientAge != ''">
        patient_age,
      </if>
      <if test="ageGroup != null and ageGroup != ''">
        age_group,
      </if>
      <if test="livingAddrDetail != null and livingAddrDetail != ''">
        living_addr_detail,
      </if>
      <if test="mainSuit != null and mainSuit != ''">
        main_suit,
      </if>
      <if test="symptomList != null and symptomList != ''">
        symptom_list,
      </if>
      <if test="diagnoseList != null and diagnoseList != ''">
        diagnose_list,
      </if>
      <if test="company != null and company != ''">
        company,
      </if>
      <if test="orgName != null and orgName != ''">
        org_name,
      </if>
      <if test="visitTime != null">
        visit_time,
      </if>
      <if test="enhanceConditionList != null and enhanceConditionList != ''">
        enhance_condition_list,
      </if>
      <if test="doctorName != null and doctorName != ''">
        doctor_name,
      </if>
      <if test="medicalHistoryNow != null and medicalHistoryNow != ''">
        medical_history_now,
      </if>
      <if test="medicalHistoryBefore != null and medicalHistoryBefore != ''">
        medical_history_before,
      </if>
      <if test="checkup != null and checkup != ''">
        checkup,
      </if>
      <if test="assistedExam != null and assistedExam != ''">
        assisted_exam,
      </if>
      <if test="inTableTime != null">
        in_table_time,
      </if>
      <if test="mainSuitSymptomList != null and mainSuitSymptomList != ''">
        main_suit_symptom_list,
      </if>
      <if test="syndromeName != null and syndromeName != ''">
        syndrome_name,
      </if>
      <if test="eatColdFlag != null and eatColdFlag != ''">
        eat_cold_flag,
      </if>
      <if test="eatUncleanFlag != null and eatUncleanFlag != ''">
        eat_unclean_flag,
      </if>
      <if test="eatOrtherFoodFlag != null and eatOrtherFoodFlag != ''">
        eat_orther_food_flag,
      </if>
      <if test="drinkFlag != null and drinkFlag != ''">
        drink_flag,
      </if>
      <if test="drugFlag != null and drugFlag != ''">
        drug_flag,
      </if>
      <if test="job != null and job != ''">
        job,
      </if>
      <if test="justModelFlag != null and justModelFlag != ''">
        just_model_flag,
      </if>
      <if test="visitTypeName != null and visitTypeName != ''">
        visit_type_name,
      </if>
      <if test="deptCode != null and deptCode != ''">
        dept_code,
      </if>
      <if test="deptName != null and deptName != ''">
        dept_name,
      </if>
      <if test="phone != null and phone != ''">
        phone,
      </if>
      <if test="identityNo != null and identityNo != ''">
        identity_no,
      </if>
      <if test="abnormalTimeFlag != null and abnormalTimeFlag != ''">
        abnormal_time_flag,
      </if>
      <if test="enhanceConditionFlag != null and enhanceConditionFlag != ''">
        enhance_condition_flag,
      </if>
      <if test="etlCreateDatetime != null">
        etl_create_datetime,
      </if>
      <if test="etlUpdateDatetime != null">
        etl_update_datetime,
      </if>
      <if test="streetCode != null and streetCode != ''">
        street_code,
      </if>
      <if test="streetName != null and streetName != ''">
        street_name,
      </if>
      <if test="orgLongitude != null">
        org_longitude,
      </if>
      <if test="orgLatitude != null">
        org_latitude,
      </if>
      <if test="livingAddrLongitude != null">
        living_addr_longitude,
      </if>
      <if test="livingAddrLatitude != null">
        living_addr_latitude,
      </if>
      <if test="streetLongitude != null">
        street_longitude,
      </if>
      <if test="streetLatitude != null">
        street_latitude,
      </if>
      <if test="companyLongitude != null">
        company_longitude,
      </if>
      <if test="companyLatitude != null">
        company_latitude,
      </if>
      <if test="sourceType != null and sourceType != ''">
        source_type,
      </if>
      <if test="orgIdAttent != null and orgIdAttent != ''">
        org_id_attent,
      </if>
      <if test="orgNameAttent != null and orgNameAttent != ''">
        org_name_attent,
      </if>
      <if test="doctorCode != null and doctorCode != ''">
        doctor_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="syndromeMedicalId != null and syndromeMedicalId != ''">
        #{syndromeMedicalId,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null and orgId != ''">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="syndromeSubCode != null and syndromeSubCode != ''">
        #{syndromeSubCode,jdbcType=VARCHAR},
      </if>
      <if test="visitDay != null">
        #{visitDay,jdbcType=DATE},
      </if>
      <if test="medicalId != null and medicalId != ''">
        #{medicalId,jdbcType=VARCHAR},
      </if>
      <if test="patientName != null and patientName != ''">
        #{patientName,jdbcType=VARCHAR},
      </if>
      <if test="patientSexName != null and patientSexName != ''">
        #{patientSexName,jdbcType=VARCHAR},
      </if>
      <if test="patientAge != null and patientAge != ''">
        #{patientAge,jdbcType=VARCHAR},
      </if>
      <if test="ageGroup != null and ageGroup != ''">
        #{ageGroup,jdbcType=VARCHAR},
      </if>
      <if test="livingAddrDetail != null and livingAddrDetail != ''">
        #{livingAddrDetail,jdbcType=VARCHAR},
      </if>
      <if test="mainSuit != null and mainSuit != ''">
        #{mainSuit,jdbcType=VARCHAR},
      </if>
      <if test="symptomList != null and symptomList != ''">
        #{symptomList,jdbcType=VARCHAR},
      </if>
      <if test="diagnoseList != null and diagnoseList != ''">
        #{diagnoseList,jdbcType=VARCHAR},
      </if>
      <if test="company != null and company != ''">
        #{company,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null and orgName != ''">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="visitTime != null">
        #{visitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enhanceConditionList != null and enhanceConditionList != ''">
        #{enhanceConditionList,jdbcType=VARCHAR},
      </if>
      <if test="doctorName != null and doctorName != ''">
        #{doctorName,jdbcType=VARCHAR},
      </if>
      <if test="medicalHistoryNow != null and medicalHistoryNow != ''">
        #{medicalHistoryNow,jdbcType=VARCHAR},
      </if>
      <if test="medicalHistoryBefore != null and medicalHistoryBefore != ''">
        #{medicalHistoryBefore,jdbcType=VARCHAR},
      </if>
      <if test="checkup != null and checkup != ''">
        #{checkup,jdbcType=VARCHAR},
      </if>
      <if test="assistedExam != null and assistedExam != ''">
        #{assistedExam,jdbcType=VARCHAR},
      </if>
      <if test="inTableTime != null">
        #{inTableTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mainSuitSymptomList != null and mainSuitSymptomList != ''">
        #{mainSuitSymptomList,jdbcType=VARCHAR},
      </if>
      <if test="syndromeName != null and syndromeName != ''">
        #{syndromeName,jdbcType=VARCHAR},
      </if>
      <if test="eatColdFlag != null and eatColdFlag != ''">
        #{eatColdFlag,jdbcType=VARCHAR},
      </if>
      <if test="eatUncleanFlag != null and eatUncleanFlag != ''">
        #{eatUncleanFlag,jdbcType=VARCHAR},
      </if>
      <if test="eatOrtherFoodFlag != null and eatOrtherFoodFlag != ''">
        #{eatOrtherFoodFlag,jdbcType=VARCHAR},
      </if>
      <if test="drinkFlag != null and drinkFlag != ''">
        #{drinkFlag,jdbcType=VARCHAR},
      </if>
      <if test="drugFlag != null and drugFlag != ''">
        #{drugFlag,jdbcType=VARCHAR},
      </if>
      <if test="job != null and job != ''">
        #{job,jdbcType=VARCHAR},
      </if>
      <if test="justModelFlag != null and justModelFlag != ''">
        #{justModelFlag,jdbcType=VARCHAR},
      </if>
      <if test="visitTypeName != null and visitTypeName != ''">
        #{visitTypeName,jdbcType=VARCHAR},
      </if>
      <if test="deptCode != null and deptCode != ''">
        #{deptCode,jdbcType=VARCHAR},
      </if>
      <if test="deptName != null and deptName != ''">
        #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="phone != null and phone != ''">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="identityNo != null and identityNo != ''">
        #{identityNo,jdbcType=VARCHAR},
      </if>
      <if test="abnormalTimeFlag != null and abnormalTimeFlag != ''">
        #{abnormalTimeFlag,jdbcType=VARCHAR},
      </if>
      <if test="enhanceConditionFlag != null and enhanceConditionFlag != ''">
        #{enhanceConditionFlag,jdbcType=VARCHAR},
      </if>
      <if test="etlCreateDatetime != null">
        #{etlCreateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="etlUpdateDatetime != null">
        #{etlUpdateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="streetCode != null and streetCode != ''">
        #{streetCode,jdbcType=VARCHAR},
      </if>
      <if test="streetName != null and streetName != ''">
        #{streetName,jdbcType=VARCHAR},
      </if>
      <if test="orgLongitude != null">
        #{orgLongitude,jdbcType=NUMERIC},
      </if>
      <if test="orgLatitude != null">
        #{orgLatitude,jdbcType=NUMERIC},
      </if>
      <if test="livingAddrLongitude != null">
        #{livingAddrLongitude,jdbcType=NUMERIC},
      </if>
      <if test="livingAddrLatitude != null">
        #{livingAddrLatitude,jdbcType=NUMERIC},
      </if>
      <if test="streetLongitude != null">
        #{streetLongitude,jdbcType=NUMERIC},
      </if>
      <if test="streetLatitude != null">
        #{streetLatitude,jdbcType=NUMERIC},
      </if>
      <if test="companyLongitude != null">
        #{companyLongitude,jdbcType=NUMERIC},
      </if>
      <if test="companyLatitude != null">
        #{companyLatitude,jdbcType=NUMERIC},
      </if>
      <if test="sourceType != null and sourceType != ''">
        #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="orgIdAttent != null and orgIdAttent != ''">
        #{orgIdAttent,jdbcType=VARCHAR},
      </if>
      <if test="orgNameAttent != null and orgNameAttent != ''">
        #{orgNameAttent,jdbcType=VARCHAR},
      </if>
      <if test="doctorCode != null and doctorCode != ''">
        #{doctorCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iflytek.cdc.edr.entity.ads.AdsSyndromeMedicalDetail">
    <!--@mbg.generated-->
    update ads.ads_syndrome_medical_detail
    <set>
      <if test="orgId != null and orgId != ''">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="syndromeSubCode != null and syndromeSubCode != ''">
        syndrome_sub_code = #{syndromeSubCode,jdbcType=VARCHAR},
      </if>
      <if test="visitDay != null">
        visit_day = #{visitDay,jdbcType=DATE},
      </if>
      <if test="medicalId != null and medicalId != ''">
        medical_id = #{medicalId,jdbcType=VARCHAR},
      </if>
      <if test="patientName != null and patientName != ''">
        patient_name = #{patientName,jdbcType=VARCHAR},
      </if>
      <if test="patientSexName != null and patientSexName != ''">
        patient_sex_name = #{patientSexName,jdbcType=VARCHAR},
      </if>
      <if test="patientAge != null and patientAge != ''">
        patient_age = #{patientAge,jdbcType=VARCHAR},
      </if>
      <if test="ageGroup != null and ageGroup != ''">
        age_group = #{ageGroup,jdbcType=VARCHAR},
      </if>
      <if test="livingAddrDetail != null and livingAddrDetail != ''">
        living_addr_detail = #{livingAddrDetail,jdbcType=VARCHAR},
      </if>
      <if test="mainSuit != null and mainSuit != ''">
        main_suit = #{mainSuit,jdbcType=VARCHAR},
      </if>
      <if test="symptomList != null and symptomList != ''">
        symptom_list = #{symptomList,jdbcType=VARCHAR},
      </if>
      <if test="diagnoseList != null and diagnoseList != ''">
        diagnose_list = #{diagnoseList,jdbcType=VARCHAR},
      </if>
      <if test="company != null and company != ''">
        company = #{company,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null and orgName != ''">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="visitTime != null">
        visit_time = #{visitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enhanceConditionList != null and enhanceConditionList != ''">
        enhance_condition_list = #{enhanceConditionList,jdbcType=VARCHAR},
      </if>
      <if test="doctorName != null and doctorName != ''">
        doctor_name = #{doctorName,jdbcType=VARCHAR},
      </if>
      <if test="medicalHistoryNow != null and medicalHistoryNow != ''">
        medical_history_now = #{medicalHistoryNow,jdbcType=VARCHAR},
      </if>
      <if test="medicalHistoryBefore != null and medicalHistoryBefore != ''">
        medical_history_before = #{medicalHistoryBefore,jdbcType=VARCHAR},
      </if>
      <if test="checkup != null and checkup != ''">
        checkup = #{checkup,jdbcType=VARCHAR},
      </if>
      <if test="assistedExam != null and assistedExam != ''">
        assisted_exam = #{assistedExam,jdbcType=VARCHAR},
      </if>
      <if test="inTableTime != null">
        in_table_time = #{inTableTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mainSuitSymptomList != null and mainSuitSymptomList != ''">
        main_suit_symptom_list = #{mainSuitSymptomList,jdbcType=VARCHAR},
      </if>
      <if test="syndromeName != null and syndromeName != ''">
        syndrome_name = #{syndromeName,jdbcType=VARCHAR},
      </if>
      <if test="eatColdFlag != null and eatColdFlag != ''">
        eat_cold_flag = #{eatColdFlag,jdbcType=VARCHAR},
      </if>
      <if test="eatUncleanFlag != null and eatUncleanFlag != ''">
        eat_unclean_flag = #{eatUncleanFlag,jdbcType=VARCHAR},
      </if>
      <if test="eatOrtherFoodFlag != null and eatOrtherFoodFlag != ''">
        eat_orther_food_flag = #{eatOrtherFoodFlag,jdbcType=VARCHAR},
      </if>
      <if test="drinkFlag != null and drinkFlag != ''">
        drink_flag = #{drinkFlag,jdbcType=VARCHAR},
      </if>
      <if test="drugFlag != null and drugFlag != ''">
        drug_flag = #{drugFlag,jdbcType=VARCHAR},
      </if>
      <if test="job != null and job != ''">
        job = #{job,jdbcType=VARCHAR},
      </if>
      <if test="justModelFlag != null and justModelFlag != ''">
        just_model_flag = #{justModelFlag,jdbcType=VARCHAR},
      </if>
      <if test="visitTypeName != null and visitTypeName != ''">
        visit_type_name = #{visitTypeName,jdbcType=VARCHAR},
      </if>
      <if test="deptCode != null and deptCode != ''">
        dept_code = #{deptCode,jdbcType=VARCHAR},
      </if>
      <if test="deptName != null and deptName != ''">
        dept_name = #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="phone != null and phone != ''">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="identityNo != null and identityNo != ''">
        identity_no = #{identityNo,jdbcType=VARCHAR},
      </if>
      <if test="abnormalTimeFlag != null and abnormalTimeFlag != ''">
        abnormal_time_flag = #{abnormalTimeFlag,jdbcType=VARCHAR},
      </if>
      <if test="enhanceConditionFlag != null and enhanceConditionFlag != ''">
        enhance_condition_flag = #{enhanceConditionFlag,jdbcType=VARCHAR},
      </if>
      <if test="etlCreateDatetime != null">
        etl_create_datetime = #{etlCreateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="etlUpdateDatetime != null">
        etl_update_datetime = #{etlUpdateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="streetCode != null and streetCode != ''">
        street_code = #{streetCode,jdbcType=VARCHAR},
      </if>
      <if test="streetName != null and streetName != ''">
        street_name = #{streetName,jdbcType=VARCHAR},
      </if>
      <if test="orgLongitude != null">
        org_longitude = #{orgLongitude,jdbcType=NUMERIC},
      </if>
      <if test="orgLatitude != null">
        org_latitude = #{orgLatitude,jdbcType=NUMERIC},
      </if>
      <if test="livingAddrLongitude != null">
        living_addr_longitude = #{livingAddrLongitude,jdbcType=NUMERIC},
      </if>
      <if test="livingAddrLatitude != null">
        living_addr_latitude = #{livingAddrLatitude,jdbcType=NUMERIC},
      </if>
      <if test="streetLongitude != null">
        street_longitude = #{streetLongitude,jdbcType=NUMERIC},
      </if>
      <if test="streetLatitude != null">
        street_latitude = #{streetLatitude,jdbcType=NUMERIC},
      </if>
      <if test="companyLongitude != null">
        company_longitude = #{companyLongitude,jdbcType=NUMERIC},
      </if>
      <if test="companyLatitude != null">
        company_latitude = #{companyLatitude,jdbcType=NUMERIC},
      </if>
      <if test="sourceType != null and sourceType != ''">
        source_type = #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="orgIdAttent != null and orgIdAttent != ''">
        org_id_attent = #{orgIdAttent,jdbcType=VARCHAR},
      </if>
      <if test="orgNameAttent != null and orgNameAttent != ''">
        org_name_attent = #{orgNameAttent,jdbcType=VARCHAR},
      </if>
      <if test="doctorCode != null and doctorCode != ''">
        doctor_code = #{doctorCode,jdbcType=VARCHAR},
      </if>
    </set>
    where syndrome_medical_id = #{syndromeMedicalId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iflytek.cdc.edr.entity.ads.AdsSyndromeMedicalDetail">
    <!--@mbg.generated-->
    update ads.ads_syndrome_medical_detail
    set org_id = #{orgId,jdbcType=VARCHAR},
      syndrome_sub_code = #{syndromeSubCode,jdbcType=VARCHAR},
      visit_day = #{visitDay,jdbcType=DATE},
      medical_id = #{medicalId,jdbcType=VARCHAR},
      patient_name = #{patientName,jdbcType=VARCHAR},
      patient_sex_name = #{patientSexName,jdbcType=VARCHAR},
      patient_age = #{patientAge,jdbcType=VARCHAR},
      age_group = #{ageGroup,jdbcType=VARCHAR},
      living_addr_detail = #{livingAddrDetail,jdbcType=VARCHAR},
      main_suit = #{mainSuit,jdbcType=VARCHAR},
      symptom_list = #{symptomList,jdbcType=VARCHAR},
      diagnose_list = #{diagnoseList,jdbcType=VARCHAR},
      company = #{company,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      visit_time = #{visitTime,jdbcType=TIMESTAMP},
      enhance_condition_list = #{enhanceConditionList,jdbcType=VARCHAR},
      doctor_name = #{doctorName,jdbcType=VARCHAR},
      medical_history_now = #{medicalHistoryNow,jdbcType=VARCHAR},
      medical_history_before = #{medicalHistoryBefore,jdbcType=VARCHAR},
      checkup = #{checkup,jdbcType=VARCHAR},
      assisted_exam = #{assistedExam,jdbcType=VARCHAR},
      in_table_time = #{inTableTime,jdbcType=TIMESTAMP},
      main_suit_symptom_list = #{mainSuitSymptomList,jdbcType=VARCHAR},
      syndrome_name = #{syndromeName,jdbcType=VARCHAR},
      eat_cold_flag = #{eatColdFlag,jdbcType=VARCHAR},
      eat_unclean_flag = #{eatUncleanFlag,jdbcType=VARCHAR},
      eat_orther_food_flag = #{eatOrtherFoodFlag,jdbcType=VARCHAR},
      drink_flag = #{drinkFlag,jdbcType=VARCHAR},
      drug_flag = #{drugFlag,jdbcType=VARCHAR},
      job = #{job,jdbcType=VARCHAR},
      just_model_flag = #{justModelFlag,jdbcType=VARCHAR},
      visit_type_name = #{visitTypeName,jdbcType=VARCHAR},
      dept_code = #{deptCode,jdbcType=VARCHAR},
      dept_name = #{deptName,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      identity_no = #{identityNo,jdbcType=VARCHAR},
      abnormal_time_flag = #{abnormalTimeFlag,jdbcType=VARCHAR},
      enhance_condition_flag = #{enhanceConditionFlag,jdbcType=VARCHAR},
      etl_create_datetime = #{etlCreateDatetime,jdbcType=TIMESTAMP},
      etl_update_datetime = #{etlUpdateDatetime,jdbcType=TIMESTAMP},
      street_code = #{streetCode,jdbcType=VARCHAR},
      street_name = #{streetName,jdbcType=VARCHAR},
      org_longitude = #{orgLongitude,jdbcType=NUMERIC},
      org_latitude = #{orgLatitude,jdbcType=NUMERIC},
      living_addr_longitude = #{livingAddrLongitude,jdbcType=NUMERIC},
      living_addr_latitude = #{livingAddrLatitude,jdbcType=NUMERIC},
      street_longitude = #{streetLongitude,jdbcType=NUMERIC},
      street_latitude = #{streetLatitude,jdbcType=NUMERIC},
      company_longitude = #{companyLongitude,jdbcType=NUMERIC},
      company_latitude = #{companyLatitude,jdbcType=NUMERIC},
      source_type = #{sourceType,jdbcType=VARCHAR},
      org_id_attent = #{orgIdAttent,jdbcType=VARCHAR},
      org_name_attent = #{orgNameAttent,jdbcType=VARCHAR},
      doctor_code = #{doctorCode,jdbcType=VARCHAR}
    where syndrome_medical_id = #{syndromeMedicalId,jdbcType=VARCHAR}
  </update>
</mapper>