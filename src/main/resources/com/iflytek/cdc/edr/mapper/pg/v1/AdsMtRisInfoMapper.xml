<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v1.AdsMtRisInfoMapper">
    <resultMap id="BaseResultMap" type="com.iflytek.cdc.edr.entity.AdsMtRisInfo">
        <!--@mbg.generated-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="global_person_id" jdbcType="VARCHAR" property="globalPersonId"/>
        <result column="examine_report_no" jdbcType="VARCHAR" property="examineReportNo"/>
        <result column="report_title" jdbcType="VARCHAR" property="reportTitle"/>
        <result column="report_type_code" jdbcType="VARCHAR" property="reportTypeCode"/>
        <result column="report_type_name" jdbcType="VARCHAR" property="reportTypeName"/>
        <result column="tech_inside_no" jdbcType="VARCHAR" property="techInsideNo"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
        <result column="org_code" jdbcType="VARCHAR" property="orgCode"/>
        <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
        <result column="apply_org_id" jdbcType="VARCHAR" property="applyOrgId"/>
        <result column="apply_org_name" jdbcType="VARCHAR" property="applyOrgName"/>
        <result column="apply_dept_code" jdbcType="VARCHAR" property="applyDeptCode"/>
        <result column="apply_dept_name" jdbcType="VARCHAR" property="applyDeptName"/>
        <result column="apply_doc_code" jdbcType="VARCHAR" property="applyDocCode"/>
        <result column="apply_doc_name" jdbcType="VARCHAR" property="applyDocName"/>
        <result column="exam_report_desc" jdbcType="VARCHAR" property="examReportDesc"/>
        <result column="exam_org_id" jdbcType="VARCHAR" property="examOrgId"/>
        <result column="exam_org_name" jdbcType="VARCHAR" property="examOrgName"/>
        <result column="exec_dept_code" jdbcType="VARCHAR" property="execDeptCode"/>
        <result column="exec_dept_name" jdbcType="VARCHAR" property="execDeptName"/>
        <result column="exec_receive_time" jdbcType="TIMESTAMP" property="execReceiveTime"/>
        <result column="executor_code" jdbcType="VARCHAR" property="executorCode"/>
        <result column="executor" jdbcType="VARCHAR" property="executor"/>
        <result column="exam_class_code" jdbcType="VARCHAR" property="examClassCode"/>
        <result column="exam_class_name" jdbcType="VARCHAR" property="examClassName"/>
        <result column="exam_item_code" jdbcType="VARCHAR" property="examItemCode"/>
        <result column="exam_item_name" jdbcType="VARCHAR" property="examItemName"/>
        <result column="instrument_code" jdbcType="VARCHAR" property="instrumentCode"/>
        <result column="instrument" jdbcType="VARCHAR" property="instrument"/>
        <result column="exam_datetime" jdbcType="TIMESTAMP" property="examDatetime"/>
        <result column="exam_view" jdbcType="VARCHAR" property="examView"/>
        <result column="exam_concluse" jdbcType="VARCHAR" property="examConcluse"/>
        <result column="report_time" jdbcType="TIMESTAMP" property="reportTime"/>
        <result column="reporter_id" jdbcType="VARCHAR" property="reporterId"/>
        <result column="reporter_name" jdbcType="VARCHAR" property="reporterName"/>
        <result column="report_audit_time" jdbcType="TIMESTAMP" property="reportAuditTime"/>
        <result column="report_auditor_code" jdbcType="VARCHAR" property="reportAuditorCode"/>
        <result column="report_auditor_name" jdbcType="VARCHAR" property="reportAuditorName"/>
        <result column="report_call_path" jdbcType="VARCHAR" property="reportCallPath"/>
        <result column="report_adnexa_type" jdbcType="VARCHAR" property="reportAdnexaType"/>
        <result column="report_adnexa_name" jdbcType="VARCHAR" property="reportAdnexaName"/>
        <result column="report_adnexa_path" jdbcType="VARCHAR" property="reportAdnexaPath"/>
        <result column="serial_no" jdbcType="VARCHAR" property="serialNo"/>
        <result column="person_info_id" jdbcType="VARCHAR" property="personInfoId"/>
        <result column="patient_name" jdbcType="VARCHAR" property="patientName"/>
        <result column="patient_sex_code" jdbcType="VARCHAR" property="patientSexCode"/>
        <result column="patient_sex_desc" jdbcType="VARCHAR" property="patientSexDesc"/>
        <result column="patient_birthday" jdbcType="DATE" property="patientBirthday"/>
        <result column="identity_type_code" jdbcType="VARCHAR" property="identityTypeCode"/>
        <result column="identity_type_name" jdbcType="VARCHAR" property="identityTypeName"/>
        <result column="identity_no" jdbcType="VARCHAR" property="identityNo"/>
        <result column="patient_age" jdbcType="VARCHAR" property="patientAge"/>
        <result column="patient_age_unit" jdbcType="VARCHAR" property="patientAgeUnit"/>
        <result column="health_card_id" jdbcType="VARCHAR" property="healthCardId"/>
        <result column="patient_source" jdbcType="VARCHAR" property="patientSource"/>
        <result column="visit_type_code" jdbcType="VARCHAR" property="visitTypeCode"/>
        <result column="frg_medical_unit" jdbcType="VARCHAR" property="frgMedicalUnit"/>
        <result column="ward_code" jdbcType="VARCHAR" property="wardCode"/>
        <result column="ward_name" jdbcType="VARCHAR" property="wardName"/>
        <result column="dept_room" jdbcType="VARCHAR" property="deptRoom"/>
        <result column="bedno" jdbcType="VARCHAR" property="bedno"/>
        <result column="exam_part_code" jdbcType="VARCHAR" property="examPartCode"/>
        <result column="exam_part_name" jdbcType="VARCHAR" property="examPartName"/>
        <result column="dicom_studyuids" jdbcType="VARCHAR" property="dicomStudyuids"/>
        <result column="creator_id" jdbcType="VARCHAR" property="creatorId"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updator_id" jdbcType="VARCHAR" property="updatorId"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="deleted" jdbcType="VARCHAR" property="deleted"/>
        <result column="source_id" jdbcType="VARCHAR" property="sourceId"/>
        <result column="event_id" jdbcType="VARCHAR" property="eventId"/>
        <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime"/>
        <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime"/>
        <result column="medical_id" jdbcType="VARCHAR" property="medicalId"/>
        <result column="visit_time" jdbcType="TIMESTAMP" property="visitTime"/>
        <result column="visit_type_name" jdbcType="VARCHAR" property="visitTypeName"/>
    </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, global_person_id, examine_report_no, report_title, report_type_code, report_type_name,
    tech_inside_no, order_id, org_id, org_code, org_name, apply_org_id, apply_org_name,
    apply_dept_code, apply_dept_name, apply_doc_code, apply_doc_name, exam_report_desc,
    exam_org_id, exam_org_name, exec_dept_code, exec_dept_name, exec_receive_time, executor_code,
    executor, exam_class_code, exam_class_name, exam_item_code, exam_item_name, instrument_code,
    instrument, exam_datetime, exam_view, exam_concluse, report_time, reporter_id, reporter_name,
    report_audit_time, report_auditor_code, report_auditor_name, report_call_path, report_adnexa_type,
    report_adnexa_name, report_adnexa_path, serial_no, person_info_id, patient_name,
    patient_sex_code, patient_sex_desc, patient_birthday, identity_type_code, identity_type_name,
    identity_no, patient_age, patient_age_unit, health_card_id, patient_source, visit_type_code,
    frg_medical_unit, ward_code, ward_name, dept_room, bedno, exam_part_code, exam_part_name,
    dicom_studyuids, creator_id, create_datetime, updator_id, update_datetime, enabled,
    deleted, source_id, event_id, etl_create_datetime, etl_update_datetime, medical_id,
    visit_time, visit_type_name
  </sql>



    <sql id="selectCheckReportSql">
        select c.id,
               c.event_id,
               c.event_id               as medical_id,
               c.business_id            as serial_no,
               o.org_id,
               o.org_code,
               o.org_name,
               e.global_person_id,
               c.his_check_id           as examine_report_no,
               c.data_src               as visit_type_code,
               c.check_datetime         as exam_datetime,
               c.check_name             as exam_item_name,
               c.check_part             as exam_part_name,
               c.exam_type_code         as exam_class_code,
               c.exam_type_desc         as exam_class_name,
               c.apply_dept_code,
               c.apply_dept_name,
               c.apply_doc_id           as apply_doc_code,
               c.apply_doc_name         as apply_doc_name,
               c.exam_conclusion        as exam_concluse,
               c.report_datetime        as report_time,
               c.report_doc_id          as reporter_id,
               c.report_doc_name        as reporter_name,
               c.verify_datetime        as report_audit_time,
               c.verify_doc_id          as report_auditor_code,
               c.verify_doc_name        as report_auditor_name,
               e.patient_name           as patient_name,
               '01'                     as identity_type_code,
               '居民身份证'               as identity_type_name,
               e.patient_id_number      as identity_no,
               c.source_create_datetime as create_datetime,
               c.source_update_datetime as update_datetime
        from dwd.dwd_ch_check_report c
                 left join dwd.dwd_event_record e
                           on c.event_id = e.event_id
                 join dim.dim_medical_organization_info o
                      on c.org_id = o.org_id
    </sql>

    <select id="selectCheckReportByPatient" resultMap="BaseResultMap">
        select distinct on (c.event_id, coalesce(c.business_id, c.id))
               c.id,
               c.event_id,
               c.event_id               as medical_id,
               c.business_id            as serial_no,
               o.org_id,
               o.org_code,
               o.org_name,
               e.global_person_id,
               c.his_check_id           as examine_report_no,
               c.data_src               as visit_type_code,
               c.check_datetime         as exam_datetime,
               c.check_name             as exam_item_name,
               c.check_part             as exam_part_name,
               c.exam_type_code         as exam_class_code,
               c.exam_type_desc         as exam_class_name,
               c.apply_dept_code,
               c.apply_dept_name,
               c.apply_doc_id           as apply_doc_code,
               c.apply_doc_name         as apply_doc_name,
               c.exam_conclusion        as exam_concluse,
               c.report_datetime        as report_time,
               c.report_doc_id          as reporter_id,
               c.report_doc_name        as reporter_name,
               c.verify_datetime        as report_audit_time,
               c.verify_doc_id          as report_auditor_code,
               c.verify_doc_name        as report_auditor_name,
               e.patient_name           as patient_name,
               '01'                     as identity_type_code,
               '居民身份证'             as identity_type_name,
               e.patient_id_number      as identity_no,
               c.source_create_datetime as create_datetime,
               c.source_update_datetime as update_datetime
        from dwd.dwd_ch_check_report c
                 left join dwd.dwd_event_record e
                           on c.event_id = e.event_id
                 join dim.dim_medical_organization_info o
                      on c.org_id = o.org_id
        <where>
            <if test="param.startTime != null and param.startTime != ''">
                and c.check_datetime >= #{param.startTime}::timestamp
            </if>
            <if test="param.endTime != null and param.endTime != ''">
                and c.check_datetime  <![CDATA[<=]]> #{param.endTime}::timestamp
            </if>
            <if test="param.patientName">
                and e.patient_name = #{param.patientName,jdbcType=VARCHAR}
            </if>
            <if test="param.idCardNo != null">
                and e.patient_id_number = #{param.idCardNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectCheckReportByIds" resultMap="BaseResultMap">
        <include refid="selectCheckReportSql"/>
        <where>
            <if test="ids != null and ids.size() != 0">
                c.id in
                <foreach collection="ids" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectCheckReportByEventIdReportNo" resultMap="BaseResultMap">
        <include refid="selectCheckReportSql"/>
        <where>
            <if test="eventId != null and eventId != ''">
                and c.event_id = #{eventId,jdbcType=VARCHAR}
            </if>
            <if test="examineReportNo != null and examineReportNo != ''">
                and c.his_check_id = #{examineReportNo,jdbcType=VARCHAR}
            </if>
        </where>

    </select>

    <select id="selectCheckReportByEventIds" resultMap="BaseResultMap">
        <include refid="selectCheckReportSql"/>
        <where>
            <if test="eventIds != null and eventIds.size() != 0">
                c.event_id in
                <foreach collection="eventIds" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>
</mapper>