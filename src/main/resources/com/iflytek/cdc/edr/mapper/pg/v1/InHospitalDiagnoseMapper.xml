<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v1.InHospitalDiagnoseMapper">

    <resultMap type="com.iflytek.cdc.edr.entity.inhospital.InHospitalDiagnose" id="InHospitalDiagnoseResult">
        <result property="id"    column="id"    />
        <result property="standerOrgCode"    column="stander_org_code"    />
        <result property="typeName"    column="type_name"    />
        <result property="inHospitalId"    column="inhospital_id"    />
        <result property="primaryFlag"    column="primary_flag"    />
        <result property="diagnoseDate"    column="diagnose_date"    />
        <result property="diagnoseCode"    column="diagnose_code"    />
        <result property="diagnoseName"    column="diagnose_name"    />
        <result property="deptName"    column="dept_name"    />
        <result property="doctorName"    column="doctor_name"    />
        <result property="zdmemo"    column="zdmemo"    />
    </resultMap>

    <sql id="selectInHospitalDiagnoseVo">
        select id, stander_org_code, type_name, inhospital_id, primary_flag, diagnose_date, diagnose_code, diagnose_name, dept_name, doctor_name, zdmemo from dwd.dwd_ch_inhospital_diagnose
    </sql>

    <select id="selectInHospitalDiagnoseList" parameterType="String" resultMap="InHospitalDiagnoseResult">
        <include refid="selectInHospitalDiagnoseVo"/>
       where
              stander_org_code = #{standerOrgCode}
              and inhospital_id =#{inHospitalId}
       order by diagnose_date
    </select>

    <select id="selectInHospitalDiagnoseById" parameterType="String" resultMap="InHospitalDiagnoseResult">
        <include refid="selectInHospitalDiagnoseVo"/>
        where id = #{id}
    </select>


</mapper>
