<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.VAdsMedSyndromeOrgParentStatsMapper">



    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        full_date, org_id, org_name, syndrome_code, syndrome_name, synd_med_cnt, synd_person_cnt, etl_create_datetime, etl_update_datetime
    </sql>
    <select id="distributionAndStats" resultType="com.iflytek.cdc.edr.dto.syndrome.SyndromeMonitorRespDTO">
	with tmp as(
        select
        coalesce(sum(synd_med_cnt),0)  medCnt,
        coalesce(sum(last_year_med_cnt),0) lastYearMedCnt,
        max(rule_id) rule_id,
        parent_syndrome_code  syndromeCode,
        parent_syndrome_name  syndromeName
        from
        ads.v_ads_med_syndrome_parent_stats t
        <where>
            <if test="startDate != null and endDate != null">
                and  CAST(full_date AS DATE) between  #{startDate} and  #{endDate}
            </if>
            <if test="regionInfoList!=null and regionInfoList.size() &gt; 0">
                and
                <foreach close=")" collection="regionInfoList" index="index" item="item" open="(  " separator=" or ">
                    (1=1
                    <if test="item.provinceCode != null and item.provinceCode != '' ">
                        and PROVINCE_CODE = #{item.provinceCode}
                    </if>
                    <if test="item.cityCode != null and item.cityCode != '' ">
                        and CITY_CODE = #{item.cityCode}
                    </if>
                    <if test="item.districtCode != null and item.districtCode !='' ">
                        and DISTRICT_CODE = #{item.districtCode}
                    </if>)
                </foreach>
            </if>
            <if test="symptom != null and symptom.size() &gt; 0">
                and parent_syndrome_code in
                <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>

        group by
        parent_syndrome_code ,
        parent_syndrome_name
        )
        select
        coalesce(tmp.medCnt,0) medCnt,
        coalesce(tmp.lastYearMedCnt,0) lastYearMedCnt,
        tmp.rule_id,
        mr.disease_code syndromeCode,
        mr.disease_name syndromeName  from dim.dim_mr_disease_monitor_rule mr  left join tmp  on tmp.syndromeCode=mr.disease_code
        where
        mr.config_type::text = 'syndrome'
        and mr.delete_flag = 0
        and mr.status = 1
        <if test="symptom != null and symptom.size() &gt; 0">
            and mr.disease_code in
            <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        order by
        syndromeCode
    </select>
    <select id="timeTrend" resultType="com.iflytek.cdc.edr.dto.syndrome.SyndromeMonitorRespDTO">
        with tmp as (
        select
        full_date,
        coalesce(sum(synd_med_cnt),0)  medCnt,
        max(rule_id) rule_id,
        parent_syndrome_code  syndromeCode,
        parent_syndrome_name  syndromeName,
        to_char(full_date, 'MM-DD') short_full_date
        from
        ads.v_ads_med_syndrome_parent_stats
        <where>
            <if test="startDate != null and endDate != null">
                and  CAST(full_date AS DATE) between  #{startDate} and  #{endDate}
            </if>
            <if test="regionInfoList!=null and regionInfoList.size() &gt; 0">
                and
                <foreach close=")" collection="regionInfoList" index="index" item="item" open="(  " separator=" or ">
                    (1=1
                    <if test="item.provinceCode != null and item.provinceCode != '' ">
                        and PROVINCE_CODE = #{item.provinceCode}
                    </if>
                    <if test="item.cityCode != null and item.cityCode != '' ">
                        and CITY_CODE = #{item.cityCode}
                    </if>
                    <if test="item.districtCode != null and item.districtCode !='' ">
                        and DISTRICT_CODE = #{item.districtCode}
                    </if>)
                </foreach>
            </if>
            <if test="symptom != null and symptom.size() &gt; 0">
                and parent_syndrome_code in
                <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        group by
        full_date,
        parent_syndrome_code ,
        parent_syndrome_name
        order by parent_syndrome_code,full_date
        )
        select
        tmp.full_date,
        to_char(tmp.full_date, 'MM-DD') short_full_date,
        coalesce(tmp.medCnt,0) medCnt,
        tmp.rule_id,
        mr.disease_code syndromeCode,
        mr.disease_name syndromeName  from dim.dim_mr_disease_monitor_rule mr  left join tmp  on tmp.syndromeCode=mr.disease_code
        where
        mr.config_type::text = 'syndrome'
        and mr.delete_flag = 0
        and mr.status = 1
        <if test="symptom != null and symptom.size() &gt; 0">
            and mr.disease_code in
            <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        order by
        syndromeCode,
        full_date

    </select>
    <select id="abnormalStats" resultType="com.iflytek.cdc.edr.dto.syndrome.SyndromeMonitorRespDTO">
        select
        coalesce(sum(synd_med_cnt),0)  medCnt,
        coalesce(sum(last_year_med_cnt),0) lastYearMedCnt,
        parent_syndrome_code   syndrome_code,
        parent_syndrome_name   syndrome_name,
        full_date,
        max(is_abnormal) is_abnormal,
        max(rule_id) rule_id,
	    to_char(full_date, 'MM-DD') short_full_date
        from
        ads.v_ads_med_syndrome_parent_stats
        <where>
            <if test="startDate != null and endDate != null">
                and  CAST(full_date AS DATE) between  #{startDate} and  #{endDate}
            </if>
            <if test="regionInfoList!=null and regionInfoList.size() &gt; 0">
            and
                <foreach close=")" collection="regionInfoList" index="index" item="item" open="(  " separator=" or ">
                    (1=1
                    <if test="item.provinceCode != null and item.provinceCode != '' ">
                        and PROVINCE_CODE = #{item.provinceCode}
                    </if>
                    <if test="item.cityCode != null and item.cityCode != '' ">
                        and CITY_CODE = #{item.cityCode}
                    </if>
                    <if test="item.districtCode != null and item.districtCode !='' ">
                        and DISTRICT_CODE = #{item.districtCode}
                    </if>)
                </foreach>
            </if>
            <if test="symptom != null and symptom.size() &gt; 0">
                and parent_syndrome_code in
                <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        group by
        parent_syndrome_code ,
        parent_syndrome_name, full_date   order by medCnt desc
    </select>
    <select id="getOrgCount" resultType="com.iflytek.cdc.edr.dto.syndrome.SyndromeGeographyTopRespDTO">
        select
        coalesce(count(distinct org_id),0)     orgCnt
        from
        ads.v_ads_med_syndrome_org_parent_stats

        <where>
            <if test="startDate != null and endDate != null">
                and  CAST(full_date AS DATE) between  #{startDate} and  #{endDate}
            </if>
            <if test="regionInfoList!=null and regionInfoList.size() &gt; 0">
                and
                <foreach close=")" collection="regionInfoList" index="index" item="item" open="(  " separator=" or ">
                    (1=1
                    <if test="item.provinceCode != null and item.provinceCode != '' ">
                        and PROVINCE_CODE = #{item.provinceCode}
                    </if>
                    <if test="item.cityCode != null and item.cityCode != '' ">
                        and CITY_CODE = #{item.cityCode}
                    </if>
                    <if test="item.districtCode != null and item.districtCode !='' ">
                        and DISTRICT_CODE = #{item.districtCode}
                    </if>)
                </foreach>
            </if>
            <if test="symptom != null and symptom.size() &gt; 0">
                and parent_syndrome_code in
                <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getDistrictCount" resultType="com.iflytek.cdc.edr.dto.syndrome.SyndromeGeographyRespDTO">
        select
        district_code,
        district_name,
        coalesce (sum(synd_med_cnt),0) medCnt
        from
        ads.v_ads_med_syndrome_parent_stats
        <where>
            <if test="startDate != null and endDate != null">
                and  CAST(full_date AS DATE) between  #{startDate} and  #{endDate}
            </if>
            <if test="regionInfoList!=null and regionInfoList.size() &gt; 0">
                and
                <foreach close=")" collection="regionInfoList" index="index" item="item" open="(  " separator=" or ">
                    (1=1
                    <if test="item.provinceCode != null and item.provinceCode != '' ">
                        and PROVINCE_CODE = #{item.provinceCode}
                    </if>
                    <if test="item.cityCode != null and item.cityCode != '' ">
                        and CITY_CODE = #{item.cityCode}
                    </if>
                    <if test="item.districtCode != null and item.districtCode !='' ">
                        and DISTRICT_CODE = #{item.districtCode}
                    </if>)
                </foreach>
            </if>
            <if test="symptom != null and symptom.size() &gt; 0">
                and parent_syndrome_code in
                <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        group by
        district_code,
        district_name  order by medCnt desc
    </select>
    <select id="getFullDateCount" resultType="com.iflytek.cdc.edr.dto.syndrome.SyndromeTimeTrendRespDTO">
        select
        synd_med_cnt med_cnt,
        last_year_med_cnt,
        avg_med_cnt::numeric(11,1) as avgMedCnt,
        reference_value::numeric(11,1) as referenceValue,
        full_date,
        rule_id,
        to_char(full_date, 'MM-DD') short_full_date
        from
        ads.v_ads_med_syndrome_parent_stats
        <where>
            <if test="startDate != null and endDate != null">
                and  CAST(full_date AS DATE) between  #{startDate} and  #{endDate}
            </if>
            <if test="regionInfoList!=null and regionInfoList.size() &gt; 0">
                and
                <foreach close=")" collection="regionInfoList" index="index" item="item" open="(  " separator=" or ">
                    (1=1
                    <if test="item.provinceCode != null and item.provinceCode != '' ">
                        and PROVINCE_CODE = #{item.provinceCode}
                    </if>
                    <if test="item.cityCode != null and item.cityCode != '' ">
                        and CITY_CODE = #{item.cityCode}
                    </if>
                    <if test="item.districtCode != null and item.districtCode !='' ">
                        and DISTRICT_CODE = #{item.districtCode}
                    </if>)
                </foreach>
            </if>
            <if test="symptom != null and symptom.size() &gt; 0">
                and parent_syndrome_code in
                <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        order by full_date
    </select>
    <select id="tableChart" resultType="com.iflytek.cdc.edr.dto.syndrome.SyndromeTimeTrendDistrictRespDTO">
        select
        max(city_code) city_code,
        max(city_name) city_name,
        district_code,
        district_name,
        full_date,
        to_char(full_date, 'MM-DD') short_full_date,
        coalesce (sum(synd_med_cnt),0) medCnt,
        coalesce (sum(avg_med_cnt::numeric(11,1)),0) avgMedCnt,
        coalesce (sum(reference_value::numeric(11,1)),0) referenceValue,
        max(rule_id) rule_id ,
        max(is_abnormal) isAbnormal ,
        coalesce (sum(last_year_med_cnt),0) lastYearMedCnt
        from
        ads.v_ads_med_syndrome_parent_stats
        <where>
            <if test="startDate != null and endDate != null">
                and  CAST(full_date AS DATE) between  #{startDate} and  #{endDate}
            </if>
            <if test="regionInfoList!=null and regionInfoList.size() &gt; 0">
                and
                <foreach close=")" collection="regionInfoList" index="index" item="item" open="(  " separator=" or ">
                    (1=1
                    <if test="item.provinceCode != null and item.provinceCode != '' ">
                        and PROVINCE_CODE = #{item.provinceCode}
                    </if>
                    <if test="item.cityCode != null and item.cityCode != '' ">
                        and CITY_CODE = #{item.cityCode}
                    </if>
                    <if test="item.districtCode != null and item.districtCode !='' ">
                        and DISTRICT_CODE = #{item.districtCode}
                    </if>)
                </foreach>
            </if>
            <if test="symptom != null and symptom.size() &gt; 0">
                and parent_syndrome_code in
                <foreach close=")" collection="symptom" index="index" item="item" open="(" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        group by
        district_code,
        district_name,full_date order by district_code

    </select>

</mapper>
