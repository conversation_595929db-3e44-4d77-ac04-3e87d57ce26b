<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.ExternalMapper">

    <select id="getMainSuitData" resultType="com.iflytek.cdc.edr.dto.external.DwdChMedicalRecord">
        SELECT *
        FROM dwd.dwd_ch_medical_record
        WHERE org_id = #{orgId}
          AND CAST(event_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getDiagnoseDetail" resultType="com.iflytek.cdc.edr.dto.external.DwdChMedicalDiagnoseResult">
        SELECT *
        FROM dwd.dwd_ch_medical_diagnose_result
        WHERE org_id = #{orgId}
          AND CAST(diagnose_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getPrescription" resultType="com.iflytek.cdc.edr.dto.external.DwdChPrescription">
        SELECT *
        FROM dwd.dwd_ch_prescription
        WHERE org_id = #{orgId}
          AND apply_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getMedicalRecord" resultType="com.iflytek.cdc.edr.dto.external.OdsDjyyJkV303EmrBc">
        select *
        from ods.ods_djyy_jk_v303_emr_bc a
                 left join dim.dim_medical_organization_info o
                           on a.stander_org_code = o.org_code
        where o.org_id = #{orgId}
          and CAST(a.record_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getDoctorAdviceInformation" resultType="com.iflytek.cdc.edr.dto.external.OdsDjyyJkV107HisYz">
        select *
        from ods.ods_djyy_jk_v107_his_yz a
                 left join dim.dim_medical_organization_info o
                           on a.stander_org_code = o.org_code
        where o.org_id = #{orgId}
          and CAST(a.order_date AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getGeneralSituation" resultType="com.iflytek.cdc.edr.dto.external.OdsDjyyJkV107HisTz">
        select *
        from ods.ods_djyy_jk_v107_his_tz a
                 left join dim.dim_medical_organization_info o
                           on a.stander_org_code = o.org_code
        where o.org_id = #{orgId}
          and CAST(a.create_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getCheckDetail" resultType="com.iflytek.cdc.edr.dto.external.DwdChCheckReport">
        SELECT *
        FROM dwd.dwd_ch_check_report
        WHERE org_id = #{orgId}
          AND check_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getExamineDetail" resultType="com.iflytek.cdc.edr.dto.external.DwdChInspectionReport">
        SELECT *
        FROM dwd.dwd_ch_inspection_report
        WHERE org_id = #{orgId}
          AND CAST(result_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getReportCardRecord" resultType="com.iflytek.cdc.edr.dto.external.DwdChReportCardRecord">
        SELECT *
        FROM dwd.dwd_ch_report_card_record
        WHERE org_id = #{orgId}
          AND CAST(create_time AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getInHospitalRecord" resultType="com.iflytek.cdc.edr.dto.external.DwdChInhospitalRecord">
        SELECT *
        FROM dwd.dwd_ch_inhospital_record
        WHERE org_id = #{orgId}
          AND CAST(in_hos_date AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getPatientInformation" resultType="com.iflytek.cdc.edr.dto.external.DimMdmPersonInfo">

    </select>
    <select id="getSymptomInformation" resultType="com.iflytek.cdc.edr.dto.external.DwdSyndromeMedicalSymptomTag">
        SELECT *
        FROM dwd.dwd_syndrome_medical_symptom_tag
        WHERE org_id = #{orgId}
          AND event_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>
    <select id="getReportCardRecordByEventIds"
            resultType="com.iflytek.cdc.edr.dto.external.DwdChReportCardRecord">
        select *
        FROM dwd.dwd_ch_report_card_record
        where event_id in
        <foreach collection="eventIds" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="findBySymptomAndDate" resultType="com.iflytek.cdc.edr.dto.external.DwdChMedicalRecord">
        select
        *
        from dwd.dwd_ch_medical_record
        where medical_id in
        (select medical_id
        from dwd.dwd_syndrome_medical_symptom_tag s
        where event_datetime <![CDATA[>=]]> #{startDate,jdbcType=TIMESTAMP}
        and event_datetime <![CDATA[<=]]> #{endDate,jdbcType=TIMESTAMP}
        and symptom_tag_list like concat('%',#{symptom,jdbcType=VARCHAR}::varchar,'%'))
    </select>

    <select id="getNewReportCardRecordByEventIds" resultType="com.iflytek.cdc.edr.dto.external.DwdChReportCardRecord">
        select *
        from ads.v_infection_report
        -- todo 后续需要处理medical_id和event_id
        where medical_id in
        <foreach collection="eventIds" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getNewSymptomInformation"
            resultType="com.iflytek.cdc.edr.dto.external.DwdSyndromeMedicalSymptomTag">
        SELECT *
        FROM ads.v_syndrome_medical_symptom_tag
        WHERE org_id = #{orgId}
          AND CAST(event_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>

    <select id="findNewBySymptomAndDate" resultType="com.iflytek.cdc.edr.dto.external.DwdChMedicalRecord">
        select *
        from ads.ads_outpat_medical_records
        where medical_id in
        (select medical_id
        from ads.v_syndrome_medical_symptom_tag s
        where event_datetime <![CDATA[>=]]> #{startDate,jdbcType=TIMESTAMP}
        and event_datetime <![CDATA[<=]]> #{endDate,jdbcType=TIMESTAMP}
        and symptom_tag_list like concat('%',#{symptom,jdbcType=VARCHAR}::varchar,'%'))
    </select>

    <select id="getNewMainSuitData" resultType="com.iflytek.cdc.edr.dto.external.DwdChMedicalRecord">
        SELECT *
        FROM ads.ads_outpat_medical_records
        WHERE org_id = #{orgId}
          AND CAST(event_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>

    <select id="getNewDiagnoseDetail" resultType="com.iflytek.cdc.edr.dto.external.DwdChMedicalDiagnoseResult">
        SELECT *
        FROM dwd.dwd_outpat_diag_detail
        WHERE org_id = #{orgId}
          AND CAST(diagnose_time AS DATE) between #{beginDate}
          and #{endDate}
    </select>

    <select id="getNewPrescription" resultType="com.iflytek.cdc.edr.dto.external.DwdChPrescription">
        SELECT *
        FROM ads.ads_outpat_pre_info
        WHERE prescribe_org_id = #{orgId}
          AND CAST(prescribe_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>

    <select id="getNewMedicalRecord" resultType="com.iflytek.cdc.edr.dto.external.OdsDjyyJkV303EmrBc"></select>

    <select id="getNewDoctorAdviceInformation"
            resultType="com.iflytek.cdc.edr.dto.external.OdsDjyyJkV107HisYz"></select>

    <select id="getNewGeneralSituation" resultType="com.iflytek.cdc.edr.dto.external.OdsDjyyJkV107HisTz"></select>

    <select id="getNewCheckDetail" resultType="com.iflytek.cdc.edr.dto.external.DwdChCheckReport">
        SELECT *
        FROM ads.ads_mt_ris_info
        WHERE org_id = #{orgId}
          AND CAST(exam_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>

    <select id="getNewExamineDetail" resultType="com.iflytek.cdc.edr.dto.external.DwdChInspectionReport">
        SELECT *
        FROM ads.ads_mt_lis_info
        WHERE org_id = #{orgId}
          AND report_time AS DATE) between #{beginDate}
          and #{endDate}
    </select>

    <select id="getNewReportCardRecord" resultType="com.iflytek.cdc.edr.dto.external.DwdChReportCardRecord">
        SELECT *
        FROM ads.v_infection_report
        WHERE org_id = #{orgId}
          AND CAST(create_datetime AS DATE) between #{beginDate}
          and #{endDate}
    </select>

    <select id="getNewInHospitalRecord" resultType="com.iflytek.cdc.edr.dto.external.DwdChInhospitalRecord">
        SELECT *
        FROM ads.ads_inpat_record
        WHERE org_id = #{orgId}
          AND CAST(admiss_time AS DATE) between #{beginDate}
          and #{endDate}
    </select>

    <select id="getLabByRelateTaskId" resultType="com.iflytek.cdc.edr.dto.external.OdsLabSampleTestResult">
        select *
        from ods.ods_lab_sample_test_result
        where relate_task_id = #{relateTaskId}
    </select>

    <select id="getSyndromeMedicalBy" resultType="com.iflytek.cdc.edr.dto.external.MedicalMappingResultDTO">
        select
        t1.medical_id,
        t1.global_person_id,
        t1.patient_id,
        t1.syndrome_code as diseaseCode,
        t3.address_name as livingAddress,
        t3.address_longitude as livingAddressLongitude,
        t3.address_latitude as livingAddressLatitude
        from ads.ads_syndrome_mapping_res t1
        join dim.dim_organization_info o on t1.org_id = o.org_id
        join dim.dim_mr_address_standard t3 on t1.std_living_address_code = t3.address_area_code
        where t1.full_date = #{fullDate} and t1.syndrome_code = #{diseaseCode}
        <if test="orgIds != null and orgIds.size() > 0">
            and o.stat_org_id in
            <foreach item="item" index="index" collection="orgIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getInfectedMedicalBy" resultType="com.iflytek.cdc.edr.dto.external.MedicalMappingResultDTO">
        select
        d.medical_id,
        d.global_person_id,
        d.patient_name,
        d.tag_code as diseaseCode,
        t3.address_name as livingAddress,
        t3.address_longitude as livingAddressLongitude,
        t3.address_latitude as livingAddressLatitude
        from ads.v_infected_suspect_mapping_result d
        join dim.dim_organization_info o on d.org_id = o.org_id
        left join dim.dim_infected_info dii on d.tag_code = dii.infected_sub_code
        join dim.dim_mr_address_standard t3 on d.std_living_address_code = t3.address_area_code
        where d.tag_type = 'INFECTED' and d.full_date = #{fullDate} and d.tag_code = #{diseaseCode}
        <if test="orgIds != null and orgIds.size() > 0">
            and o.stat_org_id in
            <foreach item="item" index="index" collection="orgIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        union
        select
        r.id as medicalId,
        r.global_person_id,
        r.patient_name,
        r.infected_sub_code as diseaseCode,
        t3.address_name as livingAddress,
        t3.address_longitude as livingAddressLongitude,
        t3.address_latitude as livingAddressLatitude
        from ads.v_infection_report r
        join dim.dim_organization_info o on r.org_id = o.org_id
        left join dim.dim_infected_info dii on r.infected_sub_code = dii.infected_sub_code
        join dim.dim_mr_address_standard t3 on r.std_living_address_code = t3.address_area_code
        where CAST(r.fill_date AS DATE) = #{fullDate} and r.infected_sub_code = #{diseaseCode}
        <if test="orgIds != null and orgIds.size() > 0">
            and o.stat_org_id in
            <foreach item="item" index="index" collection="orgIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getSymptomMedicalBy" resultType="com.iflytek.cdc.edr.dto.external.MedicalMappingResultDTO">
        select
        s.medical_id,
        s.patient_id,
        s.patient_name,
        s.global_person_id,
        dd.symptom_code as diseaseCode,
        t3.address_name as livingAddress,
        t3.address_longitude as livingAddressLongitude,
        t3.address_latitude as livingAddressLatitude
        from ads.ads_syndrome_medical_symptom_tag s
        join dim.dim_organization_info o on s.org_id = o.org_id
        join dim.dim_street_info ds on s.std_street_id = ds.street_id and ds.delete_flag != '1'
        join dim.dim_mr_symp_symptom dd on s.symptom_tag = dd.symptom_name and dd.is_deleted != 1
        join dim.dim_mr_address_standard t3 on s.std_living_address_code = t3.address_area_code
        where s.event_datetime AS DATE) = #{fullDate} and dd.symptom_code = #{diseaseCode}
        <if test="orgIds != null and orgIds.size() > 0">
            and o.stat_org_id in
            <foreach item="item" index="index" collection="orgIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getPoisonMedicalBy" resultType="com.iflytek.cdc.edr.dto.external.MedicalMappingResultDTO">
        select
        t1.medical_id,
        t1.patient_name,
        t1.global_person_id,
        t1.tag_code as diseaseCode,
        t3.address_detail_desc as livingAddress,
        t3.address_longitude as livingAddressLongitude,
        t3.address_latitude as livingAddressLatitude
        from ads.v_poison_medical_mapping_result t1
        left join dim.dim_organization_info t2 on t1.org_id = t2.org_id
        left join dim.dim_mr_address_standard t3 on t1.std_living_address_code = t3.address_area_code
        where t1.full_date = #{fullDate} and t1.tag_code = #{poisonCode}
        <if test="orgIds != null and orgIds.size() > 0">
            and t2.stat_org_id in
            <foreach item="item" index="index" collection="orgIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getOutpatientMedicalBy" resultType="com.iflytek.cdc.edr.dto.external.MedicalMappingResultDTO">
        select
        t1.medical_id,
        t1.patient_name,
        t1.global_person_id,
        t3.address_name as livingAddress,
        t3.address_longitude as livingAddressLongitude,
        t3.address_latitude as livingAddressLatitude
        from ads.ads_outpat_medical_records t1
        join dim.dim_organization_info o on t1.org_id = o.org_id
        join dim.dim_mr_address_standard t3 on t1.std_living_address_code = t3.address_area_code
        where CAST(t1.event_datetime AS DATE) = #{fullDate}
        <if test="diseaseCode == '1'.toString()">
            and t1.is_heat = '1'
        </if>
        <if test="diseaseCode == '2'.toString()">
            and t1.is_bowel = '1'
        </if>
        <if test="orgIds != null and orgIds.size() > 0">
            and o.stat_org_id in
            <foreach item="item" index="index" collection="orgIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getUnknownReasonMedicalBy"
            resultType="com.iflytek.cdc.edr.dto.external.MedicalMappingResultDTO">
        select
        d.medical_id,
        d.patient_name,
        d.global_person_id,
        d.diagnose_code as diseaseCode,
        t3.address_name as livingAddress,
        t3.address_longitude as livingAddressLongitude,
        t3.address_latitude as livingAddressLatitude
        from ads.ads_unkown_outpat_medical_records d
        join dim.dim_organization_info o on d.org_id = o.org_id
        join dim.dim_mr_address_standard t3 on d.std_living_address_code = t3.address_area_code
        where CAST(d.event_datetime AS DATE) = #{fullDate} and d.diagnose_code = #{diseaseCode}
        <if test="orgIds != null and orgIds.size() > 0">
            and o.stat_org_id in
            <foreach item="item" index="index" collection="orgIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>