<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.MultiChannelOutpatientMapper">

    <select id="getStatCount" resultType="com.iflytek.cdc.edr.dto.multichannel.TopStatResultDto">
        select
        sum(sd.med_case_cnt) as medCaseCnt,
        sum(sd.transfer_med_cnt) as transferMedCnt,
        sum(sd.nat_person_cnt) as natPersonCnt
        from ads.ads_dept_monitor_stat_d sd
        join dim.dim_organization_info doi
        on sd.org_id = doi.org_id
        where sd.full_date between #{startTime, jdbcType=TIMESTAMP} and #{endTime, jdbcType=TIMESTAMP}
        <if test="isHeat != null and isHeat != ''">
            AND sd.is_heat = #{isHeat}
        </if>
        <if test="isBowel != null and isBowel != ''">
            AND sd.is_bowel >= #{isBowel}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            AND doi.district_code = #{districtCode}
        </if>
        <if test="orgId != null and orgId != ''">
            AND sd.org_id = #{orgId}
        </if>
    </select>

    <select id="getHourStatCount" resultType="com.iflytek.cdc.edr.dto.multichannel.TopStatResultDto">
        select
        sum(sh.med_case_cnt) as medCaseCnt,
        sum(sh.transfer_med_cnt) as transferMedCnt,
        sum(sh.nat_person_cnt) as natPersonCnt
        from ads.ads_dept_monitor_stat_h sh
        join dim.dim_organization_info doi
        on sh.org_id = doi.org_id
        where sh.stat_h between #{startTime, jdbcType=TIMESTAMP} and #{endTime, jdbcType=TIMESTAMP}
        <if test="isHeat != null and isHeat != ''">
            AND sh.is_heat = #{isHeat}
        </if>
        <if test="isBowel != null and isBowel != ''">
            AND sh.is_bowel >= #{isBowel}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            AND doi.district_code = #{districtCode}
        </if>
        <if test="orgId != null and orgId != ''">
            AND sh.org_id = #{orgId}
        </if>
    </select>

    <select id="selectTop10HourDiagDistribution"
            resultType="com.iflytek.cdc.edr.dto.multichannel.DiagDistributionDto">
        select
        std_diagnose_code_component as diagnose_code,
        std_diagnose_name_component as diagnose_name,
        sum(med_case_cnt)           as count
        from ads.ads_dept_monitor_detail_h h
        join dim.dim_organization_info doi on h.org_id = doi.org_id
        where h.stat_h between #{startTime, jdbcType=TIMESTAMP} and #{endTime, jdbcType=TIMESTAMP}
        and std_diagnose_name_component is not null and std_diagnose_name_component != ''
        <if test="isHeat != null and isHeat != ''">
            and h.is_heat = #{isHeat, jdbcType=VARCHAR}
        </if>
        <if test="isBowel != null and isBowel != ''">
            and h.is_bowel = #{isBowel, jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and h.district_code = #{districtCode, jdbcType=VARCHAR}
        </if>
        <if test="orgId != null and orgId != ''">
            and h.org_id = #{orgId, jdbcType=VARCHAR}
        </if>
        group by std_diagnose_code_component, std_diagnose_name_component
        order by 3 desc, max(h.stat_h) desc
        limit 10
    </select>

    <select id="selectTop10DayDiagDistribution"
            resultType="com.iflytek.cdc.edr.dto.multichannel.DiagDistributionDto">
        select
        std_diagnose_code_component as diagnose_code,
        std_diagnose_name_component as diagnose_name,
        sum(med_case_cnt)           as count
        from ads.ads_dept_monitor_detail_d d
        join dim.dim_organization_info doi on d.org_id = doi.org_id
        where d.full_date between #{startTime, jdbcType=TIMESTAMP} and #{endTime, jdbcType=TIMESTAMP}
        and std_diagnose_name_component is not null and std_diagnose_name_component != ''
        <if test="isHeat != null and isHeat != ''">
            and d.is_heat = #{isHeat, jdbcType=VARCHAR}
        </if>
        <if test="isBowel != null and isBowel != ''">
            and d.is_bowel = #{isBowel, jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and d.district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="orgId != null and orgId != ''">
            and d.org_id = #{orgId,jdbcType=VARCHAR}
        </if>
        group by std_diagnose_code_component, std_diagnose_name_component
        order by 3 desc, max(d.full_date) desc
        limit 10
    </select>

    <select id="getMedicalDetailInfo" resultType="com.iflytek.cdc.edr.dto.multichannel.MedicalDetailInfoDto">
        select
        aomr.serial_no as outpatientNo,
        CAST(aomr.visit_time AS DATE) as outpatientTime,
        aomr.patient_name,
        aomr.patient_sex_desc as sexDesc,
        aomr.patient_age as age,
        aomr.patient_age_unit as ageUnit,
        aomr.job as career,
        aomr.doctor_name,
        aomr.std_diagnose_name as diagnoseName,
        CAST(aomr.attk_date_time AS DATE) as attackTime,
        aomr.now_addr as address,
        aomr.org_name as medicalSource,
        aomr.org_id,
        aomr.medical_id
        from ads.ads_outpat_medical_records aomr
        join dim.dim_organization_info doi on aomr.org_id = doi.org_id
        where aomr.visit_time >= #{startTime}::timestamp and aomr.visit_time &lt;= #{endTime}::timestamp
        <if test="isHeat != null and isHeat != ''">
            AND aomr.is_heat = #{isHeat}
        </if>
        <if test="isBowel != null and isBowel != ''">
            AND aomr.is_bowel = #{isBowel}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and doi.district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="orgId != null and orgId != ''">
            and doi.org_id = #{orgId,jdbcType=VARCHAR}
        </if>
        <if test="orgName != null and orgName != ''">
            and aomr.org_name = #{orgName}
        </if>
        <if test="doctorName != null and doctorName != ''">
            and aomr.doctor_name like concat('%', #{doctorName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="sortType == &quot;outpatientTime_asc&quot; ">
            order by aomr.visit_time
        </if>
        <if test="sortType == &quot;outpatientTime_desc&quot; ">
            order by aomr.visit_time desc
        </if>
        <if test="sortType == &quot;age_asc&quot; ">
            ORDER BY
            CASE
            WHEN LENGTH ( aomr.patient_age ) = 0 THEN
            1000000
            WHEN aomr.patient_age_unit LIKE'%岁%' THEN
            aomr.patient_age :: int8 * 365
            WHEN aomr.patient_age_unit LIKE '%月%' THEN
            aomr.patient_age :: int8 * 30
            WHEN aomr.patient_age_unit = '天' THEN
            aomr.patient_age :: int8 ELSE 1000000
            END,
            aomr.visit_time desc
        </if>
        <if test="sortType == &quot;age_desc&quot; ">
            ORDER BY
            CASE
            WHEN LENGTH ( aomr.patient_age ) = 0 THEN
            - 1
            WHEN aomr.patient_age_unit LIKE'%岁%' THEN
            aomr.patient_age :: int8 * 365
            WHEN aomr.patient_age_unit LIKE '%月%' THEN
            aomr.patient_age :: int8 * 30
            WHEN aomr.patient_age_unit = '天' THEN
            aomr.patient_age :: int8 ELSE -1
            END desc,
            aomr.visit_time desc
        </if>
        <if test="sortType == &quot;career_asc&quot; ">
            ORDER BY
            CASE
            WHEN (aomr.job IS NULL or aomr.job = '')THEN
            1 ELSE 0
            END,
            COUNT ( aomr.medical_id ) OVER ( PARTITION BY aomr.job ORDER BY aomr.job ) asc,
            MIN ( aomr.visit_time ) OVER ( PARTITION BY aomr.job ORDER BY aomr.job ) asc,
            aomr.visit_time desc
        </if>
        <if test="sortType == &quot;career_desc&quot; ">
            ORDER BY
            CASE
            WHEN (aomr.job IS NULL or aomr.job = '')THEN
            1 ELSE 0
            END,
            COUNT ( aomr.medical_id ) OVER ( PARTITION BY aomr.job ORDER BY aomr.job ) desc,
            MIN ( aomr.visit_time ) OVER ( PARTITION BY aomr.job ORDER BY aomr.job ) desc,
            aomr.visit_time desc
        </if>
        <if test="sortType == &quot;doctorName_asc&quot; ">
            ORDER BY
            CASE
            WHEN (aomr.doctor_code IS NULL or aomr.doctor_code = '')THEN
            1 ELSE 0
            END,
            COUNT ( aomr.medical_id ) OVER ( PARTITION BY aomr.doctor_code ORDER BY aomr.doctor_code ) asc,
            MIN ( aomr.visit_time ) OVER ( PARTITION BY aomr.doctor_code ORDER BY aomr.doctor_code ) asc,
            aomr.visit_time desc
        </if>
        <if test="sortType == &quot;doctorName_desc&quot; ">
            ORDER BY
            CASE
            WHEN (aomr.doctor_code IS NULL or aomr.doctor_code = '')THEN
            1 ELSE 0
            END,
            COUNT ( aomr.medical_id ) OVER ( PARTITION BY aomr.doctor_code ORDER BY aomr.doctor_code ) desc,
            MIN ( aomr.visit_time ) OVER ( PARTITION BY aomr.doctor_code ORDER BY aomr.doctor_code ) desc,
            aomr.visit_time desc
        </if>
        <if test="sortType == &quot;diagnoseName_asc&quot; ">
            ORDER BY
            CASE
            WHEN (aomr.diagnose_name IS NULL or aomr.diagnose_name = '')THEN
            1 ELSE 0
            END,
            COUNT ( aomr.medical_id ) OVER ( PARTITION BY aomr.diagnose_name ORDER BY aomr.diagnose_name ) asc,
            MIN ( aomr.visit_time ) OVER ( PARTITION BY aomr.diagnose_name ORDER BY aomr.diagnose_name ) asc,
            aomr.visit_time desc
        </if>
        <if test="sortType == &quot;diagnoseName_desc&quot; ">
            ORDER BY
            CASE
            WHEN (aomr.diagnose_name IS NULL or aomr.diagnose_name = '')THEN
            1 ELSE 0
            END,
            COUNT ( aomr.medical_id ) OVER ( PARTITION BY aomr.diagnose_name ORDER BY aomr.diagnose_name ) desc,
            MIN ( aomr.visit_time ) OVER ( PARTITION BY aomr.diagnose_name ORDER BY aomr.diagnose_name ) desc,
            aomr.visit_time desc
        </if>
        <if test="sortType == &quot;attackTime_asc&quot; ">
            order by aomr.attk_date_time asc nulls last
        </if>
        <if test="sortType == &quot;attackTime_desc&quot; ">
            order by aomr.attk_date_time desc nulls last
        </if>
        <if test="sortType == &quot;address_asc&quot; ">
            ORDER BY
            CASE
            WHEN (aomr.now_addr IS NULL or aomr.now_addr = '')THEN
            1 ELSE 0
            END,
            COUNT ( aomr.medical_id ) OVER ( PARTITION BY aomr.now_addr ORDER BY aomr.now_addr ) asc,
            MIN ( aomr.visit_time ) OVER ( PARTITION BY aomr.now_addr ORDER BY aomr.now_addr ) asc,
            aomr.visit_time desc
        </if>
        <if test="sortType == &quot;address_desc&quot; ">
            ORDER BY
            CASE
            WHEN (aomr.now_addr IS NULL or aomr.now_addr = '')THEN
            1 ELSE 0
            END,
            COUNT ( aomr.medical_id ) OVER ( PARTITION BY aomr.now_addr ORDER BY aomr.now_addr ) desc,
            MIN ( aomr.visit_time ) OVER ( PARTITION BY aomr.now_addr ORDER BY aomr.now_addr ) desc,
            aomr.visit_time desc
        </if>
        <if test="sortType == &quot;medicalSource_asc&quot; ">
            ORDER BY
            CASE
            WHEN (aomr.org_name IS NULL or aomr.org_name = '')THEN
            1 ELSE 0
            END,
            COUNT ( aomr.medical_id ) OVER ( PARTITION BY aomr.org_name ORDER BY aomr.org_name ) asc,
            MAX ( aomr.visit_time ) OVER ( PARTITION BY aomr.org_name ORDER BY aomr.org_name ) asc,
            aomr.visit_time desc
        </if>
        <if test="sortType == &quot;medicalSource_desc&quot; ">
            ORDER BY
            CASE
            WHEN (aomr.org_name IS NULL or aomr.org_name = '')THEN
            1 ELSE 0
            END,
            COUNT ( aomr.medical_id ) OVER ( PARTITION BY aomr.org_name ORDER BY aomr.org_name ) desc,
            MAX ( aomr.visit_time ) OVER ( PARTITION BY aomr.org_name ORDER BY aomr.org_name ) desc,
            aomr.visit_time desc
        </if>
    </select>

    <select id="getHourDetailStatCount" resultType="com.iflytek.cdc.edr.dto.multichannel.DataTrendDto">
        select
        to_char(h.stat_h, 'yyyy-MM-dd HH:00:00') as dateLine,
        sum(h.med_case_cnt) as personCount,
        sum(h.transfer_med_cnt) as transferMedCount
        from ads.ads_dept_monitor_stat_h h
        join dim.dim_organization_info doi
        on h.org_id = doi.org_id
        where h.stat_h between #{startTime, jdbcType=TIMESTAMP} and #{endTime, jdbcType=TIMESTAMP}
        <if test="isHeat != null and isHeat != ''">
            AND h.is_heat = #{isHeat}
        </if>
        <if test="isBowel != null and isBowel != ''">
            AND h.is_bowel = #{isBowel}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and doi.district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="orgId != null and orgId != ''">
            and doi.org_id = #{orgId,jdbcType=VARCHAR}
        </if>
        group by to_char(h.stat_h, 'yyyy-MM-dd HH:00:00')
        order by to_char(h.stat_h, 'yyyy-MM-dd HH:00:00')
    </select>

    <select id="getDayDetailStatCount" resultType="com.iflytek.cdc.edr.dto.multichannel.DataTrendDto">
        select
        CAST(d.full_date AS DATE) as dateLine,
        sum(d.med_case_cnt)           as personCount,
        sum(d.transfer_med_cnt) as transferMedCount
        from ads.ads_dept_monitor_stat_d d
        join dim.dim_organization_info doi
        on d.org_id = doi.org_id
        where d.full_date between #{startTime, jdbcType=TIMESTAMP} and #{endTime, jdbcType=TIMESTAMP}
        <if test="isHeat != null and isHeat != ''">
            AND d.is_heat = #{isHeat}
        </if>
        <if test="isBowel != null and isBowel != ''">
            AND d.is_bowel = #{isBowel}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and doi.district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="orgId != null and orgId != ''">
            and doi.org_id = #{orgId,jdbcType=VARCHAR}
        </if>
        group by CAST(d.full_date AS DATE)
        order by CAST(d.full_date AS DATE)
    </select>

    <select id="getMonthDetailStatCount" resultType="com.iflytek.cdc.edr.dto.multichannel.DataTrendDto">
        select
        CAST(d.full_date AS DATE) as dateLine,
        sum(d.med_case_cnt)           as personCount,
        sum(d.transfer_med_cnt) as transferMedCount
        from ads.ads_dept_monitor_stat_d d
        join dim.dim_organization_info doi
        on d.org_id = doi.org_id
        where d.full_date between #{startTime, jdbcType=TIMESTAMP} and #{endTime, jdbcType=TIMESTAMP}
        <if test="isHeat != null and isHeat != ''">
            AND d.is_heat = #{isHeat}
        </if>
        <if test="isBowel != null and isBowel != ''">
            AND d.is_bowel = #{isBowel}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and doi.district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="orgId != null and orgId != ''">
            and doi.org_id = #{orgId,jdbcType=VARCHAR}
        </if>
        group by to_char(d.full_date, 'yyyy-MM')
        order by to_char(d.full_date, 'yyyy-MM')
    </select>

    <select id="getYearDetailStatCount" resultType="com.iflytek.cdc.edr.dto.multichannel.DataTrendDto">
        select
        to_char(d.full_date, 'yyyy') as dateLine,
        sum(d.med_case_cnt)           as personCount,
        sum(d.transfer_med_cnt) as transferMedCount
        from ads.ads_dept_monitor_stat_d d
        join dim.dim_organization_info doi
        on d.org_id = doi.org_id
        where d.full_date between #{startTime, jdbcType=TIMESTAMP} and #{endTime, jdbcType=TIMESTAMP}
        <if test="isHeat != null and isHeat != ''">
            AND d.is_heat = #{isHeat}
        </if>
        <if test="isBowel != null and isBowel != ''">
            AND d.is_bowel = #{isBowel}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and doi.district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="orgId != null and orgId != ''">
            and doi.org_id = #{orgId,jdbcType=VARCHAR}
        </if>
        group by to_char(d.full_date, 'yyyy')
        order by to_char(d.full_date, 'yyyy')
    </select>

    <select id="getMedicalSource" resultType="java.lang.String">
        select
        distinct aomr.org_name
        from ads.ads_outpat_medical_records aomr
        join dim.dim_organization_info doi on aomr.org_id = doi.org_id
        where aomr.visit_time >= #{startTime}::timestamp and aomr.visit_time &lt;= #{endTime}::timestamp
        <if test="isHeat != null and isHeat != ''">
            AND aomr.is_heat = #{isHeat}
        </if>
        <if test="isBowel != null and isBowel != ''">
            AND aomr.is_bowel = #{isBowel}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and doi.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and doi.district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="orgId != null and orgId != ''">
            and doi.org_id = #{orgId,jdbcType=VARCHAR}
        </if>
        <if test="doctorName != null and doctorName != ''">
            and aomr.doctor_name like concat('%', #{doctorName,jdbcType=VARCHAR}, '%')
        </if>
    </select>

    <select id="getOrgCnt" resultType="com.iflytek.cdc.edr.vo.OutpatientOrgCntVO">
        select org_cnt_heat, org_cnt_bowel
        from ads.ads_dept_monitor_stat_d
        order by full_date desc
        limit 1;
    </select>
</mapper>