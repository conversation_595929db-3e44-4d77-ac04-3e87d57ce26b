<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.InHospitalDiagnoseMapper">

    <resultMap type="com.iflytek.cdc.edr.entity.inhospital.InHospitalDiagnose" id="InHospitalDiagnoseResult">
        <result property="id"    column="id"    />
        <result property="standerOrgCode"    column="stander_org_code"    />
        <result property="typeName"    column="type_name"    />
        <result property="inHospitalId"    column="inhospital_id"    />
        <result property="primaryFlag"    column="primary_flag"    />
        <result property="diagnoseDate"    column="diagnose_date"    />
        <result property="diagnoseCode"    column="diagnose_code"    />
        <result property="diagnoseName"    column="diagnose_name"    />
        <result property="deptName"    column="dept_name"    />
        <result property="doctorName"    column="doctor_name"    />
        <result property="zdmemo"    column="zdmemo"    />
    </resultMap>

    <sql id="selectInHospitalDiagnoseVo">
        select id,
               stander_org_code,
               type_name,
               inhospital_id,
               primary_flag,
               diagnose_date,
               diagnose_code,
               diagnose_name,
               dept_name,
               doctor_name,
               zdmemo
        from dwd.dwd_ch_inhospital_diagnose
    </sql>

    <sql id="selectNewInHospitalDiagnoseVo">
        select id,
               org_name as stander_org_code,
               diagnose_type_name as type_name,
               inhospital_no as inhospital_id,
               main_diagnose_flag as primary_flag,
               diagnose_time as diagnose_date,
               diagnose_code,
               diagnose_name,
               diagnose_dept_name as dept_name,
               diagnose_doc_name as doctor_name,
               diagnosis_result_ds as zdmemo
        from ads.ads_inpat_diag_detail
    </sql>

    <select id="selectInHospitalDiagnoseList" parameterType="String" resultMap="InHospitalDiagnoseResult">
        <include refid="selectInHospitalDiagnoseVo"/>
       where
              stander_org_code = #{standerOrgCode}
              and inhospital_id =#{inHospitalId}
       order by diagnose_date
    </select>

    <select id="selectInHospitalDiagnoseById" parameterType="String" resultMap="InHospitalDiagnoseResult">
        <include refid="selectInHospitalDiagnoseVo"/>
        where id = #{id}
    </select>

    <select id="selectNewInHospitalDiagnoseList" resultType="com.iflytek.cdc.edr.entity.inhospital.InHospitalDiagnose">
        <include refid="selectNewInHospitalDiagnoseVo"/>
        where
        org_name = #{standerOrgCode}
        and medical_id = #{medicalId}
        order by diagnose_time
    </select>


</mapper>
