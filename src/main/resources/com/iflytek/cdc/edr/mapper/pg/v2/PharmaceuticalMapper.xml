<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace= "com.iflytek.cdc.edr.mapper.pg.v2.PharmaceuticalMapper">

    <select id="getOrgCount" resultType="java.lang.Integer">
        SELECT COUNT(0)
        FROM dw."dim_organization_info" o
        WHERE delete_flag = '0'
        and source_type in ('110','120')
        and exists (select 1 from ads.v_ads_med_drug_class_stat s
        where o.org_id = s.org_id and s.full_date <![CDATA[<=]]> #{endDate})
        <if test="sourceType != null and sourceType != ''">
           and source_type = #{sourceType,jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="orgId != null and orgId != ''">
            and org_id = #{orgId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="getDrugCount" resultType="java.lang.Integer">
        SELECT SUM( COALESCE (drug_sales_cnt) )
        FROM
        ads.v_ads_med_drug_class_stat t1
        LEFT JOIN dim.dim_organization_info t2 ON t1.org_id = t2.org_id
        WHERE
        t1.full_date BETWEEN #{startTime}
        AND #{endTime}
        <if test="sourceType != null and sourceType != ''">
            and t1.source_type = #{sourceType,jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and t2.city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and t2.district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="orgId != null and orgId != ''">
            and t1.org_id = #{orgId,jdbcType=VARCHAR}
        </if>

    </select>
    <select id="getAlertCount" resultType="java.lang.Integer">

    </select>
    <select id="getStatisticsTrend" resultType="com.iflytek.cdc.edr.vo.PharmaceuticalSaleVolumeByDataVO">
        SELECT
        sc.day_short_desc as dateTime,
        SUM ( COALESCE ( t1.drug_sales_cnt, 0 ) ) AS pharmaceuticalSaleVolume,
        SUM ( COALESCE ( t1.med_case_cnt, 0 ) ) AS patientCount
        FROM
        dim.dim_date sc
        LEFT JOIN ads.v_ads_med_drug_class_stat  t1 ON sc.day_short_desc = t1.full_date
        <if test="sourceType != null and sourceType != ''">
            and t1.source_type = #{sourceType,jdbcType=VARCHAR}
        </if>
        <if test="orgId != null and orgId != ''">
            and t1.org_id = #{orgId,jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and t1.city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and t1.district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="classCode != null and classCode != ''">
            and t1.drug_class_code = #{classCode,jdbcType=VARCHAR}
        </if>
        WHERE
        sc.day_short_desc BETWEEN #{startDate}
        AND #{endDate}
        GROUP BY sc.day_short_desc
        ORDER BY
        sc.day_short_desc;
    </select>
    <select id="getPharmaceuticalClassDict" resultType="com.iflytek.cdc.edr.dto.DictDto">
        select * from dim.dim_dict where dict_code = '404354' order by  dict_value_code
    </select>
    <select id="getPatientList" resultType="com.iflytek.cdc.edr.vo.PharmaceuticalPatientVO">
        SELECT
        medical_id,
        event_id,
        source_type,
        patient_name,
        concat(patient_age, patient_age_unit) as patientAge,
        patient_sex_desc,
        serial_no,
        visit_type_name,
        visit_type_code,
        dept_name,
        doc_name
        FROM
        ads.v_ads_med_drug_class_mapping_result
        WHERE
        CAST(visit_time AS DATE) BETWEEN #{startDate}
        AND #{endDate}
        <if test="sourceType != null and sourceType != ''">
            and source_type = #{sourceType,jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="orgId != null and orgId != ''">
            and org_id = #{orgId,jdbcType=VARCHAR}
        </if>
        <if test="classCode != null and classCode != ''">
            and drug_class_code = #{classCode,jdbcType=VARCHAR}
        </if>
        GROUP BY
        medical_id,
        event_id,
        source_type,
        patient_name,
        concat(patient_age, patient_age_unit) ,
        patient_sex_desc,
        serial_no,
        visit_type_name,
        visit_type_code,
        dept_name,
        doc_name
    </select>
    <select id="getStatisticsListByClassName"
            resultType="com.iflytek.cdc.edr.vo.PharmaceuticalSaleVolumeClassTypeVO">
        SELECT
        dd.dict_value_name AS pharmaceuticalTypeName,
        dd.dict_value_code AS pharmaceuticalTypeCode,
        sc.day_short_desc AS fullDate,
        SUM ( COALESCE ( t1.drug_sales_cnt, 0 ) ) AS pharmaceuticalSaleVolume,
        SUM ( COALESCE ( t1.med_case_cnt, 0 ) ) AS patientCount
        FROM
        dim.dim_date sc
        LEFT JOIN dim.dim_dict dd ON dd.dict_code = '404354'
        LEFT JOIN ads.v_ads_med_drug_class_stat  t1 ON sc.day_short_desc = t1.full_date  and dd.dict_value_code = t1.drug_class_code
        <if test="sourceType != null and sourceType != ''">
            and t1.source_type = #{sourceType,jdbcType=VARCHAR}
        </if>
        <if test="orgId != null and orgId != ''">
            and t1.org_id = #{orgId,jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and t1.city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and t1.district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        WHERE
        sc.day_short_desc BETWEEN #{startDate}
        AND #{endDate}
        GROUP BY
        dd.dict_value_code,
        dd.dict_value_name,
        sc.day_short_desc
        ORDER BY
        sc.day_short_desc,
        dd.dict_value_code;

    </select>


    <select id="getCountByParam" resultType="com.iflytek.cdc.edr.vo.PharmaceuticalSaleVolumeClassTypeVO">
        SELECT dd.dict_value_name                     AS pharmaceuticalTypeName,
               dd.dict_value_code                     AS pharmaceuticalTypeCode,
               dmdcs.full_date                        AS fullDate,
               SUM(COALESCE(dmdcs.drug_sales_cnt, 0)) AS pharmaceuticalSaleVolume,
               SUM(COALESCE(dmdcs.med_case_cnt, 0))   AS patientCount
        FROM ads.v_ads_med_drug_class_stat dmdcs
                 join dim.dim_dict dd ON dd.dict_code = '404354' and dd.dict_value_code = dmdcs.drug_class_code
                 JOIN dim.dim_organization_info doi ON dmdcs.org_id = doi.org_id
        WHERE dmdcs.full_date BETWEEN #{startDate} AND #{endDate}
        <if test="sourceType != null and sourceType != ''">
            and dmdcs.source_type = #{sourceType,jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and doi.city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and doi.district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="orgId != null and orgId != ''">
            and dmdcs.org_id = #{orgId,jdbcType=VARCHAR}
        </if>
        GROUP BY dmdcs.full_date, dd.dict_value_code, dd.dict_value_name
        order by dd.dict_value_code
    </select>

</mapper>