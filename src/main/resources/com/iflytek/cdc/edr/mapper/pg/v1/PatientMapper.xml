<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v1.PatientMapper">

    <select id="getPatientInfoByPerson" resultType="com.iflytek.cdc.edr.entity.person.DimMdmPersonInfo">
        SELECT dmpi.global_person_id,
               dmpi.patient_id_number,
               dmpi.identity_type_name,
               dmpi.patient_name,
               dmpi.sex_name,
               CASE
                   WHEN EXTRACT(YEAR FROM AGE(dmpi.birthday)) >= 1 THEN
                       concat(EXTRACT(YEAR FROM AGE(dmpi.birthday)), '岁')
                   WHEN EXTRACT(MONTH FROM AGE(dmpi.birthday)) >= 1 THEN
                       concat(EXTRACT(MONTH FROM AGE(dmpi.birthday)), '月')
                   ELSE concat(EXTRACT(DAY FROM AGE(dmpi.birthday)), '天')
                   END AS age,
               dmpi.marital_code,
               dmpi.marital_name,
               dmpi.telephone,
               dmpi.household_addr_detail,
               dmpi.living_address,
               dmpi.company_name,
               dmpi.contact_name,
               dmpi.contact_telephone,
               dmpi.std_living_place_name,
               TO_CHAR(dmpi.create_datetime, 'YYYY-MM-DD HH24:MI:SS') AS create_datetime,
               TO_CHAR(dmpi.event_update_datetime, 'YYYY-MM-DD HH24:MI:SS') AS update_datetime
          FROM dim.dim_mdm_person_info dmpi
         WHERE dmpi.event_update_datetime IS NOT NULL
        <foreach collection="areaList" index="index" item="area" open=" AND (" separator=" OR " close=")">
            <if test='area.regionType == "1"'>
                dmpi.std_living_city_code = '340100'
            </if>
            <if test="area.cityCode != null">
                dmpi.std_living_city_code = #{area.cityCode}
            </if>
            <if test="area.districtCode != null">
                AND dmpi.std_living_place_code = #{area.districtCode}
            </if>
        </foreach>
        <if test="personInfo != null">
            AND composite_key LIKE concat('%', #{personInfo}, '%')
        </if>
        <if test="startTime != null">
            AND dmpi.create_datetime >= #{startTime}::timestamp
        </if>
        <if test="endTime != null">
            AND dmpi.create_datetime &lt;= #{endTime}::timestamp
        </if>
         ORDER BY update_datetime DESC
         LIMIT ${pageSize} OFFSET (${pageIndex} - 1) * ${pageSize}
    </select>

    <select id="getPatientInfoByEvent" resultType="com.iflytek.cdc.edr.entity.person.DimMdmPersonInfo">-->
        SELECT global_person_id,
               patient_id_number,
               identity_type_name,
               patient_name,
               sex_name,
               CASE WHEN EXTRACT(YEAR FROM AGE(birthday)) >= 1 THEN concat(EXTRACT(YEAR FROM AGE(birthday)), '岁')
                    WHEN EXTRACT(MONTH FROM AGE(birthday)) >= 1 THEN concat(EXTRACT(MONTH FROM AGE(birthday)), '月')
                    ELSE concat(EXTRACT(DAY FROM AGE(birthday)), '天')
                   END AS age,
               marital_code,
               marital_name,
               telephone,
               household_addr_detail,
               living_address,
               company_name,
               contact_name,
               contact_telephone,
               std_living_place_name,
               TO_CHAR(patient_create_datetime, 'YYYY-MM-DD HH24:MI:SS') AS create_datetime,
               TO_CHAR(MAX(event_datetime), 'YYYY-MM-DD HH24:MI:SS') AS update_datetime,
               symptom_tag_list
          FROM ads.ads_patient_event_record
        <where>
            <foreach collection="areaList" index="index" item="area" open="(" separator=" OR " close=")">
                <if test='area.cityCode != null'>
                    std_living_city_code = #{area.cityCode}
                </if>
                <if test="area.districtCode != null">
                    AND std_living_place_code = #{area.districtCode}
                </if>
            </foreach>
            <if test="personInfo != null">
                AND composite_key LIKE concat('%', #{personInfo}, '%')
            </if>
            <if test="startTime!= null">
                AND event_datetime >= #{startTime}::TIMESTAMP
            </if>
            <if test="endTime!= null">
                AND event_datetime &lt;= #{endTime}::TIMESTAMP
            </if>
            <if test="symptomList != null and symptomList.size() > 0">
                and
                <foreach collection="symptomList" item="item" index="index" open="(" close=")" separator="and">
                    symptom_tag_list LIKE concat('%', #{item}::text, '%')
                </foreach>
            </if>
            <if test="infectionName != null">
                AND primary_diagnose_name LIKE concat('%', #{infectionName}, '%')
            </if>
        </where>
         GROUP BY global_person_id,
                  patient_id_number,
                  identity_type_name,
                  patient_name,
                  sex_name,
                  birthday,
                  marital_code,
                  marital_name,
                  telephone,
                  household_addr_detail,
                  living_address,
                  company_name,
                  contact_name,
                  contact_telephone,
                  std_living_place_name,
                  patient_create_datetime,
                  symptom_tag_list
         ORDER BY max(event_datetime) DESC
         LIMIT ${pageSize} OFFSET (${pageIndex} - 1) * ${pageSize}
     </select>

    <select id="getPersonFromMedical" resultType="com.iflytek.cdc.edr.dto.person.OrgPersonDto">
        SELECT dmpi.global_person_id,
               dmpi.patient_id_number,
               dmpi.identity_type_name,
               dmpi.patient_name,
               dmpi.sex_name,
               CASE
                   WHEN EXTRACT(YEAR FROM AGE(dmpi.birthday)) >= 1 THEN
                       concat(EXTRACT(YEAR FROM AGE(dmpi.birthday)), '岁')
                   WHEN EXTRACT(MONTH FROM AGE(dmpi.birthday)) >= 1 THEN
                       concat(EXTRACT(MONTH FROM AGE(dmpi.birthday)), '月')
                   ELSE concat(EXTRACT(DAY FROM AGE(dmpi.birthday)), '天') END AS age,
               dmpi.marital_code,
               dmpi.marital_name,
               dmpi.telephone,
               dmpi.household_addr_detail,
               dmpi.living_address,
               dmpi.company_name,
               dmpi.contact_name,
               dmpi.contact_telephone,
               dmpi.std_living_place_name,
               #{orgName} AS visit_org_name,
               TO_CHAR(MAX(dcmr.event_datetime), 'yyyy/mm/dd') AS visit_datetime
          FROM dwd.dwd_ch_medical_record dcmr
              INNER JOIN dim.dim_mdm_person_info dmpi ON dcmr.global_person_id = dmpi.global_person_id
         WHERE dcmr.org_id = #{orgId}
           AND dcmr.event_datetime >= #{startTime}::timestamp
           AND dcmr.event_datetime &lt;= #{endTime}::timestamp
         GROUP BY dmpi.global_person_id,
                  dmpi.patient_id_number,
                  dmpi.patient_name,
                  dmpi.sex_name,
                  dmpi.telephone,
                  dmpi.company_name,
                  dmpi.household_addr_detail,
                  dmpi.living_address,
                  dmpi.std_living_place_name
         ORDER BY MAX(dcmr.event_datetime) desc
    </select>

    <select id="getPersonFromPharmacy" resultType="com.iflytek.cdc.edr.dto.person.OrgPersonDto">
        SELECT dmpi.global_person_id,
               dmpi.patient_id_number,
               dmpi.identity_type_name,
               dmpi.patient_name,
               dmpi.sex_name,
               CASE
                   WHEN EXTRACT(YEAR FROM AGE(dmpi.birthday)) >= 1 THEN
                           concat(EXTRACT(YEAR FROM AGE(dmpi.birthday)), '岁')
                   WHEN EXTRACT(MONTH FROM AGE(dmpi.birthday)) >= 1 THEN
                           concat(EXTRACT(MONTH FROM AGE(dmpi.birthday)), '月')
                   ELSE concat(EXTRACT(DAY FROM AGE(dmpi.birthday)), '天')
                   END AS age,
               dmpi.marital_code,
               dmpi.marital_name,
               dmpi.telephone,
               dmpi.household_addr_detail,
               dmpi.living_address,
               dmpi.company_name,
               dmpi.contact_name,
               dmpi.contact_telephone,
               dmpi.std_living_place_name,
               #{orgName} AS visit_org_name,
               TO_CHAR(MAX(ddpi.update_datetime), 'yyyy/mm/dd') AS visit_datetime
          FROM dwd.dwd_ds_purchaser_info ddpi
              INNER JOIN dim.dim_mdm_person_info dmpi ON ddpi.global_person_id = dmpi.global_person_id
         WHERE ddpi.pharmacy_id = #{orgId}
           AND ddpi.update_datetime >= #{startTime}::TIMESTAMP
           AND ddpi.update_datetime &lt;= #{endTime}::TIMESTAMP
         GROUP BY dmpi.global_person_id,
                  dmpi.patient_id_number,
                  dmpi.patient_name,
                  dmpi.sex_name,
                  dmpi.telephone,
                  dmpi.company_name,
                  dmpi.household_addr_detail,
                  dmpi.living_address,
                  dmpi.std_living_place_name
         ORDER BY MAX(ddpi.update_datetime) desc
    </select>

    <select id="getPersonByOrg" resultType="com.iflytek.cdc.edr.dto.person.OrgPersonDto">
        SELECT dmpi.global_person_id,
               dmpi.patient_id_number,
               dmpi.identity_type_name,
               dmpi.patient_name,
               dmpi.sex_name,
               CASE
                   WHEN EXTRACT(YEAR FROM AGE(dmpi.birthday)) >= 1 THEN
                       concat(EXTRACT(YEAR FROM AGE(dmpi.birthday)), '岁')
                   WHEN EXTRACT(MONTH FROM AGE(dmpi.birthday)) >= 1 THEN
                       concat(EXTRACT(MONTH FROM AGE(dmpi.birthday)), '月')
                   ELSE concat(EXTRACT(DAY FROM AGE(dmpi.birthday)), '天')
                   END AS age,
               dmpi.marital_code,
               dmpi.marital_name,
               dmpi.telephone,
               dmpi.household_addr_detail,
               dmpi.living_address,
               dmpi.company_name,
               dmpi.contact_name,
               dmpi.contact_telephone,
               dmpi.std_living_place_name,
               #{orgName} AS visit_org_name,
               TO_CHAR(MAX(der.event_datetime), 'yyyy/mm/dd') AS visit_datetime
          FROM dwd.dwd_event_record der
          INNER JOIN dim.dim_mdm_person_info dmpi ON der.global_person_id = dmpi.global_person_id
         WHERE der.org_id = #{orgId}
           AND der.event_datetime >= #{startTime}::timestamp
           AND der.event_datetime &lt;= #{endTime}::timestamp
        <if test="patientSourceCode != null and patientSourceCode != ''">
           AND der.data_src = #{patientSourceCode}
        </if>
         GROUP BY dmpi.global_person_id,
                  dmpi.patient_id_number,
                  dmpi.patient_name,
                  dmpi.sex_name,
                  dmpi.telephone,
                  dmpi.company_name,
                  dmpi.household_addr_detail,
                  dmpi.living_address,
                  dmpi.std_living_place_name
         ORDER BY MAX(der.event_datetime) DESC
    </select>
    <select id="getPatientInfoByGlobalPersonId"
            resultType="com.iflytek.cdc.edr.entity.person.DimMdmPersonInfo">
        SELECT dmpi.global_person_id,
               dmpi.patient_id_number,
               dmpi.identity_type_name,
               dmpi.patient_name,
               dmpi.sex_name,
               CASE
                   WHEN EXTRACT(YEAR FROM AGE(dmpi.birthday)) >= 1 THEN
                       concat(EXTRACT(YEAR FROM AGE(dmpi.birthday)), '岁')
                   WHEN EXTRACT(MONTH FROM AGE(dmpi.birthday)) >= 1 THEN
                       concat(EXTRACT(MONTH FROM AGE(dmpi.birthday)), '月')
                   ELSE concat(EXTRACT(DAY FROM AGE(dmpi.birthday)), '天')
                   END AS age,
               dmpi.marital_code,
               dmpi.marital_name,
               dmpi.telephone,
               dmpi.household_addr_detail,
               dmpi.living_address,
               dmpi.company_name,
               dmpi.contact_name,
               dmpi.contact_telephone,
               dmpi.std_living_place_name,
               TO_CHAR(dmpi.create_datetime, 'YYYY-MM-DD HH24:MI:SS') AS create_datetime,
               TO_CHAR(dmpi.event_update_datetime, 'YYYY-MM-DD HH24:MI:SS') AS update_datetime
        FROM dim.dim_mdm_person_info dmpi
        where global_person_id = #{globalPersonId} limit 1
    </select>

    <select id="getPatientInfosByGlobalPersonIds" resultType="com.iflytek.cdc.edr.entity.person.DimMdmPersonInfo">
        SELECT dmpi.global_person_id,
               dmpi.patient_id_number,
               dmpi.identity_type_name,
               dmpi.patient_name,
               dmpi.sex_name,
               CASE
                   WHEN EXTRACT(YEAR FROM AGE(dmpi.birthday)) >= 1 THEN
                        concat(EXTRACT(YEAR FROM AGE(dmpi.birthday)), '岁')
                   WHEN EXTRACT(MONTH FROM AGE(dmpi.birthday)) >= 1 THEN
                        concat(EXTRACT(MONTH FROM AGE(dmpi.birthday)), '月')
                   ELSE concat(EXTRACT(DAY FROM AGE(dmpi.birthday)), '天')
                   END AS age,
               dmpi.marital_code,
               dmpi.marital_name,
               dmpi.telephone,
               dmpi.household_addr_detail,
               dmpi.living_address,
               dmpi.company_name,
               dmpi.contact_name,
               dmpi.contact_telephone,
               dmpi.std_living_place_name,
        TO_CHAR(dmpi.create_datetime, 'YYYY-MM-DD HH24:MI:SS') AS create_datetime,
        TO_CHAR(dmpi.event_update_datetime, 'YYYY-MM-DD HH24:MI:SS') AS update_datetime
        FROM dim.dim_mdm_person_info dmpi
        <where>
            <choose>
                <when test="list != null and list.size() != 0">
                    global_person_id in
                    <foreach collection="list" open="(" item="item" separator="," close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </when>
                <otherwise>
                    false
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="getPersonLastEventByOrg" resultType="com.iflytek.cdc.edr.dto.person.OrgPersonDto">
        SELECT aper.global_person_id,
               aper.patient_id_number,
               aper.identity_type_name,
               aper.patient_name,
               aper.sex_name,
               aper.symptom_tag_list as symptomTagList,
               CASE
                   WHEN EXTRACT(YEAR FROM AGE(aper.birthday)) >= 1 THEN
                       concat(EXTRACT(YEAR FROM AGE(aper.birthday)), '岁')
                   WHEN EXTRACT(MONTH FROM AGE(aper.birthday)) >= 1 THEN
                       concat(EXTRACT(MONTH FROM AGE(aper.birthday)), '月')
                   ELSE concat(EXTRACT(DAY FROM AGE(aper.birthday)), '天')
                   END                                         AS age,
               aper.marital_code,
               aper.marital_name,
               aper.telephone,
               aper.household_addr_detail,
               aper.living_address,
               aper.company_name,
               aper.contact_name,
               aper.contact_telephone,
               aper.std_living_place_name,
               #{orgName}                                      AS visit_org_name,
               TO_CHAR(MAX(aper.event_datetime), 'yyyy/mm/dd') AS visit_datetime
        FROM ads.ads_patient_event_record aper
        WHERE aper.org_id = #{orgId}
          AND aper.event_datetime >= #{startTime}::timestamp
        AND aper.event_datetime &lt;= #{endTime}:: timestamp
            <if test="patientSourceCode != null and patientSourceCode != ''">
                AND aper.data_src = #{patientSourceCode}
            </if>
        GROUP BY aper.global_person_id,
            aper.patient_id_number,
            aper.identity_type_name,
            aper.patient_name,
            aper.sex_name,
            aper.symptom_tag_list,
            aper.birthday,
            aper.marital_code,
            aper.marital_name,
            aper.telephone,
            aper.household_addr_detail,
            aper.living_address,
            aper.company_name,
            aper.contact_name,
            aper.contact_telephone,
            aper.std_living_place_name
        ORDER BY MAX (aper.event_datetime) DESC
    </select>
</mapper>