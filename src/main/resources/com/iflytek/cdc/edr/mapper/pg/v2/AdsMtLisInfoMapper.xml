<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.AdsMtLisInfoMapper">
  <resultMap id="BaseResultMap" type="com.iflytek.cdc.edr.entity.AdsMtLisInfo">
    <!--@mbg.generated-->
    <!--@Table ads.ads_mt_lis_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="lab_test_report_no" jdbcType="VARCHAR" property="labTestReportNo" />
    <result column="lab_test_report_title" jdbcType="VARCHAR" property="labTestReportTitle" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="apply_org_id" jdbcType="VARCHAR" property="applyOrgId" />
    <result column="apply_org_name" jdbcType="VARCHAR" property="applyOrgName" />
    <result column="specimen_collect_time" jdbcType="TIMESTAMP" property="specimenCollectTime" />
    <result column="specimen_part_code" jdbcType="VARCHAR" property="specimenPartCode" />
    <result column="specimen_part_name" jdbcType="VARCHAR" property="specimenPartName" />
    <result column="specimen_type_code" jdbcType="VARCHAR" property="specimenTypeCode" />
    <result column="specimen_type_name" jdbcType="VARCHAR" property="specimenTypeName" />
    <result column="specimen_code" jdbcType="VARCHAR" property="specimenCode" />
    <result column="specimen_name" jdbcType="VARCHAR" property="specimenName" />
    <result column="sample_no" jdbcType="VARCHAR" property="sampleNo" />
    <result column="barcode_no" jdbcType="VARCHAR" property="barcodeNo" />
    <result column="exam_org_id" jdbcType="VARCHAR" property="examOrgId" />
    <result column="exam_org_name" jdbcType="VARCHAR" property="examOrgName" />
    <result column="exec_dept_code" jdbcType="VARCHAR" property="execDeptCode" />
    <result column="exec_dept_name" jdbcType="VARCHAR" property="execDeptName" />
    <result column="exam_type_code" jdbcType="VARCHAR" property="examTypeCode" />
    <result column="exam_type_name" jdbcType="VARCHAR" property="examTypeName" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="lab_item_class_code" jdbcType="VARCHAR" property="labItemClassCode" />
    <result column="lab_item_class_name" jdbcType="VARCHAR" property="labItemClassName" />
    <result column="test_detail_code" jdbcType="VARCHAR" property="testDetailCode" />
    <result column="test_detail_desc" jdbcType="VARCHAR" property="testDetailDesc" />
    <result column="test_result_code" jdbcType="VARCHAR" property="testResultCode" />
    <result column="item_result_name" jdbcType="VARCHAR" property="itemResultName" />
    <result column="item_unit" jdbcType="VARCHAR" property="itemUnit" />
    <result column="item_res_flag" jdbcType="VARCHAR" property="itemResFlag" />
    <result column="reference_value" jdbcType="VARCHAR" property="referenceValue" />
    <result column="reference_value_min" jdbcType="VARCHAR" property="referenceValueMin" />
    <result column="reference_value_max" jdbcType="VARCHAR" property="referenceValueMax" />
    <result column="item_res_num" jdbcType="VARCHAR" property="itemResNum" />
    <result column="item_res_num_unit" jdbcType="VARCHAR" property="itemResNumUnit" />
    <result column="item_res_val" jdbcType="VARCHAR" property="itemResVal" />
    <result column="detect_time" jdbcType="TIMESTAMP" property="detectTime" />
    <result column="reporter_id" jdbcType="VARCHAR" property="reporterId" />
    <result column="reporter_name" jdbcType="VARCHAR" property="reporterName" />
    <result column="report_time" jdbcType="TIMESTAMP" property="reportTime" />
    <result column="report_type_code" jdbcType="VARCHAR" property="reportTypeCode" />
    <result column="report_type_name" jdbcType="VARCHAR" property="reportTypeName" />
    <result column="report_auditor_id" jdbcType="VARCHAR" property="reportAuditorId" />
    <result column="report_auditor_name" jdbcType="VARCHAR" property="reportAuditorName" />
    <result column="report_audit_time" jdbcType="TIMESTAMP" property="reportAuditTime" />
    <result column="person_info_id" jdbcType="VARCHAR" property="personInfoId" />
    <result column="patient_name" jdbcType="VARCHAR" property="patientName" />
    <result column="patient_age" jdbcType="VARCHAR" property="patientAge" />
    <result column="patient_age_unit" jdbcType="VARCHAR" property="patientAgeUnit" />
    <result column="patient_sex_code" jdbcType="VARCHAR" property="patientSexCode" />
    <result column="patient_sex_desc" jdbcType="VARCHAR" property="patientSexDesc" />
    <result column="visit_type_code" jdbcType="VARCHAR" property="visitTypeCode" />
    <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
    <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime" />
    <result column="updator_id" jdbcType="VARCHAR" property="updatorId" />
    <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime" />
    <result column="enabled" jdbcType="VARCHAR" property="enabled" />
    <result column="deleted" jdbcType="VARCHAR" property="deleted" />
    <result column="is_exist_report_card" jdbcType="VARCHAR" property="isExistReportCard" />
    <result column="etl_create_datetime" jdbcType="TIMESTAMP" property="etlCreateDatetime" />
    <result column="etl_update_datetime" jdbcType="TIMESTAMP" property="etlUpdateDatetime" />
    <result column="medical_id" jdbcType="VARCHAR" property="medicalId" />
    <result column="visit_time" jdbcType="TIMESTAMP" property="visitTime" />
    <result column="visit_type_name" jdbcType="VARCHAR" property="visitTypeName" />
    <result column="identity_type_code" jdbcType="VARCHAR" property="identityTypeCode" />
    <result column="identity_type_name" jdbcType="VARCHAR" property="identityTypeName" />
    <result column="identity_no" jdbcType="VARCHAR" property="identityNo" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, event_id, serial_no, lab_test_report_no, lab_test_report_title, org_id, org_code, 
    org_name, apply_org_id, apply_org_name, specimen_collect_time, specimen_part_code, 
    specimen_part_name, specimen_type_code, specimen_type_name, specimen_code, specimen_name, 
    sample_no, barcode_no, exam_org_id, exam_org_name, exec_dept_code, exec_dept_name, 
    exam_type_code, exam_type_name, item_code, item_name, lab_item_class_code, lab_item_class_name, 
    test_detail_code, test_detail_desc, test_result_code, item_result_name, item_unit, 
    item_res_flag, reference_value, reference_value_min, reference_value_max, item_res_num, 
    item_res_num_unit, item_res_val, detect_time, reporter_id, reporter_name, report_time, 
    report_type_code, report_type_name, report_auditor_id, report_auditor_name, report_audit_time, 
    person_info_id, patient_name, patient_age, patient_age_unit, patient_sex_code, patient_sex_desc, 
    visit_type_code, creator_id, create_datetime, updator_id, update_datetime, enabled, 
    deleted, is_exist_report_card, etl_create_datetime, etl_update_datetime, medical_id, 
    visit_time, visit_type_name, identity_type_code, identity_type_name, identity_no, 
    source_id
  </sql>

  <select id="getLisInfoByPatient" resultMap="BaseResultMap">
    select distinct on (event_id, lab_test_report_no)
    <include refid="Base_Column_List"/>
    from ads.ads_mt_lis_info t
    <where>
      <if test="param.startTime != null and param.startTime != ''">
        and t.visit_time >= #{param.startTime}::timestamp
      </if>
      <if test="param.endTime != null and param.endTime != ''">
        and t.visit_time  <![CDATA[<=]]>  #{param.endTime}::timestamp
      </if>
      <if test="param.patientName">
        and t.patient_name = #{param.patientName,jdbcType=VARCHAR}
      </if>
      <if test="param.idCardNo != null">
        and t.identity_no = #{param.idCardNo,jdbcType=VARCHAR}
      </if>
    </where>

  </select>
  <select id="getLisInfoList" resultType="com.iflytek.cdc.edr.vo.LisInfoVO">
      select t1.lab_test_report_no as hisInspectId,
      t1.org_name as orgName,
      t1.visit_type_name as visitTypeName,
      CASE WHEN t1.visit_type_code = '4' THEN '3' ELSE t1.visit_type_code END as visitTypeCode,
      t1.visit_time as visitTime,
      t1.serial_no as serialNo,
      t1.patient_name as patientName,
      t1.patient_sex_desc as patientSexDesc,
      concat(t1.patient_age, t1.patient_age_unit) as patientAge,
      t1.detect_time as detectTime,
      t1.item_code as itemCode,
      t1.item_name as itemName,
      concat(t1.reference_value, t1.item_unit) as referenceValue,
      t1.item_res_val as itemResVal,
      concat(t1.item_res_num, item_res_num_unit) as itemResNum,
      t1.disease_name as diseaseName,
      t1.is_exist_report_card as isExistReportCard,
      t1.recent_report_card_datetime as reportCardDatetime,
      t1.medical_id as medicalId,
      t1.test_detail_desc as inspectName,
      t1.report_card_type AS reportCardType,
      t1.report_card_type AS reportCardTypeName
      from ads.ads_mt_lis_info t1
      LEFT JOIN dim.dim_organization_info t2 on t1.org_id = t2.org_id
      WHERE t1.report_time >= #{startDate}::timestamp
      AND t1.report_time &lt; #{endDate}::timestamp + INTERVAL '1 day'
      <if test="orgList != null and orgList.size() &gt; 0">
          <foreach collection="orgList" index="index" item="orgId" open=" AND (" separator=" OR " close=")">
              t1.org_id = #{orgId}
          </foreach>
      </if>
      <if test="provinceCode != null and provinceCode != ''">
          AND t2.province_code = #{provinceCode}
      </if>
      <if test="cityCode != null and cityCode != ''">
          AND t2.city_code = #{cityCode}
      </if>
      <if test="districtCode != null and districtCode != ''">
          AND t2.district_code = #{districtCode}
      </if>
      <if test="provinceCodes != null and provinceCodes.size() > 0">
          and t2.province_code in
          <foreach collection="provinceCodes" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="cityCodes != null and cityCodes.size() > 0">
          and t2.city_code in
          <foreach collection="cityCodes" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="districtCodes != null and districtCodes.size() > 0">
          and t2.district_code in
          <foreach collection="districtCodes" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="patientName != null and patientName != ''">
          AND t1.patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
      </if>
      <if test="sourceType != null and sourceType != ''">
          AND SUBSTR(t1.source_id, 1, 3) = #{sourceType}
      </if>
      <if test="patientSourceCode != null and patientSourceCode != ''">
          AND t1.visit_type_code = #{patientSourceCode}
      </if>
      <if test="isExistReportCard != null and isExistReportCard != ''">
          AND t1.is_exist_report_card = #{isExistReportCard}
          <if test="isExistReportCard == 1 and reportCardTypeList != null and reportCardTypeList.size() > 0">
              <foreach collection="reportCardTypeList" item="reportCardType" index="index" open=" AND (" separator=" OR " close=")">
                  t1.report_card_type LIKE CONCAT('%', #{reportCardType}, '%')
              </foreach>
          </if>
      </if>
      <if test="abnormal != null and abnormal != ''">
          AND t1.item_res_val = #{abnormal}
      </if>
      <if test="inspectItemList != null and inspectItemList.size() &gt; 0">
          <foreach collection="inspectItemList" index="index" item="insItem" open=" AND (" separator=" OR " close=")">
              t1.item_name = #{insItem}
          </foreach>
      </if>
      <if test="diseaseCode != null and diseaseCode.size() &gt; 0">
          <foreach collection="diseaseCode" index="index" item="disease" open=" AND (" separator=" OR " close=")">
              t1.disease_code = #{disease}
          </foreach>
      </if>
      <if test="customQueryParamList != null and customQueryParamList.size() > 0">
          AND (
          <trim prefixOverrides="AND|OR">
              <foreach collection="customQueryParamList" item="queryParam" index="index">
                  <choose>
                      <when test="queryParam.logicalType == 'and'"> AND </when>
                      <when test="queryParam.logicalType == 'or'"> OR </when>
                  </choose>
                  <choose>
                      <when test="queryParam.paramName == 'age'">
                          <choose>
                              <when test="queryParam.compareType == 'eq'"> t1.patient_age = #{queryParam.paramValue} </when>
                              <when test="queryParam.compareType == 'ne'"> (t1.patient_age != #{queryParam.paramValue} OR t1.patient_age IS NULL) </when>
                              <when test="queryParam.compareType == 'gt'"> (CASE WHEN t1.patient_age ~ '^[0-9\.]+$' THEN t1.patient_age::INTEGER END) <![CDATA[ > ]]> #{queryParam.paramValue}::INTEGER </when>
                              <when test="queryParam.compareType == 'lt'"> (CASE WHEN t1.patient_age ~ '^[0-9\.]+$' THEN t1.patient_age::INTEGER END) <![CDATA[ < ]]> #{queryParam.paramValue}::INTEGER </when>
                          </choose>
                          <if test="queryParam.ageUnit != null and queryParam.ageUnit != ''">
                              AND t1.patient_age_unit = #{queryParam.ageUnit}
                          </if>
                      </when>
                      <when test="queryParam.paramName == 'item'">
                          <choose>
                              <when test="queryParam.compareType == 'eq'"> t1.item_name = #{queryParam.paramValue} </when>
                              <when test="queryParam.compareType == 'ne'"> (t1.item_name != #{queryParam.paramValue} OR t1.item_name IS NULL) </when>
                              <when test="queryParam.compareType == 'co'"> t1.item_name LIKE CONCAT('%', #{queryParam.paramValue} ,'%') </when>
                              <when test="queryParam.compareType == 'nc'"> (t1.item_name NOT LIKE CONCAT('%', #{queryParam.paramValue}, '%') OR t1.item_name IS NULL) </when>
                          </choose>
                          <if test="queryParam.resultValue != null and queryParam.resultValue != ''">
                              <choose>
                                  <when test="queryParam.resultCompareType == 'eq'"> AND t1.item_res_num = #{queryParam.resultValue} </when>
                                  <when test="queryParam.resultCompareType == 'ne'"> AND (t1.item_res_num != #{queryParam.resultValue} OR t1.item_res_num IS NULL) </when>
                                  <when test="queryParam.resultCompareType == 'gt'"> AND (CASE WHEN t1.item_res_num ~ '^[0-9\.]+$' THEN t1.item_res_num::NUMERIC END) <![CDATA[ > ]]> #{queryParam.resultValue}::NUMERIC </when>
                                  <when test="queryParam.resultCompareType == 'lt'"> AND (CASE WHEN t1.item_res_num ~ '^[0-9\.]+$' THEN t1.item_res_num::NUMERIC END) <![CDATA[ < ]]> #{queryParam.resultValue}::NUMERIC </when>
                                  <when test="queryParam.resultCompareType == 'co'"> AND t1.item_res_num LIKE CONCAT('%', #{queryParam.resultValue} ,'%') </when>
                                  <when test="queryParam.resultCompareType == 'nc'"> AND (t1.item_res_num NOT LIKE CONCAT('%', #{queryParam.resultValue}, '%') OR t1.item_res_num IS NULL) </when>
                              </choose>
                          </if>
                      </when>
                      <when test="queryParam.paramName == 'disease'">
                          <choose>
                              <when test="queryParam.compareType == 'eq'"> t1.disease_code = #{queryParam.paramValue} </when>
                              <when test="queryParam.compareType == 'ne'"> (t1.disease_code != #{queryParam.paramValue} OR t1.disease_code IS NULL) </when>
                              <when test="queryParam.compareType == 'co'"> t1.disease_code LIKE CONCAT(#{queryParam.paramValue}, '%') </when>
                              <when test="queryParam.compareType == 'nc'"> (t1.disease_code NOT LIKE CONCAT(#{queryParam.paramValue}, '%') OR t1.disease_code IS NULL) </when>
                          </choose>
                      </when>
                  </choose>
              </foreach>
          </trim>
          )
      </if>
      ORDER BY coalesce(t1.create_datetime, '1900-01-01'::timestamp) DESC
  </select>
    <select id="getLisInfoByMedicalId" resultType="com.iflytek.cdc.edr.entity.AdsMtLisInfo">
        select * from ads.ads_mt_lis_info where medical_id = #{medicalId}
    </select>
</mapper>