<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.edr.mapper.pg.v2.DiseaseRecordMapper">

    <select id="getSummary" resultType="com.iflytek.cdc.edr.dto.outpatient.EdrSummaryDto">
        SELECT CAST(full_date AS DATE) AS stat_date,
               health_files_total::BIGINT AS disease_record_total,
               infection_patients_total::BIGINT AS infection_patients_total
        FROM dw.dws_edr_stat_indicator
        ORDER BY full_date DESC LIMIT 1
    </select>

    <select id="getEventList" resultType="com.iflytek.cdc.edr.dto.event.EventInfoDto">
        SELECT der.event_id,
               der.data_src AS data_src_code,
               doi.org_type_code,
               doi.org_type_name,
               doi.org_id,
               doi.org_name,
               doi.org_address,
               der.drug_prescription_tag,
               der.health_status,
               der.event_datetime
        FROM dwd.dwd_event_record der
                 INNER JOIN dim.dim_organization_info doi ON doi.org_id = der.org_id
        WHERE der.global_person_id = #{globalPersonId}
              --AND der.event_datetime >= CURRENT_DATE - INTERVAL '30 days'
        ORDER BY der.event_datetime DESC
    </select>

    <select id="getInspectionItems" resultType="com.iflytek.cdc.edr.dto.inspection.InspectionItem">
        SELECT '' as inspect_item_code, inspect_item_name, is_infected
        FROM dim.dim_ch_inspection_item
        <if test="inspectItemName != null and inspectItemName != ''">
            WHERE inspect_item_name like concat('%', #{inspectItemName, jdbcType=VARCHAR},'%')
        </if>
        ;
    </select>

    <select id="getInspectionDetail" resultType="com.iflytek.cdc.edr.dto.inspection.InspectionDetail">
        SELECT CASE data_src WHEN '2' THEN '门诊' WHEN '3' THEN '住院' END AS visit_type,
        CAST(visit_time AS DATE) as visit_time,
        business_id,
        patient_name,
        sex_name AS sex,
        concat(age, age_unit) AS age,
        inspect_item_code,
        CAST(result_datetime AS DATE) as result_datetime,
        inspect_item_name,
        result,
        exam_result_unit,
        refrange AS ref_range,
        abnormal,
        exam_item_name,
        inspect_id,
        specimen_name,
        CAST(submi_datetime AS DATE) AS submit_datetime,
        dept_name,
        CASE is_exist_report_card WHEN '1' THEN '是' ELSE '否' END AS is_exist_report_card
        FROM ads.ads_ch_inspection_report t
        WHERE result_datetime >= #{startDate}::timestamp
        AND result_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
        <if test="reportOrgCode != null">
            AND org_id = #{reportOrgCode}
        </if>
        <if test="patientName != null and patientName != ''">
            AND patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND SUBSTR(source_id, 1, 3) = #{sourceType}
        </if>
        <if test="patientSourceCode != null and patientSourceCode != ''">
            AND data_src = #{patientSourceCode}
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="abnormal != null and abnormal != ''">
            AND abnormal = #{abnormal}
        </if>
        <foreach collection="inspectItemList" index="index" item="insItem" open=" AND (" separator=" OR " close=")">
            inspect_item_name = #{insItem}
        </foreach>
        ORDER BY coalesce(t.visit_time, '1900-01-01'::timestamp) DESC
        LIMIT ${pageSize} OFFSET (${pageIndex} - 1) * ${pageSize}
    </select>

    <select id="getDicts" resultType="com.iflytek.cdc.edr.dto.DictDto">
        SELECT dict_value_code, dict_value_name
        FROM dim.dim_dict
        WHERE dict_type = #{dictType}
        AND dict_code = #{dictCode}
        <if test="dictValue != null and dictValue != ''">
            AND dict_value_name like concat('%', #{dictValue, jdbcType=VARCHAR},'%')
        </if>
    </select>
    <select id="getAllInspectionDetail" resultType="com.iflytek.cdc.edr.dto.inspection.InspectionDetail">
        SELECT CASE data_src WHEN '2' THEN '门诊' WHEN '3' THEN '住院' END AS visit_type,
        CAST(visit_time AS DATE) as visit_time,
        business_id,
        patient_name,
        sex_name AS sex,
        CONCAT(age, age_unit) AS age,
        inspect_item_code,
        CAST(result_datetime AS DATE) as result_datetime,
        inspect_item_name,
        result,
        exam_result_unit,
        refrange AS ref_range,
        abnormal,
        exam_item_name,
        inspect_id,
        specimen_name,
        CAST(submi_datetime AS DATE) AS submit_datetime,
        dept_name,
        CASE is_exist_report_card WHEN '1' THEN '是' ELSE '否' END AS is_exist_report_card
        FROM ads.ads_ch_inspection_report t
        WHERE result_datetime >= #{startDate}::timestamp
        AND result_datetime &lt; #{endDate}::timestamp + INTERVAL '1 day'
        <if test="reportOrgCode != null">
            AND org_id = #{reportOrgCode}
        </if>
        <if test="patientName != null and patientName != ''">
            AND patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND SUBSTR(source_id, 1, 3) = #{sourceType}
        </if>
        <if test="patientSourceCode != null and patientSourceCode != ''">
            AND data_src = #{patientSourceCode}
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="abnormal != null and abnormal != ''">
            AND abnormal = #{abnormal}
        </if>
        <foreach collection="inspectItemList" index="index" item="insItem" open=" AND (" separator=" OR " close=")">
            inspect_item_name = #{insItem}
        </foreach>
        ORDER BY coalesce(t.visit_time, '1900-01-01'::timestamp) DESC
    </select>
    <select id="getEventListByMedicalIds" resultType="com.iflytek.cdc.edr.dto.event.EventInfoDto">
        SELECT der.event_id,
               der.data_src AS data_src_code,
               doi.org_type_code,
               doi.org_type_name,
               doi.org_id,
               doi.org_name,
               doi.org_address,
               der.drug_prescription_tag,
               der.health_status,
               der.event_datetime
        FROM dwd.dwd_event_record der
                 INNER JOIN dim.dim_organization_info doi ON doi.org_id = der.org_id
                 inner join dwd.dwd_ch_medical_record r on der.event_id = r.event_id
        WHERE r.medical_id in
        <foreach close=")" collection="medicalIds" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        ORDER BY der.event_datetime DESC
    </select>

    <!-- 不需要infection_patients_total字段 -->
    <select id="getNewSummary" resultType="com.iflytek.cdc.edr.dto.outpatient.EdrSummaryDto">
        SELECT CAST(full_date AS DATE) AS stat_date,
               health_files_total::BIGINT AS disease_record_total
               -- infection_patients_total::BIGINT AS infection_patients_total
        FROM ads.ads_edr_stat_indicator
        ORDER BY full_date DESC LIMIT 1
    </select>

    <select id="getNewInspectionItems" resultType="com.iflytek.cdc.edr.dto.inspection.InspectionItem">
        SELECT '' as inspect_item_code, inspect_item_name, is_infected
        FROM dim.dim_inspection_item
        <if test="inspectItemName != null and inspectItemName != ''">
            WHERE inspect_item_name like concat('%', #{inspectItemName, jdbcType=VARCHAR},'%')
        </if>

    </select>

    <select id="getNewInspectionDetail" resultType="com.iflytek.cdc.edr.dto.inspection.InspectionDetail">
        SELECT CASE visit_type_code WHEN '2' THEN '门诊' WHEN '3' THEN '急诊' WHEN '4' THEN '住院' WHEN '9' THEN '其它' END AS visit_type,
        CAST(visit_time AS DATE) as visit_time,
        serial_no as business_id,
        patient_name,
        patient_sex_desc AS sex,
        concat(patient_age, patient_age_unit) AS age,
        item_code as inspect_item_code,
        CAST(report_time AS DATE) as result_datetime,
        item_name as inspect_item_name,
        item_res_num as result,
        item_res_num_unit as exam_result_unit,
        reference_value AS ref_range,
        item_res_val as abnormal,
        item_name as exam_item_name,
        lab_test_report_no as inspect_id,
        specimen_name,
        CAST(detect_time AS DATE) AS submit_datetime,
        exec_dept_name as dept_name,
        CASE is_exist_report_card WHEN '1' THEN '是' ELSE '否' END AS is_exist_report_card
        FROM ads.ads_mt_lis_info t
        WHERE report_time >= #{startDate}::timestamp
        AND report_time &lt; #{endDate}::timestamp + INTERVAL '1 day'
        <if test="reportOrgCode != null">
            AND org_id = #{reportOrgCode}
        </if>
        <if test="patientName != null and patientName != ''">
            AND patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND SUBSTR(source_id, 1, 3) = #{sourceType}
        </if>
        <if test="patientSourceCode != null and patientSourceCode != ''">
            AND visit_type_code = #{patientSourceCode}
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="abnormal != null and abnormal != ''">
            AND item_res_val = #{abnormal}
        </if>
        <foreach collection="inspectItemList" index="index" item="insItem" open=" AND (" separator=" OR " close=")">
            item_name = #{insItem}
        </foreach>
        ORDER BY coalesce(t.create_datetime, '1900-01-01'::timestamp) DESC
        LIMIT ${pageSize} OFFSET (${pageIndex} - 1) * ${pageSize}
    </select>

    <select id="getNewAllInspectionDetail" resultType="com.iflytek.cdc.edr.dto.inspection.InspectionDetail">
        SELECT CASE visit_type_code WHEN '2' THEN '门诊' WHEN '3' THEN '急诊' WHEN '4' THEN '住院' WHEN '9' THEN '其它' END AS visit_type,
        CAST(visit_time AS DATE) as visit_time,
        serial_no as business_id,
        patient_name,
        patient_sex_desc AS sex,
        CONCAT(patient_age, patient_age_unit) AS age,
        item_code as inspect_item_code,
        CAST(report_time AS DATE) as result_datetime,
        item_name as inspect_item_name,
        item_result_name as result,
        item_unit as exam_result_unit,
        reference_value AS ref_range,
        item_res_val as abnormal,
        item_name as exam_item_name,
        lab_test_report_no as inspect_id,
        specimen_name,
        CAST(detect_time AS DATE) AS submit_datetime,
        exec_dept_name as dept_name,
        CASE is_exist_report_card WHEN '1' THEN '是' ELSE '否' END AS is_exist_report_card
        FROM ads.ads_mt_lis_info t
        WHERE report_time >= #{startDate}::timestamp
        AND report_time &lt; #{endDate}::timestamp + INTERVAL '1 day'
        <if test="reportOrgCode != null">
            AND org_id = #{reportOrgCode}
        </if>
        <if test="patientName != null and patientName != ''">
            AND patient_name LIKE concat('%', #{patientName, jdbcType=VARCHAR},'%')
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND SUBSTR(source_id, 1, 3) = #{sourceType}
        </if>
        <if test="patientSourceCode != null and patientSourceCode != ''">
            AND visit_type_code = #{patientSourceCode}
        </if>
        <if test="isExistReportCard != null and isExistReportCard != ''">
            AND is_exist_report_card = #{isExistReportCard}
        </if>
        <if test="abnormal != null and abnormal != ''">
            AND item_res_val = #{abnormal}
        </if>
        <foreach collection="inspectItemList" index="index" item="insItem" open=" AND (" separator=" OR " close=")">
            item_name = #{insItem}
        </foreach>
        ORDER BY coalesce(t.create_datetime, '1900-01-01'::timestamp) DESC
    </select>

    <select id="getNewEventList" resultType="com.iflytek.cdc.edr.dto.event.EventInfoDto">
        -- todo 使用medical_id代替event_id
        SELECT aer.medical_id as event_id,
                aer.medical_id as medical_id,
               aer.data_src AS data_src_code,
               doi.org_type_code,
               doi.org_type_name,
               doi.org_id,
               doi.org_name,
               doi.org_address,
               aer.drug_prescription_tag,
               aer.health_status,
               aer.global_person_id,
               aer.event_datetime
        FROM ads.ads_event_record aer
        inner join dim.dim_organization_info doi on doi.org_id = aer.org_id
        WHERE aer.global_person_id = #{globalPersonId}
        ORDER BY aer.event_datetime DESC
    </select>

    <select id="loadByMedicalId" resultType="com.iflytek.cdc.edr.dto.event.EventInfoDto">
        -- todo 使用medical_id代替event_id
        SELECT aer.medical_id as event_id,
        aer.medical_id as medical_id,
        aer.data_src AS data_src_code,
        doi.org_type_code,
        doi.org_type_name,
        doi.org_id,
        doi.org_name,
        doi.org_address,
        aer.drug_prescription_tag,
        aer.health_status,
        aer.event_datetime,
        aer.global_person_id
        FROM ads.ads_event_record aer
        inner join dim.dim_organization_info doi on doi.org_id = aer.org_id
        WHERE aer.medical_id = #{medicalId}
        ORDER BY aer.event_datetime DESC
        limit 1
    </select>

    <select id="listByMedicalIds" resultType="com.iflytek.cdc.edr.dto.event.EventInfoDto">
        -- todo 使用medical_id代替event_id
        SELECT aer.medical_id as event_id,
        aer.medical_id as medical_id,
        aer.data_src AS data_src_code,
        doi.org_type_code,
        doi.org_type_name,
        doi.org_id,
        doi.org_name,
        doi.org_address,
        aer.drug_prescription_tag,
        aer.health_status,
        aer.event_datetime,
        aer.global_person_id
        FROM ads.ads_event_record aer
        inner join dim.dim_organization_info doi on doi.org_id = aer.org_id
        WHERE aer.medical_id in
        <foreach collection="medicalIds" open="(" separator="," close=")" item="medicalId">
            #{medicalId}
        </foreach>
        ORDER BY aer.event_datetime DESC
    </select>

    <select id="listByGlobalPersonIds" resultType="com.iflytek.cdc.edr.dto.event.EventInfoDto">
        -- todo 使用medical_id代替event_id
        SELECT aer.medical_id as event_id,
        aer.medical_id as medical_id,
        aer.data_src AS data_src_code,
        doi.org_type_code,
        doi.org_type_name,
        doi.org_id,
        doi.org_name,
        doi.org_address,
        aer.drug_prescription_tag,
        aer.health_status,
        aer.event_datetime,
        aer.global_person_id
        FROM ads.ads_event_record aer
        inner join dim.dim_organization_info doi on doi.org_id = aer.org_id
        WHERE aer.global_person_id in
        <foreach collection="globalPersonIds" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        ORDER BY aer.event_datetime DESC
    </select>

    <select id="getNewEventListByMedicalIds" resultType="com.iflytek.cdc.edr.dto.event.EventInfoDto">
        SELECT aer.medical_id as event_id,
        aer.medical_id as medical_id,
        aer.data_src AS data_src_code,
        doi.org_type_code,
        doi.org_type_name,
        doi.org_id,
        doi.org_name,
        doi.org_address,
        aer.drug_prescription_tag,
        aer.health_status,
        aer.event_datetime,
        aer.global_person_id
        FROM ads.ads_event_record aer
        INNER JOIN dim.dim_organization_info doi ON doi.org_id = aer.org_id
        inner join ads.ads_outpat_medical_records r on aer.event_id = r.event_id
        WHERE r.medical_id in
        <foreach close=")" collection="medicalIds" index="index" item="item" open="(" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        ORDER BY aer.event_datetime DESC
    </select>

    <select id="getPersonInfoByMedicalId" resultType="com.iflytek.cdc.edr.entity.AdsEventRecord">
        select aer.*,
        CASE WHEN EXTRACT(YEAR FROM AGE(dmpi.birthday)) >= 1 THEN concat(EXTRACT(YEAR FROM AGE(dmpi.birthday)), '岁')
        WHEN EXTRACT(MONTH FROM AGE(dmpi.birthday)) >= 1 THEN concat(EXTRACT(MONTH FROM AGE(dmpi.birthday)), '月')
        ELSE concat(EXTRACT(DAY FROM AGE(dmpi.birthday)), '天')
        END AS age
        from ads.ads_event_record aer
        left join dim.dim_mdm_person_info dmpi on aer.global_person_id = dmpi.global_person_id
        where medical_id = #{medicalId}
    </select>

</mapper>
