<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.dm.mapper.bu.DmApprovalAuthMapper">

    <select id="queryApprovalAuthList" resultType="com.iflytek.cdc.dm.entity.DmApprovalAuth">
        select
        auth_code,
        string_agg(approval_user_id,',') as  approvalUserId,
        string_agg(approval_user_name,',') as approvalUserName,
        Max(update_time) as updateTime
        from app.tb_cdcdm_dm_approval_auth
        where delete_flag ='0'
        <if test="authCode != null and authCode != ''">
            and auth_code = #{authCode}
        </if>
        group by auth_code
        order by auth_code
    </select>

    <select id="queryDmAnalysisResultUrl" resultType="com.iflytek.cdc.dm.entity.vo.CommonParamVO">
        SELECT
            tccp.id,
            tccp.param_value,
            tccp.param_key,
            tccp.notes
        FROM app.tb_cdcmr_common_param AS tccp
        WHERE tccp.delete_flag = '0'
            AND tccp.param_class = 'DM'
    </select>
</mapper>