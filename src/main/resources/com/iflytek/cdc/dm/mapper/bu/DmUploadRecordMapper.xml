<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.dm.mapper.bu.DmUploadRecordMapper">


    <select id="getUploadRecordList" resultType="com.iflytek.cdc.dm.entity.DmUploadRecord">
           SELECT
            dur.id,
            dur.file_name,
            dur.check_info,
            dur.upload_result,
            dur.create_time,
            a.attachment_path as attachmentUrl
        FROM app.tb_cdcdm_dm_upload_record dur
        JOIN app.tb_cdcmr_attachment a
        ON dur.attachment_id =a.id AND  a.status = '1'
        WHERE dur.delete_flag = '0' AND dur.creator_id = #{loginUserId}
        <if test="query.fileName != null and query.fileName != ''">
            AND dur.file_name like concat('%',#{query.fileName},'%')
        </if>
        <if test="query.uploadResult!= null and query.uploadResult!= ''">
            AND dur.upload_result = #{query.uploadResult}
        </if>
        <if test="query.startDate!= null">
            <![CDATA[  and dur.create_time >= #{query.startDate}]]>
        </if>
        <if test=" query.endDate!= null ">
            <![CDATA[  and dur.create_time <= #{query.endDate}]]>
        </if>
        ORDER BY dur.create_time DESC
    </select>
</mapper>