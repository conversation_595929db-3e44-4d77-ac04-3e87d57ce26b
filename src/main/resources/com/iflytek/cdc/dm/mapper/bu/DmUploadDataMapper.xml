<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.cdc.dm.mapper.bu.DmUploadDataMapper">

    <select id="getFiledNameByProcessTypeAndNames" resultType= "com.iflytek.cdc.dm.entity.vo.WarningResearchFieldSettingVO">
        SELECT name,identification
        FROM app.tb_cdcew_warning_research_field_setting
        WHERE delete_flag = '0'
        AND name in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="processType != null and processType != ''">
            AND process_type = #{processType}
        </if>

    </select>
</mapper>