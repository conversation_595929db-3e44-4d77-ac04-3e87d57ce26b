<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <contextName>cdc-data-service</contextName>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <property name="logging.maxFileSize" value="100MB"/>
    <property name="logging.maxHistory" value="7"/>
    <property name="logging.totalSizeCap" value="10GB"/>
    <property name="logging.path" value="/home/<USER>"/>
    <property name="spring.application.name" value="cdc-data-service"/>
    <property name="logging.pattern.file"
              value="%d{YYYY-MM-dd HH:mm:ss} %-5level [%thread] %logger{36} - %msg%n"/>
    <property name="logging.pattern.console"
              value="%yellow(%d{YYYY-MM-dd HH:mm:ss}) %highlight(%-5level) %red([%thread]) %cyan(%logger{36}) - %magenta(%msg%n)"/>

    <appender name="error-file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logging.path}/${spring.application.name}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logging.path}/${spring.application.name}/error/error-%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <maxFileSize>${logging.maxFileSize}</maxFileSize>
            <maxHistory>${logging.maxHistory}</maxHistory>
            <totalSizeCap>${logging.totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <append>true</append>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${logging.pattern.file}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>error</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="warn-file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logging.path}/${spring.application.name}/warn.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logging.path}/${spring.application.name}/warn/warn-%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <maxFileSize>${logging.maxFileSize}</maxFileSize>
            <maxHistory>${logging.maxHistory}</maxHistory>
            <totalSizeCap>${logging.totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <append>true</append>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${logging.pattern.file}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>warn</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logging.path}/${spring.application.name}/all.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logging.path}/${spring.application.name}/all/all-%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <maxFileSize>${logging.maxFileSize}</maxFileSize>
            <maxHistory>${logging.maxHistory}</maxHistory>
            <totalSizeCap>${logging.totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <append>true</append>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${logging.pattern.file}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${logging.pattern.console}</pattern>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="error-file"/>
        <appender-ref ref="warn-file"/>
        <appender-ref ref="file"/>
        <appender-ref ref="console"/>
    </root>

    <springProfile name="prod">
        <logger name="com.iflytek.cdc" level="INFO" additivity="false">
            <appender-ref ref="error-file"/>
            <appender-ref ref="warn-file"/>
            <appender-ref ref="file"/>
            <appender-ref ref="console"/>
        </logger>
    </springProfile>
    <springProfile name="dev, test">
        <logger name="com.iflytek.cdc" level="DEBUG" additivity="false">
            <appender-ref ref="error-file"/>
            <appender-ref ref="warn-file"/>
            <appender-ref ref="file"/>
            <appender-ref ref="console"/>
        </logger>
    </springProfile>
</configuration>
