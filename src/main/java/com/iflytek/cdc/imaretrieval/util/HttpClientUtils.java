package com.iflytek.cdc.imaretrieval.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

/**
* @description
* @author: junxiong3
* @date: 2023/12/8
*/
@Component
@Slf4j
public class HttpClientUtils {

    @Value("${http.client.setSocketTimeout:10000}")
    private int socketTimeout;

    @Value("${http.client.setConnectTimeout:10000}")
    private int connectTimeout;

    /**
     * post请求传输json参数
     * @param url  url地址
     * @param jsonParam 参数
     * @return
     */
    public JSONObject httpPost(String url, JSONObject jsonParam) {
        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(socketTimeout)
                .setConnectTimeout(connectTimeout).build();
        // post请求返回结果
        CloseableHttpClient httpClient = HttpClients.createDefault();
        JSONObject jsonResult = null;
        HttpPost httpPost = new HttpPost(url);
        // 设置请求和传输超时时间
        httpPost.setConfig(requestConfig);
        try {
            if (null != jsonParam) {
                // 解决中文乱码问题
                StringEntity entity = new StringEntity(jsonParam.toString(), "UTF-8");
                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                httpPost.setEntity(entity);
            }
            CloseableHttpResponse result = httpClient.execute(httpPost);
            // 请求发送成功，并得到响应
            if (result.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                // 读取服务器返回过来的json字符串数据
                String str = EntityUtils.toString(result.getEntity(), "UTF-8");
                // 把json字符串转换成json对象
                jsonResult = JSON.parseObject(str);
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        } finally {
            httpPost.releaseConnection();
            closeHttpClient(httpClient);
        }
        return jsonResult;
    }

    /**
     * get查询
     * @param url
     * @return
     */
    public String httpGet(String url) {
        return httpGet(url, null);
    }

    /**
     * get查询
     * @param url
     * @param header
     * @return
     */
    public String httpGet(String url, Map<String, String> header) {
        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(socketTimeout)
                .setConnectTimeout(connectTimeout).build();
        CloseableHttpClient httpClient = HttpClients.createDefault();
        String str = "";
        HttpGet httpGet = new HttpGet(url);
        if(header != null && !header.isEmpty()){
            for (Map.Entry<String, String> entry : header.entrySet()) {
                httpGet.addHeader(entry.getKey(), entry.getValue());
            }
        }
        httpGet.setConfig(requestConfig);
        CloseableHttpResponse result = null;
        try {
            result = httpClient.execute(httpGet);
            // 请求发送成功，并得到响应
            if (result.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                // 读取服务器返回过来的json字符串数据
                str = EntityUtils.toString(result.getEntity(), "UTF-8");
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }finally {
            httpGet.releaseConnection();
            closeHttpClient(httpClient);
        }
        return str;
    }

    public void closeHttpClient(CloseableHttpClient httpClient) {
        try {
            if (null != httpClient) {
                httpClient.close();
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }
}