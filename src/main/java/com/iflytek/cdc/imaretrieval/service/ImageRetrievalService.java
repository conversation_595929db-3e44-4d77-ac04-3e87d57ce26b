package com.iflytek.cdc.imaretrieval.service;


import com.iflytek.cdc.imaretrieval.entity.HealthExamDataDTO;
import com.iflytek.cdc.imaretrieval.entity.HealthExamQueryRequestDTO;
import com.iflytek.cdc.imaretrieval.entity.ImageRetrievalRecordQueryDTO;
import com.iflytek.cdc.imaretrieval.entity.ViewerKeyRequestDTO;

import java.util.List;

public interface ImageRetrievalService {

    /**
     * 查询检查数据
     *
     * @param requestDTO 查询请求参数
     * @return 检查数据响应
     */
    List<HealthExamDataDTO> queryHealthExam(HealthExamQueryRequestDTO requestDTO);

    String getViewerKey(ViewerKeyRequestDTO requestDTO);

    String saveReminderCallRecord(ImageRetrievalRecordQueryDTO requestDTO);

    boolean removeToken();

}
