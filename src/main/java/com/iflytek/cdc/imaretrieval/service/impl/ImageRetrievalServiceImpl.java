package com.iflytek.cdc.imaretrieval.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.iflytek.cdc.imaretrieval.config.ImageRetrievalConfig;
import com.iflytek.cdc.imaretrieval.entity.*;
import com.iflytek.cdc.imaretrieval.service.ImageRetrievalService;
import com.iflytek.cdc.imaretrieval.util.HttpClientUtils;
import com.iflytek.cdc.imaretrieval.util.SM2EncryptUtil;
import com.iflytek.cdc.province.entity.ads.AdsBizEdrInfo;
import com.iflytek.cdc.province.service.BizEdrInfoService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class ImageRetrievalServiceImpl implements ImageRetrievalService {
    @Autowired
    private HttpClientUtils httpClientUtils;
    @Resource
    private ImageRetrievalConfig config;
    @Resource
    private BizEdrInfoService bizEdrInfoService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public List<HealthExamDataDTO> queryHealthExam(HealthExamQueryRequestDTO requestDTO) {
        try {
            // 1. 先调用鉴权接口获取token
            String token = getAuthToken();
            if (token == null) {
                throw new MedicalBusinessException("获取鉴权token失败");
            }
            requestDTO.setToken(token);
            //需要根据archiveId获取患者信息
            AdsBizEdrInfo edrUserInfo = bizEdrInfoService.getById(requestDTO.getArchiveId());
            if (ObjectUtil.isNull(edrUserInfo)) {
                throw new MedicalBusinessException("未查询到患者档案信息");
            }

            //将身份证号码使用国密算法SM2加密
            String idCardNo = edrUserInfo.getIdCard();
            if (StrUtil.isNotBlank(idCardNo)) {
                // 验证身份证号码格式
                if (!SM2EncryptUtil.isValidIdCard(idCardNo)) {
                    log.warn("身份证号码格式无效: {}", idCardNo);
                    throw new MedicalBusinessException("身份证号码格式无效");
                }

                // 使用SM2算法加密身份证号码
                String encryptedIdCard = SM2EncryptUtil.sm2Encrypt(idCardNo, config.getPublicKey());
                log.info("身份证号码加密完成，原始号码: {}, 加密后: {}", idCardNo, encryptedIdCard);
                // 将加密后的身份证号码设置回请求对象
                requestDTO.setIdCardNo(encryptedIdCard);
                requestDTO.setPatientName(edrUserInfo.getName());
            } else {
                throw new MedicalBusinessException("当前查询人身份证号码不能为空");
            }

            requestDTO.setSearchType("1");
            requestDTO.setExamType("1");
            requestDTO.setMac("48-51-C5-6E-F8-66");
            log.info("开始查询检查数据接口={}", JSON.toJSONString(requestDTO));
            // 将请求参数转换为JSONObject
            JSONObject requestJson = JSONObject.parseObject(JSON.toJSONString(requestDTO));
            // 调用外部接口
            JSONObject responseJson = httpClientUtils.httpPost(config.getQueryUrl(), requestJson);
            log.info("外部接口响应：{}", responseJson.toJSONString());
            // 将响应转换为DTO
            ImageRetrievalResponseDTO responseDTO = JSON.parseObject(responseJson.toJSONString(), ImageRetrievalResponseDTO.class);
            if (responseDTO.getCode() == 200) {
                List<HealthExamDataDTO> result = JSONArray.parseArray(JSONObject.toJSONString(responseDTO.getDatas()), HealthExamDataDTO.class);
                return result;
            }else if(responseDTO.getCode() == 404){
                //返回状态码 404 表示未查询到数据
                return new ArrayList<>();
            } else {
                throw new MedicalBusinessException(responseDTO.getMsg());
            }
        } catch (MedicalBusinessException e) {
            log.error("查询检查数据异常", e);
            throw new MedicalBusinessException(e.getMessage());
        }catch (Exception e) {
            log.error("查询检查数据异常", e);
            throw new MedicalBusinessException("查询检查数据异常，请稍后再试");
        }
    }

    @Override
    public String getViewerKey(ViewerKeyRequestDTO requestDTO) {
        try {
            // 1. 先调用鉴权接口获取token
            String token = getAuthToken();
            if (token == null) {
                throw new MedicalBusinessException("获取鉴权token失败");
            }
            requestDTO.setToken(token);
            requestDTO.setOrgCode(config.getOrgCode());
            log.info("开始影像调阅 viewerKey 获取接口={}", JSON.toJSONString(requestDTO));
            // 将请求参数转换为JSONObject
            JSONObject requestJson = JSONObject.parseObject(JSON.toJSONString(requestDTO));
            // 调用外部接口
            JSONObject responseJson = httpClientUtils.httpPost(config.getViewerKeyUrl(), requestJson);
            log.info("外部接口响应：{}", responseJson.toJSONString());
            // 将响应转换为DTO
            ImageRetrievalResponseDTO responseDTO = JSON.parseObject(responseJson.toJSONString(), ImageRetrievalResponseDTO.class);
            if (responseDTO.getCode() == 200) {
                return ((JSONObject) responseDTO.getDatas()).getString("viewerKey");
            } else {
                throw new MedicalBusinessException(responseDTO.getMsg());
            }
        } catch (Exception e) {
            log.error("查询影像调阅 viewerKey异常", e);
            throw new MedicalBusinessException("查询影像调阅 viewerKey异常");
        }
    }

    @Override
    public String saveReminderCallRecord(ImageRetrievalRecordQueryDTO requestDTO) {
        try {
            // 1. 先调用鉴权接口获取token
            String token = getAuthToken();
            if (token == null) {
                throw new MedicalBusinessException("获取鉴权token失败");
            }
            requestDTO.setToken(token);
            requestDTO.setOrgCode(config.getOrgCode());
            requestDTO.setRetrivalTime(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            requestDTO.setRetrivaType("2");
            requestDTO.setSearchType("1");
            requestDTO.setDataType("1");
            requestDTO.setUserOperateId(UUID.randomUUID().toString().replace("-", ""));
            requestDTO.setMac("48-51-C5-6E-F8-66");
            log.info("开始提醒和调阅记录接口={}", JSON.toJSONString(requestDTO));
            // 将请求参数转换为JSONObject
            JSONObject requestJson = JSONObject.parseObject(JSON.toJSONString(requestDTO));
            // 调用外部接口
            JSONObject responseJson = httpClientUtils.httpPost(config.getSaveReminderCallRecordUrl(), requestJson);
            log.info("外部接口响应：{}", responseJson.toJSONString());

            return responseJson.getString("msg");
        } catch (Exception e) {
            log.error("查询影像调阅 viewerKey异常", e);
            throw new MedicalBusinessException("查询影像调阅 viewerKey异常");
        }
    }

    @Override
    public boolean removeToken() {
        try {
            stringRedisTemplate.delete("imaretrieval_token");
            return true;
        } catch (Exception e) {
            log.error("移除token异常", e);
        }
        return false;
    }

    private String getAuthToken() {
        try {
            //判断缓存里面是否有token
            String token = stringRedisTemplate.opsForValue().get("imaretrieval_token");
            if (StrUtil.isNotBlank(token)) {
                return token;
            }
            //缓存中没有，则重新获取
            // 构建鉴权请求参数
            AuthRequestDTO authRequest = new AuthRequestDTO();
            authRequest.setAppId(config.getAppId());
            authRequest.setAppSecret(config.getAppSecret());
            authRequest.setOrgCode(config.getOrgCode());
            log.info("鉴权请求参数：{}", JSON.toJSONString(authRequest));
            JSONObject authRequestJson = JSONObject.parseObject(JSON.toJSONString(authRequest));
            // 调用鉴权接口
            JSONObject authResponseJson = httpClientUtils.httpPost(config.getAuthUrl(), authRequestJson);
            log.info("鉴权接口响应：{}", authResponseJson.toJSONString());

            // 解析响应
            ImageRetrievalResponseDTO responseDTO = JSON.parseObject(authResponseJson.toJSONString(), ImageRetrievalResponseDTO.class);
            if (responseDTO.getCode() == 200 && ObjectUtil.isNotEmpty(responseDTO.getDatas())) {
                token = ((JSONObject) responseDTO.getDatas()).getString("token");

                stringRedisTemplate.opsForValue().set("imaretrieval_token", token, 1, TimeUnit.HOURS);
                return token;
            } else {
                log.error("鉴权失败，响应码：{}，响应消息：{}", responseDTO.getCode(), responseDTO.getMsg());
                return null;
            }
        } catch (Exception e) {
            log.error("获取鉴权token异常", e);
            return null;
        }
    }
}
