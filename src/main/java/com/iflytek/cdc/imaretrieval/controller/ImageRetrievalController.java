package com.iflytek.cdc.imaretrieval.controller;

import com.iflytek.cdc.imaretrieval.entity.HealthExamDataDTO;
import com.iflytek.cdc.imaretrieval.entity.HealthExamQueryRequestDTO;
import com.iflytek.cdc.imaretrieval.entity.ImageRetrievalRecordQueryDTO;
import com.iflytek.cdc.imaretrieval.entity.ViewerKeyRequestDTO;
import com.iflytek.cdc.imaretrieval.service.ImageRetrievalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


@RestController
@Api(tags = "影像调阅模块")
public class ImageRetrievalController {
    @Resource
    private ImageRetrievalService imageRetrievalService;
    @PostMapping("/api/imaretrieval/queryHealthExam")
    @ApiOperation("查询检查数据")
    public List<HealthExamDataDTO> queryHealthExam(@RequestBody HealthExamQueryRequestDTO requestDTO) {
        return imageRetrievalService.queryHealthExam(requestDTO);
    }

    @GetMapping("/pb/api/imaretrieval/removeToken")
    @ApiOperation("移除token")
    public boolean removeToken() {
        return imageRetrievalService.removeToken();
    }

    @PostMapping("/api/imaretrieval/getViewerKey")
    @ApiOperation("影像调阅 viewerKey 获取接口")
    public String getViewerKey(@RequestBody ViewerKeyRequestDTO requestDTO) {
        return imageRetrievalService.getViewerKey(requestDTO);
    }

    @PostMapping("/api/imaretrieval/saveReminderCallRecord")
    @ApiOperation("提醒和调阅记录")
    public String saveReminderCallRecord(@RequestBody ImageRetrievalRecordQueryDTO requestDTO) {
        return imageRetrievalService.saveReminderCallRecord(requestDTO);
    }
}
