package com.iflytek.cdc.imaretrieval.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@Data
@RefreshScope
public class ImageRetrievalConfig {
    @Value("${image.retrieval.authUrl:http://36.7.144.104:5000/third/interface/getToken}")
    public String authUrl;

    @Value("${image.retrieval.queryUrl:http://36.7.144.104:5000/third/interface/queryHealthExam}")
    public String queryUrl;

    @Value("${image.retrieval.viewerKeyUrl:http://36.7.144.104:5000/third/interface/getViewerKey}")
    public String viewerKeyUrl;
    @Value("${image.retrieval.saveReminderCallRecordUrl:http://36.7.144.104:5000/third/interface/saveReminderCallRecord}")
    public String saveReminderCallRecordUrl;

    @Value("${image.retrieval.appId:1950746649043877888}")
    public String appId;

    @Value("${image.retrieval.appSecret:5rOtmeDvVHQiSf35WXfuHVnVDaToFe9YqutHyateNb8=}")
    public String appSecret;

    @Value("${image.retrieval.orgCode:ahsjkzx123456}")
    public String orgCode;

    @Value("${image.retrieval.publicKey:048FD20FB3DC412400C5057CDC20C91F70B795D17CD59071BF34FA0E85909AEFAB565A61AA0DF58F26F0700A89D5382295F177521B17474434EE4EE9D04D393E81}")
    public String publicKey;
}
