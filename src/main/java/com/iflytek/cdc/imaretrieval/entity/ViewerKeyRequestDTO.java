package com.iflytek.cdc.imaretrieval.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("获取viewerKey请求参数")
public class ViewerKeyRequestDTO {

    @ApiModelProperty(value = "token")
    private String token;

    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    @ApiModelProperty(value = "检查ID")
    private String examId;

    @ApiModelProperty(value = "查询医生姓名")
    private String queryDocName;

    @ApiModelProperty(value = "查询医生编码")
    private String queryDocCode;

    @ApiModelProperty(value = "查询科室编码")
    private String queryDeptCode;

    @ApiModelProperty(value = "查询科室名称")
    private String queryDeptName;
}
