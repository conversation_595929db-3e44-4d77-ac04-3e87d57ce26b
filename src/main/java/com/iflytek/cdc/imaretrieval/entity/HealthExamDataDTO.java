package com.iflytek.cdc.imaretrieval.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("检查数据")
public class HealthExamDataDTO {

    @ApiModelProperty(value = "检查ID")
    private String examId;

    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    @ApiModelProperty(value = "机构名称")
    private String orgName;

    @ApiModelProperty(value = "患者ID")
    private String patientId;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "就诊号")
    private String accessNumber;

    @ApiModelProperty(value = "就诊类型")
    private String visitType;

    @ApiModelProperty(value = "检查UID")
    private String studyUid;

    @ApiModelProperty(value = "患者年龄")
    private String patientAge;

    @ApiModelProperty(value = "性别")
    private String sexCode;

    @ApiModelProperty(value = "床号")
    private String bedNo;

    @ApiModelProperty(value = "病房号")
    private String wardNo;

    @ApiModelProperty(value = "门诊号")
    private String outpatientNo;

    @ApiModelProperty(value = "急诊号")
    private String pepatientNo;

    @ApiModelProperty(value = "住院号")
    private String hospitalizedNo;

    @ApiModelProperty(value = "检查方式")
    private String modality;

    @ApiModelProperty(value = "检查部位")
    private String checkPart;

    @ApiModelProperty(value = "检查项目名称")
    private String examProjectName;

    @ApiModelProperty(value = "标准项目名称")
    private String standardProjectName;

    @ApiModelProperty(value = "标准项目编码")
    private String standardProjectCode;

    @ApiModelProperty(value = "影像描述")
    private String imageDescription;

    @ApiModelProperty(value = "影像关键ID")
    private String imageKeyId;

    @ApiModelProperty(value = "结论")
    private String conclusion;

    @ApiModelProperty(value = "临床诊断")
    private String clinicalDiagnosis;

    @ApiModelProperty(value = "阳性")
    private String positive;

    @ApiModelProperty(value = "报告医生姓名")
    private String reportDocName;

    @ApiModelProperty(value = "审核医生姓名")
    private String auditDocName;

    @ApiModelProperty(value = "报告时间")
    private String reportDatetime;

    @ApiModelProperty(value = "审核时间")
    private String auditDatetime;

    @ApiModelProperty(value = "检查时间")
    private String checkTime;

    @ApiModelProperty(value = "影像查看路径")
    private List<String> imageViewPath;

    @ApiModelProperty(value = "简要病史")
    private String briefHistory;

    @ApiModelProperty(value = "标准项目列表")
    private Object standardProjectList;

    @ApiModelProperty(value = "身份证号")
    private String idCardNo;

    @ApiModelProperty(value = "用户操作ID")
    private String userOperateId;

    @ApiModelProperty(value = "同城标识")
    private String sameCity;

    @ApiModelProperty(value = "是否信任")
    private Integer isTrust;

    @ApiModelProperty(value = "信任控制类型")
    private String trustControlType;

    @ApiModelProperty(value = "影像状态")
    private Integer imageStatus;
}
