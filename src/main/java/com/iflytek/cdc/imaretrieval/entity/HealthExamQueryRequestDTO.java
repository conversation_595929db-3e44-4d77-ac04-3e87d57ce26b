package com.iflytek.cdc.imaretrieval.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel("检查数据查询请求")
public class HealthExamQueryRequestDTO {

    private String archiveId;

    @ApiModelProperty(value = "token")
    private String token;

    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    @ApiModelProperty(value = "搜索类型1 历史检查数据查询；2 检查项目重复检查查询")
    private String searchType;

    @ApiModelProperty(value = "身份证号")
    private String idCardNo;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "检查开始时间")
    private String checkStartTime;

    @ApiModelProperty(value = "检查结束时间")
    private String checkEndTime;

    @ApiModelProperty(value = "检查类型 1 医学影像；2 其他检查（心电、内镜、超声和病理）")
    private String examType;

    @ApiModelProperty(value = "检查项目编码")
    private String examProjectCode;

    @ApiModelProperty(value = "查询医生姓名")
    private String queryDocName;

    @ApiModelProperty(value = "查询医生编码")
    private String queryDocCode;

    @ApiModelProperty(value = "查询科室名称")
    private String queryDeptName;

    @ApiModelProperty(value = "查询科室编码")
    private String queryDeptCode;

    @ApiModelProperty(value = "MAC地址")
    private String mac;

    @ApiModelProperty(value = "同城标识")
    private String sameCity;
}
