package com.iflytek.cdc.imaretrieval.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.poi.ss.formula.functions.T;

@Data
@ApiModel("影像服务查询响应")
public class ImageRetrievalResponseDTO {

    @ApiModelProperty(value = "响应消息")
    private String msg;

    @ApiModelProperty(value = "响应码")
    private Integer code;

    @ApiModelProperty(value = "数据列表")
    private Object datas;
}
