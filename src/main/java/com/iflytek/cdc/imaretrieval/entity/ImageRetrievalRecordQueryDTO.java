package com.iflytek.cdc.imaretrieval.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("影像调阅记录")
public class ImageRetrievalRecordQueryDTO {

    @ApiModelProperty(value = "token")
    private String token;

    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    @ApiModelProperty(value = "检查ID")
    private String examId;

    @ApiModelProperty(value = "查询医生姓名")
    private String queryDocName;

    @ApiModelProperty(value = "查询医生编码")
    private String queryDocCode;

    @ApiModelProperty(value = "调阅时间")
    private String retrivalTime;

    @ApiModelProperty(value = "调阅类型")
    private String retrivaType;

    @ApiModelProperty(value = "搜索类型")
    private String searchType;

    @ApiModelProperty(value = "用户操作ID")
    private String userOperateId;

    @ApiModelProperty(value = "数据类型")
    private String dataType;

    @ApiModelProperty(value = "MAC地址")
    private String mac;
}
