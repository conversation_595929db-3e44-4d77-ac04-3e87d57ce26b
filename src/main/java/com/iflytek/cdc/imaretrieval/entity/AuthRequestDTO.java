package com.iflytek.cdc.imaretrieval.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("鉴权请求参数")
public class AuthRequestDTO {

    @ApiModelProperty(value = "应用ID")
    private String appId;

    @ApiModelProperty(value = "应用密钥")
    private String appSecret;

    @ApiModelProperty(value = "机构编码")
    private String orgCode;
}
