package com.iflytek.cdc.edr.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

public class MsConstants {

    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum AddressTypeEnum {
        LIVING_ADDRESS("livingAddress", "病例现住址"),
        ORG_ADDRESS("orgAddress", "监测单位"),

        ;
        private String code;
        private String desc;

        AddressTypeEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum TimeTypeEnum {
        APPROVE_TIME("approveTime", "审核时间"),
        IDENTIFY_TIME("identifyTime", "识别时间"),
        VISIT_TIME("visitTime", "就诊时间");
        private String code;
        private String desc;

        TimeTypeEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum InfectClassEnum {
        NOTIFIABLE_DISEASE("01", "法定传染病"),
        OTHER_DISEASE("09", "其他传染病");

        private String code;
        private String desc;

        InfectClassEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum InfectTypeEnum {
        CLASS_A("1", "甲类传染病"),
        CLASS_B("2", "乙类传染病"),
        CLASS_C("3", "丙类传染病"),
        CLASS_OTHER("4", "其他法定管理及重点监测传染病"),
        OTHER("5", "其他传染病");

        private String code;
        private String desc;

        InfectTypeEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum InfectTransmitTypeEnum {
        RESPIRATORY("01", "呼吸道传染病"),
        ENTERIC("02", "肠道传染病"),
        NATURAL("03", "自然疫源及虫媒传染病"),
        BLOOD_BORNE("04", "血源及性传播传染病"),
        OTHER("99", "其他");

        private String code;
        private String desc;

        InfectTransmitTypeEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum OrgClassEnum {
        ENTERIC("110", "等级医院"),
        RESPIRATORY("120", "基层医疗机构");

        private String code;
        private String desc;

        OrgClassEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }


}
