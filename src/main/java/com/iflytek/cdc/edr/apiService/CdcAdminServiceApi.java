package com.iflytek.cdc.edr.apiService;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.reflect.TypeToken;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.iflytek.cdc.edr.dto.CommonMasterData;
import com.iflytek.cdc.edr.dto.LogExportReqDto;
import com.iflytek.cdc.edr.entity.TbCdcmrPathogenInfo;
import com.iflytek.cdc.edr.entity.app.TbCdcmrCheckAuthorityConfig;
import com.iflytek.cdc.edr.entity.app.TbCdcmrIllnessRecordAuthority;
import com.iflytek.cdc.edr.enums.WarningTypeCodeEnum;
import com.iflytek.cdc.edr.vo.EDRAuthResultVO;
import com.iflytek.cdc.province.enums.WarningTypeEnum;
import com.iflytek.cdc.province.model.dto.AdsMsProcessReqDTO;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.edr.entity.TbCdcAttachment;
import com.iflytek.cdc.edr.utils.ApiTool;
import com.iflytek.cdc.edr.vo.PopulationDataInfoVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.TopicConfigInfoVO;
import com.iflytek.cdc.province.model.pandemic.dto.message.MessageAddDTO;
import com.iflytek.cdc.province.model.pandemic.dto.message.MessageConfig;
import com.iflytek.cdc.province.model.vo.TreeNode;
import com.iflytek.cdc.province.vo.PermissionDiseaseInfoDataVO;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CdcAdminServiceApi {

    private static final String EXPORT_MAX_CODE = "cdc-platform-service:export-max";

    private static final int EXPORT_MAX_VALUE = 10000;

    @Resource
    private ApiTool apiTool;

    @Value("${cdc-admin-service:http://cdc-admin-service}")
    private String apiUrl;
    private static final String CSV_TASK_MAX_TIMEOUT_CODE = "cdc-platform-service:csv_task_max_timeout";
    /**
     * 默认超时 5分钟
     */
    private static final Long CSV_TASK_MAX_TIMEOUT_VALUE = 300L;
    /**
     * 通过区域编码查询
     */
    public List<PopulationDataInfoVO> listByAreaCodes(Date statDate,
                                                      List<String> provinceCodes,
                                                      List<String> cityCodes,
                                                      List<String> districtCodes){
        Map<String, Object> params = new HashMap<>();
        params.put("provinceCodes", provinceCodes);
        params.put("cityCodes", cityCodes);
        params.put("districtCodes", districtCodes);
        params.put("statDate", statDate);

        String url = apiUrl + "/v2/pt/populationDataInfo/listByAreaCodes";
        return apiTool.doPostList(url, params, PopulationDataInfoVO.class);
    }
    /**
     * 通过区域编码查询人口（包含街道）
     */
    public List<PopulationDataInfoVO> listByAreaCodesIncludeStreet(Date statDate,
                                                                   List<String> provinceCodes,
                                                                   List<String> cityCodes,
                                                                   List<String> districtCodes,
                                                                   List<String> streetCodes) {
        Map<String, Object> params = new HashMap<>();
        params.put("provinceCodes", provinceCodes);
        params.put("cityCodes", cityCodes);
        params.put("districtCodes", districtCodes);
        params.put("streetCodes", streetCodes);

        params.put("statDate", statDate);
        String url = apiUrl + "/v2/pt/populationDataInfo/listByEachAreaCode";
        return apiTool.doPostList(url, params, PopulationDataInfoVO.class);
    }

    /**
     * 通过区域编码查询
     */
    public Map<String, PopulationDataInfoVO> groupByAreaCodes(Date date,
                                                              List<String> provinceCodes,
                                                              List<String> cityCodes,
                                                              List<String> districtCodes){
        return listByAreaCodes(date, provinceCodes, cityCodes, districtCodes).stream().collect(Collectors.toMap(
                PopulationDataInfoVO::getAreaCode,
                 p -> p
        ));
    }
    /**
     * 通过区域编码查询（包含街道）
     */
    public Map<String, PopulationDataInfoVO> groupByAreaCodesIncludeStreet(Date date,
                                                               List<String> provinceCodes,
                                                               List<String> cityCodes,
                                                               List<String> districtCodes,
                                                                   List<String> streetCodes){
        return listByAreaCodesIncludeStreet(date, provinceCodes, cityCodes, districtCodes,streetCodes).stream().collect(Collectors.toMap(
                PopulationDataInfoVO::getAreaCode,
                p -> p
        ));
    }

    /**
     * 通过编码查询并总计
     */
    public PopulationDataInfoVO statByAreaCodes(Date statDate,
                                                List<String> provinceCodes,
                                                List<String> cityCodes,
                                                List<String> districtCodes){
        Map<String, Object> params = new HashMap<>();
        params.put("provinceCodes", provinceCodes);
        params.put("cityCodes", cityCodes);
        params.put("districtCodes", districtCodes);
        params.put("statDate", statDate);

        String url = apiUrl + "/v2/pt/populationDataInfo/statByAreaCodes";
        PopulationDataInfoVO populationDataInfoVO = apiTool.doPost(url, params, PopulationDataInfoVO.class);
        return populationDataInfoVO == null ? new PopulationDataInfoVO() : populationDataInfoVO;
    }


    public List<PopulationDataInfoVO> listByAreaCodesDates(List<Date> dateList,
                                                           List<String> provinceCodes,
                                                           List<String> cityCodes,
                                                           List<String> districtCodes) {
        Map<String, Object> params = new HashMap<>();
        params.put("dateList", dateList);
        params.put("provinceCodes", provinceCodes);
        params.put("cityCodes", cityCodes);
        params.put("districtCodes", districtCodes);

        String url = apiUrl + "/v2/pt/populationDataInfo/listByAreaCodesAndTime";
        return apiTool.doPostList(url, params, PopulationDataInfoVO.class);
    }

    public PageInfo<TbCdcmrExportTask> queryExportTaskList(ExportTaskQueryDTO queryDTO) {
        String url = apiUrl + "/pt/v2/exportTask/queryList?loginUserId="+queryDTO.getLoginUserId();
        PageInfo pageInfo = apiTool.doPost(url, queryDTO, PageInfo.class);
        List list = pageInfo.getList();
        PageInfo<TbCdcmrExportTask> resultPage = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo,resultPage);
        List<TbCdcmrExportTask> result = new ArrayList<>();
        for (Object object : list) {
            String s = JSON.toJSONString(object);
            TbCdcmrExportTask vo = JSONObject.parseObject(s, TbCdcmrExportTask.class);
            result.add(vo);
        }
        resultPage.setList(result);
        return resultPage;
    }

    public TbCdcAttachment saveAttachmentRecord(TbCdcAttachment tbCdcAttachment) {
        String url = apiUrl + "/pt/v2/fileManage/saveAttachmentRecord";
        return apiTool.doPost(url, tbCdcAttachment, TbCdcAttachment.class);
    }

    public TbCdcmrExportTask checkExistTask(ExportTaskDTO taskDTO, String loginUserId) {
        String url = apiUrl + "/pt/v2/exportTask/checkExistTask?loginUserId="+loginUserId;
        return apiTool.doPost(url, taskDTO, TbCdcmrExportTask.class);
    }
    public Boolean checkExportTaskCount( String loginUserId) {
        String url = apiUrl + "/pt/v2/exportTask/checkExportTaskCount?loginUserId="+loginUserId;
        Boolean countFlag = apiTool.doPost(url,null, Boolean.class);
        if (!countFlag){
            throw new MedicalBusinessException("任务排队中，请稍后重试");
        }
        return countFlag;
    }
    public TbCdcmrExportTask addExportTask(ExportTaskDTO taskDTO, String loginUserId) {
        String url = apiUrl + "/pt/v2/exportTask/add?loginUserId="+loginUserId;
        return apiTool.doPost(url, taskDTO, TbCdcmrExportTask.class);
    }

    public Boolean updateExportTaskById(TbCdcmrExportTask exportTask) {
        String url = apiUrl + "/pt/v2/exportTask/updateById";
        return apiTool.doPost(url, exportTask, Boolean.class);
    }

    public Long getMaxCsvTimeoutTask() {
        final String configValue = getCdcAdminServiceConfigValue(null, CSV_TASK_MAX_TIMEOUT_CODE);
        try {
            return Long.valueOf(configValue);
        } catch (Exception e) {
            log.error("返回值有误#{}", configValue, e);
        }
        return CSV_TASK_MAX_TIMEOUT_VALUE;
    }
    public String getCdcAdminServiceConfigValue(String orgId, String configCode) {
        String url = apiUrl + "/v1/pt/param/config/paramInfoByCode";
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("orgId", orgId);
        requestMap.put("configCode", configCode);
        String configValue = null;

        try {
            configValue = Objects.requireNonNull(apiTool.doPost(url, requestMap, JSONObject.class)).getString("configValue");
        } catch (Exception e) {
            log.error("调用基础管理子系统获取配置出错,orgId:" + orgId + ",configCode:" + configCode, e);
        }
        return configValue;
    }

    public List<String> getSyndromeDiseaseCodeByName(String diseaseName){

        String url = apiUrl + "/pt/v1/syndromeMonitor/getSyndromeDiseaseCodeByName?diseaseName=" + diseaseName;
        List<String> code = new ArrayList<>();
        try {
            code = Objects.requireNonNull(Arrays.asList(apiTool.doGet(url, String[].class)));
        }catch (Exception e) {
            log.error("调用基础管理子系统获取疾病code出错", e);
        }
        return code;
    }
    public void setSyndromeCode(AdsMsProcessReqDTO reqDTO) {
        if (WarningTypeCodeEnum.SYNDROME.getName().equals(reqDTO.getWarningType())){
            List<String> codes = this.getSyndromeDiseaseCodeByName(reqDTO.getDiseaseName());
            if (!codes.isEmpty()) {
                reqDTO.setSyndromeCode(codes.get(0));
            }
        }
    }

    public List<TreeNode> getInfectedInfo(String infectClassCode){

        String url = apiUrl + "/pt/v1/dim/getInfectedInfo";
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("infectClassCode", infectClassCode);
        List<TreeNode> infectedInfoList = new ArrayList<>();
        try {
            infectedInfoList = Objects.requireNonNull(Arrays.asList(apiTool.doPost(url, requestMap,  TreeNode[].class)));
        }catch (Exception e) {
            log.error("调用基础管理子系统获取传染病列表出错", e);
        }
        return infectedInfoList;
    }

    public List<TreeNode> getSyndromeInfo(){

        String url = apiUrl + "/pt/v1/dim/getSyndromeInfo";
        List<TreeNode> syndromeInfoList = new ArrayList<>();
        try {
            syndromeInfoList = Objects.requireNonNull(Arrays.asList(apiTool.doPost(url, null,  TreeNode[].class)));
        }catch (Exception e) {
            log.error("调用基础管理子系统获取症候群列表出错", e);
        }
        return syndromeInfoList;
    }

    public List<TreeNode> getInfectedInitDiagnose() {

        List<TreeNode> res = new ArrayList<>();
        try {
            String infectedUrl = apiUrl + "/pt/v1/dim/getInfectedInfo";
            String emergingUrl = apiUrl + "/pt/v1/dim/getEmergingInfo";
            List<TreeNode> infectedInfoList = Arrays.asList(apiTool.doPost(infectedUrl, null,  TreeNode[].class));
            List<TreeNode> emergingInfoList = Arrays.asList(apiTool.doPost(emergingUrl, null,  TreeNode[].class));
            if (CollUtil.isNotEmpty(infectedInfoList)) {
                res.addAll(infectedInfoList);
            }
            if (CollUtil.isNotEmpty(emergingInfoList)) {
                TreeNode root = TreeNode.from(WarningTypeEnum.EMERGING.getCode(), WarningTypeEnum.EMERGING.getDesc(), WarningTypeEnum.EMERGING.getCode());
                root.setChildren(emergingInfoList);
                res.add(root);
            }
        }catch (Exception e) {
            log.error("调用基础管理子系统获取列表出错", e);
        }
        return res;
    }

    public String saveOperationLog(LogExportReqDto queryVO, String loginUserId) {
        String url = apiUrl + "/v1/pt/operationLog/save?loginUserId="+loginUserId;
        return apiTool.doPost(url, queryVO, String.class);
    }

    public String logExportUpdate(LogExportReqDto queryVO, String loginUserId) {
        String url = apiUrl + "/v1/pt/logExport/update?loginUserId=" + loginUserId;
        return apiTool.doPost(url, queryVO, String.class);
    }

    public int getExportMax(){
        final String configValue = getCdcAdminServiceConfigValue(null, EXPORT_MAX_CODE);
        try {
            return Integer.valueOf(configValue);
        } catch (Exception e){
            log.error("返回值有误#{}",configValue,e);
        }
        return EXPORT_MAX_VALUE;
    }
    public void checkExportMax(int count) {
        int exportMax = getExportMax();
        if (count > exportMax) {
            throw new MedicalBusinessException("最多导出" + exportMax + "条数据");
        }

    }

    public void checkExportMax(Collection collection){
        int exportMax = getExportMax();
        if (collection != null && collection.size() > exportMax) {
            throw new MedicalBusinessException("最多导出" + exportMax + "条数据");
        }

    }

    public String getStatusByCodeAndGroup(String configCode, String configGroup){
        String url = apiUrl + "/v1/pt/param/config/getStatusByCodeAndGroup?configCode=" + configCode + "&configGroup=" + configGroup;
        try {
            return apiTool.doGet(url,String.class);
        } catch (Exception e) {
            log.error("调用基础管理子系统获取开关状态出错", e);
        }
        return null;
    }

    /**
     * 获取主数据级联信息
     * */
    public List<TreeNode> getMasterDataInfo(String masterDataType) {
        Map<String, String> param = new HashMap<>();
        param.put("masterDataType", masterDataType);

        String url = apiUrl + "/pt/v1/masterData/getMasterData";
        try {
            return Arrays.asList(apiTool.doPost(url, param, TreeNode[].class));
        } catch (Exception e) {
            log.error("调用基础管理子系统获取病原信息出错", e);
        }
        return new ArrayList<>();
    }

    /**
     * 获取病原信息
     * */
    public List<TbCdcmrPathogenInfo> getPathogenInfo() {

        String url = apiUrl + "/pt/v1/multichannelTopic/getPathogenInfo";
        try {
            return Arrays.asList(apiTool.doGet(url, TbCdcmrPathogenInfo[].class));
        } catch (Exception e) {
            log.error("调用基础管理子系统获取病原信息出错", e);
        }
        return new ArrayList<>();
    }

    /**
     * 获取专题的配置信息
     * */
    public TopicConfigInfoVO getTopicConfig(String topicId) {

        String url = apiUrl + "/pt/v1/multichannelTopic/getTopicConfig?topicId=" + topicId;
        try {
            return apiTool.doPost(url, null, TopicConfigInfoVO.class);
        } catch (Exception e) {
            log.error("调用基础管理子系统获取专题信息出错", e);
        }
        return null;
    }

    /**
     * 根据登录人获取权限相关信息
     * */
    public Map<String, List<TbCdcmrIllnessRecordAuthority>> getUserAuthInfo(String userId) {

        String url = apiUrl + "/pt/v1/edr/auth/getUserAuthInfo?userId=" + userId;
        try {
            Map<String, List<TbCdcmrIllnessRecordAuthority>> authorityMap = apiTool.doGet(url, Map.class);
            Gson gson = new Gson();
            authorityMap.entrySet().forEach(e -> {
                List<TbCdcmrIllnessRecordAuthority> list = gson.fromJson(gson.toJson(e.getValue()), new TypeToken<List<TbCdcmrIllnessRecordAuthority>>(){}.getType());
                e.setValue(list);
            });
            return authorityMap;
        } catch (Exception e) {
            log.error("调用基础管理子系统获用户权限信息出错", e);
        }
        return null;
    }

    /**
     * 根据登录人获取权限相关信息
     * */
    public List<TbCdcmrCheckAuthorityConfig> getCheckConfigByLoginUser(String loginUserId,
                                                                       String diseaseType) {

        String url = apiUrl + "/pt/v1/processCheckConfig/getCheckConfigByLoginUser?loginUserId=" + loginUserId + "&diseaseType=" + diseaseType;
        try {
            return Arrays.asList(apiTool.doGet(url, TbCdcmrCheckAuthorityConfig[].class));
        } catch (Exception e) {
            log.error("调用基础管理子系统获用户审核配置信息出错", e);
        }
        return new ArrayList<>();
    }

    /**
     * 通过code获取疾病信息
     */
    public CommonMasterData getDiseaseInfoByCode(String masterDataType, String masterDataCode) {
        String url = apiUrl + "/pt/v1/masterData/getDiseaseInfoByCode?masterDataType=" + masterDataType + "&masterDataCode=" + masterDataCode;
        return apiTool.doGet(url, CommonMasterData.class);
    }

    /**
     * 获取个人疾病档案查看权限
     */
    public EDRAuthResultVO validViewAuth(String loginUserId,Map<String, String> map) {
        String url = apiUrl + "/pt/v1/edr/auth/validViewAuth?loginUserId=" + loginUserId;
        try {
            return apiTool.doPost(url, map, EDRAuthResultVO.class);
        } catch (Exception e) {
            log.error("获取个人疾病档案查看权限", e);
        }
        return null;
    }

    /**
     * 保存系统消息
     */
    public String saveMessage(MessageAddDTO dto) {
        String url = apiUrl+"/v2/pb/message/save";
        return apiTool.doPost(url, dto, String.class);
    }

    /**
     * 获取消息配置ById
     */
    public MessageConfig getMessageConfigById(String id) {
        String url =  apiUrl+"/v2/pb/message/getConfigById?id=" + id;
        return apiTool.doGet(url, MessageConfig.class);
    }

    public PermissionDiseaseInfoDataVO queryUserDiseasePermission(String loginUserId, String moduleType){
        String url = String.format("%s//pt/v1/bizProcess/permission/queryPermissionDiseaseInfoData?loginUserIdd=%s&subSystemCode=%s",apiUrl,loginUserId,moduleType);

        return apiTool.doGet(url, PermissionDiseaseInfoDataVO.class);
    }
}
