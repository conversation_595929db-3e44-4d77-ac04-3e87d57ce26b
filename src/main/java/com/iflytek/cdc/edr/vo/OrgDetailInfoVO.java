package com.iflytek.cdc.edr.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 机构详细信息VO
 */
@Data
public class OrgDetailInfoVO {

    @ApiModelProperty("机构编码")
    private String orgId;

    @ApiModelProperty("机构名称")
    private String orgName;

    @ApiModelProperty("机构类型")
    private String orgType;

    @ApiModelProperty("根机构编码")
    private String rootOrgId;

    @ApiModelProperty("根机构名称")
    private String rootOrgName;

    @ApiModelProperty("管辖机构编码")
    private String manaOrgId;

    @ApiModelProperty("管辖机构名称")
    private String manaOrgName;

    @ApiModelProperty("对接状态")
    private String inStatus;

    @ApiModelProperty("省编码")
    private String provinceCode;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("市编码")
    private String cityCode;

    @ApiModelProperty("市名称")
    private String cityName;

    @ApiModelProperty("区县编码")
    private String districtCode;

    @ApiModelProperty("区县名称")
    private String districtName;

    @ApiModelProperty("功能区编码")
    private String funcDistrictCode;

    @ApiModelProperty("功能区名称")
    private String funcDistrictName;

    @ApiModelProperty("街道编码")
    private String streetCode;

    @ApiModelProperty("街道名称")
    private String streetName;
} 