package com.iflytek.cdc.edr.vo.adsMsProcess;

import cn.hutool.core.lang.Pair;
import com.iflytek.cdc.edr.utils.DataUtils;
import com.iflytek.cdc.edr.vo.CommonDateStatVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * 传染病统计指标定义父类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("传染病统计指标定义")
public class MsProcessStatVO extends CommonDateStatVO {

    @ApiModelProperty("识别病例数")
    private Integer identifyCnt;
    @ApiModelProperty("识别病例数-七日平均")
    private Integer identifyCntAvgS;
    @ApiModelProperty("识别病例数-上期")
    private Integer identifyCntLast;
    @ApiModelProperty("识别病例数-同期")
    private Integer identifyCntLastY;
    @ApiModelProperty("识别病例数-同比")
    private String identifyCntYearGrowth;
    @ApiModelProperty("识别病例数-环比")
    private String identifyCntChain;

    @ApiModelProperty("新发病例数")
    private Integer processNewCnt;
    @ApiModelProperty("新发病例数-七日平均")
    private Integer processNewCntAvgS;
    @ApiModelProperty("新发病例数-上期")
    private Integer processNewCntLast;
    @ApiModelProperty("新发病例数-同期")
    private Integer processNewCntLastY;
    @ApiModelProperty("新发病例数-同比")
    private String processNewCntYearGrowth;
    @ApiModelProperty("新发病例数-环比")
    private String processNewCntChain;
    @ApiModelProperty("新发病例数-预测值")
    private Integer processNewCntPre;

    @ApiModelProperty("死亡病例数")
    private Integer processDeadCnt;
    @ApiModelProperty("死亡病例数-七日平均")
    private Integer processDeadCntAvgS;
    @ApiModelProperty("死亡病例数-上期")
    private Integer processDeadCntLast;
    @ApiModelProperty("死亡病例数-同期")
    private Integer processDeadCntLastY;
    @ApiModelProperty("死亡病例数-同比")
    private String processDeadCntYearGrowth;
    @ApiModelProperty("死亡病例数-环比")
    private String processDeadCntChain;

    @ApiModelProperty("现有病例数(患病数)-本期")
    private Integer processNowCnt;
    @ApiModelProperty("现有病例数-七日平均")
    private Integer processNowCntAvgS;
    @ApiModelProperty("现有病例数-上期")
    private Integer processNowCntLast;
    @ApiModelProperty("现有病例数-同期")
    private Integer processNowCntLastY;
    @ApiModelProperty("现有病例数-同比")
    private String processNowCntYearGrowth;
    @ApiModelProperty("现有病例数-环比")
    private String processNowCntChain;

    @ApiModelProperty("病死数-本期")
    private Integer processDeadIllnessCnt;
    @ApiModelProperty("病死数-七日平均")
    private Integer processDeadIllnessCntAvgS;
    @ApiModelProperty("病死数-上期")
    private Integer processDeadIllnessCntLast;
    @ApiModelProperty("病死数-同期")
    private Integer processDeadIllnessCntLastY;
    @ApiModelProperty("病死数-同比")
    private String processDeadIllnessCntYearGrowth;
    @ApiModelProperty("病死数-环比")
    private String processDeadIllnessCntChain;

    @ApiModelProperty("治愈成功病例数")
    private Integer processCureCnt;
    @ApiModelProperty("治愈成功病例数-七日平均")
    private Integer processCureCntAvgS;
    @ApiModelProperty("治愈成功病例数-上期")
    private Integer processCureCntLast;
    @ApiModelProperty("治愈成功病例数-同期")
    private Integer processCureCntLastY;
    @ApiModelProperty("治愈成功病例数-同比")
    private String processCureCntYearGrowth;
    @ApiModelProperty("治愈成功病例数-环比")
    private String processCureCntChain;

    @ApiModelProperty("重症病例数")
    private Integer processSevereCnt;
    @ApiModelProperty("重症病例数-七日平均")
    private Integer processSevereCntAvgS;
    @ApiModelProperty("重症病例数-上期")
    private Integer processSevereCntLast;
    @ApiModelProperty("重症病例数-同期")
    private Integer processSevereCntLastY;
    @ApiModelProperty("重症病例数-同比")
    private String processSevereCntYearGrowth;
    @ApiModelProperty("重症病例数-环比")
    private String processSevereCntChain;

    @ApiModelProperty("出院病例数")
    private Integer processDischargedCasesCnt;
    @ApiModelProperty("治愈成功病例数-七日平均")
    private Integer processDischargedCasesCntAvgS;
    @ApiModelProperty("出院病例数-上期")
    private Integer processDischargedCasesCntLast;
    @ApiModelProperty("出院病例数-同期")
    private Integer processDischargedCasesCntLastY;
    @ApiModelProperty("出院病例数-同比")
    private String processDischargedCasesCntGrowth;
    @ApiModelProperty("出院病例数-环比")
    private String processDischargedCasesCntChain;
    
    @ApiModelProperty("病毒检测病例数")
    private Integer processVirusCnt;
    @ApiModelProperty("病毒检测病例数-上期")
    private Integer processVirusCntLast;
    @ApiModelProperty("病毒检测病例数-同期")
    private Integer processVirusCntLastY;
    @ApiModelProperty("病毒检测病例数-同比")
    private String processVirusCntYearGrowth;
    @ApiModelProperty("病毒检测病例数-环比")
    private String processVirusCntChain;

    @ApiModelProperty("病毒检测阳性病例数")
    private Integer processVirusPositiveCnt;
    @ApiModelProperty("病毒检测阳性病例数-上期")
    private Integer processVirusPositiveCntLast;
    @ApiModelProperty("病毒检测阳性病例数-同期")
    private Integer processVirusPositiveCntLastY;
    @ApiModelProperty("病毒检测阳性病例数-同比")
    private String processVirusPositiveCntYearGrowth;
    @ApiModelProperty("病毒检测阳性病例数-环比")
    private String processVirusPositiveCntChain;

    @ApiModelProperty("细菌检测病例数")
    private Integer processBacteriaCnt;
    @ApiModelProperty("细菌检测病例数-上期")
    private Integer processBacteriaCntLast;
    @ApiModelProperty("细菌检测病例数-同期")
    private Integer processBacteriaCntLastY;
    @ApiModelProperty("细菌检测病例数-同比")
    private String processBacteriaCntYearGrowth;
    @ApiModelProperty("细菌检测病例数-环比")
    private String processBacteriaCntChain;

    @ApiModelProperty("细菌检测阳性病例数")
    private Integer processBacteriaPositiveCnt;
    @ApiModelProperty("细菌检测阳性病例数-上期")
    private Integer processBacteriaPositiveCntLast;
    @ApiModelProperty("细菌检测阳性病例数-同期")
    private Integer processBacteriaPositiveCntLastY;
    @ApiModelProperty("细菌检测阳性病例数-同比")
    private String processBacteriaPositiveCntYearGrowth;
    @ApiModelProperty("细菌检测阳性病例数-环比")
    private String processBacteriaPositiveCntChain;
    
    @ApiModelProperty("病毒合并细菌感染病例数")
    private Integer processBacteriaVirusCnt;
    @ApiModelProperty("病毒合并细菌感染病例数-上期")
    private Integer processBacteriaVirusCntLast;
    @ApiModelProperty("病毒合并细菌感染病例数-同期")
    private Integer processBacteriaVirusCntLastY;
    @ApiModelProperty("病毒合并细菌感染病例数-同比")
    private String processBacteriaVirusCntYearGrowth;
    @ApiModelProperty("病毒合并细菌感染病例数-环比")
    private String processBacteriaVirusCntChain;

    @ApiModelProperty("预警值")
    private Integer prewarningValue;

    /**
     * 将统计单项（本对象）添加至合计项
     *
     * @param amount 合计项
     */
    public void amountTo(MsProcessStatVO amount) {
        amount.setIdentifyCnt(DataUtils.sum(amount.getIdentifyCnt(), this.getIdentifyCnt()));
        amount.setIdentifyCntAvgS(DataUtils.sum(amount.getIdentifyCntAvgS(), this.getIdentifyCntAvgS()));
        amount.setIdentifyCntLast(DataUtils.sum(amount.getIdentifyCntLast(), this.getIdentifyCntLast()));
        amount.setIdentifyCntLastY(DataUtils.sum(amount.getIdentifyCntLastY(), this.getIdentifyCntLastY()));
        amount.setProcessNewCnt(DataUtils.sum(amount.getProcessNewCnt(), this.getProcessNewCnt()));
        amount.setProcessNewCntAvgS(DataUtils.sum(amount.getProcessNewCntAvgS(), this.getProcessNewCntAvgS()));
        amount.setProcessNewCntLast(DataUtils.sum(amount.getProcessNewCntLast(), this.getProcessNewCntLast()));
        amount.setProcessNewCntLastY(DataUtils.sum(amount.getProcessNewCntLastY(), this.getProcessNewCntLastY()));
        amount.setProcessNewCntPre(DataUtils.sum(amount.getProcessNewCntPre(), this.getProcessNewCntPre()));
        amount.setProcessDeadCnt(DataUtils.sum(amount.getProcessDeadCnt(), this.getProcessDeadCnt()));
        amount.setProcessDeadCntAvgS(DataUtils.sum(amount.getProcessDeadCntAvgS(), this.getProcessDeadCntAvgS()));
        amount.setProcessDeadCntLast(DataUtils.sum(amount.getProcessDeadCntLast(), this.getProcessDeadCntLast()));
        amount.setProcessDeadCntLastY(DataUtils.sum(amount.getProcessDeadCntLastY(), this.getProcessDeadCntLastY()));
        amount.setProcessNowCnt(DataUtils.sum(amount.getProcessNowCnt(), this.getProcessNowCnt()));
        amount.setProcessNowCntAvgS(DataUtils.sum(amount.getProcessNowCntAvgS(), this.getProcessNowCntAvgS()));
        amount.setProcessNowCntLast(DataUtils.sum(amount.getProcessNowCntLast(), this.getProcessNowCntLast()));
        amount.setProcessNowCntLastY(DataUtils.sum(amount.getProcessNowCntLastY(), this.getProcessNowCntLastY()));
        amount.setProcessDeadIllnessCnt(DataUtils.sum(amount.getProcessDeadIllnessCnt(), this.getProcessDeadIllnessCnt()));
        amount.setProcessDeadIllnessCntAvgS(DataUtils.sum(amount.getProcessDeadIllnessCntAvgS(), this.getProcessDeadIllnessCntAvgS()));
        amount.setProcessDeadIllnessCntLast(DataUtils.sum(amount.getProcessDeadIllnessCntLast(), this.getProcessDeadIllnessCntLast()));
        amount.setProcessDeadIllnessCntLastY(DataUtils.sum(amount.getProcessDeadIllnessCntLastY(), this.getProcessDeadIllnessCntLastY()));
        amount.setProcessCureCnt(DataUtils.sum(amount.getProcessCureCnt(), this.getProcessCureCnt()));
        amount.setProcessCureCntAvgS(DataUtils.sum(amount.getProcessCureCntAvgS(), this.getProcessCureCntAvgS()));
        amount.setProcessCureCntLast(DataUtils.sum(amount.getProcessCureCntLast(), this.getProcessCureCntLast()));
        amount.setProcessCureCntLastY(DataUtils.sum(amount.getProcessCureCntLastY(), this.getProcessCureCntLastY()));
        amount.setProcessSevereCnt(DataUtils.sum(amount.getProcessSevereCnt(), this.getProcessSevereCnt()));
        amount.setProcessSevereCntAvgS(DataUtils.sum(amount.getProcessSevereCntAvgS(), this.getProcessSevereCntAvgS()));
        amount.setProcessDischargedCasesCntAvgS(DataUtils.sum(amount.getProcessDischargedCasesCntAvgS(), this.getProcessDischargedCasesCntAvgS()));
        amount.setProcessSevereCntLast(DataUtils.sum(amount.getProcessSevereCntLast(), this.getProcessSevereCntLast()));
        amount.setProcessSevereCntLastY(DataUtils.sum(amount.getProcessSevereCntLastY(), this.getProcessSevereCntLastY()));
        amount.setProcessVirusCnt(DataUtils.sum(amount.getProcessVirusCnt(), this.getProcessVirusCnt()));
        amount.setProcessVirusCntLast(DataUtils.sum(amount.getProcessVirusCntLast(), this.getProcessVirusCntLast()));
        amount.setProcessVirusCntLastY(DataUtils.sum(amount.getProcessVirusCntLastY(), this.getProcessVirusCntLastY()));
        amount.setProcessVirusPositiveCnt(DataUtils.sum(amount.getProcessVirusPositiveCnt(), this.getProcessVirusPositiveCnt()));
        amount.setProcessVirusPositiveCntLast(DataUtils.sum(amount.getProcessVirusPositiveCntLast(), this.getProcessVirusPositiveCntLast()));
        amount.setProcessVirusPositiveCntLastY(DataUtils.sum(amount.getProcessVirusPositiveCntLastY(), this.getProcessVirusPositiveCntLastY()));
        amount.setProcessBacteriaCnt(DataUtils.sum(amount.getProcessBacteriaCnt(), this.getProcessBacteriaCnt()));
        amount.setProcessBacteriaCntLast(DataUtils.sum(amount.getProcessBacteriaCntLast(), this.getProcessBacteriaCntLast()));
        amount.setProcessBacteriaCntLastY(DataUtils.sum(amount.getProcessBacteriaCntLastY(), this.getProcessBacteriaCntLastY()));
        amount.setProcessBacteriaPositiveCnt(DataUtils.sum(amount.getProcessBacteriaPositiveCnt(), this.getProcessBacteriaPositiveCnt()));
        amount.setProcessBacteriaPositiveCntLast(DataUtils.sum(amount.getProcessBacteriaPositiveCntLast(), this.getProcessBacteriaPositiveCntLast()));
        amount.setProcessBacteriaPositiveCntLastY(DataUtils.sum(amount.getProcessBacteriaPositiveCntLastY(), this.getProcessBacteriaPositiveCntLastY()));
        amount.setProcessBacteriaVirusCnt(DataUtils.sum(amount.getProcessBacteriaVirusCnt(), this.getProcessBacteriaVirusCnt()));
        amount.setProcessBacteriaVirusCntLast(DataUtils.sum(amount.getProcessBacteriaVirusCntLast(), this.getProcessBacteriaVirusCntLast()));
        amount.setProcessBacteriaVirusCntLastY(DataUtils.sum(amount.getProcessBacteriaVirusCntLastY(), this.getProcessBacteriaVirusCntLastY()));
    }

}
