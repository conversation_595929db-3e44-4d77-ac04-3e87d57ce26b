package com.iflytek.cdc.edr.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 机构对接状态分组VO
 */
@Data
public class InOrgStatusTtlVO {

    @ApiModelProperty("已对接的机构列表")
    private List<OrgDetailInfoVO> connectedOrgs;

    @ApiModelProperty("未对接的机构列表")
    private List<OrgDetailInfoVO> unconnectedOrgs;

    @ApiModelProperty("正在对接的机构列表")
    private List<OrgDetailInfoVO> connectingOrgs;

    @ApiModelProperty("不对接的机构列表")
    private List<OrgDetailInfoVO> notConnectingOrgs;

    @ApiModelProperty("已对接机构总数")
    private Integer connectedCount;

    @ApiModelProperty("未对接机构总数")
    private Integer unconnectedCount;

    @ApiModelProperty("正在对接机构总数")
    private Integer connectingCount;

    @ApiModelProperty("不对接机构总数")
    private Integer notConnectingCount;

    @ApiModelProperty("总机构数")
    private Integer totalCount;

    /**
     * 计算各状态机构数量
     */
    public void calculateCounts() {
        this.connectedCount = connectedOrgs != null ? connectedOrgs.size() : 0;
        this.unconnectedCount = unconnectedOrgs != null ? unconnectedOrgs.size() : 0;
        this.connectingCount = connectingOrgs != null ? connectingOrgs.size() : 0;
        this.notConnectingCount = notConnectingOrgs != null ? notConnectingOrgs.size() : 0;
        this.totalCount = connectedCount + unconnectedCount + connectingCount + notConnectingCount;
    }
} 