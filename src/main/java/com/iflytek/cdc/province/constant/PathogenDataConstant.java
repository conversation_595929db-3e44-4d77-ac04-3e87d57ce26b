package com.iflytek.cdc.province.constant;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class PathogenDataConstant {

    @ApiModelProperty(value = "病原类型")
    private final Object pathogenClass = PathogenDataConstant.PathogenClassEnum.values();

    @ApiModelProperty(value = "对比维度")
    private final Object compareDim = PathogenDataConstant.CompareDimTypeEnum.values();

    @ApiModelProperty(value = "排序指标")
    private final Object orderIndex = PathogenDataConstant.OrderIndexEnum.values();

    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum PathogenClassEnum {

        VIRUS("01", "病毒"),

        BACTERIA("02", "细菌"),

        FUNGUS("03", "真菌"),

        OTHER("09", "其他"),

        ;

        private final String code;
        private final String desc;

        PathogenClassEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getDescByCode(String code){
            PathogenClassEnum[] values = PathogenClassEnum.values();
            for (PathogenClassEnum value : values) {
                if(value.getCode().equals(code)){
                    return value.getDesc();
                }
            }
            return null;
        }

        public boolean anyEquals(List<String> pathogenClasses) {
            if (pathogenClasses == null || pathogenClasses.isEmpty()) {
                return false;
            }
            return pathogenClasses.stream().anyMatch(pathogenClass -> this.desc.equals(pathogenClass) || this.code.equals(pathogenClass));
        }
    }

    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum CompareDimTypeEnum {

        PNEUMONIA("pneumonia", "肺炎 VS 非肺炎", "肺炎", "非肺炎"),

        FEVER("fever", "中高热（体温>38℃）VS 低热（体温≤38℃）", "中高热（体温>38℃）", "低热（体温≤38℃）"),

        ;

        private final String type;
        private final String desc;
        private final String obj1;
        private final String obj2;

        CompareDimTypeEnum(String type, String desc, String obj1, String obj2) {
            this.type = type;
            this.desc = desc;
            this.obj1 = obj1;
            this.obj2 = obj2;
        }

        public boolean typeEquals(String type) {
            if (type == null) {
                return false;
            }
            return this.type.equals(type);
        }
    }

    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum OrderIndexEnum {

        POSITIVE_RATIO("positiveRatio", "阳性率"),

        POSITIVE_YEAR_GROWTH("yearGrowth", "阳性率同比"),

        ;

        private final String code;
        private final String desc;

        OrderIndexEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public boolean codeEquals(String code) {
            if (code == null) {
                return false;
            }
            return this.code.equals(code);
        }
    }

    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum GradeHospitalTypeEnum {

        CHILDREN_HOSPITAL("positiveRatio", "儿童医院"),

        SECOND_GRADE_HOSPITAL("yearGrowth", "二级医院"),

        THIRD_GRADE_HOSPITAL("yearGrowth", "三级医院"),

        ;

        private final String code;
        private final String desc;

        GradeHospitalTypeEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    @Getter
    public enum clueTypeEnum {

        DRUG_SALES("药品销量暴增"),
        SEARCH_INDEX("搜索指数暴增"),
        CONSULT_INDEX("资讯指数暴增"),
        TRENDING_TOPIC("热门话题上榜"),
        IMPORT_CASE("输入病例公告");

        private final String name;

        clueTypeEnum(String name) {
            this.name = name;
        }
        
        public static List<String> types() {
            return Arrays.stream(clueTypeEnum.values()).map(clueTypeEnum::getName).collect(Collectors.toList());
        }
    }
}
