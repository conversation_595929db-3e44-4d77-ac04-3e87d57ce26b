package com.iflytek.cdc.province.constant;

import com.iflytek.cdc.province.enums.CheckIdentifyStatusEnum;
import com.iflytek.cdc.province.enums.CheckProcessStatusEnum;
import com.iflytek.cdc.province.enums.CheckResultEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PandemicMonitorDataConstant {

    @ApiModelProperty(value = "审核识别状态")
    private final Object checkIdentifyStatus = CheckIdentifyStatusEnum.mapValues();

    @ApiModelProperty(value = "审核流程状态")
    private final Object checkProcessStatus = CheckProcessStatusEnum.mapValues();

    @ApiModelProperty(value = "审核结果")
    private final Object checkResult = CheckResultEnum.mapValues();

}
