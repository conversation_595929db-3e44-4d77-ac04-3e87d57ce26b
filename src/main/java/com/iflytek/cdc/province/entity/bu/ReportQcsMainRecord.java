package com.iflytek.cdc.province.entity.bu;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tb_cdcew_report_qcs_main_record", schema = "app")
@ApiModel(value = "ReportQcsMainRecord对象", description = "")
public class ReportQcsMainRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "创建者id")
    private String creatorId;

    @ApiModelProperty(value = "创建者")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改者id")
    private String updaterId;

    @ApiModelProperty(value = "更新者")
    private String updater;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "删除标志，'1' 表示已删除，'0' 表示未删除")
    private String deleteFlag;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "数据名称")
    private String title;

    @ApiModelProperty(value = "导入数据量")
    private Integer importAmount;

    @ApiModelProperty(value = "比对日期类型 onsetTime 发病日期 diagTime 诊断日期 visitTime 首次就诊时间	")
    private String compareDateType;

    @ApiModelProperty(value = "比对数据时间范围-开始")
    private Date compareStartDate;

    @ApiModelProperty(value = "比对数据时间范围-结束")
    private Date compareStartEnd;

    @ApiModelProperty(value = "比对地址类型 livingAddress 现住地址 orgAddress 就诊机构地址	")
    private String compareAddrType;

    @ApiModelProperty(value = "比对地区-省编码")
    private String compareProvinceCode;

    @ApiModelProperty(value = "比对地区-省名称")
    private String compareProvinceName;

    @ApiModelProperty(value = "比对地区-市编码")
    private String compareCityCode;

    @ApiModelProperty(value = "比对地区-市名称")
    private String compareCityName;

    @ApiModelProperty(value = "比对地区-区县编码")
    private String compareDistrictCode;

    @ApiModelProperty(value = "比对地区-区县名称")
    private String compareDistrictName;

    @ApiModelProperty(value = "完成时间（比对后回填）")
    private Date finishTime;

    @ApiModelProperty(value = "漏报数（比对后回填）")
    private Integer leakAmount;

    @ApiModelProperty(value = "迟报数（比对后回填）")
    private Integer delayAmount;

    @ApiModelProperty(value = "漏报数据导出文件名")
    private String attachmentName;

    @ApiModelProperty(value = "漏报数据导出文件下载地址")
    private String attachmentId;
}
