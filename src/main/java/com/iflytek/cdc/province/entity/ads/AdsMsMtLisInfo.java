package com.iflytek.cdc.province.entity.ads;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 诊疗(medical service)-医技-检验详情表;
 * <AUTHOR> dingyuan
 * @date : 2024-5-24
 */
@ApiModel(value = "诊疗(medical service)-医技-检验详情表")
@Data
public class AdsMsMtLisInfo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 记录id（uuid）
     */
    @ApiModelProperty(value = "记录id（uuid）")
    private String id ;

    /**
     * 病历id
     */
    @ApiModelProperty(value = "病历id")
    private String medicalId ;

    /**
     * 就诊事件id
     */
    @ApiModelProperty(value = "就诊事件id")
    private String eventId ;

    /**
     * 病程id
     */
    @ApiModelProperty(value = "病程id")
    private String processId ;

    /**
     * 生命周期id
     */
    @ApiModelProperty(value = "生命周期id")
    private String lifeId ;

    /**
     * ETL创建时间
     */
    @ApiModelProperty(value = "ETL创建时间")
    private Date etlCreateDatetime ;

    /**
     * ETL更新时间
     */
    @ApiModelProperty(value = "ETL更新时间")
    private Date etlUpdateDatetime ;

    /**
     * 采样时间
     */
    @ApiModelProperty(value = "采样时间")
    private Date sampleTime ;

    /**
     * 采样部位代码
     */
    @ApiModelProperty(value = "采样部位代码")
    private String samplePartCode ;

    /**
     * 采样部位
     */
    @ApiModelProperty(value = "采样部位")
    private String samplePart ;

    /**
     * 样本id
     */
    @ApiModelProperty(value = "样本id")
    private String sampleId ;

    /**
     * 样本名称
     */
    @ApiModelProperty(value = "样本名称")
    private String sampleName ;

    /**
     * 条码号
     */
    @ApiModelProperty(value = "条码号")
    private String sampleBarcodeNo ;

    /**
     * 样本类型（临床样本、环境样本、生物样本）
     */
    @ApiModelProperty(value = "样本类型（临床样本、环境样本、生物样本）")
    private String sampleType ;

    /**
     * 样本类别代码
     */
    @ApiModelProperty(value = "样本类别代码")
    private String sampleClassCode ;

    /**
     * 样本类别（样本小类，临检、生化、免疫学等）
     */
    @ApiModelProperty(value = "样本类别（样本小类，临检、生化、免疫学等）")
    private String sampleClass ;

    /**
     * 样本性状（液态、固态、气态等）
     */
    @ApiModelProperty(value = "样本性状（液态、固态、气态等）")
    private String sampleCharacter ;

    /**
     * 收样方式（采样等）
     */
    @ApiModelProperty(value = "收样方式（采样等）")
    private String sampleWay ;

    /**
     * 样本来源类别（人源、动物源、植物源、其它）
     */
    @ApiModelProperty(value = "样本来源类别（人源、动物源、植物源、其它）")
    private String sampleSourceType ;

    /**
     * 患者（不变）信息-姓名
     */
    @ApiModelProperty(value = "患者（不变）信息-姓名")
    private String patientName ;

    /**
     * 患者（不变）信息-性别代码
     */
    @ApiModelProperty(value = "患者（不变）信息-性别代码")
    private String patientSexCode ;

    /**
     * 患者（不变）信息-性别（有字典，313001）
     */
    @ApiModelProperty(value = "患者（不变）信息-性别（有字典，313001）")
    private String patientSexName ;

    /**
     * 患者（不变）信息-证件类型编码
     */
    @ApiModelProperty(value = "患者（不变）信息-证件类型编码")
    private String patientIdentityTypeCode ;

    /**
     * 患者（不变）信息-证件类型名称
     */
    @ApiModelProperty(value = "患者（不变）信息-证件类型名称")
    private String patientIdentityType ;

    /**
     * 患者（不变）信息-身份证号/证件号码
     */
    @ApiModelProperty(value = "患者（不变）信息-身份证号/证件号码")
    private String patientIdentityNo ;

    /**
     * 患者（不变）信息-出生日期
     */
    @ApiModelProperty(value = "患者（不变）信息-出生日期")
    private Date patientBirthDay ;

    /**
     * 患者（可变）信息-人员id
     */
    @ApiModelProperty(value = "患者（可变）信息-人员id")
    private String patientId ;

    /**
     * 患者（可变）信息-年龄
     */
    @ApiModelProperty(value = "患者（可变）信息-年龄")
    private Integer patientAge ;

    /**
     * 患者（可变）信息-年龄单位
     */
    @ApiModelProperty(value = "患者（可变）信息-年龄单位")
    private String patientAgeUnit ;

    /**
     * 患者（可变）信息-患者年龄（单位岁）
     */
    @ApiModelProperty(value = "患者（可变）信息-患者年龄（单位岁）")
    private Double patientAgeStd ;

    /**
     * 就诊流水号
     */
    @ApiModelProperty(value = "就诊流水号")
    private String serialNo ;

    /**
     * 就诊类型编码
     */
    @ApiModelProperty(value = "就诊类型编码")
    private String visitTypeCode ;

    /**
     * 就诊类型
     */
    @ApiModelProperty(value = "就诊类型")
    private String visitType ;

    /**
     * 就诊时间
     */
    @ApiModelProperty(value = "就诊时间")
    private Date visitTime ;

    /**
     * 是否有传染病报卡
     */
    @ApiModelProperty(value = "是否有传染病报卡")
    private String infectReportFlag ;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private String orgId ;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String orgName ;

    /**
     * 机构类型编码
     */
    @ApiModelProperty(value = "机构类型编码")
    private String orgTypeCode ;

    /**
     * 机构类型
     */
    @ApiModelProperty(value = "机构类型")
    private String orgType ;

    /**
     * 申请-机构id
     */
    @ApiModelProperty(value = "申请-机构id")
    private String applyOrgId ;

    /**
     * 申请-机构名称
     */
    @ApiModelProperty(value = "申请-机构名称")
    private String applyOrgName ;

    /**
     * 申请-机构类型编码
     */
    @ApiModelProperty(value = "申请-机构类型编码")
    private String applyOrgTypeCode ;

    /**
     * 申请-机构类型
     */
    @ApiModelProperty(value = "申请-机构类型")
    private String applyOrgType ;

    /**
     * 申请-科室编码
     */
    @ApiModelProperty(value = "申请-科室编码")
    private String applyDeptCode ;

    /**
     * 申请-科室名称
     */
    @ApiModelProperty(value = "申请-科室名称")
    private String applyDeptName ;

    /**
     * 申请-申请人编码
     */
    @ApiModelProperty(value = "申请-申请人编码")
    private String applyDocId ;

    /**
     * 申请-申请人姓名
     */
    @ApiModelProperty(value = "申请-申请人姓名")
    private String applyDocName ;

    /**
     * 申请-申请单描述
     */
    @ApiModelProperty(value = "申请-申请单描述")
    private String applyNote ;

    /**
     * 检验-机构编码
     */
    @ApiModelProperty(value = "检验-机构编码")
    private String testOrgId ;

    /**
     * 检验-机构名称
     */
    @ApiModelProperty(value = "检验-机构名称")
    private String testOrgName ;

    /**
     * 检验-执行科室代码
     */
    @ApiModelProperty(value = "检验-执行科室代码")
    private String testDeptCode ;

    /**
     * 检验-执行科室名称
     */
    @ApiModelProperty(value = "检验-执行科室名称")
    private String testDeptName ;

    /**
     * 检验-接收时间
     */
    @ApiModelProperty(value = "检验-接收时间")
    private Date testReceiveTime ;

    /**
     * 检验-检测人编码
     */
    @ApiModelProperty(value = "检验-检测人编码")
    private String testDocId ;

    /**
     * 检验-检测人姓名
     */
    @ApiModelProperty(value = "检验-检测人姓名")
    private String testDocName ;

    /**
     * 检验-检测时间
     */
    @ApiModelProperty(value = "检验-检测时间")
    private Date testTime ;

    /**
     * 检验-检验项目分类代码
     */
    @ApiModelProperty(value = "检验-检验项目分类代码")
    private String testClassCode ;

    /**
     * 检验-检验项目分类名称（临床免疫学检查、临床化学检查、临床血液学检查等）
     */
    @ApiModelProperty(value = "检验-检验项目分类名称（临床免疫学检查、临床化学检查、临床血液学检查等）")
    private String testClass ;

    /**
     * 检验-检验项目编码
     */
    @ApiModelProperty(value = "检验-检验项目编码")
    private String testCode ;

    /**
     * 检验-检验项目名称
     */
    @ApiModelProperty(value = "检验-检验项目名称")
    private String testName ;

    /**
     * 检验-检验明细（json，检验项目明细代码test_item_code，检验项目明细名称test_item_name，定量结果test_result，定量结果单位test_result_unit，结果参考值范围test_result_range，结果参考值最小值test_result_range_min，结果参考值最大值test_result_range_max，定性结果编码test_result_nominal_code，定性结果test_result_nominal）
     */
    @ApiModelProperty(value = "检验-检验明细（json，检验项目明细代码test_item_code，检验项目明细名称test_item_name，定量结果test_result，定量结果单位test_result_unit，结果参考值范围test_result_range，结果参考值最小值test_result_range_min，结果参考值最大值test_result_range_max，定性结果编码test_result_nominal_code，定性结果test_result_nominal）")
    private String testDetailJson ;

    /**
     * 报告单号
     */
    @ApiModelProperty(value = "报告单号")
    private String reportId ;

    /**
     * 报告标题
     */
    @ApiModelProperty(value = "报告标题")
    private String reportTitle ;

    /**
     * 报告时间
     */
    @ApiModelProperty(value = "报告时间")
    private Date reportTime ;

    /**
     * 报告类别代码
     */
    @ApiModelProperty(value = "报告类别代码")
    private String reportTypeCode ;

    /**
     * 报告类别名称
     */
    @ApiModelProperty(value = "报告类别名称")
    private String reportType ;

    /**
     * 报告审核时间
     */
    @ApiModelProperty(value = "报告审核时间")
    private Date reportAuditTime ;

    /**
     * 报告审核人编码
     */
    @ApiModelProperty(value = "报告审核人编码")
    private String reportAuditorId ;

    /**
     * 报告审核人姓名
     */
    @ApiModelProperty(value = "报告审核人姓名")
    private String reportAuditor ;

    /**
     * 报告附件路径
     */
    @ApiModelProperty(value = "报告附件路径")
    private String reportUrl ;

}
