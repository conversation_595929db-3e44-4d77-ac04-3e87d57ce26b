package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "ads.ads_edr_confirm_diag_info")
public class AdsEdrConfirmDiagInfo {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 采集创建时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * 采集更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;

    /**
     * 事件信息ID
     */
    @TableField(value = "event_id")
    private String eventId;

    /**
     * 主索引ID
     */
    @TableField(value = "empi_id")
    private String empiId;

    /**
     * 科室代码
     */
    @TableField(value = "dept_code")
    private String deptCode;

    /**
     * 科室名称
     */
    @TableField(value = "dept")
    private String dept;

    /**
     * 诊断日期时间
     */
    @TableField(value = "diagnose_time")
    private Date diagnoseTime;

    /**
     * 诊断类别代码
     */
    @TableField(value = "diagnose_classification_code")
    private String diagnoseClassificationCode;

    /**
     * 诊断类别名称
     */
    @TableField(value = "diagnose_classification")
    private String diagnoseClassification;

    /**
     * 诊断状态代码
     */
    @TableField(value = "case_type_code")
    private String caseTypeCode;

    /**
     * 诊断状态名称
     */
    @TableField(value = "case_type")
    private String caseType;

    /**
     * *疾病诊断代码
     */
    @TableField(value = "disease_code")
    private String diseaseCode;

    /**
     * 疾病诊断名称
     */
    @TableField(value = "disease")
    private String disease;

    /**
     * 病例分类代码
     */
    @TableField(value = "diagnose_state_code")
    private String diagnoseStateCode;

    /**
     * 病例分类名称（确诊病例、疑似病例等）
     */
    @TableField(value = "diagnose_state")
    private String diagnoseState;

    /**
     * 发病日期
     */
    @TableField(value = "onset_date")
    private Date onsetDate;

    /**
     * 发病统计标识
     */
    @TableField(value = "statistics_flag_code")
    private String statisticsFlagCode;

    /**
     * 临床严重程度代码
     */
    @TableField(value = "ncv_severity_code")
    private String ncvSeverityCode;

    /**
     * 临床严重程度名称
     */
    @TableField(value = "ncv_severity")
    private String ncvSeverity;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 数据来源ID
     */
    @TableField(value = "source_id")
    private String sourceId;

    /**
     * 诊断地点代码
     */
    @TableField(value = "diagnosis_org_code")
    private String diagnosisOrgCode;

    /**
     * 主索引ID
     */
    @TableField(value = "mpi_id")
    private String mpiId;

    /**
     * 诊断地点
     */
    @TableField(value = "diagnosis_org")
    private String diagnosisOrg;

    /**
     * 首复诊类型
     */
    @TableField(value = "visit_type_code")
    private String visitTypeCode;

    /**
     * 首复诊类型
     */
    @TableField(value = "visit_type")
    private String visitType;

    /**
     * 创建机构
     */
    @TableField(value = "create_org")
    private String createOrg;

    /**
     * 创建机构
     */
    @TableField(value = "create_org_name")
    private String createOrgName;

    /**
     * 修改机构
     */
    @TableField(value = "update_org")
    private String updateOrg;

    /**
     * 修改机构
     */
    @TableField(value = "update_org_name")
    private String updateOrgName;
}