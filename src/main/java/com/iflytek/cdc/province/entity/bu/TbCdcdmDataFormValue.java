package com.iflytek.cdc.province.entity.bu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 页面表单数据表
 */
@ApiModel(description="页面表单数据表")
@Data
public class TbCdcdmDataFormValue implements Serializable {
    /**
    * 主键ID
    */
    @ApiModelProperty(value="主键ID")
    private String id;

    /**
    * 表单页面配置Detail ID
    */
    @ApiModelProperty(value="表单页面配置Detail ID")
    private String formTemplateDetailId;

    /**
    * 数据模型ID
    */
    @ApiModelProperty(value="数据模型ID")
    private String modelId;

    /**
    * 数据模型版本ID
    */
    @ApiModelProperty(value="数据模型版本ID")
    private String modelVersionId;

    /**
    * 数据模型配置表单ID
    */
    @ApiModelProperty(value="数据模型配置表单ID")
    private String modelFormId;

    /**
    * 数据模型表单唯一标识值
    */
    @ApiModelProperty(value="数据模型表单唯一标识值")
    private String modelFormIdentityId;

    /**
    * 表单名称
    */
    @ApiModelProperty(value="表单名称")
    private String modelFormName;

    /**
    * 页面数据
    */
    @ApiModelProperty(value="页面数据")
    private String dataJson;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
    * 创建人
    */
    @ApiModelProperty(value="创建人")
    private String creator;

    /**
    * 修改时间
    */
    @ApiModelProperty(value="修改时间")
    private Date updateTime;

    /**
    * 修改人
    */
    @ApiModelProperty(value="修改人")
    private String updater;

    private static final long serialVersionUID = 1L;
}