package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 传染病报卡表
 */
@Data
@TableName(value = "ads.ads_rep_infect_report_info")
public class AdsRepInfectReportInfo {
    /**
     * 传报卡ID
     */
    @TableId(value = "ms_infect_report_id", type = IdType.INPUT)
    private String msInfectReportId;

    /**
     * 采集创建时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * 采集更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;

    /**
     * 事件信息ID
     */
    @TableField(value = "event_id")
    private String eventId;

    /**
     * 患者ID
     */
    @TableField(value = "patient_id")
    private String patientId;

    /**
     * 主索引ID
     */
    @TableField(value = "empi_id")
    private String empiId;

    /**
     * 患者姓名
     */
    @TableField(value = "patient_name")
    private String patientName;

    /**
     * 身份证件类别编码
     */
    @TableField(value = "patient_identity_type_code")
    private String patientIdentityTypeCode;

    /**
     * 身份证件类别
     */
    @TableField(value = "patient_identity_type")
    private String patientIdentityType;

    /**
     * 身份证号
     */
    @TableField(value = "patient_identity_no")
    private String patientIdentityNo;

    /**
     * 性别代码
     */
    @TableField(value = "patient_sex_code")
    private String patientSexCode;

    /**
     * 性别名称
     */
    @TableField(value = "patient_sex_name")
    private String patientSexName;

    /**
     * 出生日期
     */
    @TableField(value = "patient_birth_day")
    private Date patientBirthDay;

    /**
     * 年龄
     */
    @TableField(value = "age")
    private Short age;

    /**
     * 年龄单位
     */
    @TableField(value = "age_unit")
    private String ageUnit;

    /**
     * 年龄分布
     */
    @TableField(value = "age_group")
    private String ageGroup;

    /**
     * 国籍/地区代码
     */
    @TableField(value = "patient_nationality_code")
    private String patientNationalityCode;

    /**
     * 国籍/地区名称
     */
    @TableField(value = "patient_nationality_name")
    private String patientNationalityName;

    /**
     * 民族代码
     */
    @TableField(value = "patient_ethnic_code")
    private String patientEthnicCode;

    /**
     * 民族名称
     */
    @TableField(value = "patient_ethnic_name")
    private String patientEthnicName;

    /**
     * 户籍类别代码
     */
    @TableField(value = "patient_permanent_addr_type_code")
    private String patientPermanentAddrTypeCode;

    /**
     * 户籍类别名称
     */
    @TableField(value = "patient_permanent_addr_type_name")
    private String patientPermanentAddrTypeName;

    /**
     * 户籍地址代码
     */
    @TableField(value = "patient_permanent_addr_code")
    private String patientPermanentAddrCode;

    /**
     * 户籍地址名称
     */
    @TableField(value = "patient_permanent_addr_name")
    private String patientPermanentAddrName;

    /**
     * 户籍详细地址
     */
    @TableField(value = "patient_permanent_addr_detail")
    private String patientPermanentAddrDetail;

    /**
     * 婚姻状况代码
     */
    @TableField(value = "patient_marry_code")
    private String patientMarryCode;

    /**
     * 婚姻状况名称
     */
    @TableField(value = "patient_marry_name")
    private String patientMarryName;

    /**
     * 学历代码
     */
    @TableField(value = "patient_educate_code")
    private String patientEducateCode;

    /**
     * 学历名称
     */
    @TableField(value = "patient_educate_name")
    private String patientEducateName;

    /**
     * 患者电话号码
     */
    @TableField(value = "patient_phone")
    private String patientPhone;

    /**
     * 联系人/监护人与本人关系代码
     */
    @TableField(value = "contacts_rel_code")
    private String contactsRelCode;

    /**
     * 联系人/监护人姓名
     */
    @TableField(value = "patient_contact_name")
    private String patientContactName;

    /**
     * 联系人/监护人电话号码
     */
    @TableField(value = "patient_contact_phone")
    private String patientContactPhone;

    /**
     * 现住地区代码
     */
    @TableField(value = "living_area_code")
    private String livingAreaCode;

    /**
     * 现住地区名称
     */
    @TableField(value = "living_area_name")
    private String livingAreaName;

    /**
     * 现住址类别/生前住址类别代码
     */
    @TableField(value = "current_addr_type_code")
    private String currentAddrTypeCode;

    /**
     * 现住址类别/生前住址类别名称
     */
    @TableField(value = "current_addr_type_name")
    private String currentAddrTypeName;

    /**
     * 现住详细地址
     */
    @TableField(value = "living_addr_detail")
    private String livingAddrDetail;

    /**
     * 现住址标化地址
     */
    @TableField(value = "living_addr_detail_std")
    private String livingAddrDetailStd;

    /**
     * 现住址标化-省编码
     */
    @TableField(value = "living_addr_province_code")
    private String livingAddrProvinceCode;

    /**
     * 现住址标化-省
     */
    @TableField(value = "living_addr_province_name")
    private String livingAddrProvinceName;

    /**
     * 现住址标化-市编码
     */
    @TableField(value = "living_addr_city_code")
    private String livingAddrCityCode;

    /**
     * 现住址标化-市
     */
    @TableField(value = "living_addr_city_name")
    private String livingAddrCityName;

    /**
     * 现住址标化-行政区编码
     */
    @TableField(value = "living_addr_district_code")
    private String livingAddrDistrictCode;

    /**
     * 现住址标化-行政区
     */
    @TableField(value = "living_addr_district_name")
    private String livingAddrDistrictName;

    /**
     * 现住址标化-功能区编码
     */
    @TableField(value = "living_addr_func_district_code")
    private String livingAddrFuncDistrictCode;

    /**
     * 现住址标化-功能区
     */
    @TableField(value = "living_addr_func_district_name")
    private String livingAddrFuncDistrictName;

    /**
     * 现住址标化-街道编码
     */
    @TableField(value = "living_addr_street_code")
    private String livingAddrStreetCode;

    /**
     * 现住址标化-街道
     */
    @TableField(value = "living_addr_street_name")
    private String livingAddrStreetName;

    /**
     * 现住址标化-街道中心经度
     */
    @TableField(value = "living_addr_street_longitude")
    private BigDecimal livingAddrStreetLongitude;

    /**
     * 现住址标化-街道中心纬度
     */
    @TableField(value = "living_addr_street_latitude")
    private BigDecimal livingAddrStreetLatitude;

    /**
     * 现住址标化-经度
     */
    @TableField(value = "living_addr_longitude")
    private BigDecimal livingAddrLongitude;

    /**
     * 现住址标化-经度
     */
    @TableField(value = "living_addr_latitude")
    private BigDecimal livingAddrLatitude;

    /**
     * 现住址标化场所类别
     */
    @TableField(value = "living_addr_poi")
    private String livingAddrPoi;

    /**
     * 现住址标化城乡类型（城市、乡村、未知）
     */
    @TableField(value = "living_addr_type")
    private String livingAddrType;

    /**
     * 工作单位/学校名称
     */
    @TableField(value = "company")
    private String company;

    /**
     * 工作工作单位/学校标化地址/学校标化地址
     */
    @TableField(value = "company_addr_std")
    private String companyAddrStd;

    /**
     * 工作单位/学校标化地址-省编码
     */
    @TableField(value = "company_province_code")
    private String companyProvinceCode;

    /**
     * 工作单位/学校标化地址-省
     */
    @TableField(value = "company_province_name")
    private String companyProvinceName;

    /**
     * 工作单位/学校标化地址-市编码
     */
    @TableField(value = "company_city_code")
    private String companyCityCode;

    /**
     * 工作单位/学校标化地址-市
     */
    @TableField(value = "company_city_name")
    private String companyCityName;

    /**
     * 工作单位/学校标化地址-行政区编码
     */
    @TableField(value = "company_district_code")
    private String companyDistrictCode;

    /**
     * 工作单位/学校标化地址-行政区
     */
    @TableField(value = "company_district_name")
    private String companyDistrictName;

    /**
     * 工作单位/学校标化地址-功能区编码
     */
    @TableField(value = "company_func_district_code")
    private String companyFuncDistrictCode;

    /**
     * 工作单位/学校标化地址-功能区
     */
    @TableField(value = "company_func_district_name")
    private String companyFuncDistrictName;

    /**
     * 工作单位/学校标化地址-街道编码
     */
    @TableField(value = "company_street_code")
    private String companyStreetCode;

    /**
     * 工作单位/学校标化地址-街道
     */
    @TableField(value = "company_street_name")
    private String companyStreetName;

    /**
     * 工作单位/学校标化地址-街道中心经度
     */
    @TableField(value = "company_street_longitude")
    private BigDecimal companyStreetLongitude;

    /**
     * 工作单位/学校标化地址-街道中心纬度
     */
    @TableField(value = "company_street_latitude")
    private BigDecimal companyStreetLatitude;

    /**
     * 工作单位/学校标化地址-经度
     */
    @TableField(value = "company_longitude")
    private BigDecimal companyLongitude;

    /**
     * 工作单位/学校标化地址-纬度
     */
    @TableField(value = "company_latitude")
    private BigDecimal companyLatitude;

    /**
     * 人群分类代码
     */
    @TableField(value = "nultitude_type_code")
    private String nultitudeTypeCode;

    /**
     * 人群分类名称
     */
    @TableField(value = "nultitude_type_name")
    private String nultitudeTypeName;

    /**
     * 人群分类其他
     */
    @TableField(value = "nultitude_type_other")
    private String nultitudeTypeOther;

    /**
     * 病人所属地类型代码
     */
    @TableField(value = "addr_belong_type_code")
    private String addrBelongTypeCode;

    /**
     * 病人所属地类型名称
     */
    @TableField(value = "addr_belong_type")
    private String addrBelongType;

    /**
     * 诊疗活动类型代码
     */
    @TableField(value = "activity_type_code")
    private String activityTypeCode;

    /**
     * 诊疗活动类型名称
     */
    @TableField(value = "activity_type_name")
    private String activityTypeName;

    /**
     * 就诊流水号
     */
    @TableField(value = "serial_number")
    private String serialNumber;

    /**
     * 就诊地址类型代码
     */
    @TableField(value = "visit_addr_areatype2_code")
    private String visitAddrAreatype2Code;

    /**
     * 就诊地址类型名称
     */
    @TableField(value = "visit_addr_areatype2_name")
    private String visitAddrAreatype2Name;

    /**
     * 就诊机构编码
     */
    @TableField(value = "visit_org_code")
    private String visitOrgCode;

    /**
     * 就诊机构名称
     */
    @TableField(value = "visit_org_name")
    private String visitOrgName;

    /**
     * 科室代码
     */
    @TableField(value = "dept_code")
    private String deptCode;

    /**
     * 科室名称
     */
    @TableField(value = "dept_name")
    private String deptName;

    /**
     * 科室-其他
     */
    @TableField(value = "dept_other")
    private String deptOther;

    /**
     * 就诊地址编码
     */
    @TableField(value = "visit_addr_code")
    private String visitAddrCode;

    /**
     * 就诊地址名称
     */
    @TableField(value = "visit_addr_name")
    private String visitAddrName;

    /**
     * 就诊地址
     */
    @TableField(value = "visit_addr_detail")
    private String visitAddrDetail;

    /**
     * 就诊地址标化地址
     */
    @TableField(value = "visit_addr_detail_std")
    private String visitAddrDetailStd;

    /**
     * 就诊地址标化-省编码
     */
    @TableField(value = "visit_addr_province_code")
    private String visitAddrProvinceCode;

    /**
     * 就诊地址标化-省
     */
    @TableField(value = "visit_addr_province_name")
    private String visitAddrProvinceName;

    /**
     * 就诊地址标化-市编码
     */
    @TableField(value = "visit_addr_city_code")
    private String visitAddrCityCode;

    /**
     * 就诊地址标化-市
     */
    @TableField(value = "visit_addr_city_name")
    private String visitAddrCityName;

    /**
     * 就诊地址标化-行政区编码
     */
    @TableField(value = "visit_addr_district_code")
    private String visitAddrDistrictCode;

    /**
     * 就诊地址标化-行政区
     */
    @TableField(value = "visit_addr_district_name")
    private String visitAddrDistrictName;

    /**
     * 就诊地址标化-功能区编码
     */
    @TableField(value = "visit_addr_func_district_code")
    private String visitAddrFuncDistrictCode;

    /**
     * 就诊地址标化-功能区
     */
    @TableField(value = "visit_addr_func_district_name")
    private String visitAddrFuncDistrictName;

    /**
     * 就诊地址标化-街道编码
     */
    @TableField(value = "visit_addr_street_code")
    private String visitAddrStreetCode;

    /**
     * 就诊地址标化-街道
     */
    @TableField(value = "visit_addr_street_name")
    private String visitAddrStreetName;

    /**
     * 就诊地址标化-街道中心经度
     */
    @TableField(value = "visit_addr_street_longitude")
    private BigDecimal visitAddrStreetLongitude;

    /**
     * 就诊地址标化-街道中心纬度
     */
    @TableField(value = "visit_addr_street_latitude")
    private BigDecimal visitAddrStreetLatitude;

    /**
     * 就诊地址标化-经度
     */
    @TableField(value = "visit_addr_longitude")
    private BigDecimal visitAddrLongitude;

    /**
     * 就诊地址标化-经度
     */
    @TableField(value = "visit_addr_latitude")
    private BigDecimal visitAddrLatitude;

    /**
     * 就诊地址标化场所类别
     */
    @TableField(value = "visit_addr_poi")
    private String visitAddrPoi;

    /**
     * 就诊地址标化城乡类型（城市、乡村、未知）
     */
    @TableField(value = "visit_addr_type")
    private String visitAddrType;

    /**
     * 病例分类代码
     */
    @TableField(value = "cases_type_code")
    private String casesTypeCode;

    /**
     * 病例分类名称
     */
    @TableField(value = "cases_type_name")
    private String casesTypeName;

    /**
     * 发病日期时间
     */
    @TableField(value = "onset_datetime")
    private Date onsetDatetime;

    /**
     * 临床表现-其他
     */
    @TableField(value = "clinical_other")
    private String clinicalOther;

    /**
     * 发热体温
     */
    @TableField(value = "fever_num")
    private String feverNum;

    /**
     * 就诊日期时间
     */
    @TableField(value = "visit_datetime")
    private Date visitDatetime;

    /**
     * 诊断日期时间
     */
    @TableField(value = "diag_datetime")
    private Date diagDatetime;

    /**
     * 订正前疾病诊断名称
     */
    @TableField(value = "audit_disease_name")
    private String auditDiseaseName;

    /**
     * 订正前疾病诊断代码
     */
    @TableField(value = "audit_disease_code")
    private String auditDiseaseCode;

    /**
     * 订正报告时间
     */
    @TableField(value = "audit_create_date")
    private Date auditCreateDate;

    /**
     * 疾病诊断代码
     */
    @TableField(value = "diag_code")
    private String diagCode;

    /**
     * 疾病诊断名称
     */
    @TableField(value = "diag_name")
    private String diagName;

    /**
     * 初步诊断-其他
     */
    @TableField(value = "diag_other")
    private String diagOther;

    /**
     * 诊断标化(ICD10)-疾病诊断代码
     */
    @TableField(value = "diag_std_code")
    private String diagStdCode;

    /**
     * 诊断标化(ICD10)-疾病诊断名称
     */
    @TableField(value = "diag_std_name")
    private String diagStdName;

    /**
     * 其他疾病，疾病诊断选其他疾病时，填写
     */
    @TableField(value = "disease_other")
    private String diseaseOther;

    /**
     * 诊断状态代码
     */
    @TableField(value = "diag_state_code")
    private String diagStateCode;

    /**
     * 诊断状态名称
     */
    @TableField(value = "diag_state_name")
    private String diagStateName;

    /**
     * 实验室检测结论代码
     */
    @TableField(value = "test_detection_verdict_code")
    private String testDetectionVerdictCode;

    /**
     * 实验室检测结论名称
     */
    @TableField(value = "test_detection_verdict_name")
    private String testDetectionVerdictName;

    /**
     * 实验室确诊日期
     */
    @TableField(value = "labor_diagnosis_date")
    private Date laborDiagnosisDate;

    /**
     * 实验室结果代码
     */
    @TableField(value = "labor_test_result_code")
    private String laborTestResultCode;

    /**
     * 实验室结果名称
     */
    @TableField(value = "labor_test_result_name")
    private String laborTestResultName;

    /**
     * 确认（替代策略、核酸）检测阳性日期时间
     */
    @TableField(value = "confirm_aids_datetime")
    private Date confirmAidsDatetime;

    /**
     * 确认（替代策略、核酸）检测机构
     */
    @TableField(value = "confirm_org_code")
    private String confirmOrgCode;

    /**
     * 艾滋实验室确诊日期时间
     */
    @TableField(value = "hiv_diag_datetime")
    private Date hivDiagDatetime;

    /**
     * 麻痹日期时间
     */
    @TableField(value = "palsy_datetime")
    private Date palsyDatetime;

    /**
     * 麻痹症状
     */
    @TableField(value = "palsy_symptom")
    private String palsySymptom;

    /**
     * 入院日期
     */
    @TableField(value = "iut_hos_date")
    private Date iutHosDate;

    /**
     * 出院日期
     */
    @TableField(value = "out_hos_date")
    private Date outHosDate;

    /**
     * 填写出院日期
     */
    @TableField(value = "out_hos_file_date")
    private Date outHosFileDate;

    /**
     * 收治状态/转诊状态代码
     */
    @TableField(value = "mgmt_status_code")
    private String mgmtStatusCode;

    /**
     * 收治状态/转诊状态名称
     */
    @TableField(value = "mgmt_status_name")
    private String mgmtStatusName;

    /**
     * 收治机构/转诊机构代码
     */
    @TableField(value = "currmgmt_org_code")
    private String currmgmtOrgCode;

    /**
     * 收治机构/转诊机构名称
     */
    @TableField(value = "currmgmt_org_name")
    private String currmgmtOrgName;

    /**
     * 死亡日期时间
     */
    @TableField(value = "dead_datetime")
    private Date deadDatetime;

    /**
     * 死亡是否与此病有关代码
     */
    @TableField(value = "dead_by_this_flag")
    private String deadByThisFlag;

    /**
     * 直接死亡原因编码
     */
    @TableField(value = "direct_death_reason_code")
    private String directDeathReasonCode;

    /**
     * 直接死亡原因名称
     */
    @TableField(value = "direct_death_reason_name")
    private String directDeathReasonName;

    /**
     * 卡片id
     */
    @TableField(value = "card_id")
    private String cardId;

    /**
     * 卡片编号
     */
    @TableField(value = "card_code")
    private String cardCode;

    /**
     * 填卡时间
     */
    @TableField(value = "card_fill_date")
    private Date cardFillDate;

    /**
     * 报告日期时间
     */
    @TableField(value = "report_datetime")
    private Date reportDatetime;

    /**
     * 报告单位代码
     */
    @TableField(value = "report_org_code")
    private String reportOrgCode;

    /**
     * 报告单位
     */
    @TableField(value = "report_org")
    private String reportOrg;

    /**
     * 报告地区代码
     */
    @TableField(value = "report_zone_code")
    private String reportZoneCode;

    /**
     * 报告地区名称
     */
    @TableField(value = "report_zone_name")
    private String reportZoneName;

    /**
     * 报告单位地址标化地址
     */
    @TableField(value = "report_addr_detail_std")
    private String reportAddrDetailStd;

    /**
     * 报告单位地址标化-省编码
     */
    @TableField(value = "report_addr_province_code")
    private String reportAddrProvinceCode;

    /**
     * 报告单位地址标化-省
     */
    @TableField(value = "report_addr_province_name")
    private String reportAddrProvinceName;

    /**
     * 报告单位地址标化-市编码
     */
    @TableField(value = "report_addr_city_code")
    private String reportAddrCityCode;

    /**
     * 报告单位地址标化-市
     */
    @TableField(value = "report_addr_city_name")
    private String reportAddrCityName;

    /**
     * 报告单位地址标化-行政区编码
     */
    @TableField(value = "report_addr_district_code")
    private String reportAddrDistrictCode;

    /**
     * 报告单位地址标化-行政区
     */
    @TableField(value = "report_addr_district_name")
    private String reportAddrDistrictName;

    /**
     * 报告单位地址标化-功能区编码
     */
    @TableField(value = "report_addr_func_district_code")
    private String reportAddrFuncDistrictCode;

    /**
     * 报告单位地址标化-功能区
     */
    @TableField(value = "report_addr_func_district_name")
    private String reportAddrFuncDistrictName;

    /**
     * 报告单位地址标化-街道编码
     */
    @TableField(value = "report_addr_street_code")
    private String reportAddrStreetCode;

    /**
     * 报告单位地址标化-街道
     */
    @TableField(value = "report_addr_street_name")
    private String reportAddrStreetName;

    /**
     * 报告单位地址标化-街道中心纬度
     */
    @TableField(value = "report_addr_street_latitude")
    private BigDecimal reportAddrStreetLatitude;

    /**
     * 报告单位地址标化-街道中心经度
     */
    @TableField(value = "report_addr_street_longitude")
    private BigDecimal reportAddrStreetLongitude;

    /**
     * 报告单位地址标化-纬度	
     */
    @TableField(value = "report_addr_latitude")
    private BigDecimal reportAddrLatitude;

    /**
     * 报告单位地址标化-经度
     */
    @TableField(value = "report_addr_longitude")
    private BigDecimal reportAddrLongitude;

    /**
     * 报告单位地址标化城乡类型（城市、乡村、未知）
     */
    @TableField(value = "report_addr_type")
    private String reportAddrType;

    /**
     * 报告单位地址标化场所类别
     */
    @TableField(value = "report_addr_poi")
    private String reportAddrPoi;

    /**
     * 填卡医生id
     */
    @TableField(value = "fill_doc_id")
    private String fillDocId;

    /**
     * 填卡医生
     */
    @TableField(value = "fill_doc_name")
    private String fillDocName;

    /**
     * 备注信息
     */
    @TableField(value = "notes")
    private String notes;

    /**
     * 传染病类别编码
     */
    @TableField(value = "infect_type_code")
    private String infectTypeCode;

    /**
     * 传染病类别名称（甲类传染病等）
     */
    @TableField(value = "infect_type_name")
    private String infectTypeName;

    /**
     * 父级病种编码
     */
    @TableField(value = "infect_parent_code")
    private String infectParentCode;

    /**
     * 父级病种名称
     */
    @TableField(value = "infect_parent_name")
    private String infectParentName;

    /**
     * 传染病编码
     */
    @TableField(value = "infect_code")
    private String infectCode;

    /**
     * 传染病名称
     */
    @TableField(value = "infect_name")
    private String infectName;

    /**
     * 病例来源名称
     */
    @TableField(value = "treat_type_code")
    private String treatTypeCode;

    /**
     * 发现方式代码
     */
    @TableField(value = "find_way_code")
    private String findWayCode;

    /**
     * 发现方式名称
     */
    @TableField(value = "find_way_name")
    private String findWayName;

    /**
     * 发现方式其他
     */
    @TableField(value = "find_way_other")
    private String findWayOther;

    /**
     * 是否重症代码
     */
    @TableField(value = "serverity_code")
    private String serverityCode;

    /**
     * 是否重症名称
     */
    @TableField(value = "serverity_name")
    private String serverityName;

    /**
     * 感染途径代码
     */
    @TableField(value = "infect_route_code")
    private String infectRouteCode;

    /**
     * 感染途径名称
     */
    @TableField(value = "infect_route_name")
    private String infectRouteName;

    /**
     * 感染途径其他
     */
    @TableField(value = "infect_route_other")
    private String infectRouteOther;

    /**
     * 接触方式代码
     */
    @TableField(value = "contact_way_code")
    private String contactWayCode;

    /**
     * 接触方式名称
     */
    @TableField(value = "contact_way_name")
    private String contactWayName;

    /**
     * 接触史其他
     */
    @TableField(value = "contact_other")
    private String contactOther;

    /**
     * 性病史代码
     */
    @TableField(value = "venereal_dis_code")
    private String venerealDisCode;

    /**
     * 性病史名称
     */
    @TableField(value = "venereal_dis_name")
    private String venerealDisName;

    /**
     * 注射毒品史与病人共用过注射器的人数
     */
    @TableField(value = "inject_person_cnt")
    private String injectPersonCnt;

    /**
     * 与病人有非婚性行为的人数
     */
    @TableField(value = "nonweb_cnt")
    private String nonwebCnt;

    /**
     * 男男性行为史发生同性性行为的人数
     */
    @TableField(value = "msm_person_cnt")
    private String msmPersonCnt;

    /**
     * 生殖道沙眼衣原体感染代码
     */
    @TableField(value = "ct_cases_type_code")
    private String ctCasesTypeCode;

    /**
     * 生殖道沙眼衣原体感染名称
     */
    @TableField(value = "ct_cases_type_name")
    private String ctCasesTypeName;

    /**
     * 手足口病实验室结果代码
     */
    @TableField(value = "lab_result_code")
    private String labResultCode;

    /**
     * 手足口病实验室结果名称
     */
    @TableField(value = "lab_result_name")
    private String labResultName;

    /**
     * 乙肝HBsAg阳性时长类型代码
     */
    @TableField(value = "hbsag_duration_type_code")
    private String hbsagDurationTypeCode;

    /**
     * 乙肝HBsAg阳性时长类型名称
     */
    @TableField(value = "hbsag_duration_type_name")
    private String hbsagDurationTypeName;

    /**
     * 首次症状时间（首次出现乙肝症状和体征时间）
     */
    @TableField(value = "hbsag_symptom_first_datetime")
    private Date hbsagSymptomFirstDatetime;

    /**
     * 乙肝症状无症状/不详(首次出现乙肝症状和体征时间和本字段不能同时有值)
     */
    @TableField(value = "hbsag_symptom_buxiang_flag")
    private String hbsagSymptomBuxiangFlag;

    /**
     * 乙肝本次ALT
     */
    @TableField(value = "hbsag_alt_res")
    private String hbsagAltRes;

    /**
     * 抗-HBcIgM1：1000检测结果代码
     */
    @TableField(value = "hbcigm_code")
    private String hbcigmCode;

    /**
     * 抗-HBcIgM1：1000检测结果名称
     */
    @TableField(value = "hbcigm_name")
    private String hbcigmName;

    /**
     * 肝穿结果（急慢性）代码
     */
    @TableField(value = "hbliver_puncture_code")
    private String hbliverPunctureCode;

    /**
     * 肝穿结果（急慢性）名称
     */
    @TableField(value = "hbliver_puncture_name")
    private String hbliverPunctureName;

    /**
     * 恢复期血清HBsAg阴转，抗HBs阳转编码
     */
    @TableField(value = "hbsag_change_code")
    private String hbsagChangeCode;

    /**
     * 恢复期血清HBsAg阴转，抗HBs阳转名称
     */
    @TableField(value = "hbsag_change_name")
    private String hbsagChangeName;

    /**
     * 亲密接触者有无同症状代码
     */
    @TableField(value = "same_symptom_flag")
    private String sameSymptomFlag;

    /**
     * 新冠临床严重程度代码
     */
    @TableField(value = "covid_severe_type_code")
    private String covidSevereTypeCode;

    /**
     * 新冠临床严重程度名称
     */
    @TableField(value = "covid_severe_type_name")
    private String covidSevereTypeName;

    /**
     * 婴儿是否有呼吸暂停代码，取值为："1=是、2=否"
     */
    @TableField(value = "is_baby_apnea_code")
    private String isBabyApneaCode;

    /**
     * 婴儿是否有呼吸暂停名称
     */
    @TableField(value = "is_baby_apnea_name")
    private String isBabyApneaName;

    /**
     * 婴儿是否有脓毒症/休克，取值为："1=是、2=否"
     */
    @TableField(value = "is_baby_shock_code")
    private String isBabyShockCode;

    /**
     * 婴儿是否有脓毒症/休克名称
     */
    @TableField(value = "is_baby_shock_name")
    private String isBabyShockName;

    /**
     * 输入病例类型代码
     */
    @TableField(value = "input_type_code")
    private String inputTypeCode;

    /**
     * 输入病例类型名称
     */
    @TableField(value = "input_type_name")
    private String inputTypeName;

    /**
     * 输入来源地代码
     */
    @TableField(value = "input_area_code")
    private String inputAreaCode;

    /**
     * 输入来源地名称
     */
    @TableField(value = "input_area_name")
    private String inputAreaName;

    /**
     * 输入来源地-其他国家
     */
    @TableField(value = "place_other")
    private String placeOther;

    /**
     * 卡片审核状态
     */
    @TableField(value = "valid_flag")
    private String validFlag;

    /**
     * 卡片状态
     */
    @TableField(value = "card_status")
    private String cardStatus;

    /**
     * 县区审核时间
     */
    @TableField(value = "valid_time_district")
    private Date validTimeDistrict;

    /**
     * 市级审核时间
     */
    @TableField(value = "valid_time_city")
    private Date validTimeCity;

    /**
     * 省级审核时间
     */
    @TableField(value = "valid_time_province")
    private Date validTimeProvince;

    /**
     * 终审时间
     */
    @TableField(value = "valid_time")
    private Date validTime;

    /**
     * 死亡终审时间
     */
    @TableField(value = "dead_valid_time")
    private Date deadValidTime;

    /**
     * 操作人ID
     */
    @TableField(value = "operator_id")
    private String operatorId;

    /**
     * 操作时间
     */
    @TableField(value = "do_datetime")
    private Date doDatetime;

    /**
     * 最后一次修改用户id
     */
    @TableField(value = "last_modify_user")
    private String lastModifyUser;

    /**
     * 删除用户id
     */
    @TableField(value = "delete_user_id")
    private String deleteUserId;

    /**
     * 删除用户名称
     */
    @TableField(value = "delete_user_name")
    private String deleteUserName;

    /**
     * 删除时间
     */
    @TableField(value = "delete_time")
    private Date deleteTime;

    /**
     * 删除类型
     */
    @TableField(value = "deleting_type_code")
    private String deletingTypeCode;

    /**
     * 删除原因
     */
    @TableField(value = "deleting_reason_details")
    private String deletingReasonDetails;

    /**
     * 历史报告卡标签
     */
    @TableField(value = "report_tag")
    private String reportTag;

    /**
     * 删除标志：0-未删除，1-已删除,河南定制字段(应标)
     */
    @TableField(value = "card_delete_flag")
    private Short cardDeleteFlag;

    /**
     * 删除备注 河南定制字段(应标)
     */
    @TableField(value = "card_delete_remark")
    private String cardDeleteRemark;

    /**
     * 排除标志：0-未排除，1-已排除,河南定制字段(应标)
     */
    @TableField(value = "card_exclude_flag")
    private Short cardExcludeFlag;

    /**
     * 排除备注 河南定制字段(应标)
     */
    @TableField(value = "card_exclude_remark")
    private String cardExcludeRemark;

    /**
     * 加工字段：EDR生命周期ID
     */
    @TableField(value = "life_id")
    private String lifeId;

    /**
     * 加工字段：EDR档案ID
     */
    @TableField(value = "archive_id")
    private String archiveId;

    /**
     * 数据来源
     */
    @TableField(value = "data_source")
    private String dataSource;

    /**
     * 数据来源名称
     */
    @TableField(value = "data_source_name")
    private String dataSourceName;

    /**
     * 数据来源（渠道）
     */
    @TableField(value = "source_id")
    private String sourceId;
}