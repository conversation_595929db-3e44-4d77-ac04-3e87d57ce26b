package com.iflytek.cdc.province.entity.ads;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
@ApiModel("疫情事件附件信息")
public class DyqwEventAttachment {

    @ApiModelProperty("附件ID")
    private String attachmentsId;

    @ApiModelProperty("附件描述")
    private String attachmentsDesc;

    @ApiModelProperty("二进制文件")
    private byte[] blobFile;

    @ApiModelProperty("附件名称")
    private String attachmentsName;

    @ApiModelProperty("关联ID")
    private String relateId;

    @ApiModelProperty("报告ID")
    private String reportId;

}