package com.iflytek.cdc.province.entity.dim;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 传染病信息维表;
 * <AUTHOR> dingyuan
 * @date : 2024-8-16
 */
@ApiModel(value = "传染病信息维表")
@Data
public class DimInfectedInfo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id ;

    /**
     * 传染病病种二级编码
     */
    @ApiModelProperty(value = "传染病病种二级编码")
    private String infectedSubCode ;

    /**
     * 传染病病种二级名称
     */
    @ApiModelProperty(value = "传染病病种二级名称")
    private String infectedSubName ;

    /**
     * 传染病病种一级编码
     */
    @ApiModelProperty(value = "传染病病种一级编码")
    private String infectedCode ;

    /**
     * 传染病病种一级名称
     */
    @ApiModelProperty(value = "传染病病种一级名称")
    private String infectedName ;

    /**
     * 传染病类别编码
     */
    @ApiModelProperty(value = "传染病类别编码")
    private String infectedTypeCode ;

    /**
     * 传染病类别名称
     */
    @ApiModelProperty(value = "传染病类别名称")
    private String infectedTypeName ;

    /**
     * 来源
     */
    @ApiModelProperty(value = "来源")
    private String infectedSource ;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String memo ;

    /**
     * 标准传染病病种二级编码
     */
    @ApiModelProperty(value = "标准传染病病种二级编码")
    private String mdmInfectedSubCode ;

    /**
     * 标准传染病病种二级名称
     */
    @ApiModelProperty(value = "标准传染病病种二级名称")
    private String mdmInfectedSubName ;

    /**
     * 标准传染病病种一级编码
     */
    @ApiModelProperty(value = "标准传染病病种一级编码")
    private String mdmInfectedCode ;

    /**
     * 标准传染病病种一级名称
     */
    @ApiModelProperty(value = "标准传染病病种一级名称")
    private String mdmInfectedName ;

    /**
     * 标准传染病类别编码
     */
    @ApiModelProperty(value = "标准传染病类别编码")
    private String mdmInfectedTypeCode ;

    /**
     * 标准传染病类别名称
     */
    @ApiModelProperty(value = "标准传染病类别名称")
    private String mdmInfectedTypeName ;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createDatetime ;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateDatetime ;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private Date etlCreateDatetime ;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private Date etlUpdateDatetime ;

    /**
     * 传染病类别管理代码
     */
    @ApiModelProperty(value = "传染病类别管理代码")
    private String managementTypeCode ;

    /**
     * 传染病类别管理名称
     */
    @ApiModelProperty(value = "传染病类别管理名称")
    private String managementTypeName ;

    /**
     * 传染病分类类型代码
     */
    @ApiModelProperty(value = "传染病分类类型代码")
    private String infectClassCode ;

    /**
     * 传染病分类类型名称: 法定
     */
    @ApiModelProperty(value = "传染病分类类型名称: 法定")
    private String infectClassName ;

    /**
     * 传播途径代码
     */
    @ApiModelProperty(value = "传播途径代码")
    private String transmissionTypeCode ;

    /**
     * 传播途径名称
     */
    @ApiModelProperty(value = "传播途径名称")
    private String transmissionTypeName ;

    /**
     * 恢复期
     */
    @ApiModelProperty(value = "恢复期")
    private Integer recoveryDays ;

    /**
     * 潜伏期
     */
    @ApiModelProperty(value = "潜伏期")
    private Integer latentDays ;

    /**
     * 报告/识别时限（单位：小时）
     */
    @ApiModelProperty(value = "报告/识别时限（单位：小时）")
    private Integer reportDeadlineH ;

    /**
     * 监测审核时限（单位：小时）
     */
    @ApiModelProperty(value = "监测审核时限（单位：小时）")
    private Integer checkDeadlineH ;

    /**
     * 传染病传播途径分类（肠道传染病、呼吸道症候群、自然疫源及虫煤传染病、血源及性传播传染病）
     */
    @ApiModelProperty(value = "传染病传播途径分类（肠道传染病、呼吸道症候群、自然疫源及虫煤传染病、血源及性传播传染病）")
    private String transmitType ;

    /**
     * 单病例上报标识（1是，0否）
     */
    @ApiModelProperty(value = "单病例上报标识（1是，0否）")
    private String singleFlag ;

}
