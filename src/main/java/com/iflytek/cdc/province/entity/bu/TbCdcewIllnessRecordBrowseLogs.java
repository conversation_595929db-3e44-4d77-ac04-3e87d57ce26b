package com.iflytek.cdc.province.entity.bu;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.cdc.edr.annotation.Sensitive;
import com.iflytek.cdc.edr.enums.SensitiveTypeEnum;
import com.iflytek.cdc.province.enums.StatusEnum;
import com.iflytek.cdc.province.model.edr.vo.RetrievalLogExcelVO;
import com.iflytek.cdc.province.utils.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * 调阅日志表;
 * <AUTHOR> dingyuan
 * @date : 2025-1-16
 */
@ApiModel(value = "调阅日志表")
@Data
public class TbCdcewIllnessRecordBrowseLogs implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * UUID，主键，用于唯一标识每一条记录
     */
    @ApiModelProperty(value = "UUID，主键，用于唯一标识每一条记录")
    private String id ;

    /**
     * 疾病档案ID
     */
    @ApiModelProperty(value = "疾病档案ID")
    private String archiveId;

    /**
     * 患者ID
     */
    @ApiModelProperty(value = "患者ID")
    private String patientId ;

    /**
     * 患者姓名
     */
    @ApiModelProperty(value = "患者姓名")
    @Sensitive(type = SensitiveTypeEnum.PATIENT)
    private String patientName ;

    /**
     * 调阅用户ID
     */
    @ApiModelProperty(value = "调阅用户ID")
    private String browsePersonId ;

    /**
     * 调阅用户姓名
     */
    @ApiModelProperty(value = "调阅用户姓名")
    private String browsePerson ;

    /**
     * 调阅日期
     */
    @ApiModelProperty(value = "调阅日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date browseDate ;

    /**
     * 调阅状态，使用短整型
     */
    @ApiModelProperty(value = "调阅状态，使用短整型")
    private Integer status ;

    /**
     * 数据权限-住址id
     */
    @ApiModelProperty(value = "数据权限-住址id")
    private String permitLivingAreaCode ;

    /**
     * 数据权限-工作单位地址id
     */
    @ApiModelProperty(value = "数据权限-工作单位地址id")
    private String permitCompanyAreaCode ;

    /**
     * 数据权限-医疗机构地址id
     */
    @ApiModelProperty(value = "数据权限-医疗机构地址id")
    private String permitOrgAreaCode ;

    /**
     * 记录创建时间
     */
    @ApiModelProperty(value = "记录创建时间")
    private Date createTime ;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private String creatorId ;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String creator ;

    public static RetrievalLogExcelVO of(TbCdcewIllnessRecordBrowseLogs log){

        RetrievalLogExcelVO excelVO = new RetrievalLogExcelVO();
        BeanUtils.copyProperties(log, excelVO);
        excelVO.setRetrievalDate(DateUtils.formatDate(log.getBrowseDate(), DateUtils.DATE_FORMAT));
        excelVO.setRetrievalStatus(StatusEnum.getRetrievalDescByCode(log.getStatus()));
        return excelVO;
    }

}
