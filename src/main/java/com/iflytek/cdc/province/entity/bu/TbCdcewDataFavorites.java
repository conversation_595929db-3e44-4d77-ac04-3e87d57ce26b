package com.iflytek.cdc.province.entity.bu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 收藏夹表;
 * <AUTHOR> dingyuan
 * @date : 2024-5-8
 */
@ApiModel(value = "收藏夹表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TbCdcewDataFavorites implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id ;

    /**
     * 收藏夹名称
     */
    @ApiModelProperty(value = "收藏夹名称")
    private String favoritesName ;

    /**
     * 收藏夹类型
     */
    @ApiModelProperty(value = "收藏夹类型")
    private String favoritesType ;

    /**
     * 数据模型版本id
     */
    @ApiModelProperty(value = "数据模型版本id")
    private String modelVersionId ;

    /**
     * 数据量
     */
    @ApiModelProperty(value = "数据量")
    private Integer totalCount ;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String notes ;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime ;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator ;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime ;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updater ;

    /**
     * 删除标识: 0-未删除,1-已删除
     */
    @ApiModelProperty(value = "删除标识: 0-未删除,1-已删除")
    private String deleteFlag ;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private String creatorId;

    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id")
    private String updaterId;

    /**
     * 模块code
     */
    @ApiModelProperty(value = "模块code")
    private String appCode;

}
