package com.iflytek.cdc.province.entity.bu;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 订正记录表;
 * <AUTHOR> dingyuan
 * @date : 2024-4-22
 */
@ApiModel(value = "订正记录表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TbCdcewCorrectionRecord implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * uuid
     */
    @ApiModelProperty(value = "uuid")
    private String id ;

    /**
     * 数据权限-住址编码
     */
    @ApiModelProperty(value = "数据权限-住址编码")
    private String permitLivingAreaCode ;

    /**
     * 数据权限-工作单位地址编码
     */
    @ApiModelProperty(value = "数据权限-工作单位地址编码")
    private String permitCompanyAreaCode ;

    /**
     * 数据权限-医疗机构地址编码
     */
    @ApiModelProperty(value = "数据权限-医疗机构地址编码")
    private String permitOrgAreaCode ;

    /**
     * 模型 id
     */
    @ApiModelProperty(value = "模型 id")
    private String modelId ;

    /**
     * 字段 id
     */
    @ApiModelProperty(value = "字段 id")
    private String fieldId ;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String fieldName ;

    /**
     * 原始值
     */
    @ApiModelProperty(value = "原始值")
    private String originValue ;

    /**
     * 修改后值
     */
    @ApiModelProperty(value = "修改后值")
    private String newValue ;

    /**
     *
     */
    @ApiModelProperty(value = "疾病类型")
    private String diseaseType ;

    /**
     * 病例 id
     */
    @ApiModelProperty(value = "病例 id")
    private String processId ;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String patientName ;

    /**
     * 报卡 id
     */
    @ApiModelProperty(value = "报卡 id")
    private String reportId ;

    /**
     * 报告时间
     */
    @ApiModelProperty(value = "报告时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime ;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime ;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator ;

    /**
     * 创建人 id
     */
    @ApiModelProperty(value = "创建人 id")
    private String creatorId ;

    /**
     * 疾病code
     */
    @ApiModelProperty(value = "疾病code")
    private String diseaseCode ;

    /**
     * 疾病name
     */
    @ApiModelProperty(value = "疾病name")
    private String diseaseName ;

}
