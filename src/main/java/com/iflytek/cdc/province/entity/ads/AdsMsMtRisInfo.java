package com.iflytek.cdc.province.entity.ads;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 诊疗(medical service)-医技-检查详情表;
 * <AUTHOR> dingyuan
 * @date : 2024-5-24
 */
@ApiModel(value = "诊疗(medical service)-医技-检查详情表")
@Data
public class AdsMsMtRisInfo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 记录id（uuid）
     */
    @ApiModelProperty(value = "记录id（uuid）")
    private String id ;

    /**
     * 病历id
     */
    @ApiModelProperty(value = "病历id")
    private String medicalId ;

    /**
     * 就诊事件id
     */
    @ApiModelProperty(value = "就诊事件id")
    private String eventId ;

    /**
     * 病程id
     */
    @ApiModelProperty(value = "病程id")
    private String processId ;

    /**
     * 生命周期id
     */
    @ApiModelProperty(value = "生命周期id")
    private String lifeId ;

    /**
     * ETL创建时间
     */
    @ApiModelProperty(value = "ETL创建时间")
    private Date etlCreateDatetime ;

    /**
     * ETL更新时间
     */
    @ApiModelProperty(value = "ETL更新时间")
    private Date etlUpdateDatetime ;

    /**
     * 患者（不变）信息-姓名
     */
    @ApiModelProperty(value = "患者（不变）信息-姓名")
    private String patientName ;

    /**
     * 患者（不变）信息-性别代码
     */
    @ApiModelProperty(value = "患者（不变）信息-性别代码")
    private String patientSexCode ;

    /**
     * 患者（不变）信息-性别（有字典，313001）
     */
    @ApiModelProperty(value = "患者（不变）信息-性别（有字典，313001）")
    private String patientSexName ;

    /**
     * 患者（不变）信息-证件类型编码
     */
    @ApiModelProperty(value = "患者（不变）信息-证件类型编码")
    private String patientIdentityTypeCode ;

    /**
     * 患者（不变）信息-证件类型名称
     */
    @ApiModelProperty(value = "患者（不变）信息-证件类型名称")
    private String patientIdentityType ;

    /**
     * 患者（不变）信息-身份证号/证件号码
     */
    @ApiModelProperty(value = "患者（不变）信息-身份证号/证件号码")
    private String patientIdentityNo ;

    /**
     * 患者（不变）信息-出生日期
     */
    @ApiModelProperty(value = "患者（不变）信息-出生日期")
    private Date patientBirthDay ;

    /**
     * 患者（可变）信息-人员id
     */
    @ApiModelProperty(value = "患者（可变）信息-人员id")
    private String patientId ;

    /**
     * 患者（可变）信息-年龄
     */
    @ApiModelProperty(value = "患者（可变）信息-年龄")
    private Integer patientAge ;

    /**
     * 患者（可变）信息-年龄单位
     */
    @ApiModelProperty(value = "患者（可变）信息-年龄单位")
    private String patientAgeUnit ;

    /**
     * 患者（可变）信息-患者年龄（单位岁）
     */
    @ApiModelProperty(value = "患者（可变）信息-患者年龄（单位岁）")
    private Double patientAgeStd ;

    /**
     * 就诊流水号
     */
    @ApiModelProperty(value = "就诊流水号")
    private String serialNo ;

    /**
     * 就诊类型编码
     */
    @ApiModelProperty(value = "就诊类型编码")
    private String visitTypeCode ;

    /**
     * 就诊类型
     */
    @ApiModelProperty(value = "就诊类型")
    private String visitType ;

    /**
     * 就诊时间
     */
    @ApiModelProperty(value = "就诊时间")
    private Date visitTime ;

    /**
     * 是否有传染病报卡
     */
    @ApiModelProperty(value = "是否有传染病报卡")
    private String infectReportFlag ;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private String orgId ;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String orgName ;

    /**
     * 机构类型编码
     */
    @ApiModelProperty(value = "机构类型编码")
    private String orgTypeCode ;

    /**
     * 机构类型
     */
    @ApiModelProperty(value = "机构类型")
    private String orgType ;

    /**
     * 申请-机构id
     */
    @ApiModelProperty(value = "申请-机构id")
    private String applyOrgId ;

    /**
     * 申请-机构名称
     */
    @ApiModelProperty(value = "申请-机构名称")
    private String applyOrgName ;

    /**
     * 申请-机构类型编码
     */
    @ApiModelProperty(value = "申请-机构类型编码")
    private String applyOrgTypeCode ;

    /**
     * 申请-机构类型
     */
    @ApiModelProperty(value = "申请-机构类型")
    private String applyOrgType ;

    /**
     * 申请-科室编码
     */
    @ApiModelProperty(value = "申请-科室编码")
    private String applyDeptCode ;

    /**
     * 申请-科室名称
     */
    @ApiModelProperty(value = "申请-科室名称")
    private String applyDeptName ;

    /**
     * 申请-申请人编码
     */
    @ApiModelProperty(value = "申请-申请人编码")
    private String applyDocId ;

    /**
     * 申请-申请人姓名
     */
    @ApiModelProperty(value = "申请-申请人姓名")
    private String applyDocName ;

    /**
     * 申请-申请单描述
     */
    @ApiModelProperty(value = "申请-申请单描述")
    private String applyNote ;

    /**
     * 检查-机构编码
     */
    @ApiModelProperty(value = "检查-机构编码")
    private String examOrgId ;

    /**
     * 检查-机构名称
     */
    @ApiModelProperty(value = "检查-机构名称")
    private String examOrgName ;

    /**
     * 检查-执行科室代码
     */
    @ApiModelProperty(value = "检查-执行科室代码")
    private String examDeptCode ;

    /**
     * 检查-执行科室名称
     */
    @ApiModelProperty(value = "检查-执行科室名称")
    private String examDeptName ;

    /**
     * 检查-部位代码
     */
    @ApiModelProperty(value = "检查-部位代码")
    private String examPartCode ;

    /**
     * 检查-部位
     */
    @ApiModelProperty(value = "检查-部位")
    private String examPart ;

    /**
     * 检查-检测人编码
     */
    @ApiModelProperty(value = "检查-检测人编码")
    private String examDocId ;

    /**
     * 检查-检测人姓名
     */
    @ApiModelProperty(value = "检查-检测人姓名")
    private String examDocName ;

    /**
     * 检查-检测时间
     */
    @ApiModelProperty(value = "检查-检测时间")
    private Date examTime ;

    /**
     * 检查-项目分类代码
     */
    @ApiModelProperty(value = "检查-项目分类代码")
    private String examClassCode ;

    /**
     * 检查-项目分类名称（CT、X线、胃镜、B超等）
     */
    @ApiModelProperty(value = "检查-项目分类名称（CT、X线、胃镜、B超等）")
    private String examClass ;

    /**
     * 检查-项目编码
     */
    @ApiModelProperty(value = "检查-项目编码")
    private String examCode ;

    /**
     * 检查-项目名称
     */
    @ApiModelProperty(value = "检查-项目名称")
    private String examName ;

    /**
     * 检查-仪器代码
     */
    @ApiModelProperty(value = "检查-仪器代码")
    private String examInstrumentCode ;

    /**
     * 检查-仪器名称
     */
    @ApiModelProperty(value = "检查-仪器名称")
    private String examInstrument ;

    /**
     * 检查-检查所见
     */
    @ApiModelProperty(value = "检查-检查所见")
    private String examView ;

    /**
     * 检查-检查结论
     */
    @ApiModelProperty(value = "检查-检查结论")
    private String examResult ;

    /**
     * 报告单号
     */
    @ApiModelProperty(value = "报告单号")
    private String reportId ;

    /**
     * 报告标题
     */
    @ApiModelProperty(value = "报告标题")
    private String reportTitle ;

    /**
     * 报告时间
     */
    @ApiModelProperty(value = "报告时间")
    private Date reportTime ;

    /**
     * 报告类别代码
     */
    @ApiModelProperty(value = "报告类别代码")
    private String reportTypeCode ;

    /**
     * 报告类别名称
     */
    @ApiModelProperty(value = "报告类别名称")
    private String reportType ;

    /**
     * 报告人编码
     */
    @ApiModelProperty(value = "报告人编码")
    private String reporterId ;

    /**
     * 报告人姓名
     */
    @ApiModelProperty(value = "报告人姓名")
    private String reporter ;

    /**
     * 报告审核时间
     */
    @ApiModelProperty(value = "报告审核时间")
    private Date reportAuditTime ;

    /**
     * 报告审核人编码
     */
    @ApiModelProperty(value = "报告审核人编码")
    private String reportAuditorId ;

    /**
     * 报告审核人姓名
     */
    @ApiModelProperty(value = "报告审核人姓名")
    private String reportAuditor ;

    /**
     * 报告附件路径
     */
    @ApiModelProperty(value = "报告附件路径")
    private String reportUrl ;

    /**
     * 影像数据id
     */
    @ApiModelProperty(value = "影像数据id")
    private String dicomId ;

}
