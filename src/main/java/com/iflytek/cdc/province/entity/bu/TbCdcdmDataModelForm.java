package com.iflytek.cdc.province.entity.bu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 数据模型配置表
 */
@ApiModel(description="数据模型配置表")
@Data
public class TbCdcdmDataModelForm implements Serializable {
    /**
    * 主键ID
    */
    @ApiModelProperty(value="主键ID")
    private String modelFormId;

    /**
    * 数据模型ID
    */
    @ApiModelProperty(value="数据模型ID")
    private String modelId;

    /**
    * 数据模型版本ID
    */
    @ApiModelProperty(value="数据模型版本ID")
    private String modelVersionId;

    /**
    * 表单名称
    */
    @ApiModelProperty(value="表单名称")
    private String formName;

    /**
    * 表单可重复
    */
    @ApiModelProperty(value="表单可重复")
    private Integer isRepeat;

    /**
    * 表单最大可重复次数
    */
    @ApiModelProperty(value="表单最大可重复次数")
    private Integer maxRepeatCnt;

    /**
    * 表单唯一标识
    */
    @ApiModelProperty(value="表单唯一标识")
    private String formIdentityColumn;

    /**
    * 表单json
    */
    @ApiModelProperty(value="表单json")
    private String formJson;

    /**
    * 是否删除
    */
    @ApiModelProperty(value="是否删除")
    private Integer isDeleted;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
    * 创建人
    */
    @ApiModelProperty(value="创建人")
    private String creator;

    /**
    * 修改时间
    */
    @ApiModelProperty(value="修改时间")
    private Date updateTime;

    /**
    * 修改人
    */
    @ApiModelProperty(value="修改人")
    private String updater;

    /**
     * 排序标识
     */
    @ApiModelProperty(value="排序标识")
    private Integer orderFlag;

    /**
     * 引用模型的模型id，若没有引用为空
     * */
    @ApiModelProperty(value="引用模型的模型id，若没有引用为空")
    private String quoteModel;

    private static final long serialVersionUID = 1L;
}