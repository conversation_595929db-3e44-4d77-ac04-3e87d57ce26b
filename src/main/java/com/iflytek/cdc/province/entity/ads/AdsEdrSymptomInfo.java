package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * edr症状信息
 */
@Data
@TableName(value = "ads.ads_edr_symptom_info")
public class AdsEdrSymptomInfo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 采集创建时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * 采集更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;

    /**
     * 事件信息ID
     */
    @TableField(value = "event_id")
    private String eventId;

    /**
     * 患者ID
     */
    @TableField(value = "patient_id")
    private String patientId;

    /**
     * 主索引ID
     */
    @TableField(value = "empi_id")
    private String empiId;

    /**
     * 就诊日期时间
     */
    @TableField(value = "serial_number")
    private Date serialNumber;

    /**
     * 症状出现日期
     */
    @TableField(value = "afp_palsy_date")
    private Date afpPalsyDate;

    /**
     * 症状代码
     */
    @TableField(value = "symptom_code")
    private String symptomCode;

    /**
     * 现住地址代码
     */
    @TableField(value = "current_addr_code")
    private String currentAddrCode;

    /**
     * 现住地区名称
     */
    @TableField(value = "current_addr")
    private String currentAddr;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 数据来源ID
     */
    @TableField(value = "source_id")
    private String sourceId;

    /**
     * 主索引 ID
     */
    @TableField(value = "mpi_id")
    private String mpiId;

    /**
     * 数据修改机构名称
     */
    @TableField(value = "update_org_name")
    private String updateOrgName;

    /**
     * 修改机构
     */
    @TableField(value = "update_org")
    private String updateOrg;

    /**
     * 创建机构
     */
    @TableField(value = "create_org")
    private String createOrg;

    /**
     * 创建机构名称
     */
    @TableField(value = "create_org_name")
    private String createOrgName;
}