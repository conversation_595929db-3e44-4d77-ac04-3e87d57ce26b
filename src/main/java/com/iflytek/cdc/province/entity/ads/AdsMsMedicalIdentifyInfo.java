package com.iflytek.cdc.province.entity.ads;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 诊疗(medical service)-病历识别详情信息（只要重新识别了都要有记录）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="AdsMsMedicalIdentifyInfo对象", description="诊疗(medical service)-病历识别详情信息（只要重新识别了都要有记录）")
public class AdsMsMedicalIdentifyInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "病历识别id(uuid)")
    @TableId(value = "identify_id")
    private String identifyId;

    @ApiModelProperty(value = "病历id")
    private String medicalId;

    @ApiModelProperty(value = "就诊事件id")
    private String eventId;

    @ApiModelProperty(value = "病程id")
    private String processId;

    @ApiModelProperty(value = "生命周期id")
    private String lifeId;

    @ApiModelProperty(value = "ETL创建时间")
    private LocalDateTime etlCreateDatetime;

    @ApiModelProperty(value = "ETL更新时间")
    private LocalDateTime etlUpdateDatetime;

    @ApiModelProperty(value = "姓名")
    private String patientName;

    @ApiModelProperty(value = "性别（有字典，313001）")
    private String patientSexName;

    @ApiModelProperty(value = "身份证号")
    private String patientIdentityNo;

    @ApiModelProperty(value = "出生日期")
    private LocalDate patientBirthDay;

    @ApiModelProperty(value = "人员id")
    private String patientId;

    @ApiModelProperty(value = "患者年龄（单位岁）")
    private BigDecimal patientAge;

    @ApiModelProperty(value = "患者联系方式")
    private String patientPhone;

    @ApiModelProperty(value = "现住详细地址")
    private String livingAddrDetail;

    @ApiModelProperty(value = "现住址场所类别")
    private String livingAddrPoi;

    @ApiModelProperty(value = "现住址类别")
    private String livingAddrType;

    @ApiModelProperty(value = "现住址标化地址")
    private String livingAddrDetailStd;

    @ApiModelProperty(value = "现住地-省编码")
    private String livingAddrProvinceCode;

    @ApiModelProperty(value = "现住地-省")
    private String livingAddrProvince;

    @ApiModelProperty(value = "现住地-市编码")
    private String livingAddrCityCode;

    @ApiModelProperty(value = "现住地-市")
    private String livingAddrCity;

    @ApiModelProperty(value = "现住地-行政区编码")
    private String livingAddrDistrictCode;

    @ApiModelProperty(value = "现住地-行政区")
    private String livingAddrDistrict;

    @ApiModelProperty(value = "现住址-功能区编码")
    private String livingAddrFuncDistrictCode;

    @ApiModelProperty(value = "现住址-功能区")
    private String livingAddrFuncDistrict;

    @ApiModelProperty(value = "现住址-街道编码")
    private String livingAddrStreetCode;

    @ApiModelProperty(value = "现住址-街道")
    private String livingAddrStreet;

    @ApiModelProperty(value = "现住址-街道中心经度")
    private BigDecimal livingAddrStreetLongitude;

    @ApiModelProperty(value = "现住址-街道中心纬度")
    private BigDecimal livingAddrStreetLatitude;

    @ApiModelProperty(value = "现住址-经度")
    private BigDecimal livingAddrLongitude;

    @ApiModelProperty(value = "现住址-经度")
    private BigDecimal livingAddrLatitude;

    @ApiModelProperty(value = "人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）")
    private String personType;

    @ApiModelProperty(value = "职业")
    private String job;

    @ApiModelProperty(value = "职业风险")
    private String jobRisk;

    @ApiModelProperty(value = "工作单位/学校名称")
    private String company;

    @ApiModelProperty(value = "单位/学校标化地址")
    private String companyAddress;

    @ApiModelProperty(value = "单位-省编码")
    private String companyProvinceCode;

    @ApiModelProperty(value = "单位-省")
    private String companyProvince;

    @ApiModelProperty(value = "单位-市编码")
    private String companyCityCode;

    @ApiModelProperty(value = "单位-市")
    private String companyCity;

    @ApiModelProperty(value = "单位-行政区编码")
    private String companyDistrictCode;

    @ApiModelProperty(value = "单位-行政区")
    private String companyDistrict;

    @ApiModelProperty(value = "单位-功能区编码")
    private String companyFuncDistrictCode;

    @ApiModelProperty(value = "单位-功能区")
    private String companyFunctionDistrict;

    @ApiModelProperty(value = "单位-街道编码")
    private String companyStreetCode;

    @ApiModelProperty(value = "单位-街道")
    private String companyStreet;

    @ApiModelProperty(value = "单位-街道中心经度")
    private BigDecimal companyStreetLongitude;

    @ApiModelProperty(value = "单位-街道中心维度")
    private BigDecimal companyStreetLatitude;

    @ApiModelProperty(value = "单位-经度")
    private BigDecimal companyLongitude;

    @ApiModelProperty(value = "单位-纬度")
    private BigDecimal companyLatitude;

    @ApiModelProperty(value = "机构id（主索引）")
    private String orgId;

    @ApiModelProperty(value = "机构名称")
    private String orgName;

    @ApiModelProperty(value = "机构类别（等级医院、基层医疗）")
    private String orgClass;

    @ApiModelProperty(value = "机构类型名称(204003)")
    private String orgTypeName;

    @ApiModelProperty(value = "机构详细地址")
    private String orgAddrDetail;

    @ApiModelProperty(value = "机构标化地址")
    private String orgAddrDetailStd;

    @ApiModelProperty(value = "机构-省编码")
    private String orgAddrProvinceCode;

    @ApiModelProperty(value = "机构-省")
    private String orgAddrProvince;

    @ApiModelProperty(value = "机构-市编码")
    private String orgAddrCityCode;

    @ApiModelProperty(value = "机构-市")
    private String orgAddrCity;

    @ApiModelProperty(value = "机构-行政区编码")
    private String orgAddrDistrictCode;

    @ApiModelProperty(value = "机构-行政区")
    private String orgAddrDistrict;

    @ApiModelProperty(value = "机构-功能区编码")
    private String orgAddrFunctionDistrictCode;

    @ApiModelProperty(value = "机构-功能区")
    private String orgAddrFunctionDistrict;

    @ApiModelProperty(value = "机构-街道编码")
    private String orgAddrStreetCode;

    @ApiModelProperty(value = "机构-街道")
    private String orgAddrStreet;

    @ApiModelProperty(value = "机构经度")
    private BigDecimal orgLongitude;

    @ApiModelProperty(value = "机构纬度")
    private BigDecimal orgLatitude;

    @ApiModelProperty(value = "发病时间")
    private LocalDateTime onsetTime;

    @ApiModelProperty(value = "就诊时间")
    private LocalDateTime visitTime;

    @ApiModelProperty(value = "就诊科室编码")
    private String deptCode;

    @ApiModelProperty(value = "就诊科室")
    private String deptName;

    @ApiModelProperty(value = "主诉")
    private String suit;

    @ApiModelProperty(value = "症状")
    private String symptom;

    @ApiModelProperty(value = "诊断信息（json）")
    private String diagJson;

    @ApiModelProperty(value = "病原检测信息（json）")
    private String pathogenJson;

    @ApiModelProperty(value = "用药信息（json）")
    private String drugJson;

    @ApiModelProperty(value = "报告时间")
    private LocalDateTime identifyTime;

    @ApiModelProperty(value = "报告异常类别（症候群病例、传染病病例）")
    private String identifyDiseaseType;

    @ApiModelProperty(value = "报告疾病编码")
    private String identifyDiseaseCode;

    @ApiModelProperty(value = "报告疾病名称(传染病名称/症候去名称/普通疾病名称)")
    private String identifyDiseaseName;

    @ApiModelProperty(value = "传染病分类（甲类、乙类、丙类、其它法定、其它非法定）")
    private String identifyInfectType;

    @ApiModelProperty(value = "传染病传播途径分类（（肠道传染病、呼吸道症候群、自然疫源及虫煤传染病、血源及性传播传染病））")
    private String identifyInfectTransmitType;

    @ApiModelProperty(value = "监测结果依据类型（诊断、检查、检验等）")
    private String identifySupportType;

    @ApiModelProperty(value = "报告类型（系统报告、人工修正）")
    private String identifyType;

    @ApiModelProperty(value = "报告机构编码")
    private String identifyOrgId;

    @ApiModelProperty(value = "报告机构名称")
    private String identifyOrgName;

    @ApiModelProperty(value = "转归时间")
    private LocalDateTime outcomeTime;

    @ApiModelProperty(value = "转归状态（治愈、治疗成功、死亡、带病）")
    private String outcomeStatus;

    @ApiModelProperty(value = "死亡原因（详细的，因该病死亡，其他）")
    private String deadReason;

    @ApiModelProperty(value = "审核结果（1是，0否）")
    private String checkResult;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime checkTime;

    @ApiModelProperty(value = "1-甲类管理")
    private String managementTypeCode;


}
