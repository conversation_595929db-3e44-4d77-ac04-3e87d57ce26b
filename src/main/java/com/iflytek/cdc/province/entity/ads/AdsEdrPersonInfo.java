package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 个人主数据信息
 */
@Data
@TableName(value = "ads.ads_edr_person_info")
public class AdsEdrPersonInfo {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 采集创建时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * 采集更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;

    /**
     * 创建机构
     */
    @TableField(value = "create_org")
    private String createOrg;

    /**
     * 所属机构名称
     */
    @TableField(value = "create_org_name")
    private String createOrgName;

    /**
     * 主索引ID
     */
    @TableField(value = "empi_id")
    private String empiId;

    /**
     * *患者姓名
     */
    @TableField(value = "patient_name")
    private String patientName;

    /**
     * *身份证号
     */
    @TableField(value = "id_card")
    private String idCard;

    /**
     * 出生日期
     */
    @TableField(value = "birth_date")
    private Date birthDate;

    /**
     * 性别代码
     */
    @TableField(value = "gender_code")
    private String genderCode;

    /**
     * 性别名称
     */
    @TableField(value = "gender")
    private String gender;

    /**
     * 曾用姓名
     */
    @TableField(value = "former_name")
    private String formerName;

    /**
     * 国籍代码
     */
    @TableField(value = "nationality_code")
    private String nationalityCode;

    /**
     * 国籍/地区名称
     */
    @TableField(value = "nationality")
    private String nationality;

    /**
     * 民族代码
     */
    @TableField(value = "nation_code")
    private String nationCode;

    /**
     * 民族名称
     */
    @TableField(value = "nation")
    private String nation;

    /**
     * 婚姻状况代码
     */
    @TableField(value = "marital_status_code")
    private String maritalStatusCode;

    /**
     * 婚姻状况名称
     */
    @TableField(value = "marital_status")
    private String maritalStatus;

    /**
     * 户籍地区编码
     */
    @TableField(value = "permanent_addr_code")
    private String permanentAddrCode;

    /**
     * 户籍地址名称
     */
    @TableField(value = "permanent_addr")
    private String permanentAddr;

    /**
     * *户籍详细地址
     */
    @TableField(value = "permanent_addr_detail")
    private String permanentAddrDetail;

    /**
     * 临时身份证号
     */
    @TableField(value = "temp_id_card")
    private String tempIdCard;

    /**
     * *监护人ID
     */
    @TableField(value = "contacts_id_card")
    private String contactsIdCard;

    /**
     * *联系人/监护人姓名
     */
    @TableField(value = "contacts_name")
    private String contactsName;

    /**
     * *联系电话
     */
    @TableField(value = "contacts_tel")
    private String contactsTel;

    /**
     * 现住地址代码
     */
    @TableField(value = "current_addr_code")
    private String currentAddrCode;

    /**
     * 现住地区名称
     */
    @TableField(value = "current_addr")
    private String currentAddr;

    /**
     * *现住详细地址
     */
    @TableField(value = "current_addr_detail")
    private String currentAddrDetail;

    /**
     * 现住址标化地址
     */
    @TableField(value = "current_addr_detail_std")
    private String currentAddrDetailStd;

    /**
     * 现住址标化-省编码
     */
    @TableField(value = "current_addr_province_code")
    private String currentAddrProvinceCode;

    /**
     * 现住址标化-省
     */
    @TableField(value = "current_addr_province")
    private String currentAddrProvince;

    /**
     * 现住址标化-市编码
     */
    @TableField(value = "current_addr_city_code")
    private String currentAddrCityCode;

    /**
     * 现住址标化-市
     */
    @TableField(value = "current_addr_city")
    private String currentAddrCity;

    /**
     * 现住址标化-行政区编码
     */
    @TableField(value = "current_addr_district_code")
    private String currentAddrDistrictCode;

    /**
     * 现住址标化-行政区
     */
    @TableField(value = "current_addr_district")
    private String currentAddrDistrict;

    /**
     * 现住址标化-功能区编码
     */
    @TableField(value = "current_addr_func_district_code")
    private String currentAddrFuncDistrictCode;

    /**
     * 现住址标化-功能区
     */
    @TableField(value = "current_addr_func_district")
    private String currentAddrFuncDistrict;

    /**
     * 现住址标化-街道编码
     */
    @TableField(value = "current_addr_street_code")
    private String currentAddrStreetCode;

    /**
     * 现住址标化-街道
     */
    @TableField(value = "current_addr_street")
    private String currentAddrStreet;

    /**
     * 现住址标化-街道中心经度
     */
    @TableField(value = "current_addr_street_longitude")
    private BigDecimal currentAddrStreetLongitude;

    /**
     * 现住址标化-街道中心纬度
     */
    @TableField(value = "current_addr_street_latitude")
    private BigDecimal currentAddrStreetLatitude;

    /**
     * 现住址标化-经度
     */
    @TableField(value = "current_addr_longitude")
    private BigDecimal currentAddrLongitude;

    /**
     * 现住址标化-经度
     */
    @TableField(value = "current_addr_latitude")
    private BigDecimal currentAddrLatitude;

    /**
     * 现住址标化场所类别
     */
    @TableField(value = "current_addr_poi")
    private String currentAddrPoi;

    /**
     * 现住址标化地址分类
     */
    @TableField(value = "current_addr_type")
    private String currentAddrType;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 数据来源ID
     */
    @TableField(value = "source_id")
    private String sourceId;

    /**
     * 主索引ID
     */
    @TableField(value = "mpi_id")
    private String mpiId;

    /**
     * 修改机构
     */
    @TableField(value = "update_org")
    private String updateOrg;

    /**
     * 修改机构
     */
    @TableField(value = "update_org_name")
    private String updateOrgName;

    /**
     * 医疗机构(机构主索引)-所属机构编码
     */
    @TableField(value = "create_org_id")
    private String createOrgId;
}