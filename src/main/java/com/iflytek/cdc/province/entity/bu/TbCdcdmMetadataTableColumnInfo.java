package com.iflytek.cdc.province.entity.bu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 表元信息
 */
@ApiModel(description = "表元信息")
@Data
public class TbCdcdmMetadataTableColumnInfo implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 表元信息ID
     */
    @ApiModelProperty(value = "表元信息ID")
    private String tableId;

    /**
     * 字段编码
     */
    @ApiModelProperty(value = "字段编码")
    private String columnCode;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String columnName;

    /**
     * 字段类型
     */
    @ApiModelProperty(value = "字段类型")
    private String columnType;

    /**
     * 字段名称描述
     */
    @ApiModelProperty(value = "字段名称描述")
    private String columnDesc;

    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    private String dataType;

    /**
     * 业务字段
     */
    @ApiModelProperty(value = "业务字段")
    private String businessColumn;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String memo;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String creator;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者")
    private String updater;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者ID")
    private String creatorId;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者ID")
    private String updaterId;

    /**
     * 删除标志
     */
    @ApiModelProperty(value = "删除标志")
    private String deleteFlag;

    private static final long serialVersionUID = 1L;
}