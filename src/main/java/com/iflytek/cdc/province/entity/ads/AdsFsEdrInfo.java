package com.iflytek.cdc.province.entity.ads;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 检索-edr详情表;
 * <AUTHOR> dingyuan
 * @date : 2025-3-5
 */
@ApiModel(value = "检索-edr详情表")
@Data
public class AdsFsEdrInfo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 生命周期id
     */
    @ApiModelProperty(value = "生命周期id")
    private String lifeId ;

    /**
     * 模型ID
     */
    @ApiModelProperty(value = "模型ID")
    private String modelId ;

    /**
     * 模型名称
     */
    @ApiModelProperty(value = "模型名称")
    private String modelName ;

    /**
     * 数据权限-住址编码
     */
    @ApiModelProperty(value = "数据权限-住址编码")
    private String permitLivingAreaCode ;

    /**
     * 数据权限-工作单位地址编码
     */
    @ApiModelProperty(value = "数据权限-工作单位地址编码")
    private String permitCompanyAreaCode ;

    /**
     * 数据权限-医疗机构地址编码
     */
    @ApiModelProperty(value = "数据权限-医疗机构地址编码")
    private String permitOrgAreaCode ;

    /**
     * 模型内容JSON
     */
    @ApiModelProperty(value = "模型内容JSON")
    private String contentJson ;

    /**
     * ETL创建时间
     */
    @ApiModelProperty(value = "ETL创建时间")
    private Date etlCreateDatetime ;

    /**
     * ETL更新时间
     */
    @ApiModelProperty(value = "ETL更新时间")
    private Date etlUpdateDatetime ;

    @ApiModelProperty(value = "个人疾病档案id")
    private String archiveId;

    @ApiModelProperty(value = "注销标识")
    private String deleteFlag;

}
