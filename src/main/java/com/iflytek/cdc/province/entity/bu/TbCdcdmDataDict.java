package com.iflytek.cdc.province.entity.bu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ;
 * <AUTHOR> dingyuan
 * @date : 2024-8-30
 */
@ApiModel(value = "")
@Data
public class TbCdcdmDataDict implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id ;

    /**
     * 编码（业务唯一键）
     */
    @ApiModelProperty(value = "编码（业务唯一键）")
    private String code ;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name ;

    /**
     * 层级数
     */
    @ApiModelProperty(value = "层级数")
    private Integer attrCount ;

    /**
     * 值域数
     */
    @ApiModelProperty(value = "值域数")
    private Integer attrValueCount ;

    /**
     * 1:值域;2:url
     */
    @ApiModelProperty(value = "1:值域;2:url")
    private String type ;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String notes ;

    /**
     * 状态（启用状态 1启用；0未启用）
     */
    @ApiModelProperty(value = "状态（启用状态 1启用；0未启用）")
    private Integer status ;

    /**
     * 删除标识： 0-未删除，1-已删除
     */
    @ApiModelProperty(value = "删除标识： 0-未删除，1-已删除")
    private String deleteFlag ;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime ;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime ;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private String creatorId ;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String creator ;

    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id")
    private String updaterId ;

    /**
     * 更新人name
     */
    @ApiModelProperty(value = "更新人name")
    private String updater ;

}
