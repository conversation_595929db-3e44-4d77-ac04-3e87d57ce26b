package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@ApiModel(value = "BizEdrInfo")
@EqualsAndHashCode(callSuper = false)
@TableName(value = "ads_biz_edr_info", schema = "ads")
public class AdsBizEdrInfo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private String id;

    private String empiId;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("身份证号")
    private String idCard;

    private String tempEmpiId;

    @ApiModelProperty("临时身份证号")
    private String oldName;

    @ApiModelProperty("临时身份证号")
    private String tempIdCard;

    @ApiModelProperty("死亡时间")
    private String deathDateTime;

    @ApiModelProperty("直接死亡原因")
    private String directDeadReasonName;

    @ApiModelProperty("根本死亡原因")
    private String rootDeadReasonName;

    @ApiModelProperty("间接死亡原因")
    private String indirectDeadReasonName;

    @ApiModelProperty("死亡诊断")
    private String deathDiagnosis;

    @ApiModelProperty("数据权限-住址编码")
    private String permitLivingAreaCode;

    @ApiModelProperty("数据权限-工作单位地址编码")
    private String permitCompanyAreaCode;

    @ApiModelProperty("数据权限-医疗机构地址编码")
    private String permitOrgAreaCode;
}
