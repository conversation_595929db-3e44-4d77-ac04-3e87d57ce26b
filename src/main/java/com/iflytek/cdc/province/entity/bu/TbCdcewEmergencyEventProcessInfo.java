package com.iflytek.cdc.province.entity.bu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 突发公共卫生事件病例表;
 * <AUTHOR> dingyuan
 * @date : 2025-5-7
 */
@ApiModel(value = "突发公共卫生事件病例表")
@Data
public class TbCdcewEmergencyEventProcessInfo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 病人姓名
     */
    @ApiModelProperty(value = "病人姓名")
    private String patientName ;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String patientSexName ;

    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    private Integer patientAge;

    /**
     * 年龄单位
     */
    @ApiModelProperty(value = "年龄单位")
    private String patientAgeUnit;

    /**
     * 职业
     */
    @ApiModelProperty(value = "职业")
    private String job;

    /**
     * 出生时间
     */
    @ApiModelProperty(value = "出生时间")
    private Date birthDay ;

    /**
     * 人群分类
     */
    @ApiModelProperty(value = "人群分类")
    private String personType ;

    /**
     * 人群分类其他
     */
    @ApiModelProperty(value = "人群分类其他")
    private String personTypeOther ;

    /**
     * 居住地省
     */
    @ApiModelProperty(value = "居住地省")
    private String livingProvinceCode ;

    /**
     * 居住地省名称
     */
    @ApiModelProperty(value = "居住地省名称")
    private String livingProvinceName ;

    /**
     * 居住地市
     */
    @ApiModelProperty(value = "居住地市")
    private String livingCityCode ;

    /**
     * 居住地市名称
     */
    @ApiModelProperty(value = "居住地市名称")
    private String livingCityName ;

    /**
     * 居住地区
     */
    @ApiModelProperty(value = "居住地区")
    private String livingDistrictCode ;

    /**
     * 居住地区名称
     */
    @ApiModelProperty(value = "居住地区名称")
    private String livingDistrictName ;

    /**
     * 居住地街道
     */
    @ApiModelProperty(value = "居住地街道")
    private String livingStreetCode ;

    /**
     * 居住地街道名称
     */
    @ApiModelProperty(value = "居住地街道名称")
    private String livingStreetName ;

    /**
     * 详细现住址
     */
    @ApiModelProperty(value = "详细现住址")
    private String livingAddrDetail ;

    /**
     * 现住址经度
     */
    @ApiModelProperty(value = "现住址经度")
    private Double livingAddrLongitude ;

    /**
     * 现住址纬度
     */
    @ApiModelProperty(value = "现住址纬度")
    private Double livingAddrLatitude ;

    /**
     * 公司省
     */
    @ApiModelProperty(value = "公司省")
    private String companyProvinceCode ;

    /**
     * 公司省名称
     */
    @ApiModelProperty(value = "公司省名称")
    private String companyProvinceName ;

    /**
     * 公司市
     */
    @ApiModelProperty(value = "公司市")
    private String companyCityCode ;

    /**
     * 公司市名称
     */
    @ApiModelProperty(value = "公司市名称")
    private String companyCityName ;

    /**
     * 公司区
     */
    @ApiModelProperty(value = "公司区")
    private String companyDistrictCode ;

    /**
     * 公司区名称
     */
    @ApiModelProperty(value = "公司区名称")
    private String companyDistrictName ;

    /**
     * 公司街道
     */
    @ApiModelProperty(value = "公司街道")
    private String companyStreetCode ;

    /**
     * 公司街道名称
     */
    @ApiModelProperty(value = "公司街道名称")
    private String companyStreetName ;

    /**
     * 工作单位/学校
     */
    @ApiModelProperty(value = "工作单位/学校")
    private String company ;

    /**
     * 公司经度
     */
    @ApiModelProperty(value = "公司经度")
    private Double companyAddrLongitude ;

    /**
     * 公司纬度
     */
    @ApiModelProperty(value = "公司纬度")
    private Double companyAddrLatitude ;

    /**
     * 机构省
     */
    @ApiModelProperty(value = "机构省")
    private String orgProvinceCode ;

    /**
     * 机构省名称
     */
    @ApiModelProperty(value = "机构省名称")
    private String orgProvinceName ;

    /**
     * 机构市
     */
    @ApiModelProperty(value = "机构市")
    private String orgCityCode ;

    /**
     * 机构市名称
     */
    @ApiModelProperty(value = "机构市名称")
    private String orgCityName ;

    /**
     * 机构区
     */
    @ApiModelProperty(value = "机构区")
    private String orgDistrictCode ;

    /**
     * 机构区名称
     */
    @ApiModelProperty(value = "机构区名称")
    private String orgDistrictName ;

    /**
     * 机构街道
     */
    @ApiModelProperty(value = "机构街道")
    private String orgStreetCode ;

    /**
     * 机构街道名称
     */
    @ApiModelProperty(value = "机构街道名称")
    private String orgStreetName ;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private String orgId ;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String orgName ;

    /**
     * 机构经度
     */
    @ApiModelProperty(value = "机构经度")
    private Double orgAddrLongitude ;

    /**
     * 机构纬度
     */
    @ApiModelProperty(value = "机构纬度")
    private Double orgAddrLatitude ;

    /**
     * 发病日期
     */
    @ApiModelProperty(value = "发病日期")
    private Date onsetTime ;

    /**
     * 首诊时间
     */
    @ApiModelProperty(value = "首诊时间")
    private Date firstVisitTime ;

    /**
     * 首诊单位id
     */
    @ApiModelProperty(value = "首诊单位id")
    private String firstVisitOrgId ;

    /**
     * 首诊单位
     */
    @ApiModelProperty(value = "首诊单位")
    private String firstVisitOrgName ;

    /**
     * 诊断日期
     */
    @ApiModelProperty(value = "诊断日期")
    private Date diagnoseTime ;

    /**
     * 疾病类型
     */
    @ApiModelProperty(value = "疾病类型")
    private String diseaseType ;

    /**
     * 最新诊断疾病code
     */
    @ApiModelProperty(value = "最新诊断疾病code")
    private String diseaseCode ;

    /**
     * 最新诊断疾病name
     */
    @ApiModelProperty(value = "最新诊断疾病name")
    private String diseaseName ;

    /**
     * 转归状态
     */
    @ApiModelProperty(value = "转归状态")
    private String outcomeStatus ;

    /**
     * 转归时间
     */
    @ApiModelProperty(value = "转归时间")
    private Date outcomeTime ;

    /**
     * 死亡时间
     */
    @ApiModelProperty(value = "死亡时间")
    private Date deathTime ;

    /**
     * 重症标识
     */
    @ApiModelProperty(value = "重症标识")
    private String severeFlag;

    /**
     * 重症时间
     */
    @ApiModelProperty(value = "重症时间")
    private Date severeTime;

    /**
     * 排除标识
     */
    @ApiModelProperty(value = "排除标识")
    private String outFlag;

    /**
     * 排除时间
     */
    @ApiModelProperty(value = "排除时间")
    private Date outTime;

    /**
     * 病例类型
     */
    @ApiModelProperty(value = "病例类型")
    private String processType ;

    @ApiModelProperty("主键id")
    private String id;
    @ApiModelProperty("创建者id")
    private String creatorId;
    @ApiModelProperty("创建者")
    private String creator;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("修改者id")
    private String updaterId;
    @ApiModelProperty("修改者")
    private String updater;
    @ApiModelProperty("修改时间")
    private Date updateTime;
    @ApiModelProperty("删除标记")
    private String deleteFlag;

}
