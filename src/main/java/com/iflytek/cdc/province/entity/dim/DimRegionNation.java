package com.iflytek.cdc.province.entity.dim;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 行政区划维表
 */
@ApiModel(value = "行政区划维表")
@Data
@TableName("dim.dim_region_nation")
public class DimRegionNation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 地区代码
     */
    @ApiModelProperty(value = "地区代码")
    private String regionCode;

    /**
     * 父级行政区划ID
     */
    @ApiModelProperty(value = "父级行政区划ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 省份编码
     */
    @ApiModelProperty(value = "省份编码")
    private String provinceCode;

    /**
     * 省份名称
     */
    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    /**
     * 城市编码
     */
    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String cityName;

    /**
     * 区县编码
     */
    @ApiModelProperty(value = "区县编码")
    private String districtCode;

    /**
     * 区县名称
     */
    @ApiModelProperty(value = "区县名称")
    private String districtName;

    /**
     * 街道编码
     */
    @ApiModelProperty(value = "街道编码")
    private String streetCode;

    /**
     * 街道名称
     */
    @ApiModelProperty(value = "街道名称")
    private String streetName;

    /**
     * 乡村
     */
    @ApiModelProperty(value = "乡村")
    private String village;

    /**
     * 初始
     */
    @ApiModelProperty(value = "初始")
    private String initial;

    /**
     * 区号
     */
    @ApiModelProperty(value = "区号")
    private String areacode;

    /**
     * ETL创建时间
     */
    @ApiModelProperty(value = "ETL创建时间")
    private Date etlCreateDatetime;

    /**
     * ETL更新时间
     */
    @ApiModelProperty(value = "ETL更新时间")
    private Date etlUpdateDatetime;
}
