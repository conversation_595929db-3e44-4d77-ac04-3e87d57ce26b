package com.iflytek.cdc.province.entity.bu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 核实记录表;
 * <AUTHOR> dingyuan
 * @date : 2024-4-22
 */
@ApiModel(value = "核实记录表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TbCdcdmCheckRecord implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ApiModelProperty( "")
    private String id ;

    /**
     * 病程id
     */
    @ApiModelProperty( "病程id")
    private String processId ;

    /**
     * 是否核实
     */
    @ApiModelProperty( "是否核实")
    private Integer isChecked ;

    /**
     * 核实人
     */
    @ApiModelProperty( "核实人")
    private String checker ;

    /**
     * 核实时间
     */
    @ApiModelProperty( "核实时间")
    private Date checkTime ;

    /**
     * 最新-就诊（vist）-病历id
     */
    @ApiModelProperty( "最新-就诊（vist）-病历id")
    private String lvMedicalId ;

    /**
     * 最新-识别（identify）-就诊的病历识别id
     */
    @ApiModelProperty( "最新-识别（identify）-就诊的病历识别id")
    private String liIdentifyId ;

    @ApiModelProperty(value = "疾病病程id")
    private String diseaseProcessId;

    @ApiModelProperty(value = "最新识别疾病code")
    private String liDiseaseCode;

    @ApiModelProperty(value = "最新识别疾病name")
    private String liDiseaseName;

    @ApiModelProperty(value = "最新识别病历id")
    private String liMedicalId;

    @ApiModelProperty(value = "疾病类型")
    private String diseaseType;

}
