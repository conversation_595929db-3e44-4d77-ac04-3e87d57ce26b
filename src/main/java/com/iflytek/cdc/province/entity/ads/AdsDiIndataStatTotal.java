package com.iflytek.cdc.province.entity.ads;

import java.util.Date;

/**
 * 数据接入(data integration)-接入数据统计-患者数
 */
public class AdsDiIndataStatTotal {
    /**
    * 统计日期
    */
    private Date day;

    /**
    * 机构类型（等级医院/基层医疗机构/病原实验室）
    */
    private String orgType;

    /**
    * 区域编码
    */
    private String areaCode;

    /**
    * ETL创建时间
    */
    private Date etlCreateDatetime;

    /**
    * ETL更新时间
    */
    private Date etlUpdateDatetime;

    /**
    * 统计期所在年
    */
    private Short year;

    /**
    * 统计期所在季度
    */
    private Short quarter;

    /**
    * 统计期所在月
    */
    private Short month;

    /**
    * 统计期所在旬类型（上、中、下）
    */
    private String tenDay;

    /**
    * 统计期所在周（年第几周）
    */
    private Short week;

    /**
    * 全局患者量-今日
    */
    private Integer globalPatientCnt;

    /**
    * 全局患者量-累计
    */
    private Integer globalPatientCntTotal;

    /**
    * 区域名称
    */
    private String areaName;

    /**
    * 区域类型(province/city/district/street)
    */
    private String areaType;

    public Date getDay() {
        return day;
    }

    public void setDay(Date day) {
        this.day = day;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public Date getEtlCreateDatetime() {
        return etlCreateDatetime;
    }

    public void setEtlCreateDatetime(Date etlCreateDatetime) {
        this.etlCreateDatetime = etlCreateDatetime;
    }

    public Date getEtlUpdateDatetime() {
        return etlUpdateDatetime;
    }

    public void setEtlUpdateDatetime(Date etlUpdateDatetime) {
        this.etlUpdateDatetime = etlUpdateDatetime;
    }

    public Short getYear() {
        return year;
    }

    public void setYear(Short year) {
        this.year = year;
    }

    public Short getQuarter() {
        return quarter;
    }

    public void setQuarter(Short quarter) {
        this.quarter = quarter;
    }

    public Short getMonth() {
        return month;
    }

    public void setMonth(Short month) {
        this.month = month;
    }

    public String getTenDay() {
        return tenDay;
    }

    public void setTenDay(String tenDay) {
        this.tenDay = tenDay;
    }

    public Short getWeek() {
        return week;
    }

    public void setWeek(Short week) {
        this.week = week;
    }

    public Integer getGlobalPatientCnt() {
        return globalPatientCnt;
    }

    public void setGlobalPatientCnt(Integer globalPatientCnt) {
        this.globalPatientCnt = globalPatientCnt;
    }

    public Integer getGlobalPatientCntTotal() {
        return globalPatientCntTotal;
    }

    public void setGlobalPatientCntTotal(Integer globalPatientCntTotal) {
        this.globalPatientCntTotal = globalPatientCntTotal;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getAreaType() {
        return areaType;
    }

    public void setAreaType(String areaType) {
        this.areaType = areaType;
    }
}