package com.iflytek.cdc.province.entity.dim;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 街道信息维表;
 * <AUTHOR> dingyuan
 * @date : 2024-8-8
 */
@ApiModel(value = "街道信息维表")
@Data
public class DimStreetInfo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 街道id
     */
    @ApiModelProperty(value = "街道id")
    private String streetId ;

    /**
     * 省份编码
     */
    @ApiModelProperty(value = "省份编码")
    private String provinceCode ;

    /**
     * 省份名称
     */
    @ApiModelProperty(value = "省份名称")
    private String provinceName ;

    /**
     * 城市编码
     */
    @ApiModelProperty(value = "城市编码")
    private String cityCode ;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String cityName ;

    /**
     * 区县编码
     */
    @ApiModelProperty(value = "区县编码")
    private String districtCode ;

    /**
     * 区县名称
     */
    @ApiModelProperty(value = "区县名称")
    private String districtName ;

    /**
     * 街道编码
     */
    @ApiModelProperty(value = "街道编码")
    private String streetCode ;

    /**
     * 街道名称
     */
    @ApiModelProperty(value = "街道名称")
    private String streetName ;

    /**
     * 街道中心点经度
     */
    @ApiModelProperty(value = "街道中心点经度")
    private Double streetLongitude ;

    /**
     * 街道中心点纬度
     */
    @ApiModelProperty(value = "街道中心点纬度")
    private Double streetLatitude ;

    /**
     * 街道来源
     */
    @ApiModelProperty(value = "街道来源")
    private String streetSource ;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String memo ;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createDatetime ;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateDatetime ;

    /**
     * ETL创建时间
     */
    @ApiModelProperty(value = "ETL创建时间")
    private Date etlCreateDatetime ;

    /**
     * ETL更新时间
     */
    @ApiModelProperty(value = "ETL更新时间")
    private Date etlUpdateDatetime ;

    /**
     * 行政区划区县编码
     */
    @ApiModelProperty(value = "行政区划区县编码")
    private String standardDistrictCode ;

    /**
     * 行政区划区县名称
     */
    @ApiModelProperty(value = "行政区划区县名称")
    private String standardDistrictName ;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String deleteFlag ;

    /**
     * 是否变更名称
     */
    @ApiModelProperty(value = "是否变更名称")
    private String isNameChanged ;

    /**
     * 原乡镇/街道名称
     */
    @ApiModelProperty(value = "原乡镇/街道名称")
    private String originalStreetName ;

    /**
     * 乡镇/街道别名
     */
    @ApiModelProperty(value = "乡镇/街道别名")
    private String aliasStreetName ;

}
