package com.iflytek.cdc.province.entity.bu;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tb_cdcew_report_qcs_stats", schema = "app")
@ApiModel(value = "ReportQcsStats对象", description = "")
public class ReportQcsStats implements Serializable {

    public static final String TB_NAME = "tb_cdcew_report_qcs_stats";
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "创建者id")
    private String creatorId;

    @ApiModelProperty(value = "创建者")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改者id")
    private String updaterId;

    @ApiModelProperty(value = "更新者")
    private String updater;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "删除标志，'1' 已删除，'0' 未删除")
    private String deleteFlag;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "主记录ID（外键）")
    private String mainRecordId;

    @ApiModelProperty(value = "报告单位")
    private String reportOrgName;

    @ApiModelProperty(value = "报告单位地址-省编码")
    private String reportOrgAddrProvinceCode;

    @ApiModelProperty(value = "报告单位地址-省名称")
    private String reportOrgAddrProvince;

    @ApiModelProperty(value = "报告单位地址-市编码")
    private String reportOrgAddrCityCode;

    @ApiModelProperty(value = "报告单位地址-市名称")
    private String reportOrgAddrCity;

    @ApiModelProperty(value = "报告单位地址-区编码")
    private String reportOrgAddrDistrictCode;

    @ApiModelProperty(value = "报告单位地址-区名称")
    private String reportOrgAddrDistrict;

    @ApiModelProperty(value = "报告单位地址-功能区编码")
    private String reportOrgAddrFuncDistrictCode;

    @ApiModelProperty(value = "报告单位地址-功能区名称")
    private String reportOrgAddrFuncDistrict;

    @ApiModelProperty(value = "报告单位地址-街道编码")
    private String reportOrgAddrStreetCode;

    @ApiModelProperty(value = "报告单位地址-街道名称")
    private String reportOrgAddrStreet;

    @ApiModelProperty(value = "应报告数（就诊识别数）")
    private Integer identifyCnt;

    @ApiModelProperty(value = "报告发病数（报卡报告数）")
    private Integer reportCnt;

    @ApiModelProperty(value = "漏报数")
    private Integer leakCnt;

    @ApiModelProperty(value = "迟报数")
    private Integer delayCnt;


}
