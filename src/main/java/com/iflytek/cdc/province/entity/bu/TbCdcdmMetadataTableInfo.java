package com.iflytek.cdc.province.entity.bu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@ApiModel(description="public.tb_cdcdm_metadata_table_info")
@Data
public class TbCdcdmMetadataTableInfo implements Serializable {
    /**
    * 主键
    */
    @ApiModelProperty(value="主键")
    private String id;

    /**
    * 表编码
    */
    @ApiModelProperty(value="表编码")
    private String tableCode;

    /**
    * 表schema
    */
    @ApiModelProperty(value="表schema")
    private String schema;

    /**
    * 表名称
    */
    @ApiModelProperty(value="表名称")
    private String tableName;

    /**
    * 表类型
    */
    @ApiModelProperty(value="表类型")
    private String tableType;

    /**
    * 表别名
    */
    @ApiModelProperty(value="表别名")
    private String alias;

    /**
    * 备注
    */
    @ApiModelProperty(value="备注")
    private String memo;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
    * 更新时间
    */
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
    * 创建者
    */
    @ApiModelProperty(value="创建者")
    private String creator;

    /**
    * 更新者
    */
    @ApiModelProperty(value="更新者")
    private String updater;

    /**
     * 创建者
     */
    @ApiModelProperty(value="创建者Id")
    private String creatorId;

    /**
     * 更新者
     */
    @ApiModelProperty(value="更新者Id")
    private String updaterId;

    /**
    * 删除标志
    */
    @ApiModelProperty(value="删除标志")
    private String deleteFlag;

    /**
     * 数据来源标识
     */
    @ApiModelProperty(value="数据来源标识")
    private String dataSourceKey;

    private String filterCondition;

    public String getFullTableName() {
        return schema + "." + tableName;
    }

    private static final long serialVersionUID = 1L;
}