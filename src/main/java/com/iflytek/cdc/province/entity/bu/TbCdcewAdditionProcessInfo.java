package com.iflytek.cdc.province.entity.bu;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 新增病例表;
 * <AUTHOR> dingyuan
 * @date : 2025-3-11
 */
@ApiModel(value = "新增病例表")
@Data
public class TbCdcewAdditionProcessInfo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * uuid
     */
    @ApiModelProperty(value = "uuid")
    private String id ;

    /**
     * 病程id
     */
    @ApiModelProperty(value = "病程id")
    private String processId ;

    /**
     * 数据模型id
     */
    @ApiModelProperty(value = "数据模型id")
    private String modelId ;

    /**
     * 主索引ID
     */
    @ApiModelProperty(value = "主索引ID")
    private String empiId ;

    /**
     * 数据权限-住址编码
     */
    @ApiModelProperty(value = "数据权限-住址编码")
    private String permitLivingAreaCode ;

    /**
     * 数据权限-工作单位地址编码
     */
    @ApiModelProperty(value = "数据权限-工作单位地址编码")
    private String permitCompanyAreaCode ;

    /**
     * 数据权限-医疗机构地址编码
     */
    @ApiModelProperty(value = "数据权限-医疗机构地址编码")
    private String permitOrgAreaCode ;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String patientName ;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String sexName ;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String identityNo ;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private Date birthDay ;

    /**
     * 新增病例的json
     */
    @ApiModelProperty(value = "新增病例的json")
    private String contentJson ;

    /**
     * 疾病类型：infected传染病；syndrome症候群
     */
    @ApiModelProperty(value = "疾病类型：infected传染病；syndrome症候群")
    private String diseaseType ;

    /**
     * 疾病code
     */
    @ApiModelProperty(value = "疾病code")
    private String diseaseCode ;

    /**
     * 疾病name
     */
    @ApiModelProperty(value = "疾病name")
    private String diseaseName ;

    /**
     * 新增类型： process-病例；report-报卡
     */
    @ApiModelProperty(value = "新增类型： process-病例；report-报卡")
    private String additionType ;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime ;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator ;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private String creatorId ;
    
    @ApiModelProperty(value = "症候群病例id")
    @TableField(exist = false)
    private String syndromeProcessId;
    @ApiModelProperty(value = "传染病病例id")
    @TableField(exist = false)
    private String infectProcessId;

}
