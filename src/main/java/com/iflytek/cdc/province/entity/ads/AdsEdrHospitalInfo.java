package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 出入院信息
 */
@Data
@TableName(value = "ads.ads_edr_hospital_info")
public class AdsEdrHospitalInfo {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 采集创建时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * 采集更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;

    /**
     * 事件信息ID
     */
    @TableField(value = "event_id")
    private String eventId;

    /**
     * 主索引ID
     */
    @TableField(value = "empi_id")
    private String empiId;

    /**
     * 入院日期
     */
    @TableField(value = "admission_date")
    private Date admissionDate;

    /**
     * 入院诊断代码
     */
    @TableField(value = "admission_diagnosis_code")
    private String admissionDiagnosisCode;

    /**
     * 入院诊断名称
     */
    @TableField(value = "admission_diagnosis")
    private String admissionDiagnosis;

    /**
     * 出院日期
     */
    @TableField(value = "discharge_date")
    private Date dischargeDate;

    /**
     * 出院诊断代码
     */
    @TableField(value = "discharge_diagnosis_code")
    private String dischargeDiagnosisCode;

    /**
     * 出院诊断名称
     */
    @TableField(value = "discharge_diagnosis")
    private String dischargeDiagnosis;

    /**
     * 病情转归代码
     */
    @TableField(value = "disease_progression_code")
    private String diseaseProgressionCode;

    /**
     * 病情转归名称
     */
    @TableField(value = "disease_progression")
    private String diseaseProgression;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 数据来源ID
     */
    @TableField(value = "source_id")
    private String sourceId;

    /**
     * 主索引ID
     */
    @TableField(value = "mpi_id")
    private String mpiId;

    /**
     * 创建机构
     */
    @TableField(value = "create_org")
    private String createOrg;

    /**
     * 创建机构名称
     */
    @TableField(value = "create_org_name")
    private String createOrgName;

    /**
     * 修改机构
     */
    @TableField(value = "update_org")
    private String updateOrg;

    /**
     * 修改机构名称
     */
    @TableField(value = "update_org_name")
    private String updateOrgName;
}