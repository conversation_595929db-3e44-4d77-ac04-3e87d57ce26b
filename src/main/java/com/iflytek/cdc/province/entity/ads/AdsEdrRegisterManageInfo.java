package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * edr登记管理情况信息
 */
@Data
@TableName(value = "ads.ads_edr_register_manage_info")
public class AdsEdrRegisterManageInfo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * etl首次入库时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * etl数据更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;

    /**
     * 事件信息ID
     */
    @TableField(value = "event_id")
    private String eventId;

    /**
     * 登记分类代码
     */
    @TableField(value = "registration_type_code")
    private String registrationTypeCode;

    /**
     * 登记分类名称
     */
    @TableField(value = "registration_type")
    private String registrationType;

    /**
     * 主索引ID
     */
    @TableField(value = "mpi_id")
    private String mpiId;

    /**
     * HIV检查时间
     */
    @TableField(value = "hivab_date")
    private Date hivabDate;

    /**
     * HIV检查结果代码
     */
    @TableField(value = "hivab_result_code")
    private String hivabResultCode;

    /**
     * 排除日期
     */
    @TableField(value = "outhos_date")
    private Date outhosDate;

    /**
     * 诊断分型代码
     */
    @TableField(value = "diagnostic_classification_code")
    private String diagnosticClassificationCode;

    /**
     * 诊断分型名称
     */
    @TableField(value = "diagnostic_classification")
    private String diagnosticClassification;

    /**
     * 治疗分类代码
     */
    @TableField(value = "treatment_type_code")
    private String treatmentTypeCode;

    /**
     * 治疗分类名称
     */
    @TableField(value = "treatment_type")
    private String treatmentType;

    /**
     * 治疗模式代码
     */
    @TableField(value = "treatment_mode_code")
    private String treatmentModeCode;

    /**
     * 治疗模式名称
     */
    @TableField(value = "treatment_mode")
    private String treatmentMode;

    /**
     * 等待治疗原因代码
     */
    @TableField(value = "wait_treatment_reason_code")
    private String waitTreatmentReasonCode;

    /**
     * 等待治疗原因
     */
    @TableField(value = "wait_treatment_reason")
    private String waitTreatmentReason;

    /**
     * 疗程时间
     */
    @TableField(value = "treatment_shceme_days")
    private Integer treatmentShcemeDays;

    /**
     * 项目类别代码
     */
    @TableField(value = "diagnose_typing_code")
    private String diagnoseTypingCode;

    /**
     * 项目类别名称
     */
    @TableField(value = "diagnose_typing")
    private String diagnoseTyping;

    /**
     * 既往抗结核治疗史
     */
    @TableField(value = "anti_tb_his")
    private String antiTbHis;

    /**
     * 重点人群代码
     */
    @TableField(value = "focus_group_code")
    private String focusGroupCode;

    /**
     * 重点人群名称
     */
    @TableField(value = "focus_group")
    private String focusGroup;

    /**
     * 病案类别代码
     */
    @TableField(value = "record_type_code")
    private String recordTypeCode;

    /**
     * 病案类别名称
     */
    @TableField(value = "record_type")
    private String recordType;

    /**
     * 转入日期
     */
    @TableField(value = "transin_date")
    private Date transinDate;

    /**
     * 转入单位机构代码
     */
    @TableField(value = "transin_org_code")
    private String transinOrgCode;

    /**
     * 转入单位机构名称
     */
    @TableField(value = "transin_org")
    private String transinOrg;

    /**
     * 到位日期
     */
    @TableField(value = "trace_update")
    private Date traceUpdate;

    /**
     * 转入未到位原因代码
     */
    @TableField(value = "non_transin_reason_code")
    private String nonTransinReasonCode;

    /**
     * 转入未到位原因
     */
    @TableField(value = "non_transin_reason")
    private String nonTransinReason;

    /**
     * 服药管理单位代码
     */
    @TableField(value = "dot_org_code")
    private String dotOrgCode;

    /**
     * 服药管理单位名称
     */
    @TableField(value = "dot_org")
    private String dotOrg;

    /**
     * 管理单位代码
     */
    @TableField(value = "registration_org_code")
    private String registrationOrgCode;

    /**
     * 管理单位名称
     */
    @TableField(value = "registration_org")
    private String registrationOrg;

    /**
     * 督导单位代码
     */
    @TableField(value = "supervision_org_code")
    private String supervisionOrgCode;

    /**
     * 督导单位名称
     */
    @TableField(value = "supervision_org")
    private String supervisionOrg;

    /**
     * 追踪单位
     */
    @TableField(value = "trace_org_code")
    private String traceOrgCode;

    /**
     * 追踪单位名称
     */
    @TableField(value = "trace_org")
    private String traceOrg;

    /**
     * 追踪地区
     */
    @TableField(value = "trace_zone_code")
    private String traceZoneCode;

    /**
     * 追踪情况代码
     */
    @TableField(value = "trace_result_code")
    private String traceResultCode;

    /**
     * 追踪情况
     */
    @TableField(value = "trace_result")
    private String traceResult;

    /**
     * 本次首诊日期
     */
    @TableField(value = "visit_date")
    private Date visitDate;

    /**
     * 初诊机构代码
     */
    @TableField(value = "preliminary_diagnostic_org_code")
    private String preliminaryDiagnosticOrgCode;

    /**
     * 初诊机构名称
     */
    @TableField(value = "preliminary_diagnostic_org")
    private String preliminaryDiagnosticOrg;

    /**
     * 初诊地区
     */
    @TableField(value = "preliminary_diagnostic_zone_code")
    private String preliminaryDiagnosticZoneCode;

    /**
     * 初诊地区
     */
    @TableField(value = "preliminary_diagnostic_zone")
    private String preliminaryDiagnosticZone;

    /**
     * 首诊断单位
     */
    @TableField(value = "first_diagnostic_org_code")
    private String firstDiagnosticOrgCode;

    /**
     * 首诊断机构名称
     */
    @TableField(value = "first_diagnostic_org")
    private String firstDiagnosticOrg;

    /**
     * 首诊断地区代码
     */
    @TableField(value = "first_diagnostic_zone_code")
    private String firstDiagnosticZoneCode;

    /**
     * 首诊断地区
     */
    @TableField(value = "first_diagnostic_zone")
    private String firstDiagnosticZone;

    /**
     * 首管理单位
     */
    @TableField(value = "first_manage_org_code")
    private String firstManageOrgCode;

    /**
     * 首管理机构名称
     */
    @TableField(value = "first_manage_org")
    private String firstManageOrg;

    /**
     * 首管理地区
     */
    @TableField(value = "first_manage_zone_code")
    private String firstManageZoneCode;

    /**
     * 首管理地区
     */
    @TableField(value = "first_manage_zone")
    private String firstManageZone;

    /**
     * 现诊断单位
     */
    @TableField(value = "current_diagnostic_org_code")
    private String currentDiagnosticOrgCode;

    /**
     * 现诊断机构名称
     */
    @TableField(value = "current_diagnostic_org")
    private String currentDiagnosticOrg;

    /**
     * 现诊断地区
     */
    @TableField(value = "current_diagnostic_zone_code")
    private String currentDiagnosticZoneCode;

    /**
     * 现诊断地区
     */
    @TableField(value = "current_diagnostic_zone")
    private String currentDiagnosticZone;

    /**
     * 现管理单位
     */
    @TableField(value = "current_manage_org_code")
    private String currentManageOrgCode;

    /**
     * 现管理机构名称
     */
    @TableField(value = "current_manage_org")
    private String currentManageOrg;

    /**
     * 现管理地区
     */
    @TableField(value = "current_manage_zone_code")
    private String currentManageZoneCode;

    /**
     * 现管理地区
     */
    @TableField(value = "current_manage_zone")
    private String currentManageZone;

    /**
     * 创建机构
     */
    @TableField(value = "create_org")
    private String createOrg;

    /**
     * 创建机构名称
     */
    @TableField(value = "create_org_name")
    private String createOrgName;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新机构
     */
    @TableField(value = "update_org")
    private String updateOrg;

    /**
     * 更新机构名称
     */
    @TableField(value = "update_org_name")
    private String updateOrgName;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 更新人
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 数据来源ID
     */
    @TableField(value = "source_id")
    private String sourceId;

    /**
     * 主索引ID
     */
    @TableField(value = "empi_id")
    private String empiId;
}