package com.iflytek.cdc.province.entity.ads;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 诊疗(medical service)-门急诊-病历详情;
 * <AUTHOR> dingyuan
 * @date : 2024-5-24
 */
@ApiModel(value = "诊疗(medical service)-门急诊-病历详情")
@Data
public class AdsMsOutpatMedicalInfo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 病历id
     */
    @ApiModelProperty(value = "病历id")
    private String medicalId ;

    /**
     * 就诊事件id
     */
    @ApiModelProperty(value = "就诊事件id")
    private String eventId ;

    /**
     * 病程id
     */
    @ApiModelProperty(value = "病程id")
    private String processId ;

    /**
     * 生命周期id
     */
    @ApiModelProperty(value = "生命周期id")
    private String lifeId ;

    /**
     * ETL创建时间
     */
    @ApiModelProperty(value = "ETL创建时间")
    private Date etlCreateDatetime ;

    /**
     * ETL更新时间
     */
    @ApiModelProperty(value = "ETL更新时间")
    private Date etlUpdateDatetime ;

    /**
     * 患者（不变）信息-姓名
     */
    @ApiModelProperty(value = "患者（不变）信息-姓名")
    private String patientName ;

    /**
     * 患者（不变）信息-性别代码
     */
    @ApiModelProperty(value = "患者（不变）信息-性别代码")
    private String patientSexCode ;

    /**
     * 患者（不变）信息-性别（有字典，313001）
     */
    @ApiModelProperty(value = "患者（不变）信息-性别（有字典，313001）")
    private String patientSexName ;

    /**
     * 患者（不变）信息-证件类型编码
     */
    @ApiModelProperty(value = "患者（不变）信息-证件类型编码")
    private String patientIdentityTypeCode ;

    /**
     * 患者（不变）信息-证件类型名称
     */
    @ApiModelProperty(value = "患者（不变）信息-证件类型名称")
    private String patientIdentityType ;

    /**
     * 患者（不变）信息-身份证号/身份证件号码
     */
    @ApiModelProperty(value = "患者（不变）信息-身份证号/身份证件号码")
    private String patientIdentityNo ;

    /**
     * 患者（不变）信息-出生日期
     */
    @ApiModelProperty(value = "患者（不变）信息-出生日期")
    private Date patientBirthDay ;

    /**
     * 患者（可变）信息-人员id
     */
    @ApiModelProperty(value = "患者（可变）信息-人员id")
    private String patientId ;

    /**
     * 患者（可变）信息-年龄
     */
    @ApiModelProperty(value = "患者（可变）信息-年龄")
    private Integer patientAge ;

    /**
     * 患者（可变）信息-年龄单位
     */
    @ApiModelProperty(value = "患者（可变）信息-年龄单位")
    private String patientAgeUnit ;

    /**
     * 患者（可变）信息-患者联系方式
     */
    @ApiModelProperty(value = "患者（可变）信息-患者联系方式")
    private String patientPhone ;

    /**
     * 患者（可变）信息-现住详细地址
     */
    @ApiModelProperty(value = "患者（可变）信息-现住详细地址")
    private String livingAddrDetail ;

    /**
     * 患者（可变）信息-现住址标化地址
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址标化地址")
    private String livingAddrDetailStd ;

    /**
     * 患者（可变）信息-现住地-省编码
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-省编码")
    private String livingAddrProvinceCode ;

    /**
     * 患者（可变）信息-现住地-省
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-省")
    private String livingAddrProvince ;

    /**
     * 患者（可变）信息-现住地-市编码
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-市编码")
    private String livingAddrCityCode ;

    /**
     * 患者（可变）信息-现住地-市
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-市")
    private String livingAddrCity ;

    /**
     * 患者（可变）信息-现住地-行政区编码
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-行政区编码")
    private String livingAddrDistrictCode ;

    /**
     * 患者（可变）信息-现住地-行政区
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-行政区")
    private String livingAddrDistrict ;

    /**
     * 患者（可变）信息-现住址-功能区编码
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-功能区编码")
    private String livingAddrFuncDistrictCode ;

    /**
     * 患者（可变）信息-现住址-功能区
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-功能区")
    private String livingAddrFuncDistrict ;

    /**
     * 患者（可变）信息-现住址-街道编码
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-街道编码")
    private String livingAddrStreetCode ;

    /**
     * 患者（可变）信息-现住址-街道
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-街道")
    private String livingAddrStreet ;

    /**
     * 患者（可变）信息-现住址-街道中心经度
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-街道中心经度")
    private Double livingAddrStreetLongitude ;

    /**
     * 患者（可变）信息-现住址-街道中心纬度
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-街道中心纬度")
    private Double livingAddrStreetLatitude ;

    /**
     * 患者（可变）信息-现住址-经度
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-经度")
    private Double livingAddrLongitude ;

    /**
     * 患者（可变）信息-现住址-经度
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-经度")
    private Double livingAddrLatitude ;

    /**
     * 患者（可变）信息-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）
     */
    @ApiModelProperty(value = "患者（可变）信息-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）")
    private String personType ;

    /**
     * 患者（可变）信息-职业
     */
    @ApiModelProperty(value = "患者（可变）信息-职业")
    private String job ;

    /**
     * 患者（可变）信息-工作单位/学校名称
     */
    @ApiModelProperty(value = "患者（可变）信息-工作单位/学校名称")
    private String company ;

    /**
     * 患者（可变）信息-单位/学校标化地址
     */
    @ApiModelProperty(value = "患者（可变）信息-单位/学校标化地址")
    private String companyAddress ;

    /**
     * 患者（可变）信息-单位-省编码
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-省编码")
    private String companyProvinceCode ;

    /**
     * 患者（可变）信息-单位-省
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-省")
    private String companyProvince ;

    /**
     * 患者（可变）信息-单位-市编码
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-市编码")
    private String companyCityCode ;

    /**
     * 患者（可变）信息-单位-市
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-市")
    private String companyCity ;

    /**
     * 患者（可变）信息-单位-行政区编码
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-行政区编码")
    private String companyDistrictCode ;

    /**
     * 患者（可变）信息-单位-行政区
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-行政区")
    private String companyDistrict ;

    /**
     * 患者（可变）信息-单位-功能区编码
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-功能区编码")
    private String companyFuncDistrictCode ;

    /**
     * 患者（可变）信息-单位-功能区
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-功能区")
    private String companyFuncDistrict ;

    /**
     * 患者（可变）信息-单位-街道编码
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-街道编码")
    private String companyStreetCode ;

    /**
     * 患者（可变）信息-单位-街道
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-街道")
    private String companyStreet ;

    /**
     * 患者（可变）信息-单位-街道中心经度
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-街道中心经度")
    private Double companyStreetLongitude ;

    /**
     * 患者（可变）信息-单位-街道中心维度
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-街道中心维度")
    private Double companyStreetLatitude ;

    /**
     * 患者（可变）信息-单位-经度
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-经度")
    private Double companyLongitude ;

    /**
     * 患者（可变）信息-单位-纬度
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-纬度")
    private Double companyLatitude ;

    /**
     * 机构id（主索引）
     */
    @ApiModelProperty(value = "机构id（主索引）")
    private String orgId ;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String orgName ;

    /**
     * 机构类别（等级医院、基层医疗）
     */
    @ApiModelProperty(value = "机构类别（等级医院、基层医疗）")
    private String orgClass ;

    /**
     * 机构类型名称(204003)
     */
    @ApiModelProperty(value = "机构类型名称(204003)")
    private String orgTypeName ;

    /**
     * 机构详细地址
     */
    @ApiModelProperty(value = "机构详细地址")
    private String orgAddrDetail ;

    /**
     * 机构标化地址
     */
    @ApiModelProperty(value = "机构标化地址")
    private String orgAddrDetailStd ;

    /**
     * 机构-省编码
     */
    @ApiModelProperty(value = "机构-省编码")
    private String orgAddrProvinceCode ;

    /**
     * 机构-省
     */
    @ApiModelProperty(value = "机构-省")
    private String orgAddrProvince ;

    /**
     * 机构-市编码
     */
    @ApiModelProperty(value = "机构-市编码")
    private String orgAddrCityCode ;

    /**
     * 机构-市
     */
    @ApiModelProperty(value = "机构-市")
    private String orgAddrCity ;

    /**
     * 机构-行政区编码
     */
    @ApiModelProperty(value = "机构-行政区编码")
    private String orgAddrDistrictCode ;

    /**
     * 机构-行政区
     */
    @ApiModelProperty(value = "机构-行政区")
    private String orgAddrDistrict ;

    /**
     * 机构-功能区编码
     */
    @ApiModelProperty(value = "机构-功能区编码")
    private String orgAddrFuncDistrictCode ;

    /**
     * 机构-功能区
     */
    @ApiModelProperty(value = "机构-功能区")
    private String orgAddrFuncDistrict ;

    /**
     * 机构-街道编码
     */
    @ApiModelProperty(value = "机构-街道编码")
    private String orgAddrStreetCode ;

    /**
     * 机构-街道
     */
    @ApiModelProperty(value = "机构-街道")
    private String orgAddrStreet ;

    /**
     * 机构经度
     */
    @ApiModelProperty(value = "机构经度")
    private Double orgLongitude ;

    /**
     * 机构纬度
     */
    @ApiModelProperty(value = "机构纬度")
    private Double orgLatitude ;

    /**
     * 发病时间
     */
    @ApiModelProperty(value = "发病时间")
    private Date onsetTime ;

    /**
     * 就诊时间
     */
    @ApiModelProperty(value = "就诊时间")
    private Date visitTime ;

    /**
     * 就诊科室编码
     */
    @ApiModelProperty(value = "就诊科室编码")
    private String deptCode ;

    /**
     * 就诊科室
     */
    @ApiModelProperty(value = "就诊科室")
    private String deptName ;

    /**
     * 就诊类型编码
     */
    @ApiModelProperty(value = "就诊类型编码")
    private String visitTypeCode ;

    /**
     * 就诊类型
     */
    @ApiModelProperty(value = "就诊类型")
    private String visitType ;

    /**
     * 接诊医生编码
     */
    @ApiModelProperty(value = "接诊医生编码")
    private String docId ;

    /**
     * 接诊医生
     */
    @ApiModelProperty(value = "接诊医生")
    private String docName ;

    /**
     * 就诊流水号
     */
    @ApiModelProperty(value = "就诊流水号")
    private String serialNo ;

    /**
     * 初诊标识（1是0否）
     */
    @ApiModelProperty(value = "初诊标识（1是0否）")
    private String firstDiagFlag ;

    /**
     * 急诊标识（1是0否）
     */
    @ApiModelProperty(value = "急诊标识（1是0否）")
    private String emergencyFlag ;

    /**
     * 症状信息（json，症状编码symptom_code，症状symptom）
     */
    @ApiModelProperty(value = "症状信息（json，症状编码symptom_code，症状symptom）")
    private String symptomJson ;

    /**
     * 主诉
     */
    @ApiModelProperty(value = "主诉")
    private String suit ;

    /**
     * 现病史
     */
    @ApiModelProperty(value = "现病史")
    private String diseaseHistoryNow ;

    /**
     * 既往史
     */
    @ApiModelProperty(value = "既往史")
    private String diseaseHistoryBefore ;

    /**
     * 体格检查
     */
    @ApiModelProperty(value = "体格检查")
    private String checkup ;

    /**
     * 传染病史
     */
    @ApiModelProperty(value = "传染病史")
    private String infectHistory ;

    /**
     * 过敏史
     */
    @ApiModelProperty(value = "过敏史")
    private String allergyHistory ;

    /**
     * 遗传史
     */
    @ApiModelProperty(value = "遗传史")
    private String geneticHistory ;

    /**
     * 体征信息（json，舒张压diastolic_pressure，平均压mean_pressure，收缩压systolic_pressure，空腹血糖（fasting blood glucose）fbg，餐后血糖（postprandial blood glucose）pbg，体温temperature，呼吸频率breath_freq，血氧blood_oxygen，心率heart_rate，体重（单位kg）weight，身高（单位cm）height，脉搏pulse）
     */
    @ApiModelProperty(value = "体征信息（json，舒张压diastolic_pressure，平均压mean_pressure，收缩压systolic_pressure，空腹血糖（fasting blood glucose）fbg，餐后血糖（postprandial blood glucose）pbg，体温temperature，呼吸频率breath_freq，血氧blood_oxygen，心率heart_rate，体重（单位kg）weight，身高（单位cm）height，脉搏pulse）")
    private String physicalSignJson ;

    /**
     * 诊断信息（json，诊断编码diag_code，诊断名称diag_name，主诊断标识diag_main_flag，诊断类型（初步诊断、疑似诊断、确诊诊断？）diag_type，诊断疾病类型（急性、慢性）diag_disease_type，标准诊断名称diag_code_std，标准诊断编码diag_name_std，四诊结果four_diag，舌象tongue_manifestation，脉象pulse_condition，症候代码symptom_code，症候symptom，病名代码disease_code，病名disease，辩证依据treat_support，治则治法treat）
     */
    @ApiModelProperty(value = "诊断信息（json，诊断编码diag_code，诊断名称diag_name，主诊断标识diag_main_flag，诊断类型（初步诊断、疑似诊断、确诊诊断？）diag_type，诊断疾病类型（急性、慢性）diag_disease_type，标准诊断名称diag_code_std，标准诊断编码diag_name_std，四诊结果four_diag，舌象tongue_manifestation，脉象pulse_condition，症候代码symptom_code，症候symptom，病名代码disease_code，病名disease，辩证依据treat_support，治则治法treat）")
    private String diagJson ;

    /**
     * 治疗意见/处置意见
     */
    @ApiModelProperty(value = "治疗意见/处置意见")
    private String dealNote ;

    /**
     * 病原检测信息（json，检测时间pathogen_report_time，检测项目pathogen_item_name，检测定性结果pathogen_result_qualit，检测定量结果pathogen_result_quantit，检测定量单位pathogen_result_quantit_unit）
     */
    @ApiModelProperty(value = "病原检测信息（json，检测时间pathogen_report_time，检测项目pathogen_item_name，检测定性结果pathogen_result_qualit，检测定量结果pathogen_result_quantit，检测定量单位pathogen_result_quantit_unit）")
    private String pathogenJson ;

    /**
     * 转归时间
     */
    @ApiModelProperty(value = "转归时间")
    private Date outcomeTime ;

    /**
     * 转归状态（治愈、治疗成功、死亡、带病）
     */
    @ApiModelProperty(value = "转归状态（治愈、治疗成功、死亡、带病）")
    private String outcomeStatus ;

    /**
     * 死亡原因（详细的，因该病死亡，其他）
     */
    @ApiModelProperty(value = "死亡原因（详细的，因该病死亡，其他）")
    private String deadReason ;

    /**
     * 传染病报卡标识（1是0否）
     */
    @ApiModelProperty(value = "传染病报卡标识（1是0否）")
    private String infectReportFlag ;

    /**
     * 病历类型编码
     */
    @ApiModelProperty(value = "病历类型编码")
    private String mrTypeCode ;

    /**
     * 病历类型（中医、西医等）
     */
    @ApiModelProperty(value = "病历类型（中医、西医等）")
    private String mrType ;

    /**
     * 病历完成时间
     */
    @ApiModelProperty(value = "病历完成时间")
    private Date mrCompleteTime ;

    /**
     * 签名医生编码
     */
    @ApiModelProperty(value = "签名医生编码")
    private String signDocId ;

    /**
     * 签名医生
     */
    @ApiModelProperty(value = "签名医生")
    private String signDoc ;

    /**
     * 签名时间
     */
    @ApiModelProperty(value = "签名时间")
    private Date signTime ;

    /**
     * 个人史
     */
    @ApiModelProperty(value = "个人史")
    private String personalHistory ;

    /**
     * 家族史
     */
    @ApiModelProperty(value = "家族史")
    private String familyHistory ;

    /**
     * 辅助检查
     */
    @ApiModelProperty(value = "辅助检查")
    private String assistExam ;

    /**
     * 主诊断编码
     */
    @ApiModelProperty(value = "主诊断编码")
    private String mainDiagCode ;

    /**
     * 主诊断名称
     */
    @ApiModelProperty(value = "主诊断名称")
    private String mainDiag ;

}
