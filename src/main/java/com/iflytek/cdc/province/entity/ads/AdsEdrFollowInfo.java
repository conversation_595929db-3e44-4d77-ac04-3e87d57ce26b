package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 随访信息
 */
@Data
@TableName(value = "ads.ads_edr_follow_info")
public class AdsEdrFollowInfo {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * etl首次入库时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * etl数据更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;

    /**
     * 事件信息ID
     */
    @TableField(value = "event_id")
    private String eventId;

    /**
     * 本次随访日期
     */
    @TableField(value = "followup_date")
    private Date followupDate;

    /**
     * 随访方式代码
     */
    @TableField(value = "followup_mode_code")
    private String followupModeCode;

    /**
     * 随访方式名称
     */
    @TableField(value = "followup_mode")
    private String followupMode;

    /**
     * 随访状态代码
     */
    @TableField(value = "followup_status_code")
    private String followupStatusCode;

    /**
     * 随访状态
     */
    @TableField(value = "followup_status")
    private String followupStatus;

    /**
     * 失访原因代码
     */
    @TableField(value = "lost_reason_code")
    private String lostReasonCode;

    /**
     * 失访原因
     */
    @TableField(value = "lost_reason")
    private String lostReason;

    /**
     * 母婴传播病例，其阳性母亲有效证件号
     */
    @TableField(value = "mother_idcard")
    private String motherIdcard;

    /**
     * 主索引ID
     */
    @TableField(value = "empi_id")
    private String empiId;

    /**
     * 孕周（d）
     */
    @TableField(value = "gestational_age")
    private Integer gestationalAge;

    /**
     * 身高
     */
    @TableField(value = "height")
    private String height;

    /**
     * 体重
     */
    @TableField(value = "weight")
    private String weight;

    /**
     * 最近一次性行为安全套使用情况代码
     */
    @TableField(value = "last_condom_use_code")
    private String lastCondomUseCode;

    /**
     * 最近一次性行为安全套使用情况
     */
    @TableField(value = "last_condom_use")
    private String lastCondomUse;

    /**
     * 当前配偶HIV感染状况代码
     */
    @TableField(value = "spouse_hiv_code")
    private String spouseHivCode;

    /**
     * 当前配偶HIV感染状况
     */
    @TableField(value = "spouse_hiv")
    private String spouseHiv;

    /**
     * 当前配偶HIV感染状况检测日期
     */
    @TableField(value = "spouse_hiv_date")
    private Date spouseHivDate;

    /**
     * 若当前配偶感染状况为阳性其有效证件号为
     */
    @TableField(value = "spouse_idcard")
    private String spouseIdcard;

    /**
     * 配偶变更及配偶间性行为情况代码
     */
    @TableField(value = "spouse_status_code")
    private String spouseStatusCode;

    /**
     * 配偶变更及配偶间性行为情况
     */
    @TableField(value = "spouse_status")
    private String spouseStatus;

    /**
     * 服药管理方式代码
     */
    @TableField(value = "dot_mode_code")
    private String dotModeCode;

    /**
     * 服药管理方式名称
     */
    @TableField(value = "dot_mode")
    private String dotMode;

    /**
     * 未落实服药管理原因代码
     */
    @TableField(value = "non_dot_reason_code")
    private String nonDotReasonCode;

    /**
     * 未落实服药管理原因
     */
    @TableField(value = "non_dot_reason")
    private String nonDotReason;

    /**
     * 服药管理落实日期
     */
    @TableField(value = "dot_date")
    private Date dotDate;

    /**
     * 应服药天数
     */
    @TableField(value = "planned_med_days")
    private Integer plannedMedDays;

    /**
     * 实际服药天数
     */
    @TableField(value = "actualmed_days")
    private Long actualmedDays;

    /**
     * 最近7天抗病毒治疗药物漏服次数
     */
    @TableField(value = "drug_leakages")
    private Integer drugLeakages;

    /**
     * 目前是否服用复方新诺明预防机会性感染
     */
    @TableField(value = "smz_code")
    private String smzCode;

    /**
     * 下次随访领药日期
     */
    @TableField(value = "next_followup_date")
    private Date nextFollowupDate;

    /**
     * 换药原因代码
     */
    @TableField(value = "drug_change_reason_code")
    private String drugChangeReasonCode;

    /**
     * 换药原因
     */
    @TableField(value = "drug_change_reason")
    private String drugChangeReason;

    /**
     * 换药副反应代码
     */
    @TableField(value = "drug_change_sideeffect_code")
    private String drugChangeSideeffectCode;

    /**
     * 换药副反应
     */
    @TableField(value = "drug_change_sideeffect")
    private String drugChangeSideeffect;

    /**
     * 停药原因代码
     */
    @TableField(value = "drug_stop_reason_code")
    private String drugStopReasonCode;

    /**
     * 停药原因
     */
    @TableField(value = "drug_stop_reason")
    private String drugStopReason;

    /**
     * 停药原因副反应代码
     */
    @TableField(value = "drug_stop_sideeffect_code")
    private String drugStopSideeffectCode;

    /**
     * 停药原因副反应
     */
    @TableField(value = "drug_stop_sideeffect")
    private String drugStopSideeffect;

    /**
     * 服药随访结果代码
     */
    @TableField(value = "drug_followup_result_code")
    private String drugFollowupResultCode;

    /**
     * 服药随访结果
     */
    @TableField(value = "drug_followup_result")
    private String drugFollowupResult;

    /**
     * 麻痹部位代码
     */
    @TableField(value = "paralysis_position_code")
    private String paralysisPositionCode;

    /**
     * 麻痹部位名称
     */
    @TableField(value = "paralysis_position")
    private String paralysisPosition;

    /**
     * 麻痹程度代码
     */
    @TableField(value = "paralysis_degree_code")
    private String paralysisDegreeCode;

    /**
     * 麻痹程度名称
     */
    @TableField(value = "paralysis_degree")
    private String paralysisDegree;

    /**
     * 麻痹部位行走能力代码
     */
    @TableField(value = "walking_ability_code")
    private String walkingAbilityCode;

    /**
     * 麻痹部位行走能力
     */
    @TableField(value = "walking_ability")
    private String walkingAbility;

    /**
     * 未接受治疗原因代码
     */
    @TableField(value = "non_treatment_reason_code")
    private String nonTreatmentReasonCode;

    /**
     * 未接受治疗原因
     */
    @TableField(value = "non_treatment_reason")
    private String nonTreatmentReason;

    /**
     * WHO临床分期代码
     */
    @TableField(value = "who_ctnm_code")
    private String whoCtnmCode;

    /**
     * WHO临床分期名称
     */
    @TableField(value = "who_ctnm")
    private String whoCtnm;

    /**
     * 临床处置代码
     */
    @TableField(value = "clinical_measures_code")
    private String clinicalMeasuresCode;

    /**
     * 临床处置
     */
    @TableField(value = "clinical_measures")
    private String clinicalMeasures;

    /**
     * 转出日期
     */
    @TableField(value = "transout_date")
    private Date transoutDate;

    /**
     * 转出单位机构代码
     */
    @TableField(value = "transout_org_code")
    private String transoutOrgCode;

    /**
     * 转出单位机构名称
     */
    @TableField(value = "transout_org")
    private String transoutOrg;

    /**
     * 转入日期
     */
    @TableField(value = "transin_date")
    private Date transinDate;

    /**
     * 转入单位机构代码
     */
    @TableField(value = "transin_org_code")
    private String transinOrgCode;

    /**
     * 转入单位机构名称
     */
    @TableField(value = "transin_org")
    private String transinOrg;

    /**
     * 病情转归代码
     */
    @TableField(value = "disease_progression_code")
    private String diseaseProgressionCode;

    /**
     * 病情转归名称
     */
    @TableField(value = "disease_progression")
    private String diseaseProgression;

    /**
     * 后遗症代码
     */
    @TableField(value = "disease_sequelae_code")
    private String diseaseSequelaeCode;

    /**
     * 后遗症
     */
    @TableField(value = "disease_sequelae")
    private String diseaseSequelae;

    /**
     * 医疗付费方式代码
     */
    @TableField(value = "payment_type_code")
    private String paymentTypeCode;

    /**
     * 医疗付费方式名称
     */
    @TableField(value = "payment_type")
    private String paymentType;

    /**
     * 创建机构
     */
    @TableField(value = "create_org")
    private String createOrg;

    /**
     * 创建机构名称
     */
    @TableField(value = "create_org_name")
    private String createOrgName;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改机构
     */
    @TableField(value = "update_org")
    private String updateOrg;

    /**
     * 更新机构名称
     */
    @TableField(value = "update_org_name")
    private String updateOrgName;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 数据来源ID
     */
    @TableField(value = "source_id")
    private String sourceId;

    /**
     * 主索引ID
     */
    @TableField(value = "mpi_id")
    private String mpiId;
}