package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 预防接种史信息
 */
@Data
@TableName(value = "ads.ads_edr_vaccinate_history")
public class AdsEdrVaccinateHistory {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * etl首次入库时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * etl数据更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;

    /**
     * 事件信息ID
     */
    @TableField(value = "event_id")
    private String eventId;

    /**
     * 主索引ID
     */
    @TableField(value = "empi_id")
    private String empiId;

    /**
     * 疫苗种类代码
     */
    @TableField(value = "vaccine_type_code")
    private String vaccineTypeCode;

    /**
     * 疫苗种类名称
     */
    @TableField(value = "vaccine_type")
    private String vaccineType;

    /**
     * 接种地点代码
     */
    @TableField(value = "vaccination_location_code")
    private String vaccinationLocationCode;

    /**
     * 接种地点
     */
    @TableField(value = "vaccination_location")
    private String vaccinationLocation;

    /**
     * 接种时间
     */
    @TableField(value = "vaccination_time")
    private Date vaccinationTime;

    /**
     * 接种剂次
     */
    @TableField(value = "vaccination_dose")
    private Short vaccinationDose;

    /**
     * 疫苗批号
     */
    @TableField(value = "vaccine_number")
    private String vaccineNumber;

    /**
     * AEFI诊断代码
     */
    @TableField(value = "aefi_diagnosis_code")
    private String aefiDiagnosisCode;

    /**
     * AEFI诊断名称
     */
    @TableField(value = "aefi_diagnosis")
    private String aefiDiagnosis;

    /**
     * 创建机构
     */
    @TableField(value = "create_org")
    private String createOrg;

    /**
     * 创建机构名称
     */
    @TableField(value = "create_org_name")
    private String createOrgName;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改机构
     */
    @TableField(value = "update_org")
    private String updateOrg;

    /**
     * 更新机构名称
     */
    @TableField(value = "update_org_name")
    private String updateOrgName;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 数据来源ID
     */
    @TableField(value = "source_id")
    private String sourceId;

    /**
     * 主索引ID
     */
    @TableField(value = "mpi_id")
    private String mpiId;
}