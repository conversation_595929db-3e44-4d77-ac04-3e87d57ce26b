package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 暴露史信息
 */
@Data
@TableName(value = "ads.ads_edr_exposure_history_info")
public class AdsEdrExposureHistoryInfo {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 采集创建时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * 采集更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;

    /**
     * 事件信息ID
     */
    @TableField(value = "event_id")
    private String eventId;

    /**
     * 主索引ID
     */
    @TableField(value = "empi_id")
    private String empiId;

    /**
     * 创建机构
     */
    @TableField(value = "create_org")
    private String createOrg;

    /**
     * 修改机构
     */
    @TableField(value = "update_org")
    private String updateOrg;

    /**
     * 记录医疗机构名称
     */
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 亲密接触者有无同症状代码
     */
    @TableField(value = "contactflag_code")
    private String contactflagCode;

    /**
     * 性病史代码
     */
    @TableField(value = "venereal_dis_code")
    private String venerealDisCode;

    /**
     * 性病史名称
     */
    @TableField(value = "venereal_dis")
    private String venerealDis;

    /**
     * 接触方式代码
     */
    @TableField(value = "contact_type_code")
    private String contactTypeCode;

    /**
     * 接触方式名称
     */
    @TableField(value = "contact_type")
    private String contactType;

    /**
     * 与病人共用过注射器的人数
     */
    @TableField(value = "inject_count")
    private Integer injectCount;

    /**
     * 与病人有非婚性行为的人数
     */
    @TableField(value = "nonweb_count")
    private Integer nonwebCount;

    /**
     * 发生同性性行为的人数
     */
    @TableField(value = "sm_count")
    private Integer smCount;

    /**
     * 接触对象代码
     */
    @TableField(value = "contact_object_code")
    private String contactObjectCode;

    /**
     * 接触对象名称
     */
    @TableField(value = "contact_object")
    private String contactObject;

    /**
     * 接触时间
     */
    @TableField(value = "contact_time")
    private Date contactTime;

    /**
     * 发现方式代码
     */
    @TableField(value = "discovery_mode_code")
    private String discoveryModeCode;

    /**
     * 发现方式名称
     */
    @TableField(value = "discovery_mode")
    private String discoveryMode;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 数据来源ID
     */
    @TableField(value = "source_id")
    private String sourceId;

    /**
     * 主索引ID
     */
    @TableField(value = "mpi_id")
    private String mpiId;

    /**
     * 创建机构名称
     */
    @TableField(value = "create_org_name")
    private String createOrgName;

    /**
     * 修改机构名称
     */
    @TableField(value = "update_org_name")
    private String updateOrgName;
}