package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 个人状态信息
 */
@Data
@TableName(value = "ads.ads_edr_person_status_info")
public class AdsEdrPersonStatusInfo {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 采集创建时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * 采集更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;

    /**
     * 创建机构
     */
    @TableField(value = "create_org")
    private String createOrg;

    /**
     * 医疗机构(机构主索引)-机构编码
     */
    @TableField(value = "create_org_id")
    private String createOrgId;

    /**
     * 所属机构名称
     */
    @TableField(value = "create_org_name")
    private String createOrgName;

    /**
     * 机构详细地址
     */
    @TableField(value = "create_org_detail")
    private String createOrgDetail;

    /**
     * 机构标化地址
     */
    @TableField(value = "create_org_detail_std")
    private String createOrgDetailStd;

    /**
     * 机构标化地址-省编码
     */
    @TableField(value = "create_org_province_code")
    private String createOrgProvinceCode;

    /**
     * 机构标化地址-省
     */
    @TableField(value = "create_org_province")
    private String createOrgProvince;

    /**
     * 机构标化地址-市编码
     */
    @TableField(value = "create_org_city_code")
    private String createOrgCityCode;

    /**
     * 机构标化地址-市
     */
    @TableField(value = "create_org_city")
    private String createOrgCity;

    /**
     * 机构标化地址-行政区编码
     */
    @TableField(value = "create_org_district_code")
    private String createOrgDistrictCode;

    /**
     * 机构标化地址-行政区
     */
    @TableField(value = "create_org_district")
    private String createOrgDistrict;

    /**
     * 机构标化地址-功能区编码
     */
    @TableField(value = "create_org_func_district_code")
    private String createOrgFuncDistrictCode;

    /**
     * 机构标化地址-功能区
     */
    @TableField(value = "create_org_func_district")
    private String createOrgFuncDistrict;

    /**
     * 机构标化地址-街道编码
     */
    @TableField(value = "create_org_street_code")
    private String createOrgStreetCode;

    /**
     * 机构标化地址-街道
     */
    @TableField(value = "create_org_street")
    private String createOrgStreet;

    /**
     * 机构标化地址-街道中心经度
     */
    @TableField(value = "create_org_street_longitude")
    private BigDecimal createOrgStreetLongitude;

    /**
     * 机构标化地址-街道中心纬度
     */
    @TableField(value = "create_org_street_latitude")
    private BigDecimal createOrgStreetLatitude;

    /**
     * 机构标化地址-经度
     */
    @TableField(value = "create_org_longitude")
    private BigDecimal createOrgLongitude;

    /**
     * 机构标化地址-纬度
     */
    @TableField(value = "create_org_latitude")
    private BigDecimal createOrgLatitude;

    /**
     * 事件信息ID
     */
    @TableField(value = "event_id")
    private String eventId;

    /**
     * 主索引ID
     */
    @TableField(value = "empi_id")
    private String empiId;

    /**
     * 学历代码
     */
    @TableField(value = "education_code")
    private String educationCode;

    /**
     * 学历名称
     */
    @TableField(value = "education")
    private String education;

    /**
     * 工作单位
     */
    @TableField(value = "workunit")
    private String workunit;

    /**
     * 工作单位标化地址/学校标化地址
     */
    @TableField(value = "workunit_addr_std")
    private String workunitAddrStd;

    /**
     * 工作单位/学校标化地址-省编码
     */
    @TableField(value = "workunit_province_code")
    private String workunitProvinceCode;

    /**
     * 工作单位/学校标化地址-省
     */
    @TableField(value = "workunit_province")
    private String workunitProvince;

    /**
     * 工作单位/学校标化地址-市编码
     */
    @TableField(value = "workunit_city_code")
    private String workunitCityCode;

    /**
     * 工作单位/学校标化地址-市
     */
    @TableField(value = "workunit_city")
    private String workunitCity;

    /**
     * 工作单位/学校标化地址-行政区编码
     */
    @TableField(value = "workunit_district_code")
    private String workunitDistrictCode;

    /**
     * 工作单位/学校标化地址-行政区
     */
    @TableField(value = "workunit_district")
    private String workunitDistrict;

    /**
     * 工作单位/学校标化地址-功能区编码
     */
    @TableField(value = "workunit_func_district_code")
    private String workunitFuncDistrictCode;

    /**
     * 工作单位/学校标化地址-功能区
     */
    @TableField(value = "workunit_func_district")
    private String workunitFuncDistrict;

    /**
     * 工作单位/学校标化地址-街道编码
     */
    @TableField(value = "workunit_street_code")
    private String workunitStreetCode;

    /**
     * 工作单位/学校标化地址-街道
     */
    @TableField(value = "workunit_street")
    private String workunitStreet;

    /**
     * 工作单位/学校标化地址-街道中心经度
     */
    @TableField(value = "workunit_street_longitude")
    private BigDecimal workunitStreetLongitude;

    /**
     * 工作单位/学校标化地址-街道中心纬度
     */
    @TableField(value = "workunit_street_latitude")
    private BigDecimal workunitStreetLatitude;

    /**
     * 工作单位/学校标化地址-经度
     */
    @TableField(value = "workunit_longitude")
    private BigDecimal workunitLongitude;

    /**
     * 工作单位/学校标化地址-纬度
     */
    @TableField(value = "workunit_latitude")
    private BigDecimal workunitLatitude;

    /**
     * 病例分类
     */
    @TableField(value = "diagnose_state_code")
    private String diagnoseStateCode;

    /**
     * 病例分类名称（确诊病例、疑似病例等）
     */
    @TableField(value = "diagnose_state")
    private String diagnoseState;

    /**
     * 风险分级代码
     */
    @TableField(value = "risk_rating_code")
    private String riskRatingCode;

    /**
     * 风险分级名称
     */
    @TableField(value = "risk_rating")
    private String riskRating;

    /**
     * 关注病种名称
     */
    @TableField(value = "disease")
    private String disease;

    /**
     * 排查状态
     */
    @TableField(value = "screening_status_code")
    private String screeningStatusCode;

    /**
     * 排查状态名称
     */
    @TableField(value = "screening_status")
    private String screeningStatus;

    /**
     * 流动状态
     */
    @TableField(value = "flow_status_code")
    private String flowStatusCode;

    /**
     * 流动状态名称
     */
    @TableField(value = "flow_status")
    private String flowStatus;

    /**
     * 人群分类代码
     */
    @TableField(value = "nultitude_type_code")
    private String nultitudeTypeCode;

    /**
     * 人群分类名称
     */
    @TableField(value = "nultitude_type")
    private String nultitudeType;

    /**
     * 药物过敏史
     */
    @TableField(value = "allergy_drug")
    private String allergyDrug;

    /**
     * 关注病种
     */
    @TableField(value = "disease_code")
    private String diseaseCode;

    /**
     * 诊断标化(ICD10)-西医疾病诊断编码
     */
    @TableField(value = "diag_std_code")
    private String diagStdCode;

    /**
     * 诊断标化(ICD10)-西医疾病诊断名称
     */
    @TableField(value = "diag_std_name")
    private String diagStdName;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 主索引ID
     */
    @TableField(value = "mpi_id")
    private String mpiId;

    /**
     * 修改机构
     */
    @TableField(value = "update_org")
    private String updateOrg;

    /**
     * 修改机构
     */
    @TableField(value = "update_org_name")
    private String updateOrgName;
}