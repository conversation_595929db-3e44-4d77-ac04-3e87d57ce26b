package com.iflytek.cdc.province.entity.bu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 数据模型版本表
 */
@ApiModel(description="数据模型版本表")
@Data
public class TbCdcdmDataModelVersion implements Serializable {
    /**
    * 主键ID
    */
    @ApiModelProperty(value="主键ID")
    private String modelVersionId;

    /**
    * 数据模型ID
    */
    @ApiModelProperty(value="数据模型ID")
    private String modelId;

    /**
    * 数据模型标签
    */
    @ApiModelProperty(value="数据模型标签")
    private String modelLabel;

    /**
    * 版本
    */
    @ApiModelProperty(value="版本")
    private String version;

    /**
    * 状态
    */
    @ApiModelProperty(value="状态")
    private String status;

    /**
    * 备注
    */
    @ApiModelProperty(value="备注")
    private String note;

    /**
    * 是否启用 1-是，0-否
    */
    @ApiModelProperty(value="是否启用 1-是，0-否")
    private Integer isEnable;

    /**
    * 是否删除 1-已删除、0-未删除
    */
    @ApiModelProperty(value="是否删除 1-已删除、0-未删除")
    private Integer isDeleted;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
    * 创建人
    */
    @ApiModelProperty(value="创建人")
    private String creator;

    /**
    * 修改时间
    */
    @ApiModelProperty(value="修改时间")
    private Date updateTime;

    /**
    * 修改人
    */
    @ApiModelProperty(value="修改人")
    private String updater;

    private static final long serialVersionUID = 1L;
}