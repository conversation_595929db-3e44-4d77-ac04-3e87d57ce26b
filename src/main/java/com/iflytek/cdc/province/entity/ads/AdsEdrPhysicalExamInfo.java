package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * edr体格检查信息
 */
@Data
@TableName(value = "ads.ads_edr_physical_exam_info")
public class AdsEdrPhysicalExamInfo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 采集创建时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * 采集更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;

    /**
     * 事件信息ID
     */
    @TableField(value = "event_id")
    private String eventId;

    /**
     * 主索引ID
     */
    @TableField(value = "empi_id")
    private String empiId;

    /**
     * 就诊日期时间
     */
    @TableField(value = "serial_number")
    private Date serialNumber;

    /**
     * 报告日期
     */
    @TableField(value = "\"timestamp\"")
    private Date timestamp;

    /**
     * 检查项目代码
     */
    @TableField(value = "examination_item_code")
    private String examinationItemCode;

    /**
     * 检查项目名称
     */
    @TableField(value = "examination_item")
    private String examinationItem;

    /**
     * 检查结果
     */
    @TableField(value = "examination_result_code")
    private String examinationResultCode;

    /**
     * 检查结果名称
     */
    @TableField(value = "examination_result")
    private String examinationResult;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 数据来源ID
     */
    @TableField(value = "source_id")
    private String sourceId;

    /**
     * 主索引ID
     */
    @TableField(value = "mpi_id")
    private String mpiId;

    /**
     * 创建机构
     */
    @TableField(value = "create_org")
    private String createOrg;

    /**
     * 创建机构名称
     */
    @TableField(value = "create_org_name")
    private String createOrgName;

    /**
     * 修改机构
     */
    @TableField(value = "update_org")
    private String updateOrg;

    /**
     * 修改机构名称
     */
    @TableField(value = "update_org_name")
    private String updateOrgName;
}