package com.iflytek.cdc.province.entity.bu;

import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.cdc.province.enums.BriefStatisticsSourceEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * （手动）生成简报记录-实体类
 */
@ApiModel(value = "生成简报记录实体对象")
@Data
@TableName("app.tb_cdcbr_manual_brief_record")
public class TbCdcbrManualBriefRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * UUID，主键，用于唯一标识每一条记录
     */
    @ApiModelProperty(value = "UUID，主键，用于唯一标识每一条记录")
    private String id;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者ID")
    private String creatorId;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String creator;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者ID")
    private String updaterId;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者")
    private String updater;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 删除标志: '1'-已删除 '0'-未删除
     */
    @ApiModelProperty(value = "删除标志: '1'-已删除 '0'-未删除", allowableValues = "'1','2'")
    private String deleteFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String memo;

    /**
     * 简报标题
     */
    @ApiModelProperty(value = "简报标题")
    private String title;

    /**
     * 数据来源：简报数据导入、疫情概况统计
     */
    @ApiModelProperty(value = "数据来源：简报数据导入、疫情概况统计", allowableValues = BriefStatisticsSourceEnum.ALLOWABLE_VALUES)
    private String statisticsSource;

    /**
     * 统计时间-开始
     */
    @ApiModelProperty(value = "统计时间-开始")
    private Date statisticsStartTime;

    /**
     * 统计时间-结束
     */
    @ApiModelProperty(value = "统计时间-结束")
    private Date statisticsEndTime;

    /**
     * 区域分布-省-编码
     */
    @ApiModelProperty(value = "区域分布-省-编码")
    private String statisticsProvinceCode;

    /**
     * 区域分布-省-名称
     */
    @ApiModelProperty(value = "区域分布-省-名称")
    private String statisticsProvinceName;

    /**
     * 区域分布-市-编码
     */
    @ApiModelProperty(value = "区域分布-市-编码")
    private String statisticsCityCode;

    /**
     * 区域分布-市-名称
     */
    @ApiModelProperty(value = "区域分布-市-名称")
    private String statisticsCityName;

    /**
     * 区域分布-区县-编码
     */
    @ApiModelProperty(value = "区域分布-区县-编码")
    private String statisticsDistrictCode;

    /**
     * 区域分布-区县-名称
     */
    @ApiModelProperty(value = "区域分布-区县-名称")
    private String statisticsDistrictName;

    /**
     * 生成时间
     */
    @ApiModelProperty(value = "生成时间")
    private Date generateTime;

    /**
     * 原始上传文件（excel），可选
     */
    @ApiModelProperty(value = "原始上传文件（excel），可选")
    private String statisticsFilePath;

    /**
     * 生成简报文件（word）
     */
    @ApiModelProperty(value = "生成简报文件（word）")
    private String generateFilePath;
}
