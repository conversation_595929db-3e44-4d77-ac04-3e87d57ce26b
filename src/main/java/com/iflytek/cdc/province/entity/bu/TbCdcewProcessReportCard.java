package com.iflytek.cdc.province.entity.bu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 病例相关报卡表;
 * <AUTHOR> dingyuan
 * @date : 2025-3-11
 */
@ApiModel(value = "病例相关报卡表")
@Data
public class TbCdcewProcessReportCard implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键id(uuid)
     */
    @ApiModelProperty(value = "主键id(uuid)")
    private String id ;

    /**
     * 报卡id(唯一键)
     */
    @ApiModelProperty(value = "报卡id(唯一键)")
    private String reportId ;

    /**
     * 病例id
     */
    @ApiModelProperty(value = "病例id")
    private String processId ;

    /**
     * 疾病类型：infected传染病；syndrome症候群
     */
    @ApiModelProperty(value = "疾病类型：infected传染病；syndrome症候群")
    private String diseaseType ;

    /**
     * 报告时间
     */
    @ApiModelProperty(value = "报告时间")
    private Date reportTime ;

    /**
     * 患者id
     */
    @ApiModelProperty(value = "患者id")
    private String patientId ;

    /**
     * 患者姓名
     */
    @ApiModelProperty(value = "患者姓名")
    private String patientName ;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String sexName ;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private Date birthDay ;

    /**
     * 数据权限-住址编码
     */
    @ApiModelProperty(value = "数据权限-住址编码")
    private String permitLivingAreaCode ;

    /**
     * 数据权限-工作单位地址编码
     */
    @ApiModelProperty(value = "数据权限-工作单位地址编码")
    private String permitCompanyAreaCode ;

    /**
     * 数据权限-医疗机构地址编码
     */
    @ApiModelProperty(value = "数据权限-医疗机构地址编码")
    private String permitOrgAreaCode ;

    /**
     * 疾病code
     */
    @ApiModelProperty(value = "疾病code")
    private String diseaseCode ;

    /**
     * 疾病name
     */
    @ApiModelProperty(value = "疾病name")
    private String diseaseName ;

    /**
     * 诊断状态
     */
    @ApiModelProperty(value = "诊断状态")
    private String diagnoseStatus ;

    /**
     * 诊断时间
     */
    @ApiModelProperty(value = "诊断时间")
    private Date diagnoseTime ;

    /**
     * 排除标识
     */
    @ApiModelProperty(value = "排除标识")
    private String outFlag ;

    /**
     * 重症标识
     */
    @ApiModelProperty(value = "重症标识")
    private String severeFlag ;

    /**
     * 审核识别状态
     */
    @ApiModelProperty(value = "审核识别状态")
    private String checkIdentifyStatus ;

    /**
     * 审核流程状态
     */
    @ApiModelProperty(value = "审核流程状态")
    private String checkProcessStatus ;

    /**
     * 发病时间
     */
    @ApiModelProperty(value = "发病时间")
    private Date onsetTime ;

    /**
     * 就诊时间
     */
    @ApiModelProperty(value = "就诊时间")
    private Date visitTime ;

    /**
     * 审核时间
     */
    @ApiModelProperty(value = "审核时间")
    private Date checkTime ;

    /**
     * 超时状态
     */
    @ApiModelProperty(value = "超时状态")
    private String timeoutStatus ;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime ;

    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id")
    private String updaterId ;

    /**
     * 更新人name
     */
    @ApiModelProperty(value = "更新人name")
    private String updater ;

}
