package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 实验室检测信息
 */
@Data
@TableName(value = "ads.ads_edr_lis_info")
public class AdsEdrLisInfo {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 采集创建时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * 采集更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;

    /**
     * 事件信息ID
     */
    @TableField(value = "event_id")
    private String eventId;

    /**
     * 主索引ID
     */
    @TableField(value = "empi_id")
    private String empiId;

    /**
     * 标本类别代码
     */
    @TableField(value = "specimen_category")
    private String specimenCategory;

    /**
     * 标本类别名称
     */
    @TableField(value = "specimen_category_name")
    private String specimenCategoryName;

    /**
     * 标本采样日期时间
     */
    @TableField(value = "specimen_sampling_date")
    private Date specimenSamplingDate;

    /**
     * 接收标本日期时间
     */
    @TableField(value = "specimen_receiving_date")
    private Date specimenReceivingDate;

    /**
     * 检验项目代码
     */
    @TableField(value = "examination_item_code")
    private String examinationItemCode;

    /**
     * 检验项目名称
     */
    @TableField(value = "examination_item")
    private String examinationItem;

    /**
     * 检测方法类别代码
     */
    @TableField(value = "examination_method_code")
    private String examinationMethodCode;

    /**
     * 检测日期
     */
    @TableField(value = "examination_date")
    private Date examinationDate;

    /**
     * 检验标本号
     */
    @TableField(value = "specimen_no")
    private String specimenNo;

    /**
     * 检验定量结果
     */
    @TableField(value = "examination_quantification")
    private String examinationQuantification;

    /**
     * 检验定量结果参考区间-上限
     */
    @TableField(value = "examination_quantification_upper")
    private String examinationQuantificationUpper;

    /**
     * 检验定量结果参考区间-下限
     */
    @TableField(value = "examination_quantification_lower")
    private String examinationQuantificationLower;

    /**
     * 检验定量结果超出或低于参考值
     */
    @TableField(value = "examination_quantification_ri")
    private String examinationQuantificationRi;

    /**
     * 检验定量结果计量单位
     */
    @TableField(value = "examination_quantification_unit")
    private String examinationQuantificationUnit;

    /**
     * 检验定性结果代码
     */
    @TableField(value = "source_examination_result_code")
    private String sourceExaminationResultCode;

    /**
     * 实验室检测结论名称
     */
    @TableField(value = "source_examination_result")
    private String sourceExaminationResult;

    /**
     * 检测报告日期
     */
    @TableField(value = "examination_report_date")
    private Date examinationReportDate;

    /**
     * 确诊状态
     */
    @TableField(value = "confirm_status_code")
    private String confirmStatusCode;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 数据来源ID
     */
    @TableField(value = "source_id")
    private String sourceId;

    /**
     * 主索引ID
     */
    @TableField(value = "mpi_id")
    private String mpiId;

    /**
     * 月序
     */
    @TableField(value = "\"month\"")
    private String month;

    /**
     * 创建机构
     */
    @TableField(value = "create_org")
    private String createOrg;

    /**
     * 创建机构名称
     */
    @TableField(value = "create_org_name")
    private String createOrgName;

    /**
     * 修改机构
     */
    @TableField(value = "update_org")
    private String updateOrg;

    /**
     * 修改机构名称
     */
    @TableField(value = "update_org_name")
    private String updateOrgName;
}