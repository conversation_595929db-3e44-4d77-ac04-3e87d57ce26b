package com.iflytek.cdc.province.entity.bu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@ApiModel(description="public.tb_cdcdm_data_attr_value")
@Data
public class TbCdcdmDataAttrValue implements Serializable {
    /**
    * 主键
    */
    @ApiModelProperty(value="主键")
    private String id;

    /**
    * 值域ID
    */
    @ApiModelProperty(value="值域ID")
    private String dataAttrId;

    @ApiModelProperty(value="")
    private String attrValue1;

    @ApiModelProperty(value="")
    private String attrValue2;

    @ApiModelProperty(value="")
    private String attrValue3;

    @ApiModelProperty(value="")
    private String attrValue4;

    @ApiModelProperty(value="")
    private String attrValue5;

    @ApiModelProperty(value="")
    private String attrValue6;

    @ApiModelProperty(value="")
    private String attrValue7;

    @ApiModelProperty(value="")
    private String attrValue8;

    @ApiModelProperty(value="")
    private String attrValue9;

    @ApiModelProperty(value="")
    private String attrValue10;

    /**
    * 是否删除
    */
    @ApiModelProperty(value="是否删除")
    private Integer isDeleted;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
    * 创建人
    */
    @ApiModelProperty(value="创建人")
    private String creator;

    /**
    * 修改时间
    */
    @ApiModelProperty(value="修改时间")
    private Date updateTime;

    /**
    * 修改人
    */
    @ApiModelProperty(value="修改人")
    private String updater;

    private static final long serialVersionUID = 1L;
}