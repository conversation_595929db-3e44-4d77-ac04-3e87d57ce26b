package com.iflytek.cdc.province.entity.bu;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 病例相关报卡审核记录;
 * <AUTHOR> dingyuan
 * @date : 2025-3-10
 */
@ApiModel(value = "病例相关报卡审核记录")
@Data
public class TbCdcewProcessReportCheckLog implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id ;

    /**
     * 报卡ID
     */
    @ApiModelProperty(value = "报卡ID")
    private String reportId ;

    /**
     * 病例ID
     */
    @ApiModelProperty(value = "病例ID")
    private String processId ;

    /**
     * 疾病类型：infected传染病；syndrome症候群
     */
    @ApiModelProperty(value = "疾病类型：infected传染病；syndrome症候群")
    private String diseaseType ;

    /**
     * 审核识别状态
     */
    @ApiModelProperty(value = "审核识别状态")
    private String checkIdentifyStatus ;

    /**
     * 审核退回原因
     */
    @ApiModelProperty(value = "审核退回原因")
    private String rejectReason ;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime ;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator ;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private String creatorId ;

}
