package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


@Data
@ApiModel("疫情事件信息")
public class DyqwEvent {


    // 基础信息
    @ApiModelProperty("事件ID")
    private String eventId;
    @ApiModelProperty("事件类别")
    private String eventTypeId;
    @ApiModelProperty("事件类别名称")
    private String eventType;
    @ApiModelProperty("事件名称")
    private String eventName;

    // 等级信息
    @ApiModelProperty("事件严重等级编码")
    private String severityGradeCode;
    @ApiModelProperty("事件严重等级描述")
    private String severityGrade;

    // 报告信息
    @ApiModelProperty("报告地区编码")
    private String reportZoneCode;
    @ApiModelProperty("报告地区")
    private String reportZone;
    @ApiModelProperty("报告单位编码")
    private String reportOrganizationCode;
    @ApiModelProperty("报告单位")
    private String reportOrganization;

    // 状态信息
    @ApiModelProperty("事件状态")
    private String eventStatusCode;
    @ApiModelProperty("事件状态描述")
    private String eventStatus;
    @ApiModelProperty("审核等级")
    private String reviewGradeCode;
    @ApiModelProperty("审核等级描述")
    private String reviewGrade;
    @ApiModelProperty("最后一次报告ID")
    private String lastReportId;

    // 时间信息
    @ApiModelProperty("事件创建时间")
    private Date eventCreateTime;
    @ApiModelProperty("事件结案时间")
    private Date eventFinalTime;
    @ApiModelProperty("事件删除时间")
    private Date eventDeleteTime;
    @ApiModelProperty("最近一次修改时间")
    private Date latestModifyTime;
    @ApiModelProperty("国家审核时间")
    private Date nationReviewTime;
    @ApiModelProperty("省级审核时间")
    private Date provReviewTime;
    @ApiModelProperty("市级审核时间")
    private Date cityReviewTime;
    @ApiModelProperty("县区级审核时间")
    private Date countyReviewTime;
    @ApiModelProperty("事件发生时间")
    private Date eventHappenTime;

    // 学校信息
    @ApiModelProperty("学校类别")
    private String schoolTypeCode;
    @ApiModelProperty("学校类别名称")
    private String schoolTypeName;

    // 审核信息
    @ApiModelProperty("事件审核状态")
    private Integer eventReviewStatusCode;
    @ApiModelProperty("事件审核状态描述")
    private String eventReviewStatus;
    @ApiModelProperty("事件审核审核人id")
    private Integer eventReviewPersonId;
    @ApiModelProperty("事件审核审核人名称")
    private String eventReviewPerson;
    @ApiModelProperty("事件审核时间")
    private Date eventReviewTime;
    @ApiModelProperty("第一次审核时间")
    private Date firstReviewTime;

    // 其他信息
    @ApiModelProperty("昨日修改事件")
    private String yesterdayModifyTime;
    @ApiModelProperty("是否有过删除")
    private String ifDeleteOnce;
    @ApiModelProperty("删除原因")
    private String deleteReason;
    @ApiModelProperty("合并标识")
    private Integer mergeIdentity;
    @ApiModelProperty("原始事件关联的合并事件id")
    private String relateEventId;

    // 地区信息
    @ApiModelProperty("事件发生地区编码")
    private String eventHappenDistrictCode;
    @ApiModelProperty("事件发生地区")
    private String eventHappenDistrict;

    // 报告人信息
    @ApiModelProperty("事件报告人id")
    private Integer eventReportPersonId;
    @ApiModelProperty("事件报告人名称")
    private String eventReportPerson;

    @TableField(exist = false)
    private String type;

    @TableField(exist = false)
    private String value;


    @ApiModelProperty("关联报告列表")
    private List<DyqwEventReport> reportList;

}