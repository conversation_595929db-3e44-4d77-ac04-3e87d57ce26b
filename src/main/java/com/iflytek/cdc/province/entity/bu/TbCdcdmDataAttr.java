package com.iflytek.cdc.province.entity.bu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@ApiModel(description="public.tb_cdcdm_data_attr")
@Data
public class TbCdcdmDataAttr implements Serializable {
    /**
    * 主键
    */
    @ApiModelProperty(value="主键")
    private String id;

    /**
    * 名称
    */
    @ApiModelProperty(value="名称")
    private String name;

    /**
    * 层级数
    */
    @ApiModelProperty(value="层级数")
    private Integer attrCount;

    /**
    * 值域个数
    */
    @ApiModelProperty(value="值域个数")
    private Integer attrValueCount;

    /**
    * 类型：1. 值域-单值；2. 值域-代码-值；3-接口
    */
    @ApiModelProperty(value="类型：1. 值域-单值；2. 值域-代码-值；3-接口")
    private String type;

    /**
    * 接口url
    */
    @ApiModelProperty(value="接口url")
    private String dataUrl;

    /**
    * 数据字典编码
    */
    @ApiModelProperty(value="数据字典编码")
    private String dataDictCode;

    /**
    * 备注
    */
    @ApiModelProperty(value="备注")
    private String note;

    /**
    * 是否删除
    */
    @ApiModelProperty(value="是否删除")
    private Integer isDeleted;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
    * 创建人
    */
    @ApiModelProperty(value="创建人")
    private String creator;

    /**
    * 修改时间
    */
    @ApiModelProperty(value="修改时间")
    private Date updateTime;

    /**
    * 修改人
    */
    @ApiModelProperty(value="修改人")
    private String updater;

    private static final long serialVersionUID = 1L;
}