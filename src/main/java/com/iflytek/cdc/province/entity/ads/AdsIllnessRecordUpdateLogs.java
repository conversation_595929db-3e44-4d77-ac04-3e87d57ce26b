package com.iflytek.cdc.province.entity.ads;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.cdc.province.enums.MaintenanceOperateEnum;
import com.iflytek.cdc.province.enums.StatusEnum;
import com.iflytek.cdc.province.model.edr.vo.MaintenanceLogExcelVO;
import com.iflytek.cdc.province.utils.DateUtils;
import com.iflytek.fpva.common.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Date;

/**
 * ;
 * <AUTHOR> dingyuan
 * @date : 2025-1-17
 */
@ApiModel(value = "edr更新日志表")
@Data
public class AdsIllnessRecordUpdateLogs {

    private static final long serialVersionUID = 1L;

    /**
     * uuid，唯一标识
     */
    @ApiModelProperty(value = "uuid，唯一标识")
    private String id ;

    /**
     * 疾病档案id
     */
    @ApiModelProperty(value = "疾病档案id")
    private String recordId ;

    /**
     * 患者ID
     */
    @ApiModelProperty(value = "患者ID")
    private String patientId ;

    /**
     * 患者姓名
     */
    @ApiModelProperty(value = "患者姓名")
    private String patientName ;

    /**
     * 维护更新操作
     */
    @ApiModelProperty(value = "维护更新操作")
    private String operator ;

    /**
     * 更新状态
     */
    @ApiModelProperty(value = "更新状态")
    private Integer status ;

    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码")
    private String provinceCode ;

    /**
     * 省名称
     */
    @ApiModelProperty(value = "省名称")
    private String provinceName ;

    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码")
    private String cityCode ;

    /**
     * 市名称
     */
    @ApiModelProperty(value = "市名称")
    private String cityName ;

    /**
     * 区编码
     */
    @ApiModelProperty(value = "区编码")
    private String districtCode ;

    /**
     * 区名称
     */
    @ApiModelProperty(value = "区名称")
    private String districtName ;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date updateTime ;

    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id")
    private String updateId ;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updater ;

    /**
     * 更新类型，标识应用侧和数仓侧
     */
    @ApiModelProperty(value = "更新类型，标识应用侧和数仓侧")
    private String sourceType ;
    
    public static MaintenanceLogExcelVO of(AdsIllnessRecordUpdateLogs log){

        MaintenanceLogExcelVO excelVO = new MaintenanceLogExcelVO();
        BeanUtils.copyProperties(log, excelVO);
        excelVO.setMaintenanceTime(DateUtils.formatDate(log.getUpdateTime(), DateUtils.DATE_FORMAT));
        excelVO.setOperatorDesc(MaintenanceOperateEnum.getDescByCode(log.getOperator()));
        excelVO.setUpdateStatus(StatusEnum.getMaintenanceDescByCode(log.getStatus()));
        return excelVO;
    }

}
