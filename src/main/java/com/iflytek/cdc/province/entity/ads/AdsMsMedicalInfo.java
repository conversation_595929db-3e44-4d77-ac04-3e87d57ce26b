package com.iflytek.cdc.province.entity.ads;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 诊疗(medical service)-病历详情（基本标签建设）;
 * <AUTHOR> dingyuan
 * @date : 2024-9-12
 */
@ApiModel(value = "诊疗(medical service)-病历详情（基本标签建设）")
@Data
public class AdsMsMedicalInfo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 病历id
     */
    @ApiModelProperty(value = "病历id")
    private String medicalId ;

    /**
     * 就诊事件id
     */
    @ApiModelProperty(value = "就诊事件id")
    private String eventId ;

    /**
     * 病程id
     */
    @ApiModelProperty(value = "病程id")
    private String processId ;

    /**
     * 生命周期id
     */
    @ApiModelProperty(value = "生命周期id")
    private String lifeId ;

    /**
     * ETL创建时间
     */
    @ApiModelProperty(value = "ETL创建时间")
    private Date etlCreateDatetime ;

    /**
     * ETL更新时间
     */
    @ApiModelProperty(value = "ETL更新时间")
    private Date etlUpdateDatetime ;

    /**
     * 患者（不变）信息-姓名
     */
    @ApiModelProperty(value = "患者（不变）信息-姓名")
    private String patientName ;

    /**
     * 患者（不变）信息-性别（有字典，313001）
     */
    @ApiModelProperty(value = "患者（不变）信息-性别（有字典，313001）")
    private String patientSexName ;

    /**
     * 患者（不变）信息-身份证件类别
     */
    @ApiModelProperty(value = "患者（不变）信息-身份证件类别")
    private String patientIdentityType ;

    /**
     * 患者（不变）信息-身份证号
     */
    @ApiModelProperty(value = "患者（不变）信息-身份证号")
    private String patientIdentityNo ;

    /**
     * 患者（不变）信息-出生日期
     */
    @ApiModelProperty(value = "患者（不变）信息-出生日期")
    private Date patientBirthDay ;

    /**
     * 患者（可变）信息-人员id
     */
    @ApiModelProperty(value = "患者（可变）信息-人员id")
    private String patientId ;

    /**
     * 患者（可变）信息-患者年龄（单位岁）
     */
    @ApiModelProperty(value = "患者（可变）信息-患者年龄（单位岁）")
    private Integer patientAge ;

    /**
     * 患者（可变）信息-患者联系方式
     */
    @ApiModelProperty(value = "患者（可变）信息-患者联系方式")
    private String patientPhone ;

    /**
     * 患者（可变）信息-现住详细地址
     */
    @ApiModelProperty(value = "患者（可变）信息-现住详细地址")
    private String livingAddrDetail ;

    /**
     * 患者（可变）信息-现住址场所类别
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址场所类别")
    private String livingAddrPoi ;

    /**
     * 患者（可变）信息-现住址城乡类型（城市、乡村、未知）
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址城乡类型（城市、乡村、未知）")
    private String livingAddrType ;

    /**
     * 患者（可变）信息-现住址标化地址
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址标化地址")
    private String livingAddrDetailStd ;

    /**
     * 患者（可变）信息-现住地-省编码
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-省编码")
    private String livingAddrProvinceCode ;

    /**
     * 患者（可变）信息-现住地-省
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-省")
    private String livingAddrProvince ;

    /**
     * 患者（可变）信息-现住地-市编码
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-市编码")
    private String livingAddrCityCode ;

    /**
     * 患者（可变）信息-现住地-市
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-市")
    private String livingAddrCity ;

    /**
     * 患者（可变）信息-现住地-行政区编码
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-行政区编码")
    private String livingAddrDistrictCode ;

    /**
     * 患者（可变）信息-现住地-行政区
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-行政区")
    private String livingAddrDistrict ;

    /**
     * 患者（可变）信息-现住址-功能区编码
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-功能区编码")
    private String livingAddrFuncDistrictCode ;

    /**
     * 患者（可变）信息-现住址-功能区
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-功能区")
    private String livingAddrFuncDistrict ;

    /**
     * 患者（可变）信息-现住址-乡（镇、街道办事处）
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-乡（镇、街道办事处）")
    private String livingAddrTown ;

    /**
     * 患者（可变）信息-现住址-村（街、路、弄等）
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-村（街、路、弄等）")
    private String livingAddrVillage ;

    /**
     * 患者（可变）信息-现住址 门牌号码
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址 门牌号码")
    private String livingAddrDoor ;

    /**
     * 患者（可变）信息-现住址-街道编码
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-街道编码")
    private String livingAddrStreetCode ;

    /**
     * 患者（可变）信息-现住址-街道
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-街道")
    private String livingAddrStreet ;

    /**
     * 患者（可变）信息-现住址-街道中心经度
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-街道中心经度")
    private Double livingAddrStreetLongitude ;

    /**
     * 患者（可变）信息-现住址-街道中心纬度
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-街道中心纬度")
    private Double livingAddrStreetLatitude ;

    /**
     * 患者（可变）信息-现住址-经度
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-经度")
    private Double livingAddrLongitude ;

    /**
     * 患者（可变）信息-现住址-经度
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-经度")
    private Double livingAddrLatitude ;

    /**
     * 患者（可变）信息-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）
     */
    @ApiModelProperty(value = "患者（可变）信息-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）")
    private String personType ;

    /**
     * 患者（可变）信息-职业
     */
    @ApiModelProperty(value = "患者（可变）信息-职业")
    private String job ;

    /**
     * 患者（可变）信息-职业风险
     */
    @ApiModelProperty(value = "患者（可变）信息-职业风险")
    private String jobRisk ;

    /**
     * 患者（可变）信息-工作单位/学校名称
     */
    @ApiModelProperty(value = "患者（可变）信息-工作单位/学校名称")
    private String company ;

    /**
     * 患者（可变）信息-单位/学校标化地址
     */
    @ApiModelProperty(value = "患者（可变）信息-单位/学校标化地址")
    private String companyAddrDetailStd ;

    /**
     * 单位-城乡类型（城市、乡村、未知）
     */
    @ApiModelProperty(value = "单位-城乡类型（城市、乡村、未知）")
    private String companyAddrType ;

    /**
     * 患者（可变）信息-单位-省编码
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-省编码")
    private String companyProvinceCode ;

    /**
     * 患者（可变）信息-单位-省
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-省")
    private String companyProvince ;

    /**
     * 患者（可变）信息-单位-市编码
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-市编码")
    private String companyCityCode ;

    /**
     * 患者（可变）信息-单位-市
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-市")
    private String companyCity ;

    /**
     * 患者（可变）信息-单位-行政区编码
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-行政区编码")
    private String companyDistrictCode ;

    /**
     * 患者（可变）信息-单位-行政区
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-行政区")
    private String companyDistrict ;

    /**
     * 患者（可变）信息-单位-功能区编码
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-功能区编码")
    private String companyFuncDistrictCode ;

    /**
     * 患者（可变）信息-单位-功能区
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-功能区")
    private String companyFuncDistrict ;

    /**
     * 患者（可变）信息-单位-街道编码
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-街道编码")
    private String companyStreetCode ;

    /**
     * 患者（可变）信息-单位-街道
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-街道")
    private String companyStreet ;

    /**
     * 患者（可变）信息-单位-街道中心经度
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-街道中心经度")
    private Double companyStreetLongitude ;

    /**
     * 患者（可变）信息-单位-街道中心纬度
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-街道中心纬度")
    private Double companyStreetLatitude ;

    /**
     * 患者（可变）信息-单位-经度
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-经度")
    private Double companyLongitude ;

    /**
     * 患者（可变）信息-单位-纬度
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-纬度")
    private Double companyLatitude ;

    /**
     * 机构id（主索引）
     */
    @ApiModelProperty(value = "机构id（主索引）")
    private String orgId ;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String orgName ;

    /**
     * 机构类别（等级医院、基层医疗）
     */
    @ApiModelProperty(value = "机构类别（等级医院、基层医疗）")
    private String orgClass ;

    /**
     * 机构类型名称(204003)
     */
    @ApiModelProperty(value = "机构类型名称(204003)")
    private String orgTypeName ;

    /**
     * 机构详细地址
     */
    @ApiModelProperty(value = "机构详细地址")
    private String orgAddrDetail ;

    /**
     * 机构-城乡类型（城市、乡村、未知）
     */
    @ApiModelProperty(value = "机构-城乡类型（城市、乡村、未知）")
    private String orgAddrType ;

    /**
     * 机构标化地址
     */
    @ApiModelProperty(value = "机构标化地址")
    private String orgAddrDetailStd ;

    /**
     * 机构-省编码
     */
    @ApiModelProperty(value = "机构-省编码")
    private String orgAddrProvinceCode ;

    /**
     * 机构-省
     */
    @ApiModelProperty(value = "机构-省")
    private String orgAddrProvince ;

    /**
     * 机构-市编码
     */
    @ApiModelProperty(value = "机构-市编码")
    private String orgAddrCityCode ;

    /**
     * 机构-市
     */
    @ApiModelProperty(value = "机构-市")
    private String orgAddrCity ;

    /**
     * 机构-行政区编码
     */
    @ApiModelProperty(value = "机构-行政区编码")
    private String orgAddrDistrictCode ;

    /**
     * 机构-行政区
     */
    @ApiModelProperty(value = "机构-行政区")
    private String orgAddrDistrict ;

    /**
     * 机构-功能区编码
     */
    @ApiModelProperty(value = "机构-功能区编码")
    private String orgAddrFuncDistrictCode ;

    /**
     * 机构-功能区
     */
    @ApiModelProperty(value = "机构-功能区")
    private String orgAddrFuncDistrict ;

    /**
     * 机构-街道编码
     */
    @ApiModelProperty(value = "机构-街道编码")
    private String orgAddrStreetCode ;

    /**
     * 机构-街道
     */
    @ApiModelProperty(value = "机构-街道")
    private String orgAddrStreet ;

    /**
     * 机构-街道中心经度
     */
    @ApiModelProperty(value = "机构-街道中心经度")
    private Double orgAddrStreetLongitude ;

    /**
     * 机构-街道中心纬度
     */
    @ApiModelProperty(value = "机构-街道中心纬度")
    private Double orgAddrStreetLatitude ;

    /**
     * 机构经度
     */
    @ApiModelProperty(value = "机构经度")
    private Double orgLongitude ;

    /**
     * 机构纬度
     */
    @ApiModelProperty(value = "机构纬度")
    private Double orgLatitude ;

    /**
     * 发病时间
     */
    @ApiModelProperty(value = "发病时间")
    private Date onsetTime ;

    /**
     * 就诊时间/入院时间
     */
    @ApiModelProperty(value = "就诊时间/入院时间")
    private Date visitTime ;

    /**
     * 所在日
     */
    @ApiModelProperty(value = "所在日")
    private Date day ;

    /**
     * 统计期所在周（年第几周）
     */
    @ApiModelProperty(value = "统计期所在周（年第几周）")
    private Integer week ;

    /**
     * 所在旬
     */
    @ApiModelProperty(value = "所在旬")
    private String tenDay ;

    /**
     * 所在月
     */
    @ApiModelProperty(value = "所在月")
    private Integer month ;

    /**
     * 所在季度
     */
    @ApiModelProperty(value = "所在季度")
    private Integer quarter ;

    /**
     * 所在年
     */
    @ApiModelProperty(value = "所在年")
    private Integer year ;

    /**
     * 周末标识（1是，0否）
     */
    @ApiModelProperty(value = "周末标识（1是，0否）")
    private String weekendFlag ;

    /**
     * 节假日标识（1是，0否）
     */
    @ApiModelProperty(value = "节假日标识（1是，0否）")
    private String holidayFlag ;

    /**
     * 是否为异常时间就诊病历（非8-18点就诊）
     */
    @ApiModelProperty(value = "是否为异常时间就诊病历（非8-18点就诊）")
    private String abnormalTimeFlag ;

    /**
     * 是否模板病历（基于模板未做明显更改，即同一医生在一分钟内病历数大于等于2）
     */
    @ApiModelProperty(value = "是否模板病历（基于模板未做明显更改，即同一医生在一分钟内病历数大于等于2）")
    private String justModelFlag ;

    /**
     * 就诊类型编码
     */
    @ApiModelProperty(value = "就诊类型编码")
    private String visitTypeCode ;

    /**
     * 就诊类型（门诊、急诊、住院等）
     */
    @ApiModelProperty(value = "就诊类型（门诊、急诊、住院等）")
    private String visitTypeName ;

    /**
     * 就诊科室编码
     */
    @ApiModelProperty(value = "就诊科室编码")
    private String deptCode ;

    /**
     * 就诊科室
     */
    @ApiModelProperty(value = "就诊科室")
    private String deptName ;

    /**
     * 发热门诊标识(1是，0否)
     */
    @ApiModelProperty(value = "发热门诊标识(1是，0否)")
    private String heatFlag ;

    /**
     * 肠道门诊标识(1是，0否)
     */
    @ApiModelProperty(value = "肠道门诊标识(1是，0否)")
    private String bowelFlag ;

    /**
     * 接诊医生编码
     */
    @ApiModelProperty(value = "接诊医生编码")
    private String doctorCode ;

    /**
     * 接诊医生名称
     */
    @ApiModelProperty(value = "接诊医生名称")
    private String doctorName ;

    /**
     * 初诊标识
     */
    @ApiModelProperty(value = "初诊标识")
    private String firstDiagFlag ;

    /**
     * 急诊标识
     */
    @ApiModelProperty(value = "急诊标识")
    private String emergencyFlag ;

    /**
     * 主诉
     */
    @ApiModelProperty(value = "主诉")
    private String suit ;

    /**
     * 主诉解析症状
     */
    @ApiModelProperty(value = "主诉解析症状")
    private String suitSymptom ;

    /**
     * 主诉解析症状发病时间（与主诉症状顺序对应）
     */
    @ApiModelProperty(value = "主诉解析症状发病时间（与主诉症状顺序对应）")
    private String suitOnsetTime ;

    /**
     * 解析诱因（|分隔）
     */
    @ApiModelProperty(value = "解析诱因（|分隔）")
    private String cause ;

    /**
     * 症状
     */
    @ApiModelProperty(value = "症状")
    private String symptom ;

    /**
     * 特异症状
     */
    @ApiModelProperty(value = "特异症状")
    private String symptomSpecial ;

    /**
     * 现病史
     */
    @ApiModelProperty(value = "现病史")
    private String diseaseHistoryNow ;

    /**
     * 既往史
     */
    @ApiModelProperty(value = "既往史")
    private String diseaseHistoryBefore ;

    /**
     * 暴露史
     */
    @ApiModelProperty(value = "暴露史")
    private String exposeHistory ;

    /**
     * 旅居史
     */
    @ApiModelProperty(value = "旅居史")
    private String travelHistory ;

    /**
     * 接触史
     */
    @ApiModelProperty(value = "接触史")
    private String touchHistory ;

    /**
     * 传染病史/流行病学史
     */
    @ApiModelProperty(value = "传染病史/流行病学史")
    private String infectHistory ;

    /**
     * 粪便性状（dict）
     */
    @ApiModelProperty(value = "粪便性状（dict）")
    private String shitDesc ;

    /**
     * 发热性状（dict)
     */
    @ApiModelProperty(value = "发热性状（dict)")
    private String heatDesc ;

    /**
     * 生冷刺激饮食标识
     */
    @ApiModelProperty(value = "生冷刺激饮食标识")
    private String eatColdFlag ;

    /**
     * 不洁饮食标识
     */
    @ApiModelProperty(value = "不洁饮食标识")
    private String eatUncleanFlag ;

    /**
     * 其他饮食标识
     */
    @ApiModelProperty(value = "其他饮食标识")
    private String eatOrtherFoodFlag ;

    /**
     * 饮酒标识
     */
    @ApiModelProperty(value = "饮酒标识")
    private String drinkFlag ;

    /**
     * 吃药标识
     */
    @ApiModelProperty(value = "吃药标识")
    private String drugFlag ;

    /**
     * 个人史
     */
    @ApiModelProperty(value = "个人史")
    private String personalHistory ;

    /**
     * 过敏史
     */
    @ApiModelProperty(value = "过敏史")
    private String allergyHistory ;

    /**
     * 遗传史
     */
    @ApiModelProperty(value = "遗传史")
    private String geneticHistory ;

    /**
     * 家族史
     */
    @ApiModelProperty(value = "家族史")
    private String familyHistory ;

    /**
     * 体格检查
     */
    @ApiModelProperty(value = "体格检查")
    private String checkup ;

    /**
     * 辅助检查
     */
    @ApiModelProperty(value = "辅助检查")
    private String assistedExam ;

    /**
     * 体温
     */
    @ApiModelProperty(value = "体温")
    private String temperature ;

    /**
     * 呼吸频率
     */
    @ApiModelProperty(value = "呼吸频率")
    private String breathFreq ;

    /**
     * 舒张压
     */
    @ApiModelProperty(value = "舒张压")
    private String diastolicPressure ;

    /**
     * 平均压
     */
    @ApiModelProperty(value = "平均压")
    private String meanPressure ;

    /**
     * 收缩压
     */
    @ApiModelProperty(value = "收缩压")
    private String systolicPressure ;

    /**
     * 空腹血糖
     */
    @ApiModelProperty(value = "空腹血糖")
    private String fastingGlucose ;

    /**
     * 餐后血糖
     */
    @ApiModelProperty(value = "餐后血糖")
    private String postprandialGlucose ;

    /**
     * 血氧
     */
    @ApiModelProperty(value = "血氧")
    private String bloodOxygen ;

    /**
     * 心率
     */
    @ApiModelProperty(value = "心率")
    private String heartRate ;

    /**
     * 体重
     */
    @ApiModelProperty(value = "体重")
    private String weight ;

    /**
     * 身高
     */
    @ApiModelProperty(value = "身高")
    private String height ;

    /**
     * 脉率
     */
    @ApiModelProperty(value = "脉率")
    private String pulse ;

    /**
     * 残疾情况描述
     */
    @ApiModelProperty(value = "残疾情况描述")
    private String disabilitySituation ;

    /**
     * 主诊断编码
     */
    @ApiModelProperty(value = "主诊断编码")
    private String mainDiagCode ;

    /**
     * 主诊断
     */
    @ApiModelProperty(value = "主诊断")
    private String mainDiag ;

    /**
     * 标准主诊断编码
     */
    @ApiModelProperty(value = "标准主诊断编码")
    private String mainDiagStdCode ;

    /**
     * 标准主诊断
     */
    @ApiModelProperty(value = "标准主诊断")
    private String mainDiagStd ;

    /**
     * 主诊断类型（确诊、疑似）
     */
    @ApiModelProperty(value = "主诊断类型（确诊、疑似）")
    private String mainDiagType ;

    /**
     * 主诊断慢性病标识
     */
    @ApiModelProperty(value = "主诊断慢性病标识")
    private String chronicFlag ;

    /**
     * 诊断信息（json，诊断编码diag_code，诊断名称diag_name，主诊断标识diag_main_flag，诊断类型（初步诊断、疑似诊断、确诊诊断？）diag_type，诊断疾病类型（急性、慢性）diag_disease_type，标准诊断名称diag_code_std，标准诊断编码diag_name_std）
     */
    @ApiModelProperty(value = "诊断信息（json，诊断编码diag_code，诊断名称diag_name，主诊断标识diag_main_flag，诊断类型（初步诊断、疑似诊断、确诊诊断？）diag_type，诊断疾病类型（急性、慢性）diag_disease_type，标准诊断名称diag_code_std，标准诊断编码diag_name_std）")
    private String diagJson ;

    /**
     * 病原检测信息（json，检测时间pathogen_report_time，检测项目pathogen_item_name，检测定性结果pathogen_result_qualit，检测定量结果pathogen_result_quantit，检测定量单位pathogen_result_quantit_unit）
     */
    @ApiModelProperty(value = "病原检测信息（json，检测时间pathogen_report_time，检测项目pathogen_item_name，检测定性结果pathogen_result_qualit，检测定量结果pathogen_result_quantit，检测定量单位pathogen_result_quantit_unit）")
    private String pathogenJson ;

    /**
     * 用药信息（json，药品名称drug_name，药物规格drug_specs，药物使用总剂量drug_dosage_total，药物使用单次剂量drug_dosage，药物用法频次drug_usage）
     */
    @ApiModelProperty(value = "用药信息（json，药品名称drug_name，药物规格drug_specs，药物使用总剂量drug_dosage_total，药物使用单次剂量drug_dosage，药物用法频次drug_usage）")
    private String drugJson ;

    /**
     * 转归时间
     */
    @ApiModelProperty(value = "转归时间")
    private Date outcomeTime ;

    /**
     * 转归状态（1治愈、2好转、3稳定、4恶化、5死亡、9其他）
     */
    @ApiModelProperty(value = "转归状态（1治愈、2好转、3稳定、4恶化、5死亡、9其他）")
    private String outcomeStatus ;

    /**
     * 死亡原因（详细的，因该病死亡，其他）
     */
    @ApiModelProperty(value = "死亡原因（详细的，因该病死亡，其他）")
    private String deadReason ;

    /**
     * 出院时间
     */
    @ApiModelProperty(value = "出院时间")
    private Date dischargeTime ;

    /**
     * 病历类型（西医、中医、中西医等）
     */
    @ApiModelProperty(value = "病历类型（西医、中医、中西医等）")
    private String emrType ;

    /**
     * 信息完整性标识（1是0否，病历id、姓名、性别、年龄、身份证号、现住址、诊断、诊断时间、就诊科室、就诊机构都有值）
     */
    @ApiModelProperty(value = "信息完整性标识（1是0否，病历id、姓名、性别、年龄、身份证号、现住址、诊断、诊断时间、就诊科室、就诊机构都有值）")
    private String fullFlag ;

    /**
     * 病历完成时间
     */
    @ApiModelProperty(value = "病历完成时间")
    private Date mrCompleteTime ;

    /**
     * 病历识别id
     */
    @ApiModelProperty(value = "病历识别id")
    private String identifyId ;

    /**
     * 症候群-识别类型（1系统报告、2人工修正）
     */
    @ApiModelProperty(value = "症候群-识别类型（1系统报告、2人工修正）")
    private String identifySyndromeWay ;

    /**
     * 症候群（json，症候群编码identify_syndrome_code、症候群名称identify_syndrome_name、识别标准identify_syndrome_rule、结果依据identify_syndrome_support）
     */
    @ApiModelProperty(value = "症候群（json，症候群编码identify_syndrome_code、症候群名称identify_syndrome_name、识别标准identify_syndrome_rule、结果依据identify_syndrome_support）")
    private String identifySyndromeJson ;

    /**
     * 症候群-识别时间
     */
    @ApiModelProperty(value = "症候群-识别时间")
    private Date identifySyndromeTime ;

    /**
     * 症候群-审核时间
     */
    @ApiModelProperty(value = "症候群-审核时间")
    private Date identifySyndromeCheckTime ;

    /**
     * 传染病-识别次数
     */
    @ApiModelProperty(value = "传染病-识别次数")
    private Integer identifyInfectCnt ;

    /**
     * 报告卡信息-传染病-报告卡id
     */
    @ApiModelProperty(value = "报告卡信息-传染病-报告卡id")
    private String repInfectCardId ;

    /**
     * 报告卡信息-传染病-病种编码
     */
    @ApiModelProperty(value = "报告卡信息-传染病-病种编码")
    private String repInfectCode ;

    /**
     * 报告卡信息-传染病-病种名称
     */
    @ApiModelProperty(value = "报告卡信息-传染病-病种名称")
    private String repInfectName ;

    /**
     * 报告卡信息-传染病-报告时间
     */
    @ApiModelProperty(value = "报告卡信息-传染病-报告时间")
    private Date repInfectTime ;

    /**
     * 报告卡信息-中毒-报告卡id
     */
    @ApiModelProperty(value = "报告卡信息-中毒-报告卡id")
    private String repPoisonCardId ;

    /**
     * 报告卡信息-中毒-病种编码
     */
    @ApiModelProperty(value = "报告卡信息-中毒-病种编码")
    private String repPoisonCode ;

    /**
     * 报告卡信息-中毒-病种名称
     */
    @ApiModelProperty(value = "报告卡信息-中毒-病种名称")
    private String repPoisonName ;

    /**
     * 报告卡信息-中毒-报告时间
     */
    @ApiModelProperty(value = "报告卡信息-中毒-报告时间")
    private Date repPoisonTime ;

    /**
     * 报告卡信息-不明原因-报告卡id
     */
    @ApiModelProperty(value = "报告卡信息-不明原因-报告卡id")
    private String repUnknowCardId ;

    /**
     * 报告卡信息-不明原因-病种编码
     */
    @ApiModelProperty(value = "报告卡信息-不明原因-病种编码")
    private String repUnknowCode ;

    /**
     * 报告卡信息-不明原因-病种名称
     */
    @ApiModelProperty(value = "报告卡信息-不明原因-病种名称")
    private String repUnknowName ;

    /**
     * 报告卡信息-不明原因-报告时间
     */
    @ApiModelProperty(value = "报告卡信息-不明原因-报告时间")
    private Date repUnknowTime ;

    /**
     * 报告卡信息-食源性-报告卡id
     */
    @ApiModelProperty(value = "报告卡信息-食源性-报告卡id")
    private String repFoodCardId ;

    /**
     * 报告卡信息-食源性-病种编码
     */
    @ApiModelProperty(value = "报告卡信息-食源性-病种编码")
    private String repFoodCode ;

    /**
     * 报告卡信息-食源性-病种名称
     */
    @ApiModelProperty(value = "报告卡信息-食源性-病种名称")
    private String repFoodName ;

    /**
     * 报告卡信息-食源性-报告时间
     */
    @ApiModelProperty(value = "报告卡信息-食源性-报告时间")
    private Date repFoodTime ;

    /**
     * 报告卡信息-死亡-报告卡id
     */
    @ApiModelProperty(value = "报告卡信息-死亡-报告卡id")
    private String repDeadCardId ;

    /**
     * 报告卡信息-死亡-病种编码
     */
    @ApiModelProperty(value = "报告卡信息-死亡-病种编码")
    private String repDeadCode ;

    /**
     * 报告卡信息-死亡-病种名称
     */
    @ApiModelProperty(value = "报告卡信息-死亡-病种名称")
    private String repDeadName ;

    /**
     * 报告卡信息-死亡-报告时间
     */
    @ApiModelProperty(value = "报告卡信息-死亡-报告时间")
    private Date repDeadTime ;

    /**
     * 中医-舌象
     */
    @ApiModelProperty(value = "中医-舌象")
    private String tcmTongueManifestation ;

    /**
     * 中医-脉象
     */
    @ApiModelProperty(value = "中医-脉象")
    private String tcmPulseCondition ;

    /**
     * 中医-四诊结果
     */
    @ApiModelProperty(value = "中医-四诊结果")
    private String tcmFourDiag ;

    /**
     * 中医-证候代码
     */
    @ApiModelProperty(value = "中医-证候代码")
    private String tcmSymptomCode ;

    /**
     * 中医-证候
     */
    @ApiModelProperty(value = "中医-证候")
    private String tcmSymptom ;

    /**
     * 中医-病名代码
     */
    @ApiModelProperty(value = "中医-病名代码")
    private String tcmDiseaseCode ;

    /**
     * 中医-病名
     */
    @ApiModelProperty(value = "中医-病名")
    private String tcmDisease ;

    /**
     * 中医-辩证依据
     */
    @ApiModelProperty(value = "中医-辩证依据")
    private String tcmTreatSupport ;

    /**
     * 中医-治则治法
     */
    @ApiModelProperty(value = "中医-治则治法")
    private String tcmTreat ;

    /**
     * 主诊断时间
     */
    @ApiModelProperty(value = "主诊断时间")
    private Date mainDiagTime ;

    /**
     * 症状解析-临床症状（json，顺位代码sort、标签tag、解析的此类标签对应的值或描述tag_value、解析的值的发生时间tag_value_time、数据源（就诊类型名称-业务表简称-业务类简称【-字段简称】，如住院-入院记录-体格检查-体温）data_source）
     */
    @ApiModelProperty(value = "症状解析-临床症状（json，顺位代码sort、标签tag、解析的此类标签对应的值或描述tag_value、解析的值的发生时间tag_value_time、数据源（就诊类型名称-业务表简称-业务类简称【-字段简称】，如住院-入院记录-体格检查-体温）data_source）")
    private String symptomExtractMedJson ;

    /**
     * 症状解析-检查症状（json，检查项目名称exam_name、顺位代码sort、标签tag、解析的此类标签对应的值或描述tag_value、症状发生时间tag_value_time、数据源（就诊类型名称-业务表简称-业务类简称【-字段简称】，如住院-入院记录-体格检查-体温）data_source）
     */
    @ApiModelProperty(value = "症状解析-检查症状（json，检查项目名称exam_name、顺位代码sort、标签tag、解析的此类标签对应的值或描述tag_value、症状发生时间tag_value_time、数据源（就诊类型名称-业务表简称-业务类简称【-字段简称】，如住院-入院记录-体格检查-体温）data_source）")
    private String symptomExtractRisJson ;

    /**
     * 症状解析-检验症状（json，检验项目名称test_name、顺位代码sort、标签tag、解析的此类标签对应的值或描述tag_value、症状发生时间tag_value_time、数据源（就诊类型名称-业务表简称-业务类简称【-字段简称】，如住院-入院记录-体格检查-体温）data_source）
     */
    @ApiModelProperty(value = "症状解析-检验症状（json，检验项目名称test_name、顺位代码sort、标签tag、解析的此类标签对应的值或描述tag_value、症状发生时间tag_value_time、数据源（就诊类型名称-业务表简称-业务类简称【-字段简称】，如住院-入院记录-体格检查-体温）data_source）")
    private String symptomExtractLisJson ;

    /**
     * 传染病-识别结果（json，识别类型（1系统报告、2人工修正）identify_infect_way、传染病编码identify_infect_code、传染病名称identify_infect_name、传染病诊断类别（疑似病例、临床诊断病例、确诊病例、病原携带者等）identify_infect_class、识别标准identify_infect_rule、结果依据identify_infect_support、传染病分类（甲类、乙类、丙类、其它法定、其它非法定）identify_infect_type、传染病传播途径分类（肠道传染病、呼吸道症候群、自然疫源及虫煤传染病、血源及性传播传染病）identify_infect_transmit_type、单病例上报标识（1是0否）identify_infect_single_flag、识别时间identify_infect_time、报告/识别及时性标识（0未执行、1及时、2延时，报告时间 - 诊断时间 根据传染病维表的时效性要求判断）identify_infect_intime_flag、审核时间check_infect_time、审核及时性标识（0未执行、1及时、2延时，审核时间 - 报告时间 根据传染病维表的时效性要求判断）check_infect_intime_flag）
     */
    @ApiModelProperty(value = "传染病-识别结果（json，识别类型（1系统报告、2人工修正）identify_infect_way、传染病编码identify_infect_code、传染病名称identify_infect_name、传染病诊断类别（疑似病例、临床诊断病例、确诊病例、病原携带者等）identify_infect_class、识别标准identify_infect_rule、结果依据identify_infect_support、传染病分类（甲类、乙类、丙类、其它法定、其它非法定）identify_infect_type、传染病传播途径分类（肠道传染病、呼吸道症候群、自然疫源及虫煤传染病、血源及性传播传染病）identify_infect_transmit_type、单病例上报标识（1是0否）identify_infect_single_flag、识别时间identify_infect_time、报告/识别及时性标识（0未执行、1及时、2延时，报告时间 - 诊断时间 根据传染病维表的时效性要求判断）identify_infect_intime_flag、审核时间check_infect_time、审核及时性标识（0未执行、1及时、2延时，审核时间 - 报告时间 根据传染病维表的时效性要求判断）check_infect_intime_flag）")
    private String identifyInfectJson ;

    /**
     * 传染病识别标识（0未识别，1完成识别，2识别中）
     */
    @ApiModelProperty(value = "传染病识别标识（0未识别，1完成识别，2识别中）")
    private String identifyInfectFlag ;

}
