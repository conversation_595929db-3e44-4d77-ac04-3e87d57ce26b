package com.iflytek.cdc.province.entity.bu;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tb_cdcew_report_qcs_upload_info", schema = "app")
@ApiModel(value = "ReportQcsUploadInfo对象", description = "")
public class ReportQcsUploadInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "创建者id")
    private String creatorId;

    @ApiModelProperty(value = "创建者")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改者id")
    private String updaterId;

    @ApiModelProperty(value = "更新者")
    private String updater;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "删除标志，'1' 表示已删除，'0' 表示未删除")
    private String deleteFlag;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "主记录ID（外键）")
    private String mainRecordId;

    @ApiModelProperty(value = "报告卡ID")
    private String reportCardId;

    @ApiModelProperty(value = "卡片编号")
    private String reportCardCode;

    @ApiModelProperty(value = "卡片状态")
    private String reportCardStatus;

    @ApiModelProperty(value = "报告类别")
    private String reportTypeName;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "患儿家长姓名")
    private String parentName;

    @ApiModelProperty(value = "有效证件类型")
    private String identityTypeName;

    @ApiModelProperty(value = "有效证件号码")
    private String identityNo;

    @ApiModelProperty(value = "患者性别")
    private String sexName;

    @ApiModelProperty(value = "出生日期")
    private Date birthDate;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "年龄单位")
    private String ageUnit;

    @ApiModelProperty(value = "患者工作单位")
    private String company;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "病人属于")
    private String belongName;

    @ApiModelProperty(value = "现住地址国标")
    private String livingAddrAreaCode;

    @ApiModelProperty(value = "现住详细地址")
    private String livingAddrDetail;

    @ApiModelProperty(value = "人群分类")
    private String personTypeName;

    @ApiModelProperty(value = "病例分类")
    private String caseName1;

    @ApiModelProperty(value = "病例分类2")
    private String caseName2;

    @ApiModelProperty(value = "发病日期")
    private Date onsetTime;

    @ApiModelProperty(value = "诊断时间")
    private Date diagTime;

    @ApiModelProperty(value = "死亡日期")
    private Date deathDate;

    @ApiModelProperty(value = "疾病名称")
    private String diseaseName;

    @ApiModelProperty(value = "订正前病种")
    private String unrevisedDiseaseName;

    @ApiModelProperty(value = "订正前诊断时间")
    private Date unrevisedIdentifyTime;

    @ApiModelProperty(value = "订正前终审时间")
    private Date unrevisedFinalCheckTime;

    @ApiModelProperty(value = "填卡医生")
    private String reportDoctorName;

    @ApiModelProperty(value = "医生填卡日期")
    private Date reportFillDate;

    @ApiModelProperty(value = "报告单位地区编码")
    private String reportOrgAddrAreaCode;

    @ApiModelProperty(value = "报告单位")
    private String reportOrgName;

    @ApiModelProperty(value = "单位类型")
    private String reportOrgTypeCode;

    @ApiModelProperty(value = "报告卡录入时间")
    private Date uploadRecordTime;

    @ApiModelProperty(value = "录卡用户")
    private String uploadDoctorName;

    @ApiModelProperty(value = "录卡用户所属单位")
    private String uploadOrgName;

    @ApiModelProperty(value = "县区审核时间")
    private Date districtCheckTime;

    @ApiModelProperty(value = "地市审核时间")
    private Date cityCheckTime;

    @ApiModelProperty(value = "省市审核时间")
    private Date provinceCheckTime;

    @ApiModelProperty(value = "审核状态")
    private String checkStatus;

    @ApiModelProperty(value = "订正报告时间")
    private Date revisedIdentifyTime;

    @ApiModelProperty(value = "订正终审时间")
    private Date revisedFinalCheckTime;

    @ApiModelProperty(value = "终审死亡时间")
    private Date revisedDeathTime;

    @ApiModelProperty(value = "订正用户")
    private String reviseDoctorName;

    @ApiModelProperty(value = "订正用户所属单位")
    private String reviseOrgName;

    @ApiModelProperty(value = "删除时间")
    private Date deleteOpTime;

    @ApiModelProperty(value = "删除用户")
    private String deleteDoctorName;

    @ApiModelProperty(value = "删除用户所属单位")
    private String deleteOrgName;

    @ApiModelProperty(value = "删除原因")
    private String deleteReason;

    @ApiModelProperty(value = "报告卡备注")
    private String reportCardNote;

    @ApiModelProperty(value = "加工字段：主索引标识")
    private String empiId;

    @ApiModelProperty(value = "加工字段：现住地址-省编码")
    private String livingAddrProvinceCode;

    @ApiModelProperty(value = "加工字段：现住地址-省名称")
    private String livingAddrProvince;

    @ApiModelProperty(value = "加工字段：现住地址-市编码")
    private String livingAddrCityCode;

    @ApiModelProperty(value = "加工字段：现住地址-市名称")
    private String livingAddrCity;

    @ApiModelProperty(value = "加工字段：现住地址-区编码")
    private String livingAddrDistrictCode;

    @ApiModelProperty(value = "加工字段：现住地址-区名称")
    private String livingAddrDistrict;

    @ApiModelProperty(value = "加工字段：现住地址-功能区编码")
    private String livingAddrFuncDistrictCode;

    @ApiModelProperty(value = "加工字段：现住地址-功能区名称")
    private String livingAddrFuncDistrict;

    @ApiModelProperty(value = "加工字段：现住地址-街道编码")
    private String livingAddrStreetCode;

    @ApiModelProperty(value = "加工字段：现住地址-街道名称")
    private String livingAddrStreet;

    @ApiModelProperty(value = "加工字段：报告单位地址-省编码")
    private String reportOrgAddrProvinceCode;

    @ApiModelProperty(value = "加工字段：报告单位地址-省名称")
    private String reportOrgAddrProvince;

    @ApiModelProperty(value = "加工字段：报告单位地址-市编码")
    private String reportOrgAddrCityCode;

    @ApiModelProperty(value = "加工字段：报告单位地址-市名称")
    private String reportOrgAddrCity;

    @ApiModelProperty(value = "加工字段：报告单位地址-区编码")
    private String reportOrgAddrDistrictCode;

    @ApiModelProperty(value = "加工字段：报告单位地址-区名称")
    private String reportOrgAddrDistrict;

    @ApiModelProperty(value = "加工字段：报告单位地址-功能区编码")
    private String reportOrgAddrFuncDistrictCode;

    @ApiModelProperty(value = "加工字段：报告单位地址-功能区名称")
    private String reportOrgAddrFuncDistrict;

    @ApiModelProperty(value = "加工字段：报告单位地址-街道编码")
    private String reportOrgAddrStreetCode;

    @ApiModelProperty(value = "加工字段：报告单位地址-街道名称")
    private String reportOrgAddrStreet;

    @ApiModelProperty(value = "加工字段：年龄分组")
    private String ageGroup;

    @ApiModelProperty(value = "加工字段：死亡标识，1 表示是，0 表示否")
    private Integer deathFlag;

    @ApiModelProperty(value = "加工字段：延迟标识，1 表示是，0 表示否")
    private Integer delayFlag;

    @ApiModelProperty(value = "加工字段：疾病编码（根据名称获取）")
    private String diseaseCode;

    @ApiModelProperty(value = "加工字段：传染病分类")
    private String infectType;


}
