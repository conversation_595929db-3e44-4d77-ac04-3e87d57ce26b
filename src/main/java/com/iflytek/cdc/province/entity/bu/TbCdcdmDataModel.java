package com.iflytek.cdc.province.entity.bu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 数据模型表
 */
@ApiModel(description="数据模型表")
@Data
public class TbCdcdmDataModel implements Serializable {
    /**
    * 数据模型ID
    */
    @ApiModelProperty(value="数据模型ID")
    private String modelId;

    /**
    * 数据模型名称
    */
    @ApiModelProperty(value="数据模型名称")
    private String modelName;

    /**
    * 数据模型分类
    */
    @ApiModelProperty(value="数据模型分类")
    private String modelType;

    /**
    * 是否是系统内置 1-是，0-否，默认0
    */
    @ApiModelProperty(value="是否是系统内置 1-是，0-否，默认0")
    private Integer isBuiltIn;

    /**
    * 备注
    */
    @ApiModelProperty(value="备注")
    private String note;

    private static final long serialVersionUID = 1L;
}