package com.iflytek.cdc.province.entity.ads;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 诊疗(medical service)-患者生命周期信息表;
 * <AUTHOR> dingyuan
 * @date : 2024-9-12
 */
@ApiModel(value = "诊疗(medical service)-患者生命周期信息表")
@Data
public class AdsMsLifeInfo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 患者生命周期id
     */
    @ApiModelProperty(value = "患者生命周期id")
    private String lifeId ;

    /**
     * ETL创建时间
     */
    @ApiModelProperty(value = "ETL创建时间")
    private Date etlCreateDatetime ;

    /**
     * ETL更新时间
     */
    @ApiModelProperty(value = "ETL更新时间")
    private Date etlUpdateDatetime ;

    /**
     * 患者（不变）信息-姓名
     */
    @ApiModelProperty(value = "患者（不变）信息-姓名")
    private String patientName ;

    /**
     * 患者（不变）信息-性别（有字典，313001）
     */
    @ApiModelProperty(value = "患者（不变）信息-性别（有字典，313001）")
    private String patientSexName ;

    /**
     * 患者（不变）信息-身份证号
     */
    @ApiModelProperty(value = "患者（不变）信息-身份证号")
    private String patientIdentityNo ;

    /**
     * 患者（不变）信息-出生日期
     */
    @ApiModelProperty(value = "患者（不变）信息-出生日期")
    private Date patientBirthDay ;

    /**
     * 最新-就诊（vist）-病历id
     */
    @ApiModelProperty(value = "最新-就诊（vist）-病历id")
    private String lvMedicalId ;

    /**
     * 最新-就诊（vist）-就诊事件id
     */
    @ApiModelProperty(value = "最新-就诊（vist）-就诊事件id")
    private String lvEventId ;

    /**
     * 最新-病程（process）-病程id
     */
    @ApiModelProperty(value = "最新-病程（process）-病程id")
    private String lpProcessId ;

    /**
     * 最新-就诊（vist）-患者（可变）信息-人员id
     */
    @ApiModelProperty(value = "最新-就诊（vist）-患者（可变）信息-人员id")
    private String lvPatientId ;

    /**
     * 最新-就诊（vist）-患者（可变）信息-患者年龄（单位岁）
     */
    @ApiModelProperty(value = "最新-就诊（vist）-患者（可变）信息-患者年龄（单位岁）")
    private Double lvPatientAge ;

    /**
     * 最新-就诊（vist）-患者（可变）信息-患者联系方式
     */
    @ApiModelProperty(value = "最新-就诊（vist）-患者（可变）信息-患者联系方式")
    private String lvPatientPhone ;

    /**
     * 最新-就诊（vist）-患者（可变）信息-现住详细地址
     */
    @ApiModelProperty(value = "最新-就诊（vist）-患者（可变）信息-现住详细地址")
    private String lvLivingAddrDetail ;

    /**
     * 最新-就诊（vist）-患者（可变）信息-现住址标化地址
     */
    @ApiModelProperty(value = "最新-就诊（vist）-患者（可变）信息-现住址标化地址")
    private String lvLivingAddrDetailStd ;

    /**
     * 最新-就诊（vist）-患者（可变）信息-现住址-城乡类型（城市、乡村、未知）
     */
    @ApiModelProperty(value = "最新-就诊（vist）-患者（可变）信息-现住址-城乡类型（城市、乡村、未知）")
    private String lvLivingAddrType ;

    /**
     * 最新-就诊（vist）-患者（可变）信息-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）
     */
    @ApiModelProperty(value = "最新-就诊（vist）-患者（可变）信息-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）")
    private String lvPersonType ;

    /**
     * 最新-就诊（vist）-患者（可变）信息-职业
     */
    @ApiModelProperty(value = "最新-就诊（vist）-患者（可变）信息-职业")
    private String lvJob ;

    /**
     * 最新-就诊（vist）-患者（可变）信息-职业风险
     */
    @ApiModelProperty(value = "最新-就诊（vist）-患者（可变）信息-职业风险")
    private String lvJobRisk ;

    /**
     * 最新-就诊（vist）-患者（可变）信息-工作单位/学校名称
     */
    @ApiModelProperty(value = "最新-就诊（vist）-患者（可变）信息-工作单位/学校名称")
    private String lvCompany ;

    /**
     * 最新-就诊（vist）-患者（可变）信息-单位/学校标化地址
     */
    @ApiModelProperty(value = "最新-就诊（vist）-患者（可变）信息-单位/学校标化地址")
    private String lvCompanyAddrDetailStd ;

    /**
     * 最新-就诊（vist）-患者（可变）信息-单位-城乡类型（城市、乡村、未知）
     */
    @ApiModelProperty(value = "最新-就诊（vist）-患者（可变）信息-单位-城乡类型（城市、乡村、未知）")
    private String lvCompanyAddrType ;

    /**
     * 最新-就诊（vist）-就诊时间
     */
    @ApiModelProperty(value = "最新-就诊（vist）-就诊时间")
    private Date lvVisitTime ;

    /**
     * 最新-病程（process）-开始时间
     */
    @ApiModelProperty(value = "最新-病程（process）-开始时间")
    private Date lpStartTime ;

    /**
     * 最新-病程（process）-结束时间
     */
    @ApiModelProperty(value = "最新-病程（process）-结束时间")
    private Date lpEndTime ;

    /**
     * 最新-病程（process）-死亡时间
     */
    @ApiModelProperty(value = "最新-病程（process）-死亡时间")
    private Date lpDeadTime ;

    /**
     * 最新-病程（process）-死亡原因（详细的，因该病死亡，其他）
     */
    @ApiModelProperty(value = "最新-病程（process）-死亡原因（详细的，因该病死亡，其他）")
    private String lpDeadReason ;

    /**
     * 最新-病程（process）-就诊事件数
     */
    @ApiModelProperty(value = "最新-病程（process）-就诊事件数")
    private Integer lpVisitEventCnt ;

    /**
     * 最新-病程（process）-就诊医疗机构
     */
    @ApiModelProperty(value = "最新-病程（process）-就诊医疗机构")
    private String lpVisitOrg ;

    /**
     * 最新-病程（process）-现发疾病名称
     */
    @ApiModelProperty(value = "最新-病程（process）-现发疾病名称")
    private String lpDisease ;

    /**
     * 最新-病程（process）-慢性病标识
     */
    @ApiModelProperty(value = "最新-病程（process）-慢性病标识")
    private String lpChronicFlag ;

    /**
     * 最新-病程（process）-传染病标识
     */
    @ApiModelProperty(value = "最新-病程（process）-传染病标识")
    private String lpInfectFlag ;

    /**
     * 最新-病程（process）-病程结束标识
     */
    @ApiModelProperty(value = "最新-病程（process）-病程结束标识")
    private String lpEndFlag ;

    /**
     * 累计就诊事件数
     */
    @ApiModelProperty(value = "累计就诊事件数")
    private Integer visitEventCnt ;

    /**
     * 累计就诊医疗机构（按时间顺序用|拼接，不去重，总数量需和就诊次数一致）
     */
    @ApiModelProperty(value = "累计就诊医疗机构（按时间顺序用|拼接，不去重，总数量需和就诊次数一致）")
    private String visitOrg ;

    /**
     * 累计病程数
     */
    @ApiModelProperty(value = "累计病程数")
    private Integer processCnt ;

    /**
     * 累计疾病名称（按时间顺序用|拼接，不去重，总数量需和病程数一致）
     */
    @ApiModelProperty(value = "累计疾病名称（按时间顺序用|拼接，不去重，总数量需和病程数一致）")
    private String disease ;

    /**
     * 已发慢病史
     */
    @ApiModelProperty(value = "已发慢病史")
    private String diseaseChronic ;

    /**
     * 已发传染病（|隔开）
     */
    @ApiModelProperty(value = "已发传染病（|隔开）")
    private String diseaseInfect ;

    /**
     * 患者（不变）信息-护照号（可能会变）
     */
    @ApiModelProperty(value = "患者（不变）信息-护照号（可能会变）")
    private String patientPassportNo ;

    /**
     * 患者（不变）信息-军官证号
     */
    @ApiModelProperty(value = "患者（不变）信息-军官证号")
    private String patientMilitaryNo ;

    /**
     * 患者（不变）信息-港澳来陆通行证
     */
    @ApiModelProperty(value = "患者（不变）信息-港澳来陆通行证")
    private String patientPassportNoHm ;

    /**
     * 患者（不变）信息-台湾来陆通行证
     */
    @ApiModelProperty(value = "患者（不变）信息-台湾来陆通行证")
    private String patientPassportNoT ;

    /**
     * 患者（不变）信息-其它证件类型
     */
    @ApiModelProperty(value = "患者（不变）信息-其它证件类型")
    private String patientOtherIdentityType ;

    /**
     * 患者（不变）信息-其它证件号码
     */
    @ApiModelProperty(value = "患者（不变）信息-其它证件号码")
    private String patientOtherIdentityNo ;

}
