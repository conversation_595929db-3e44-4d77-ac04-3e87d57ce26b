package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 初步诊断信息
 */
@Data
@TableName(value = "ads.ads_edr_init_diag_info")
public class AdsEdrInitDiagInfo {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 采集创建时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * 采集更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;

    /**
     * 事件信息ID
     */
    @TableField(value = "event_id")
    private String eventId;

    /**
     * 主索引ID
     */
    @TableField(value = "empi_id")
    private String empiId;

    /**
     * 诊断机构代码
     */
    @TableField(value = "org_code")
    private String orgCode;

    /**
     * 医疗机构(机构主索引)-就诊机构代码
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 诊断机构
     */
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 初步诊断代码
     */
    @TableField(value = "initial_diagnosis_code")
    private String initialDiagnosisCode;

    /**
     * 初步诊断名称
     */
    @TableField(value = "initial_diagnosis")
    private String initialDiagnosis;

    /**
     * 就诊日期时间
     */
    @TableField(value = "serial_number")
    private Date serialNumber;

    /**
     * 现住地址代码
     */
    @TableField(value = "current_addr_code")
    private String currentAddrCode;

    /**
     * 现住地区名称
     */
    @TableField(value = "current_addr")
    private String currentAddr;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 主索引ID
     */
    @TableField(value = "mpi_id")
    private String mpiId;

    /**
     * 创建机构
     */
    @TableField(value = "create_org")
    private String createOrg;

    /**
     * 创建机构
     */
    @TableField(value = "create_org_name")
    private String createOrgName;

    /**
     * 修改机构
     */
    @TableField(value = "update_org")
    private String updateOrg;

    /**
     * 修改机构
     */
    @TableField(value = "update_org_name")
    private String updateOrgName;
}