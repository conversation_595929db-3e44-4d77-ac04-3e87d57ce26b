package com.iflytek.cdc.province.entity.bu;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 症候群核实记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tb_cdcew_syndrome_verify_record", schema = "app")
@ApiModel(value = "SyndromeVerifyRecord对象")
public class SyndromeVerifyRecord implements Serializable {

    public static final String TB_NAME = "tb_cdcew_syndrome_verify_record";
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "删除标识: 0-未删除,1-已删除")
    private String deleteFlag;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人id")
    private String creatorId;

    @ApiModelProperty(value = "创建人姓名")
    private String creator;
} 