package com.iflytek.cdc.province.entity.ads;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


@Data
@ApiModel("疫情事件报告信息")
public class DyqwEventReport {

    @ApiModelProperty("报告ID")
    private String reportId;

    @ApiModelProperty("事件ID")
    private String eventId;

    @ApiModelProperty("事件名称")
    private String eventName;

    @ApiModelProperty("事件类别")
    private String typeId;

    @ApiModelProperty("事件类别名称")
    private String typeName;

    @ApiModelProperty("事件严重等级")
    private String eventSeverityLevelCode;

    @ApiModelProperty("事件严重等级描述")
    private String eventSeverityLevel;

    @ApiModelProperty("报告地区编码")
    private String reportDistrictCode;

    @ApiModelProperty("报告地区")
    private String reportDistrict;

    @ApiModelProperty("报告单位编码")
    private String reportUnitCode;

    @ApiModelProperty("报告单位")
    private String reportUnit;

    @ApiModelProperty("报告发生地区编码")
    private String reportHappenDistrictCode;

    @ApiModelProperty("报告发生地区")
    private String reportHappenDistrict;

    @ApiModelProperty("报告详细地址")
    private String reportAddress;

    @ApiModelProperty("学校类别")
    private String schoolTypeCode;

    @ApiModelProperty("波及或暴露人口数")
    private Integer exposedPopulation;

    @ApiModelProperty("发病数")
    private Integer diseaseNumber;

    @ApiModelProperty("死亡数")
    private Integer deadNumber;

    @ApiModelProperty("接到报告时间")
    private Date receiveTime;

    @ApiModelProperty("报告创建时间")
    private Date createTime;

    @ApiModelProperty("报告人")
    private Integer userId;

    @ApiModelProperty("最后修改时间")
    private Date latestModifyTime;

    @ApiModelProperty("是否是结案报告")
    private String ifFinalReport;

    @ApiModelProperty("首例病人发病时间")
    private Date firstPatientTime;

    @ApiModelProperty("末例病人发病时间")
    private Date lastPatientTime;

    @ApiModelProperty("事件发生时间")
    private Date eventHappenTime;

    @ApiModelProperty("国家审核时间")
    private Date nationReviewTime;

    @ApiModelProperty("省级审核时间")
    private Date provReviewTime;

    @ApiModelProperty("市级审核时间")
    private Date cityReviewTime;

    @ApiModelProperty("县级审核时间")
    private Date countyReviewTime;

    @ApiModelProperty("审核级别")
    private String reviewLevelCode;

    @ApiModelProperty("审核级别描述")
    private String reviewLevel;

    @ApiModelProperty("报告人/单位")
    private String reportPerson;

    @ApiModelProperty("联系电话")
    private String telephone;

    @ApiModelProperty("事件波及的地域或范围")
    private String eventScope;

    @ApiModelProperty("直接经济损失")
    private Integer directLost;

    @ApiModelProperty("间接经济损失")
    private Integer indirectLost;

    @ApiModelProperty("合并事件报告对应的原始报告ID")
    private String relateRepoerId;

    @ApiModelProperty("初步核实认定时间")
    private Date firstCheckTime;

    // 卫生行政管理
    @ApiModelProperty("卫生行政管理国家级")
    private Integer pubMN;
    @ApiModelProperty("卫生行政管理省级")
    private Integer pubMP;
    @ApiModelProperty("卫生行政管理地市级")
    private Integer pubMC;
    @ApiModelProperty("卫生行政管理县级")
    private Integer pubMCounty;

    // 医疗救治
    @ApiModelProperty("医疗救治国家级")
    private Integer medTN;
    @ApiModelProperty("医疗救治省级")
    private Integer medTP;
    @ApiModelProperty("医疗救治地市级")
    private Integer medTCity;
    @ApiModelProperty("医疗救治县级")
    private Integer medTC;

    // 疾病预防控制
    @ApiModelProperty("疾病预防控制国家级")
    private Integer disPCN;
    @ApiModelProperty("疾病预防控制省级")
    private Integer disPCP;
    @ApiModelProperty("疾病预防控制地市级")
    private Integer disPCCity;
    @ApiModelProperty("疾病预防控制县级")
    private Integer disPCC;

    // 卫生监督执法
    @ApiModelProperty("卫生监督执法国家级")
    private Integer heaSLN;
    @ApiModelProperty("卫生监督执法省级")
    private Integer heaSLP;
    @ApiModelProperty("卫生监督执法地市级")
    private Integer heaSLCity;
    @ApiModelProperty("卫生监督执法县级")
    private Integer heaSLC;

    // 综合保障
    @ApiModelProperty("综合保障国家级")
    private Integer comSN;
    @ApiModelProperty("综合保障省级")
    private Integer comSP;
    @ApiModelProperty("综合保障地市级")
    private Integer comSCity;
    @ApiModelProperty("综合保障县级")
    private Integer comSC;

    // 其他
    @ApiModelProperty("其他国家级")
    private Integer otherNation;
    @ApiModelProperty("其他省级")
    private Integer otherProvince;
    @ApiModelProperty("其他地市级")
    private Integer otherCity;
    @ApiModelProperty("其他县级")
    private Integer otherContry;

    @ApiModelProperty("事件信息来源")
    private String eventInformationSource;

    @ApiModelProperty("事件发生场所")
    private String eventHappenPlace;

    // 传染病相关
    @ApiModelProperty("事件发生原因(传染病)")
    private String eventHappenReason1;
    @ApiModelProperty("病人处理过程(传染病)")
    private String patientTreatmentProcess1;
    @ApiModelProperty("事件控制措施(传染病)")
    private String incidentControlMeasures;
    @ApiModelProperty("毒物名称")
    private String poisonName;
    @ApiModelProperty("责任单位")
    private String responsibilityUnit;
    @ApiModelProperty("现场开展工作")
    private String sceneBeginWork;

    // 环境因素事件
    @ApiModelProperty("致病因素(环境因素事件)")
    private String pathogenicFactors2;
    @ApiModelProperty("事件发生原因(环境因素事件)")
    private String eventHappenReason2;
    @ApiModelProperty("引发事件污染物(环境因素事件)")
    private String pollutantsTriggerEvents2;
    @ApiModelProperty("被污染环境")
    private String pollutedEnvironment;
    @ApiModelProperty("病人处理过程(环境因素事件)")
    private String patientTreatmentProcess2;
    @ApiModelProperty("事件控制措施(环境因素事件)")
    private String incidentControlMeasures2;

    // 预防接种、服药事件
    @ApiModelProperty("致病因素(预防接种、服药事件)")
    private String pathogenicFactors3;
    @ApiModelProperty("事件发生原因(预防接种、服药事件)")
    private String eventHappenReason3;
    @ApiModelProperty("引发事件污染物(预防接种、服药事件)")
    private String pollutantsTriggerEvents3;
    @ApiModelProperty("病人处理过程(预防接种、服药事件)")
    private String patientTreatmentProcess3;
    @ApiModelProperty("事件控制措施(预防接种、服药事件)")
    private String incidentControlMeasures3;
    @ApiModelProperty("引发事件农药")
    private String triggerEventsPesticide;

    // 农药中毒
    @ApiModelProperty("致病因素(农药中毒)")
    private String pathogenicFactors4;
    @ApiModelProperty("事件发生原因(农药中毒)")
    private String eventHappenReason4;
    @ApiModelProperty("病人处理过程(农药中毒)")
    private String patientTreatmentProcess4;
    @ApiModelProperty("事件控制措施(农药中毒)")
    private String incidentControlMeasures4;

    @ApiModelProperty("学校类别名称")
    private String schoolTypeName;

    @ApiModelProperty("关联附件信息")
    private List<DyqwEventAttachment> attachmentList;
}