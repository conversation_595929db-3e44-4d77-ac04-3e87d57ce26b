package com.iflytek.cdc.province.entity.ads;

import com.iflytek.cdc.edr.annotation.ReportCardField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 诊疗(medical service)-病程信息表;
 * <AUTHOR> dingyuan
 * @date : 2024-4-24
 */
@ApiModel(value = "诊疗(medical service)-病程信息表")
@Data
public class AdsMsProcessInfo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 病程id(uuid)同首次的病历识别id，后续其它数据更新变动此id不会变动
     */
    @ApiModelProperty( "病程id(uuid)同首次的病历识别id，后续其它数据更新变动此id不会变动")
    @ReportCardField(label = "病程id(uuid)同首次的病历识别id，后续其它数据更新变动此id不会变动", group = "")
    private String processId ;

    /**
     * ETL创建时间
     */
    @ApiModelProperty( "ETL创建时间")
    @ReportCardField(label = "ETL创建时间", group = "", dateFormat = "yyyy-MM-dd HH:mm")
    private Date etlCreateDatetime ;

    /**
     * ETL更新时间
     */
    @ApiModelProperty( "ETL更新时间")
    @ReportCardField(label = "ETL更新时间", group = "", dateFormat = "yyyy-MM-dd HH:mm")
    private Date etlUpdateDatetime ;

    /**
     * 姓名
     */
    @ApiModelProperty( "姓名")
    @ReportCardField(label = "姓名", group = "")
    private String patientName ;

    /**
     * 性别（有字典，313001）
     */
    @ApiModelProperty( "性别（有字典，313001）")
    @ReportCardField(label = "性别（有字典，313001）", group = "")
    private String patientSexName ;

    /**
     * 身份证号
     */
    @ApiModelProperty( "身份证号")
    @ReportCardField(label = "身份证号", group = "")
    private String patientIdentityNo ;

    /**
     * 出生日期
     */
    @ApiModelProperty( "出生日期")
    @ReportCardField(label = "出生日期", group = "", dateFormat = "yyyy-MM-dd HH:mm")
    private Date patientBirthDay ;

    /**
     * 首次-就诊（vist）-就诊的病历识别id
     */
    @ApiModelProperty( "首次-就诊（vist）-就诊的病历识别id")
    @ReportCardField(label = "首次-就诊（vist）-就诊的病历识别id", group = "")
    private String fvIdentifyId ;

    /**
     * 首次-就诊（vist）-人员id
     */
    @ApiModelProperty( "首次-就诊（vist）-人员id")
    @ReportCardField(label = "首次-就诊（vist）-人员id", group = "")
    private String fvPatientId ;

    /**
     * 首次-就诊（vist）-患者年龄（单位岁）
     */
    @ApiModelProperty( "首次-就诊（vist）-患者年龄（单位岁）")
    @ReportCardField(label = "首次-就诊（vist）-患者年龄（单位岁）", group = "")
    private Double fvPatientAge ;

    /**
     * 首次-就诊（vist）-患者联系方式
     */
    @ApiModelProperty( "首次-就诊（vist）-患者联系方式")
    @ReportCardField(label = "首次-就诊（vist）-患者联系方式", group = "")
    private String fvPatientPhone ;

    /**
     * 首次-就诊（vist）-现住详细地址
     */
    @ApiModelProperty( "首次-就诊（vist）-现住详细地址")
    @ReportCardField(label = "首次-就诊（vist）-现住详细地址", group = "")
    private String fvLivingAddrDetail ;

    /**
     * 首次-就诊（vist）-现住址标化地址
     */
    @ApiModelProperty( "首次-就诊（vist）-现住址标化地址")
    @ReportCardField(label = "首次-就诊（vist）-现住址标化地址", group = "")
    private String fvLivingAddrDetailStd ;

    /**
     * 首次-就诊（vist）-现住址-城乡类型（城市、乡村、未知）
     */
    @ApiModelProperty( "首次-就诊（vist）-现住址-城乡类型（城市、乡村、未知）")
    @ReportCardField(label = "首次-就诊（vist）-现住址-城乡类型（城市、乡村、未知）", group = "")
    private String fvLivingAddrType ;

    /**
     * 首次-就诊（vist）-现住地-省编码
     */
    @ApiModelProperty( "首次-就诊（vist）-现住地-省编码")
    @ReportCardField(label = "首次-就诊（vist）-现住地-省编码", group = "")
    private String fvLivingAddrProvinceCode ;

    /**
     * 首次-就诊（vist）-现住地-省
     */
    @ApiModelProperty( "首次-就诊（vist）-现住地-省")
    @ReportCardField(label = "首次-就诊（vist）-现住地-省", group = "")
    private String fvLivingAddrProvince ;

    /**
     * 首次-就诊（vist）-现住地-市编码
     */
    @ApiModelProperty( "首次-就诊（vist）-现住地-市编码")
    @ReportCardField(label = "首次-就诊（vist）-现住地-市编码", group = "")
    private String fvLivingAddrCityCode ;

    /**
     * 首次-就诊（vist）-现住地-市
     */
    @ApiModelProperty( "首次-就诊（vist）-现住地-市")
    @ReportCardField(label = "首次-就诊（vist）-现住地-市", group = "")
    private String fvLivingAddrCity ;

    /**
     * 首次-就诊（vist）-现住地-行政区编码
     */
    @ApiModelProperty( "首次-就诊（vist）-现住地-行政区编码")
    @ReportCardField(label = "首次-就诊（vist）-现住地-行政区编码", group = "")
    private String fvLivingAddrDistrictCode ;

    /**
     * 首次-就诊（vist）-现住地-行政区
     */
    @ApiModelProperty( "首次-就诊（vist）-现住地-行政区")
    @ReportCardField(label = "首次-就诊（vist）-现住地-行政区", group = "")
    private String fvLivingAddrDistrict ;

    /**
     * 首次-就诊（vist）-现住址-功能区编码
     */
    @ApiModelProperty( "首次-就诊（vist）-现住址-功能区编码")
    @ReportCardField(label = "首次-就诊（vist）-现住址-功能区编码", group = "")
    private String fvLivingAddrFuncDistrictCode ;

    /**
     * 首次-就诊（vist）-现住址-功能区
     */
    @ApiModelProperty( "首次-就诊（vist）-现住址-功能区")
    @ReportCardField(label = "首次-就诊（vist）-现住址-功能区", group = "")
    private String fvLivingAddrFuncDistrict ;

    /**
     * 首次-就诊（vist）-现住址-街道编码
     */
    @ApiModelProperty( "首次-就诊（vist）-现住址-街道编码")
    @ReportCardField(label = "首次-就诊（vist）-现住址-街道编码", group = "")
    private String fvLivingAddrStreetCode ;

    /**
     * 首次-就诊（vist）-现住址-街道
     */
    @ApiModelProperty( "首次-就诊（vist）-现住址-街道")
    @ReportCardField(label = "首次-就诊（vist）-现住址-街道", group = "")
    private String fvLivingAddrStreet ;

    /**
     * 首次-就诊（vist）-现住址-街道中心经度
     */
    @ApiModelProperty( "首次-就诊（vist）-现住址-街道中心经度")
    @ReportCardField(label = "首次-就诊（vist）-现住址-街道中心经度", group = "")
    private Double fvLivingAddrStreetLongitude ;

    /**
     * 首次-就诊（vist）-现住址-街道中心纬度
     */
    @ApiModelProperty( "首次-就诊（vist）-现住址-街道中心纬度")
    @ReportCardField(label = "首次-就诊（vist）-现住址-街道中心纬度", group = "")
    private Double fvLivingAddrStreetLatitude ;

    /**
     * 首次-就诊（vist）-现住址-经度
     */
    @ApiModelProperty( "首次-就诊（vist）-现住址-经度")
    @ReportCardField(label = "首次-就诊（vist）-现住址-经度", group = "")
    private Double fvLivingAddrLongitude ;

    /**
     * 首次-就诊（vist）-现住址-经度
     */
    @ApiModelProperty( "首次-就诊（vist）-现住址-经度")
    @ReportCardField(label = "首次-就诊（vist）-现住址-经度", group = "")
    private Double fvLivingAddrLatitude ;

    /**
     * 首次-就诊（vist）-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）
     */
    @ApiModelProperty( "首次-就诊（vist）-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）")
    @ReportCardField(label = "首次-就诊（vist）-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）", group = "")
    private String fvPersonType ;

    /**
     * 首次-就诊（vist）-职业
     */
    @ApiModelProperty( "首次-就诊（vist）-职业")
    @ReportCardField(label = "首次-就诊（vist）-职业", group = "")
    private String fvJob ;

    /**
     * 首次-就诊（vist）-职业风险
     */
    @ApiModelProperty( "首次-就诊（vist）-职业风险")
    @ReportCardField(label = "首次-就诊（vist）-职业风险", group = "")
    private String fvJobRisk ;

    /**
     * 首次-就诊（vist）-工作单位/学校名称
     */
    @ApiModelProperty( "首次-就诊（vist）-工作单位/学校名称")
    @ReportCardField(label = "首次-就诊（vist）-工作单位/学校名称", group = "")
    private String fvCompany ;

    /**
     * 首次-就诊（vist）-单位/学校标化地址
     */
    @ApiModelProperty( "首次-就诊（vist）-单位/学校标化地址")
    @ReportCardField(label = "首次-就诊（vist）-单位/学校标化地址", group = "")
    private String fvCompanyAddress ;

    /**
     * 首次-就诊（vist）-单位-省编码
     */
    @ApiModelProperty( "首次-就诊（vist）-单位-省编码")
    @ReportCardField(label = "首次-就诊（vist）-单位-省编码", group = "")
    private String fvCompanyProvinceCode ;

    /**
     * 首次-就诊（vist）-单位-省
     */
    @ApiModelProperty( "首次-就诊（vist）-单位-省")
    @ReportCardField(label = "首次-就诊（vist）-单位-省", group = "")
    private String fvCompanyProvince ;

    /**
     * 首次-就诊（vist）-单位-市编码
     */
    @ApiModelProperty( "首次-就诊（vist）-单位-市编码")
    @ReportCardField(label = "首次-就诊（vist）-单位-市编码", group = "")
    private String fvCompanyCityCode ;

    /**
     * 首次-就诊（vist）-单位-市
     */
    @ApiModelProperty( "首次-就诊（vist）-单位-市")
    @ReportCardField(label = "首次-就诊（vist）-单位-市", group = "")
    private String fvCompanyCity ;

    /**
     * 首次-就诊（vist）-单位-行政区编码
     */
    @ApiModelProperty( "首次-就诊（vist）-单位-行政区编码")
    @ReportCardField(label = "首次-就诊（vist）-单位-行政区编码", group = "")
    private String fvCompanyDistrictCode ;

    /**
     * 首次-就诊（vist）-单位-行政区
     */
    @ApiModelProperty( "首次-就诊（vist）-单位-行政区")
    @ReportCardField(label = "首次-就诊（vist）-单位-行政区", group = "")
    private String fvCompanyDistrict ;

    /**
     * 首次-就诊（vist）-单位-功能区编码
     */
    @ApiModelProperty( "首次-就诊（vist）-单位-功能区编码")
    @ReportCardField(label = "首次-就诊（vist）-单位-功能区编码", group = "")
    private String fvCompanyFuncDistrictCode ;

    /**
     * 首次-就诊（vist）-单位-功能区
     */
    @ApiModelProperty( "首次-就诊（vist）-单位-功能区")
    @ReportCardField(label = "首次-就诊（vist）-单位-功能区", group = "")
    private String fvCompanyFunctionDistrict ;

    /**
     * 首次-就诊（vist）-单位-街道编码
     */
    @ApiModelProperty( "首次-就诊（vist）-单位-街道编码")
    @ReportCardField(label = "首次-就诊（vist）-单位-街道编码", group = "")
    private String fvCompanyStreetCode ;

    /**
     * 首次-就诊（vist）-单位-街道
     */
    @ApiModelProperty( "首次-就诊（vist）-单位-街道")
    @ReportCardField(label = "首次-就诊（vist）-单位-街道", group = "")
    private String fvCompanyStreet ;

    /**
     * 首次-就诊（vist）-单位-街道中心经度
     */
    @ApiModelProperty( "首次-就诊（vist）-单位-街道中心经度")
    @ReportCardField(label = "首次-就诊（vist）-单位-街道中心经度", group = "")
    private Double fvCompanyStreetLongitude ;

    /**
     * 首次-就诊（vist）-单位-街道中心维度
     */
    @ApiModelProperty( "首次-就诊（vist）-单位-街道中心维度")
    @ReportCardField(label = "首次-就诊（vist）-单位-街道中心维度", group = "")
    private Double fvCompanyStreetLatitude ;

    /**
     * 首次-就诊（vist）-单位-经度
     */
    @ApiModelProperty( "首次-就诊（vist）-单位-经度")
    @ReportCardField(label = "首次-就诊（vist）-单位-经度", group = "")
    private Double fvCompanyLongitude ;

    /**
     * 首次-就诊（vist）-单位-纬度
     */
    @ApiModelProperty( "首次-就诊（vist）-单位-纬度")
    @ReportCardField(label = "首次-就诊（vist）-单位-纬度", group = "")
    private Double fvCompanyLatitude ;

    /**
     * 首次-就诊（vist）-单位-城乡类型（城市、乡村、未知）
     */
    @ApiModelProperty( "首次-就诊（vist）-单位-城乡类型（城市、乡村、未知）")
    @ReportCardField(label = "首次-就诊（vist）-单位-城乡类型（城市、乡村、未知）", group = "")
    private String fvCompanyAddrType ;

    /**
     * 首次-就诊（vist）-机构id（主索引）
     */
    @ApiModelProperty( "首次-就诊（vist）-机构id（主索引）")
    @ReportCardField(label = "首次-就诊（vist）-机构id（主索引）", group = "")
    private String fvOrgId ;

    /**
     * 首次-就诊（vist）-机构名称
     */
    @ApiModelProperty( "首次-就诊（vist）-机构名称")
    @ReportCardField(label = "首次-就诊（vist）-机构名称", group = "")
    private String fvOrgName ;

    /**
     * 首次-就诊（vist）-机构类别（等级医院、基层医疗）
     */
    @ApiModelProperty( "首次-就诊（vist）-机构类别（等级医院、基层医疗）")
    @ReportCardField(label = "首次-就诊（vist）-机构类别（等级医院、基层医疗）", group = "")
    private String fvOrgClass ;

    /**
     * 首次-就诊（vist）-机构类型名称(204003)
     */
    @ApiModelProperty( "首次-就诊（vist）-机构类型名称(204003)")
    @ReportCardField(label = "首次-就诊（vist）-机构类型名称(204003)", group = "")
    private String fvOrgTypeName ;

    /**
     * 首次-就诊（vist）-机构详细地址
     */
    @ApiModelProperty( "首次-就诊（vist）-机构详细地址")
    @ReportCardField(label = "首次-就诊（vist）-机构详细地址", group = "")
    private String fvOrgAddrDetail ;

    /**
     * 首次-就诊（vist）-机构标化地址
     */
    @ApiModelProperty( "首次-就诊（vist）-机构标化地址")
    @ReportCardField(label = "首次-就诊（vist）-机构标化地址", group = "")
    private String fvOrgAddrDetailStd ;

    /**
     * 首次-就诊（vist）-机构-城乡类型（城市、乡村、未知）
     */
    @ApiModelProperty( "首次-就诊（vist）-机构-城乡类型（城市、乡村、未知）")
    @ReportCardField(label = "首次-就诊（vist）-机构-城乡类型（城市、乡村、未知）", group = "")
    private String fvOrgAddrType ;

    /**
     * 首次-就诊（vist）-机构-省编码
     */
    @ApiModelProperty( "首次-就诊（vist）-机构-省编码")
    @ReportCardField(label = "首次-就诊（vist）-机构-省编码", group = "")
    private String fvOrgAddrProvinceCode ;

    /**
     * 首次-就诊（vist）-机构-省
     */
    @ApiModelProperty( "首次-就诊（vist）-机构-省")
    @ReportCardField(label = "首次-就诊（vist）-机构-省", group = "")
    private String fvOrgAddrProvince ;

    /**
     * 首次-就诊（vist）-机构-市编码
     */
    @ApiModelProperty( "首次-就诊（vist）-机构-市编码")
    @ReportCardField(label = "首次-就诊（vist）-机构-市编码", group = "")
    private String fvOrgAddrCityCode ;

    /**
     * 首次-就诊（vist）-机构-市
     */
    @ApiModelProperty( "首次-就诊（vist）-机构-市")
    @ReportCardField(label = "首次-就诊（vist）-机构-市", group = "")
    private String fvOrgAddrCity ;

    /**
     * 首次-就诊（vist）-机构-行政区编码
     */
    @ApiModelProperty( "首次-就诊（vist）-机构-行政区编码")
    @ReportCardField(label = "首次-就诊（vist）-机构-行政区编码", group = "")
    private String fvOrgAddrDistrictCode ;

    /**
     * 首次-就诊（vist）-机构-行政区
     */
    @ApiModelProperty( "首次-就诊（vist）-机构-行政区")
    @ReportCardField(label = "首次-就诊（vist）-机构-行政区", group = "")
    private String fvOrgAddrDistrict ;

    /**
     * 首次-就诊（vist）-机构-功能区编码
     */
    @ApiModelProperty( "首次-就诊（vist）-机构-功能区编码")
    @ReportCardField(label = "首次-就诊（vist）-机构-功能区编码", group = "")
    private String fvOrgAddrFunctionDistrictCode ;

    /**
     * 首次-就诊（vist）-机构-功能区
     */
    @ApiModelProperty( "首次-就诊（vist）-机构-功能区")
    @ReportCardField(label = "首次-就诊（vist）-机构-功能区", group = "")
    private String fvOrgAddrFunctionDistrict ;

    /**
     * 首次-就诊（vist）-机构-街道编码
     */
    @ApiModelProperty( "首次-就诊（vist）-机构-街道编码")
    @ReportCardField(label = "首次-就诊（vist）-机构-街道编码", group = "")
    private String fvOrgAddrStreetCode ;

    /**
     * 首次-就诊（vist）-机构-街道
     */
    @ApiModelProperty( "首次-就诊（vist）-机构-街道")
    @ReportCardField(label = "首次-就诊（vist）-机构-街道", group = "")
    private String fvOrgAddrStreet ;

    /**
     * 首次-就诊（vist）-机构经度
     */
    @ApiModelProperty( "首次-就诊（vist）-机构经度")
    @ReportCardField(label = "首次-就诊（vist）-机构经度", group = "")
    private Double fvOrgLongitude ;

    /**
     * 首次-就诊（vist）-机构纬度
     */
    @ApiModelProperty( "首次-就诊（vist）-机构纬度")
    @ReportCardField(label = "首次-就诊（vist）-机构纬度", group = "")
    private Double fvOrgLatitude ;

    /**
     * 首次-就诊（vist）-就诊时间
     */
    @ApiModelProperty( "首次-就诊（vist）-就诊时间")
    @ReportCardField(label = "首次-就诊（vist）-就诊时间", group = "", dateFormat = "yyyy-MM-dd HH:mm")
    private Date fvVisitTime ;

    /**
     * 首次-就诊（vist）-就诊所在日
     */
    @ApiModelProperty( "首次-就诊（vist）-就诊所在日")
    @ReportCardField(label = "首次-就诊（vist）-就诊所在日", group = "", dateFormat = "yyyy-MM-dd HH:mm")
    private Date fvVisitDay ;

    /**
     * 首次-就诊（vist）-就诊科室编码
     */
    @ApiModelProperty( "首次-就诊（vist）-就诊科室编码")
    @ReportCardField(label = "首次-就诊（vist）-就诊科室编码", group = "")
    private String fvDeptCode ;

    /**
     * 首次-就诊（vist）-就诊科室
     */
    @ApiModelProperty( "首次-就诊（vist）-就诊科室")
    @ReportCardField(label = "首次-就诊（vist）-就诊科室", group = "")
    private String fvDeptName ;

    /**
     * 首次-就诊（vist）-主诉
     */
    @ApiModelProperty( "首次-就诊（vist）-主诉")
    @ReportCardField(label = "首次-就诊（vist）-主诉", group = "")
    private String fvSuit ;

    /**
     * 首次-就诊（vist）-症状
     */
    @ApiModelProperty( "首次-就诊（vist）-症状")
    @ReportCardField(label = "首次-就诊（vist）-症状", group = "")
    private String fvSymptom ;

    /**
     * 首次-就诊（vist）-诊断
     */
    @ApiModelProperty( "首次-就诊（vist）-诊断")
    @ReportCardField(label = "首次-就诊（vist）-诊断", group = "")
    private String fvDiag ;

    /**
     * 首次-就诊（vist）-标化诊断
     */
    @ApiModelProperty( "首次-就诊（vist）-标化诊断")
    @ReportCardField(label = "首次-就诊（vist）-标化诊断", group = "")
    private String fvDiagStd ;

    /**
     * 首次-识别（identify）-症候群-识别症候群的病历识别id
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-识别症候群的病历识别id")
    @ReportCardField(label = "首次-识别（identify）-症候群-识别症候群的病历识别id", group = "")
    private String fiSynIdentifyId ;

    /**
     * 首次-识别（identify）-症候群-人员id
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-人员id")
    @ReportCardField(label = "首次-识别（identify）-症候群-人员id", group = "")
    private String fiSynPatientId ;

    /**
     * 首次-识别（identify）-症候群-患者年龄（单位岁）
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-患者年龄（单位岁）")
    @ReportCardField(label = "首次-识别（identify）-症候群-患者年龄（单位岁）", group = "")
    private Double fiSynPatientAge ;

    /**
     * 首次-识别（identify）-症候群-患者联系方式
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-患者联系方式")
    @ReportCardField(label = "首次-识别（identify）-症候群-患者联系方式", group = "")
    private String fiSynPatientPhone ;

    /**
     * 首次-识别（identify）-症候群-现住详细地址
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-现住详细地址")
    @ReportCardField(label = "首次-识别（identify）-症候群-现住详细地址", group = "")
    private String fiSynLivingAddrDetail ;

    /**
     * 首次-识别（identify）-症候群-现住址标化地址
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-现住址标化地址")
    @ReportCardField(label = "首次-识别（identify）-症候群-现住址标化地址", group = "")
    private String fiSynLivingAddrDetailStd ;

    /**
     * 首次-识别（identify）-症候群-现住址-城乡类型（城市、乡村、未知）
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-现住址-城乡类型（城市、乡村、未知）")
    @ReportCardField(label = "首次-识别（identify）-症候群-现住址-城乡类型（城市、乡村、未知）", group = "")
    private String fiSynLivingAddrType ;

    /**
     * 首次-识别（identify）-症候群-现住地-省编码
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-现住地-省编码")
    @ReportCardField(label = "首次-识别（identify）-症候群-现住地-省编码", group = "")
    private String fiSynLivingAddrProvinceCode ;

    /**
     * 首次-识别（identify）-症候群-现住地-省
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-现住地-省")
    @ReportCardField(label = "首次-识别（identify）-症候群-现住地-省", group = "")
    private String fiSynLivingAddrProvince ;

    /**
     * 首次-识别（identify）-症候群-现住地-市编码
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-现住地-市编码")
    @ReportCardField(label = "首次-识别（identify）-症候群-现住地-市编码", group = "")
    private String fiSynLivingAddrCityCode ;

    /**
     * 首次-识别（identify）-症候群-现住地-市
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-现住地-市")
    @ReportCardField(label = "首次-识别（identify）-症候群-现住地-市", group = "")
    private String fiSynLivingAddrCity ;

    /**
     * 首次-识别（identify）-症候群-现住地-行政区编码
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-现住地-行政区编码")
    @ReportCardField(label = "首次-识别（identify）-症候群-现住地-行政区编码", group = "")
    private String fiSynLivingAddrDistrictCode ;

    /**
     * 首次-识别（identify）-症候群-现住地-行政区
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-现住地-行政区")
    @ReportCardField(label = "首次-识别（identify）-症候群-现住地-行政区", group = "")
    private String fiSynLivingAddrDistrict ;

    /**
     * 首次-识别（identify）-症候群-现住址-功能区编码
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-现住址-功能区编码")
    @ReportCardField(label = "首次-识别（identify）-症候群-现住址-功能区编码", group = "")
    private String fiSynLivingAddrFuncDistrictCode ;

    /**
     * 首次-识别（identify）-症候群-现住址-功能区
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-现住址-功能区")
    @ReportCardField(label = "首次-识别（identify）-症候群-现住址-功能区", group = "")
    private String fiSynLivingAddrFuncDistrict ;

    /**
     * 首次-识别（identify）-症候群-现住址-街道编码
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-现住址-街道编码")
    @ReportCardField(label = "首次-识别（identify）-症候群-现住址-街道编码", group = "")
    private String fiSynLivingAddrStreetCode ;

    /**
     * 首次-识别（identify）-症候群-现住址-街道
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-现住址-街道")
    @ReportCardField(label = "首次-识别（identify）-症候群-现住址-街道", group = "")
    private String fiSynLivingAddrStreet ;

    /**
     * 首次-识别（identify）-症候群-现住址-街道中心经度
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-现住址-街道中心经度")
    @ReportCardField(label = "首次-识别（identify）-症候群-现住址-街道中心经度", group = "")
    private Double fiSynLivingAddrStreetLongitude ;

    /**
     * 首次-识别（identify）-症候群-现住址-街道中心纬度
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-现住址-街道中心纬度")
    @ReportCardField(label = "首次-识别（identify）-症候群-现住址-街道中心纬度", group = "")
    private Double fiSynLivingAddrStreetLatitude ;

    /**
     * 首次-识别（identify）-症候群-现住址-经度
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-现住址-经度")
    @ReportCardField(label = "首次-识别（identify）-症候群-现住址-经度", group = "")
    private Double fiSynLivingAddrLongitude ;

    /**
     * 首次-识别（identify）-症候群-现住址-经度
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-现住址-经度")
    @ReportCardField(label = "首次-识别（identify）-症候群-现住址-经度", group = "")
    private Double fiSynLivingAddrLatitude ;

    /**
     * 首次-识别（identify）-症候群-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）")
    @ReportCardField(label = "首次-识别（identify）-症候群-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）", group = "")
    private String fiSynPersonType ;

    /**
     * 首次-识别（identify）-症候群-职业
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-职业")
    @ReportCardField(label = "首次-识别（identify）-症候群-职业", group = "")
    private String fiSynJob ;

    /**
     * 首次-识别（identify）-症候群-职业风险
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-职业风险")
    @ReportCardField(label = "首次-识别（identify）-症候群-职业风险", group = "")
    private String fiSynJobRisk ;

    /**
     * 首次-识别（identify）-症候群-工作单位/学校名称
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-工作单位/学校名称")
    @ReportCardField(label = "首次-识别（identify）-症候群-工作单位/学校名称", group = "")
    private String fiSynCompany ;

    /**
     * 首次-识别（identify）-症候群-单位/学校标化地址
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-单位/学校标化地址")
    @ReportCardField(label = "首次-识别（identify）-症候群-单位/学校标化地址", group = "")
    private String fiSynCompanyAddress ;

    /**
     * 首次-识别（identify）-症候群-单位-省编码
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-单位-省编码")
    @ReportCardField(label = "首次-识别（identify）-症候群-单位-省编码", group = "")
    private String fiSynCompanyProvinceCode ;

    /**
     * 首次-识别（identify）-症候群-单位-省
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-单位-省")
    @ReportCardField(label = "首次-识别（identify）-症候群-单位-省", group = "")
    private String fiSynCompanyProvince ;

    /**
     * 首次-识别（identify）-症候群-单位-市编码
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-单位-市编码")
    @ReportCardField(label = "首次-识别（identify）-症候群-单位-市编码", group = "")
    private String fiSynCompanyCityCode ;

    /**
     * 首次-识别（identify）-症候群-单位-市
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-单位-市")
    @ReportCardField(label = "首次-识别（identify）-症候群-单位-市", group = "")
    private String fiSynCompanyCity ;

    /**
     * 首次-识别（identify）-症候群-单位-行政区编码
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-单位-行政区编码")
    @ReportCardField(label = "首次-识别（identify）-症候群-单位-行政区编码", group = "")
    private String fiSynCompanyDistrictCode ;

    /**
     * 首次-识别（identify）-症候群-单位-行政区
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-单位-行政区")
    @ReportCardField(label = "首次-识别（identify）-症候群-单位-行政区", group = "")
    private String fiSynCompanyDistrict ;

    /**
     * 首次-识别（identify）-症候群-单位-功能区编码
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-单位-功能区编码")
    @ReportCardField(label = "首次-识别（identify）-症候群-单位-功能区编码", group = "")
    private String fiSynCompanyFuncDistrictCode ;

    /**
     * 首次-识别（identify）-症候群-单位-功能区
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-单位-功能区")
    @ReportCardField(label = "首次-识别（identify）-症候群-单位-功能区", group = "")
    private String fiSynCompanyFunctionDistrict ;

    /**
     * 首次-识别（identify）-症候群-单位-街道编码
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-单位-街道编码")
    @ReportCardField(label = "首次-识别（identify）-症候群-单位-街道编码", group = "")
    private String fiSynCompanyStreetCode ;

    /**
     * 首次-识别（identify）-症候群-单位-街道
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-单位-街道")
    @ReportCardField(label = "首次-识别（identify）-症候群-单位-街道", group = "")
    private String fiSynCompanyStreet ;

    /**
     * 首次-识别（identify）-症候群-单位-街道中心经度
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-单位-街道中心经度")
    @ReportCardField(label = "首次-识别（identify）-症候群-单位-街道中心经度", group = "")
    private Double fiSynCompanyStreetLongitude ;

    /**
     * 首次-识别（identify）-症候群-单位-街道中心维度
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-单位-街道中心维度")
    @ReportCardField(label = "首次-识别（identify）-症候群-单位-街道中心维度", group = "")
    private Double fiSynCompanyStreetLatitude ;

    /**
     * 首次-识别（identify）-症候群-单位-经度
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-单位-经度")
    @ReportCardField(label = "首次-识别（identify）-症候群-单位-经度", group = "")
    private Double fiSynCompanyLongitude ;

    /**
     * 首次-识别（identify）-症候群-单位-纬度
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-单位-纬度")
    @ReportCardField(label = "首次-识别（identify）-症候群-单位-纬度", group = "")
    private Double fiSynCompanyLatitude ;

    /**
     * 首次-识别（identify）-症候群-单位-城乡类型（城市、乡村、未知）
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-单位-城乡类型（城市、乡村、未知）")
    @ReportCardField(label = "首次-识别（identify）-症候群-单位-城乡类型（城市、乡村、未知）", group = "")
    private String fiSynCompanyAddrType ;

    /**
     * 首次-识别（identify）-症候群-机构id（主索引）
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构id（主索引）")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构id（主索引）", group = "")
    private String fiSynOrgId ;

    /**
     * 首次-识别（identify）-症候群-机构名称
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构名称")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构名称", group = "")
    private String fiSynOrgName ;

    /**
     * 首次-识别（identify）-症候群-机构类别（等级医院、基层医疗）
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构类别（等级医院、基层医疗）")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构类别（等级医院、基层医疗）", group = "")
    private String fiSynOrgClass ;

    /**
     * 首次-识别（identify）-症候群-机构类型名称(204003)
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构类型名称(204003)")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构类型名称(204003)", group = "")
    private String fiSynOrgTypeName ;

    /**
     * 首次-识别（identify）-症候群-机构详细地址
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构详细地址")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构详细地址", group = "")
    private String fiSynOrgAddrDetail ;

    /**
     * 首次-识别（identify）-症候群-机构标化地址
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构标化地址")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构标化地址", group = "")
    private String fiSynOrgAddrDetailStd ;

    /**
     * 首次-识别（identify）-症候群-机构-城乡类型（城市、乡村、未知）
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构-城乡类型（城市、乡村、未知）")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构-城乡类型（城市、乡村、未知）", group = "")
    private String fiSynOrgAddrType ;

    /**
     * 首次-识别（identify）-症候群-机构-省编码
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构-省编码")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构-省编码", group = "")
    private String fiSynOrgAddrProvinceCode ;

    /**
     * 首次-识别（identify）-症候群-机构-省
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构-省")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构-省", group = "")
    private String fiSynOrgAddrProvince ;

    /**
     * 首次-识别（identify）-症候群-机构-市编码
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构-市编码")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构-市编码", group = "")
    private String fiSynOrgAddrCityCode ;

    /**
     * 首次-识别（identify）-症候群-机构-市
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构-市")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构-市", group = "")
    private String fiSynOrgAddrCity ;

    /**
     * 首次-识别（identify）-症候群-机构-行政区编码
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构-行政区编码")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构-行政区编码", group = "")
    private String fiSynOrgAddrDistrictCode ;

    /**
     * 首次-识别（identify）-症候群-机构-行政区
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构-行政区")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构-行政区", group = "")
    private String fiSynOrgAddrDistrict ;

    /**
     * 首次-识别（identify）-症候群-机构-功能区编码
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构-功能区编码")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构-功能区编码", group = "")
    private String fiSynOrgAddrFunctionDistrictCode ;

    /**
     * 首次-识别（identify）-症候群-机构-功能区
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构-功能区")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构-功能区", group = "")
    private String fiSynOrgAddrFunctionDistrict ;

    /**
     * 首次-识别（identify）-症候群-机构-街道编码
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构-街道编码")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构-街道编码", group = "")
    private String fiSynOrgAddrStreetCode ;

    /**
     * 首次-识别（identify）-症候群-机构-街道
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构-街道")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构-街道", group = "")
    private String fiSynOrgAddrStreet ;

    /**
     * 首次-识别（identify）-症候群-机构经度
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构经度")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构经度", group = "")
    private Double fiSynOrgLongitude ;

    /**
     * 首次-识别（identify）-症候群-机构纬度
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-机构纬度")
    @ReportCardField(label = "首次-识别（identify）-症候群-机构纬度", group = "")
    private Double fiSynOrgLatitude ;

    /**
     * 首次-识别（identify）-症候群-就诊时间
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-就诊时间")
    @ReportCardField(label = "首次-识别（identify）-症候群-就诊时间", group = "", dateFormat = "yyyy-MM-dd HH:mm")
    private Date fiSynVisitTime ;

    /**
     * 首次-识别（identify）-症候群-就诊所在日
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-就诊所在日")
    @ReportCardField(label = "首次-识别（identify）-症候群-就诊所在日", group = "", dateFormat = "yyyy-MM-dd HH:mm")
    private Date fiSynVisitDay ;

    /**
     * 首次-识别（identify）-症候群-就诊科室编码
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-就诊科室编码")
    @ReportCardField(label = "首次-识别（identify）-症候群-就诊科室编码", group = "")
    private String fiSynDeptCode ;

    /**
     * 首次-识别（identify）-症候群-就诊科室
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-就诊科室")
    @ReportCardField(label = "首次-识别（identify）-症候群-就诊科室", group = "")
    private String fiSynDeptName ;

    /**
     * 首次-识别（identify）-症候群-主诉
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-主诉")
    @ReportCardField(label = "首次-识别（identify）-症候群-主诉", group = "")
    private String fiSynSuit ;

    /**
     * 首次-识别（identify）-症候群-症状
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-症状")
    @ReportCardField(label = "首次-识别（identify）-症候群-症状", group = "")
    private String fiSynSymptom ;

    /**
     * 首次-识别（identify）-症候群-诊断
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-诊断")
    @ReportCardField(label = "首次-识别（identify）-症候群-诊断", group = "")
    private String fiSynDiag ;

    /**
     * 首次-识别（identify）-症候群-标化诊断
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-标化诊断")
    @ReportCardField(label = "首次-识别（identify）-症候群-标化诊断", group = "")
    private String fiSynDiagStd ;

    /**
     * 首次-识别（identify）-症候群-报告时间
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-报告时间")
    @ReportCardField(label = "首次-识别（identify）-症候群-报告时间", group = "", dateFormat = "yyyy-MM-dd HH:mm")
    private Date fiSynIdentifyTime ;

    /**
     * 首次-识别（identify）-症候群-报告疾病编码
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-报告疾病编码")
    @ReportCardField(label = "首次-识别（identify）-症候群-报告疾病编码", group = "")
    private String fiSynDiseaseCode ;

    /**
     * 首次-识别（identify）-症候群-报告疾病名称
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-报告疾病名称")
    @ReportCardField(label = "首次-识别（identify）-症候群-报告疾病名称", group = "")
    private String fiSynDiseaseName ;

    /**
     * 首次-识别（identify）-症候群-监测结果依据类型（诊断、检查、检验等）
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-监测结果依据类型（诊断、检查、检验等）")
    @ReportCardField(label = "首次-识别（identify）-症候群-监测结果依据类型（诊断、检查、检验等）", group = "")
    private String fiSynSupport ;

    /**
     * 首次-识别（identify）-症候群-报告类型（系统报告、人工修正）
     */
    @ApiModelProperty( "首次-识别（identify）-症候群-报告类型（系统报告、人工修正）")
    @ReportCardField(label = "首次-识别（identify）-症候群-报告类型（系统报告、人工修正）", group = "")
    private String fiSynType ;

    /**
     * 首次-识别（identify）-传染病-识别传染病的病历识别id
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-识别传染病的病历识别id")
    @ReportCardField(label = "首次-识别（identify）-传染病-识别传染病的病历识别id", group = "")
    private String fiInfIdentifyId ;

    /**
     * 首次-识别（identify）-传染病-人员id
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-人员id")
    @ReportCardField(label = "首次-识别（identify）-传染病-人员id", group = "")
    private String fiInfPatientId ;

    /**
     * 首次-识别（identify）-传染病-患者年龄（单位岁）
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-患者年龄（单位岁）")
    @ReportCardField(label = "首次-识别（identify）-传染病-患者年龄（单位岁）", group = "")
    private Double fiInfPatientAge ;

    /**
     * 首次-识别（identify）-传染病-患者联系方式
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-患者联系方式")
    @ReportCardField(label = "首次-识别（identify）-传染病-患者联系方式", group = "")
    private String fiInfPatientPhone ;

    /**
     * 首次-识别（identify）-传染病-现住详细地址
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-现住详细地址")
    @ReportCardField(label = "首次-识别（identify）-传染病-现住详细地址", group = "")
    private String fiInfLivingAddrDetail ;

    /**
     * 首次-识别（identify）-传染病-现住址标化地址
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-现住址标化地址")
    @ReportCardField(label = "首次-识别（identify）-传染病-现住址标化地址", group = "")
    private String fiInfLivingAddrDetailStd ;

    /**
     * 首次-识别（identify）-传染病-现住址-城乡类型（城市、乡村、未知）
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-现住址-城乡类型（城市、乡村、未知）")
    @ReportCardField(label = "首次-识别（identify）-传染病-现住址-城乡类型（城市、乡村、未知）", group = "")
    private String fiInfLivingAddrType ;

    /**
     * 首次-识别（identify）-传染病-现住地-省编码
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-现住地-省编码")
    @ReportCardField(label = "首次-识别（identify）-传染病-现住地-省编码", group = "")
    private String fiInfLivingAddrProvinceCode ;

    /**
     * 首次-识别（identify）-传染病-现住地-省
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-现住地-省")
    @ReportCardField(label = "首次-识别（identify）-传染病-现住地-省", group = "")
    private String fiInfLivingAddrProvince ;

    /**
     * 首次-识别（identify）-传染病-现住地-市编码
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-现住地-市编码")
    @ReportCardField(label = "首次-识别（identify）-传染病-现住地-市编码", group = "")
    private String fiInfLivingAddrCityCode ;

    /**
     * 首次-识别（identify）-传染病-现住地-市
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-现住地-市")
    @ReportCardField(label = "首次-识别（identify）-传染病-现住地-市", group = "")
    private String fiInfLivingAddrCity ;

    /**
     * 首次-识别（identify）-传染病-现住地-行政区编码
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-现住地-行政区编码")
    @ReportCardField(label = "首次-识别（identify）-传染病-现住地-行政区编码", group = "")
    private String fiInfLivingAddrDistrictCode ;

    /**
     * 首次-识别（identify）-传染病-现住地-行政区
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-现住地-行政区")
    @ReportCardField(label = "首次-识别（identify）-传染病-现住地-行政区", group = "")
    private String fiInfLivingAddrDistrict ;

    /**
     * 首次-识别（identify）-传染病-现住址-功能区编码
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-现住址-功能区编码")
    @ReportCardField(label = "首次-识别（identify）-传染病-现住址-功能区编码", group = "")
    private String fiInfLivingAddrFuncDistrictCode ;

    /**
     * 首次-识别（identify）-传染病-现住址-功能区
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-现住址-功能区")
    @ReportCardField(label = "首次-识别（identify）-传染病-现住址-功能区", group = "")
    private String fiInfLivingAddrFuncDistrict ;

    /**
     * 首次-识别（identify）-传染病-现住址-街道编码
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-现住址-街道编码")
    @ReportCardField(label = "首次-识别（identify）-传染病-现住址-街道编码", group = "")
    private String fiInfLivingAddrStreetCode ;

    /**
     * 首次-识别（identify）-传染病-现住址-街道
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-现住址-街道")
    @ReportCardField(label = "首次-识别（identify）-传染病-现住址-街道", group = "")
    private String fiInfLivingAddrStreet ;

    /**
     * 首次-识别（identify）-传染病-现住址-街道中心经度
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-现住址-街道中心经度")
    @ReportCardField(label = "首次-识别（identify）-传染病-现住址-街道中心经度", group = "")
    private Double fiInfLivingAddrStreetLongitude ;

    /**
     * 首次-识别（identify）-传染病-现住址-街道中心纬度
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-现住址-街道中心纬度")
    @ReportCardField(label = "首次-识别（identify）-传染病-现住址-街道中心纬度", group = "")
    private Double fiInfLivingAddrStreetLatitude ;

    /**
     * 首次-识别（identify）-传染病-现住址-经度
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-现住址-经度")
    @ReportCardField(label = "首次-识别（identify）-传染病-现住址-经度", group = "")
    private Double fiInfLivingAddrLongitude ;

    /**
     * 首次-识别（identify）-传染病-现住址-经度
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-现住址-经度")
    @ReportCardField(label = "首次-识别（identify）-传染病-现住址-经度", group = "")
    private Double fiInfLivingAddrLatitude ;

    /**
     * 首次-识别（identify）-传染病-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）")
    @ReportCardField(label = "首次-识别（identify）-传染病-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）", group = "")
    private String fiInfPersonType ;

    /**
     * 首次-识别（identify）-传染病-职业
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-职业")
    @ReportCardField(label = "首次-识别（identify）-传染病-职业", group = "")
    private String fiInfJob ;

    /**
     * 首次-识别（identify）-传染病-职业风险
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-职业风险")
    @ReportCardField(label = "首次-识别（identify）-传染病-职业风险", group = "")
    private String fiInfJobRisk ;

    /**
     * 首次-识别（identify）-传染病-工作单位/学校名称
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-工作单位/学校名称")
    @ReportCardField(label = "首次-识别（identify）-传染病-工作单位/学校名称", group = "")
    private String fiInfCompany ;

    /**
     * 首次-识别（identify）-传染病-单位/学校标化地址
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-单位/学校标化地址")
    @ReportCardField(label = "首次-识别（identify）-传染病-单位/学校标化地址", group = "")
    private String fiInfCompanyAddress ;

    /**
     * 首次-识别（identify）-传染病-单位-省编码
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-单位-省编码")
    @ReportCardField(label = "首次-识别（identify）-传染病-单位-省编码", group = "")
    private String fiInfCompanyProvinceCode ;

    /**
     * 首次-识别（identify）-传染病-单位-省
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-单位-省")
    @ReportCardField(label = "首次-识别（identify）-传染病-单位-省", group = "")
    private String fiInfCompanyProvince ;

    /**
     * 首次-识别（identify）-传染病-单位-市编码
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-单位-市编码")
    @ReportCardField(label = "首次-识别（identify）-传染病-单位-市编码", group = "")
    private String fiInfCompanyCityCode ;

    /**
     * 首次-识别（identify）-传染病-单位-市
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-单位-市")
    @ReportCardField(label = "首次-识别（identify）-传染病-单位-市", group = "")
    private String fiInfCompanyCity ;

    /**
     * 首次-识别（identify）-传染病-单位-行政区编码
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-单位-行政区编码")
    @ReportCardField(label = "首次-识别（identify）-传染病-单位-行政区编码", group = "")
    private String fiInfCompanyDistrictCode ;

    /**
     * 首次-识别（identify）-传染病-单位-行政区
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-单位-行政区")
    @ReportCardField(label = "首次-识别（identify）-传染病-单位-行政区", group = "")
    private String fiInfCompanyDistrict ;

    /**
     * 首次-识别（identify）-传染病-单位-功能区编码
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-单位-功能区编码")
    @ReportCardField(label = "首次-识别（identify）-传染病-单位-功能区编码", group = "")
    private String fiInfCompanyFuncDistrictCode ;

    /**
     * 首次-识别（identify）-传染病-单位-功能区
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-单位-功能区")
    @ReportCardField(label = "首次-识别（identify）-传染病-单位-功能区", group = "")
    private String fiInfCompanyFunctionDistrict ;

    /**
     * 首次-识别（identify）-传染病-单位-街道编码
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-单位-街道编码")
    @ReportCardField(label = "首次-识别（identify）-传染病-单位-街道编码", group = "")
    private String fiInfCompanyStreetCode ;

    /**
     * 首次-识别（identify）-传染病-单位-街道
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-单位-街道")
    @ReportCardField(label = "首次-识别（identify）-传染病-单位-街道", group = "")
    private String fiInfCompanyStreet ;

    /**
     * 首次-识别（identify）-传染病-单位-街道中心经度
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-单位-街道中心经度")
    @ReportCardField(label = "首次-识别（identify）-传染病-单位-街道中心经度", group = "")
    private Double fiInfCompanyStreetLongitude ;

    /**
     * 首次-识别（identify）-传染病-单位-街道中心维度
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-单位-街道中心维度")
    @ReportCardField(label = "首次-识别（identify）-传染病-单位-街道中心维度", group = "")
    private Double fiInfCompanyStreetLatitude ;

    /**
     * 首次-识别（identify）-传染病-单位-经度
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-单位-经度")
    @ReportCardField(label = "首次-识别（identify）-传染病-单位-经度", group = "")
    private Double fiInfCompanyLongitude ;

    /**
     * 首次-识别（identify）-传染病-单位-纬度
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-单位-纬度")
    @ReportCardField(label = "首次-识别（identify）-传染病-单位-纬度", group = "")
    private Double fiInfCompanyLatitude ;

    /**
     * 首次-识别（identify）-传染病-单位-城乡类型（城市、乡村、未知）
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-单位-城乡类型（城市、乡村、未知）")
    @ReportCardField(label = "首次-识别（identify）-传染病-单位-城乡类型（城市、乡村、未知）", group = "")
    private String fiInfCompanyAddrType ;

    /**
     * 首次-识别（identify）-传染病-机构id（主索引）
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构id（主索引）")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构id（主索引）", group = "")
    private String fiInfOrgId ;

    /**
     * 首次-识别（identify）-传染病-机构名称
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构名称")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构名称", group = "")
    private String fiInfOrgName ;

    /**
     * 首次-识别（identify）-传染病-机构类别（等级医院、基层医疗）
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构类别（等级医院、基层医疗）")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构类别（等级医院、基层医疗）", group = "")
    private String fiInfOrgClass ;

    /**
     * 首次-识别（identify）-传染病-机构类型名称(204003)
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构类型名称(204003)")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构类型名称(204003)", group = "")
    private String fiInfOrgTypeName ;

    /**
     * 首次-识别（identify）-传染病-机构详细地址
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构详细地址")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构详细地址", group = "")
    private String fiInfOrgAddrDetail ;

    /**
     * 首次-识别（identify）-传染病-机构标化地址
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构标化地址")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构标化地址", group = "")
    private String fiInfOrgAddrDetailStd ;

    /**
     * 首次-识别（identify）-传染病-机构-城乡类型（城市、乡村、未知）
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构-城乡类型（城市、乡村、未知）")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构-城乡类型（城市、乡村、未知）", group = "")
    private String fiInfOrgAddrType ;

    /**
     * 首次-识别（identify）-传染病-机构-省编码
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构-省编码")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构-省编码", group = "")
    private String fiInfOrgAddrProvinceCode ;

    /**
     * 首次-识别（identify）-传染病-机构-省
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构-省")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构-省", group = "")
    private String fiInfOrgAddrProvince ;

    /**
     * 首次-识别（identify）-传染病-机构-市编码
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构-市编码")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构-市编码", group = "")
    private String fiInfOrgAddrCityCode ;

    /**
     * 首次-识别（identify）-传染病-机构-市
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构-市")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构-市", group = "")
    private String fiInfOrgAddrCity ;

    /**
     * 首次-识别（identify）-传染病-机构-行政区编码
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构-行政区编码")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构-行政区编码", group = "")
    private String fiInfOrgAddrDistrictCode ;

    /**
     * 首次-识别（identify）-传染病-机构-行政区
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构-行政区")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构-行政区", group = "")
    private String fiInfOrgAddrDistrict ;

    /**
     * 首次-识别（identify）-传染病-机构-功能区编码
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构-功能区编码")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构-功能区编码", group = "")
    private String fiInfOrgAddrFunctionDistrictCode ;

    /**
     * 首次-识别（identify）-传染病-机构-功能区
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构-功能区")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构-功能区", group = "")
    private String fiInfOrgAddrFunctionDistrict ;

    /**
     * 首次-识别（identify）-传染病-机构-街道编码
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构-街道编码")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构-街道编码", group = "")
    private String fiInfOrgAddrStreetCode ;

    /**
     * 首次-识别（identify）-传染病-机构-街道
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构-街道")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构-街道", group = "")
    private String fiInfOrgAddrStreet ;

    /**
     * 首次-识别（identify）-传染病-机构经度
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构经度")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构经度", group = "")
    private Double fiInfOrgLongitude ;

    /**
     * 首次-识别（identify）-传染病-机构纬度
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-机构纬度")
    @ReportCardField(label = "首次-识别（identify）-传染病-机构纬度", group = "")
    private Double fiInfOrgLatitude ;

    /**
     * 首次-识别（identify）-传染病-就诊时间
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-就诊时间")
    @ReportCardField(label = "首次-识别（identify）-传染病-就诊时间", group = "", dateFormat = "yyyy-MM-dd HH:mm")
    private Date fiInfVisitTime ;

    /**
     * 首次-识别（identify）-传染病-就诊所在日
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-就诊所在日")
    @ReportCardField(label = "首次-识别（identify）-传染病-就诊所在日", group = "", dateFormat = "yyyy-MM-dd HH:mm")
    private Date fiInfVisitDay ;

    /**
     * 首次-识别（identify）-传染病-就诊科室编码
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-就诊科室编码")
    @ReportCardField(label = "首次-识别（identify）-传染病-就诊科室编码", group = "")
    private String fiInfDeptCode ;

    /**
     * 首次-识别（identify）-传染病-就诊科室
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-就诊科室")
    @ReportCardField(label = "首次-识别（identify）-传染病-就诊科室", group = "")
    private String fiInfDeptName ;

    /**
     * 首次-识别（identify）-传染病-主诉
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-主诉")
    @ReportCardField(label = "首次-识别（identify）-传染病-主诉", group = "")
    private String fiInfSuit ;

    /**
     * 首次-识别（identify）-传染病-症状
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-症状")
    @ReportCardField(label = "首次-识别（identify）-传染病-症状", group = "")
    private String fiInfSymptom ;

    /**
     * 首次-识别（identify）-传染病-诊断
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-诊断")
    @ReportCardField(label = "首次-识别（identify）-传染病-诊断", group = "")
    private String fiInfDiag ;

    /**
     * 首次-识别（identify）-传染病-标化诊断
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-标化诊断")
    @ReportCardField(label = "首次-识别（identify）-传染病-标化诊断", group = "")
    private String fiInfDiagStd ;

    /**
     * 首次-识别（identify）-传染病-报告时间
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-报告时间")
    @ReportCardField(label = "首次-识别（identify）-传染病-报告时间", group = "", dateFormat = "yyyy-MM-dd HH:mm")
    private Date fiInfIdentifyTime ;

    /**
     * 首次-识别（identify）-传染病-报告疾病编码
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-报告疾病编码")
    @ReportCardField(label = "首次-识别（identify）-传染病-报告疾病编码", group = "")
    private String fiInfDiseaseCode ;

    /**
     * 首次-识别（identify）-传染病-报告疾病名称
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-报告疾病名称")
    @ReportCardField(label = "首次-识别（identify）-传染病-报告疾病名称", group = "")
    private String fiInfDiseaseName ;

    /**
     * 首次-识别（identify）-传染病-监测结果依据类型（诊断、检查、检验等）
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-监测结果依据类型（诊断、检查、检验等）")
    @ReportCardField(label = "首次-识别（identify）-传染病-监测结果依据类型（诊断、检查、检验等）", group = "")
    private String fiInfSupport ;

    /**
     * 首次-识别（identify）-传染病-报告类型（系统报告、人工修正）
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-报告类型（系统报告、人工修正）")
    @ReportCardField(label = "首次-识别（identify）-传染病-报告类型（系统报告、人工修正）", group = "")
    private String fiInfType ;

    /**
     * 首次-识别（identify）-传染病-传染病分类（甲类、乙类、丙类、其它法定、其它非法定）
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-传染病分类（甲类、乙类、丙类、其它法定、其它非法定）")
    @ReportCardField(label = "首次-识别（identify）-传染病-传染病分类（甲类、乙类、丙类、其它法定、其它非法定）", group = "")
    private String fiInfInfectType ;

    /**
     * 首次-识别（identify）-传染病-传染病传播途径分类（（肠道传染病、呼吸道症候群、自然疫源及虫煤传染病、血源及性传播传染病））
     */
    @ApiModelProperty( "首次-识别（identify）-传染病-传染病传播途径分类（（肠道传染病、呼吸道症候群、自然疫源及虫煤传染病、血源及性传播传染病））")
    @ReportCardField(label = "首次-识别（identify）-传染病-传染病传播途径分类（（肠道传染病、呼吸道症候群、自然疫源及虫煤传染病、血源及性传播传染病））", group = "")
    private String fiInfInfectTransmit ;

    /**
     * 首次-审核（check）-症候群-被审核的症候群病历识别id
     */
    @ApiModelProperty( "首次-审核（check）-症候群-被审核的症候群病历识别id")
    @ReportCardField(label = "首次-审核（check）-症候群-被审核的症候群病历识别id", group = "")
    private String fcSynIdentifyId ;

    /**
     * 首次-审核（check）-症候群-报告症候群编码
     */
    @ApiModelProperty( "首次-审核（check）-症候群-报告症候群编码")
    @ReportCardField(label = "首次-审核（check）-症候群-报告症候群编码", group = "")
    private String fcSynDiseaseCode ;

    /**
     * 首次-审核（check）-症候群-报告症候群名称
     */
    @ApiModelProperty( "首次-审核（check）-症候群-报告症候群名称")
    @ReportCardField(label = "首次-审核（check）-症候群-报告症候群名称", group = "")
    private String fcSynDiseaseName ;

    /**
     * 首次-审核（check）-传染病-被审核的传染病病历识别id
     */
    @ApiModelProperty( "首次-审核（check）-传染病-被审核的传染病病历识别id")
    @ReportCardField(label = "首次-审核（check）-传染病-被审核的传染病病历识别id", group = "")
    private String fcInfIdentifyId ;

    /**
     * 首次-审核（check）-传染病-审核的传染病编码
     */
    @ApiModelProperty( "首次-审核（check）-传染病-审核的传染病编码")
    @ReportCardField(label = "首次-审核（check）-传染病-审核的传染病编码", group = "")
    private String fcInfDiseaseCode ;

    /**
     * 首次-审核（check）-传染病-审核的传染病名称
     */
    @ApiModelProperty( "首次-审核（check）-传染病-审核的传染病名称")
    @ReportCardField(label = "首次-审核（check）-传染病-审核的传染病名称", group = "")
    private String fcInfDiseaseName ;

    /**
     * 首次-审核（check）-传染病-审核传染病时间
     */
    @ApiModelProperty( "首次-审核（check）-传染病-审核传染病时间")
    @ReportCardField(label = "首次-审核（check）-传染病-审核传染病时间", group = "", dateFormat = "yyyy-MM-dd HH:mm")
    private Date fcInfCheckTime ;

    /**
     * 首次-审核（check）-传染病-审核的传染病分类（甲类、乙类、丙类、其它法定、其它非法定）
     */
    @ApiModelProperty( "首次-审核（check）-传染病-审核的传染病分类（甲类、乙类、丙类、其它法定、其它非法定）")
    @ReportCardField(label = "首次-审核（check）-传染病-审核的传染病分类（甲类、乙类、丙类、其它法定、其它非法定）", group = "")
    private String fcInfInfectType ;

    /**
     * 首次-审核（check）-传染病-审核的传染病传播途径分类（（肠道传染病、呼吸道症候群、自然疫源及虫煤传染病、血源及性传播传染病））
     */
    @ApiModelProperty( "首次-审核（check）-传染病-审核的传染病传播途径分类（（肠道传染病、呼吸道症候群、自然疫源及虫煤传染病、血源及性传播传染病））")
    @ReportCardField(label = "首次-审核（check）-传染病-审核的传染病传播途径分类（（肠道传染病、呼吸道症候群、自然疫源及虫煤传染病、血源及性传播传染病））", group = "")
    private String fcInfInfectTransmit ;

    /**
     * 最新-就诊（vist）-就诊的病历识别id
     */
    @ApiModelProperty( "最新-就诊（vist）-就诊的病历识别id")
    @ReportCardField(label = "最新-就诊（vist）-就诊的病历识别id", group = "")
    private String lvIdentifyId ;

    /**
     * 最新-就诊（vist）-人员id
     */
    @ApiModelProperty( "最新-就诊（vist）-人员id")
    @ReportCardField(label = "最新-就诊（vist）-人员id", group = "")
    private String lvPatientId ;

    /**
     * 最新-就诊（vist）-患者年龄（单位岁）
     */
    @ApiModelProperty( "最新-就诊（vist）-患者年龄（单位岁）")
    @ReportCardField(label = "最新-就诊（vist）-患者年龄（单位岁）", group = "")
    private Double lvPatientAge ;

    /**
     * 最新-就诊（vist）-患者联系方式
     */
    @ApiModelProperty( "最新-就诊（vist）-患者联系方式")
    @ReportCardField(label = "最新-就诊（vist）-患者联系方式", group = "")
    private String lvPatientPhone ;

    /**
     * 最新-就诊（vist）-现住详细地址
     */
    @ApiModelProperty( "最新-就诊（vist）-现住详细地址")
    @ReportCardField(label = "最新-就诊（vist）-现住详细地址", group = "")
    private String lvLivingAddrDetail ;

    /**
     * 最新-就诊（vist）-现住址标化地址
     */
    @ApiModelProperty( "最新-就诊（vist）-现住址标化地址")
    @ReportCardField(label = "最新-就诊（vist）-现住址标化地址", group = "")
    private String lvLivingAddrDetailStd ;

    /**
     * 最新-就诊（vist）-现住址-城乡类型（城市、乡村、未知）
     */
    @ApiModelProperty( "最新-就诊（vist）-现住址-城乡类型（城市、乡村、未知）")
    @ReportCardField(label = "最新-就诊（vist）-现住址-城乡类型（城市、乡村、未知）", group = "")
    private String lvLivingAddrType ;

    /**
     * 最新-就诊（vist）-现住地-省编码
     */
    @ApiModelProperty( "最新-就诊（vist）-现住地-省编码")
    @ReportCardField(label = "最新-就诊（vist）-现住地-省编码", group = "")
    private String lvLivingAddrProvinceCode ;

    /**
     * 最新-就诊（vist）-现住地-省
     */
    @ApiModelProperty( "最新-就诊（vist）-现住地-省")
    @ReportCardField(label = "最新-就诊（vist）-现住地-省", group = "")
    private String lvLivingAddrProvince ;

    /**
     * 最新-就诊（vist）-现住地-市编码
     */
    @ApiModelProperty( "最新-就诊（vist）-现住地-市编码")
    @ReportCardField(label = "最新-就诊（vist）-现住地-市编码", group = "")
    private String lvLivingAddrCityCode ;

    /**
     * 最新-就诊（vist）-现住地-市
     */
    @ApiModelProperty( "最新-就诊（vist）-现住地-市")
    @ReportCardField(label = "最新-就诊（vist）-现住地-市", group = "")
    private String lvLivingAddrCity ;

    /**
     * 最新-就诊（vist）-现住地-行政区编码
     */
    @ApiModelProperty( "最新-就诊（vist）-现住地-行政区编码")
    @ReportCardField(label = "最新-就诊（vist）-现住地-行政区编码", group = "")
    private String lvLivingAddrDistrictCode ;

    /**
     * 最新-就诊（vist）-现住地-行政区
     */
    @ApiModelProperty( "最新-就诊（vist）-现住地-行政区")
    @ReportCardField(label = "最新-就诊（vist）-现住地-行政区", group = "")
    private String lvLivingAddrDistrict ;

    /**
     * 最新-就诊（vist）-现住址-功能区编码
     */
    @ApiModelProperty( "最新-就诊（vist）-现住址-功能区编码")
    @ReportCardField(label = "最新-就诊（vist）-现住址-功能区编码", group = "")
    private String lvLivingAddrFuncDistrictCode ;

    /**
     * 最新-就诊（vist）-现住址-功能区
     */
    @ApiModelProperty( "最新-就诊（vist）-现住址-功能区")
    @ReportCardField(label = "最新-就诊（vist）-现住址-功能区", group = "")
    private String lvLivingAddrFuncDistrict ;

    /**
     * 最新-就诊（vist）-现住址-街道编码
     */
    @ApiModelProperty( "最新-就诊（vist）-现住址-街道编码")
    @ReportCardField(label = "最新-就诊（vist）-现住址-街道编码", group = "")
    private String lvLivingAddrStreetCode ;

    /**
     * 最新-就诊（vist）-现住址-街道
     */
    @ApiModelProperty( "最新-就诊（vist）-现住址-街道")
    @ReportCardField(label = "最新-就诊（vist）-现住址-街道", group = "")
    private String lvLivingAddrStreet ;

    /**
     * 最新-就诊（vist）-现住址-街道中心经度
     */
    @ApiModelProperty( "最新-就诊（vist）-现住址-街道中心经度")
    @ReportCardField(label = "最新-就诊（vist）-现住址-街道中心经度", group = "")
    private Double lvLivingAddrStreetLongitude ;

    /**
     * 最新-就诊（vist）-现住址-街道中心纬度
     */
    @ApiModelProperty( "最新-就诊（vist）-现住址-街道中心纬度")
    @ReportCardField(label = "最新-就诊（vist）-现住址-街道中心纬度", group = "")
    private Double lvLivingAddrStreetLatitude ;

    /**
     * 最新-就诊（vist）-现住址-经度
     */
    @ApiModelProperty( "最新-就诊（vist）-现住址-经度")
    @ReportCardField(label = "最新-就诊（vist）-现住址-经度", group = "")
    private Double lvLivingAddrLongitude ;

    /**
     * 最新-就诊（vist）-现住址-经度
     */
    @ApiModelProperty( "最新-就诊（vist）-现住址-经度")
    @ReportCardField(label = "最新-就诊（vist）-现住址-经度", group = "")
    private Double lvLivingAddrLatitude ;

    /**
     * 最新-就诊（vist）-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）
     */
    @ApiModelProperty( "最新-就诊（vist）-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）")
    @ReportCardField(label = "最新-就诊（vist）-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）", group = "")
    private String lvPersonType ;

    /**
     * 最新-就诊（vist）-职业
     */
    @ApiModelProperty( "最新-就诊（vist）-职业")
    @ReportCardField(label = "最新-就诊（vist）-职业", group = "")
    private String lvJob ;

    /**
     * 最新-就诊（vist）-职业风险
     */
    @ApiModelProperty( "最新-就诊（vist）-职业风险")
    @ReportCardField(label = "最新-就诊（vist）-职业风险", group = "")
    private String lvJobRisk ;

    /**
     * 最新-就诊（vist）-工作单位/学校名称
     */
    @ApiModelProperty( "最新-就诊（vist）-工作单位/学校名称")
    @ReportCardField(label = "最新-就诊（vist）-工作单位/学校名称", group = "")
    private String lvCompany ;

    /**
     * 最新-就诊（vist）-单位/学校标化地址
     */
    @ApiModelProperty( "最新-就诊（vist）-单位/学校标化地址")
    @ReportCardField(label = "最新-就诊（vist）-单位/学校标化地址", group = "")
    private String lvCompanyAddress ;

    /**
     * 最新-就诊（vist）-单位-省编码
     */
    @ApiModelProperty( "最新-就诊（vist）-单位-省编码")
    @ReportCardField(label = "最新-就诊（vist）-单位-省编码", group = "")
    private String lvCompanyProvinceCode ;

    /**
     * 最新-就诊（vist）-单位-省
     */
    @ApiModelProperty( "最新-就诊（vist）-单位-省")
    @ReportCardField(label = "最新-就诊（vist）-单位-省", group = "")
    private String lvCompanyProvince ;

    /**
     * 最新-就诊（vist）-单位-市编码
     */
    @ApiModelProperty( "最新-就诊（vist）-单位-市编码")
    @ReportCardField(label = "最新-就诊（vist）-单位-市编码", group = "")
    private String lvCompanyCityCode ;

    /**
     * 最新-就诊（vist）-单位-市
     */
    @ApiModelProperty( "最新-就诊（vist）-单位-市")
    @ReportCardField(label = "最新-就诊（vist）-单位-市", group = "")
    private String lvCompanyCity ;

    /**
     * 最新-就诊（vist）-单位-行政区编码
     */
    @ApiModelProperty( "最新-就诊（vist）-单位-行政区编码")
    @ReportCardField(label = "最新-就诊（vist）-单位-行政区编码", group = "")
    private String lvCompanyDistrictCode ;

    /**
     * 最新-就诊（vist）-单位-行政区
     */
    @ApiModelProperty( "最新-就诊（vist）-单位-行政区")
    @ReportCardField(label = "最新-就诊（vist）-单位-行政区", group = "")
    private String lvCompanyDistrict ;

    /**
     * 最新-就诊（vist）-单位-功能区编码
     */
    @ApiModelProperty( "最新-就诊（vist）-单位-功能区编码")
    @ReportCardField(label = "最新-就诊（vist）-单位-功能区编码", group = "")
    private String lvCompanyFuncDistrictCode ;

    /**
     * 最新-就诊（vist）-单位-功能区
     */
    @ApiModelProperty( "最新-就诊（vist）-单位-功能区")
    @ReportCardField(label = "最新-就诊（vist）-单位-功能区", group = "")
    private String lvCompanyFunctionDistrict ;

    /**
     * 最新-就诊（vist）-单位-街道编码
     */
    @ApiModelProperty( "最新-就诊（vist）-单位-街道编码")
    @ReportCardField(label = "最新-就诊（vist）-单位-街道编码", group = "")
    private String lvCompanyStreetCode ;

    /**
     * 最新-就诊（vist）-单位-街道
     */
    @ApiModelProperty( "最新-就诊（vist）-单位-街道")
    @ReportCardField(label = "最新-就诊（vist）-单位-街道", group = "")
    private String lvCompanyStreet ;

    /**
     * 最新-就诊（vist）-单位-街道中心经度
     */
    @ApiModelProperty( "最新-就诊（vist）-单位-街道中心经度")
    @ReportCardField(label = "最新-就诊（vist）-单位-街道中心经度", group = "")
    private Double lvCompanyStreetLongitude ;

    /**
     * 最新-就诊（vist）-单位-街道中心维度
     */
    @ApiModelProperty( "最新-就诊（vist）-单位-街道中心维度")
    @ReportCardField(label = "最新-就诊（vist）-单位-街道中心维度", group = "")
    private Double lvCompanyStreetLatitude ;

    /**
     * 最新-就诊（vist）-单位-经度
     */
    @ApiModelProperty( "最新-就诊（vist）-单位-经度")
    @ReportCardField(label = "最新-就诊（vist）-单位-经度", group = "")
    private Double lvCompanyLongitude ;

    /**
     * 最新-就诊（vist）-单位-纬度
     */
    @ApiModelProperty( "最新-就诊（vist）-单位-纬度")
    @ReportCardField(label = "最新-就诊（vist）-单位-纬度", group = "")
    private Double lvCompanyLatitude ;

    /**
     * 最新-就诊（vist）-单位-城乡类型（城市、乡村、未知）
     */
    @ApiModelProperty( "最新-就诊（vist）-单位-城乡类型（城市、乡村、未知）")
    @ReportCardField(label = "最新-就诊（vist）-单位-城乡类型（城市、乡村、未知）", group = "")
    private String lvCompanyAddrType ;

    /**
     * 最新-就诊（vist）-机构id（主索引）
     */
    @ApiModelProperty( "最新-就诊（vist）-机构id（主索引）")
    @ReportCardField(label = "最新-就诊（vist）-机构id（主索引）", group = "")
    private String lvOrgId ;

    /**
     * 最新-就诊（vist）-机构名称
     */
    @ApiModelProperty( "最新-就诊（vist）-机构名称")
    @ReportCardField(label = "最新-就诊（vist）-机构名称", group = "")
    private String lvOrgName ;

    /**
     * 最新-就诊（vist）-机构类别（等级医院、基层医疗）
     */
    @ApiModelProperty( "最新-就诊（vist）-机构类别（等级医院、基层医疗）")
    @ReportCardField(label = "最新-就诊（vist）-机构类别（等级医院、基层医疗）", group = "")
    private String lvOrgClass ;

    /**
     * 最新-就诊（vist）-机构类型名称(204003)
     */
    @ApiModelProperty( "最新-就诊（vist）-机构类型名称(204003)")
    @ReportCardField(label = "最新-就诊（vist）-机构类型名称(204003)", group = "")
    private String lvOrgTypeName ;

    /**
     * 最新-就诊（vist）-机构详细地址
     */
    @ApiModelProperty( "最新-就诊（vist）-机构详细地址")
    @ReportCardField(label = "最新-就诊（vist）-机构详细地址", group = "")
    private String lvOrgAddrDetail ;

    /**
     * 最新-就诊（vist）-机构标化地址
     */
    @ApiModelProperty( "最新-就诊（vist）-机构标化地址")
    @ReportCardField(label = "最新-就诊（vist）-机构标化地址", group = "")
    private String lvOrgAddrDetailStd ;

    /**
     * 最新-就诊（vist）-机构-城乡类型（城市、乡村、未知）
     */
    @ApiModelProperty( "最新-就诊（vist）-机构-城乡类型（城市、乡村、未知）")
    @ReportCardField(label = "最新-就诊（vist）-机构-城乡类型（城市、乡村、未知）", group = "")
    private String lvOrgAddrType ;

    /**
     * 最新-就诊（vist）-机构-省编码
     */
    @ApiModelProperty( "最新-就诊（vist）-机构-省编码")
    @ReportCardField(label = "最新-就诊（vist）-机构-省编码", group = "")
    private String lvOrgAddrProvinceCode ;

    /**
     * 最新-就诊（vist）-机构-省
     */
    @ApiModelProperty( "最新-就诊（vist）-机构-省")
    @ReportCardField(label = "最新-就诊（vist）-机构-省", group = "")
    private String lvOrgAddrProvince ;

    /**
     * 最新-就诊（vist）-机构-市编码
     */
    @ApiModelProperty( "最新-就诊（vist）-机构-市编码")
    @ReportCardField(label = "最新-就诊（vist）-机构-市编码", group = "")
    private String lvOrgAddrCityCode ;

    /**
     * 最新-就诊（vist）-机构-市
     */
    @ApiModelProperty( "最新-就诊（vist）-机构-市")
    @ReportCardField(label = "最新-就诊（vist）-机构-市", group = "")
    private String lvOrgAddrCity ;

    /**
     * 最新-就诊（vist）-机构-行政区编码
     */
    @ApiModelProperty( "最新-就诊（vist）-机构-行政区编码")
    @ReportCardField(label = "最新-就诊（vist）-机构-行政区编码", group = "")
    private String lvOrgAddrDistrictCode ;

    /**
     * 最新-就诊（vist）-机构-行政区
     */
    @ApiModelProperty( "最新-就诊（vist）-机构-行政区")
    @ReportCardField(label = "最新-就诊（vist）-机构-行政区", group = "")
    private String lvOrgAddrDistrict ;

    /**
     * 最新-就诊（vist）-机构-功能区编码
     */
    @ApiModelProperty( "最新-就诊（vist）-机构-功能区编码")
    @ReportCardField(label = "最新-就诊（vist）-机构-功能区编码", group = "")
    private String lvOrgAddrFunctionDistrictCode ;

    /**
     * 最新-就诊（vist）-机构-功能区
     */
    @ApiModelProperty( "最新-就诊（vist）-机构-功能区")
    @ReportCardField(label = "最新-就诊（vist）-机构-功能区", group = "")
    private String lvOrgAddrFunctionDistrict ;

    /**
     * 最新-就诊（vist）-机构-街道编码
     */
    @ApiModelProperty( "最新-就诊（vist）-机构-街道编码")
    @ReportCardField(label = "最新-就诊（vist）-机构-街道编码", group = "")
    private String lvOrgAddrStreetCode ;

    /**
     * 最新-就诊（vist）-机构-街道
     */
    @ApiModelProperty( "最新-就诊（vist）-机构-街道")
    @ReportCardField(label = "最新-就诊（vist）-机构-街道", group = "")
    private String lvOrgAddrStreet ;

    /**
     * 最新-就诊（vist）-机构经度
     */
    @ApiModelProperty( "最新-就诊（vist）-机构经度")
    @ReportCardField(label = "最新-就诊（vist）-机构经度", group = "")
    private Double lvOrgLongitude ;

    /**
     * 最新-就诊（vist）-机构纬度
     */
    @ApiModelProperty( "最新-就诊（vist）-机构纬度")
    @ReportCardField(label = "最新-就诊（vist）-机构纬度", group = "")
    private Double lvOrgLatitude ;

    /**
     * 最新-就诊（vist）-就诊时间
     */
    @ApiModelProperty( "最新-就诊（vist）-就诊时间")
    @ReportCardField(label = "最新-就诊（vist）-就诊时间", group = "", dateFormat = "yyyy-MM-dd HH:mm")
    private Date lvVisitTime ;

    /**
     * 最新-就诊（vist）-就诊所在日
     */
    @ApiModelProperty( "最新-就诊（vist）-就诊所在日")
    @ReportCardField(label = "最新-就诊（vist）-就诊所在日", group = "", dateFormat = "yyyy-MM-dd HH:mm")
    private Date lvVisitDay ;

    /**
     * 最新-就诊（vist）-就诊科室编码
     */
    @ApiModelProperty( "最新-就诊（vist）-就诊科室编码")
    @ReportCardField(label = "最新-就诊（vist）-就诊科室编码", group = "")
    private String lvDeptCode ;

    /**
     * 最新-就诊（vist）-就诊科室
     */
    @ApiModelProperty( "最新-就诊（vist）-就诊科室")
    @ReportCardField(label = "最新-就诊（vist）-就诊科室", group = "")
    private String lvDeptName ;

    /**
     * 最新-就诊（vist）-主诉
     */
    @ApiModelProperty( "最新-就诊（vist）-主诉")
    @ReportCardField(label = "最新-就诊（vist）-主诉", group = "")
    private String lvSuit ;

    /**
     * 最新-就诊（vist）-症状
     */
    @ApiModelProperty( "最新-就诊（vist）-症状")
    @ReportCardField(label = "最新-就诊（vist）-症状", group = "")
    private String lvSymptom ;

    /**
     * 最新-就诊（vist）-诊断
     */
    @ApiModelProperty( "最新-就诊（vist）-诊断")
    @ReportCardField(label = "最新-就诊（vist）-诊断", group = "")
    private String lvDiag ;

    /**
     * 最新-就诊（vist）-标化诊断
     */
    @ApiModelProperty( "最新-就诊（vist）-标化诊断")
    @ReportCardField(label = "最新-就诊（vist）-标化诊断", group = "")
    private String lvDiagStd ;

    /**
     * 最新-识别（identify）-报告时间
     */
    @ApiModelProperty( "最新-识别（identify）-报告时间")
    @ReportCardField(label = "最新-识别（identify）-报告时间", group = "", dateFormat = "yyyy-MM-dd HH:mm")
    private Date liIdentifyTime ;

    /**
     * 最新-识别（identify）-报告疾病编码（传染病编码/症候群编码）
     */
    @ApiModelProperty( "最新-识别（identify）-报告疾病编码（传染病编码/症候群编码）")
    @ReportCardField(label = "最新-识别（identify）-报告疾病编码（传染病编码/症候群编码）", group = "")
    private String liDiseaseCode ;

    /**
     * 最新-识别（identify）-报告疾病名称（传染病名称/症候群名称）
     */
    @ApiModelProperty( "最新-识别（identify）-报告疾病名称（传染病名称/症候群名称）")
    @ReportCardField(label = "最新-识别（identify）-报告疾病名称（传染病名称/症候群名称）", group = "")
    private String liDiseaseName ;

    /**
     * 最新-识别（identify）-监测结果依据类型（诊断、检查、检验等）
     */
    @ApiModelProperty( "最新-识别（identify）-监测结果依据类型（诊断、检查、检验等）")
    @ReportCardField(label = "最新-识别（identify）-监测结果依据类型（诊断、检查、检验等）", group = "")
    private String liSupport ;

    /**
     * 最新-识别（identify）-报告类型（系统报告、人工修正）
     */
    @ApiModelProperty( "最新-识别（identify）-报告类型（系统报告、人工修正）")
    @ReportCardField(label = "最新-识别（identify）-报告类型（系统报告、人工修正）", group = "")
    private String liType ;

    /**
     * 最新-识别（identify）-传染病分类（甲类、乙类、丙类、其它法定、其它非法定）
     */
    @ApiModelProperty( "最新-识别（identify）-传染病分类（甲类、乙类、丙类、其它法定、其它非法定）")
    @ReportCardField(label = "最新-识别（identify）-传染病分类（甲类、乙类、丙类、其它法定、其它非法定）", group = "")
    private String liInfectType ;

    /**
     * 最新-识别（identify）-传染病传播途径分类（（肠道传染病、呼吸道症候群、自然疫源及虫煤传染病、血源及性传播传染病））
     */
    @ApiModelProperty( "最新-识别（identify）-传染病传播途径分类（（肠道传染病、呼吸道症候群、自然疫源及虫煤传染病、血源及性传播传染病））")
    @ReportCardField(label = "最新-识别（identify）-传染病传播途径分类（（肠道传染病、呼吸道症候群、自然疫源及虫煤传染病、血源及性传播传染病））", group = "")
    private String liInfectTransmit ;

    /**
     * 最新-识别（identify）-报告机构编码
     */
    @ApiModelProperty( "最新-识别（identify）-报告机构编码")
    @ReportCardField(label = "最新-识别（identify）-报告机构编码", group = "")
    private String liOrgId ;

    /**
     * 最新-识别（identify）-报告机构名称
     */
    @ApiModelProperty( "最新-识别（identify）-报告机构名称")
    @ReportCardField(label = "最新-识别（identify）-报告机构名称", group = "")
    private String liOrgName ;

    /**
     * 最新-识别（identify）-审核时间
     */
    @ApiModelProperty( "最新-识别（identify）-审核时间")
    @ReportCardField(label = "最新-识别（identify）-审核时间", group = "", dateFormat = "yyyy-MM-dd HH:mm")
    private Date liCheckTime ;

    /**
     * 最新-转归（outcome）-转归时间
     */
    @ApiModelProperty( "最新-转归（outcome）-转归时间")
    @ReportCardField(label = "最新-转归（outcome）-转归时间", group = "", dateFormat = "yyyy-MM-dd HH:mm")
    private Date loOutcomeTime ;

    /**
     * 最新-转归（outcome）-转归状态（治愈、治疗成功、死亡、带病）
     */
    @ApiModelProperty( "最新-转归（outcome）-转归状态（治愈、治疗成功、死亡、带病）")
    @ReportCardField(label = "最新-转归（outcome）-转归状态（治愈、治疗成功、死亡、带病）", group = "")
    private String loOutcomeStatus ;

    /**
     * 最新-转归（outcome）-死亡原因（详细的，因该病死亡，其他）
     */
    @ApiModelProperty( "最新-转归（outcome）-死亡原因（详细的，因该病死亡，其他）")
    @ReportCardField(label = "最新-转归（outcome）-死亡原因（详细的，因该病死亡，其他）", group = "")
    private String loDeadReason ;

    /**
     * 总体-识别次数
     */
    @ApiModelProperty( "总体-识别次数")
    @ReportCardField(label = "总体-识别次数", group = "")
    private Integer identifyCnt ;

    /**
     * 最新-识别（identify）-审核结果（1是，0否）
     */
    @ApiModelProperty( "最新-识别（identify）-审核结果（1是，0否）")
    @ReportCardField(label = "最新-识别（identify）-审核结果（1是，0否）", group = "")
    private String liCheckResult ;

    /**
     * 最新-就诊（vist）-病历id
     */
    @ApiModelProperty( "最新-就诊（vist）-病历id")
    @ReportCardField(label = "最新-就诊（vist）-病历id", group = "")
    private String lvMedicalId ;

    /**
     * 最新-就诊（vist）-主诊断编码
     */
    @ApiModelProperty( "最新-就诊（vist）-主诊断编码")
    @ReportCardField(label = "最新-就诊（vist）-主诊断编码", group = "")
    private String lvMainDiagCode ;

    /**
     * 最新-就诊（vist）-主诊断
     */
    @ApiModelProperty( "最新-就诊（vist）-主诊断")
    @ReportCardField(label = "最新-就诊（vist）-主诊断", group = "")
    private String lvMainDiag ;

    /**
     * 最新-就诊（vist）-标准主诊断编码
     */
    @ApiModelProperty( "最新-就诊（vist）-标准主诊断编码")
    @ReportCardField(label = "最新-就诊（vist）-标准主诊断编码", group = "")
    private String lvMainDiagStdCode ;

    /**
     * 最新-就诊（vist）-标准主诊断
     */
    @ApiModelProperty( "最新-就诊（vist）-标准主诊断")
    @ReportCardField(label = "最新-就诊（vist）-标准主诊断", group = "")
    private String lvMainDiagStd ;

    /**
     * 最新-就诊（vist）-主诊断类型（确诊、疑似）
     */
    @ApiModelProperty( "最新-就诊（vist）-主诊断类型（确诊、疑似）")
    @ReportCardField(label = "最新-就诊（vist）-主诊断类型（确诊、疑似）", group = "")
    private String lvMainDiagType ;

    /**
     * 最新-就诊（vist）-诊断信息（json，诊断编码，诊断名称，主诊断标识，诊断类型（疑似诊断、确诊诊断？），诊断疾病类型（急性、慢性），标准诊断编码，标准诊断名称）
     */
    @ApiModelProperty( "最新-就诊（vist）-诊断信息（json，诊断编码，诊断名称，主诊断标识，诊断类型（疑似诊断、确诊诊断？），诊断疾病类型（急性、慢性），标准诊断编码，标准诊断名称）")
    @ReportCardField(label = "最新-就诊（vist）-诊断信息（json，诊断编码，诊断名称，主诊断标识，诊断类型（疑似诊断、确诊诊断？），诊断疾病类型（急性、慢性），标准诊断编码，标准诊断名称）", group = "")
    private String lvDiagJson ;

    /**
     * 最新-就诊（vist）-信息完整性标识（1是0否）
     */
    @ApiModelProperty( "最新-就诊（vist）-信息完整性标识（1是0否）")
    @ReportCardField(label = "最新-就诊（vist）-信息完整性标识（1是0否）", group = "")
    private String lvFullFlag ;

    /**
     * 最新-识别（identify）-就诊的病历识别id
     */
    @ApiModelProperty( "最新-识别（identify）-就诊的病历识别id")
    @ReportCardField(label = "最新-识别（identify）-就诊的病历识别id", group = "")
    private String liIdentifyId ;

    /**
     * 最新-识别（identify）-报告异常类别（症候群病例、传染病病例）
     */
    @ApiModelProperty( "最新-识别（identify）-报告异常类别（症候群病例、传染病病例）")
    @ReportCardField(label = "最新-识别（identify）-报告异常类别（症候群病例、传染病病例）", group = "")
    private String liDiseaseType ;

    /**
     * 最新-识别（identify）-及时性标识（0未执行、1及时、2延时，报告时间 - 诊断时间 根据传染病维表的时效性要求判断）
     */
    @ApiModelProperty( "最新-识别（identify）-及时性标识（0未执行、1及时、2延时，报告时间 - 诊断时间 根据传染病维表的时效性要求判断）")
    @ReportCardField(label = "最新-识别（identify）-及时性标识（0未执行、1及时、2延时，报告时间 - 诊断时间 根据传染病维表的时效性要求判断）", group = "")
    private String liIntimeFlag ;

    /**
     * 最新-总体（total）-质量合格标识（1是，0否）
     */
    @ApiModelProperty( "最新-总体（total）-质量合格标识（1是，0否）")
    @ReportCardField(label = "最新-总体（total）-质量合格标识（1是，0否）", group = "")
    private String ltConformFlag ;

}