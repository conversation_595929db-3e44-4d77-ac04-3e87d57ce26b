package com.iflytek.cdc.province.entity.ads;

import java.io.Serializable;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 数据接入(data integration)-接入机构详情;
 */
@ApiModel(value = "数据接入(data integration)-接入机构详情")
@Data
public class AdsDiInorgInfo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * ETL创建时间
     */
    @ApiModelProperty( "ETL创建时间")
    private Date etlCreateDatetime ;

    /**
     * ETL更新时间
     */
    @ApiModelProperty( "ETL更新时间")
    private Date etlUpdateDatetime ;

    /**
     * 机构-省编码
     */
    @ApiModelProperty( "机构-省编码")
    private String orgAddrProvinceCode ;

    /**
     * 机构-省
     */
    @ApiModelProperty( "机构-省")
    private String orgAddrProvince ;

    /**
     * 机构-市编码
     */
    @ApiModelProperty( "机构-市编码")
    private String orgAddrCityCode ;

    /**
     * 机构-市
     */
    @ApiModelProperty( "机构-市")
    private String orgAddrCity ;

    /**
     * 机构-行政区编码
     */
    @ApiModelProperty( "机构-行政区编码")
    private String orgAddrDistrictCode ;

    /**
     * 机构-行政区
     */
    @ApiModelProperty( "机构-行政区")
    private String orgAddrDistrict ;

    /**
     * 机构-功能区编码
     */
    @ApiModelProperty( "机构-功能区编码")
    private String orgAddrFuncDistrictCode;

    /**
     * 机构-功能区
     */
    @ApiModelProperty( "机构-功能区")
    private String orgAddrFuncDistrict;

    /**
     * 机构-街道编码
     */
    @ApiModelProperty( "机构-街道编码")
    private String orgAddrStreetCode ;

    /**
     * 机构-街道
     */
    @ApiModelProperty( "机构-街道")
    private String orgAddrStreet ;

    /**
     * 机构编码
     */
    @ApiModelProperty( "机构编码")
    private String orgId ;

    /**
     * 机构名称
     */
    @ApiModelProperty( "机构名称")
    private String orgName ;

    /**
     * 机构类型（等级医院/基层医疗机构/病原实验室）
     */
    @ApiModelProperty( "机构类型（等级医院/基层医疗机构/病原实验室）")
    private String orgType ;

    /**
     * 根机构编码
     */
    @ApiModelProperty( "根机构编码")
    private String rootOrgId ;

    /**
     * 根机构名称
     */
    @ApiModelProperty( "根机构名称")
    private String rootOrgName ;

    /**
     * 管辖机构编码
     */
    @ApiModelProperty( "管辖机构编码")
    private String manaOrgId ;

    /**
     * 管辖机构名称
     */
    @ApiModelProperty( "管辖机构名称")
    private String manaOrgName ;

    /**
     * 对接状态（未对接、正在对接、已对接、不对接）
     */
    @ApiModelProperty( "对接状态（未对接、正在对接、已对接、不对接）")
    private String inStatus ;

}