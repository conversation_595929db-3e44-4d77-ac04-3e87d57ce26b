package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 旅居史信息
 */
@Data
@TableName(value = "ads.ads_edr_travel_history_info")
public class AdsEdrTravelHistoryInfo {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 采集创建时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * 采集更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;

    /**
     * 事件信息ID
     */
    @TableField(value = "event_id")
    private String eventId;

    /**
     * 主索引ID
     */
    @TableField(value = "empi_id")
    private String empiId;

    /**
     * 创建机构
     */
    @TableField(value = "create_org")
    private String createOrg;

    /**
     * 记录医疗机构名称
     */
    @TableField(value = "create_org_name")
    private String createOrgName;

    /**
     * 输入病例类型代码
     */
    @TableField(value = "foreign_type_code")
    private String foreignTypeCode;

    /**
     * 输入病例类型名称
     */
    @TableField(value = "foreign_type")
    private String foreignType;

    /**
     * 输入来源地代码
     */
    @TableField(value = "place_code")
    private String placeCode;

    /**
     * 输入来源地名称
     */
    @TableField(value = "place")
    private String place;

    /**
     * 发病时在现住址居住时间
     */
    @TableField(value = "residence_days")
    private Integer residenceDays;

    /**
     * 疫源风险
     */
    @TableField(value = "epidemic_source_risk_code")
    private String epidemicSourceRiskCode;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 数据来源ID
     */
    @TableField(value = "source_id")
    private String sourceId;

    /**
     * 主索引ID
     */
    @TableField(value = "mpi_id")
    private String mpiId;

    /**
     * 修改机构
     */
    @TableField(value = "update_org")
    private String updateOrg;

    /**
     * 修改机构名称
     */
    @TableField(value = "update_org_name")
    private String updateOrgName;

    /**
     * 医疗机构(机构主索引)-记录医疗机构代码
     */
    @TableField(value = "create_org_id")
    private String createOrgId;
}