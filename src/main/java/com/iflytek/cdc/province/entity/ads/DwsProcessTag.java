package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "dws.dws_process_tag")
public class DwsProcessTag {
    @TableId(value = "tag_id", type = IdType.INPUT)
    private String tagId;

    @TableField(value = "source_id")
    private String sourceId;

    @TableField(value = "empi_id")
    private String empiId;

    @TableField(value = "tag")
    private String tag;

    @TableField(value = "tag_value")
    private String tagValue;

    @TableField(value = "occur_time")
    private Date occurTime;

    @TableField(value = "tag_category")
    private String tagCategory;

    @TableField(value = "tag_source")
    private String tagSource;

    @TableField(value = "remark")
    private String remark;

    @TableField(value = "enabled")
    private String enabled;

    @TableField(value = "etl_create_time")
    private Date etlCreateTime;

    @TableField(value = "etl_update_time")
    private Date etlUpdateTime;

    @TableField(value = "event_id")
    private String eventId;
}