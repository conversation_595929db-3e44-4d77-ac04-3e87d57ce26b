package com.iflytek.cdc.province.entity.bu;

import com.baomidou.mybatisplus.annotation.TableName;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import com.iflytek.cdc.province.entity.ads.AdsMsInfectReportInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "tb_cdcew_report_qcs_card_info", schema = "app")
@ApiModel(value = "ReportQcsCardInfo对象")
public class ReportQcsCardInfo implements Serializable {

    public static final String TB_NAME = "tb_cdcew_report_qcs_card_info";
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "创建者id")
    private String creatorId;

    @ApiModelProperty(value = "创建者")
    private String creator;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改者id")
    private String updaterId;

    @ApiModelProperty(value = "更新者")
    private String updater;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "删除标志 '1' 已删除 '0' 未删除")
    private String deleteFlag;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "主记录ID（外键）")
    private String mainRecordId;

    @ApiModelProperty(value = "数仓的病程ID")
    private String processId;

    @ApiModelProperty(value = "数仓的报告卡ID")
    private String reportId;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "患者性别")
    private String sexName;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "年龄单位")
    private String ageUnit;

    @ApiModelProperty(value = "所属地区省级编码")
    private String livingAddrProvinceCode;

    @ApiModelProperty(value = "所属地区省级名称")
    private String livingAddrProvince;

    @ApiModelProperty(value = "所属地区市级编码")
    private String livingAddrCityCode;

    @ApiModelProperty(value = "所属地区市级名称")
    private String livingAddrCity;

    @ApiModelProperty(value = "所属地区区级编码")
    private String livingAddrDistrictCode;

    @ApiModelProperty(value = "所属地区区级名称")
    private String livingAddrDistrict;

    @ApiModelProperty(value = "所属功能区编码")
    private String livingAddrFuncDistrictCode;

    @ApiModelProperty(value = "所属功能区名称")
    private String livingAddrFuncDistrict;

    @ApiModelProperty(value = "所属街道编码")
    private String livingAddrStreetCode;

    @ApiModelProperty(value = "所属街道名称")
    private String livingAddrStreet;

    @ApiModelProperty(value = "发病日期")
    private Date onsetTime;

    @ApiModelProperty(value = "首次就诊时间")
    private Date firstVisitTime;

    @ApiModelProperty(value = "首次就诊机构ID")
    private String orgId;

    @ApiModelProperty(value = "首次就诊机构名称")
    private String orgName;

    @ApiModelProperty(value = "就诊机构所在省级编码")
    private String orgAddrProvinceCode;

    @ApiModelProperty(value = "就诊机构所在省级名称")
    private String orgAddrProvince;

    @ApiModelProperty(value = "就诊机构所在市级编码")
    private String orgAddrCityCode;

    @ApiModelProperty(value = "就诊机构所在市级名称")
    private String orgAddrCity;

    @ApiModelProperty(value = "就诊机构所在区级编码")
    private String orgAddrDistrictCode;

    @ApiModelProperty(value = "就诊机构所在区级名称")
    private String orgAddrDistrict;

    @ApiModelProperty(value = "就诊机构所在功能区编码")
    private String orgAddrFuncDistrictCode;

    @ApiModelProperty(value = "就诊机构所在功能区名称")
    private String orgAddrFuncDistrict;

    @ApiModelProperty(value = "就诊机构所在街道编码")
    private String orgAddrStreetCode;

    @ApiModelProperty(value = "就诊机构所在街道名称")
    private String orgAddrStreet;

    @ApiModelProperty(value = "疾病名称")
    private String diseaseName;

    @ApiModelProperty(value = "疾病编码")
    private String diseaseCode;

    @ApiModelProperty(value = "诊断时间")
    private Date diagTime;

    @ApiModelProperty(value = "证件类型")
    private String patientIdentityType;

    @ApiModelProperty(value = "证件号码")
    private String patientIdentityNo;

    @ApiModelProperty(value = "出生日期")
    private Date patientBirthDay;

    @ApiModelProperty(value = "死亡时间")
    private Date deadTime;

    @ApiModelProperty(value = "工作单位")
    private String company;

    @ApiModelProperty(value = "联系电话")
    private String patientPhone;

    @ApiModelProperty(value = "现住地址")
    private String livingAddrDetail;

    @ApiModelProperty(value = "人群分类")
    private String personType;

    @ApiModelProperty(value = "诊断状态")
    private String identifyClass;

    @ApiModelProperty(value = "就诊类型")
    private String visitTypeName;

    @ApiModelProperty(value = "初诊标识")
    private String firstDiagFlag;


    public static ReportQcsCardInfo transformInfectReport(AdsMsInfectReportInfo reportInfo, String mainRecordId, UapUserPo uapUserPo, String tableId) {
        ReportQcsCardInfo cardInfo = new ReportQcsCardInfo();
        BeanUtils.copyProperties(reportInfo, cardInfo);

        cardInfo.setDiseaseCode(reportInfo.getInfectCode());
        cardInfo.setDiseaseName(reportInfo.getInfectName());
        cardInfo.setProcessId(reportInfo.getInfectProcessId());
        cardInfo.setReportId(reportInfo.getInfectReportId());
        cardInfo.setSexName(reportInfo.getPatientSexName());
        cardInfo.setAge(reportInfo.getPatientAge());
        cardInfo.setAgeUnit(reportInfo.getPatientAgeUnit());
        if (reportInfo.getVisitTime() != null) {
            cardInfo.setFirstVisitTime(reportInfo.getVisitTime());
        }

        cardInfo.setMainRecordId(mainRecordId);
        cardInfo.setId(tableId);
        if (Objects.nonNull(uapUserPo)) {
            cardInfo.setCreator(uapUserPo.getName());
            cardInfo.setCreatorId(uapUserPo.getId());
            cardInfo.setCreateTime(new Date());
            cardInfo.setUpdater(uapUserPo.getName());
            cardInfo.setUpdaterId(uapUserPo.getId());
            cardInfo.setUpdateTime(new Date());
        }
        cardInfo.setDeleteFlag("0");

        return cardInfo;
    }
}
