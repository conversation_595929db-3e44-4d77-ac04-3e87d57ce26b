package com.iflytek.cdc.province.entity.bu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 表单页面配置表
 */
@ApiModel(description="表单页面配置表")
@Data
public class TbCdcdmDataFormTemplate implements Serializable {
    /**
    * 主键ID
    */
    @ApiModelProperty(value="主键ID")
    private String formTemplateDetailId;

    /**
    * 页面模板代码
    */
    @ApiModelProperty(value="页面模板代码")
    private String formTemplateCode;

    /**
    * 页面模板名称
    */
    @ApiModelProperty(value="页面模板名称")
    private String formTemplateName;

    /**
    * 配置信息
    */
    @ApiModelProperty(value="配置信息")
    private String configInfo;

    /**
    * 配置json
    */
    @ApiModelProperty(value="配置json")
    private String configJson;

    /**
    * 数据模型ID
    */
    @ApiModelProperty(value="数据模型ID")
    private String modelId;

    /**
     * 数据模型名称
     */
    @ApiModelProperty(value="数据模型名称")
    private String modelName;

    /**
    * 数据模型版本ID
    */
    @ApiModelProperty(value="数据模型版本ID")
    private String modelVersionId;

    /**
    * 是否启用
    */
    @ApiModelProperty(value="是否启用")
    private Integer isEnable;

    /**
    * 是否删除
    */
    @ApiModelProperty(value="是否删除")
    private Integer isDeleted;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
    * 创建人
    */
    @ApiModelProperty(value="创建人")
    private String creator;

    /**
    * 修改时间
    */
    @ApiModelProperty(value="修改时间")
    private Date updateTime;

    /**
    * 修改人
    */
    @ApiModelProperty(value="修改人")
    private String updater;

    /**
     * 模型使用关键字段，用于串联该模型下所有的表
     */
    @ApiModelProperty(value="模型使用关键字段，用于串联该模型下所有的表")
    private String keyField;

    /**
     * 数据搜索时主表id
     */
    @ApiModelProperty(value = "数据搜索时主表id")
    private String masterTableId ;

    /**
     * 数据搜索时主表
     */
    @ApiModelProperty(value = "数据搜索时主表")
    private String masterTable ;

    /**
     * 该数据模型展示字段(主表中不可变字段)
     */
    @ApiModelProperty(value = "该数据配置信息")
    private String fieldsConfig ;

    /**
     * 数据模型属于该页面的某一个组
     */
    @ApiModelProperty(value = "数据模型属于该页面的某一个组")
    private String modelGroup ;

    /**
     * 前端配置
     */
    @ApiModelProperty(value = "前端配置")
    private String webConfig ;

    /**
     * 关联疾病（eg:症候群对应具体的数据模型配置）
     */
    @ApiModelProperty(value = "关联疾病（eg:症候群对应具体的数据模型配置）")
    private String relatedDisease ;

    /**
     * 业务键（进行业务查询时使用的主键）
     */
    @ApiModelProperty(value = "业务键（进行业务查询时使用的主键）")
    private String businessKey ;

    /**
     * 检索表
     */
    @ApiModelProperty(value = "检索表")
    private String retrieveTable ;

    @ApiModelProperty("es 索引名")
    private String esIndexName;

    private static final long serialVersionUID = 1L;
}