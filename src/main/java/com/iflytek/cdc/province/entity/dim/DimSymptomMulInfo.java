package com.iflytek.cdc.province.entity.dim;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 解析症状维表（基于临床、检查、检验等特异标签的解析结果生成的维表）
 */
@Data
@TableName(value = "dim.dim_symptom_mul_info")
public class DimSymptomMulInfo {
    /**
     * 标签名称
     */
    @TableId(value = "tag", type = IdType.INPUT)
    private String tag;

    /**
     * 标签属性类型（1定量、2定性）
     */
    @TableId(value = "tag_value_type", type = IdType.INPUT)
    private String tagValueType;

    /**
     * 解析出的此标签对应的值或描述
     */
    @TableId(value = "tag_value", type = IdType.INPUT)
    private String tagValue;

    /**
     * 数据源（就诊类型名称-业务表简称-业务类简称【-字段简称】，如住院-入院记录-体格检查-体温）
     */
    @TableId(value = "data_source", type = IdType.INPUT)
    private String dataSource;

    /**
     * 标签类别（1临床、2检查、3检验）
     */
    @TableId(value = "tag_class", type = IdType.INPUT)
    private String tagClass;

    /**
     * id
     */
    @TableField(value = "id")
    private String id;

    /**
     * ETL创建时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * ETL更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;
}