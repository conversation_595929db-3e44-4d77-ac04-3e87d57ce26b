package com.iflytek.cdc.province.entity.ads;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 数据接入(data integration)-接入数据统计-日表;
 */
@ApiModel(value = "数据接入(data integration)-接入数据统计-日表")
@Data
public class AdsDiIndataStatD implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * id(uuid)
     */
    @ApiModelProperty( "id")
    private String id ;

    /**
     * ETL创建时间
     */
    @ApiModelProperty( "ETL创建时间")
    private Date etlCreateDatetime ;

    /**
     * ETL更新时间
     */
    @ApiModelProperty( "ETL更新时间")
    private Date etlUpdateDatetime ;

    /**
     * 统计期所在年
     */
    @ApiModelProperty( "统计期所在年")
    private Integer year ;

    /**
     * 统计期所在季度
     */
    @ApiModelProperty( "统计期所在季度")
    private Integer quarter ;

    /**
     * 统计期所在月
     */
    @ApiModelProperty( "统计期所在月")
    private Integer month ;

    /**
     * 统计期所在旬类型（上、中、下）
     */
    @ApiModelProperty( "统计期所在旬类型（上、中、下）")
    private String tenDay ;

    /**
     * 统计期所在周（年第几周）
     */
    @ApiModelProperty( "统计期所在周（年第几周）")
    private Integer week ;

    /**
     * 统计期所在周周一
     */
    @ApiModelProperty( "统计期所在周周一")
    private Date monday ;

    /**
     * 统计日期
     */
    @ApiModelProperty( "统计日期")
    private Date day ;

    /**
     * 机构类型（等级医院/基层医疗机构/病原实验室）
     */
    @ApiModelProperty( "机构类型（等级医院/基层医疗机构/病原实验室）")
    private String orgType ;

    /**
     * 门诊病历数-今日
     */
    @ApiModelProperty( "门诊病历数-今日")
    private Integer medicalOutpatCnt ;

    /**
     * 门诊病历数-去年同期
     */
    @ApiModelProperty( "门诊病历数-去年同期")
    private Integer medicalOutpatCntLastY ;

    /**
     * 门诊病历数-昨日
     */
    @ApiModelProperty( "门诊病历数-昨日")
    private Integer medicalOutpatCntLast ;

    /**
     * 门诊病历数-累计
     */
    @ApiModelProperty( "门诊病历数-累计")
    private Integer medicalOutpatCntTotal ;

    /**
     * 住院病历数-今日
     */
    @ApiModelProperty( "住院病历数-今日")
    private Integer medicalInpatCnt ;

    /**
     * 住院病历数-去年同期
     */
    @ApiModelProperty( "住院病历数-去年同期")
    private Integer medicalInpatCntLastY ;

    /**
     * 住院病历数-昨日
     */
    @ApiModelProperty( "住院病历数-昨日")
    private Integer medicalInpatCntLast ;

    /**
     * 住院病历数-累计
     */
    @ApiModelProperty( "住院病历数-累计")
    private Integer medicalInpatCntTotal ;

    /**
     * 病历数-今日
     */
    @ApiModelProperty( "病历数-今日")
    private Integer medicalCnt ;

    /**
     * 病历数-去年同期
     */
    @ApiModelProperty( "病历数-去年同期")
    private Integer medicalCntLastY ;

    /**
     * 病历数-昨日
     */
    @ApiModelProperty( "病历数-昨日")
    private Integer medicalCntLast ;

    /**
     * 病历数-累计
     */
    @ApiModelProperty( "病历数-累计")
    private Integer medicalCntTotal ;

    /**
     * 全局患者量-今日
     */
    @ApiModelProperty( "全局患者量-今日")
    private Integer globalPatientCnt ;

    /**
     * 全局患者量-去年同期
     */
    @ApiModelProperty( "全局患者量-去年同期")
    private Integer globalPatientCntLastY ;

    /**
     * 全局患者量-昨日
     */
    @ApiModelProperty( "全局患者量-昨日")
    private Integer globalPatientCntLast ;

    /**
     * 全局患者量-累计
     */
    @ApiModelProperty( "全局患者量-累计")
    private Integer globalPatientCntTotal ;

}