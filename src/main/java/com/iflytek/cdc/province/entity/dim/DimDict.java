package com.iflytek.cdc.province.entity.dim;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 字典表
 */
@Data
@TableName(value = "dim.dim_dict")
public class DimDict {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 字典种类
     */
    @TableField(value = "dict_category")
    private String dictCategory;

    /**
     * 字典类型
     */
    @TableField(value = "dict_type")
    private String dictType;

    /**
     * 字典编码
     */
    @TableField(value = "dict_code")
    private String dictCode;

    /**
     * 字典名称
     */
    @TableField(value = "dict_name")
    private String dictName;

    /**
     * 字典值编码
     */
    @TableField(value = "dict_value_code")
    private String dictValueCode;

    /**
     * 字典值名称
     */
    @TableField(value = "dict_value_name")
    private String dictValueName;

    /**
     * 字典来源
     */
    @TableField(value = "dict_source")
    private String dictSource;

    /**
     * 备注
     */
    @TableField(value = "memo")
    private String memo;

    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;

    /**
     * 父级代码
     */
    @TableField(value = "parent_coded_value")
    private String parentCodedValue;
}