package com.iflytek.cdc.province.entity.bu;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 新冠病毒测序明细
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "tb_coronavirus_sample_collection")
public class TbCoronavirusSampleCollection implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final String TB_NAME = "tb_coronavirus_sample_collection";

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "实验室名称")
    private String laboratoryName;

    @ApiModelProperty(value = "标本类型")
    private String specimenType;

    @ApiModelProperty(value = "采样日期")
    private Date collectTime;

    @ApiModelProperty(value = "标本来源")
    private String specimenSource;

    @ApiModelProperty(value = "检测日期")
    private Date inspectTime;

    @ApiModelProperty(value = "试剂厂家")
    private String reagentVerder;

    @ApiModelProperty(value = "ORF1ab Ct值")
    private Double orf1abct;

    @ApiModelProperty(value = "N Ct值")
    private Double nct;

    @ApiModelProperty(value = "测序日期")
    private Date checkSequenceDate;

    @ApiModelProperty(value = "测序方法")
    private String checkSeqMethod;

    @ApiModelProperty(value = "序列覆盖度")
    private Double seqCoverRate;

    @ApiModelProperty(value = "序列对比结果")
    private String seqCompareResult;

    @ApiModelProperty(value = "病毒亚型")
    private String subtypeName;


    @ApiModelProperty(value = "创建者id")
    private String creatorId;


    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改者id")
    private String updaterId;


    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
