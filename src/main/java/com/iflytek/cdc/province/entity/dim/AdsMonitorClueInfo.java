package com.iflytek.cdc.province.entity.dim;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 监测线索详情表
 */
@Data
@TableName(value = "ads.ads_monitor_clue_info")
public class AdsMonitorClueInfo {
    /**
     * 线索id-主键
     */
    @TableId(value = "clue_id", type = IdType.INPUT)
    private String clueId;

    /**
     * 类型(药品销量暴增,搜索指数暴增,热门话题上榜,输入病例公告)
     */
    @TableId(value = "type", type = IdType.INPUT)
    private String type;

    /**
     * etl创建日期时间
     */
    @TableField(value = "etl_create_datetime")
    private Object etlCreateDatetime;

    /**
     * etl更新日期时间
     */
    @TableField(value = "etl_update_datetime")
    private Object etlUpdateDatetime;

    /**
     * 日
     */
    @TableField(value = "\"day\"")
    private Date day;

    /**
     * 周
     */
    @TableField(value = "week")
    private Integer week;

    /**
     * 旬
     */
    @TableField(value = "ten_day")
    private String tenDay;

    /**
     * 月
     */
    @TableField(value = "\"month\"")
    private Integer month;

    /**
     * 季度
     */
    @TableField(value = "quarter")
    private Integer quarter;

    /**
     * 年
     */
    @TableField(value = "\"year\"")
    private Integer year;

    /**
     * 关键词
     */
    @TableField(value = "key_word")
    private String keyWord;

    /**
     * 信息来源
     */
    @TableField(value = "source_info")
    private String sourceInfo;

    /**
     * 省份编码
     */
    @TableField(value = "province_code")
    private String provinceCode;

    /**
     * 省份名称
     */
    @TableField(value = "province_name")
    private String provinceName;

    /**
     * 市编码
     */
    @TableField(value = "city_code")
    private String cityCode;

    /**
     * 市名称
     */
    @TableField(value = "city_name")
    private String cityName;

    /**
     * 区县编码
     */
    @TableField(value = "district_code")
    private String districtCode;

    /**
     * 区县名称
     */
    @TableField(value = "district_name")
    private String districtName;
}