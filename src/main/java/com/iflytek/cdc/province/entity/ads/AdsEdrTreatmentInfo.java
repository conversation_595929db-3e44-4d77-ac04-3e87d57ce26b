package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 治疗用药信息
 */
@Data
@TableName(value = "ads.ads_edr_treatment_info")
public class AdsEdrTreatmentInfo {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 采集创建时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * 采集更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;

    /**
     * 事件信息ID
     */
    @TableField(value = "event_id")
    private String eventId;

    /**
     * 主索引ID
     */
    @TableField(value = "empi_id")
    private String empiId;

    /**
     * 登记号/治疗号
     */
    @TableField(value = "registration_no")
    private String registrationNo;

    /**
     * 治疗类型代码
     */
    @TableField(value = "treatment_type_code")
    private String treatmentTypeCode;

    /**
     * 治疗类别名称
     */
    @TableField(value = "treatment_type")
    private String treatmentType;

    /**
     * 治疗类别代码
     */
    @TableField(value = "treatment_category_code")
    private String treatmentCategoryCode;

    /**
     * 治疗类型名称
     */
    @TableField(value = "treatment_category")
    private String treatmentCategory;

    /**
     * 治疗方案代码
     */
    @TableField(value = "treatment_shceme_code")
    private String treatmentShcemeCode;

    /**
     * 治疗方案名称
     */
    @TableField(value = "treatment_shceme")
    private String treatmentShceme;

    /**
     * 开始治疗日期
     */
    @TableField(value = "begin_treatment_date")
    private Date beginTreatmentDate;

    /**
     * 停止治疗日期
     */
    @TableField(value = "stop_treatment_date")
    private Date stopTreatmentDate;

    /**
     * 停止治疗原因代码
     */
    @TableField(value = "end_treatment_reason_code")
    private String endTreatmentReasonCode;

    /**
     * 结束治疗原因
     */
    @TableField(value = "end_treatment_reason")
    private String endTreatmentReason;

    /**
     * 未接受治疗原因代码
     */
    @TableField(value = "non_treatment_reason_code")
    private String nonTreatmentReasonCode;

    /**
     * 没有接受治疗原因
     */
    @TableField(value = "non_treatment_reason")
    private String nonTreatmentReason;

    /**
     * 病例无残留麻痹从麻痹至正常时间
     */
    @TableField(value = "recovery_days")
    private Integer recoveryDays;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private String updateTime;

    /**
     * 主索引 ID
     */
    @TableField(value = "mpi_id")
    private String mpiId;

    /**
     * 创建机构
     */
    @TableField(value = "create_org")
    private String createOrg;

    /**
     * 创建机构名称
     */
    @TableField(value = "create_org_name")
    private String createOrgName;

    /**
     * 修改机构
     */
    @TableField(value = "update_org")
    private String updateOrg;

    /**
     * 修改机构名称
     */
    @TableField(value = "update_org_name")
    private String updateOrgName;

    /**
     * 数据来源ID
     */
    @TableField(value = "source_id")
    private String sourceId;
}