package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * edr患者生命周期信息
 */
@Data
@TableName(value = "ads.ads_edr_life_info")
public class AdsEdrLifeInfo {
    private static final long serialVersionUID = 1L;
    /**
     * uuid，唯一标识
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 疾病档案id
     */
    @TableField(value = "archive_id")
    private String archiveId;

    /**
     * 患者ID
     */
    @TableField(value = "life_id")
    private String lifeId;

    /**
     * 标签（字段所属组）
     */
    @TableField(value = "\"label\"")
    private String label;

    /**
     * 字段内容
     */
    @TableField(value = "\"value\"")
    private String value;

    /**
     * 发生时间
     */
    @TableField(value = "occur_time")
    private Date occurTime;

    /**
     * 状态
     */
    @TableField(value = "\"status\"")
    private Short status;

    /**
     * 删除状态
     */
    @TableField(value = "delete_flag")
    private String deleteFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建人id
     */
    @TableField(value = "creator_id")
    private String creatorId;

    /**
     * 创建人
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 更新人id
     */
    @TableField(value = "update_id")
    private String updateId;

    /**
     * 更新人
     */
    @TableField(value = "updater")
    private String updater;

    /**
     * ETL创建时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * ETL更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;
}