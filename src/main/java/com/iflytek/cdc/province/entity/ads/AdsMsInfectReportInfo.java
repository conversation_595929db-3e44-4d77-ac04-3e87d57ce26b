package com.iflytek.cdc.province.entity.ads;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 诊疗(medical service)-传染病-报告卡信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "ads_ms_infect_report_info", schema = "ads")
@ApiModel(value = "AdsMsInfectReportInfo对象", description = "诊疗(medical service)-传染病-报告卡信息")
public class AdsMsInfectReportInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "传染病报告卡id")
    private String infectReportId;

    @ApiModelProperty(value = "传染病病程id")
    private String infectProcessId;

    @ApiModelProperty(value = "生命周期id")
    private String lifeId;

    @ApiModelProperty(value = "数据权限-住址编码")
    private String permitLivingAreaCode;

    @ApiModelProperty(value = "数据权限-工作单位地址编码")
    private String permitCompanyAreaCode;

    @ApiModelProperty(value = "数据权限-医疗机构地址编码")
    private String permitOrgAreaCode;

    @ApiModelProperty(value = "ETL创建时间")
    private Date etlCreateDatetime;

    @ApiModelProperty(value = "ETL更新时间")
    private Date etlUpdateDatetime;

    @ApiModelProperty(value = "识别类型（1系统报告、2人工修正）")
    private String identifyWay;

    @ApiModelProperty(value = "传染病编码")
    private String infectCode;

    @ApiModelProperty(value = "传染病名称")
    private String infectName;

    @ApiModelProperty(value = "传染病分类（甲类、乙类、丙类、其它法定、其它非法定）")
    private String infectType;

    @ApiModelProperty(value = "传染病传播途径分类（肠道传染病、呼吸道症候群、自然疫源及虫煤传染病、血源及性传播传染病）")
    private String infectTransmitType;

    @ApiModelProperty(value = "传染病结果类别-确诊类型（对应诊断类型分析，确诊病例、临床诊断病例、疑似病例、无症状感染者/带菌者/隐性感染/携带者）")
    private String identifyClass;

    @ApiModelProperty(value = "传染病-识别标准")
    private String infectRule;

    @ApiModelProperty(value = "传染病-结果依据")
    private String infectSupport;

    @ApiModelProperty(value = "识别/报告时间")
    private Date identifyTime;

    @ApiModelProperty(value = "报告及时性标识（0未执行、1及时、2超时，报告时间 - 诊断时间 根据传染病维表的时效性要求判断）")
    private String identifyIntimeFlag;

    @ApiModelProperty(value = "审核状态（0无需审核、1未审核、2审核完成、3审核中）")
    private String checkStatus;

    @ApiModelProperty(value = "审核时间")
    private Date checkTime;

    @ApiModelProperty(value = "审核及时性标识（0未执行、1及时、2超时，审核时间 - 报告时间 根据传染病维表的时效性要求判断）")
    private String checkIntimeFlag;

    @ApiModelProperty(value = "审核通过标识（1是，0否） ")
    private String checkedFlag;

    @ApiModelProperty(value = "病程管理编码（根症候群编码）")
    private String manageDiseaseCode;

    @ApiModelProperty(value = "病程管理名称（根症候群名称）")
    private String manageDiseaseName;

    @ApiModelProperty(value = "人员主索引")
    private String empiId;

    @ApiModelProperty(value = "姓名")
    private String patientName;

    @ApiModelProperty(value = "性别")
    private String patientSexName;

    @ApiModelProperty(value = "身份证件类别")
    private String patientIdentityType;

    @ApiModelProperty(value = "身份证号")
    private String patientIdentityNo;

    @ApiModelProperty(value = "出生日期")
    private Date patientBirthDay;

    @ApiModelProperty(value = "病历id")
    private String medicalId;

    @ApiModelProperty(value = "就诊事件id")
    private String eventId;

    @ApiModelProperty(value = "人员id")
    private String patientId;

    @ApiModelProperty(value = "患者年龄")
    private Integer patientAge;

    @ApiModelProperty(value = "患者年龄单位")
    private String patientAgeUnit;

    @ApiModelProperty(value = "患者联系方式")
    private String patientPhone;

    @ApiModelProperty(value = "现住详细地址")
    private String livingAddrDetail;

    @ApiModelProperty(value = "现住址标化地址")
    private String livingAddrDetailStd;

    @ApiModelProperty(value = "现住址-城乡类型（城市、乡村、未知）")
    private String livingAddrType;

    @ApiModelProperty(value = "现住地-省编码")
    private String livingAddrProvinceCode;

    @ApiModelProperty(value = "现住地-省")
    private String livingAddrProvince;

    @ApiModelProperty(value = "现住地-市编码")
    private String livingAddrCityCode;

    @ApiModelProperty(value = "现住地-市")
    private String livingAddrCity;

    @ApiModelProperty(value = "现住地-行政区编码")
    private String livingAddrDistrictCode;

    @ApiModelProperty(value = "现住地-行政区")
    private String livingAddrDistrict;

    @ApiModelProperty(value = "现住址-功能区编码")
    private String livingAddrFuncDistrictCode;

    @ApiModelProperty(value = "现住址-功能区")
    private String livingAddrFuncDistrict;

    @ApiModelProperty(value = "现住址-街道编码")
    private String livingAddrStreetCode;

    @ApiModelProperty(value = "现住址-街道")
    private String livingAddrStreet;

    @ApiModelProperty(value = "现住址-街道中心经度")
    private BigDecimal livingAddrStreetLongitude;

    @ApiModelProperty(value = "现住址-街道中心纬度")
    private BigDecimal livingAddrStreetLatitude;

    @ApiModelProperty(value = "现住址-经度")
    private BigDecimal livingAddrLongitude;

    @ApiModelProperty(value = "现住址-经度")
    private BigDecimal livingAddrLatitude;

    @ApiModelProperty(value = "人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）")
    private String personType;

    @ApiModelProperty(value = "重点人群标识（1是，0否）")
    private String focusPersonFlag;

    @ApiModelProperty(value = "职业")
    private String job;

    @ApiModelProperty(value = "职业风险")
    private String jobRisk;

    @ApiModelProperty(value = "工作单位/学校名称")
    private String company;

    @ApiModelProperty(value = "单位/学校标化地址")
    private String companyAddrDetailStd;

    @ApiModelProperty(value = "单位-省编码")
    private String companyProvinceCode;

    @ApiModelProperty(value = "单位-省")
    private String companyProvince;

    @ApiModelProperty(value = "单位-市编码")
    private String companyCityCode;

    @ApiModelProperty(value = "单位-市")
    private String companyCity;

    @ApiModelProperty(value = "单位-行政区编码")
    private String companyDistrictCode;

    @ApiModelProperty(value = "单位-行政区")
    private String companyDistrict;

    @ApiModelProperty(value = "单位-功能区编码")
    private String companyFuncDistrictCode;

    @ApiModelProperty(value = "单位-功能区")
    private String companyFuncDistrict;

    @ApiModelProperty(value = "单位-街道编码")
    private String companyStreetCode;

    @ApiModelProperty(value = "单位-街道")
    private String companyStreet;

    @ApiModelProperty(value = "单位-街道中心经度")
    private BigDecimal companyStreetLongitude;

    @ApiModelProperty(value = "单位-街道中心维度")
    private BigDecimal companyStreetLatitude;

    @ApiModelProperty(value = "单位-经度")
    private BigDecimal companyLongitude;

    @ApiModelProperty(value = "单位-纬度")
    private BigDecimal companyLatitude;

    @ApiModelProperty(value = "单位-城乡类型（城市、乡村、未知）")
    private String companyAddrType;

    @ApiModelProperty(value = "机构id（主索引）")
    private String orgId;

    @ApiModelProperty(value = "机构名称")
    private String orgName;

    @ApiModelProperty(value = "机构类别（等级医院、基层医疗）")
    private String orgClass;

    @ApiModelProperty(value = "机构类型名称")
    private String orgTypeName;

    @ApiModelProperty(value = "机构详细地址")
    private String orgAddrDetail;

    @ApiModelProperty(value = "机构标化地址")
    private String orgAddrDetailStd;

    @ApiModelProperty(value = "机构-城乡类型（城市、乡村、未知）")
    private String orgAddrType;

    @ApiModelProperty(value = "机构-省编码")
    private String orgAddrProvinceCode;

    @ApiModelProperty(value = "机构-省")
    private String orgAddrProvince;

    @ApiModelProperty(value = "机构-市编码")
    private String orgAddrCityCode;

    @ApiModelProperty(value = "机构-市")
    private String orgAddrCity;

    @ApiModelProperty(value = "机构-行政区编码")
    private String orgAddrDistrictCode;

    @ApiModelProperty(value = "机构-行政区")
    private String orgAddrDistrict;

    @ApiModelProperty(value = "机构-功能区编码")
    private String orgAddrFuncDistrictCode;

    @ApiModelProperty(value = "机构-功能区")
    private String orgAddrFuncDistrict;

    @ApiModelProperty(value = "机构-街道编码")
    private String orgAddrStreetCode;

    @ApiModelProperty(value = "机构-街道")
    private String orgAddrStreet;

    @ApiModelProperty(value = "机构经度")
    private BigDecimal orgLongitude;

    @ApiModelProperty(value = "机构纬度")
    private BigDecimal orgLatitude;

    @ApiModelProperty(value = "病原检测信息（json，检测时间pathogen_report_time，检测项目pathogen_item_name，检测定性结果pathogen_result_qualit，检测定量结果pathogen_result_quantit，检测定量单位pathogen_result_quantit_unit）")
    private String pathogenJson;

    @ApiModelProperty(value = "病人所属（住址或工作单位是否和医疗机构在同一区域，本县区、本市其他县区、本省其它地市、外省、港澳台、外籍）")
    private String addrBelongType;

    @ApiModelProperty(value = "病例地址（json，病例地址addr_area、病例地址分类addr_area_type、边境地区标识addr_border_flag、重点场所标识focus_place_flag、重点场所类型focus_place_type）")
    private String addrAreaJson;

    @ApiModelProperty(value = "重点场所标识（1是，0否）")
    private String focusPlaceFlag;

    @ApiModelProperty(value = "发病时间")
    private Date onsetTime;

    @ApiModelProperty(value = "就诊时间")
    private Date visitTime;

    @ApiModelProperty(value = "就诊所在日")
    private Date visitDay;

    @ApiModelProperty(value = "就诊科室编码")
    private String deptCode;

    @ApiModelProperty(value = "就诊科室")
    private String deptName;

    @ApiModelProperty(value = "主诉")
    private String suit;

    @ApiModelProperty(value = "症状")
    private String symptom;

    @ApiModelProperty(value = "现病史")
    private String medicalHistoryNow;

    @ApiModelProperty(value = "既往史")
    private String medicalHistoryBefore;

    @ApiModelProperty(value = "体格检查")
    private String checkup;

    @ApiModelProperty(value = "辅助检查")
    private String assistedExam;

    @ApiModelProperty(value = "主诊断编码")
    private String mainDiagCode;

    @ApiModelProperty(value = "主诊断")
    private String mainDiag;

    @ApiModelProperty(value = "标准主诊断编码")
    private String mainDiagStdCode;

    @ApiModelProperty(value = "标准主诊断")
    private String mainDiagStd;

    @ApiModelProperty(value = "主诊断类型（确诊、疑似）")
    private String mainDiagType;

    @ApiModelProperty(value = "诊断信息（json，诊断编码diag_code，诊断名称diag_name，主诊断标识diag_main_flag，诊断类型（疑似诊断、确诊诊断？）diag_type，诊断疾病类型（急性、慢性）diag_disease_type，标准诊断编码diag_code_std，标准诊断名称diag_name_std）")
    private String diagJson;

    @ApiModelProperty(value = "暴露史（json，contact_time接触时间、contact_object接触对象（1人，2物品，3环境）、contact_type_code接触方式代码、contact_type_name接触方式（饲养，分拣，加工，触摸等）、contactflag_code密接者有无同症状（1有，2无）、venereal_history性病史（1有，2无，3不详）、inject_cnt与病人共用过注射器的人数、nonweb_cnt与病人有非婚性行为的人数、homosexuality_cnt发生同性性行为的人数、discovery_mode_code发现方式代码、discovery_mode_name发现方式代码（基层推介，因症就诊，转诊，追踪，术前检测等））")
    private String exposeHistoryJson;

    @ApiModelProperty(value = "旅居史（json，foreign_flag输入病例标识（0否，1是）、foreign_place_code输入来源地代码（国家和地区代码）、foreign_place输入来源地、stay_length_d发病时在现住址居住时长（单位：天）、epidemic_source_risk_flag疫源风险标识（0否，1是））")
    private String travelHistoryJson;

    @ApiModelProperty(value = "体格检查（json，exam_time检查报告日期、exam_item_code检查项目代码、exam_item_name检查项目名称、exam_result_code检查结果代码、exam_result_name检查结果）")
    private String checkupJson;

    @ApiModelProperty(value = "症状信息（json，里面的症状要去重，都取第一次的，项目名称（症状、检查项目、检验项目等）exam_name、顺位代码sort、标签tag、解析的此类标签对应的值或描述tag_value、症状发生时间tag_value_time、数据源（就诊类型名称-业务表简称-业务类简称【-字段简称】，如住院-入院记录-体格检查-体温）data_source）")
    private String symptomExtrJson;

    @ApiModelProperty(value = "监测症状解析信息（json，顺位代码sort、监测症状发生时间symptom_time、监测症状分类symptom_class、监测症状编码symptom_code、监测症状名称symptom_name、监测症状识别依据support、疾病表现类型disease_show_class）")
    private String symptomAttentExtrJson;

    @ApiModelProperty(value = "就诊（vist）-出院时间")
    private Date dischargeTime;

    @ApiModelProperty(value = "就诊（vist）-出院诊断代码")
    private String dischargeDiagCode;

    @ApiModelProperty(value = "就诊（vist）-出院诊断")
    private String dischargeDiag;

    @ApiModelProperty(value = "转归（outcome）-转归时间")
    private Date outcomeTime;

    @ApiModelProperty(value = "转归（outcome）-转归状态（治愈、好转、稳定、恶化、死亡、其他等）")
    private String outcomeStatus;

    @ApiModelProperty(value = "转归（outcome）-治愈/治疗成功标识（1是，0否）")
    private String recoveryFlag;

    @ApiModelProperty(value = "转归（outcome）-死亡时间")
    private Date deadTime;

    @ApiModelProperty(value = "转归（outcome）-死亡原因（详细的，因该病死亡，其他）")
    private String deadReason;

    @ApiModelProperty(value = "转归（outcome）-因该病死亡标识（1是，0否）")
    private String deadThisFlag;

    @ApiModelProperty(value = "信息完整性标识（1是0否，报告卡必填字段都有值）")
    private String fullFlag;

    @ApiModelProperty(value = "质量合格标识（1是，0否，报告及时、审核及时且信息完整）")
    private String conformFlag;

    @ApiModelProperty(value = "肺炎病例标识（1是、0否）")
    private String pneumoniaFlag;

    @ApiModelProperty(value = "病原检测标识（1是，0否）")
    private String pathogenFlag;

    @ApiModelProperty(value = "漏报标识（1是、0否，人工新增的报告卡）")
    private String leakFlag;

    @ApiModelProperty(value = "重症标识（1是，0否，重症监护标识）")
    private String severeFlag;

    @ApiModelProperty(value = "传染病结果修订时间")
    private Date infectReviseTime;

    @ApiModelProperty(value = "修订前的传染病编码")
    private String infectCodeOld;

    @ApiModelProperty(value = "修订前的传染病名称")
    private String infectNameOld;

    @ApiModelProperty(value = "最新修订的信息更新sql")
    private String lastReviseSql;

    @ApiModelProperty(value = "诊断时间")
    private Date diagTime;

    @ApiModelProperty(value = "诊类型（门诊）")
    private String visitTypeName;

    @ApiModelProperty(value = "初诊标识（初诊）")
    private String firstDiagFlag;
}
