package com.iflytek.cdc.province.entity.ads;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 诊疗(medical service)-住院-病历详情;
 * <AUTHOR> dingyuan
 * @date : 2024-5-24
 */
@ApiModel(value = "诊疗(medical service)-住院-病历详情")
@Data
public class AdsMsInpatMedicalInfo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 病历id
     */
    @ApiModelProperty(value = "病历id")
    private String medicalId ;

    /**
     * 就诊事件id
     */
    @ApiModelProperty(value = "就诊事件id")
    private String eventId ;

    /**
     * 病程id
     */
    @ApiModelProperty(value = "病程id")
    private String processId ;

    /**
     * 生命周期id
     */
    @ApiModelProperty(value = "生命周期id")
    private String lifeId ;

    /**
     * ETL创建时间
     */
    @ApiModelProperty(value = "ETL创建时间")
    private Date etlCreateDatetime ;

    /**
     * ETL更新时间
     */
    @ApiModelProperty(value = "ETL更新时间")
    private Date etlUpdateDatetime ;

    /**
     * 患者（不变）信息-姓名
     */
    @ApiModelProperty(value = "患者（不变）信息-姓名")
    private String patientName ;

    /**
     * 患者（不变）信息-性别代码
     */
    @ApiModelProperty(value = "患者（不变）信息-性别代码")
    private String patientSexCode ;

    /**
     * 患者（不变）信息-性别（有字典，313001）
     */
    @ApiModelProperty(value = "患者（不变）信息-性别（有字典，313001）")
    private String patientSexName ;

    /**
     * 患者（不变）信息-证件类型编码
     */
    @ApiModelProperty(value = "患者（不变）信息-证件类型编码")
    private String patientIdentityTypeCode ;

    /**
     * 患者（不变）信息-证件类型名称
     */
    @ApiModelProperty(value = "患者（不变）信息-证件类型名称")
    private String patientIdentityType ;

    /**
     * 患者（不变）信息-身份证号/身份证件号码
     */
    @ApiModelProperty(value = "患者（不变）信息-身份证号/身份证件号码")
    private String patientIdentityNo ;

    /**
     * 患者（不变）信息-出生日期
     */
    @ApiModelProperty(value = "患者（不变）信息-出生日期")
    private Date patientBirthDay ;

    /**
     * 患者（可变）信息-人员id
     */
    @ApiModelProperty(value = "患者（可变）信息-人员id")
    private String patientId ;

    /**
     * 患者（可变）信息-年龄
     */
    @ApiModelProperty(value = "患者（可变）信息-年龄")
    private Integer patientAge ;

    /**
     * 患者（可变）信息-年龄单位
     */
    @ApiModelProperty(value = "患者（可变）信息-年龄单位")
    private String patientAgeUnit ;

    /**
     * 患者（可变）信息-患者联系方式
     */
    @ApiModelProperty(value = "患者（可变）信息-患者联系方式")
    private String patientPhone ;

    /**
     * 患者（可变）信息-患者来源（1本市，2市郊，3本省，4外省）
     */
    @ApiModelProperty(value = "患者（可变）信息-患者来源（1本市，2市郊，3本省，4外省）")
    private String patientSource ;

    /**
     * 患者（可变）信息-现住详细地址
     */
    @ApiModelProperty(value = "患者（可变）信息-现住详细地址")
    private String livingAddrDetail ;

    /**
     * 患者（可变）信息-现住址标化地址
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址标化地址")
    private String livingAddrDetailStd ;

    /**
     * 患者（可变）信息-现住地-省编码
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-省编码")
    private String livingAddrProvinceCode ;

    /**
     * 患者（可变）信息-现住地-省
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-省")
    private String livingAddrProvince ;

    /**
     * 患者（可变）信息-现住地-市编码
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-市编码")
    private String livingAddrCityCode ;

    /**
     * 患者（可变）信息-现住地-市
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-市")
    private String livingAddrCity ;

    /**
     * 患者（可变）信息-现住地-行政区编码
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-行政区编码")
    private String livingAddrDistrictCode ;

    /**
     * 患者（可变）信息-现住地-行政区
     */
    @ApiModelProperty(value = "患者（可变）信息-现住地-行政区")
    private String livingAddrDistrict ;

    /**
     * 患者（可变）信息-现住址-功能区编码
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-功能区编码")
    private String livingAddrFuncDistrictCode ;

    /**
     * 患者（可变）信息-现住址-功能区
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-功能区")
    private String livingAddrFuncDistrict ;

    /**
     * 患者（可变）信息-现住址-街道编码
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-街道编码")
    private String livingAddrStreetCode ;

    /**
     * 患者（可变）信息-现住址-街道
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-街道")
    private String livingAddrStreet ;

    /**
     * 患者（可变）信息-现住址-街道中心经度
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-街道中心经度")
    private Double livingAddrStreetLongitude ;

    /**
     * 患者（可变）信息-现住址-街道中心纬度
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-街道中心纬度")
    private Double livingAddrStreetLatitude ;

    /**
     * 患者（可变）信息-现住址-经度
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-经度")
    private Double livingAddrLongitude ;

    /**
     * 患者（可变）信息-现住址-经度
     */
    @ApiModelProperty(value = "患者（可变）信息-现住址-经度")
    private Double livingAddrLatitude ;

    /**
     * 患者（可变）信息-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）
     */
    @ApiModelProperty(value = "患者（可变）信息-人群分类名称（幼托儿童、散居儿童、学生（大中小学）、教师、保育员及保姆、餐饮食品业、商业服务、医务人员、工人、民工、农民、牧民、渔（船）民、干部职员、离退人员、家务及待业、其他、不详）")
    private String personType ;

    /**
     * 患者（可变）信息-职业
     */
    @ApiModelProperty(value = "患者（可变）信息-职业")
    private String job ;

    /**
     * 患者（可变）信息-工作单位/学校名称
     */
    @ApiModelProperty(value = "患者（可变）信息-工作单位/学校名称")
    private String company ;

    /**
     * 患者（可变）信息-单位/学校标化地址
     */
    @ApiModelProperty(value = "患者（可变）信息-单位/学校标化地址")
    private String companyAddress ;

    /**
     * 患者（可变）信息-单位-省编码
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-省编码")
    private String companyProvinceCode ;

    /**
     * 患者（可变）信息-单位-省
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-省")
    private String companyProvince ;

    /**
     * 患者（可变）信息-单位-市编码
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-市编码")
    private String companyCityCode ;

    /**
     * 患者（可变）信息-单位-市
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-市")
    private String companyCity ;

    /**
     * 患者（可变）信息-单位-行政区编码
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-行政区编码")
    private String companyDistrictCode ;

    /**
     * 患者（可变）信息-单位-行政区
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-行政区")
    private String companyDistrict ;

    /**
     * 患者（可变）信息-单位-功能区编码
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-功能区编码")
    private String companyFuncDistrictCode ;

    /**
     * 患者（可变）信息-单位-功能区
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-功能区")
    private String companyFuncDistrict ;

    /**
     * 患者（可变）信息-单位-街道编码
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-街道编码")
    private String companyStreetCode ;

    /**
     * 患者（可变）信息-单位-街道
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-街道")
    private String companyStreet ;

    /**
     * 患者（可变）信息-单位-街道中心经度
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-街道中心经度")
    private Double companyStreetLongitude ;

    /**
     * 患者（可变）信息-单位-街道中心维度
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-街道中心维度")
    private Double companyStreetLatitude ;

    /**
     * 患者（可变）信息-单位-经度
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-经度")
    private Double companyLongitude ;

    /**
     * 患者（可变）信息-单位-纬度
     */
    @ApiModelProperty(value = "患者（可变）信息-单位-纬度")
    private Double companyLatitude ;

    /**
     * 患者（可变）信息-陪护人姓名
     */
    @ApiModelProperty(value = "患者（可变）信息-陪护人姓名")
    private String chaperone ;

    /**
     * 患者（可变）信息-陪护人联系方式
     */
    @ApiModelProperty(value = "患者（可变）信息-陪护人联系方式")
    private String chaperoneTel ;

    /**
     * 机构id（主索引）
     */
    @ApiModelProperty(value = "机构id（主索引）")
    private String orgId ;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String orgName ;

    /**
     * 机构类别（等级医院、基层医疗）
     */
    @ApiModelProperty(value = "机构类别（等级医院、基层医疗）")
    private String orgClass ;

    /**
     * 机构类型名称(204003)
     */
    @ApiModelProperty(value = "机构类型名称(204003)")
    private String orgTypeName ;

    /**
     * 机构详细地址
     */
    @ApiModelProperty(value = "机构详细地址")
    private String orgAddrDetail ;

    /**
     * 机构标化地址
     */
    @ApiModelProperty(value = "机构标化地址")
    private String orgAddrDetailStd ;

    /**
     * 机构-省编码
     */
    @ApiModelProperty(value = "机构-省编码")
    private String orgAddrProvinceCode ;

    /**
     * 机构-省
     */
    @ApiModelProperty(value = "机构-省")
    private String orgAddrProvince ;

    /**
     * 机构-市编码
     */
    @ApiModelProperty(value = "机构-市编码")
    private String orgAddrCityCode ;

    /**
     * 机构-市
     */
    @ApiModelProperty(value = "机构-市")
    private String orgAddrCity ;

    /**
     * 机构-行政区编码
     */
    @ApiModelProperty(value = "机构-行政区编码")
    private String orgAddrDistrictCode ;

    /**
     * 机构-行政区
     */
    @ApiModelProperty(value = "机构-行政区")
    private String orgAddrDistrict ;

    /**
     * 机构-功能区编码
     */
    @ApiModelProperty(value = "机构-功能区编码")
    private String orgAddrFuncDistrictCode ;

    /**
     * 机构-功能区
     */
    @ApiModelProperty(value = "机构-功能区")
    private String orgAddrFuncDistrict ;

    /**
     * 机构-街道编码
     */
    @ApiModelProperty(value = "机构-街道编码")
    private String orgAddrStreetCode ;

    /**
     * 机构-街道
     */
    @ApiModelProperty(value = "机构-街道")
    private String orgAddrStreet ;

    /**
     * 机构经度
     */
    @ApiModelProperty(value = "机构经度")
    private Double orgLongitude ;

    /**
     * 机构纬度
     */
    @ApiModelProperty(value = "机构纬度")
    private Double orgLatitude ;

    /**
     * 门诊-就诊流水号
     */
    @ApiModelProperty(value = "门诊-就诊流水号")
    private String outpatSerialNo ;

    /**
     * 门诊-就诊时间
     */
    @ApiModelProperty(value = "门诊-就诊时间")
    private Date outpatVisitTime ;

    /**
     * 门诊-科室编码
     */
    @ApiModelProperty(value = "门诊-科室编码")
    private String outpatDeptCode ;

    /**
     * 门诊-科室
     */
    @ApiModelProperty(value = "门诊-科室")
    private String outpatDeptName ;

    /**
     * 门诊-接诊医生编码
     */
    @ApiModelProperty(value = "门诊-接诊医生编码")
    private String outpatDocId ;

    /**
     * 门诊-接诊医生
     */
    @ApiModelProperty(value = "门诊-接诊医生")
    private String outpatDocName ;

    /**
     * 门诊-西医-诊断编码
     */
    @ApiModelProperty(value = "门诊-西医-诊断编码")
    private String outpatDiagCode ;

    /**
     * 门诊-西医-诊断名称
     */
    @ApiModelProperty(value = "门诊-西医-诊断名称")
    private String outpatDiagName ;

    /**
     * 门诊-中医-症候代码
     */
    @ApiModelProperty(value = "门诊-中医-症候代码")
    private String outpatTcmSymptomCode ;

    /**
     * 门诊-中医-症候
     */
    @ApiModelProperty(value = "门诊-中医-症候")
    private String outpatTcmSymptom ;

    /**
     * 门诊-中医-病名代码
     */
    @ApiModelProperty(value = "门诊-中医-病名代码")
    private String outpatTcmDiseaseCode ;

    /**
     * 门诊-中医-病名
     */
    @ApiModelProperty(value = "门诊-中医-病名")
    private String outpatTcmDisease ;

    /**
     * 入院-入院来源（1 门诊 2 急诊 3 转入 9 其他）
     */
    @ApiModelProperty(value = "入院-入院来源（1 门诊 2 急诊 3 转入 9 其他）")
    private String admissSource ;

    /**
     * 入院-转入来源医院编码
     */
    @ApiModelProperty(value = "入院-转入来源医院编码")
    private String admissSourceOrgCode ;

    /**
     * 入院-转入来源医院
     */
    @ApiModelProperty(value = "入院-转入来源医院")
    private String admissSourceOrg ;

    /**
     * 入院-入院方式（步行、扶送、平车、轮椅、抱送等）
     */
    @ApiModelProperty(value = "入院-入院方式（步行、扶送、平车、轮椅、抱送等）")
    private String admissWay ;

    /**
     * 入院-入院情况（1-一般 2-危 3-急 4-重）
     */
    @ApiModelProperty(value = "入院-入院情况（1-一般 2-危 3-急 4-重）")
    private String admissSituation ;

    /**
     * 入院-登记时间
     */
    @ApiModelProperty(value = "入院-登记时间")
    private Date admissRegisterTime ;

    /**
     * 入院-入院时间（即接诊入区时间）
     */
    @ApiModelProperty(value = "入院-入院时间（即接诊入区时间）")
    private Date admissTime ;

    /**
     * 入院-入院状态（(0-待入院 1-入院登记 2-已入院 3-已作废)）
     */
    @ApiModelProperty(value = "入院-入院状态（(0-待入院 1-入院登记 2-已入院 3-已作废)）")
    private String admissStatus ;

    /**
     * 入院-科室编码
     */
    @ApiModelProperty(value = "入院-科室编码")
    private String admissDeptCode ;

    /**
     * 入院-科室
     */
    @ApiModelProperty(value = "入院-科室")
    private String admissDeptName ;

    /**
     * 入院-病区编码
     */
    @ApiModelProperty(value = "入院-病区编码")
    private String admissWardCode ;

    /**
     * 入院-病区
     */
    @ApiModelProperty(value = "入院-病区")
    private String admissWardName ;

    /**
     * 入院-病房
     */
    @ApiModelProperty(value = "入院-病房")
    private String admissRoom ;

    /**
     * 入院-病床
     */
    @ApiModelProperty(value = "入院-病床")
    private String admissBed ;

    /**
     * 住院就诊流水号
     */
    @ApiModelProperty(value = "住院就诊流水号")
    private String serialNo ;

    /**
     * 住院次数
     */
    @ApiModelProperty(value = "住院次数")
    private Integer inpatTimes ;

    /**
     * 住院医师编码
     */
    @ApiModelProperty(value = "住院医师编码")
    private String docAssistantId ;

    /**
     * 住院医师
     */
    @ApiModelProperty(value = "住院医师")
    private String docAssistant ;

    /**
     * 主治医师编码
     */
    @ApiModelProperty(value = "主治医师编码")
    private String docAttendingId ;

    /**
     * 主治医师
     */
    @ApiModelProperty(value = "主治医师")
    private String docAttending ;

    /**
     * 主任医师编码
     */
    @ApiModelProperty(value = "主任医师编码")
    private String docDirectorId ;

    /**
     * 主任医师
     */
    @ApiModelProperty(value = "主任医师")
    private String docDirector ;

    /**
     * 护理级别（0-特级 1-Ⅰ级 2- Ⅱ级 3- Ⅲ级）
     */
    @ApiModelProperty(value = "护理级别（0-特级 1-Ⅰ级 2- Ⅱ级 3- Ⅲ级）")
    private String nursingLevel ;

    /**
     * 责任护士编码
     */
    @ApiModelProperty(value = "责任护士编码")
    private String nurseId ;

    /**
     * 责任护士
     */
    @ApiModelProperty(value = "责任护士")
    private String nurse ;

    /**
     * 临床路径
     */
    @ApiModelProperty(value = "临床路径")
    private String clinicalPath ;

    /**
     * 出院-转出目的医院编码
     */
    @ApiModelProperty(value = "出院-转出目的医院编码")
    private String outTargetOrgId ;

    /**
     * 出院-转出目的医院
     */
    @ApiModelProperty(value = "出院-转出目的医院")
    private String outTargetOrg ;

    /**
     * 出院-科室编码
     */
    @ApiModelProperty(value = "出院-科室编码")
    private String outDeptCode ;

    /**
     * 出院-科室
     */
    @ApiModelProperty(value = "出院-科室")
    private String outDeptName ;

    /**
     * 出院-病区编码
     */
    @ApiModelProperty(value = "出院-病区编码")
    private String outWardCode ;

    /**
     * 出院-病区
     */
    @ApiModelProperty(value = "出院-病区")
    private String outWardName ;

    /**
     * 出院-病房
     */
    @ApiModelProperty(value = "出院-病房")
    private String outRoom ;

    /**
     * 出院-病床
     */
    @ApiModelProperty(value = "出院-病床")
    private String outBed ;

    /**
     * 出院-出区时间（出院时间）
     */
    @ApiModelProperty(value = "出院-出区时间（出院时间）")
    private Date outTime ;

    /**
     * 出院-出区备注
     */
    @ApiModelProperty(value = "出院-出区备注")
    private String outNote ;

    /**
     * 转归时间
     */
    @ApiModelProperty(value = "转归时间")
    private Date outcomeTime ;

    /**
     * 转归状态（治愈、治疗成功、死亡、带病）
     */
    @ApiModelProperty(value = "转归状态（治愈、治疗成功、死亡、带病）")
    private String outcomeStatus ;

    /**
     * 死亡原因（详细的，因该病死亡，其他）
     */
    @ApiModelProperty(value = "死亡原因（详细的，因该病死亡，其他）")
    private String deadReason ;

    /**
     * 诊断信息（json，发病部位代码part_code，发病部位part，诊断类别代码diag_class_code，诊断类别（入院诊断、出院诊断、补充诊断、门（急）诊诊断等diag_class，诊断编码diag_code，诊断名称diag_name，诊断时间diag_time，主诊断标识diag_main_flag，诊断类型（初步诊断、疑似诊断、确诊诊断？）diag_type，诊断疾病类型（急性、慢性）diag_disease_type，治疗结果编码treat_result_code，治疗结果（治愈、好转、死亡等）treat_result，标准诊断名称diag_code_std，标准诊断编码diag_name_std，四诊结果four_diag，舌象tongue_manifestation，脉象pulse_condition，症候代码symptom_code，症候symptom，病名代码disease_code，病名disease，辩证依据treat_support，治则治法treat）
     */
    @ApiModelProperty(value = "诊断信息（json，发病部位代码part_code，发病部位part，诊断类别代码diag_class_code，诊断类别（入院诊断、出院诊断、补充诊断、门（急）诊诊断等diag_class，诊断编码diag_code，诊断名称diag_name，诊断时间diag_time，主诊断标识diag_main_flag，诊断类型（初步诊断、疑似诊断、确诊诊断？）diag_type，诊断疾病类型（急性、慢性）diag_disease_type，治疗结果编码treat_result_code，治疗结果（治愈、好转、死亡等）treat_result，标准诊断名称diag_code_std，标准诊断编码diag_name_std，四诊结果four_diag，舌象tongue_manifestation，脉象pulse_condition，症候代码symptom_code，症候symptom，病名代码disease_code，病名disease，辩证依据treat_support，治则治法treat）")
    private String diagJson ;

    /**
     * 症状信息（json，症状编码symptom_code，症状symptom）
     */
    @ApiModelProperty(value = "症状信息（json，症状编码symptom_code，症状symptom）")
    private String symptomJson ;

    /**
     * 主诉
     */
    @ApiModelProperty(value = "主诉")
    private String suit ;

    /**
     * 现病史
     */
    @ApiModelProperty(value = "现病史")
    private String diseaseHistoryNow ;

    /**
     * 既往史
     */
    @ApiModelProperty(value = "既往史")
    private String diseaseHistoryBefore ;

    /**
     * 体格检查
     */
    @ApiModelProperty(value = "体格检查")
    private String checkup ;

    /**
     * 传染病史
     */
    @ApiModelProperty(value = "传染病史")
    private String infectHistory ;

    /**
     * 过敏史
     */
    @ApiModelProperty(value = "过敏史")
    private String allergyHistory ;

    /**
     * 遗传史
     */
    @ApiModelProperty(value = "遗传史")
    private String geneticHistory ;

    /**
     * 治疗意见/处置意见
     */
    @ApiModelProperty(value = "治疗意见/处置意见")
    private String dealNote ;

    /**
     * 病原检测信息（json，检测时间pathogen_report_time，检测项目pathogen_item_name，检测定性结果pathogen_result_qualit，检测定量结果pathogen_result_quantit，检测定量单位pathogen_result_quantit_unit）
     */
    @ApiModelProperty(value = "病原检测信息（json，检测时间pathogen_report_time，检测项目pathogen_item_name，检测定性结果pathogen_result_qualit，检测定量结果pathogen_result_quantit，检测定量单位pathogen_result_quantit_unit）")
    private String pathogenJson ;

    /**
     * 传染病报卡标识（1是0否）
     */
    @ApiModelProperty(value = "传染病报卡标识（1是0否）")
    private String infectReportFlag ;

    /**
     * 病历类型编码
     */
    @ApiModelProperty(value = "病历类型编码")
    private String mrTypeCode ;

    /**
     * 病历类型（中医、西医等）
     */
    @ApiModelProperty(value = "病历类型（中医、西医等）")
    private String mrType ;

    /**
     * 病历完成时间
     */
    @ApiModelProperty(value = "病历完成时间")
    private Date mrCompleteTime ;

    /**
     * 签名医生编码
     */
    @ApiModelProperty(value = "签名医生编码")
    private String signDocId ;

    /**
     * 签名医生
     */
    @ApiModelProperty(value = "签名医生")
    private String signDoc ;

    /**
     * 签名时间
     */
    @ApiModelProperty(value = "签名时间")
    private Date signTime ;

    /**
     * 记录状态（10-入院登记 20-病房接诊 30-出院登记 40-出院结算 50-无费退院 60-转科申请）
     */
    @ApiModelProperty(value = "记录状态（10-入院登记 20-病房接诊 30-出院登记 40-出院结算 50-无费退院 60-转科申请）")
    private String recordStatus ;

    /**
     * 当前科室编码
     */
    @ApiModelProperty(value = "当前科室编码")
    private String nowDeptCode ;

    /**
     * 当前科室
     */
    @ApiModelProperty(value = "当前科室")
    private String nowDeptName ;

    /**
     * 当前病区编码
     */
    @ApiModelProperty(value = "当前病区编码")
    private String nowWardCode ;

    /**
     * 当前病区
     */
    @ApiModelProperty(value = "当前病区")
    private String nowWardName ;

    /**
     * 当前病房
     */
    @ApiModelProperty(value = "当前病房")
    private String nowRoom ;

    /**
     * 当前病床
     */
    @ApiModelProperty(value = "当前病床")
    private String nowBed ;

}
