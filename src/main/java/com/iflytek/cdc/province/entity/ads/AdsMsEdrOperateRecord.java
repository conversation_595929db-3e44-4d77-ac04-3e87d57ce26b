package com.iflytek.cdc.province.entity.ads;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.iflytek.cdc.edr.annotation.Sensitive;
import com.iflytek.cdc.edr.enums.SensitiveTypeEnum;
import com.iflytek.cdc.province.enums.MaintenanceOperateEnum;
import com.iflytek.cdc.province.enums.StatusEnum;
import com.iflytek.cdc.province.model.edr.vo.MaintenanceLogExcelVO;
import com.iflytek.cdc.province.utils.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Optional;

/**
 * edr信息操作记录（修改、注销等）;
 * <AUTHOR> dingyuan
 * @date : 2025-3-5
 */
@ApiModel(value = "edr信息操作记录（修改、注销等）")
@Data
public class AdsMsEdrOperateRecord implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * uuid，唯一标识
     */
    @ApiModelProperty(value = "uuid，唯一标识")
    private String id ;

    /**
     * 疾病档案id
     */
    @ApiModelProperty(value = "疾病档案id")
    private String archiveId ;

    /**
     * 患者ID
     */
    @ApiModelProperty(value = "患者ID")
    private String lifeId ;

    /**
     * 患者姓名
     */
    @ApiModelProperty(value = "患者姓名")
    @Sensitive(type = SensitiveTypeEnum.PATIENT)
    private String patientName ;

    /**
     * 维护更新操作类型（更新档案、修改档案、注销档案）
     */
    @ApiModelProperty(value = "维护更新操作类型（更新档案、修改档案、注销档案）")
    private String operateType ;

    /**
     * 更新成功标识（1是，0否）
     */
    @ApiModelProperty(value = "更新成功标识（1是，0否）")
    private String successFlag ;

    /**
     * 数据权限-住址id
     */
    @ApiModelProperty(value = "数据权限-住址id")
    private String permitLivingAreaCode ;

    /**
     * 数据权限-工作单位地址id
     */
    @ApiModelProperty(value = "数据权限-工作单位地址id")
    private String permitCompanyAreaCode ;

    /**
     * 数据权限-医疗机构地址id
     */
    @ApiModelProperty(value = "数据权限-医疗机构地址id")
    private String permitOrgAreaCode ;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date updateTime ;

    /**
     * 更新人id
     */
    @ApiModelProperty(value = "更新人id")
    private String updateId ;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updater ;

    /**
     * 维护更新操作类型（系统操作、人为操作）
     */
    @ApiModelProperty(value = "维护更新操作类型（系统操作、人为操作）")
    private String operateClass ;

    /**
     * 患者id
     */
    @ApiModelProperty(value = "患者id")
    private String patientId ;

    /**
     * 个人疾病档案更新后的json
     */
    @ApiModelProperty(value = "个人疾病档案更新后的json")
    private String contentJson;

    public static MaintenanceLogExcelVO of(AdsMsEdrOperateRecord log){

        MaintenanceLogExcelVO excelVO = new MaintenanceLogExcelVO();
        excelVO.setMaintenanceTime(DateUtils.formatDate(log.getUpdateTime(), DateUtils.DATE_FORMAT));
        excelVO.setArchiveId(log.getArchiveId());
        excelVO.setPatientId(log.getPatientId());
        excelVO.setPatientName(log.getPatientName());
        excelVO.setUpdater(log.getUpdater());
        excelVO.setOperatorDesc(MaintenanceOperateEnum.getDescByCode(log.getOperateType()));
        excelVO.setUpdateStatus(StatusEnum.getMaintenanceDescByCode(Integer.parseInt(Optional.ofNullable(log.getSuccessFlag()).orElse("0"))));
        return excelVO;
    }

}
