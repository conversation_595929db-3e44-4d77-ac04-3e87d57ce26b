package com.iflytek.cdc.province.entity.bu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 收藏数据明细表;
 * <AUTHOR> dingyuan
 * @date : 2024-5-8
 */
@ApiModel(value = "收藏数据明细表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TbCdcewDataFavoritesDetail implements Serializable{

    private static final long serialVersionUID = 1L;
    /**
     * 收藏记录ID
     */
    @ApiModelProperty(value = "收藏记录ID")
    private String id ;

    /**
     * 收藏夹ID
     */
    @ApiModelProperty(value = "收藏夹ID")
    private String dataFavoritesId ;

    /**
     * 收藏夹数据类型
     */
    @ApiModelProperty(value = "收藏夹 收藏的数据类型")
    private String dataType ;

    /**
     * 数据记录ID
     */
    @ApiModelProperty(value = "数据记录ID")
    private String dataRecordId ;

    /**
     * 数据模型版本id
     */
    @ApiModelProperty(value = "数据模型id")
    private String dataModelId ;

    /**
     * 数据收藏状态:0-未收藏；1-已收藏
     */
    @ApiModelProperty(value = "数据收藏状态:0-未收藏；1-已收藏")
    private Integer status ;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String dataQueryUrl ;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String dataQueryParam ;

    /**
     * 收藏时间
     */
    @ApiModelProperty(value = "收藏时间")
    private Date collectTime ;

    @ApiModelProperty(value = "数据模型版本id")
    private String modelVersionId;

    @ApiModelProperty(value = "数据所处系统")
    private String moduleType;

}
