package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "dws.dws_process_tag_relation")
public class DwsProcessTagRelation {
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    @TableField(value = "process_id")
    private String processId;

    @TableField(value = "empi_id")
    private String empiId;

    @TableField(value = "disease_type")
    private String diseaseType;

    @TableField(value = "disease_code")
    private String diseaseCode;

    @TableField(value = "tag_id")
    private String tagId;

    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;
}