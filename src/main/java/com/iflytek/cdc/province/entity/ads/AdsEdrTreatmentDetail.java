package com.iflytek.cdc.province.entity.ads;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 治疗用药明细
 */
@Data
@TableName(value = "ads.ads_edr_treatment_detail")
public class AdsEdrTreatmentDetail {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 采集创建时间
     */
    @TableField(value = "etl_create_datetime")
    private Date etlCreateDatetime;

    /**
     * 采集更新时间
     */
    @TableField(value = "etl_update_datetime")
    private Date etlUpdateDatetime;

    /**
     * 事件信息ID
     */
    @TableField(value = "event_id")
    private String eventId;

    /**
     * 主索引ID
     */
    @TableField(value = "empi_id")
    private String empiId;

    /**
     * 通用药物代码
     */
    @TableField(value = "drug_code")
    private String drugCode;

    /**
     * 药物名称
     */
    @TableField(value = "drug")
    private String drug;

    /**
     * 药物剂型代码
     */
    @TableField(value = "drug_dosage_code")
    private String drugDosageCode;

    /**
     * 药物剂型名称
     */
    @TableField(value = "drug_dosage")
    private String drugDosage;

    /**
     * 服药方法代码
     */
    @TableField(value = "drug_dosage_route_code")
    private String drugDosageRouteCode;

    /**
     * 服药方法名称
     */
    @TableField(value = "drug_dosage_route")
    private String drugDosageRoute;

    /**
     * 结束服药日期
     */
    @TableField(value = "drug_end_date")
    private Date drugEndDate;

    /**
     * 开始服药日期
     */
    @TableField(value = "drug_begin_date")
    private Date drugBeginDate;

    /**
     * 每日用药次数
     */
    @TableField(value = "drug_frequency")
    private Integer drugFrequency;

    /**
     * 药物使用次剂量
     */
    @TableField(value = "drug_dose_code")
    private String drugDoseCode;

    /**
     * 药物使用总剂量
     */
    @TableField(value = "drug_given_quantity")
    private String drugGivenQuantity;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 数据来源ID
     */
    @TableField(value = "source_id")
    private String sourceId;

    /**
     * 主索引 ID
     */
    @TableField(value = "mpi_id")
    private String mpiId;

    /**
     * 创建机构
     */
    @TableField(value = "create_org")
    private String createOrg;

    /**
     * 创建机构名称
     */
    @TableField(value = "create_org_name")
    private String createOrgName;

    /**
     * 修改机构
     */
    @TableField(value = "update_org")
    private String updateOrg;

    /**
     * 修改机构名称
     */
    @TableField(value = "update_org_name")
    private String updateOrgName;
}