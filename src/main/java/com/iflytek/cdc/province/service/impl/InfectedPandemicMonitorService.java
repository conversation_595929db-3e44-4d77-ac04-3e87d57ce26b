package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.dto.workbench.UserTodoListDTO;
import com.iflytek.cdc.province.model.pandemic.dto.MedicalQueryDTO;
import com.iflytek.cdc.province.mapper.pg.AdsMsProcessInfoMapper;
import com.iflytek.cdc.province.model.vo.TreeNode;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.service.IPandemicMonitorService;
import com.iflytek.cdc.province.service.MonitorCommonUtils;
import com.iflytek.cdc.edr.vo.dm.ProcessRecordVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service("infectedMonitorCategory")
public class InfectedPandemicMonitorService implements IPandemicMonitorService {

    @Resource
    private AdsMsProcessInfoMapper adsMsProcessInfoMapper;

    @Resource
    private MonitorCommonUtils monitorCommonUtils;

    @Resource
    private CommonUtilService commonUtilService;

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Override
    public List<ProcessRecordVO> getMedicalList(MedicalQueryDTO dto) {
        return getInfectedMedicalList(dto);
    }

    @Override
    public List<UserTodoListDTO> getMedicalListToDo(MedicalQueryDTO dto) {
        //查询传染病病例
        List<ProcessRecordVO> infectedMedicalList = getInfectedMedicalList(dto);
        List<UserTodoListDTO> ret = infectedMedicalList.stream()
                .map(UserTodoListDTO::buildFromInfectedMedical)
                .collect(Collectors.toList());
        return ret;
    }

    private List<ProcessRecordVO> getInfectedMedicalList(MedicalQueryDTO dto) {
        //构建传染病种类的codeList查询
        dto.setInfectCodeList(this.getDiseaseCodeListBy(dto.getInfectTypeList(), dto.getInfectCodeList()));

        //根据给定条件查询 传染病 病例列表
        return adsMsProcessInfoMapper.getInfectedMedicalList(dto);
    }

    /**
     * 传染病code处理、根据种类以及子类code得到最后需要查询的codeList
     * */
    private List<String> getDiseaseCodeListBy(List<String> infectTypeList, List<String> infectCodeList){

        List<TreeNode> diseaseCodeTree = adminServiceApi.getInfectedInfo(null);

        //如果选择大类，则获取大类下所有子类
        List<String> classDiseaseCodeList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(infectTypeList)) {

            infectTypeList.forEach(e -> {
                for (TreeNode node : diseaseCodeTree) {
                    TreeNode.getAllNodeBy(classDiseaseCodeList, node, e, TreeNode::getValue, TreeNode::getValue);
                }
            });
        }

        //如果选择了子类则获取子类下所有code
        List<String> diseaseCodeList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(infectCodeList)) {

            infectCodeList.forEach(e -> {
                for (TreeNode node : diseaseCodeTree) {
                    TreeNode.getAllNodeBy(diseaseCodeList, node, e, TreeNode::getValue, TreeNode::getValue);
                }
            });
        }
        //如果只选择大类，则返回大类下所有子类的code
        return CollectionUtil.isEmpty(diseaseCodeList) ? classDiseaseCodeList : CommonUtilService.intersection(classDiseaseCodeList, diseaseCodeList);
    }

}
