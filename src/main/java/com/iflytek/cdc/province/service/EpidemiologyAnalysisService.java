package com.iflytek.cdc.province.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.province.model.epidemiology.CritiaclDeathProcessVO;
import com.iflytek.cdc.province.model.epidemiology.PatientVisitSituationVO;
import com.iflytek.cdc.province.model.pathogen.EpidemiologyReqDTO;

import java.util.List;

/**
 * 流行病学分析
 */
public interface EpidemiologyAnalysisService {


    /**
     * 患者就诊情况
     */
    List<PatientVisitSituationVO> patientVisitSituationChart(EpidemiologyReqDTO reqDTO);


    /**
     * 患者就诊科室分布图
     */
    List<PatientVisitSituationVO> patientVisitDeptChart(EpidemiologyReqDTO reqDTO);


    /**
     * 入院患者就诊情况图
     */
    PatientVisitSituationVO admissPatientVisitChart(EpidemiologyReqDTO reqDTO);


    /**
     * 出院患者就诊情况图
     */
    PatientVisitSituationVO leavePatientVisitChart(EpidemiologyReqDTO reqDTO);


    /**
     * 转重症死亡病例分页列表
     */
    PageInfo<CritiaclDeathProcessVO> pageCritiaclDeathProcess(EpidemiologyReqDTO reqDTO);

}
