package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.province.model.dto.dm.InformationHubQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.edr.vo.pandemic.MedicalInfoVO;

public interface InformationHubService {

    PageInfo<MedicalInfoVO> getOriginMedicalInfo(InformationHubQueryDTO dto);

    PageInfo<MedicalInfoVO> getInpatientMedicalInfo(InformationHubQueryDTO dto);

    TbCdcmrExportTask getOutpatientMedicalInfoExport(String loginUserId, InformationHubQueryDTO dto);

    TbCdcmrExportTask getInpatientMedicalInfoExport(String loginUserId, InformationHubQueryDTO dto);
}
