package com.iflytek.cdc.province.service;

import cn.hutool.core.collection.CollUtil;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.dto.SourceKeyDTO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoDetailVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO;
import com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo;
import com.iflytek.cdc.province.enums.DeleteFlagEnum;
import com.iflytek.cdc.province.enums.LoOutcomeStatusEnum;
import com.iflytek.cdc.province.enums.ProcessInfoTypeEnum;
import com.iflytek.cdc.province.mapper.bu.TbCdcewEmergencyEventProcessInfoMapper;
import com.iflytek.cdc.province.model.dto.MsProcessLogQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoConditionQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.vo.*;
import com.iflytek.cdc.province.utils.DateUtils;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import org.apache.commons.lang.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class CommonProcessInfoService {

    @Resource
    private List<ProcessInfoServiceBase> processInfoServiceBases;

    @Resource
    private TbCdcewEmergencyEventProcessInfoMapper emergencyEventProcessInfoMapper;

    @Value("${fpva.cdc.batchSize:1000}")
    private int batchSize;

    /**
     * 病例数指标
     */
    public List<MedCntIndicatorVO> listMedCntIndicator(String processType, MsProcessSimpleInfoQueryDTO queryDTO){
        ProcessInfoServiceBase processInfoService = getProcessInfoService(processType);
        queryDTO.setProcessType(processType);
        List<MedCntIndicatorVO> medCntIndicatorVOS = processInfoService.listMedCntIndicator(queryDTO);
        Map<String, MedCntIndicatorVO> medCntIndicatorVOMap = medCntIndicatorVOS.stream().collect(Collectors.toMap(MedCntIndicatorVO::getStatDate, Function.identity()));
        List<MedCntIndicatorVO> outcomeCntIndicator = processInfoService.listOutcomeCntIndicator(queryDTO);
        Map<String, MedCntIndicatorVO> outComeIndicatorVOMap = outcomeCntIndicator.stream().collect(Collectors.toMap(MedCntIndicatorVO::getStatDate, Function.identity()));
        Date startDate = queryDTO.getStartDate();
        Date endDate = queryDTO.getEndDate();
        if (startDate == null || endDate == null){
            if (!medCntIndicatorVOS.isEmpty()){
                startDate = DateUtils.formatDate(medCntIndicatorVOS.get(0).getStatDate());
                endDate = DateUtils.formatDate(medCntIndicatorVOS.get(medCntIndicatorVOS.size()-1).getStatDate());
            }
            if (!outcomeCntIndicator.isEmpty()){
                Date outComeStatStartDate = DateUtils.formatDate(outcomeCntIndicator.get(0).getStatDate());
                Date outComeStatEndDate = DateUtils.formatDate(outcomeCntIndicator.get(outcomeCntIndicator.size() -1 ).getStatDate());
                startDate = startDate == null || outComeStatStartDate.before(startDate) ? outComeStatStartDate : startDate ;
                endDate = endDate == null || outComeStatEndDate.after(endDate) ? outComeStatEndDate : endDate;
            }
        }
        if (startDate == null || endDate == null){
            return new ArrayList<>();
        }
        List<String> days = DateUtils.getDatesBetweenAsStr(startDate, endDate);
        int totalMedCaseCnt = 0;
        int totalDeathCnt = 0;
        int totalDiseaseDeathCnt = 0;
        List<MedCntIndicatorVO> results = new ArrayList<>();
        for (String d : days){
            MedCntIndicatorVO medCntIndicatorVO = new MedCntIndicatorVO();
            medCntIndicatorVO.setStatDate(d);
            MedCntIndicatorVO medCnt = medCntIndicatorVOMap.get(d);
            if (medCnt == null){
                medCntIndicatorVO.setMedCaseCnt(0);
            }else {
                medCntIndicatorVO.setMedCaseCnt(medCnt.getMedCaseCnt());
            }
            MedCntIndicatorVO outComeCnt = outComeIndicatorVOMap.get(d);
            if (outComeCnt == null){
                medCntIndicatorVO.setDeathCnt(0);
                medCntIndicatorVO.setRecoverCnt(0);
                medCntIndicatorVO.setDiseaseDeathCnt(0);
            }else {
                medCntIndicatorVO.setDeathCnt(outComeCnt.getDeathCnt());
                medCntIndicatorVO.setRecoverCnt(outComeCnt.getRecoverCnt());
                medCntIndicatorVO.setDiseaseDeathCnt(outComeCnt.getDiseaseDeathCnt());
            }
            totalMedCaseCnt += medCntIndicatorVO.getMedCaseCnt();
            totalDeathCnt  += medCntIndicatorVO.getDeathCnt();
            totalDiseaseDeathCnt += medCntIndicatorVO.getDiseaseDeathCnt();
            medCntIndicatorVO.setTotalMedCaseCnt(totalMedCaseCnt);
            medCntIndicatorVO.setTotalDeathCnt(totalDeathCnt);
            medCntIndicatorVO.setTotalDiseaseDeathCnt(totalDiseaseDeathCnt);
            results.add(medCntIndicatorVO);
        }
        return results;
    }

    /**
     * 病例数指标 区域统计
     */
    public List<AreaMedCntIndicatorVO> listAreaMedCntIndicator(String processType, MsProcessSimpleInfoQueryDTO queryDTO){
        ProcessInfoServiceBase processInfoService = getProcessInfoService(processType);
        List<AreaMedCntIndicatorVO> medCntIndicatorVOS = processInfoService.listAreaMedCntIndicator(queryDTO);
        Map<String, List<AreaMedCntIndicatorVO>> medCntIndicatorVOMap = medCntIndicatorVOS.stream().
                filter(m -> StringUtils.isNotEmpty(m.getAreaLevel())
                        && StringUtils.isNotEmpty(m.getAreaCode())
                        && StringUtils.isNotEmpty(m.getAreaName())
                ).
                collect(Collectors.groupingBy(AreaMedCntIndicatorVO::getAreaCode));
        List<AreaMedCntIndicatorVO> outcomeCntIndicator = processInfoService.listAreaOutcomeCntIndicator(queryDTO);
        Map<String, List<AreaMedCntIndicatorVO>> outComeIndicatorVOMap = outcomeCntIndicator.stream().
                filter(m -> StringUtils.isNotEmpty(m.getAreaLevel())
                        && StringUtils.isNotEmpty(m.getAreaCode())
                        && StringUtils.isNotEmpty(m.getAreaName())
                ).collect(Collectors.groupingBy(AreaMedCntIndicatorVO::getAreaCode));
        Set<String> areaCodes = new HashSet<>(medCntIndicatorVOMap.keySet());
        areaCodes.addAll(outComeIndicatorVOMap.keySet());
        List<String> days = DateUtils.getDatesBetweenAsStr(queryDTO.getStartDate(), queryDTO.getEndDate());
        List<AreaMedCntIndicatorVO> results = new ArrayList<>();
        for (String areaCode : areaCodes){
            List<AreaMedCntIndicatorVO> areaMedCntIndicatorVOS = medCntIndicatorVOMap.get(areaCode);
            List<AreaMedCntIndicatorVO> areaOutcomeCntIndicatorVOS = outComeIndicatorVOMap.get(areaCode);
            
            AreaMedCntIndicatorVO temp = areaMedCntIndicatorVOS == null ? areaOutcomeCntIndicatorVOS.get(0) : areaMedCntIndicatorVOS.get(0);
            Map<String, AreaMedCntIndicatorVO> areaDateMedMap = new HashMap<>();
            Map<String, AreaMedCntIndicatorVO> areaDateOutcomeMap = new HashMap<>();
            if (areaMedCntIndicatorVOS != null){
                areaDateMedMap = areaMedCntIndicatorVOS.stream().collect(Collectors.toMap(AreaMedCntIndicatorVO::getStatDate, Function.identity()));
            }
            if (areaOutcomeCntIndicatorVOS != null){
                areaDateOutcomeMap = areaOutcomeCntIndicatorVOS.stream().collect(Collectors.toMap(AreaMedCntIndicatorVO::getStatDate, Function.identity()));
            }
            int totalMedCaseCnt = 0;
            int totalDeathCnt = 0;
            int totalDiseaseDeathCnt = 0;
            for (String d : days){
                AreaMedCntIndicatorVO areaMedCntIndicatorVO = new AreaMedCntIndicatorVO();
                areaMedCntIndicatorVO.setStatDate(d);
                areaMedCntIndicatorVO.setAreaCode(areaCode);
                areaMedCntIndicatorVO.setAreaLevel(temp.getAreaLevel());
                areaMedCntIndicatorVO.setAreaName(temp.getAreaName());
                MedCntIndicatorVO medCnt = areaDateMedMap.get(d);
                if (medCnt == null){
                    areaMedCntIndicatorVO.setMedCaseCnt(0);
                }else {
                    areaMedCntIndicatorVO.setMedCaseCnt(medCnt.getMedCaseCnt());
                }
                MedCntIndicatorVO outComeCnt = areaDateOutcomeMap.get(d);
                if (outComeCnt == null){
                    areaMedCntIndicatorVO.setDeathCnt(0);
                    areaMedCntIndicatorVO.setRecoverCnt(0);
                    areaMedCntIndicatorVO.setDiseaseDeathCnt(0);
                }else {
                    areaMedCntIndicatorVO.setDeathCnt(outComeCnt.getDeathCnt());
                    areaMedCntIndicatorVO.setRecoverCnt(outComeCnt.getRecoverCnt());
                    areaMedCntIndicatorVO.setDiseaseDeathCnt(outComeCnt.getDiseaseDeathCnt());
                }
                totalMedCaseCnt += areaMedCntIndicatorVO.getMedCaseCnt();
                totalDeathCnt  += areaMedCntIndicatorVO.getDeathCnt();
                totalDiseaseDeathCnt += areaMedCntIndicatorVO.getDiseaseDeathCnt();
                areaMedCntIndicatorVO.setTotalMedCaseCnt(totalMedCaseCnt);
                areaMedCntIndicatorVO.setTotalDeathCnt(totalDeathCnt);
                areaMedCntIndicatorVO.setTotalDiseaseDeathCnt(totalDiseaseDeathCnt);
                results.add(areaMedCntIndicatorVO);
            }
        }
        return results;
    }

    /**
     * 根据id查询地址相关信息
     */
    public List<MsProcessInfoAddressVO> listAddressByIds(String processType, MsProcessSimpleInfoQueryDTO dto){

        ProcessInfoServiceBase processInfoService = getProcessInfoService(processType);
        dto.setProcessType(processType);
        List<SourceKeyDTO> list = dto.getSourceKeyDTOS();
        if (CollUtil.isNotEmpty(list)){
            List<String> ids = list.stream().map(SourceKeyDTO::getSourceKey).collect(Collectors.toList());
            dto.setIds(ids);
            return processInfoService.listAddressByIds(dto);
        }
        return new ArrayList<>();
    }


    /**
     * 分页查询病程简单信息
     */
    public PageInfo<MsProcessSimpleInfoVO> simpleInfoPageList(String processType, MsProcessSimpleInfoQueryDTO queryDTO){

        ProcessInfoServiceBase processInfoService = getProcessInfoService(processType);
        queryDTO.setProcessType(processType);
        return processInfoService.simpleInfoPageList(queryDTO);
    }

    /**
     * 根据id单个查询病程简易信息
     */
    public MsProcessSimpleInfoDetailVO loadProcessSimple(String processType, String id){
        ProcessInfoServiceBase processInfoService = getProcessInfoService(processType);
        return processInfoService.loadProcessSimpleById(id);
    }

    /**
     * 导出病程简单信息
     */
    public TbCdcmrExportTask exportSimpleInfo(String processType, MsProcessSimpleInfoQueryDTO queryDTO){
        ProcessInfoServiceBase processInfoService = getProcessInfoService(processType);
        queryDTO.setProcessType(processType);
        return processInfoService.exportSimpleInfo(queryDTO);
    }


    /**
     * 查询患者信息
     */
   public List<MsPatientInfoVO> listPatientInfoByProcessIds(String processType, MsProcessSimpleInfoQueryDTO dto){
       ProcessInfoServiceBase processInfoService = getProcessInfoService(processType);
       dto.setProcessType(processType);
       return processInfoService.listPatientInfoByProcessIds(dto);
   }

    /**
     * 查询简单病程信息
     */
    public List<MsProcessSimpleInfoVO> listSimpleInfo(String processType, MsProcessSimpleInfoQueryDTO queryDTO){
        ProcessInfoServiceBase processInfoService = getProcessInfoService(processType);
        queryDTO.setProcessType(processType);
        return processInfoService.listSimpleInfo(queryDTO);
    }

    /**
     * 查询病程模型简易信息
     */
    public Object listProcessModelSimpleInfo(String processType, MsProcessSimpleInfoQueryDTO queryDTO){
        ProcessInfoServiceBase processInfoService = getProcessInfoService(processType);
        return processInfoService.listProcessModelSimpleInfo(queryDTO);
    }

    /**
     * 病程时间轴
     */
    public List<MsProcessLogVO> listProcessLog(MsProcessLogQueryDTO queryDTO){
        ProcessInfoServiceBase processInfoService = getProcessInfoService(queryDTO.getProcessType());
        return processInfoService.listProcessLog(queryDTO);
    }


    /**
     * 监测简易信息
     */
    public SurveillanceReportVO loadSurveillanceReportVO(String processType, MsProcessSimpleInfoQueryDTO queryDTO){
        List<MsProcessSimpleInfoVO> processInfoVOS = listSimpleInfo (processType, queryDTO);
        if (processInfoVOS.isEmpty()){
            return null;
        }
        MsProcessSimpleInfoVO fiPatientInfo = processInfoVOS.stream().filter(r -> r.getFiVisitTime() != null).min(Comparator.comparing(MsProcessSimpleInfoVO::getFiVisitTime)).orElse(null);
        MsProcessSimpleInfoVO liPatientInfo = processInfoVOS.stream().filter(r -> r.getLvVisitTime() != null).max(Comparator.comparing(MsProcessSimpleInfoVO::getLvVisitTime)).orElse(null);
        return getSurveillanceReportVO(fiPatientInfo, liPatientInfo);
    }

    private SurveillanceReportVO getSurveillanceReportVO(MsProcessSimpleInfoVO fiPatientInfo, MsProcessSimpleInfoVO liPatientInfo) {
        SurveillanceReportVO reportVO = new SurveillanceReportVO();
        if (fiPatientInfo != null){
            reportVO.setFirstCaseVisitTime(fiPatientInfo.getFiVisitTime());
            reportVO.setFirstCaseOrgName(fiPatientInfo.getFiOrgName());
        }
        if (fiPatientInfo == null || (liPatientInfo != null && !liPatientInfo.getId().equals(fiPatientInfo.getId()))){
            reportVO.setLatestCaseVisitTime(liPatientInfo.getLvVisitTime());
            reportVO.setLatestCaseOrgName(liPatientInfo.getLvOrgName());
        }
        return reportVO;
    }

    private ProcessInfoServiceBase getProcessInfoService(String processType){
        return processInfoServiceBases.stream().filter(p -> p.getProcessInfoType().equals(ProcessInfoTypeEnum.MUL.getCode())).findFirst().orElseThrow(() -> new MedicalBusinessException("未找到服务"));
    }

    @Transactional
    public void syncProcessInfoBy(String processType, List<String> ids) {
        ProcessInfoServiceBase processInfoService = getProcessInfoService(processType);
        List<TbCdcewEmergencyEventProcessInfo> processInfoList = processInfoService.getProcessInfoBy(ids);
        processInfoList.forEach(info ->{
            info.setDiseaseType(processType);
            info.setProcessType(processType);
        });
        if (CollUtil.isNotEmpty(processInfoList)) {
            //同步病例默认未删除
            processInfoList.forEach(e -> e.setDeleteFlag(DeleteFlagEnum.NO.getCode()));
            Lists.partition(processInfoList, batchSize).forEach(e -> 
                emergencyEventProcessInfoMapper.insertBatch(e)
            );
        }
    }

    public ProcessPathogenInfoVO getProcessPathogenInfoBy(String processType, List<String> ids) {

        return getProcessInfoService(processType).getProcessPathogenInfoBy(ids);
    }

    public PageInfo<MsProcessSimpleInfoVO> syndromeSimpleInfoPageListByConditions(String processType, MsProcessSimpleInfoConditionQueryDTO queryDTO) {
        ProcessInfoServiceBase processInfoService = getProcessInfoService(processType);
        return processInfoService.syndromeSimpleInfoPageListByConditions(queryDTO);
    }

    /**
     * 分页查询治疗情况
     */
    public PageInfo<PatientTreatmentInfoVO> pageListTreatment(String processType, MsProcessSimpleInfoQueryDTO dto){
        ProcessInfoServiceBase processInfoService = getProcessInfoService(processType);
        dto.setOutComeStatus(LoOutcomeStatusEnum.CURE.getDesc());
        dto.setProcessType(processType);
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        List<MsProcessSimpleInfoVO> msProcessSimpleInfoVOS = processInfoService.listSimpleInfo(dto);
        return new PageInfo<>(msProcessSimpleInfoVOS).convert(m -> {
            PatientTreatmentInfoVO patientTreatmentInfoVO = new PatientTreatmentInfoVO();
            BeanUtils.copyProperties(m, patientTreatmentInfoVO);
            return patientTreatmentInfoVO;
        });
    }

    /**
     * 分页查询死亡情况
     */
    public PageInfo<PatientDeathInfoVO> pageListDeath(String processType, MsProcessSimpleInfoQueryDTO dto){
        ProcessInfoServiceBase processInfoService = getProcessInfoService(processType);
        dto.setOutComeStatus(LoOutcomeStatusEnum.DEATH.getDesc());
        dto.setProcessType(processType);
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        List<MsProcessSimpleInfoVO> msProcessSimpleInfoVOS = processInfoService.listSimpleInfo(dto);
        return new PageInfo<>(msProcessSimpleInfoVOS).convert(m -> {
            PatientDeathInfoVO patientDeathInfoVO = new PatientDeathInfoVO();
            BeanUtils.copyProperties(m, patientDeathInfoVO);
            return patientDeathInfoVO;
        });
    }
}
