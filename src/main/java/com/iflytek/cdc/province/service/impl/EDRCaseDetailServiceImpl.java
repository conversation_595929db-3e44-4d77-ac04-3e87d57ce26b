package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.cdc.province.mapper.pg.*;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrPersonInfoVO;
import com.iflytek.cdc.province.entity.ads.AdsEdrVaccinateHistory;
import com.iflytek.cdc.province.model.vo.edrcase.*;
import com.iflytek.cdc.province.service.EDRCaseDetailService;
import lombok.extern.java.Log;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@Configuration
@ConditionalOnProperty(value = "dataService.version", havingValue = "v2")
public class EDRCaseDetailServiceImpl implements EDRCaseDetailService {

    @Resource
    AdsEdrPersonInfoMapper adsEdrPersonInfoMapper;
    @Resource
    AdsEdrRegisterManageInfoMapper adsEdrRegisterManageInfoMapper;
    @Resource
    AdsEdrFollowInfoMapper adsEdrFollowInfoMapper;

    @Resource
    AdsEdrDeadInfoMapper adsEdrDeadInfoMapper;

    @Resource
    AdsEdrPersonStatusInfoMapper adsEdrPersonStatusInfoMapper;
    @Resource
    AdsEdrVaccinateHistoryMapper adsEdrVaccinateHistoryMapper;

    //确诊结果信息
    @Resource
    AdsEdrConfirmDiagInfoMapper adsEdrConfirmDiagInfoMapper;

    //暴露史信息
    @Resource
    AdsEdrExposureHistoryInfoMapper adsEdrExposureHistoryInfoMapper;

    //出入院信息
    @Resource
    AdsEdrHospitalInfoMapper adsEdrHospitalInfoMapper;

    //初步诊断信息
    @Resource
    AdsEdrInitDiagInfoMapper adsEdrInitDiagInfoMapper;

    //体格检查
    @Resource
    AdsEdrPhysicalExamInfoMapper adsEdrPhysicalExamInfoMapper;
     //症状信息
    @Resource
    AdsEdrSymptomInfoMapper dsEdrSymptomInfoMapper;
    //旅居史
    @Resource
    AdsEdrTravelHistoryInfoMapper adsEdrTravelHistoryInfoMapper;

    //治疗用药详情信息
    @Resource
    AdsEdrTreatmentDetailMapper adsEdrTreatmentDetailMapper;

    //影像学检查信息
    @Resource
    AdsEdrRisInfoMapper adsEdrRisInfoMapper;

    //实验室检测信息
    @Resource
    AdsEdrLisInfoMapper adsEdrLisInfoMapper;

    //报卡信息
    @Resource
    AdsRepInfectReportInfoMapper adsRepInfectReportInfoMapper;

    @Resource
    DwsProcessTagMapper dwsProcessTagMapper;



    @Override
    public AdsEdrPersonInfoVO getAdsEdrPersonInfo(String empiId) {
        return adsEdrPersonInfoMapper.getAdsEdrPersonInfo(empiId);
    }

    @Override
    public List<AdsEdrRegisterManageInfoVO> getAdsEdrRegisterManageInfoList(String empiId, String[] eventIds) {
        return adsEdrRegisterManageInfoMapper.getAdsEdrRegisterManageInfoList(empiId,eventIds);
    }

    @Override
    public List<AdsEdrFollowInfoVO> getAdsEdrFollowInfoVOList(String empiId, String[] eventIds) {
        return adsEdrFollowInfoMapper.getAdsEdrFollowInfoVOList(empiId,eventIds);
    }

    @Override
    public AdsEdrDeadInfoVO getAdsEdrDeadInfo(String empiId, String[] eventIds) {
        return adsEdrDeadInfoMapper.getAdsEdrDeadInfo(empiId,eventIds);
    }

    @Override
    public List<AdsEdrVaccinateHistory> getAdsEdrVaccinateHistoryList(String empiId, String[] eventIds) {
        return adsEdrVaccinateHistoryMapper.getAdsEdrVaccinateHistoryList(empiId,eventIds);
    }

    @Override
    public List<AdsEdrProcessTagVO> getAdsEdrProcessTagList(String empiId) {
        List<AdsEdrProcessTagVO> list = adsEdrPersonStatusInfoMapper.getAdsEdrProcessTagList(empiId);
        List<AdsEdrProcessTagVO> newList = new ArrayList<>();
        
        if (CollectionUtil.isNotEmpty(list)) {
            // 按照 infectedSubName 分组，收集 eventId
            Map<String, List<String>> groupedByInfectedSubName = list.stream()
                    .collect(Collectors.groupingBy(
                            AdsEdrProcessTagVO::getInfectedSubName,
                            Collectors.mapping(AdsEdrProcessTagVO::getId, Collectors.toList())
                    ));
            
            // 构造新的返回列表
            for (Map.Entry<String, List<String>> entry : groupedByInfectedSubName.entrySet()) {
                AdsEdrProcessTagVO newVo = new AdsEdrProcessTagVO();
                newVo.setInfectedSubName(entry.getKey());
                newVo.setEventIds(entry.getValue().toArray(new String[0]));
                newList.add(newVo);
            }
        }
        
        return newList;
    }

    @Override
    public List<AdsEdrPersonStatusInfoVO> getAdsEdrPersonStatusInfoList(String empiId, String[] eventIds) {
        return adsEdrPersonStatusInfoMapper.getAdsEdrPersonStatusInfoList(empiId, eventIds);
    }

    @Override
    public List<DwsProcessTagVO> getDwsProcessTagList(String empiId) {
        return dwsProcessTagMapper.getDwsProcessTagList(empiId);
    }


    /**
     * 根据empiId查询报卡信息
     * @param empiId
     * @return
     */
    @Override
    public List<AdsRepInfectReportInfoVO> findReportByempiIdList(String empiId) {
        if(StrUtil.isEmpty(empiId)){
            log.info("主索引id不能为空！");
            return null;
        }
        return adsRepInfectReportInfoMapper.findReportByempiIdList(empiId);
    }

    /**
     * 根据事件id查询患者edr疾病详情信息
     * @param eventId
     * @return
     */
    @Override
    public AdsEdrDiseaseDetailVO getAdsEdrDiseaseDetail(String eventId) {
        if(StrUtil.isEmpty(eventId)){
            log.info("事件id不能为空！");
            return null;
        }
        AdsEdrDiseaseDetailVO adsEdrDiseaseDetail = new AdsEdrDiseaseDetailVO();
        //体格检查
        adsEdrDiseaseDetail.setAdsEdrPhysicalExamInfoList(adsEdrPhysicalExamInfoMapper.getPhysicalExamByEventId(eventId));
        //症状信息
        adsEdrDiseaseDetail.setAdsEdrSymptomInfoList(dsEdrSymptomInfoMapper.getSymptomByEventId(eventId));
        //旅居史
        adsEdrDiseaseDetail.setAdsEdrTravelHistoryInfoList(adsEdrTravelHistoryInfoMapper.getTravelHistoryByEventId(eventId));
        //暴露史
        adsEdrDiseaseDetail.setAdsEdrExposureHistoryInfoList(adsEdrExposureHistoryInfoMapper.getExposureHistoryByEventId(eventId));
        //确诊结果
        adsEdrDiseaseDetail.setAdsEdrConfirmDiagInfoList(adsEdrConfirmDiagInfoMapper.getDiagInfoByEventId(eventId));
        //初步诊断
        adsEdrDiseaseDetail.setAdsEdrInitDiagInfoList(adsEdrInitDiagInfoMapper.getDiagInfoByEventId(eventId));
        //出入院
        adsEdrDiseaseDetail.setAdsEdrHospitalInfoList(adsEdrHospitalInfoMapper.getHospitalByEventId(eventId));
        //用药信息
        adsEdrDiseaseDetail.setAdsEdrTreatmentInfoMap(groupTreatment(eventId));
        return adsEdrDiseaseDetail;
    }

    /**
     * 根据事件id查询影像学检查信息
     */
    @Override
    public List<AdsEdrRisInfoVO> getRisByEventId(String eventId) {
        if(StrUtil.isEmpty(eventId)){
            log.info("事件id不能为空！");
            return null;
        }
        return adsEdrRisInfoMapper.getRisByEventId(eventId);
    }

    /**
     * 根据事件id查询实验室检测信息
     * @param eventId
     * @return
     */
    @Override
    public List<AdsEdrLisInfoVO> getLisByEventId(String eventId) {
        if(StrUtil.isEmpty(eventId)){
            log.info("事件id不能为空！");
            return null;
        }
        return adsEdrLisInfoMapper.getLisByEventId(eventId);
    }



    //edr用药信息分组
    public Map<List<Object>, List<AdsEdrTreatmentDetailVO>> groupTreatment(String eventId) {
        Map<List<Object>, List<AdsEdrTreatmentDetailVO>> treatmentDetailMap = new HashMap<>();
        List<AdsEdrTreatmentDetailVO> treatmentDetaiList = adsEdrTreatmentDetailMapper.getTreatmentDetaiByEventId(eventId);
        if (CollectionUtil.isNotEmpty(treatmentDetaiList)) {
            Function<AdsEdrTreatmentDetailVO, List<Object>> keyExtractor = key ->
                    Arrays.asList(
                    key.getId(),
                    key.getEtlCreateDatetime(),
                    key.getEtlUpdateDatetime(),
                    key.getEmpiId(),
                    key.getEventId(),
                    key.getEndTreatmentReason(),
                    key.getNonTreatmentReason(),
                    key.getBeginTreatmentDate(),
                    key.getRegistrationNo(),
                    key.getStopTreatmentDate(),
                    key.getRecoveryDays(),
                    key.getNonTreatmentReasonCode(),
                    key.getEndTreatmentReasonCode(),
                    key.getTreatmentShceme(),
                    key.getTreatmentShcemeCode(),
                    key.getTreatmentCategoryCode(),
                    key.getTreatmentType(),
                    key.getTreatmentTypeCode());
            treatmentDetailMap = treatmentDetaiList.stream().collect(Collectors.groupingBy(keyExtractor));

        }
        return treatmentDetailMap;
    }

}
