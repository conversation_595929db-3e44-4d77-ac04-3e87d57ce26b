package com.iflytek.cdc.province.service;

import cn.hutool.core.collection.CollectionUtil;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.enums.MasterDataEnum;
import com.iflytek.cdc.edr.vo.WarningSignalVO;
import com.iflytek.cdc.province.enums.MultichannelTopicConfigEnum;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.TbCdcmrMultichannelTopicConfig;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.TopicConfigInfoVO;
import com.iflytek.cdc.province.model.vo.TreeNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class IntegratedUtilsService {

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    /**
     * 设置多渠道综合预警病原 查询参数
     * */
    public <T> void setIntegratedPathogenQuery(T t,
                                               Function<T, List<String>> pathogenIdFunction,
                                               BiConsumer<T, List<String>> pathogenNameList){
        processTopicQuery(t, pathogenIdFunction, pathogenNameList, MultichannelTopicConfigEnum.PATHOGEN.getCode(), this::getAllPathogenNameBy);
    }


    private <T> void processTopicQuery(T t,
                                       Function<T, List<String>> queryFunction,
                                       BiConsumer<T, List<String>> queryListConsumer,
                                       String configKey,
                                       Function<List<String>, List<String>> dataProcessor) {
//        VirusCheckQueryDTO::getPathogenIdList
        List<String> queryCodes = queryFunction != null ? queryFunction.apply(t) : new ArrayList<>();

//        pathogenNameList
        if (queryListConsumer != null) {
            List<String> processedData = dataProcessor != null ? dataProcessor.apply(queryCodes) : queryCodes;
            queryListConsumer.accept(t, processedData);
        }
    }

    /**
     * 根据前端传参的病原id 查询该病原id以及所有子类的name
     * */
    public List<String> getAllPathogenNameBy(List<String> pathogenIdList){

        List<TreeNode> root = adminServiceApi.getMasterDataInfo(MasterDataEnum.PATHOGEN.getCode());
        if (CollectionUtil.isEmpty(pathogenIdList) || CollectionUtil.isEmpty(root)) {
            return null;
        }
        List<String> res = new ArrayList<>();
        pathogenIdList.forEach(e->{
            for (TreeNode node : root) {
                TreeNode.getAllNodeBy(res, node, e, TreeNode::getId, TreeNode::getLabel);
            }
        });
        return res.stream().distinct().collect(Collectors.toList());
    }

}
