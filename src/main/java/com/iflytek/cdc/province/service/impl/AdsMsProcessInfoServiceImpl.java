package com.iflytek.cdc.province.service.impl;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.iflytek.cdc.edr.vo.adsMsProcess.*;
import com.iflytek.cdc.province.enums.FlagEnum;
import com.iflytek.cdc.province.mapper.pg.AdsMsProcessSyndromeAlResultMapper;
import com.iflytek.cdc.province.mapper.pg.AdsMulProcessCaseInfoMapper;
import com.iflytek.cdc.province.model.dto.MsProcessLogQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.mapper.pg.AdsMsProcessInfoMapper;
import com.iflytek.cdc.province.model.vo.*;
import com.iflytek.cdc.province.service.AdsMsProcessInfoService;
import com.iflytek.cdc.province.service.ExportTaskService;
import com.iflytek.cdc.province.utils.GsonUtils;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AdsMsProcessInfoServiceImpl implements AdsMsProcessInfoService {
    @Resource
    private AdsMsProcessInfoMapper adsMsProcessInfoMapper;

    @Resource
    private AdsMsProcessSyndromeAlResultMapper adsMsProcessSyndromeAlResultMapper;

    @Resource
    private ExportTaskService exportTaskService;

    @Resource
    private AdsMulProcessCaseInfoMapper adsMulProcessCaseInfoMapper;


    @Override
    public List<MsProcessSimpleInfoVO> listInfectedSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listInfectedSimpleInfo(queryDTO);
    }

    @Override
    public List<MsProcessMonitorInfoVO> listSyndromeProcessDetailInfo(MsProcessSimpleInfoQueryDTO dto) {

        return adsMulProcessCaseInfoMapper.listSyndromeProcessDetailInfo(dto);
    }
}
