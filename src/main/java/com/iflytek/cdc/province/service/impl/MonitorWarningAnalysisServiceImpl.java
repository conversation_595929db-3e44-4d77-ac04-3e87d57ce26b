package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.apiService.UapServiceApi;
import com.iflytek.cdc.edr.common.Common;
import com.iflytek.cdc.edr.enums.DateDimEnum;
import com.iflytek.cdc.edr.utils.DateFormatUtil;
import com.iflytek.cdc.province.mapper.pg.MonitorWarningAnalysisMapper;
import com.iflytek.cdc.province.model.dto.AdsMsProcessReqDTO;
import com.iflytek.cdc.province.model.dto.MonitorWarningAnalysisQueryDTO;
import com.iflytek.cdc.province.model.vo.*;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.service.MonitorWarningAnalysisService;
import com.iflytek.cdc.province.utils.DateUtils;
import com.iflytek.cdc.province.utils.PageUtils;
import com.iflytek.cdc.province.utils.StatUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class MonitorWarningAnalysisServiceImpl implements MonitorWarningAnalysisService {


    @Resource
    private UapServiceApi uapServiceApi;

    @Resource
    private MonitorWarningAnalysisMapper monitorWarningAnalysisMapper;

    @Override
    public ThreeCatePeopleViewVO getThreeCatePeopleView(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName) {
        setAreaInfoList(queryDTO, loginUserName);
        return monitorWarningAnalysisMapper.threeCatePeopleView(queryDTO);
    }


    @Override
    public List<ThreeCatePeopleViewVO> threeCatePeopleTimeTrend(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName) {
        setAreaInfoList(queryDTO, loginUserName);
        fullDateRange(queryDTO);
        if (queryDTO.getStartDate() != null && queryDTO.getEndDate() != null) {
            queryDTO.setStartDate(DateFormatUtil.getTheFistSecondOfOneDay(queryDTO.getStartDate()));
            queryDTO.setEndDate(DateFormatUtil.getTheLastSecondOfOneDay(queryDTO.getEndDate()));
        }
        List<ThreeCatePeopleViewVO> voList = monitorWarningAnalysisMapper.threeCatePeopleTimeTrend(queryDTO);
        return StatUtils.fulfillDateData(voList, queryDTO.getStartDate(), queryDTO.getEndDate(), DateDimEnum.DAY,
                ThreeCatePeopleViewVO::getStatDate,
                ThreeCatePeopleViewVO::setStatDate,
                ThreeCatePeopleViewVO.dataConverter
        );
    }

    /**
     * 韦恩图
     */
    @Override
    public ThreeCatePeopleViewVO veenView(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName) {
        setAreaInfoList(queryDTO, loginUserName);
        return monitorWarningAnalysisMapper.vennView(queryDTO);
    }

    /**
     * 设置区域信息多选
     */
    private void setAreaInfoList(AdsMsProcessReqDTO queryDTO, String loginUserName) {
        uapServiceApi.setProvinceAndCityAndDistrict(loginUserName, queryDTO,
                AdsMsProcessReqDTO::setProvinceCodes,
                AdsMsProcessReqDTO::setCityCodes,
                AdsMsProcessReqDTO::setDistrictCodes,
                (d, areaLevel) -> {
                    if (d.getAreaLevel() == null) {
                        d.setAreaLevel(areaLevel);
                    }
                });
        // 融合权限和过滤条件
        if (CollectionUtil.isNotEmpty(queryDTO.getDistrictCodeList())) {
            if (CollUtil.isEmpty(queryDTO.getDistrictCodes())) {
                queryDTO.setDistrictCodes(queryDTO.getDistrictCodeList());
            } else {
                queryDTO.setDistrictCodes(CommonUtilService.intersection(queryDTO.getDistrictCodeList(), queryDTO.getDistrictCodes()));
            }
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getCityCodeList())) {
            if (CollUtil.isEmpty(queryDTO.getCityCodes())) {
                queryDTO.setCityCodes(queryDTO.getCityCodeList());
            } else {
                queryDTO.setCityCodes(CommonUtilService.intersection(queryDTO.getCityCodeList(), queryDTO.getCityCodes()));
            }
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getStreetCodeList())) {
            if (CollUtil.isEmpty(queryDTO.getStreetCodes())) {
                queryDTO.setStreetCodes(queryDTO.getStreetCodeList());
            } else {
                queryDTO.setStreetCodes(CommonUtilService.intersection(queryDTO.getStreetCodeList(), queryDTO.getStreetCodes()));
            }
        } else {
            queryDTO.setStreetCodes(new ArrayList<>());
        }
    }

    @Override
    public ReportOverallVO reportOverall(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName) {
        setAreaInfoList(queryDTO, loginUserName);
        return monitorWarningAnalysisMapper.reportOverall(queryDTO);
    }


    @Override
    public PageInfo<DiagnoseProcessVO> pageDiagnoseProcess(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName) {
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        setAreaInfoList(queryDTO, loginUserName);
        return PageInfo.of(monitorWarningAnalysisMapper.selectDiagnoseProcess(queryDTO));
    }

    @Override
    public PatientVisitDetailVO getPatientDetailByProcessId(String processId) {
        return monitorWarningAnalysisMapper.selectPatientDetailByProcessId(processId);
    }

    /**
     * 报卡病例列表
     */
    @Override
    public PageInfo<ReportProcessVO> pageReportProcess(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName) {
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        setAreaInfoList(queryDTO, loginUserName);
        fullDateRange(queryDTO);
        return PageInfo.of(monitorWarningAnalysisMapper.selectReportProcess(queryDTO));
    }

    @Override
    public ReportProcessVO getReportProcessDetailByProcessId(String processId) {
        return monitorWarningAnalysisMapper.selectReportProcessByProcessId(processId);
    }

    /**
     * 应排查病例统计
     */
    @Override
    public InvestigatedCaseVO investigatedCaseStat(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName) {
        setAreaInfoList(queryDTO, loginUserName);
        return monitorWarningAnalysisMapper.investigatedCaseStat(queryDTO);
    }

    /**
     * 应排查病例列表
     */
    @Override
    public PageInfo<InvestigatedCaseVO> pageInvestigatedCase(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName) {
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        setAreaInfoList(queryDTO, loginUserName);
        return PageInfo.of(monitorWarningAnalysisMapper.selectInvestigatedCase(queryDTO));
    }

    /**
     * 应排查任务统计
     */
    @Override
    public InvestigatedTaskVO investigatedTaskStat(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName) {
        setAreaInfoList(queryDTO, loginUserName);
        InvestigatedTaskVO taskVO = monitorWarningAnalysisMapper.investigatedTaskStat(queryDTO);
        taskVO.calculateAllRates();
        return taskVO;
    }

    /**
     * 应排查任务区域统计
     */
    @Override
    public PageInfo<InvestigatedTaskVO> pageTaskStatAreaGroup(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName) {
        setAreaInfoList(queryDTO, loginUserName);
        List<InvestigatedTaskVO> voList = monitorWarningAnalysisMapper.taskStatAreaGroup(queryDTO);
        if (voList.isEmpty()) {
            return PageInfo.emptyPageInfo();
        }
        InvestigatedTaskVO sumVo = InvestigatedTaskVO.ofSum(voList);
        voList.add(0, sumVo);
        voList.forEach(InvestigatedTaskVO::calculateAllRates);
        return PageUtils.ofPage(voList, queryDTO.getPageIndex(), queryDTO.getPageSize());
    }


    @Override
    public List<InvestigatedTaskVO> taskStatAreaGroup(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName) {
        setAreaInfoList(queryDTO, loginUserName);
        List<InvestigatedTaskVO> voList = monitorWarningAnalysisMapper.taskStatAreaGroup(queryDTO);
        InvestigatedTaskVO sumVo = InvestigatedTaskVO.ofSum(voList);
        voList.add(0, sumVo);
        return voList;
    }

    /**
     * 应排查任务区域统计
     */
    @Override
    public byte[] taskStatAreaGroupExport(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName) {
        List<InvestTaskStatVO> voList = this.checkTaskStat(queryDTO, loginUserName);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        EasyExcel.write(baos, InvestTaskStatVO.class)
                .sheet("应排查任务区域统计")
                .doWrite(voList);
        return baos.toByteArray();
    }


    @Override
    public List<InvestTaskStatVO> checkTaskStat(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName) {
        List<InvestTaskStatVO> voList = monitorWarningAnalysisMapper.checkTaskStat(queryDTO);
        InvestTaskStatVO sumVo = InvestTaskStatVO.sumCheckTask(voList);
        voList.add(0, sumVo);
        voList.forEach(InvestTaskStatVO::calculateAllRates);
        return voList;
    }

    @Override
    public PageInfo<InvestTaskStatVO> checkTaskStatPageList(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName) {
        List<InvestTaskStatVO> voList = this.checkTaskStat(queryDTO, loginUserName);
        return PageUtils.ofPage(voList, queryDTO.getPageIndex(), queryDTO.getPageSize());
    }

    @Override
    public List<InvestTaskStatVO> investTaskStat(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName) {
        List<InvestTaskStatVO> voList = new ArrayList<>();
        List<InvestTaskStatVO> reportVoList = monitorWarningAnalysisMapper.investTaskStatReport(queryDTO);
        List<InvestTaskStatVO> investVoList = monitorWarningAnalysisMapper.investTaskStatInfect(queryDTO);
        voList.addAll(reportVoList);
        voList.addAll(investVoList);

        InvestTaskStatVO sumVo = InvestTaskStatVO.sumInvestTask(voList);
        voList.add(0, sumVo);
        return voList;
    }

    // 设置默认日期范围(当不选择日期范围时)
    private void fullDateRange(MonitorWarningAnalysisQueryDTO queryDTO) {
        Date startDate = queryDTO.getStartDate();
        Date endDate = queryDTO.getEndDate();
        if (startDate == null && endDate == null) {
            endDate = new Date();
            startDate = DateUtils.addDays(endDate, -30);
        } else if (startDate != null && endDate == null) {
            endDate = new Date();
        } else {
            startDate = DateUtils.addDays(endDate, -30);
        }
        queryDTO.setStartDate(startDate);
        queryDTO.setEndDate(endDate);
    }

}
