package com.iflytek.cdc.province.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.dto.org.OrgParams;
import com.iflytek.cdc.edr.enums.QueryOrgTypeSourceTypeEnum;
import com.iflytek.cdc.province.mapper.pg.OrgCommonMapper;
import com.iflytek.cdc.province.model.dto.dm.OrgQueryDTO;
import com.iflytek.cdc.province.model.vo.OrgInfoVO;
import com.iflytek.cdc.province.model.vo.TreeNode;
import com.iflytek.cdc.province.model.vo.ValueDomainVO;
import com.iflytek.cdc.province.service.OrgCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class OrgCommonServiceImpl implements OrgCommonService {

    @Resource
    private OrgCommonMapper orgCommonMapper;

    @Override
    public List<ValueDomainVO> getAllOrgInfo() {

        return orgCommonMapper.getAllOrgInfo();
    }

    @Override
    public List<OrgInfoVO> getAllOrgList(OrgParams orgParams) {

        List<String> sourceType = new ArrayList<>();
        if(Objects.nonNull(orgParams)) {
            sourceType = QueryOrgTypeSourceTypeEnum.getValues(orgParams.getQueryOrgType());
        }
        return orgCommonMapper.getAllOrgList(orgParams, sourceType);
    }

    @Override
    public List<TreeNode> getOrgList(OrgQueryDTO dto) {

        return orgCommonMapper.getOrgListBy(dto);
    }
}
