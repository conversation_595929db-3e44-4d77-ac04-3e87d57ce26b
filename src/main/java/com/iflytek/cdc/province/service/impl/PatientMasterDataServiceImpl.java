package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import com.iflytek.cdc.province.enums.MedicalTypeEnum;
import com.iflytek.cdc.province.mapper.pg.AdsMsLifeInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsMsMedicalInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsPdPathogenCheckInfoMapper;
import com.iflytek.cdc.province.model.dto.PatientQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.vo.PatientListVO;
import com.iflytek.cdc.province.model.vo.PatientMedicalInfoVO;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.service.ExportTaskService;
import com.iflytek.cdc.province.service.PatientMasterDataService;
import com.iflytek.cdc.province.utils.ExcelUtils;
import com.iflytek.cdc.province.utils.FieldUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

@Service
@Slf4j
public class PatientMasterDataServiceImpl implements PatientMasterDataService {

    @Resource
    private AdsMsLifeInfoMapper adsMsLifeInfoMapper;

    @Resource
    private AdsMsMedicalInfoMapper adsMsMedicalInfoMapper;
    
    @Resource
    private AdsPdPathogenCheckInfoMapper adsPdPathogenCheckInfoMapper;

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Resource
    private ExportTaskService exportTaskService;

    @Resource
    private CommonUtilService commonUtilService;

    @Override
    public PageInfo<PatientListVO> getPatientList(PatientQueryDTO dto) {
        try (Page<?> ignored = PageHelper.startPage(dto.getPageIndex(), dto.getPageSize())) {
            return new PageInfo<>(adsMsLifeInfoMapper.getPatientList(dto));
        }
    }

    @Override
    public List<PatientMedicalInfoVO> getPatientMedicalList(String patientId) {

        List<PatientMedicalInfoVO> medicalInfoVOList = adsMsMedicalInfoMapper.getPatientMedicalList(patientId);
        medicalInfoVOList.forEach(e -> {
            //门、急诊都用门诊数据模型展示
            if(Objects.equals(e.getVisitTypeCode(), MedicalTypeEnum.OUTPATIENT.getCode()) ||
                    Objects.equals(e.getVisitTypeCode(), MedicalTypeEnum.EMERGENCY.getCode())) {
                e.setMedicalType(MedicalTypeEnum.OUTPATIENT.getType());
            }
            //住院病历使用住院数据模型展示
            if(Objects.equals(e.getVisitTypeCode(), MedicalTypeEnum.INPATIENT.getCode())) {
                e.setMedicalType(MedicalTypeEnum.INPATIENT.getType());
            }
        });
        //查询病原检测信息
        List<PatientMedicalInfoVO> pathogenInfoList = adsPdPathogenCheckInfoMapper.getPatientPathogenList(patientId);
        pathogenInfoList.forEach(e -> {
            e.setVisitTypeName(MedicalTypeEnum.PATHOGEN.getDesc());
            e.setMedicalType(MedicalTypeEnum.PATHOGEN.getType());
        });

        return commonUtilService.mergeLists(medicalInfoVOList, pathogenInfoList);
    }

    @Override
    public TbCdcmrExportTask patientListExport(PatientQueryDTO dto) {

        String taskParams = JSONObject.toJSONString(dto);
        ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                                                 dto.getTaskName(),
                                                 dto.getTaskUrl(),
                                                 taskParams,
                                                 dto.getModuleType(),
                                                 dto.getExportType());

        UapUserPo uapUserPo = userInfo.get();
        TbCdcmrExportTask existTask = adminServiceApi.checkExistTask(taskDTO, uapUserPo.getId());
        if (null != existTask){
            return existTask;
        }
        adminServiceApi.checkExportTaskCount(uapUserPo.getId());

        //导出列表的id为空则直接返回空
        if(CollectionUtil.isEmpty(dto.getRecordIds())){
            return null;
        }
        List<PatientListVO> patientList = adsMsLifeInfoMapper.getPatientListByRecordIds(dto.getRecordIds());
        int count = dto.getRecordIds().size();
        adminServiceApi.checkExportMax(count);
        taskDTO.setTotalCount(count);
        TbCdcmrExportTask exportTask = adminServiceApi.addExportTask(taskDTO, uapUserPo.getId());

        List<ExcelUtils.PropertyMeta> propertyMetaList = ExcelUtils.getPropertyMetaListBy(PatientListVO.class, null);
        byte[] bytes = FieldUtils.exportExcelByFormConfig(patientList, PatientListVO.class, true, propertyMetaList);
        exportTaskService.runTaskAndUploadFile(bytes,exportTask);
        return exportTask;
    }

}
