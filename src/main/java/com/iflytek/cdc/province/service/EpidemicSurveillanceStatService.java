package com.iflytek.cdc.province.service;

import com.iflytek.cdc.province.model.dto.EpSurStatQueryDTO;
import com.iflytek.cdc.province.model.vo.OrgIndexStatVO;
import com.iflytek.cdc.province.model.vo.ReportCardStatVO;

import java.util.List;

public interface EpidemicSurveillanceStatService {


    OrgIndexStatVO orgIndexStat(EpSurStatQueryDTO queryDTO);

    byte[] orgIndexStatExport(EpSurStatQueryDTO queryDTO);

    ReportCardStatVO reportCardStat(EpSurStatQueryDTO queryDTO);


    List<ReportCardStatVO> orgTypeReportStat(EpSurStatQueryDTO queryDTO);


    ReportCardStatVO reportWayStat(EpSurStatQueryDTO queryDTO);

    ReportCardStatVO loginStat(EpSurStatQueryDTO queryDTO);


    List<ReportCardStatVO> directReportStat(EpSurStatQueryDTO queryDTO);


    List<ReportCardStatVO> repeatReportStat(EpSurStatQueryDTO queryDTO);

    List<ReportCardStatVO> reportcardTimeTrend(EpSurStatQueryDTO queryDTO, String loginUserName);
}
