package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.AttachmentType;
import com.deepoove.poi.data.Attachments;
import com.deepoove.poi.policy.AttachmentRenderPolicy;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.common.Common;
import com.iflytek.cdc.edr.entity.TbCdcAttachment;
import com.iflytek.cdc.edr.enums.MasterDataEnum;
import com.iflytek.cdc.edr.utils.DataUtils;
import com.iflytek.cdc.edr.vo.MsOtherInfectCntExistExcelVO;
import com.iflytek.cdc.edr.vo.PopulationDataInfoVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsInfectCntExcelVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsInfectCntVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsInfectExsitCntExcelVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsOtherInfectCntExcelVO;
import com.iflytek.cdc.province.cache.DimRegionCache;
import com.iflytek.cdc.province.entity.bu.TbCdcbrManualBriefRecord;
import com.iflytek.cdc.province.mapper.bu.TbCdcbrManualBriefRecordMapper;
import com.iflytek.cdc.province.model.brief.*;
import com.iflytek.cdc.province.model.vo.TreeNode;
import com.iflytek.cdc.province.service.FileService;
import com.iflytek.cdc.province.service.ManualBriefRecordService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.exception.MedicalFatalException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生成简报记录-服务实现类
 */
@Service
@Slf4j
public class ManualBriefRecordServiceImpl implements ManualBriefRecordService {

    @Resource
    private FileService fileService;

    @Value("${storage.switch:swift}")
    private String storageSwitch;

    @Value("${swift.prefix:/file-obj}")
    private String swiftPrefix;

    @Value("${minio.prefix:/minIoFile}")
    private String minioPrefix;

    @Autowired
    private BatchUidService batchUidService;

    @Resource
    private TbCdcbrManualBriefRecordMapper manualBriefRecordMapper;

    @Resource
    private CdcAdminServiceApi cdcAdminServiceApi;

    @Resource
    private DimRegionCache dimRegionCache;

    @Override
    public ManualBriefRecordVO generateByStats(ManualBriefGenerateInfo info, List<MsInfectCntVO> statisticsData, Boolean isGuangxi, boolean notifiableDiseases) {
        return generate(info, statisticsData,isGuangxi, notifiableDiseases);
    }

    @Override
    public ManualBriefRecordVO generateByImport(ManualBriefGenerateInfo info, MultipartFile uploadData) {
        try {
            int residentPopulation = this.getTotalPopulation(info);
            List<BriefRecordImportVO> importData = EasyExcel.read(uploadData.getInputStream())
                    .head(BriefRecordImportVO.class)
                    .headRowNumber(2)
                    .sheet().doReadSync();
            List<MsInfectCntVO> statisticsData = this.calculateImportData(importData, residentPopulation);

            return generate(info, statisticsData, false, true);
        } catch (IOException e) {
            log.error("上传文件解析失败", e);
            throw new MedicalBusinessException("生成简报失败，请重试");
        }
    }

    private List<MsInfectCntVO> calculateImportData(List<BriefRecordImportVO> importData, int residentPopulation) {
        List<MsInfectCntVO> result = new ArrayList<>();

        MsInfectCntVO amountVo = new MsInfectCntVO();
        result.add(amountVo);
        for (BriefRecordImportVO item : importData) {
            MsInfectCntVO vo = new MsInfectCntVO();
            BeanUtils.copyProperties(item, vo);
            vo.setResidentPopulation(residentPopulation);
            // 计算合计和比率
            // 识别该条数据的levelType
            dealCurrentDataLevelType(item,vo);
            // 判断是否需要计算合计
            if (checkParentFlag(vo)){
                vo.amountTo(amountVo);
            }
            vo.calculateAllRates();
            result.add(vo);
        }
        amountVo.setInfectClass(Common.AMOUNT_TO);
        amountVo.setInfectType(Common.AMOUNT_TO);
        amountVo.setInfectName(Common.AMOUNT_TO);
        amountVo.setResidentPopulation(residentPopulation);
        amountVo.calculateAllRates();

        dealImportData(result);

        return result;
    }

    
    private ManualBriefRecordVO generate(ManualBriefGenerateInfo info, List<MsInfectCntVO> statisticsData,
                                         Boolean isGuangxi, boolean notifiableDiseases) {
        if (hasNoStatsData(statisticsData)) {
            throw new MedicalBusinessException("统计数据不能为空！");
        }
        this.ensureRegionAndInfectNames(info);

        final String diseaseTitle = buildDiseaseTitle(info, notifiableDiseases);
        final String regionName = StrUtil.firstNonBlank(info.getDistrictName(), info.getCityName(), info.getProvinceName());
        final String startDate = DateUtil.format(info.getStartDate(), "yyyy年MM月dd日");
        final String endDate = DateUtil.format(info.getEndDate(), "yyyy年MM月dd日");
        final Date generateTime = new Date();
        final String generateTimeStr = DateUtil.format(generateTime, "yyyy年MM月dd日HH时mm分");

        BriefRecordExportModel exportModel = this.buildExportModel(statisticsData,isGuangxi, notifiableDiseases, generateTimeStr);
        // 补充数据源信息
        exportModel.setSingleInfectType(StrUtil.isNotBlank(info.getInfectType()) && StrUtil.isBlank(info.getInfectCode()));
        exportModel.setSingleInfectCode(StrUtil.isNotBlank(info.getInfectCode()));
        exportModel.setDiseaseTitle(diseaseTitle);
        exportModel.setRegionName(regionName);
        exportModel.setStartDate(startDate);
        exportModel.setEndDate(endDate);

        byte[] fileBytes;
        try {
            fileBytes = this.generateWord(exportModel);
        } catch (IOException e) {
            log.error("生成简报文件失败", e);
            throw new MedicalFatalException("生成简报失败，请重试");
        }
        // 动态生成标题，格式：XX市 开始时间 至 结束时间 传染病名称 疫情概况简报
        final String fullTitle = StrUtil.builder()
                .append(regionName).append(startDate).append("至").append(endDate).append(diseaseTitle).append("疫情概况简报")
                .toString();

        String generateFilePath = upload(fileBytes, fullTitle + ".docx");

        TbCdcbrManualBriefRecord entity = new TbCdcbrManualBriefRecord();
        entity.setId(String.valueOf(batchUidService.getUid("tb_cdcbr_manual_brief_record")));
        entity.setCreatorId(info.getCreatorId());
        entity.setCreator(info.getCreator());
        entity.setUpdaterId(info.getCreatorId());
        entity.setUpdater(info.getCreator());
        entity.setTitle(fullTitle);
        entity.setStatisticsSource(info.getStatisticsSource());
        entity.setStatisticsStartTime(info.getStartDate());
        entity.setStatisticsEndTime(info.getEndDate());
        entity.setStatisticsProvinceCode(info.getProvinceCode());
        entity.setStatisticsProvinceName(info.getProvinceName());
        entity.setStatisticsCityCode(info.getCityCode());
        entity.setStatisticsCityName(info.getCityName());
        entity.setStatisticsDistrictCode(info.getDistrictCode());
        entity.setStatisticsDistrictName(info.getDistrictName());
        entity.setGenerateTime(generateTime);
        entity.setGenerateFilePath(generateFilePath);

        manualBriefRecordMapper.insert(entity);

        return ManualBriefRecordVO.of(entity, fileBytes);
    }

    private boolean hasNoStatsData(List<MsInfectCntVO> statisticsData) {
        return CollUtil.isEmpty(statisticsData)
                || (statisticsData.size() == 1 && Common.AMOUNT_TO.equals(statisticsData.get(0).getInfectType()));
    }

    private String buildDiseaseTitle(ManualBriefGenerateInfo info, boolean notifiableDiseases) {
        String defaultName = notifiableDiseases ? "法定传染病" : "其他传染病";
        String diseaseName = StrUtil.firstNonBlank(info.getInfectName(), info.getInfectType(), info.getInfectClass(), defaultName);
        // 保证名字带传染病
        if (!diseaseName.contains("传染病")) {
            diseaseName = diseaseName + "传染病";
        }
        return diseaseName;
    }

    /**
     * 保证区划名称
     */
    private void ensureRegionAndInfectNames(ManualBriefGenerateInfo info) {
        if (StrUtil.isAllBlank(info.getProvinceName(), info.getCityName(), info.getDistrictName())) {
            dimRegionCache.fillRegionNames(info::getProvinceCode, info::setProvinceName,
                    info::getCityCode, info::setCityName,
                    info::getDistrictCode, info::setDistrictName, null, null);
        }
        if (StrUtil.isNotBlank(info.getInfectCode()) && StrUtil.isBlank(info.getInfectName())) {
            List<TreeNode> root = cdcAdminServiceApi.getMasterDataInfo(MasterDataEnum.INFECTED.getCode());
            if (CollUtil.isNotEmpty(root)) {
                for (TreeNode node : root) {
                    if (info.getInfectCode().equals(node.getId())) {
                        info.setInfectName(node.getLabel());
                        break;
                    }
                }
            }
        }
    }

    private BriefRecordExportModel buildExportModel(List<MsInfectCntVO> statisticsData, Boolean isGuangxi, boolean notifiableDiseases,
                                                    String generateTimeStr) {
        BriefRecordExportModel model = new BriefRecordExportModel();
        model.setNotifiable(notifiableDiseases);
        model.setGenerateTime(generateTimeStr);
        // 填充正文字段
        this.fillMainBody(model, statisticsData, notifiableDiseases);

        // 处理附件字段
        ByteArrayOutputStream excel = new ByteArrayOutputStream();
        if (notifiableDiseases) {
            if (isGuangxi){
                EasyExcel.write(excel, MsInfectExsitCntExcelVO.class).sheet(generateTimeStr).doWrite(
                        statisticsData.stream().map(MsInfectExsitCntExcelVO::of).collect(Collectors.toList()));
            }else {
                EasyExcel.write(excel, MsInfectCntExcelVO.class).sheet(generateTimeStr).doWrite(
                        statisticsData.stream().map(MsInfectCntExcelVO::of).collect(Collectors.toList()));
            }
        } else {
            if (isGuangxi){
                EasyExcel.write(excel, MsOtherInfectCntExistExcelVO.class).sheet(generateTimeStr).doWrite(
                        statisticsData.stream().map(MsOtherInfectCntExistExcelVO::of).collect(Collectors.toList()));
            }else {
                EasyExcel.write(excel, MsOtherInfectCntExcelVO.class).sheet(generateTimeStr).doWrite(
                        statisticsData.stream().map(MsOtherInfectCntExcelVO::of).collect(Collectors.toList()));
            }
        }
        model.setAttachment(Attachments.ofBytes(excel.toByteArray(), AttachmentType.XLSX).create());

        return model;
    }

    private void fillMainBody(BriefRecordExportModel model, List<MsInfectCntVO> statisticsData, boolean notifiableDiseases) {
        MsInfectCntVO amountVo = statisticsData.get(0);
        model.setResidentPopulation(amountVo.getResidentPopulation());

        model.setDiseaseCount(statisticsData.size()-1);
        model.setTotalNewCnt(amountVo.getProcessNewCnt());
        model.setTotalDeadCnt(amountVo.getProcessDeadCnt());
        model.setTotalNewRate(amountVo.getNewRate());
        model.setTotalDeadRate(amountVo.getDeadRate());
        // 非法定传染病只需要总览，法定传染病需要按类型细分
        if (notifiableDiseases) {
            // 甲类传染病
            Set<String> aTypeDiseaseNames = new LinkedHashSet<>();
            // 乙类传染病
            Map<String, Integer> bTypeDiseases = new LinkedHashMap<>();
            Set<String> bTypeZeroDiseaseNames = new LinkedHashSet<>();
            Set<String> bTypeNonzeroDiseaseNames = new LinkedHashSet<>();
            // 丙类传染病
            Map<String, Integer> cTypeDiseases = new LinkedHashMap<>();
            Set<String> cTypeZeroDiseaseNames = new LinkedHashSet<>();
            Set<String> cTypeNonzeroDiseaseNames = new LinkedHashSet<>();
            // 其它法定传染病
            Map<String, Integer> otherTypeDiseases = new LinkedHashMap<>();
            for (MsInfectCntVO vo : statisticsData) {
                // 跳过合计列
                if (Common.AMOUNT_TO.equals(vo.getInfectType()) || Common.AMOUNT_TO.equals(vo.getInfectName())) {
                    continue;
                }
                String infectName;
                String levelType = vo.getLevelType();
                if ("传染病病种".equals(levelType)){
                    infectName = vo.getInfectTypeName();
                }else if("传染病亚型".equals(levelType)){
                    infectName = vo.getInfectSubtypeName();
                }else{
                    infectName = vo.getInfectName();
                }
                boolean parentFlag = checkParentFlag(vo);
                // 按名称分类统计
                if (vo.getInfectType().contains("甲类")) {
                    aTypeDiseaseNames.add(infectName);
                    if (parentFlag){
                        model.setATypeTotalNewCnt(DataUtils.sum(model.getATypeTotalNewCnt(), vo.getProcessNewCnt()));
                        model.setATypeTotalDeadCnt(DataUtils.sum(model.getATypeTotalDeadCnt(), vo.getProcessDeadCnt()));
                    }
                    if ("鼠疫".equals(infectName)) {
                        if (parentFlag){
                            model.setPlagueNewCnt(DataUtils.sum(model.getPlagueNewCnt(), vo.getProcessNewCnt()));
                            model.setPlagueDeadCnt(DataUtils.sum(model.getPlagueDeadCnt(), vo.getProcessDeadCnt()));
                        }
                        
                    }
                    if ("霍乱".equals(infectName)) {
                        if (parentFlag){
                            model.setCholeraNewCnt(DataUtils.sum(model.getCholeraNewCnt(), vo.getProcessNewCnt()));
                            model.setCholeraDeadCnt(DataUtils.sum(model.getCholeraDeadCnt(), vo.getProcessDeadCnt()));
                        }
                        
                    }
                } else if (vo.getInfectType().contains("乙类")) {
                    if (parentFlag){
                        model.setBTypeTotalNewCnt(DataUtils.sum(model.getBTypeTotalNewCnt(), vo.getProcessNewCnt()));
                        model.setBTypeTotalDeadCnt(DataUtils.sum(model.getBTypeTotalDeadCnt(), vo.getProcessDeadCnt()));
                        if (CollectionUtil.isEmpty(vo.getChildren())){
                            bTypeDiseases.put(infectName, ObjectUtil.defaultIfNull(vo.getProcessNewCnt(), 0));
                        } else {
                            bTypeDiseases.put(infectName, 0);
                        }
                    } else {
                        bTypeDiseases.put(infectName, ObjectUtil.defaultIfNull(vo.getProcessNewCnt(), 0));
                    }
                    if (DataUtils.sum(vo.getProcessNewCnt(), vo.getProcessDeadCnt()) == 0) {
                        bTypeZeroDiseaseNames.add(infectName);
                    } else {
                        bTypeNonzeroDiseaseNames.add(infectName);
                        if (parentFlag){
                            model.setBTypeNonzeroNewCnt(DataUtils.sum(model.getBTypeNonzeroNewCnt(), vo.getProcessNewCnt()));
                            model.setBTypeNonzeroDeadCnt(DataUtils.sum(model.getBTypeNonzeroDeadCnt(), vo.getProcessDeadCnt()));
                        }

                    }
                } else if (vo.getInfectType().contains("丙类")) {

                    if (parentFlag){
                        model.setCTypeTotalNewCnt(DataUtils.sum(model.getCTypeTotalNewCnt(), vo.getProcessNewCnt()));
                        model.setCTypeTotalDeadCnt(DataUtils.sum(model.getCTypeTotalDeadCnt(), vo.getProcessDeadCnt()));
                        if (CollectionUtil.isEmpty(vo.getChildren())){
                            cTypeDiseases.put(infectName, ObjectUtil.defaultIfNull(vo.getProcessNewCnt(), 0));
                        } else {
                            cTypeDiseases.put(infectName, 0);
                        }

                    } else {
                        cTypeDiseases.put(infectName, ObjectUtil.defaultIfNull(vo.getProcessNewCnt(), 0));
                    }
                    if (DataUtils.sum(vo.getProcessNewCnt(), vo.getProcessDeadCnt()) == 0) {
                        cTypeZeroDiseaseNames.add(infectName);
                    } else {
                        cTypeNonzeroDiseaseNames.add(infectName);
                        if (parentFlag){
                            model.setCTypeNonzeroNewCnt(DataUtils.sum(model.getCTypeNonzeroNewCnt(), vo.getProcessNewCnt()));
                            model.setCTypeNonzeroDeadCnt(DataUtils.sum(model.getCTypeNonzeroDeadCnt(), vo.getProcessDeadCnt()));
                        }

                    }
                } else {

                    if (parentFlag){
                        model.setOtherTypeTotalNewCnt(DataUtils.sum(model.getOtherTypeTotalNewCnt(), vo.getProcessNewCnt()));
                        model.setOtherTypeTotalDeadCnt(DataUtils.sum(model.getOtherTypeTotalDeadCnt(), vo.getProcessDeadCnt()));
                        if (CollectionUtil.isEmpty(vo.getChildren())){
                            otherTypeDiseases.put(infectName, ObjectUtil.defaultIfNull(vo.getProcessNewCnt(), 0));
                        } else {
                            otherTypeDiseases.put(infectName, 0);
                        }

                    } else {
                        otherTypeDiseases.put(infectName, ObjectUtil.defaultIfNull(vo.getProcessNewCnt(), 0));
                    }

                }
            }
            // 甲类传染病
            model.setATypeDiseaseCount(aTypeDiseaseNames.size());
            model.setATypeTotalNewRate(DataUtils.hundredThousandth(model.getATypeTotalNewCnt(), model.getResidentPopulation()));
            model.setATypeTotalDeadRate(DataUtils.hundredThousandth(model.getATypeTotalDeadCnt(), model.getResidentPopulation()));
            model.setPlagueNewRate(DataUtils.hundredThousandth(model.getPlagueNewCnt(), model.getResidentPopulation()));
            model.setPlagueDeadRate(DataUtils.hundredThousandth(model.getPlagueDeadCnt(), model.getResidentPopulation()));
            model.setCholeraNewRate(DataUtils.hundredThousandth(model.getCholeraNewCnt(), model.getResidentPopulation()));
            model.setCholeraDeadRate(DataUtils.hundredThousandth(model.getCholeraDeadCnt(), model.getResidentPopulation()));
            // 乙类传染病
            model.setBTypeZeroDiseaseNames(String.join("、", bTypeZeroDiseaseNames));
            model.setBTypeNonzeroDiseaseCount(bTypeNonzeroDiseaseNames.size());
            model.setBTypeTotalNewRate(DataUtils.hundredThousandth(model.getBTypeTotalNewCnt(), model.getResidentPopulation()));
            model.setBTypeTotalDeadRate(DataUtils.hundredThousandth(model.getBTypeTotalDeadCnt(), model.getResidentPopulation()));
            Map<String, Integer> bTypeTop5Diseases = this.topN(bTypeDiseases, 5);
            model.setBTypeTop5DiseaseNames(String.join("、", bTypeTop5Diseases.keySet()));
            Integer bTypeTop5DiseaseCount = bTypeTop5Diseases.values().stream().mapToInt(Integer::intValue).sum();
            model.setBTypeTop5NewRate(DataUtils.ratio(bTypeTop5DiseaseCount, model.getBTypeTotalNewCnt()));
            // 丙类传染病
            model.setCTypeZeroDiseaseNames(String.join("、", cTypeZeroDiseaseNames));
            model.setCTypeNonzeroDiseaseCount(cTypeNonzeroDiseaseNames.size());
            model.setCTypeTotalNewRate(DataUtils.hundredThousandth(model.getCTypeTotalNewCnt(), model.getResidentPopulation()));
            model.setCTypeTotalDeadRate(DataUtils.hundredThousandth(model.getCTypeTotalDeadCnt(), model.getResidentPopulation()));
            Map<String, Integer> cTypeTop3Diseases = this.topN(cTypeDiseases, 3);
            model.setCTypeTop3DiseaseNames(String.join("、", cTypeTop3Diseases.keySet()));
            Integer cTypeTop3DiseaseCount = cTypeTop3Diseases.values().stream().mapToInt(Integer::intValue).sum();
            model.setCTypeTop3NewRate(DataUtils.ratio(cTypeTop3DiseaseCount, model.getCTypeTotalNewCnt()));
            // 其它法定传染病
            model.setOtherTypeDiseaseCount(otherTypeDiseases.size());
            model.setOtherTypeTotalNewRate(DataUtils.hundredThousandth(model.getOtherTypeTotalNewCnt(), model.getResidentPopulation()));
            model.setOtherTypeTotalDeadRate(DataUtils.hundredThousandth(model.getOtherTypeTotalDeadCnt(), model.getResidentPopulation()));
            Map<String, Integer> otherTypeTop3Diseases = this.topN(cTypeDiseases, 3);
            model.setOtherTypeTop3DiseaseNames(String.join("、", otherTypeTop3Diseases.keySet()));
            Integer otherTypeTop3DiseaseCount = otherTypeTop3Diseases.values().stream().mapToInt(Integer::intValue).sum();
            model.setOtherTypeTop3NewRate(DataUtils.ratio(otherTypeTop3DiseaseCount, model.getOtherTypeTotalNewCnt()));
        }
    }

    private Map<String, Integer> topN(Map<String, Integer> original, int topN) {
        Map<String, Integer> sorted = MapUtil.sortByValue(original, true);
        if (sorted.size() <= topN) {
            return sorted;
        }
        Map<String, Integer> subMap = new LinkedHashMap<>();
        int index = 0;
        for (Map.Entry<String, Integer> entry : sorted.entrySet()) {
            if (index == topN) {
                break;
            }
            subMap.put(entry.getKey(), entry.getValue());
            index++;
        }
        return subMap;
    }

    private byte[] generateWord(BriefRecordExportModel exportModel) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        InputStream template = Objects.requireNonNull(Thread.currentThread()
                .getContextClassLoader()
                .getResourceAsStream("template/briefInfectTemplate.docx"));
        Configure build = Configure.builder()
                .bind("attachment", new AttachmentRenderPolicy())
                .useSpringEL()
                .build();
        XWPFTemplate.compile(template, build)
                .render(exportModel)
                .writeAndClose(outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 上传文件并记录上传成功
     *
     * @param bytes 文件数据
     */
    private String upload(byte[] bytes, String fileName) {
        TbCdcAttachment tbCdcAttachment = fileService.uploadFile(bytes, fileName);

        String attachmentPath = tbCdcAttachment.getAttachmentPath();
        if ("swift".equals(storageSwitch)) {
            attachmentPath = swiftPrefix.concat(attachmentPath);
        } else {
            attachmentPath = minioPrefix.concat(attachmentPath);
        }
        return attachmentPath;
    }

    @Override
    public PageInfo<ManualBriefRecordVO> queryList(ManualBriefQueryDTO queryParam) {
        try (Page<?> ignored = PageHelper.startPage(queryParam.getPageIndex(), queryParam.getPageSize())) {
            return PageInfo.of(manualBriefRecordMapper.queryList(queryParam));
        }
    }

    @Override
    public int getTotalPopulation(ManualBriefRangeDTO range) {
        List<String> provinceCodes = StrUtil.isBlank(range.getProvinceCode()) ? ListUtil.empty() : ListUtil.of(range.getProvinceCode());
        List<String> cityCodes = StrUtil.isBlank(range.getCityCode()) ? ListUtil.empty() : ListUtil.of(range.getCityCode());
        List<String> districtCodes = StrUtil.isBlank(range.getDistrictCode()) ? ListUtil.empty() : ListUtil.of(range.getDistrictCode());
        PopulationDataInfoVO stats = cdcAdminServiceApi.statByAreaCodes(range.getEndDate(), provinceCodes, cityCodes, districtCodes);
        return stats != null ? stats.getResidentPopulation() : 0;
    }


    private void dealImportData(List<MsInfectCntVO> msInfectCntVOS){
        Map<String, MsInfectCntVO> parentMap = new HashMap<>();
        // 第一遍遍历，找出所有父对象
        for (MsInfectCntVO vo : msInfectCntVOS) {
            if (StrUtil.isNotBlank(vo.getInfectName()) && StrUtil.isBlank(vo.getInfectSubtypeName())) {
                // 初始化 children 列表
                vo.setChildren(new ArrayList<>());
                parentMap.put(vo.getInfectName(), vo);
            }
            if (checkParentFlag(vo)){
                String levelType = vo.getLevelType();
                if (StrUtil.equals(levelType,"传染病病种")){
                    parentMap.put(vo.getInfectTypeName(), vo);
                }
                if (StrUtil.equals(levelType,"传染病")){
                    parentMap.put(vo.getInfectName(), vo);
                }
                vo.setChildren(new ArrayList<>());
            }
        }

        for (MsInfectCntVO vo : msInfectCntVOS) {
            if (!checkParentFlag(vo)){
                String levelType = vo.getLevelType();
                MsInfectCntVO parent = null;
                if (StrUtil.equals(levelType,"传染病亚型")){
                    parent = parentMap.get(vo.getInfectName());
                }

                if (StrUtil.equals(levelType,"传染病")){
                    parent = parentMap.get(vo.getInfectTypeName());
                }

                if (parent != null){
                    parent.getChildren().add(vo);
                }
            }
        }

    }



    private void dealCurrentDataLevelType(BriefRecordImportVO item, MsInfectCntVO vo) {
        // 传染病病种
        String infectTypeName = item.getInfectTypeName();
        // 传染病
        String infectName = item.getInfectName();
        // 传染病亚型
        String infectSubtypeName = item.getInfectSubtypeName();

        if (StrUtil.isBlank(infectTypeName) && StrUtil.isNotBlank(infectName) && StrUtil.isBlank(infectSubtypeName)){
            vo.setLevelType("传染病");
            return;
        }
        if (StrUtil.isBlank(infectTypeName) && StrUtil.isNotBlank(infectName) && StrUtil.isNotBlank(infectSubtypeName)){
            vo.setLevelType("传染病亚型");
            return;
        }
        if (StrUtil.isNotBlank(infectTypeName) && StrUtil.isBlank(infectName) && StrUtil.isBlank(infectSubtypeName)){
            vo.setLevelType("传染病病种");
            return;
        }
        if (StrUtil.isNotBlank(infectTypeName) && StrUtil.isNotBlank(infectName) && StrUtil.isBlank(infectSubtypeName)){
            vo.setLevelType("传染病");
            return;
        }
        if (StrUtil.isNotBlank(infectTypeName) && StrUtil.isNotBlank(infectName) && StrUtil.isNotBlank(infectSubtypeName)){
            vo.setLevelType("传染病亚型");
        }
    }


    private boolean checkParentFlag(MsInfectCntVO vo) {
        String levelType = vo.getLevelType();
        if (StrUtil.equals(levelType, "传染病病种")){
            return true;
        }
        if (StrUtil.equals(levelType,"传染病")){
            String infectTypeName = vo.getInfectTypeName();
            String infectSubtypeName = vo.getInfectSubtypeName();
            return StrUtil.isBlank(infectTypeName) && StrUtil.isBlank(infectSubtypeName);
        }
        return false;
    }
}
