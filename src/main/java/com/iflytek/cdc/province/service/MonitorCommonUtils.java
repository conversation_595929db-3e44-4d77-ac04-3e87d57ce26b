package com.iflytek.cdc.province.service;

import com.iflytek.cdc.edr.common.BooleanEnum;
import com.iflytek.cdc.edr.common.Common;
import com.iflytek.cdc.edr.enums.DataSourceKey;
import com.iflytek.cdc.province.cache.TableInfoCache;
import com.iflytek.cdc.province.dm.DataModel;
import com.iflytek.cdc.province.dm.FormGroup;
import com.iflytek.cdc.province.dm.ModelForm;
import com.iflytek.cdc.province.dm.engine.Column;
import com.iflytek.cdc.province.dm.engine.Table;
import com.iflytek.cdc.province.enums.TableSchemaEnum;
import com.iflytek.cdc.province.mapper.bu.DataModelBUBaseMapper;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmDataFormTemplateMapper;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmMetadataTableColumnInfoMapper;
import com.iflytek.cdc.province.mapper.mpp.DataModelMPPBaseMapper;
import com.iflytek.cdc.province.mapper.pg.DataModelPGBaseMapper;
import com.iflytek.cdc.province.model.dto.dm.*;
import com.iflytek.cdc.province.service.cdcadmin.CdcAdminService;
import com.iflytek.cdc.edr.utils.ExcelUtils;
import com.iflytek.cdc.edr.vo.FieldVO;
import com.iflytek.cdc.edr.vo.dm.*;
import com.iflytek.cdc.edr.vo.pandemic.MedicalInfoVO;
import com.iflytek.cdc.province.entity.bu.*;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MonitorCommonUtils {

    private static final String TB_CDCDM_CHECK_RECORD = "tb_cdcdm_check_record";

    private static final String TB_CDCDM_CORRECTION_RECORD = "tb_cdcdm_correction_record";

    private static final String TB_CDCEW_DATA_FAVORITES = "tb_cdcew_data_favorites";

    private static final String TB_CDCEW_DATA_FAVORITES_DETAIL = "tb_cdcew_data_favorites_detail";

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private CdcAdminService cdcAdminService;

    @Resource
    private TbCdcdmDataFormTemplateMapper tbCdcdmDataFormTemplateMapper;

    @Resource
    private TbCdcdmMetadataTableColumnInfoMapper tbCdcdmMetadataTableColumnInfoMapper;

    @Resource
    private DataModelPGBaseMapper pgBaseMapper;

    @Resource
    private DataModelBUBaseMapper buBaseMapper;

    @Resource
    private DataModelMPPBaseMapper mppBaseMapper;

    @Resource
    private TableInfoCache tableInfoCache;

    public TbCdcewDataFavorites createFavoritesEntity(FavoritesEditDTO dto, String loginUserId, String loginUserName) {

        String id = String.valueOf(batchUidService.getUid(TB_CDCEW_DATA_FAVORITES));
        return TbCdcewDataFavorites.builder()
                .id(StringUtils.isBlank(dto.getId()) ? id : dto.getId())
                .favoritesName(dto.getFavoritesName())
                .favoritesType(dto.getFavoritesType())
                .appCode(dto.getModuleType())
                .notes(dto.getNotes())
                .createTime(new Date())
                .creator(loginUserName)
                .updateTime(new Date())
                .updater(loginUserName)
                .deleteFlag(Common.STR_ZERO)
                .creatorId(loginUserId)
                .updaterId(loginUserId)
                .build();
    }

    /**
     * 根据收藏数据传参生成收藏详情的list
     */
    public List<TbCdcewDataFavoritesDetail> generateFavoritesDetailList(DataCollectionStatusDTO dto) {

        List<TbCdcewDataFavoritesDetail> favoritesDetails = new ArrayList<>();
        List<String> recordIds = dto.getDataRecordIdList();
        recordIds.forEach(e -> {
            String id = String.valueOf(batchUidService.getUid(TB_CDCEW_DATA_FAVORITES_DETAIL));
            TbCdcewDataFavoritesDetail detail = new TbCdcewDataFavoritesDetail();
            BeanUtils.copyProperties(dto, detail);
            detail.setId(id);
            detail.setDataFavoritesId(dto.getFavoritesId());
            detail.setDataRecordId(e);
            detail.setStatus(Common.INT_ONE);
            detail.setCollectTime(new Date());
            favoritesDetails.add(detail);
        });
        return favoritesDetails;
    }

    public DataModel buildDmEngineModel(DataModelVO dataModelVO) {

        DataModel dataModel = DataModelMapper.INSTANCE.buildDataModel(dataModelVO);

        // 查询模型配置
        TbCdcdmDataFormTemplate template = tbCdcdmDataFormTemplateMapper
                .getModelConfigByModelId(dataModel.getModelId());
        dataModel.setTemplate(template);

        for (DataModelFormVO formVO : dataModelVO.getFormList()) {
            ModelForm modelForm = ModelForm.builder()
                    .modelId(formVO.getModelId())
                    .modelVersionId(formVO.getModelVersionId())
                    .modelFormId(formVO.getModelFormId())
                    .formCode(formVO.getModelFormId())
                    .formName(formVO.getFormName())
                    .isRepeat(BooleanEnum.isTrue(formVO.getIsRepeat()))
                    .maxRepeatCnt(formVO.getMaxRepeatCnt())
                    .quoteModel(formVO.getQuoteModel())
                    .build();

            dataModel.addForm(modelForm);

            for (FormGroupTmpl groupVO : formVO.getFormGroupList()) {
                FormGroup group = FormGroup.builder()
                        .id(groupVO.getField())
                        .code(groupVO.getField())
                        .name(groupVO.getTitle())
                        .type(groupVO.getType())
                        .isRepeat(BooleanEnum.isTrue(groupVO.getRepeat()))
                        .maxRepeatTimes(groupVO.getProps().getMax())
                        .build();
                modelForm.addGroup(group);

                FormGroupTmpl.GroupProps props = groupVO.getProps();
                for (FormGroupTmpl.FieldRule fieldVO : props.getRules()) {
                    TbCdcdmMetadataTableInfo tableInfo = fieldVO.getTableInfo();
                    TbCdcdmMetadataTableColumnInfo tableColumnInfo = fieldVO.getTableColumnInfo();
                    if (tableInfo != null && tableColumnInfo != null) {
                        Table table = Table.builder()
                                .dataSourceKey(tableInfo.getDataSourceKey())
                                .tableId(tableInfo.getId())
                                .tableCode(tableInfo.getTableCode())
                                .schema(tableInfo.getSchema())
                                .tableName(tableInfo.getTableName())
                                .tableType(tableInfo.getTableType())
                                .alias(tableInfo.getAlias())
                                .filterCondition(tableInfo.getFilterCondition())
                                .build();

                        Column column = Column.builder()
                                .table(table)
                                .id(fieldVO.getField())
                                .columnId(tableColumnInfo.getId())
                                .columnCode(tableColumnInfo.getColumnCode())
                                .columnType(tableColumnInfo.getDataType())
                                .columnName(tableColumnInfo.getColumnName())
                                .fieldName(tableColumnInfo.getColumnName())
                                .desensitizedType(fieldVO.getDesensitizedType())
                                .props(fieldVO.getProps())
                                .build();

                        group.addColumn(column);

                        if (Objects.equals(column.getColumnId(), formVO.getFormIdentityColumn())) {
                            modelForm.setFormIdentityColumn(column);
                        }
                    }
                    //添加容错处理 - 应用未配置字段时处理应用数仓不一致的问题
                    else {
                        Column column = Column.builder()
                                              .id(fieldVO.getField())
                                              .desensitizedType(fieldVO.getDesensitizedType())
                                              .props(fieldVO.getProps())
                                              .build();
                        group.addColumn(column);
                    }
                }
            }
        }
        return dataModel;
    }

    /**
     * 转换为excel文件流
     * 
     * @param res
     * @return
     */
    public byte[] buildByte(List<MedicalInfoVO> res) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("Sheet1");
        Row firstRow = sheet.createRow(0);

        for (int i = 0; i < res.size(); i++) {
            // isDisplay = 1 显示的字段
            List<FieldVO> fieldVOList = res.get(i).getFieldVOList()
                    .stream().filter(v -> "1".equals(v.getIsDisplay())).collect(Collectors.toList());
            fieldVOList.sort(Comparator.comparing(FieldVO::getOrderFlag));
            Row row = sheet.createRow(i + 1);
            for (int j = 0; j < fieldVOList.size(); j++) {
                FieldVO fieldVO = fieldVOList.get(j);
                if (i == 0) {
                    // 设置表头
                    ExcelUtils.createCellAndSetValue(firstRow, String.valueOf(fieldVO.getLabel()), j);
                }
                ExcelUtils.createCellAndSetValue(row, String.valueOf(fieldVO.getValue()), j);
            }
        }
        ByteArrayOutputStream outputStream = ExcelUtils.generateOutputStream(workbook);
        return outputStream.toByteArray();
    }

    /*
     * 根据查询表的schema 选择不同的数据源
     */
    public List<Map<String, Object>> queryByTableSchema(String dataSourceKey, String schema, String sql) {
        if (StringUtils.isNotBlank(dataSourceKey)) {
            if (dataSourceKey.equals(DataSourceKey.MPP.getKey())) {
                return mppBaseMapper.selectDataBySql(sql);
            }
            if (dataSourceKey.equals(DataSourceKey.BU.getKey())) {
                return buBaseMapper.selectDataBySql(sql);
            }
            if (dataSourceKey.equals(DataSourceKey.PG_SQL.getKey())) {
                return pgBaseMapper.selectDataBySql(sql);
            }
            return new ArrayList<>();
        }
        if (TableSchemaEnum.dataBusinessSchemaList().contains(schema)) {
            return buBaseMapper.selectDataBySql(sql);
        }
        if (TableSchemaEnum.dataWarehouseSchemaList().contains(schema)) {
            return pgBaseMapper.selectDataBySql(sql);
        }
        return new ArrayList<>();
    }

}
