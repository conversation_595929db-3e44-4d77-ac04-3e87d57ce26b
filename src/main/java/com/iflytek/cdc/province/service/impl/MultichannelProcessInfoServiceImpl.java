package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.util.StringUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.utils.DateFormatUtil;
import com.iflytek.cdc.edr.vo.adsMsProcess.*;
import com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo;
import com.iflytek.cdc.province.enums.ProcessInfoTypeEnum;
import com.iflytek.cdc.province.enums.WarningTypeEnum;
import com.iflytek.cdc.province.mapper.pg.AdsMulProcessCaseInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsMulProcessInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsPdPathogenCheckInfoMapper;
import com.iflytek.cdc.province.model.dto.MsProcessLogQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoConditionQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.dto.PathogenCombinationQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.vo.*;
import com.iflytek.cdc.province.model.pathogen.PathogenInfectionSituationVO;
import com.iflytek.cdc.province.service.ExportTaskService;
import com.iflytek.cdc.province.service.MultichannelProcessInfoService;
import com.iflytek.cdc.province.service.MultichannelUtilsService;
import com.iflytek.cdc.province.service.ProcessInfoServiceBase;
import com.iflytek.cdc.province.utils.DateUtils;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class MultichannelProcessInfoServiceImpl implements MultichannelProcessInfoService, ProcessInfoServiceBase {

    @Resource
    private AdsMulProcessInfoMapper adsMulProcessInfoMapper;

    @Resource
    private AdsPdPathogenCheckInfoMapper adsPdPathogenCheckInfoMapper;

    @Resource
    private ExportTaskService exportTaskService;

    @Resource
    private MultichannelUtilsService multichannelUtilsService;

    @Resource
    private AdsMulProcessCaseInfoMapper adsMulProcessCaseInfoMapper;

    @Override
    public List<PathogenCheckVO> listMultichannelPathogenCheckResult(PathogenCombinationQueryDTO dto) {

        if(CollectionUtil.isEmpty(dto.getSourceKeyList())){
            return new ArrayList<>();
        }
        return adsMulProcessCaseInfoMapper.listPathogenCheckResult(dto);
    }

    @Override
    public List<PathogenCheckVO> listPositivePathogenCheckResult(PathogenCombinationQueryDTO dto) {

        if(CollectionUtil.isEmpty(dto.getSourceKeyList())){
            return new ArrayList<>();
        }
        dto.setWarningType(WarningTypeEnum.MULTICHANNEL.getCode());
        return adsPdPathogenCheckInfoMapper.listPositivePathogenCheckResult(dto);
    }

    @Override
    public List<PathogenInfectionSituationVO> getPathogenInfectionSituation(PathogenCombinationQueryDTO dto) {

        if(StringUtils.isNotBlank(dto.getPathogenId())){
            List<String> pathogenNameList = multichannelUtilsService.getAllPathogenNameBy(Collections.singletonList(dto.getPathogenId()));
            dto.setPathogenNameList(CollectionUtil.isEmpty(pathogenNameList) ? null : pathogenNameList);
        }
        return adsPdPathogenCheckInfoMapper.getPathogenInfectionSituation(dto);
    }

    @Override
    public String getProcessInfoType() {
        return ProcessInfoTypeEnum.MULTICHANNEL.getCode();
    }

    @Override
    public List<MedCntIndicatorVO> listMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {

        return adsMulProcessInfoMapper.listMultichannelMedCntIndicator(queryDTO);
    }

    @Override
    public List<MedCntIndicatorVO> listOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        queryDTO.setEndDate(queryDTO.getEndDate() == null ? null : DateFormatUtil.getTheLastSecondOfOneDay(DateUtils.formatDate(queryDTO.getEndDate())));
        return adsMulProcessInfoMapper.listMultichannelOutcomeCntIndicator(queryDTO);
    }

    @Override
    public List<AreaMedCntIndicatorVO> listAreaMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        queryDTO.setEndDate(queryDTO.getEndDate() == null ? null : DateFormatUtil.getTheLastSecondOfOneDay(DateUtils.formatDate(queryDTO.getEndDate())));
        return adsMulProcessInfoMapper.listMultichannelAreaMedCntIndicator(queryDTO);
    }

    @Override
    public List<AreaMedCntIndicatorVO> listAreaOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        queryDTO.setEndDate(queryDTO.getEndDate() == null ? null : DateFormatUtil.getTheLastSecondOfOneDay(DateUtils.formatDate(queryDTO.getEndDate())));
        return adsMulProcessInfoMapper.listMultichannelAreaOutcomeCntIndicator(queryDTO);
    }

    @Override
    public List<MsProcessInfoAddressVO> listAddressByIds(MsProcessSimpleInfoQueryDTO dto) {

        return adsMulProcessInfoMapper.listMultichannelMsProcessInfoAddress(dto);
    }

    @Override
    public PageInfo<MsProcessSimpleInfoVO> simpleInfoPageList(MsProcessSimpleInfoQueryDTO queryDTO) {
        queryDTO.setEndDate(queryDTO.getEndDate() == null ? null : DateFormatUtil.getTheLastSecondOfOneDay(DateUtils.formatDate(queryDTO.getEndDate())));
        try (Page<?> ignored = PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize())) {
            List<MsProcessSimpleInfoVO> processInfoList = adsMulProcessInfoMapper.listMultichannelProcessInfo(queryDTO);
            return new PageInfo<>(processInfoList);
        }
    }

    @Override
    public MsProcessSimpleInfoDetailVO loadProcessSimpleById(String id) {
        return adsMulProcessInfoMapper.loadMultichannelSimpleInfo(id);
    }

    @Override
    public TbCdcmrExportTask exportSimpleInfo(MsProcessSimpleInfoQueryDTO dto) {
        dto.setEndDate(dto.getEndDate() == null ? null : DateFormatUtil.getTheLastSecondOfOneDay(DateUtils.formatDate(dto.getEndDate())));
        ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                dto.getTaskName(),
                dto.getTaskUrl(),
                null,
                dto.getModuleType(),
                dto.getExportType(),
                dto.getExportName());

        return exportTaskService.addAndUploadFile(dto,
                () -> adsMulProcessInfoMapper.listMultichannelProcessInfo(dto),
                () -> Optional.ofNullable(adsMulProcessInfoMapper.countMultichannelProcess(dto)).orElse(0),
                taskDTO,
                MultichannelProcessSimpleInfoExcelVO.class,
                MultichannelProcessSimpleInfoExcelVO::of);
    }

    @Override
    public List<MsPatientInfoVO> listPatientInfoByProcessIds(MsProcessSimpleInfoQueryDTO dto) {
        return adsMulProcessInfoMapper.listMultichannelPatientInfo(dto);
    }

    @Override
    public List<MsProcessSimpleInfoVO> listSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsMulProcessInfoMapper.listMultichannelProcessInfo(queryDTO);
    }

    @Override
    public Object listProcessModelSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {
        return new ArrayList<>();
    }

    @Override
    public List<MsProcessLogVO> listProcessLog(MsProcessLogQueryDTO queryDTO) {
        throw new MedicalBusinessException("暂不支持多渠道病例时间轴");
    }

    @Override
    public List<TbCdcewEmergencyEventProcessInfo> getProcessInfoBy(List<String> ids) {

        return adsMulProcessInfoMapper.getMultichannelProcessInfoBy(ids);
    }

    @Override
    public ProcessPathogenInfoVO getProcessPathogenInfoBy(List<String> ids) {

        return adsMulProcessInfoMapper.getMultichannelProcessPathogenInfoBy(ids);
    }

    @Override
    public PageInfo<MsProcessSimpleInfoVO> syndromeSimpleInfoPageListByConditions(MsProcessSimpleInfoConditionQueryDTO queryDTO) {
        return null;
    }
}