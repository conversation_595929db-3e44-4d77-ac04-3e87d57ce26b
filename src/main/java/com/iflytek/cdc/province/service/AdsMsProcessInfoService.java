package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO;
import com.iflytek.cdc.province.model.dto.MsProcessLogQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO;
import com.iflytek.cdc.province.model.vo.*;

import java.util.List;

/**
 * 病程服务
 */
public interface AdsMsProcessInfoService {

    /**
     * 根据id查询传染病简单病程信息
     */
    List<MsProcessSimpleInfoVO> listInfectedSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 症候群病例 重点场所以及监测症状信息
     */
    List<MsProcessMonitorInfoVO> listSyndromeProcessDetailInfo(MsProcessSimpleInfoQueryDTO dto);

}
