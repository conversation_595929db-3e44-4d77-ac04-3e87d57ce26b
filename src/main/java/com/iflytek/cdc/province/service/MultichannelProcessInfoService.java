package com.iflytek.cdc.province.service;

import com.iflytek.cdc.province.model.dto.PathogenCombinationQueryDTO;
import com.iflytek.cdc.province.model.vo.PathogenCheckVO;
import com.iflytek.cdc.province.model.pathogen.PathogenInfectionSituationVO;

import java.util.List;

public interface MultichannelProcessInfoService {


    /**
     * 查询多渠道病例病原检测结果
     * */
    List<PathogenCheckVO> listMultichannelPathogenCheckResult(PathogenCombinationQueryDTO dto);

    /**
     * 查询多渠道病例阳性病原检测
     * */
    List<PathogenCheckVO> listPositivePathogenCheckResult(PathogenCombinationQueryDTO dto);

    /**
     * 查询多渠道病例病原感染情况
     * */
    List<PathogenInfectionSituationVO> getPathogenInfectionSituation(PathogenCombinationQueryDTO dto);
}
