package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import com.iflytek.cdc.province.entity.dim.DimStreetInfo;
import com.iflytek.cdc.province.mapper.pg.DimStreetInfoMapper;
import com.iflytek.cdc.province.model.vo.TreeNode;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.service.DimStreetInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

@Service
@Slf4j
public class DimStreetInfoServiceImpl implements DimStreetInfoService {

    @Resource
    private DimStreetInfoMapper dimStreetInfoMapper;

    @Resource
    private CommonUtilService commonUtilService;

    @Override
    public List<TreeNode> getAreaInfo() {

        List<DimStreetInfo> allStreetInfo = dimStreetInfoMapper.listAll();
        return this.buildTreeByStreetInfo(allStreetInfo);
    }

    /**
     * 通过street实体类构建树结构
     * */
    public List<TreeNode> buildTreeByStreetInfo(List<DimStreetInfo> allStreetInfo){

        if(CollectionUtil.isEmpty(allStreetInfo)){
            return new ArrayList<>();
        }

        return commonUtilService.groupByList(allStreetInfo,
                                            DimStreetInfo::getProvinceName,
                                            DimStreetInfo::getProvinceCode,
                                            (cityEntry) -> commonUtilService.groupByList(
                                                    cityEntry.getValue(),
                                                    DimStreetInfo::getCityName,
                                                    DimStreetInfo::getCityCode,
                                                    (districtEntry) -> commonUtilService.groupByList(
                                                            districtEntry.getValue(),
                                                            DimStreetInfo::getDistrictName,
                                                            DimStreetInfo::getDistrictCode,
                                                            (streetEntry) -> commonUtilService.groupByList(
                                                                    streetEntry.getValue(),
                                                                    DimStreetInfo::getStreetName,
                                                                    DimStreetInfo::getStreetCode,
                                                                    null))));
    }

}
