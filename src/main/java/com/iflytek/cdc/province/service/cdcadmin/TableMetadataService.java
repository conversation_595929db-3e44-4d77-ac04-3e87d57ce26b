package com.iflytek.cdc.province.service.cdcadmin;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableColumnInfo;
import com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableInfo;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmMetadataTableColumnInfoMapper;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmMetadataTableInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;


@Component
@Slf4j
public class TableMetadataService {

    private final Cache<String, TbCdcdmMetadataTableInfo> TABLE_METADATA = CacheBuilder.newBuilder()
                                                                                       .expireAfterWrite(12, TimeUnit.DAYS)
                                                                                       .build();

    private final Cache<String, TbCdcdmMetadataTableColumnInfo> TABLE_COLUMN_METADATA = CacheBuilder.newBuilder()
                                                                                                    .expireAfterWrite(12, TimeUnit.DAYS)
                                                                                                    .build();


    @Resource
    TbCdcdmMetadataTableInfoMapper tbCdcdmMetadataTableInfoMapper;

    @Resource
    TbCdcdmMetadataTableColumnInfoMapper tbCdcdmMetadataTableColumnInfoMapper;

    public TbCdcdmMetadataTableColumnInfo selectColumnInfo(String id) {
        try {
            TABLE_COLUMN_METADATA.get(id, () -> tbCdcdmMetadataTableColumnInfoMapper.selectByPrimaryKey(id));
        } catch (ExecutionException e) {
            log.error("表字段-元信息加载失败: {}", id, e);
        }
        return tbCdcdmMetadataTableColumnInfoMapper.selectByPrimaryKey(id);
    }
}
