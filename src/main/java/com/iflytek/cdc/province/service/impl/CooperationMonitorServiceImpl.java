package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.province.constant.PathogenDataConstant;
import com.iflytek.cdc.province.enums.PathogenProcessTypeEnum;
import com.iflytek.cdc.province.enums.PopulationTypes;
import com.iflytek.cdc.province.mapper.pg.AdsDsDrugStatMapper;
import com.iflytek.cdc.province.mapper.pg.AdsPoHotListInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsPoIndexInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsPoInfectTransmitInfoMapper;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.CollaborMonitorQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ProcessPathogenQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.VirusCheckQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.DrugSalesVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.IncidentCaseNoticeVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorKeywordVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.TopicConfigInfoVO;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.service.CooperationMonitorService;
import com.iflytek.cdc.province.service.IntegratedUtilsService;
import com.iflytek.cdc.province.service.MultichannelUtilsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CooperationMonitorServiceImpl implements CooperationMonitorService {

    @Resource
    private AdsDsDrugStatMapper adsDsDrugStatMapper;

    @Resource
    private AdsPoIndexInfoMapper adsPoIndexInfoMapper;

    @Resource
    private AdsPoHotListInfoMapper adsPoHotListInfoMapper;

    @Resource
    private AdsPoInfectTransmitInfoMapper adsPoInfectTransmitInfoMapper;

    @Resource
    private MultichannelUtilsService multichannelUtilsService;

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Resource
    private IntegratedUtilsService integratedUtilsService;

    //设置多渠道查询参数
    private void setCollaborQueryDTO(CollaborMonitorQueryDTO queryDTO, TopicConfigInfoVO configInfoVO) {

        //设置区域查询参数
        CommonUtilService.setAreaQueryDTO(queryDTO);

        multichannelUtilsService.setTopicKeysAndDrugsQuery(queryDTO,
                                                           configInfoVO,
                                                           null,
                                                           CollaborMonitorQueryDTO::setTopicKeywords,
                                                           null,
                                                           CollaborMonitorQueryDTO::setTopicDrugNames);
    }

    //设置预警场景查询参数
    private void setWarningScenarioQueryDTO(CollaborMonitorQueryDTO queryDTO) {

        //设置区域查询参数
        CommonUtilService.setAreaQueryDTO(queryDTO);
        //设置人群分类
        if (StringUtils.isNotBlank(queryDTO.getWarningScenarioType())) {
            queryDTO.setPersonTypeList(PopulationTypes.getPersonTypeList(queryDTO.getWarningScenarioType()));
        }
        if (StringUtils.isNotBlank(queryDTO.getOtherWarningScenarioType())) {
            queryDTO.setPersonTypeList(PopulationTypes.specialPersonTypeList());
        }
    }

    private <T> T setQueryDTOAndExecute(CollaborMonitorQueryDTO queryDTO,
                                        Function<CollaborMonitorQueryDTO, T> executor) {

        if (StringUtils.isNotBlank(queryDTO.getTopicId())) {
            // 获取多渠道专题信息
            TopicConfigInfoVO configInfoVO = adminServiceApi.getTopicConfig(queryDTO.getTopicId());
            if (configInfoVO == null) {
                return (T) Collections.emptyList();
            }
            setCollaborQueryDTO(queryDTO, configInfoVO);
        } else {
            if (StringUtils.isBlank(queryDTO.getWarningScenarioType()) && StringUtils.isBlank(queryDTO.getOtherWarningScenarioType())) {
                return (T) Collections.emptyList();
            }
            //设置预警场景查询参数
            setWarningScenarioQueryDTO(queryDTO);
        }
        return executor.apply(queryDTO);
    }

    @Override
    public List<DrugSalesVO> listSalesGrowthRanking(CollaborMonitorQueryDTO queryDTO) {

        return setQueryDTOAndExecute(queryDTO, q -> {
                    try (Page<?> ignored = PageHelper.startPage(q.getPageIndex(), q.getPageSize())) {
                        return adsDsDrugStatMapper.selectDrugSalesBy(q);
                    }});
    }

    @Override
    public List<DrugSalesVO> listIntegratedSalesGrowthRanking(CollaborMonitorQueryDTO queryDTO) {
        setWarningScenarioQueryDTO(queryDTO);
        return adsDsDrugStatMapper.selectDrugSalesBy(queryDTO);
    }

    @Override
    public List<MonitorKeywordVO> listSearchIndexSituation(CollaborMonitorQueryDTO queryDTO) {

        return setQueryDTOAndExecute(queryDTO, q -> {
            if (StrUtil.isBlank(queryDTO.getIndexClass())) {
                queryDTO.setIndexClass("搜索指数");
            }
            return adsPoIndexInfoMapper.selectKeywordCountBy(queryDTO);
        });
    }

    @Override
    public List<MonitorKeywordVO> listIntegratedSearchIndexSituation(CollaborMonitorQueryDTO queryDTO) {
        setWarningScenarioQueryDTO(queryDTO);
        if (StrUtil.isBlank(queryDTO.getIndexClass())) {
            queryDTO.setIndexClass("搜索指数");
        }
        return adsPoIndexInfoMapper.selectKeywordCountBy(queryDTO);
    }

    @Override
    public List<MonitorKeywordVO> listKeywordSituation(CollaborMonitorQueryDTO queryDTO) {

        return setQueryDTOAndExecute(queryDTO, adsPoHotListInfoMapper::selectKeywordCountBy);
    }

    @Override
    public List<MonitorKeywordVO> searchKeywordSituation(CollaborMonitorQueryDTO queryDTO) {

        return setQueryDTOAndExecute(queryDTO, adsPoHotListInfoMapper::searchKeywordSituation);
    }

    @Override
    public List<IncidentCaseNoticeVO> listIncidentCaseNotice(CollaborMonitorQueryDTO queryDTO) {

        return setQueryDTOAndExecute(queryDTO, adsPoInfectTransmitInfoMapper::selectIncidentCountWithLastYearBy);
    }

    public void preprocessQueryDTO(ProcessPathogenQueryDTO queryDTO) {
        // 设置区域查询参数
        CommonUtilService.setAreaQueryDTO(queryDTO);

        // 根据传入的病原id集合 查询对应的病原以及所有子类id集合
        integratedUtilsService.setIntegratedPathogenQuery(queryDTO,
                ProcessPathogenQueryDTO::getPathogenIdList,
                ProcessPathogenQueryDTO::setAllPathogenNameList);

        // 病原分类代码转换为描述
        if (CollectionUtil.isNotEmpty(queryDTO.getPathogenClassCode())) {
            List<String> res = queryDTO.getPathogenClassCode().stream()
                    .map(PathogenDataConstant.PathogenClassEnum::getDescByCode)
                    .collect(Collectors.toList());
            queryDTO.setPathogenClassCode(res);
        }
    }
}
