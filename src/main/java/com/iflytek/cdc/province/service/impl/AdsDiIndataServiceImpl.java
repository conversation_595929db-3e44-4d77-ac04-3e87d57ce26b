package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.iflytek.cdc.edr.apiService.UapServiceApi;
import com.iflytek.cdc.edr.enums.DateDimEnum;
import com.iflytek.cdc.edr.utils.DataUtils;
import com.iflytek.cdc.edr.utils.DateFormatUtil;
import com.iflytek.cdc.edr.utils.DimDateUtils;
import com.iflytek.cdc.edr.vo.DiIndataStatVO;
import com.iflytek.cdc.province.entity.ads.AdsDiIndataStatTotal;
import com.iflytek.cdc.province.enums.CycleTypeEnum;
import com.iflytek.cdc.province.mapper.pg.AdsDiIndataStatMapper;
import com.iflytek.cdc.province.mapper.pg.AdsDiIndataStatTotalMapper;
import com.iflytek.cdc.province.model.dto.DiIndataQueryParam;
import com.iflytek.cdc.province.model.vo.TimeTrendVO;
import com.iflytek.cdc.province.service.AdsDiIndataStatService;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.utils.StatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Configuration
@ConditionalOnProperty(value = "dataService.version", havingValue = "v2")
public class AdsDiIndataServiceImpl implements AdsDiIndataStatService {
    @Resource
    private AdsDiIndataStatMapper adsDiIndataStatMapper;
    @Resource
    private AdsDiIndataStatTotalMapper adsDiIndataStatTotalMapper;

    @Resource
    private DimDateUtils dimDateUtils;

    @Override
    public DiIndataStatVO getLastCount(DiIndataQueryParam dateDimQueryParam) {
        CommonUtilService.setAreaQueryDTO(dateDimQueryParam);

        //累计病历量统计
        Date medicalStatDate = Optional.ofNullable(adsDiIndataStatMapper.getLastStatDate(dateDimQueryParam)).orElse(DateFormatUtil.today());
        DiIndataStatVO lastMedicalCount = adsDiIndataStatMapper.getLastCount(null, null, dateDimQueryParam); // 累计病历量统计
        if (lastMedicalCount == null) {
            lastMedicalCount = new DiIndataStatVO();
        }
        Date medicalStatDateLast = DateUtil.offsetDay(medicalStatDate, -1);
        Date startDate = DateFormatUtil.getTheFistSecondOfOneDay(medicalStatDateLast);
        Date endDate = DateFormatUtil.getTheLastSecondOfOneDay(medicalStatDateLast);
        lastMedicalCount.setStatDate(DateUtil.formatDate(medicalStatDate));
        DiIndataStatVO lastMedicalCountLast = adsDiIndataStatMapper.getLastCount(startDate, endDate, dateDimQueryParam); // 昨日病历量统计
        if (lastMedicalCountLast != null) {
            lastMedicalCount.setMedicalCntLast(lastMedicalCountLast.getMedicalCntTotal());
            lastMedicalCount.setMedicalOutpatCntLast(lastMedicalCountLast.getMedicalOutpatCntTotal());
            lastMedicalCount.setMedicalInpatCntLast(lastMedicalCountLast.getMedicalInpatCntTotal());
        }

        //累计患者量统计
        Date lastStatDate = adsDiIndataStatTotalMapper.getLastStatDate(dateDimQueryParam);
        DateTime yesterday = DateUtil.offsetDay(lastStatDate, -1);
        AdsDiIndataStatTotal totalPatientCount = adsDiIndataStatTotalMapper.getTotalPatientCount(lastStatDate, dateDimQueryParam);
        AdsDiIndataStatTotal datePatientCount = adsDiIndataStatTotalMapper.getTotalPatientCount(yesterday, dateDimQueryParam);

        lastMedicalCount.setGlobalPatientCntTotal(Optional.ofNullable(totalPatientCount)
                .map(AdsDiIndataStatTotal::getGlobalPatientCntTotal)
                .orElse(0));
        lastMedicalCount.setGlobalPatientCntLast(Optional.ofNullable(datePatientCount)
                .map(AdsDiIndataStatTotal::getGlobalPatientCnt)
                .orElse(0));
        return lastMedicalCount;
    }

    @Override
    public List<DiIndataStatVO> statLineChart(DiIndataQueryParam queryParam) {

        CommonUtilService.setAreaQueryDTO(queryParam);
        Date endDate = Optional.ofNullable(adsDiIndataStatMapper.getLastStatDate(queryParam)).orElse(DateFormatUtil.today());
        queryParam.setEndDate(DateFormatUtil.getTheLastSecondOfOneDay(endDate));
        queryParam.setStartDate(DateFormatUtil.getTheFistSecondOfOneDay(DateUtils.addYears(endDate, -1)));
        if (queryParam.getStartDate() != null && queryParam.getEndDate() != null) {
            queryParam.setStartDate(DateFormatUtil.getTheFistSecondOfOneDay(queryParam.getStartDate()));
            queryParam.setEndDate(DateFormatUtil.getTheLastSecondOfOneDay(queryParam.getEndDate()));
        }

        DateDimEnum dateDimEnum = DateDimEnum.getByCode(queryParam.getDateDimType());
        if(dateDimEnum == null){
            dateDimEnum = DateDimEnum.DAY;
        }
        //查询本期以及同期的 累计病历、累计门诊病历、累计住院病历
        List<DiIndataStatVO> medicalStatLine = StatUtils.fulfillLastValueByDay(queryParam, CycleTypeEnum.LAST_YEAR, DiIndataStatVO::new,
                adsDiIndataStatMapper::dataStat, DiIndataStatVO::getStatDate, DiIndataStatVO::setStatDate, DiIndataStatVO.buildLastYOperation());
        //查询累计患者量
        List<DiIndataStatVO> patientCountLine = StatUtils.fulfillLastValueByDay(queryParam, CycleTypeEnum.LAST_YEAR, DiIndataStatVO::new,
                adsDiIndataStatTotalMapper::patientStat, DiIndataStatVO::getStatDate, DiIndataStatVO::setStatDate, DiIndataStatVO.buildLastYOperation());

        List<DiIndataStatVO> mergeList = mergeMedicalAndPatientStats(medicalStatLine, patientCountLine);
        return StatUtils.fulfillAndConvertDayDateData(
                mergeList,
                queryParam.getStartDate(),
                queryParam.getEndDate(),
                dateDimEnum,
                DiIndataStatVO::getStatDate,
                DiIndataStatVO::setStatDate,
                DiIndataStatVO.dataConverter);
    }

    private List<DiIndataStatVO> mergeMedicalAndPatientStats(List<DiIndataStatVO> medicalStatLine,
                                                             List<DiIndataStatVO> patientCountLine) {
        // 构建日期映射表
        Map<String, DiIndataStatVO> medicalMap = medicalStatLine.stream()
                .collect(Collectors.toMap(DiIndataStatVO::getStatDate, Function.identity()));
        Map<String, DiIndataStatVO> patientMap = patientCountLine.stream()
                .collect(Collectors.toMap(DiIndataStatVO::getStatDate, Function.identity()));
        // 获取所有日期（并集）
        Set<String> allDates = new HashSet<>();
        allDates.addAll(medicalMap.keySet());
        allDates.addAll(patientMap.keySet());
        // 合并处理
        List<DiIndataStatVO> mergedList = new ArrayList<>();
        for (String date : allDates) {
            // 获取或创建病历统计对象
            DiIndataStatVO medicalStat = medicalMap.getOrDefault(date, new DiIndataStatVO());
            medicalStat.setStatDate(date);
            // 如果存在患者量数据，则合并
            if (patientMap.containsKey(date)) {
                DiIndataStatVO patientStat = patientMap.get(date);
                medicalStat.setGlobalPatientCnt(patientStat.getGlobalPatientCnt());
                medicalStat.setGlobalPatientCntLastY(patientStat.getGlobalPatientCntLastY());
            }
            mergedList.add(medicalStat);
        }
        mergedList.sort(Comparator.comparing(DiIndataStatVO::getStatDate));
        return mergedList;
    }

}
