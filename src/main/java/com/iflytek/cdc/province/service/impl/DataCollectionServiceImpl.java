package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.apiService.CdcPlatformApi;
import com.iflytek.cdc.edr.common.Common;
import com.iflytek.cdc.edr.dto.SignalQueryDTO;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import com.iflytek.cdc.edr.vo.WarningSignalVO;
import com.iflytek.cdc.province.cache.TableInfoCache;
import com.iflytek.cdc.province.dm.FilterParam;
import com.iflytek.cdc.province.entity.bu.*;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmDataFormTemplateMapper;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmMetadataTableColumnInfoMapper;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.mapper.bu.TbCdcewDataFavoritesDetailMapper;
import com.iflytek.cdc.province.mapper.bu.TbCdcewDataFavoritesMapper;
import com.iflytek.cdc.province.model.dto.dm.*;
import com.iflytek.cdc.province.model.vo.WarningSignalExcelVO;
import com.iflytek.cdc.province.service.*;
import com.iflytek.cdc.edr.utils.DateFormatUtil;
import com.iflytek.cdc.edr.vo.dm.DataModelDetailVO;
import com.iflytek.cdc.edr.vo.dm.FavoritesListVO;
import com.iflytek.cdc.edr.vo.pandemic.MedicalInfoVO;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

@Service
@Slf4j
public class DataCollectionServiceImpl implements DataCollectionService {

    @Resource
    private MonitorCommonUtils monitorCommonUtils;

    @Resource
    private TbCdcewDataFavoritesMapper tbCdcewDataFavoritesMapper;

    @Resource
    private TbCdcewDataFavoritesDetailMapper tbCdcewDataFavoritesDetailMapper;

    @Resource
    private DataModelDataService dataModelDataService;

    @Resource
    private DataModelCommonUtils dataModelCommonUtils;

    @Resource
    private TbCdcdmDataFormTemplateMapper templateMapper;

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Resource
    private CdcPlatformApi cdcPlatformApi;

    @Resource
    private ExportTaskService exportTaskService;

    @Resource
    private CommonUtilService commonUtilService;

    @Resource
    private TableInfoCache tableInfoCache;

    @Resource
    private TbCdcdmMetadataTableColumnInfoMapper columnInfoMapper;

    /**
     * 查看收藏夹列表
     * */
    @Override
    public PageInfo<FavoritesListVO> getFavoritesList(FavoriteQueryDTO dto) {

        UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
        dto.setCreatorId(uapUserPo.getId());
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        List<FavoritesListVO> favoritesList = tbCdcewDataFavoritesMapper.getFavoritesList(dto);
        return new PageInfo<>(favoritesList);
    }

    @Override
    public void editFavorites(FavoritesEditDTO dto) {

        UapUserPo uapUserPo = userInfo.get();
        this.checkDataAuth(uapUserPo.getId(), dto.getId());
        tbCdcewDataFavoritesMapper.editFavorites(monitorCommonUtils.createFavoritesEntity(dto, uapUserPo.getId(), uapUserPo.getName()));
    }

    @Override
    public void deleteFavorites(String favoritesId) {

        UapUserPo uapUserPo = userInfo.get();
        this.checkDataAuth(uapUserPo.getId(), favoritesId);
        tbCdcewDataFavoritesMapper.updateFavorites(favoritesId, uapUserPo.getId(), uapUserPo.getName());
    }

    /**
     * 查看收藏夹详情
     * */
    @Override
    public PageInfo<String> getFavoritesDetails(FavoritesDetailsQueryDTO dto) {

        UapUserPo uapUserPo = userInfo.get();
        this.checkDataAuth(uapUserPo.getId(), dto.getFavoritesId());

        //查看收藏夹详情id列表
        List<TbCdcewDataFavoritesDetail> details = tbCdcewDataFavoritesDetailMapper.getFavoritesDetails(dto);
        if(CollectionUtil.isEmpty(details)){
            return new PageInfo<>();
        }
        //获取该文件夹存储数据的 数据模型id
        String modelId = details.get(0).getDataModelId();
        //收藏数据的记录id
        List<String> recordIds = details.stream().map(TbCdcewDataFavoritesDetail::getDataRecordId).collect(Collectors.toList());

        return queryPageFavoritesData(modelId, dto.getPageIndex(), dto.getPageSize(), recordIds);
    }

    /**
     * 分页查询收藏 数据详情
     * */
    private PageInfo<String> queryPageFavoritesData(String modelId,
                                                    Integer pageIndex,
                                                    Integer pageSize,
                                                    List<String> recordIds){

        //数据模型配置
        TbCdcdmDataFormTemplate template = templateMapper.getModelConfigByModelId(modelId);
        if(null == template){
            throw new RuntimeException("数据配置错误！");
        }
        //数据模型的主键
        TbCdcdmMetadataTableColumnInfo columnInfo = columnInfoMapper.selectByTableAndBusiColName(template.getMasterTableId(), template.getKeyField());
        //数据模型的业务主键
        String businessKey = template.getBusinessKey();
        String key = (businessKey != null && tableInfoCache.getColumnInfoByCache(businessKey) != null)
                ? tableInfoCache.getColumnInfoByCache(businessKey).getColumnName() : columnInfo.getColumnName();
        FilterParam filterParam = new FilterParam().put(key, recordIds);

        TbCdcdmMetadataTableInfo tableInfo = tableInfoCache.getTableInfoByCache(template.getRetrieveTable());
        if(tableInfo == null){
            return new PageInfo<>();
        }
        String sql = dataModelCommonUtils.buildRetrieveQuerySql(template, tableInfo, filterParam);
        //todo : 目前收藏只处理 病例数据和原始病历（暂时不支持信号收藏）
        PageHelper.startPage(pageIndex, pageSize);
        List<Map<String, Object>> result = monitorCommonUtils.queryByTableSchema(tableInfo.getDataSourceKey(), tableInfo.getSchema(), sql);
        List<String> jsonList = result.stream()
                .map(map -> map != null ? map.get(Common.CONTENT_JSON) : null)
                .filter(Objects::nonNull)
                .map(Object::toString)
                .map(e -> dataModelCommonUtils.desensitizationJson(modelId, e))
                .collect(Collectors.toList());

        return commonUtilService.fromPageInfo(new PageInfo<>(result), jsonList);
    }

    @Override
    public TbCdcmrExportTask getFavoritesDetailsExport(FavoritesDetailsQueryDTO dto) {

        UapUserPo uapUserPo = userInfo.get();
        String taskParams = JSONObject.toJSONString(dto);
        ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                                                 dto.getTaskName(),
                                                 dto.getTaskUrl(),
                                                 taskParams,
                                                 dto.getModuleType(),
                                                 dto.getExportType());

        TbCdcmrExportTask existTask = adminServiceApi.checkExistTask(taskDTO, uapUserPo.getId());
        if (null != existTask){
            return existTask;
        }
        adminServiceApi.checkExportTaskCount(uapUserPo.getId());

        List<TbCdcewDataFavoritesDetail> details = tbCdcewDataFavoritesDetailMapper.getFavoritesDetails(dto);
        //获取该文件夹存储数据的 数据模型版本id
        String modelVersionId = details.get(0).getModelVersionId();
        List<String> recordIds = details.stream().map(TbCdcewDataFavoritesDetail::getDataRecordId).collect(Collectors.toList());
        //如果选择了导出的数据，则只导出选择的数据
        recordIds = CollectionUtil.isEmpty(dto.getRecordIds()) ? recordIds : CommonUtilService.intersection(recordIds, dto.getRecordIds());

        //获取该模块的数据模型
        List<DataModelDetailVO> detailVOS = dataModelDataService.getDataModelByCode(null, null, null);
        //一个模块中配置数据模型不允许重复 - 此处过滤查询的数据模型
        DataModelDetailVO modelDetail = detailVOS.stream().filter(e -> modelVersionId.equals(e.getModelVersionId())).findFirst().orElse(null);
        if(modelDetail == null){
            throw new MedicalBusinessException("无数据!");
        }
        //构建查询参数
        SearchQueryDTO queryDTO = new SearchQueryDTO();
        BeanUtils.copyProperties(dto, queryDTO);
        queryDTO.setModelId(modelDetail.getModelId());
        List<MedicalInfoVO> result = dataModelCommonUtils.getRecordList(queryDTO, recordIds, Boolean.FALSE, Boolean.TRUE);

        Integer count = result.size();
        adminServiceApi.checkExportMax(count);
        taskDTO.setTotalCount(count);
        TbCdcmrExportTask exportTask = adminServiceApi.addExportTask(taskDTO, uapUserPo.getId());
        byte[] bytes = monitorCommonUtils.buildByte(result);
        exportTaskService.runTaskAndUploadFile(bytes,exportTask);
        return exportTask;
    }

    @Override
    @Transactional
    public void batchDataCollection(DataCollectionStatusDTO dto) {

        UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
        //本次批量收藏数据的数据模型版本id
        String modelVersionId = dto.getDataModelVersionId();
        //获取本次更新的收藏文件夹id、所处系统
        String favoritesId = dto.getFavoritesId();
        //判断当前收藏的数据是否和已收藏的数据 数据模型版本 一致
        TbCdcewDataFavorites favorites = tbCdcewDataFavoritesMapper.getFavoritesInfo(favoritesId);
        if(StringUtils.isNotBlank(favorites.getModelVersionId()) && !Objects.equals(modelVersionId, favorites.getModelVersionId())){
            log.info("收藏数据和收藏夹已有数据 不是同一个数据模型版本");
            return;
        }
        //初次符合条件的收藏 更新收藏夹的数据模型版本id
        if(StringUtils.isBlank(favorites.getModelVersionId())){
            tbCdcewDataFavoritesMapper.updateFavoritesModelVersion(uapUserPo.getId(), uapUserPo.getName(), modelVersionId, favoritesId, dto.getModuleType());
        }
        List<TbCdcewDataFavoritesDetail> details = monitorCommonUtils.generateFavoritesDetailList(dto);
        //插入收藏数据
        tbCdcewDataFavoritesDetailMapper.batchInsertCollection(details);
        //更新收藏文件夹 文件数量
        tbCdcewDataFavoritesMapper.updateFavoritesDataCount(uapUserPo.getId(), uapUserPo.getName(), favoritesId);
    }

    /**
     * 取消收藏，变更某个文件夹下 收藏数据的收藏状态
     * */
    @Override
    @Transactional
    public void batchDataUnCollection(DataCollectionStatusDTO dto) {

        UapUserPo uapUserPo = userInfo.get();
        //获取本次更新的收藏文件夹id
        String favoritesId = dto.getFavoritesId();

        //（批量）取消收藏 - 单个文件夹下操作
        tbCdcewDataFavoritesDetailMapper.updateCollectionStatus(favoritesId, dto.getDataRecordIdList());

        //更新收藏文件夹 文件数量
        tbCdcewDataFavoritesMapper.updateFavoritesDataCount(uapUserPo.getId(), uapUserPo.getName(), favoritesId);
    }

    @Override
    public void checkDataAuth(String loginUserId, String id) {
        if (StrUtil.isNotBlank(id)){
            TbCdcewDataFavorites favorites = tbCdcewDataFavoritesMapper.getByIdAndUser(id, loginUserId);
            if (favorites == null){
                throw new MedicalBusinessException(" 当前用户无权限操作该收藏夹信息");
            }
        }
    }

    @Override
    public PageInfo<WarningSignalVO> getSignalFavoritesDetails(FavoritesDetailsQueryDTO dto, String loginUserId) {

        UapUserPo uapUserPo = userInfo.get();
        this.checkDataAuth(uapUserPo.getId(), dto.getFavoritesId());

        //查看收藏夹详情id列表
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        List<TbCdcewDataFavoritesDetail> details = tbCdcewDataFavoritesDetailMapper.getFavoritesDetails(dto);
        if(CollectionUtil.isEmpty(details)){
            return new PageInfo<>();
        }
        //收藏数据的记录id
        List<String> recordIds = details.stream().map(TbCdcewDataFavoritesDetail::getDataRecordId).collect(Collectors.toList());

        Map<String, TbCdcewDataFavoritesDetail> detailMap = details.stream().collect(Collectors.toMap(TbCdcewDataFavoritesDetail::getDataRecordId,
                                                                                                      Function.identity(),
                                                                                                      (a, b) -> a));

        SignalQueryDTO queryDTO = SignalQueryDTO.builder().signalIds(recordIds).build();
        List<WarningSignalVO> signalVOList = cdcPlatformApi.eventListInfo(queryDTO, loginUserId);
        signalVOList.forEach(e -> {
            TbCdcewDataFavoritesDetail detail = detailMap.get(e.getSignalId());
            if (detail != null){
                e.setCollectTime(detail.getCollectTime());
            }
        });
        signalVOList = signalVOList.stream().sorted(Comparator.comparing(WarningSignalVO::getCollectTime).reversed()).collect(Collectors.toList());
        return commonUtilService.fromPageInfo(new PageInfo<>(details), signalVOList);
    }

    @Override
    public TbCdcmrExportTask signalFavoritesDetailsExport(FavoritesDetailsQueryDTO dto, String loginUserId) {

        SignalQueryDTO queryDTO = SignalQueryDTO.builder().signalIds(dto.getRecordIds()).build();
        List<WarningSignalVO> signalVOList = cdcPlatformApi.eventListInfo(queryDTO, loginUserId);

        ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                                                 dto.getTaskName(),
                                                 dto.getTaskUrl(),
                                                 JSONObject.toJSONString(dto),
                                                 dto.getModuleType(),
                                                 dto.getExportType());

        return exportTaskService.addAndUploadFile(dto,
                                                  () -> signalVOList,
                                                  () -> CollUtil.isEmpty(signalVOList) ? 0 : signalVOList.size(),
                                                  taskDTO,
                                                  WarningSignalExcelVO.class,
                                                  WarningSignalExcelVO::of);
    }

}
