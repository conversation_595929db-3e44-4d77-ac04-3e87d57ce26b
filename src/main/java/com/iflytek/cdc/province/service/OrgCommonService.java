package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.dto.org.OrgParams;
import com.iflytek.cdc.province.model.dto.dm.OrgQueryDTO;
import com.iflytek.cdc.province.model.vo.OrgInfoVO;
import com.iflytek.cdc.province.model.vo.TreeNode;
import com.iflytek.cdc.province.model.vo.ValueDomainVO;

import java.util.List;

public interface OrgCommonService {

    /**
     * 查询所有机构，平铺
     * */
    List<ValueDomainVO> getAllOrgInfo();

    List<OrgInfoVO> getAllOrgList(OrgParams orgParams);

    List<TreeNode> getOrgList(OrgQueryDTO dto);
}
