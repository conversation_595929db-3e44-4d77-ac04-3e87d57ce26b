package com.iflytek.cdc.province.service.impl;

import com.iflytek.cdc.province.mapper.bu.TbCdcdmClimateEnvironmentAnalysisMapper;
import com.iflytek.cdc.province.model.vo.ClimateEnvironmentAnalysisVo;
import com.iflytek.cdc.province.service.SignalService;
import com.iflytek.cdc.reportcard.dto.common.CommonQueryDTO;
import com.iflytek.cdc.reportcard.dto.common.DateDim;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SignalServiceImpl implements SignalService {

    @Autowired
    private TbCdcdmClimateEnvironmentAnalysisMapper tbCdcdmClimateEnvironmentAnalysisMapper;

    @Override
    public List<ClimateEnvironmentAnalysisVo> queryClimateEnvironmentAnalysis(CommonQueryDTO commonQuery) {
        List<DateDim> dateDims = DateDim.yearAndMonth(commonQuery.getStartDate(), commonQuery.getEndDate());
        commonQuery.setDateDims(dateDims);
        return tbCdcdmClimateEnvironmentAnalysisMapper.queryClimateEnvironmentAnalysis(commonQuery);
    }
}
