package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.cdc.province.entity.bu.SyndromeVerifyRecord;
import com.iflytek.cdc.province.mapper.bu.SyndromeVerifyRecordMapper;
import com.iflytek.cdc.province.service.SyndromeVerifyRecordService;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

/**
 * <p>
 * 症候群核实记录服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
public class SyndromeVerifyRecordServiceImpl implements SyndromeVerifyRecordService {

    @Resource
    private SyndromeVerifyRecordMapper syndromeVerifyRecordMapper;

    @Resource
    private BatchUidService batchUidService;

    @Override
    public SyndromeVerifyRecord getById(String id) {
        if (StrUtil.isBlank(id)) {
            log.warn("查询症候群核实记录失败：ID为空");
            return null;
        }
        return syndromeVerifyRecordMapper.selectById(id);
    }

    @Override
    public List<SyndromeVerifyRecord> getByIds(List<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            log.warn("查询症候群核实记录失败：ID列表为空");
            return CollUtil.newArrayList();
        }
        return syndromeVerifyRecordMapper.selectByIds(ids);
    }

    @Override
    public boolean insert(SyndromeVerifyRecord record) {
        if (record == null) {
            log.warn("插入症候群核实记录失败：记录为空");
            return false;
        }
        
        try {
            // 设置创建时间
            if (record.getCreateTime() == null) {
                record.setCreateTime(new Date());
            }
            // 设置删除标识
            if (StrUtil.isBlank(record.getDeleteFlag())) {
                record.setDeleteFlag("0");
            }
            // 设置创建人信息
            if (userInfo.get() != null) {
                if (StrUtil.isBlank(record.getCreatorId())) {
                    record.setCreatorId(userInfo.get().getId());
                }
                if (StrUtil.isBlank(record.getCreator())) {
                    record.setCreator(userInfo.get().getName());
                }
            }
            int result = syndromeVerifyRecordMapper.insert(record);
            return result > 0;
        } catch (Exception e) {
            log.error("插入症候群核实记录失败：", e);
            return false;
        }
    }

    @Override
    public boolean updateById(SyndromeVerifyRecord record) {
        if (record == null || StrUtil.isBlank(record.getId())) {
            log.warn("更新症候群核实记录失败：记录为空或ID为空");
            return false;
        }
        
        try {
            int result = syndromeVerifyRecordMapper.updateById(record);
            return result > 0;
        } catch (Exception e) {
            log.error("更新症候群核实记录失败：", e);
            return false;
        }
    }

    @Override
    public boolean deleteById(String id) {
        if (StrUtil.isBlank(id)) {
            log.warn("删除症候群核实记录失败：ID为空");
            return false;
        }
        
        try {
            SyndromeVerifyRecord record = new SyndromeVerifyRecord();
            record.setId(id);
            record.setDeleteFlag("1");
            record.setCreateTime(new Date());
            
            // 设置更新人信息
            if (userInfo.get() != null) {
                record.setCreatorId(userInfo.get().getId());
                record.setCreator(userInfo.get().getName());
            }
            
            int result = syndromeVerifyRecordMapper.updateById(record);
            return result > 0;
        } catch (Exception e) {
            log.error("删除症候群核实记录失败：", e);
            return false;
        }
    }
} 