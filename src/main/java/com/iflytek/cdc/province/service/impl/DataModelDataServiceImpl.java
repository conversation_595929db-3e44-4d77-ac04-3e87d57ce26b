package com.iflytek.cdc.province.service.impl;

import com.iflytek.cdc.edr.annotation.DataRetrieveLogAnnotation;
import com.iflytek.cdc.edr.common.Common;
import com.iflytek.cdc.province.cache.TableInfoCache;
import com.iflytek.cdc.province.dm.DataModel;
import com.iflytek.cdc.province.dm.FilterParam;
import com.iflytek.cdc.province.dm.FormGroup;
import com.iflytek.cdc.province.dm.ModelForm;
import com.iflytek.cdc.province.dm.engine.Convertor;
import com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormTemplate;
import com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableColumnInfo;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmDataFormTemplateMapper;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmMetadataTableColumnInfoMapper;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmMetadataTableInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsMsMedicalInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsMsProcessInfoMapper;
import com.iflytek.cdc.province.model.dto.IdentifyRecordDTO;
import com.iflytek.cdc.province.model.vo.ProcessEvent;
import com.iflytek.cdc.province.model.vo.SubmitReportDTO;
import com.iflytek.cdc.province.service.DataModelCommonUtils;
import com.iflytek.cdc.province.service.DataModelDataService;
import com.iflytek.cdc.province.service.MonitorCommonUtils;
import com.iflytek.cdc.province.service.cdcadmin.CdcAdminService;
import com.iflytek.cdc.edr.vo.dm.*;
import com.iflytek.cdc.edr.vo.dm.FormValueVO.FieldValueVO;
import com.iflytek.cdc.edr.vo.dm.FormValueVO.GroupValueVO;
import com.iflytek.cdc.province.utils.GsonUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DataModelDataServiceImpl implements DataModelDataService {


    @Resource
    private Convertor convertor;

    @Resource
    private CdcAdminService cdcAdminService;

    @Resource
    private MonitorCommonUtils monitorCommonUtils;

    @Resource
    private TbCdcdmDataFormTemplateMapper tbCdcdmDataFormTemplateMapper;

    @Resource
    private TbCdcdmMetadataTableColumnInfoMapper tbCdcdmMetadataTableColumnInfoMapper;

    @Resource
    private TbCdcdmMetadataTableInfoMapper tbCdcdmMetadataTableInfoMapper;

    @Resource
    private AdsMsProcessInfoMapper adsMsProcessInfoMapper;

    @Resource
    private AdsMsMedicalInfoMapper adsMsMedicalInfoMapper;

    @Resource
    private TableInfoCache tableInfoCache;
    
    @Resource
    private DataModelCommonUtils dataModelCommonUtils;

    @Override
    public DataModelVO requestModelData(String modelId, Map<String, Object> params) {

        // 1. 根据model id获取数据模型配置
        DataModelVO dataModelVO = cdcAdminService.getDataModelConfig(modelId);

        // 2. 查询数据
        DataModel dataModel = monitorCommonUtils.buildDmEngineModel(dataModelVO);
        DataModel modelData = convertor.buildModelData(dataModel, new FilterParam(params));

        // 4. 数据模型填充数据
        fillDataModeVO(modelData, dataModelVO);

        return dataModelVO;
    }

    private void fillDataModeVO(DataModel modelData, DataModelVO dataModelVO) {
        Map<String, ModelForm> formDataMap = modelData.getModelFormMap();

        for (DataModelFormVO modelFormVO : dataModelVO.getFormList()) {
            ModelForm formData = formDataMap.get(modelFormVO.getModelFormId());
            if (formData != null) {
                List<FormValueVO> formValueList = new ArrayList<>();
                modelFormVO.setFormValueList(formValueList);

                List<ModelForm.FormValue> formValues = formData.getFormValues();
                for (ModelForm.FormValue formValue : formValues) {
                    Object formIdentity = Optional.ofNullable(formValue.getFormIdentityValue()).orElse(formValue.getModelFormId());
                    FormValueVO formValueVO = new FormValueVO(formIdentity);
                    formValueList.add(formValueVO);

                    List<FormGroup.GroupValue> groupValues = formValue.getGroupValues();
                    for (FormGroup.GroupValue groupValue : groupValues) {
                        GroupValueVO groupValueVO = new GroupValueVO(groupValue.getGroupId());
                        for (Map<String, Object> rowValue : groupValue.getValues()) {
                            List<FieldValueVO> fieldValueVOS = rowValue.entrySet()
                                                                       .stream()
                                                                       .map(e -> new FieldValueVO(e.getKey(), e.getValue()))
                                                                       .collect(Collectors.toList());
                            groupValueVO.addRowValue(fieldValueVOS);
                        }
                        formValueVO.getGroupValueList().add(groupValueVO);
                    }
                }
            }
        }
    }

    @Override
    public List<DataModelDetailVO> getDataModelByCode(String formTemplateCode, String configInfo, String modelVersionId) {

        List<String> configInfoList = new ArrayList<>();
        if(StringUtils.isNotBlank(configInfo)){
            configInfo = configInfo.replace('，', ',');
            configInfoList = Arrays.asList(configInfo.split(","));
        }
        return tbCdcdmDataFormTemplateMapper.getDataModelByCode(formTemplateCode, configInfoList, Common.VERSION_STATUS_PUBLISHED, modelVersionId);
    }

    @Override
    public List<IdentifyRecordVO> getIdentifyAndRecordId(IdentifyRecordDTO dto) {

//        //设置process_id
//        dto.buildProcessId();
//        // 校验输入参数
//        if (StringUtils.isBlank(dto.getModelId()) || StringUtils.isBlank(dto.getDiseaseType()) || StringUtils.isBlank(dto.getProcessId())) {
//            return new ArrayList<>();
//        }
//
//        //获取该模块的数据模型
//        DataModelDetailVO detail = tbCdcdmDataFormTemplateMapper.getDataModelByModelId(dto.getModelId(), Common.VERSION_STATUS_PUBLISHED);
//        if(detail == null){
//            return new ArrayList<>();
//        }
//
//        //查询主表信息
//        TbCdcdmMetadataTableColumnInfo columnInfo = tbCdcdmMetadataTableColumnInfoMapper.selectByTableAndBusiColName(detail.getMasterTableId(), detail.getKeyField());
//
//        //该病程中的病历记录id
//        String eventJsonStr = adsMsProcessInfoMapper.getMedicalByProcessId(dto.getDiseaseType(), dto.getProcessId()); //todo 后续需要优化改模块
//        if (StringUtils.isBlank(eventJsonStr)) {
//            return new ArrayList<>();
//        }
//        List<ProcessEvent> events = Arrays.asList(GsonUtils.fromJson(eventJsonStr, ProcessEvent[].class));
//        List<String> eventIdList = events.stream().map(ProcessEvent::getEventId).collect(Collectors.toList());
//        List<String> recordIds = adsMsMedicalInfoMapper.getMedicalIdByEventIdList(eventIdList);
//
//        //查询该模型配置的唯一键
//        Map<String, DataModelVO> modelIdentifyMap = dataModelCommonUtils.getIdentifyFieldByRecordId(detail, columnInfo, recordIds);
//        if(Objects.isNull(modelIdentifyMap)){
//            return new ArrayList<>();
//        }
//
//        // 构建结果列表
//        List<IdentifyRecordVO> result = new ArrayList<>();
//        for (String recordId : recordIds) {
//            DataModelVO mapValue = modelIdentifyMap.get(recordId);
//            if (Objects.nonNull(mapValue)) {
//                IdentifyRecordVO recordVO = new IdentifyRecordVO();
//                recordVO.setModelId(dto.getModelId());
//                recordVO.setMedicalId(recordId);
//                recordVO.setKeyField(detail.getKeyField());
//                recordVO.setIdentifyFieldValue(mapValue.getIdentifyFieldValue());
//                recordVO.setIdentifyFieldTitle(mapValue.getIdentifyFieldTitle());
//                result.add(recordVO);
//            }
//        }
//
//        // 根据 identifyFieldValue 倒序排序
//        result.sort(Comparator.comparing(IdentifyRecordVO::getIdentifyFieldValue).reversed());
//        return result;
        return new ArrayList<>();
    }

    @Override
    public DataModelVO getDataModelInfo(String modelId, String modelVersionId) {

        return cdcAdminService.getDataModelInfo(modelId, modelVersionId);
    }

    @Override
    @DataRetrieveLogAnnotation()
    public String getDetailDataByModel(String loginUserId, String modelId, Map<String, Object> params) {

        TbCdcdmDataFormTemplate template = tbCdcdmDataFormTemplateMapper.getModelConfigByModelId(modelId);
        if(template == null){
            throw new RuntimeException("数据模型配置错误");
        }
        //接收前端传入的过滤条件
        FilterParam filterParam = new FilterParam(params);
        //如果存在检索表配置，则使用检索表查询对应的详情
        if(StringUtils.isNotBlank(template.getRetrieveTable())){
            String jsonStr = dataModelCommonUtils.getModelDetailDataByRetrieveTable(template, filterParam);
            return dataModelCommonUtils.desensitizationJson(modelId, jsonStr);
        }
        //非检索表正常查询 将查询出来的结果构造成对应的数据结构
        return dataModelCommonUtils.getDetailDataByModelAndFilter(modelId, filterParam);
    }

    @Override
    public DataModelVO getFormReportBy(String modelId, Map<String, Object> params) {

        DataModelVO dataModel = getDataModelInfo(modelId, null);
        TbCdcdmDataFormTemplate template = tbCdcdmDataFormTemplateMapper.getModelConfigByModelId(modelId);
        if(template == null){
            throw new RuntimeException("数据模型配置错误");
        }
        //todo 补充查询逻辑

        return dataModel;
    }

}
