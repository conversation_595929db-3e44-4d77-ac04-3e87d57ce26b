package com.iflytek.cdc.province.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.dto.DictParams;
import com.iflytek.cdc.province.model.vo.DictValueVO;
import com.iflytek.cdc.province.service.DimDictService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.cdc.province.mapper.pg.DimDictMapper;
import com.iflytek.cdc.province.entity.dim.DimDict;

import java.util.List;

@Service
public class DimDictServiceImpl extends ServiceImpl<DimDictMapper, DimDict> implements DimDictService {

    @Override
    public DictValueVO getDictBy(String dictCode, String dictValueCode) {
        DimDict dimDict = this.getBaseMapper().getOneBy(dictCode, dictValueCode);

        if (dimDict != null) {
            return new DictValueVO(dimDict.getId(), dimDict.getDictValueCode(), dimDict.getDictValueName());
        }

        return null;
    }

    @Override
    public List<DictValueVO> getDictValueBy(String dictCode) {
        return this.getBaseMapper().searchBy(dictCode, null);
    }

    @Override
    public PageInfo<DictValueVO> searchDictValueBy(DictParams dictParams) {
        String dictCode = dictParams.getDictCode();
        String searchValue = dictParams.getDictValue();

        PageHelper.startPage(dictParams.getPageIndex(), dictParams.getPageSize());
        List<DictValueVO> dictValues = this.getBaseMapper().searchBy(dictCode, searchValue);

        return new PageInfo<>(dictValues);
    }
}
