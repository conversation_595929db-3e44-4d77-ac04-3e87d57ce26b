package com.iflytek.cdc.province.service.impl;

import com.iflytek.cdc.edr.dto.DimInfectedInfoQuery;
import com.iflytek.cdc.province.mapper.pg.DimInfectedInfoMapper;
import com.iflytek.cdc.province.service.DimInfectedInfoService;
import com.iflytek.cdc.edr.vo.ConstantVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class DimInfectedInfoServiceImpl implements DimInfectedInfoService {
    @Resource
    private DimInfectedInfoMapper dimInfectedInfoMapper;

    @Override
    public List<ConstantVO> listInfectedClass() {
        return dimInfectedInfoMapper.listInfectedClass();
    }

    @Override
    public List<ConstantVO> listInfectedType(DimInfectedInfoQuery query) {
        return dimInfectedInfoMapper.listInfectedType(query);
    }

    @Override
    public List<ConstantVO> listInfectedInfoBy(DimInfectedInfoQuery query) {
        return dimInfectedInfoMapper.listInfectedInfoBy(query);
    }

    @Override
    public List<String> getInfectedCodeByNames(List<String> names) {
        return dimInfectedInfoMapper.getInfectedCodeByNames(names);
    }
}
