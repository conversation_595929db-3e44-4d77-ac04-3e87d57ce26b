package com.iflytek.cdc.province.service;

import com.iflytek.cdc.edr.enums.ConfigPademicMonitorEnum;
import com.iflytek.cdc.edr.utils.GetBeanUtil;
import org.springframework.util.Assert;

/**
 * <AUTHOR> dingyuan
 * @date : 2024-06-07
 * PandemicMonitorFactory - 疫情监测 工厂
 */
public class PandemicMonitorFactory {

    public static IPandemicMonitorService getMedicalService(String type) {
        final IPandemicMonitorService medicalService = GetBeanUtil.getBean(IPandemicMonitorService.class, ConfigPademicMonitorEnum.getBeanNameByType(type));
        Assert.isTrue(medicalService != null, "未获取到处理类型:" + type);
        return medicalService;
    }

    private PandemicMonitorFactory() {
        //不让实例化该类
    }
}
