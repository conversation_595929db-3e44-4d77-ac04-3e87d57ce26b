package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.iflytek.cdc.edr.common.Common;
import com.iflytek.cdc.edr.configuration.EsModelConfigProperties;
import com.iflytek.cdc.province.cache.TableInfoCache;
import com.iflytek.cdc.province.dm.PrivilegeParam;
import com.iflytek.cdc.province.dm.engine.AnsiSqlBuilder;
import com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormTemplate;
import com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableColumnInfo;
import com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableInfo;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmDataFormTemplateMapper;
import com.iflytek.cdc.province.mapper.pg.AdsFsSuggestWordMapper;
import com.iflytek.cdc.province.model.dto.dm.*;
import com.iflytek.cdc.province.model.vo.FieldsConfigAnalysisVO;
import com.iflytek.cdc.province.model.vo.SuggestWordVO;
import com.iflytek.cdc.province.service.*;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@ConditionalOnProperty(name = EsModelConfigProperties.PROP_ENABLED, havingValue = "false", matchIfMissing = true)
public class PgModelModelDataSearchServiceImpl implements ModelDataSearchService {

    @Resource
    private MonitorCommonUtils monitorCommonUtils;

    @Resource
    private CommonUtilService commonUtilService;

    @Resource
    private TbCdcdmDataFormTemplateMapper templateMapper;

    @Resource
    private DataModelCommonUtils dataModelCommonUtils;

    @Resource
    private TableInfoCache tableInfoCache;

    @Resource
    private AdsFsSuggestWordMapper suggestWordMapper;

    @Override
    public PageInfo<String> retrieveTableSearchByKey(KeyWordSearchDTO dto) {

        return getSearchResultBy(dto, (tableInfo, diseaseNameColumn, configFields, privilegeParam) ->
                AnsiSqlBuilder.buildRetrieveKeyWordSearchSql(dto.getKeyWord(), dto.getDiseaseName(), tableInfo, diseaseNameColumn, configFields, privilegeParam));
    }

    @Override
    public PageInfo<String> retrieveTableSearchByConditions(CombinationQueryDTO dto) {

        List<ConfigFieldsDTO> inputFilter = dataModelCommonUtils.buildInputFilterListBy(dto.getConditionDTOList());
        return getSearchResultBy(dto, (tableInfo, diseaseNameColumn, configFields, privilegeParam) ->
                AnsiSqlBuilder.buildRetrieveConditionSearchSql(inputFilter, dto.getDiseaseName(), tableInfo, diseaseNameColumn, configFields, privilegeParam));
    }

    @Override
    public PageInfo<SuggestWordVO> getSuggestWords(SuggestSearchDTO dto) {
        try (Page<SuggestWordVO> ignored = PageHelper.startPage(dto.getPageIndex(), dto.getPageSize())) {
            return PageInfo.of(suggestWordMapper.getSuggestWords(dto.getText()));
        }
    }

    /**
     * 根据 数据模型、模型配置检索表、检索表过滤条件以及输入过滤条件 查询数据池结果
     * */
    private <T extends DataSearchCommonQuery> PageInfo<String> getSearchResultBy(T dto, SqlBuilderFunction sqlBuilder) {
        //查询数据模型配置
        TbCdcdmDataFormTemplate template = templateMapper.getModelConfigByModelId(dto.getModelId());
        if(template == null){
            throw new MedicalBusinessException("模型不存在");
        }
        //数据池搜索默认配置检索表
        if(StrUtil.isEmpty(template.getRetrieveTable())){
            throw new MedicalBusinessException("模型未配置检索表");
        }
        Gson gson = new Gson();
        //解析配置字段
        FieldsConfigAnalysisVO analysisVO = gson.fromJson(template.getFieldsConfig(), FieldsConfigAnalysisVO.class);
        if (analysisVO != null) {
            //查看该模型的检索表是否配置了过滤信息 有则需要将过滤条件拼接到sql中
            List<ConfigFieldsDTO> configFields = dataModelCommonUtils.buildConfigFilter(analysisVO.getRetrieveFilter());
            //校验检索表 为空返回
            TbCdcdmMetadataTableInfo tableInfo = tableInfoCache.getTableInfoByCache(template.getRetrieveTable());
            if(tableInfo == null){
                log.error("检索表 [{}] 未配置 metadata ", template.getRetrieveTable());
                return PageInfo.emptyPageInfo();
            }
            TbCdcdmMetadataTableColumnInfo diseaseNameColumn = tableInfoCache.getColumnInfoByName(tableInfo.getTableCode(), "syndrome_name");
            // 先实现区划权限
            PrivilegeParam privilegeParam = CommonUtilService.buildPrivilegeParam(dto);
            // 拼接查询 SQL
            String sql = sqlBuilder.buildSql(tableInfo, diseaseNameColumn, configFields, privilegeParam);
            log.debug("数据池查询SQL: {}", sql);
            PageHelper.startPage(dto.getPageIndex(), dto.getPageSize()).setOrderBy(" etl_update_datetime desc ");  // 临时排序
            List<Map<String, Object>> pageData = monitorCommonUtils.queryByTableSchema(tableInfo.getDataSourceKey(), tableInfo.getSchema(), sql);

            // 非空处理
            if (CollectionUtil.isNotEmpty(pageData)) {
                List<String> desensitizationRes = pageData.stream()
                                                          .map(map -> map.get(Common.CONTENT_JSON))
                                                          .filter(Objects::nonNull)
                                                          .map(Object::toString)
                                                          .map(e -> dataModelCommonUtils.desensitizationJson(dto.getModelId(), e))
                                                          .collect(Collectors.toList());
                return commonUtilService.fromPageInfo(new PageInfo<>(pageData), desensitizationRes);
            }
        }
        return PageInfo.emptyPageInfo();
    }

    // 定义一个函数式接口用于生成 SQL
    @FunctionalInterface
    private interface SqlBuilderFunction {
        String buildSql(TbCdcdmMetadataTableInfo tableInfo,
                        TbCdcdmMetadataTableColumnInfo diseaseNameColumn,
                        List<ConfigFieldsDTO> configFields,
                        PrivilegeParam privilegeParam);
    }
}
