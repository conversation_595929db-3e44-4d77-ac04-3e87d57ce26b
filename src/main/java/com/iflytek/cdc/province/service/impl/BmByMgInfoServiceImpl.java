package com.iflytek.cdc.province.service.impl;

import com.iflytek.cdc.province.mapper.pg.AdsBmMonitorInfoMapper;
import com.iflytek.cdc.province.model.dto.*;
import com.iflytek.cdc.province.service.BmByMgInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class BmByMgInfoServiceImpl implements BmByMgInfoService {

    @Resource
    private AdsBmMonitorInfoMapper adsBmMonitorInfoMapper;

    @Override
    public BmMonitorResultDTO getBmMonitorResultByRelateTaskId(String relateTaskId, String monitorType) {
        BmMonitorResultDTO bmMonitorResultDTO = new BmMonitorResultDTO();
        if (monitorType.equals("1")) {
            List<MiFoOvitrapDTO> miFoOvitrapDTOList = adsBmMonitorInfoMapper.getMiFoOvitrapDTOList(relateTaskId);
            bmMonitorResultDTO.setMiFoOvitrapList(miFoOvitrapDTOList);
        } else if (monitorType.equals("2")) {
            List<MiFoBreteauIndexDTO> miFoBreteauIndexDTOList = adsBmMonitorInfoMapper.getMiFoBreteauIndexDTOList(relateTaskId);
            bmMonitorResultDTO.setMiFoBreteauIndexList(miFoBreteauIndexDTOList);
        } else if (monitorType.equals("3")) {
            List<MiFoDoubleMosquitoDTO> miFoDoubleMosquitoDTOList = adsBmMonitorInfoMapper.getMiFoDoubleMosquitoDTOList(relateTaskId);
            bmMonitorResultDTO.setMiFoDoubleMosquitoList(miFoDoubleMosquitoDTOList);
        }
        return bmMonitorResultDTO;
    }

    @Override
    public ByMonitorResultDTO getByMonitorResultByRelateTaskId(String relateTaskId, String sampleType) {
        ByMonitorResultDTO byMonitorResultDTO = new ByMonitorResultDTO();
        if (sampleType.equals("1")) {
            List<PdPopulationSampleDTO> populationSampleDTOList = adsBmMonitorInfoMapper.getPopulationSampleDTOList(relateTaskId);
            byMonitorResultDTO.setPopulationSampleList(populationSampleDTOList);
        } else if (sampleType.equals("2")) {
            List<PdObjectSampleDTO> objectSampleDTOList = adsBmMonitorInfoMapper.getObjectSampleDTOList(relateTaskId);
            byMonitorResultDTO.setObjectSampleList(objectSampleDTOList);
        } else if (sampleType.equals("3")) {
            List<PdBiologicalSampleDTO> biologicalSampleDTOList = adsBmMonitorInfoMapper.getBiologicalSampleDTOList(relateTaskId);
            byMonitorResultDTO.setBiologicalSampleList(biologicalSampleDTOList);
        } else if (sampleType.equals("4")) {
            List<PdFoodSampleDTO> foodSampleDTOList = adsBmMonitorInfoMapper.getFoodSampleDTOList(relateTaskId);
            byMonitorResultDTO.setFoodSampleList(foodSampleDTOList);
        } else if (sampleType.equals("5")) {
            List<PdEnvironmentalSampleDTO> environmentalSampleDTOList = adsBmMonitorInfoMapper.getEnvironmentalSampleDTOList(relateTaskId);
            byMonitorResultDTO.setEnvironmentalSampleList(environmentalSampleDTOList);
        }
        return byMonitorResultDTO;
    }
}
