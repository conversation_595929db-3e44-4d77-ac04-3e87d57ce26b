package com.iflytek.cdc.province.service;


import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.province.model.brief.ManualBriefRecordVO;
import com.iflytek.cdc.province.model.dto.AdsMsProcessReqDTO;
import com.iflytek.cdc.edr.vo.adsMsProcess.*;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoConditionQueryDTO;
import com.iflytek.cdc.province.model.vo.SyndromeOrgProcessStatsVO;
import org.springframework.http.ResponseEntity;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 诊疗(medical service)-传染病-病程分析-按审核时间-月表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-28
 */
public interface AdsMsProcessStatService {

    /**
     * 监测病例量总览
     */
    AdsMsProcessRespVO processStat(AdsMsProcessReqDTO queryParam, String loginUserName);

    /**
     * 新发传染病病例数统计
     */
    List<AdsMsProcessRespVO> processStatLineChart(AdsMsProcessReqDTO queryParam, String loginUserName);

    /**
     * 法定传染病-发病数死亡数类别统计
     */
    MsInfectTypeStatVO notifiableInfectTypeStat(AdsMsProcessReqDTO queryParam, String loginUserName);

    /**
     * 传染病疾病发病数top10统计
     */
    List<MsInfectNewCntVO> groupInfectNewCntTop10(AdsMsProcessReqDTO queryParam, String loginUserName);


    /**
     *  传染病疾病发病数下降top10统计
     */
    List<MsInfectNewCntVO> groupInfectNewCntDeclineTop10(AdsMsProcessReqDTO queryParam, String loginUserName);


    /**
     * 传染病数量统计
     */
    List<MsInfectCntVO> groupInfectCnt(AdsMsProcessReqDTO queryParam, String loginUserName);

    /**
     * 传染病数量统计
     */
    PageInfo<MsInfectCntVO> groupInfectCntPage(AdsMsProcessReqDTO queryParam, String loginUserName);



    /**
     * 流行强度描述-地区分布-不同行政区疾病特征
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    List<AdsMsProcessRespVO> groupArea(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    /**
     * 流行强度描述-地区分布-不同行政区疾病特征-导出
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    ResponseEntity<byte[]> groupAreaExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 流行强度描述-地区分布-城乡分布特征
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    List<AdsMsProcessRespVO> groupAddrType(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 疫情概况明细导出-法定传染病
     */
    ManualBriefRecordVO groupInfectCntExport(AdsMsProcessReqDTO reqDTO, String loginUserName);

    /**
     * 疫情概况明细导出-法定传染病
     */
    ManualBriefRecordVO groupOtherInfectCntExport(AdsMsProcessReqDTO reqDTO, String loginUserName);

    /**
     * 流行强度描述-地区分布-城乡分布特征-导出
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    ResponseEntity<byte[]> groupAddrTypeExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 传染病传播途径分组统计
     */
    List<MsInfectTransmitTypeCntVO> groupInfectTransmitTypeCnt(AdsMsProcessReqDTO queryParam, String loginUserName);

    /**
     * 传染病传播途径分组统计导出
     */
    byte[] groupInfectTransmitTypeCntExport(AdsMsProcessReqDTO queryParam, String loginUserName);


    /**
     * 不同年龄段疾病特征
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    List<AdsMsProcessRespVO> groupAge(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    /**
     * 不同年龄段疾病特征-导出
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    ResponseEntity<byte[]> groupAgeExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    /**
     * 不同性别疾病特征
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    List<AdsMsProcessRespVO> groupSex(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    /**
     * 不同性别疾病特征-导出
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */

    ResponseEntity<byte[]> groupSexExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    /**
     * 不同职业疾病特征
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    List<AdsMsProcessRespVO> groupJob(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    /**
     * 不同职业疾病特征-导出
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */

    ResponseEntity<byte[]> groupJobExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 查询预计数据
     */
    List<AdsMsProcessRespVO> listNewCntPre(AdsMsProcessReqDTO queryParam, String loginUserName);

    /**
     * 流行强度描述-时间分布-月度
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    List<AdsMsProcessRespVO> monthTimeTrend(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 流行强度描述-时间分布-月度-导出
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    ResponseEntity<byte[]> monthTimeTrendExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    /**
     * 流行强度描述-时间分布-季度
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    List<AdsMsProcessRespVO> quarterTimeTrend(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    /**
     * 流行强度描述-时间分布-季度-导出
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    ResponseEntity<byte[]> quarterTimeTrendExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 流行强度描述-时间分布-年度
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    List<AdsMsProcessRespVO> yearTimeTrend(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    /**
     * 流行强度描述-时间分布-年度-导出
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    ResponseEntity<byte[]> yearTimeTrendExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);


    /**
     * 流行强度描述-时间分布-半年
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    List<AdsMsProcessRespVO> halfYearTimeTrend(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);


    /**
     * 流行强度描述-时间分布-半年-导出
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    ResponseEntity<byte[]> halfYearTimeTrendExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 流行动态变化-总体变化趋势
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    List<AdsMsProcessRespVO> overall(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 流行动态变化-分地区变化趋势
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    Collection<List<AdsMsProcessRespVO>> areaChange(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 流行动态变化-分性别变化趋势
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    Collection<List<AdsMsProcessRespVO>> sexChange(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 分年龄变化趋势
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    Collection<List<AdsMsProcessRespVO>> ageChange(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 各个区域的病例数相关统计
     */
    List<AdsMsProcessRespVO> areaMedDateCount(AdsMsProcessReqDTO reqDTO);

    /**
     * 病情分布分析-诊断类型（确诊类型）
     */
    List<AdsMsProcessRespVO> groupIdentifyClass(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 病情分布分析-病情转归（转归类型）
     */
    List<AdsMsProcessRespVO> groupOutcomeStatus(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 病情分布分析-首发症状
     */
    List<AdsMsProcessRespVO> groupSymptomFirst(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 病情分布分析-既往疾病
     */
    List<AdsMsProcessRespVO> groupHistoryBefore(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 病情分布分析-病原阳性分析（病原检测定性结果）
     */
    EpidemicDistributionRespVO groupPathogenResNominal(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 不同疾病占比分析
     */
    List<AdsMsProcessRespVO> groupDiseaseName(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 不同疾病占比分析-导出
     */
    ResponseEntity<byte[]> groupDiseaseNameExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 流行动态变化-态势感知推演（实时再生数，无缓存）
     */
    String overallAwareness(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 流行动态变化-获取态势感知结果（实时再生数）
     */
    List<ProcessAwarenessRespVO> loadAwarenessResult(AdsMsProcessReqDTO reqDTO, String loginUserId);

    public List<AdsMsProcessRespVO> groupAreaDetail(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    ResponseEntity<byte[]> groupAreaDetailExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    List<AdsMsProcessRespVO> groupAreaDripDetail(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 性别 - 大模型演示加上的接口（大模型演示接口需要加到基线，其他模块不调用）
     */
    List<MsInfectCntVO> groupInfectSexCnt(AdsMsProcessReqDTO queryParam, String loginUserName);

    /**
     * 年龄 - 大模型演示加上的接口（大模型演示接口需要加到基线，其他模块不调用）
     */
    List<MsInfectCntVO> groupInfectAgeCnt(AdsMsProcessReqDTO queryParam, String loginUserName);

    List<AdsMsProcessRespVO> getDiseaseDistributionBox(String loginUserName);

    /**
     *  环比趋势图
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    List<AdsMsProcessRespVO> qOQTrendChart(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    /**
     * 症候群机构病程统计
     */
    PageInfo<SyndromeOrgProcessStatsVO> listSyndromeOrgProcessStats(AdsMsProcessReqDTO queryDTO);

    /**
     * 按病种统计发病数, 发病率, 病死率
     */
    List<MsInfectCntVO> groupInfectDiseaseCnt(AdsMsProcessReqDTO queryParam, String loginUserName);


    /**
     * 按地区统计
     */
    List<AdsMsProcessRespVO> groupInfectAreaCnt(AdsMsProcessReqDTO queryParam, String loginUserName);

    /**
     * 按地区下的医疗机构统计 发病数, 发病率, 病死率
     */
    List<AdsMsProcessRespVO> groupInfectAreaOrgCnt(AdsMsProcessReqDTO queryParam, String loginUserName);


    /**
     *
     */
    byte[] groupInfectAreaCntExport(AdsMsProcessReqDTO queryParam);

    /**
     *
     */
    byte[] groupInfectAreaOrgCntExport(AdsMsProcessReqDTO queryParam);

    /**
     * 时间研判模型
     */
    List<AdsMsProcessRespVO> timeJudgeModelTrend(AdsMsProcessReqDTO reqDTO, String loginUserName);


    /**
     * 高发地区统计
     */
    List<AdsMsProcessRespVO> highPrevalenceAreaTop3(AdsMsProcessReqDTO reqDTO, String loginUserName);

    /**
     *  发病死亡统计
     */
    List<AdsMsProcessRespVO> groupDeath(AdsMsProcessReqDTO reqDTO, String loginUserName);


}
