package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.utils.DateFormatUtil;
import com.iflytek.cdc.edr.vo.adsMsProcess.*;
import com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo;
import com.iflytek.cdc.province.enums.ProcessInfoTypeEnum;
import com.iflytek.cdc.province.enums.WarningTypeEnum;
import com.iflytek.cdc.province.mapper.pg.AdsIntegratedProcessInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsMulProcessCaseInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsMulProcessInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsPdPathogenCheckInfoMapper;
import com.iflytek.cdc.province.model.dto.MsProcessLogQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoConditionQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.dto.PathogenCombinationQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.pathogen.PathogenInfectionSituationVO;
import com.iflytek.cdc.province.model.vo.*;
import com.iflytek.cdc.province.service.ExportTaskService;
import com.iflytek.cdc.province.service.IntegratedProcessInfoService;
import com.iflytek.cdc.province.service.ProcessInfoServiceBase;
import com.iflytek.cdc.province.utils.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class IntegratedProcessInfoServiceImpl implements ProcessInfoServiceBase, IntegratedProcessInfoService {

    @Resource
    private AdsIntegratedProcessInfoMapper adsIntegratedProcessInfoMapper;

    @Resource
    private AdsPdPathogenCheckInfoMapper adsPdPathogenCheckInfoMapper;

    @Resource
    private AdsMulProcessInfoMapper adsMulProcessInfoMapper;

    @Resource
    private ExportTaskService exportTaskService;

    @Resource
    private AdsMulProcessCaseInfoMapper adsMulProcessCaseInfoMapper;

    @Override
    public String getProcessInfoType() {
        return ProcessInfoTypeEnum.INTEGRATED.getCode();
    }

    @Override
    public List<MedCntIndicatorVO> listMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsIntegratedProcessInfoMapper.listIntegratedMedCntIndicator(queryDTO);
    }

    @Override
    public List<MsProcessInfoAddressVO> listAddressByIds(MsProcessSimpleInfoQueryDTO dto) {

        return adsIntegratedProcessInfoMapper.listIntegratedAddressByIds(dto);
    }

    @Override
    public List<MsPatientInfoVO> listPatientInfoByProcessIds(MsProcessSimpleInfoQueryDTO dto) {
        return adsIntegratedProcessInfoMapper.listIntegratedPatientInfo(dto);
    }

    @Override
    public List<MsProcessSimpleInfoVO> listSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsIntegratedProcessInfoMapper.listIntegratedProcessInfo(queryDTO);
    }

    public List<AreaMedCntIndicatorVO> listAreaMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        queryDTO.setEndDate(queryDTO.getEndDate() == null ? null : DateFormatUtil.getTheLastSecondOfOneDay(DateUtils.formatDate(queryDTO.getEndDate())));
        return adsIntegratedProcessInfoMapper.listIntegratedAreaMedCntIndicator(queryDTO);
    }

    @Override
    public List<AreaMedCntIndicatorVO> listAreaOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        queryDTO.setEndDate(queryDTO.getEndDate() == null ? null : DateFormatUtil.getTheLastSecondOfOneDay(DateUtils.formatDate(queryDTO.getEndDate())));
        return adsIntegratedProcessInfoMapper.listIntegrateAreaOutcomeCntIndicator(queryDTO);
    }

    @Override
    public List<MedCntIndicatorVO> listOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        return Collections.emptyList();
    }

    @Override
    public PageInfo<MsProcessSimpleInfoVO> simpleInfoPageList(MsProcessSimpleInfoQueryDTO queryDTO) {
        queryDTO.setEndDate(queryDTO.getEndDate() == null ? null : DateFormatUtil.getTheLastSecondOfOneDay(DateUtils.formatDate(queryDTO.getEndDate())));
        try (Page<?> ignored = PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize())) {
            List<MsProcessSimpleInfoVO> processInfoList = adsIntegratedProcessInfoMapper.listIntegratedProcessInfo(queryDTO);
            return new PageInfo<>(processInfoList);
        }
    }

    @Override
    public MsProcessSimpleInfoDetailVO loadProcessSimpleById(String id) {
        return null;
    }

    @Override
    public TbCdcmrExportTask exportSimpleInfo(MsProcessSimpleInfoQueryDTO dto) {
        dto.setEndDate(dto.getEndDate() == null ? null : DateFormatUtil.getTheLastSecondOfOneDay(DateUtils.formatDate(dto.getEndDate())));
        ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                dto.getTaskName(),
                dto.getTaskUrl(),
                null,
                dto.getModuleType(),
                dto.getExportType(),
                dto.getExportName());

        return exportTaskService.addAndUploadFile(dto,
                () -> adsIntegratedProcessInfoMapper.listIntegratedProcessInfo(dto),
                () -> Optional.ofNullable(adsIntegratedProcessInfoMapper.countIntegratedProcess(dto)).orElse(0),
                taskDTO,
                MultichannelProcessSimpleInfoExcelVO.class,
                MultichannelProcessSimpleInfoExcelVO::of);
    }

    @Override
    public Object listProcessModelSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {
        return null;
    }

    @Override
    public List<MsProcessLogVO> listProcessLog(MsProcessLogQueryDTO queryDTO) {
        return null;
    }

    @Override
    public List<TbCdcewEmergencyEventProcessInfo> getProcessInfoBy(List<String> ids) {

        return adsMulProcessInfoMapper.getMultichannelProcessInfoBy(ids);
    }

    @Override
    public ProcessPathogenInfoVO getProcessPathogenInfoBy(List<String> ids) {
        return null;
    }

    @Override
    public PageInfo<MsProcessSimpleInfoVO> syndromeSimpleInfoPageListByConditions(MsProcessSimpleInfoConditionQueryDTO queryDTO) {
        return null;
    }

    public List<MedCntIndicatorVO> listIntegratedMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsIntegratedProcessInfoMapper.listIntegratedMedCntIndicator(queryDTO);
    }

    @Override
    public List<PathogenCheckVO> listIntegratedPathogenCheckResult(PathogenCombinationQueryDTO dto) {

        return adsMulProcessCaseInfoMapper.listPathogenCheckResult(dto);
    }

    @Override
    public List<PathogenCheckVO> listIntegratedPositivePathogenCheckResult(PathogenCombinationQueryDTO dto) {

        if(CollectionUtil.isEmpty(dto.getSourceKeyList())){
            return new ArrayList<>();
        }
        dto.setWarningType(WarningTypeEnum.INTEGRATED.getCode());
        return adsPdPathogenCheckInfoMapper.listPositivePathogenCheckResult(dto);
    }

    @Override
    public List<PathogenInfectionSituationVO> listIntegratedPathogenInfectionSituation(PathogenCombinationQueryDTO dto) {

        return adsPdPathogenCheckInfoMapper.getPathogenInfectionSituation(dto);
    }

}
