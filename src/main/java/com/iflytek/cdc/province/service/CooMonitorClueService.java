package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ClueDrugSalesQueryDto;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ClueMonitorQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ClueIndexQueryDto;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.DrugDailySalesVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorClueInfoVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.AdsPoIndexInfoVO;

import java.util.List;

public interface CooMonitorClueService {


    List<String> listClueSources();

    PageInfo<MonitorClueInfoVO> clueSearch(ClueMonitorQueryDTO queryDTO);

    List<DrugDailySalesVO> drugSalesDetail(ClueDrugSalesQueryDto queryDTO);

    AdsPoIndexInfoVO searchIndexDetail(ClueIndexQueryDto queryDTO);

    AdsPoIndexInfoVO consultIndexDetail(ClueIndexQueryDto queryDTO);
}
