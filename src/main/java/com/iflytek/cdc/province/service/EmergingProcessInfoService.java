package com.iflytek.cdc.province.service;

import com.iflytek.cdc.province.model.dto.PathogenCombinationQueryDTO;
import com.iflytek.cdc.province.model.pathogen.PathogenInfectionSituationVO;
import com.iflytek.cdc.province.model.vo.PathogenCheckVO;

import java.util.List;

public interface EmergingProcessInfoService {

    /**
     * 查询新发突发病例病原检测结果
     * */
    List<PathogenCheckVO> listEmergingPathogenCheckResult(PathogenCombinationQueryDTO dto);

    /**
     * 查询新发突发病例阳性病原检测
     * */
    List<PathogenCheckVO> listEmergingPositivePathogenCheckResult(PathogenCombinationQueryDTO dto);

    /**
     * 查询新发突发病例病原感染情况
     * */
    List<PathogenInfectionSituationVO> listEmergingPathogenInfectionSituation(PathogenCombinationQueryDTO dto);

}
