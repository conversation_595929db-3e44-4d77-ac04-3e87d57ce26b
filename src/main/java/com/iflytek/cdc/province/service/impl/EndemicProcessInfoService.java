package com.iflytek.cdc.province.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoDetailVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO;
import com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo;
import com.iflytek.cdc.province.enums.ProcessInfoTypeEnum;
import com.iflytek.cdc.province.mapper.pg.EndemicProcessInfoMapper;
import com.iflytek.cdc.province.model.dto.MsProcessLogQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoConditionQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.vo.*;
import com.iflytek.cdc.province.service.ExportTaskService;
import com.iflytek.cdc.province.service.ProcessInfoServiceBase;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class EndemicProcessInfoService implements ProcessInfoServiceBase {
    
    @Resource
    private EndemicProcessInfoMapper endemicProcessInfoMapper;

    @Resource
    private ExportTaskService exportTaskService;

    @Override
    public String getProcessInfoType() {
        
        return ProcessInfoTypeEnum.ENDEMIC.getCode();
    }

    @Override
    public List<MedCntIndicatorVO> listMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {

        return endemicProcessInfoMapper.listMedCntIndicator(queryDTO);
    }

    @Override
    public List<MedCntIndicatorVO> listOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {

        return endemicProcessInfoMapper.listOutcomeCntIndicator(queryDTO);
    }

    @Override
    public List<AreaMedCntIndicatorVO> listAreaMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {

        return endemicProcessInfoMapper.listAreaMedCntIndicator(queryDTO);
    }

    @Override
    public List<AreaMedCntIndicatorVO> listAreaOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {

        return endemicProcessInfoMapper.listAreaOutcomeCntIndicator(queryDTO);
    }

    @Override
    public List<MsProcessInfoAddressVO> listAddressByIds(MsProcessSimpleInfoQueryDTO dto) {

        return endemicProcessInfoMapper.listAddressByIds(dto);
    }

    @Override
    public PageInfo<MsProcessSimpleInfoVO> simpleInfoPageList(MsProcessSimpleInfoQueryDTO queryDTO) {

        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return new PageInfo<>(endemicProcessInfoMapper.listSimpleInfo(queryDTO));
    }

    @Override
    public MsProcessSimpleInfoDetailVO loadProcessSimpleById(String id) {

        return endemicProcessInfoMapper.loadProcessSimpleById(id);
    }

    @Override
    public TbCdcmrExportTask exportSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {

        ExportTaskDTO taskDTO = ExportTaskDTO.of(queryDTO.getExportTaskId(),
                                                 queryDTO.getTaskName(),
                                                 queryDTO.getTaskUrl(),
                                                 null,
                                                 queryDTO.getModuleType(),
                                                 queryDTO.getExportType(),
                                                 queryDTO.getExportName());

        return exportTaskService.addAndUploadFile(queryDTO,
                                                  () -> endemicProcessInfoMapper.listSimpleInfo(queryDTO),
                                                  () -> Optional.ofNullable(endemicProcessInfoMapper.countEmerging(queryDTO)).orElse(0),
                                                  taskDTO,
                                                  MsEndemicProcessSimpleInfoExcelVO.class,
                                                  MsEndemicProcessSimpleInfoExcelVO::of);
    }

    @Override
    public List<MsPatientInfoVO> listPatientInfoByProcessIds(MsProcessSimpleInfoQueryDTO dto) {

        return endemicProcessInfoMapper.listPatientInfoByProcessIds(dto);
    }

    @Override
    public List<MsProcessSimpleInfoVO> listSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {

        return endemicProcessInfoMapper.listSimpleInfo(queryDTO);
    }

    @Override
    public Object listProcessModelSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {

        return endemicProcessInfoMapper.listProcessModelSimpleInfo(queryDTO);
    }

    @Override
    public List<MsProcessLogVO> listProcessLog(MsProcessLogQueryDTO queryDTO) {

        return endemicProcessInfoMapper.listProcessLog(queryDTO);
    }

    @Override
    public List<TbCdcewEmergencyEventProcessInfo> getProcessInfoBy(List<String> ids) {

        return endemicProcessInfoMapper.getProcessInfoBy(ids);
    }

    @Override
    public ProcessPathogenInfoVO getProcessPathogenInfoBy(List<String> ids) {

        return endemicProcessInfoMapper.getProcessPathogenInfoBy(ids);
    }

    @Override
    public PageInfo<MsProcessSimpleInfoVO> syndromeSimpleInfoPageListByConditions(MsProcessSimpleInfoConditionQueryDTO queryDTO) {

        return new PageInfo<>(new ArrayList<>());
    }

}
