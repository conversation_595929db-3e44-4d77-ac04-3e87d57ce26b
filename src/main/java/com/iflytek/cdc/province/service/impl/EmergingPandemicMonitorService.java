package com.iflytek.cdc.province.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.iflytek.cdc.province.mapper.pg.AdsMsProcessInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsMulProcessCaseInfoMapper;
import org.springframework.stereotype.Service;

import com.iflytek.cdc.edr.dto.workbench.UserTodoListDTO;
import com.iflytek.cdc.edr.vo.dm.ProcessRecordVO;
import com.iflytek.cdc.province.model.pandemic.dto.MedicalQueryDTO;
import com.iflytek.cdc.province.service.IPandemicMonitorService;

import javax.annotation.Resource;

@Service("emergingMonitorCategory")
public class EmergingPandemicMonitorService implements IPandemicMonitorService {

    @Resource
    private AdsMsProcessInfoMapper adsMsProcessInfoMapper;

    @Resource
    private AdsMulProcessCaseInfoMapper adsMulProcessCaseInfoMapper;

    @Override
    public List<ProcessRecordVO> getMedicalList(MedicalQueryDTO dto) {

        return adsMulProcessCaseInfoMapper.getMedicalList(dto);
    }

    @Override
    public List<UserTodoListDTO> getMedicalListToDo(MedicalQueryDTO dto) {
        return new ArrayList<>();
    }

}
