package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.iflytek.cdc.province.service.PredictionService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SimplePredictionServiceImpl implements PredictionService {

    @Override
    public List<Double> predict(List<Double> inputData, Integer predictionDays) {
        if (CollectionUtil.isEmpty(inputData)) {
            return new ArrayList<>();
        }

        // 设置移动平均的窗口大小，可以根据需要调整
        int windowSize = Math.min(3, inputData.size()); // 默认使用3天移动平均

        List<Double> predictions = new ArrayList<>();
        List<Double> movingWindow = new ArrayList<>();

        // 初始化移动窗口，取最后windowSize个数据点
        for (int i = Math.max(0, inputData.size() - windowSize); i < inputData.size(); i++) {
            movingWindow.add(inputData.get(i));
        }

        // 预测未来predictionDays天的值
        for (int i = 0; i < predictionDays; i++) {
            // 计算移动平均值作为预测值
            double prediction = calculateMovingAverage(movingWindow);
            predictions.add(prediction);

            // 更新移动窗口：移除最旧的值，添加新的预测值
            if (movingWindow.size() >= windowSize) {
                movingWindow.remove(0);
            }
            movingWindow.add(prediction);
        }

        return predictions;
    }

    /**
     * 计算移动窗口内数据的平均值
     */
    private double calculateMovingAverage(List<Double> window) {
        if (CollectionUtil.isEmpty(window)) {
            return 0.0;
        }

        double sum = 0.0;
        for (Double value : window) {
            sum += value;
        }

        return sum / window.size();
    }

    public static void main(String[] args) {
        SimplePredictionServiceImpl service = new SimplePredictionServiceImpl();

        ArrayList<Double> list = new ArrayList<>();
        list.add(4.0);
        list.add(3.0);
        list.add(1.0);
//        list.add(2.0);
//        list.add(3.0);

        List<Double> predictions = service.predict(list, 3);
        System.out.println("Predictions: " + predictions);
    }
}