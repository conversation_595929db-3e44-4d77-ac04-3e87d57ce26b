package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.apiService.UapServiceApi;
import com.iflytek.cdc.edr.utils.DimDateUtils;
import com.iflytek.cdc.edr.utils.FileUtils;
import com.iflytek.cdc.province.enums.PathogenProcessTypeEnum;
import com.iflytek.cdc.province.mapper.pg.AdsPdPathogenMultiStatMapper;
import com.iflytek.cdc.province.mapper.pg.AdsPdPathogenStatMapper;
import com.iflytek.cdc.province.mapper.pg.AdsPdPathogenTwofoldStatMapper;
import com.iflytek.cdc.province.model.dto.DateDimQueryParam;
import com.iflytek.cdc.province.model.pathogen.PathogenAnalysisReqDTO;
import com.iflytek.cdc.province.model.pathogen.PathogenMultiClassStatExcelVO;
import com.iflytek.cdc.province.model.pathogen.PathogenMultiClassStatVO;
import com.iflytek.cdc.province.model.pathogen.PathogenNameGroupVO;
import com.iflytek.cdc.province.model.pathogen.PathogenTwofoldStatVO;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.service.PathogenAnalysisService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 病原相关分析统计接口实现类
 */
@Service
public class PathogenAnalysisServiceImpl implements PathogenAnalysisService {

    @Resource
    private AdsPdPathogenStatMapper pathogenStatMapper;

    @Resource
    private AdsPdPathogenTwofoldStatMapper pathogenTwofoldStatMapper;

    @Resource
    private AdsPdPathogenMultiStatMapper pathogenMultiStatMapper;

    @Resource
    private CdcAdminServiceApi cdcAdminServiceApi;

    @Resource
    private UapServiceApi uapServiceApi;

    @Resource
    private DimDateUtils dimDateUtils;

    @Override
    public List<PathogenNameGroupVO> groupPositiveByName(PathogenAnalysisReqDTO req, String loginUserName) {
        setQueryParamInfoAreaMultiChoose(req, loginUserName);
        // 按时间维度分表
        req.setDateDims(dimDateUtils.getDateDims(req.getDateDimType(), req.getStartDate(), req.getEndDate()));
        req.dealDate(req);
        List<PathogenNameGroupVO> vos = pathogenStatMapper.groupPositiveByName(req);
        for (PathogenNameGroupVO vo : vos) {
            vo.calculateAllRates();
        }
        return vos;
    }

    @Override
    public List<PathogenTwofoldStatVO> groupTwofoldByName(PathogenAnalysisReqDTO req, String loginUserName) {
        setQueryParamInfoMultiChoose(req, loginUserName);
        List<PathogenTwofoldStatVO> vos = pathogenTwofoldStatMapper.groupTwofoldByName(req);
        for (PathogenTwofoldStatVO vo : vos) {
            vo.calculateAllRates();
        }
        return vos;
    }

    @Override
    public List<PathogenMultiClassStatVO> groupMultiClassByName(PathogenAnalysisReqDTO req, String loginUserName) {
        setQueryParamInfoMultiChoose(req, loginUserName);
        List<PathogenMultiClassStatVO> vos = pathogenMultiStatMapper.groupMultiClassByName(req);
        for (PathogenMultiClassStatVO vo : vos) {
            vo.calculateTotal();
        }
        return vos.stream().sorted((a, b) -> b.getTotalPositiveCnt().compareTo(a.getTotalPositiveCnt()))
                .collect(Collectors.toList());
    }

    @Override
    public ResponseEntity<byte[]> groupMultiClassByNameExport(PathogenAnalysisReqDTO req, String loginUserName) {
        List<PathogenMultiClassStatVO> vos = groupMultiClassByName(req, loginUserName);
        // 将病原体多重检测统计数据转换为Excel导出格式
        HttpHeaders httpHeaders = FileUtils.getHttpHeaders("症候群病原感染情况.xlsx");
        ByteArrayOutputStream excel = new ByteArrayOutputStream();
        EasyExcel.write(excel, PathogenMultiClassStatExcelVO.class).sheet()
                .doWrite(vos.stream().map(PathogenMultiClassStatExcelVO::of).collect(Collectors.toList()));
        return new ResponseEntity<>(excel.toByteArray(), httpHeaders, HttpStatus.CREATED);
    }

    /**
     * 设置查询参数信息（区划信息和疾病编码）
     */
    private void setQueryParamInfo(PathogenAnalysisReqDTO queryParam, String loginUserName) {
        // 区域信息+数据权限融合
        setAreaInfo(queryParam, loginUserName);
        // 疾病编码
        if (StrUtil.isNotBlank(queryParam.getDiseaseCode())) {
            return;
        }
        if (StrUtil.equals(PathogenProcessTypeEnum.SYNDROME.getCode(), queryParam.getProcessType())) {
            List<String> diseaseCodeList = cdcAdminServiceApi.getSyndromeDiseaseCodeByName(queryParam.getDiseaseName());
            queryParam.setDiseaseCode(CollUtil.isEmpty(diseaseCodeList) ? null : diseaseCodeList.get(0));
        }
    }

    private void setQueryParamInfoMultiChoose(PathogenAnalysisReqDTO queryParam, String loginUserName) {
        // 区域信息+数据权限融合
        setAreaInfoList(queryParam, loginUserName);
        // 疾病编码
        if (StrUtil.isNotBlank(queryParam.getDiseaseCode())) {
            return;
        }
        if (StrUtil.equals(PathogenProcessTypeEnum.SYNDROME.getCode(), queryParam.getProcessType())) {
            List<String> diseaseCodeList = cdcAdminServiceApi.getSyndromeDiseaseCodeByName(queryParam.getDiseaseName());
            queryParam.setDiseaseCode(CollUtil.isEmpty(diseaseCodeList) ? null : diseaseCodeList.get(0));
        }
    }


    /**
     * 设置查询参数信息（区划信息和疾病编码）
     */
    private void setQueryParamInfoAreaMultiChoose(PathogenAnalysisReqDTO queryParam, String loginUserName) {
        // 区域信息+数据权限融合
        setAreaInfoList(queryParam, loginUserName);
        // 疾病编码
        if (StrUtil.isNotBlank(queryParam.getDiseaseCode())) {
            return;
        }
        if (StrUtil.equals(PathogenProcessTypeEnum.SYNDROME.getCode(), queryParam.getProcessType())) {
            List<String> diseaseCodeList = cdcAdminServiceApi.getSyndromeDiseaseCodeByName(queryParam.getDiseaseName());
            queryParam.setDiseaseCode(CollUtil.isEmpty(diseaseCodeList) ? null : diseaseCodeList.get(0));
        }
    }

    private void setAreaInfoList(DateDimQueryParam queryParam, String loginUserName) {
        uapServiceApi.setProvinceAndCityAndDistrict(loginUserName, queryParam,
                DateDimQueryParam::setProvinceCodes,
                DateDimQueryParam::setCityCodes,
                DateDimQueryParam::setDistrictCodes,
                (d, areaLevel) -> {
                    if (d.getAreaLevel() == null) {
                        d.setAreaLevel(areaLevel);
                    }
                });
        // 融合权限和过滤条件
        if (CollectionUtil.isNotEmpty(queryParam.getDistrictCodeList())) {
            if (CollUtil.isEmpty(queryParam.getDistrictCodes())) {
                queryParam.setDistrictCodes(queryParam.getDistrictCodeList());
            } else {
                queryParam.setDistrictCodes(CommonUtilService.intersection(
                        queryParam.getDistrictCodeList(), queryParam.getDistrictCodes()));
            }
        }
        if (CollectionUtil.isNotEmpty(queryParam.getCityCodeList())) {
            if (CollUtil.isEmpty(queryParam.getCityCodes())) {
                queryParam.setCityCodes(queryParam.getCityCodeList());
            } else {
                queryParam.setCityCodes(
                        CommonUtilService.intersection(queryParam.getCityCodeList(),
                                queryParam.getCityCodes()));
            }
        }
    }

    private void setAreaInfo(DateDimQueryParam queryParam, String loginUserName) {
        uapServiceApi.setProvinceAndCityAndDistrict(loginUserName, queryParam,
                DateDimQueryParam::setProvinceCodes,
                DateDimQueryParam::setCityCodes,
                DateDimQueryParam::setDistrictCodes,
                (d, areaLevel) -> {
                    if (d.getAreaLevel() == null) {
                        d.setAreaLevel(areaLevel);
                    }
                });
        // 融合权限和过滤条件
        if (StrUtil.isNotBlank(queryParam.getDistrictCode())) {
            if (CollUtil.isEmpty(queryParam.getDistrictCodes())) {
                queryParam.setDistrictCodes(Collections.singletonList(queryParam.getDistrictCode()));
            } else {
                queryParam.setDistrictCodes(CommonUtilService.intersection(
                        Collections.singletonList(queryParam.getDistrictCode()), queryParam.getDistrictCodes()));
            }
        }
        if (StrUtil.isNotBlank(queryParam.getCityCode())) {
            if (CollUtil.isEmpty(queryParam.getCityCodes())) {
                queryParam.setCityCodes(Collections.singletonList(queryParam.getCityCode()));
            } else {
                queryParam.setCityCodes(
                        CommonUtilService.intersection(Collections.singletonList(queryParam.getCityCode()),
                                queryParam.getCityCodes()));
            }
        }
    }
}
