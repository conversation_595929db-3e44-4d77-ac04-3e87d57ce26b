package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.province.entity.ads.DyqwEvent;
import com.iflytek.cdc.province.model.dto.DyqwEventDTO;

import java.util.List;
import java.util.Map;

public interface EmergencyEventService {

     DyqwEvent getEventByEventId(String id);

     PageInfo<DyqwEvent> getEmergencyEvents(DyqwEventDTO dto);

     Map<String, List<String>> getDownBox();

}
