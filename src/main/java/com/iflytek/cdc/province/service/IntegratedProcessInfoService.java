package com.iflytek.cdc.province.service;

import com.iflytek.cdc.province.model.dto.PathogenCombinationQueryDTO;
import com.iflytek.cdc.province.model.pathogen.PathogenInfectionSituationVO;
import com.iflytek.cdc.province.model.vo.PathogenCheckVO;

import java.util.List;

public interface IntegratedProcessInfoService {

    /**
     * 查询综合预警病例病原检测结果
     * */
    List<PathogenCheckVO> listIntegratedPathogenCheckResult(PathogenCombinationQueryDTO dto);

    /**
     * 查询综合预警病例阳性病原检测
     * */
    List<PathogenCheckVO> listIntegratedPositivePathogenCheckResult(PathogenCombinationQueryDTO dto);

    /**
     * 查询综合预警病例病原感染情况
     * */
    List<PathogenInfectionSituationVO> listIntegratedPathogenInfectionSituation(PathogenCombinationQueryDTO dto);

}
