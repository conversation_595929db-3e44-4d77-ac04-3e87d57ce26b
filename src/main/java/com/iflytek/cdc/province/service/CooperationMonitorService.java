package com.iflytek.cdc.province.service;

import com.iflytek.cdc.province.model.multichannelMonitor.dto.CollaborMonitorQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.DrugSalesVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.IncidentCaseNoticeVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorKeywordVO;

import java.util.List;

public interface CooperationMonitorService {

    /**
     * 销售同比增长排行
     * */
    List<DrugSalesVO> listSalesGrowthRanking(CollaborMonitorQueryDTO queryDTO);

    /**
     * 监测关键词搜索指数情况
     * */
    List<MonitorKeywordVO> listSearchIndexSituation(CollaborMonitorQueryDTO queryDTO);


    /**
     * 监测关键词上榜情况
     * */
    List<MonitorKeywordVO> listKeywordSituation(CollaborMonitorQueryDTO queryDTO);

    List<MonitorKeywordVO> searchKeywordSituation(CollaborMonitorQueryDTO queryDTO);

    /**
     * 输入病例公告情况
     * */
    List<IncidentCaseNoticeVO> listIncidentCaseNotice(CollaborMonitorQueryDTO queryDTO);

    List<DrugSalesVO> listIntegratedSalesGrowthRanking(CollaborMonitorQueryDTO queryDTO);

    List<MonitorKeywordVO> listIntegratedSearchIndexSituation(CollaborMonitorQueryDTO queryDTO);
}
