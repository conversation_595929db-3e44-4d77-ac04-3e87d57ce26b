package com.iflytek.cdc.province.service.impl;

import com.iflytek.cdc.province.entity.bu.TbCoronavirusSampleCollection;
import com.iflytek.cdc.province.mapper.bu.TbCoronavirusSampleCollectionMapper;
import com.iflytek.cdc.province.model.dto.CommonQuery;
import com.iflytek.cdc.province.model.dto.CoronavirusSampleQueryDTO;
import com.iflytek.cdc.province.model.vo.CoronavirusSampleCollectionVO;
import com.iflytek.cdc.province.service.CoronavirusSampleCollectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CoronavirusSampleCollectionServiceImpl implements CoronavirusSampleCollectionService {


    @Resource
    private TbCoronavirusSampleCollectionMapper coronavirusSampleCollectionMapper;



    @Override
    public List<TbCoronavirusSampleCollection> listAll(CoronavirusSampleQueryDTO queryDTO) {
        if (queryDTO.getSampleStartDate() == null || queryDTO.getSampleEndDate() == null) {
            queryDTO.setSampleStartDate(queryDTO.getStartDate());
            queryDTO.setSampleEndDate(queryDTO.getEndDate());
        }
        return coronavirusSampleCollectionMapper.selectAllList(queryDTO);
    }

    @Override
    public List<CoronavirusSampleCollectionVO> groupSubtype() {
        List<CoronavirusSampleCollectionVO> voList = coronavirusSampleCollectionMapper.groupSubtype();
        Integer total = coronavirusSampleCollectionMapper.countTotal();
        return voList.stream().peek(vo -> vo.setRate((double) vo.getCount() / total)).collect(Collectors.toList());
    }
}
