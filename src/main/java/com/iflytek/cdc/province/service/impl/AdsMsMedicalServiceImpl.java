package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.apiService.UapServiceApi;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import com.iflytek.cdc.edr.enums.DateDimEnum;
import com.iflytek.cdc.edr.utils.DateFormatUtil;
import com.iflytek.cdc.edr.vo.DiIndataStatVO;
import com.iflytek.cdc.province.entity.ads.AdsMsMedicalInfo;
import com.iflytek.cdc.province.mapper.pg.AdsMsMedicalInfoMapper;
import com.iflytek.cdc.province.model.dto.FieldOperation;
import com.iflytek.cdc.province.model.dto.MsMedicalQuery;
import com.iflytek.cdc.province.mapper.pg.AdsMsMedicalMapper;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.vo.VisitPersonInfoVO;
import com.iflytek.cdc.province.service.AdsMsMedicalService;
import com.iflytek.cdc.edr.utils.DataUtils;
import com.iflytek.cdc.edr.utils.DimDateUtils;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsMedicalVisitStatVO;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.service.ExportTaskService;
import com.iflytek.cdc.province.service.MonitorCommonUtils;
import com.iflytek.cdc.province.utils.ExcelUtils;
import com.iflytek.cdc.province.utils.StatUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

@Service
@Configuration
@ConditionalOnProperty(value = "dataService.version", havingValue = "v2")
public class AdsMsMedicalServiceImpl implements AdsMsMedicalService {
    @Resource
    private AdsMsMedicalMapper adsMsMedicalMapper;
    @Resource
    private DataUtils dataUtils;
    @Resource
    private DimDateUtils dimDateUtils;
    @Resource
    private UapServiceApi uapServiceApi;

    @Resource
    private CommonUtilService commonUtilService;

    @Resource
    private AdsMsMedicalInfoMapper adsMsMedicalInfoMapper;

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Resource
    private ExportTaskService exportTaskService;

    @Resource
    private MonitorCommonUtils monitorCommonUtils;

    @Override
    public MsMedicalVisitStatVO medicalVisitStat(MsMedicalQuery query, String loginUserName) {

        setAreaInfoList(query, loginUserName);
        // 创建FieldOperation列表
        List<FieldOperation<MsMedicalVisitStatVO>> operations = new ArrayList<>();
        operations.add(FieldOperation.<MsMedicalVisitStatVO>builder().getter(MsMedicalVisitStatVO::getMedicalCnt).yoySetter(MsMedicalVisitStatVO::setMedicalCntYearGrowth).chainSetter(MsMedicalVisitStatVO::setMedicalCntChain).build());
        operations.add(FieldOperation.<MsMedicalVisitStatVO>builder().getter(MsMedicalVisitStatVO::getMedicalInpatCnt).yoySetter(MsMedicalVisitStatVO::setMedicalInpatCntYearGrowth).chainSetter(MsMedicalVisitStatVO::setMedicalInpatCntChain).build());
        operations.add(FieldOperation.<MsMedicalVisitStatVO>builder().getter(MsMedicalVisitStatVO::getMedicalOutpatCnt).yoySetter(MsMedicalVisitStatVO::setMedicalOutpatCntYearGrowth).chainSetter(MsMedicalVisitStatVO::setMedicalOutpatCntChain).build());
        operations.add(FieldOperation.<MsMedicalVisitStatVO>builder().getter(MsMedicalVisitStatVO::getMedicalOutpatCntBowel).yoySetter(MsMedicalVisitStatVO::setMedicalOutpatCntBowelYearGrowth).chainSetter(MsMedicalVisitStatVO::setMedicalOutpatCntBowelChain).build());
        operations.add(FieldOperation.<MsMedicalVisitStatVO>builder().getter(MsMedicalVisitStatVO::getMedicalOutpatCntHeat).yoySetter(MsMedicalVisitStatVO::setMedicalOutpatCntHeatYearGrowth).chainSetter(MsMedicalVisitStatVO::setMedicalOutpatCntHeatChain).build());
        return StatUtils.comparisonResult(query, adsMsMedicalMapper::medicalVisitStat, operations);
    }

    @Override
    public List<MsMedicalVisitStatVO> visitStatLineChart(MsMedicalQuery queryParam, String loginUserName) {

        setAreaInfoList(queryParam, loginUserName);
        if (queryParam.getStartDate() != null && queryParam.getEndDate() != null) {
            queryParam.setStartDate(DateFormatUtil.getTheFistSecondOfOneDay(queryParam.getStartDate()));
            queryParam.setEndDate(DateFormatUtil.getTheLastSecondOfOneDay(queryParam.getEndDate()));
        }
        DateDimEnum dateDimEnum = DateDimEnum.getByCode(queryParam.getDateDimType());
        if(dateDimEnum == null){
            dateDimEnum = DateDimEnum.DAY;
        }
        return StatUtils.fulfillAndConvertDayDateData(
                StatUtils.fulfillPeriodValueByDay(queryParam, q -> adsMsMedicalMapper.dataStat(q), MsMedicalVisitStatVO::getStatDate, MsMedicalVisitStatVO.currValueAndLastValueMappingList()),
                queryParam.getStartDate(),
                queryParam.getEndDate(),
                dateDimEnum,
                MsMedicalVisitStatVO::getStatDate,
                MsMedicalVisitStatVO::setStatDate,
                MsMedicalVisitStatVO.dataConverter);
    }

    @Override
    public List<AdsMsMedicalInfo> listMsMedicalInfo(MsMedicalQuery queryParam) {
        return adsMsMedicalMapper.listMsMedicalInfo(queryParam);
    }

    /**
     * 设置区域信息
     * TODO: 和 CommonUtilService.setAreaQueryDTO 方法重复，可以优化
     */
    private void setAreaInfo(MsMedicalQuery queryDTO, String loginUserName){
        uapServiceApi.setProvinceAndCityAndDistrict(loginUserName, queryDTO,
                MsMedicalQuery::setProvinceCodes,
                MsMedicalQuery::setCityCodes,
                MsMedicalQuery::setDistrictCodes,
                (d, areaLevel) -> {
                    if (d.getAreaLevel() == null){
                        d.setAreaLevel(areaLevel);
                    }
                });
        // 融合权限和过滤条件
        if (StrUtil.isNotBlank(queryDTO.getDistrictCode())) {
            if (CollUtil.isEmpty(queryDTO.getDistrictCodes())) {
                queryDTO.setDistrictCodes(Collections.singletonList(queryDTO.getDistrictCode()));
            } else {
                queryDTO.setDistrictCodes(CommonUtilService.intersection(Collections.singletonList(queryDTO.getDistrictCode()), queryDTO.getDistrictCodes()));
            }
        }
        if (StrUtil.isNotBlank(queryDTO.getCityCode())) {
            if (CollUtil.isEmpty(queryDTO.getCityCodes())) {
                queryDTO.setCityCodes(Collections.singletonList(queryDTO.getCityCode()));
            } else {
                queryDTO.setCityCodes(CommonUtilService.intersection(Collections.singletonList(queryDTO.getCityCode()), queryDTO.getCityCodes()));
            }
        }
    }

    /**
     * 设置区域信息
     * TODO: 和 CommonUtilService.setAreaQueryDTO 方法重复，可以优化
     */
    private void setAreaInfoList(MsMedicalQuery queryDTO, String loginUserName){
        uapServiceApi.setProvinceAndCityAndDistrict(loginUserName, queryDTO,
                MsMedicalQuery::setProvinceCodes,
                MsMedicalQuery::setCityCodes,
                MsMedicalQuery::setDistrictCodes,
                (d, areaLevel) -> {
                    if (d.getAreaLevel() == null){
                        d.setAreaLevel(areaLevel);
                    }
                });
        // 融合权限和过滤条件
        if (CollectionUtil.isNotEmpty(queryDTO.getDistrictCodeList())) {
            if (CollUtil.isEmpty(queryDTO.getDistrictCodes())) {
                queryDTO.setDistrictCodes(queryDTO.getDistrictCodeList());
            } else {
                queryDTO.setDistrictCodes(CommonUtilService.intersection(queryDTO.getDistrictCodeList(), queryDTO.getDistrictCodes()));
            }
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getCityCodeList())) {
            if (CollUtil.isEmpty(queryDTO.getCityCodes())) {
                queryDTO.setCityCodes(queryDTO.getCityCodeList());
            } else {
                queryDTO.setCityCodes(CommonUtilService.intersection(queryDTO.getCityCodeList(), queryDTO.getCityCodes()));
            }
        }
    }

    @Override
    public PageInfo<VisitPersonInfoVO> visitPersonDetail(MsMedicalQuery query) {

        CommonUtilService.setAreaQueryDTO(query);
        if (query.getStartDate() != null && query.getEndDate() != null) {
            query.setStartDate(DateFormatUtil.getTheFistSecondOfOneDay(query.getStartDate()));
            query.setEndDate(DateFormatUtil.getTheLastSecondOfOneDay(query.getEndDate()));
        }
        PageHelper.startPage(query.getPageIndex(), query.getPageSize());
        return new PageInfo<>(adsMsMedicalInfoMapper.getVisitPersonDetailBy(query));
    }

    @Override
    public TbCdcmrExportTask visitPersonDetailExport(MsMedicalQuery query) {

        UapUserPo uapUserPo = userInfo.get();
        String taskParams = JSONObject.toJSONString(query);
        ExportTaskDTO taskDTO = ExportTaskDTO.of(query.getExportTaskId(),
                                                 query.getTaskName(),
                                                 query.getTaskUrl(),
                                                 taskParams,
                                                 query.getModuleType(),
                                                 query.getExportType());

        TbCdcmrExportTask existTask = adminServiceApi.checkExistTask(taskDTO, uapUserPo.getId());
        if (null != existTask){
            return existTask;
        }
        adminServiceApi.checkExportTaskCount(uapUserPo.getId());

        CommonUtilService.setAreaQueryDTO(query);
        if (query.getStartDate() != null && query.getEndDate() != null) {
            query.setStartDate(DateFormatUtil.getTheFistSecondOfOneDay(query.getStartDate()));
            query.setEndDate(DateFormatUtil.getTheLastSecondOfOneDay(query.getEndDate()));
        }
        List<VisitPersonInfoVO> result = adsMsMedicalInfoMapper.getVisitPersonDetailBy(query);

        Integer count = result.size();
        adminServiceApi.checkExportMax(count);
        taskDTO.setTotalCount(count);
        TbCdcmrExportTask exportTask = adminServiceApi.addExportTask(taskDTO, uapUserPo.getId());
        byte[] bytes = ExcelUtils.exportToExcel(result, VisitPersonInfoVO.class);
        exportTaskService.runTaskAndUploadFile(bytes,exportTask);
        return exportTask;
    }
}
