package com.iflytek.cdc.province.service;

import java.util.List;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.edr.vo.MedicalPatientDetailInfo;
import com.iflytek.cdc.province.model.vo.EdrIdentityVO;
import com.iflytek.cdc.province.model.vo.PatientTransferInfoVO;

/**
 * EDR 数据查询服务
 */
public interface EdrDataSearchService {

    /**
     * edr 病例分页数据
     */
    PageInfo<MsProcessSimpleInfoVO> pageList(String processType, MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 根据患者唯一身份标识查询该患者的详情数据
     */
    MedicalPatientDetailInfo loadByPatientId(String patientId , List<String> eventIds);

    /**
     * 根据empiId查询个人档案标识信息
     * 
     * @param empiId EMPI ID
     * @param residentIdCard 身份证号
     * @return 个人档案标识信息
     */
    EdrIdentityVO findEdrIdentityByEmpiId(String empiId, String residentIdCard);

    /**
     * 分页查询症候群转院人群详情
     */
    PageInfo<PatientTransferInfoVO> pageListTransfer(MsProcessSimpleInfoQueryDTO dto);


}
