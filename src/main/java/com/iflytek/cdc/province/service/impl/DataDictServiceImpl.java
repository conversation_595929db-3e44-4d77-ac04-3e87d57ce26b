package com.iflytek.cdc.province.service.impl;

import com.iflytek.cdc.province.entity.bu.TbCdcdmDataDictValue;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmDataDictMapper;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmDataDictValueMapper;
import com.iflytek.cdc.province.service.DataDictService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class DataDictServiceImpl implements DataDictService {
    @Resource
    private TbCdcdmDataDictValueMapper tbCdcdmDataDictValueMapper;
    
    @Override
    public List<TbCdcdmDataDictValue> listByDataDictCode(String code) {
        return tbCdcdmDataDictValueMapper.listByDataDictCode(code);
    }

    @Override
    public String getValue(String dictCode, String code) {
        return listByDataDictCode(dictCode).stream().filter(item -> item.getCode().equals(code)).findFirst()
                .map(TbCdcdmDataDictValue::getName).orElse(null);
    }
}
