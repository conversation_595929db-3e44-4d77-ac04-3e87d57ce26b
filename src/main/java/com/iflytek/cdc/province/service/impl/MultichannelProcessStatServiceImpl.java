package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.lang.Pair;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.common.Common;
import com.iflytek.cdc.edr.entity.TbCdcmrPathogenInfo;
import com.iflytek.cdc.edr.enums.DateDimEnum;
import com.iflytek.cdc.edr.utils.DimDateUtils;
import com.iflytek.cdc.edr.utils.MathUtil;
import com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO;
import com.iflytek.cdc.province.constant.PathogenDataConstant;
import com.iflytek.cdc.province.enums.CycleTypeEnum;
import com.iflytek.cdc.province.enums.FlagEnum;
import com.iflytek.cdc.province.mapper.pg.*;
import com.iflytek.cdc.province.model.dto.DateDim;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.AgeRange;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ProcessPathogenQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ProcessStatAnalysisQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.*;
import com.iflytek.cdc.province.model.pathogen.PathogenAnalysisReqDTO;
import com.iflytek.cdc.province.model.pathogen.PathogenInfectionSituationVO;
import com.iflytek.cdc.province.model.pathogen.PathogenNameGroupVO;
import com.iflytek.cdc.province.model.pathogen.PathogenTwofoldStatVO;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.service.DimInfectedInfoService;
import com.iflytek.cdc.province.service.MultichannelProcessStatService;
import com.iflytek.cdc.province.service.MultichannelUtilsService;
import com.iflytek.cdc.province.utils.AgeUtil;
import com.iflytek.cdc.province.utils.PageUtils;
import com.iflytek.cdc.province.utils.StatUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MultichannelProcessStatServiceImpl implements MultichannelProcessStatService {
    
    @Resource
    private CommonUtilService commonUtilService;

    @Resource
    private DimDateUtils dimDateUtils;

    @Resource
    private AdsMulProcessStatMapper adsMulProcessStatMapper;
    
    @Resource
    private AdsMsInfectEvolveStatMapper adsMsInfectEvolveStatMapper;

    @Resource
    private MultichannelUtilsService multichannelUtilsService;

    @Resource
    private AdsPdPathogenStatMapper adsPdPathogenStatMapper;

    @Resource
    private AdsPdPathogenMultiStatMapper adsPdPathogenMultiStatMapper;

    @Resource
    private AdsPdPathogenTwofoldStatMapper adsPdPathogenTwofoldStatMapper;

    @Resource
    private CdcAdminServiceApi cdcAdminServiceApi;

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Resource
    private DimInfectedInfoService dimInfectedInfoService;
    
    @Override
    public PageInfo<AdsMsProcessRespVO> getEpidemiologicalProfile(ProcessStatAnalysisQueryDTO queryDTO) {

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, PageInfo::new, q -> {
            // 获取人口数
            final int population = commonUtilService.getPopulation(q);
            List<AdsMsProcessRespVO> respVOList = StatUtils.build(q,
                    AdsMsProcessRespVO::new,
                    AdsMsProcessRespVO::getDescriptionCode,
                    AdsMsProcessRespVO::setDescriptionCode,
                    adsMulProcessStatMapper::diseaseOverAll,
                    AdsMsProcessRespVO.buildNewAndDeadOperations(),
                    AdsMsProcessRespVO.buildOtherProperties()
            );
            respVOList.forEach(vo -> {
                vo.setPopulation(population);
                vo.onlyCalculateRates(null);
            });

            return PageUtils.ofPage(respVOList, q.getPageIndex(), q.getPageSize());
        });
    }

    @Override
    public List<ProcessTimeLineStatVO> getDiseaseTrend(ProcessStatAnalysisQueryDTO queryDTO) {
        dealFilterInfectCode(queryDTO);

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, ArrayList::new, q -> {
            q.setDateDims(dimDateUtils.getDateDims(q.getDateDimType(), q.getStartDate(), q.getEndDate()));

            return dimDateUtils.fillTimeData(q.getDateDimType(), ProcessTimeLineStatVO.class, q.getDateDims(),
                    buildStat(q, adsMulProcessStatMapper::diseaseOnsetTrend,
                            ProcessTimeLineStatVO::getStatDate,
                            ProcessTimeLineStatVO::setStatDate,
                            ProcessTimeLineStatVO.buildOtherProperties()));
        });
    }

    @Override
    public List<SyndromeConfirmSituationVO> getConfirmationOfSyndrome(ProcessStatAnalysisQueryDTO queryDTO) {



        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, ArrayList::new, q -> {

            List<SyndromeConfirmSituationVO> situationVOList = new ArrayList<>();
            //查询 症候群-传染病 结果
            List<SyndromeRelateInfectedProcessVO> processVOList = dimDateUtils.getDateDimData(q,
                                                                                              () -> adsMsInfectEvolveStatMapper.getSyndromeInfectDayDiagnose(q),
                                                                                              () -> adsMsInfectEvolveStatMapper.getSyndromeInfectWeekDiagnose(q),
                                                                                              () -> adsMsInfectEvolveStatMapper.getSyndromeInfectMeadowDiagnose(q),
                                                                                              () -> adsMsInfectEvolveStatMapper.getSyndromeInfectMonthDiagnose(q),
                                                                                              () -> adsMsInfectEvolveStatMapper.getSyndromeInfectQuarterDiagnose(q),
                                                                                              () -> adsMsInfectEvolveStatMapper.getSyndromeInfectYearDiagnose(q));
            //按照症候群进行分类
            Map<Pair<String, String>, List<SyndromeRelateInfectedProcessVO>> syndromeMap = commonUtilService.buildGroupMap(processVOList,
                                                                                                                           SyndromeRelateInfectedProcessVO::getSyndrome,
                                                                                                                           e -> StringUtils.isNotBlank(e.getSyndromeName()) && StringUtils.isNotBlank(e.getSyndromeCode()));
            syndromeMap.forEach((k, v) -> {
                SyndromeConfirmSituationVO vo = new SyndromeConfirmSituationVO();
                vo.setDiseaseCode(k.getKey());
                vo.setDiseaseName(k.getValue());
                vo.setProcessCount(v.stream().mapToInt(SyndromeRelateInfectedProcessVO::getProcessNewCnt).sum());
                List<SyndromeConfirmSituationVO.RelativeInfected> infectedList = new ArrayList<>();
                Map<Pair<String, String>, List<SyndromeRelateInfectedProcessVO>> infectedMap = commonUtilService.buildGroupMap(v,
                        SyndromeRelateInfectedProcessVO::getInfect,
                        e -> StringUtils.isNotBlank(e.getInfectCode()) && StringUtils.isNotBlank(e.getInfectName()));
                infectedMap.forEach((key, value) -> {
                    SyndromeConfirmSituationVO.RelativeInfected infect = new SyndromeConfirmSituationVO.RelativeInfected();
                    infect.setInfectedCode(key.getKey());
                    infect.setInfectedName(key.getValue());
                    infect.setInfectedProcessCount(value.stream().mapToInt(SyndromeRelateInfectedProcessVO::getProcessNewCnt).sum());
                    infectedList.add(infect);
                });
                vo.setInfectedList(infectedList);
                situationVOList.add(vo);
            });
            return situationVOList;
        });
    }

    @Override
    public List<MonitorProcessFlowVO> getMonitorProcessFlow(ProcessStatAnalysisQueryDTO queryDTO) {

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, ArrayList::new, q -> dimDateUtils.getDateDimData(q,
            () -> adsMsInfectEvolveStatMapper.getMonitorProcessFlowDayStat(q),
            () -> adsMsInfectEvolveStatMapper.getMonitorProcessFlowWeekStat(q),
            () -> adsMsInfectEvolveStatMapper.getMonitorProcessFlowMeadowStat(q),
            () -> adsMsInfectEvolveStatMapper.getMonitorProcessFlowMonthStat(q),
            () -> adsMsInfectEvolveStatMapper.getMonitorProcessFlowQuarterStat(q),
            () -> adsMsInfectEvolveStatMapper.getMonitorProcessFlowYearStat(q)));
    }

    @Override
    public PneumoniaOccursSituationVO getPneumoniaOccursSituation(ProcessStatAnalysisQueryDTO queryDTO) {

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, PneumoniaOccursSituationVO::new, q -> {
            PneumoniaOccursSituationVO situationVO = new PneumoniaOccursSituationVO();
            q.setDateDims(dimDateUtils.getDateDims(q.getDateDimType(), q.getStartDate(), q.getEndDate()));

            List<PneumoniaOccursSituationVO> voList = adsMulProcessStatMapper.pneumoniaSituation(q);

            //设置总病例数、肺炎病例、肺炎行病原检测病例
            situationVO.setTotalProcessCount(voList.stream().mapToInt(PneumoniaOccursSituationVO::getTotalProcessCount).sum());
            situationVO.setPneumoniaProcessCount(voList.stream().mapToInt(PneumoniaOccursSituationVO::getPneumoniaProcessCount).sum());
            //肺炎行病原检测病例
            int detectCount = voList.stream().mapToInt(PneumoniaOccursSituationVO::getPathogenDetectProcessCount).sum();
            situationVO.setPathogenDetectProcessCount(detectCount);

            Map<Pair<String, String>, List<PneumoniaOccursSituationVO>> voMap = voList.stream().collect(Collectors.groupingBy(PneumoniaOccursSituationVO::getDisease));
            List<PneumoniaOccursSituationVO.DiseaseRatio> diseaseRatioList = new ArrayList<>();
            voMap.forEach((k, v) -> {
                PneumoniaOccursSituationVO.DiseaseRatio diseaseRatio = new PneumoniaOccursSituationVO.DiseaseRatio();
                diseaseRatio.setDiseaseCode(k.getKey());
                diseaseRatio.setDiseaseName(k.getValue());
                int processCount = v.stream().mapToInt(PneumoniaOccursSituationVO::getTotalProcessCount).sum();
                diseaseRatio.setProcessCount(processCount);
                diseaseRatio.setRatio(MathUtil.div(processCount, detectCount, 4));
                diseaseRatioList.add(diseaseRatio);
            });
            situationVO.setDiseasePageInfo(PageUtils.ofPage(diseaseRatioList, q.getPageIndex(), q.getPageSize()));
            return situationVO;
        });
    }

    @Override
    public List<IndicatorDataVO> getProcessChanges(ProcessStatAnalysisQueryDTO queryDTO) {
        dealFilterInfectCode(queryDTO);

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, ArrayList::new, q -> adsMulProcessStatMapper.existsProcessStat(q));
    }

    private void dealFilterInfectCode(ProcessStatAnalysisQueryDTO queryDTO) {
        List<String> filterInfectNames = queryDTO.getFilterInfectNames();
        if (CollectionUtils.isEmpty(filterInfectNames)) {
            return;
        }
        List<String> filterInfectCodes = dimInfectedInfoService.getInfectedCodeByNames(filterInfectNames);
        List<String> diseaseCodeList = queryDTO.getDiseaseCodeList();
        if (!CollectionUtils.isEmpty(diseaseCodeList) && diseaseCodeList.contains("00051")) {
            queryDTO.setFilterInfectCodes(null);
        }else {
            queryDTO.setFilterInfectCodes(filterInfectCodes);
        }

    }

    @Override
    public PageInfo<ProcessSexDistributionVO> sexDistribution(ProcessStatAnalysisQueryDTO queryDTO) {

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, PageInfo::new, q -> {
            int population = commonUtilService.getPopulation(q);
            q.setDateDims(dimDateUtils.getDateDims(q.getDateDimType(), q.getStartDate(), q.getEndDate()));
            //传染病按性别统计
            List<ProcessSexDistributionVO> sexInfectDistributionList = adsMulProcessStatMapper.sexDistributionStat(q);

            sexInfectDistributionList.forEach(e -> {
                e.setMaleProcessRatio(MathUtil.div(multichannelUtilsService.getMultiNum(e.getMaleProcessCount()), population, 4));
                e.setFemaleProcessRatio(MathUtil.div(multichannelUtilsService.getMultiNum(e.getFemaleProcessCount()), population, 4));
                e.setSexRatio("1:" + MathUtil.div(e.getFemaleProcessCount(), e.getMaleProcessCount(), 2));
            });
            return PageUtils.ofPage(sexInfectDistributionList, q.pageIndex, q.pageSize);
        });
    }

    @Override
    public List<ProcessTimeLineStatVO> sexTrendsInDisease(ProcessStatAnalysisQueryDTO queryDTO) {
        dealFilterInfectCode(queryDTO);

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, ArrayList::new, q -> {
            q.setDateDims(dimDateUtils.getDateDims(q.getDateDimType(), q.getStartDate(), q.getEndDate()));

            return dimDateUtils.fillTimeData(q.getDateDimType(), ProcessTimeLineStatVO.class, q.getDateDims(),
                    adsMulProcessStatMapper.sexTimeLineTrend(q));
        });
    }

    @Override
    public PageInfo<ProcessDistributionVO> ageDistribution(ProcessStatAnalysisQueryDTO queryDTO) {

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, PageInfo::new, q -> {
            int population = commonUtilService.getPopulation(q);
            q.setDateDims(dimDateUtils.getDateDims(q.getDateDimType(), q.getStartDate(), q.getEndDate()));
            //查询统计值
            List<DoubleIndicatorStatVO> statVOList = adsMulProcessStatMapper.ageDistributionStat(q);

            List<ProcessDistributionVO> result = multichannelUtilsService.getMultiIndicatorStat(statVOList,
                                                                                                population,
                                                                                                true,
                                                                                                AgeUtil.AVG_AGE_RANGE.stream().map(AgeRange::getDesc).collect(Collectors.toList()));

            return PageUtils.ofPage(result, q.getPageIndex(), q.getPageSize());
        });
    }

    @Override
    public List<ProcessTrendVO> ageTrendsInDisease(ProcessStatAnalysisQueryDTO queryDTO) {
        dealFilterInfectCode(queryDTO);

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, ArrayList::new, q -> {
            List<GroupSumIntVO> ageTrendList = adsMulProcessStatMapper.ageTimeLineTrend(q);

            return multichannelUtilsService.getIndicatorTrends(q, ageTrendList);
        });
    }

    @Override
    public PageInfo<ProcessDistributionVO> jobDistribution(ProcessStatAnalysisQueryDTO queryDTO) {

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, PageInfo::new, q -> {
            // 获取人口数
            final int population = commonUtilService.getPopulation(q);

            // 查询统计值
            q.setDateDims(dimDateUtils.getDateDims(q.getDateDimType(), q.getStartDate(), q.getEndDate()));
            List<DoubleIndicatorStatVO> statVOList = buildDoubleIndicatorStat(q, adsMulProcessStatMapper::jobDistributionStat,
                    DoubleIndicatorStatVO::getXIndicatorCode,
                    DoubleIndicatorStatVO::setXIndicatorCode,
                    DoubleIndicatorStatVO.buildOtherProperties());
            //避免修改原始值，使用copyList操作
            List<DoubleIndicatorStatVO> copyList = statVOList;
            List<Pair<String, String>> top5Disease = multichannelUtilsService.getTopNDisease(copyList, DoubleIndicatorStatVO::getYIndicator, Common.yIndicator, 5);
            List<String> codeList = top5Disease.stream().map(Pair::getKey).collect(Collectors.toList());
            List<String> nameList = top5Disease.stream().map(Pair::getValue).collect(Collectors.toList());
            //取 同比增长前五的传染病
            statVOList = statVOList.stream().filter(e -> codeList.contains(e.getYIndicatorCode())).collect(Collectors.toList());

            List<ProcessDistributionVO> result = multichannelUtilsService.getMultiIndicatorStat(statVOList,
                                                                                                population,
                                                                                                true,
                                                                                                nameList);

            return PageUtils.ofPage(result, q.getPageIndex(), q.getPageSize());
        });
    }

    @Override
    public PopulationStatVO populationCharacteristics(ProcessStatAnalysisQueryDTO queryDTO) {
        dealFilterInfectCode(queryDTO);


        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, PopulationStatVO::new, q -> {
            PopulationStatVO vo = new PopulationStatVO();
            List<ProcessInfoVO> processInfoVOList = adsMulProcessStatMapper.characterDistributionStat(q);

            List<GroupSumIntVO> ageDistribution = multichannelUtilsService.groupBy(processInfoVOList, ProcessInfoVO::getPatientAgeGroup, ProcessInfoVO::getCount);
            List<GroupSumIntVO> sexDistribution = multichannelUtilsService.groupBy(processInfoVOList, ProcessInfoVO::getPatientSexName, ProcessInfoVO::getCount);
            List<GroupSumIntVO> jobDistribution = multichannelUtilsService.groupBy(processInfoVOList, ProcessInfoVO::getPatientJob, ProcessInfoVO::getCount);

            vo.setAgeGroup(fulfillAgeGroup(ageDistribution));
            vo.setSexGroup(sexDistribution);
            vo.setJobGroup(commonUtilService.fulfillRatio(jobDistribution, GroupSumIntVO::getValue, GroupSumIntVO::setRatioValue));
            return vo;
        });
    }
    
    //填充年龄段
    private List<GroupSumIntVO> fulfillAgeGroup(List<GroupSumIntVO> ageDistribution){
        List<GroupSumIntVO> ret = new ArrayList<>();
        Map<String, GroupSumIntVO> ageMap = commonUtilService.buildRankingMap(ageDistribution, GroupSumIntVO::getName, e -> StringUtils.isNotBlank(e.getName()));
        AgeUtil.AVG_AGE_RANGE.forEach(e -> {
            GroupSumIntVO sumIntVO = new GroupSumIntVO();
            sumIntVO.setName(e.getDesc());
            sumIntVO.setValue(Optional.ofNullable(ageMap.getOrDefault(e.getDesc(), new GroupSumIntVO()).getValue()).orElse(0));
            ret.add(sumIntVO);
        });
        return ret;
    }
    
    @Override
    public PageInfo<ProcessDistributionVO> regionDistribution(ProcessStatAnalysisQueryDTO queryDTO) {

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, PageInfo::new, q -> {
            int population = commonUtilService.getPopulation(q);
            //查询统计值
            q.setDateDims(dimDateUtils.getDateDims(q.getDateDimType(), q.getStartDate(), q.getEndDate()));
            List<DoubleIndicatorStatVO> statVOList = adsMulProcessStatMapper.regionDistributionStat(q);

            List<ProcessDistributionVO> result = multichannelUtilsService.getMultiIndicatorStat(statVOList,
                                                                                                population,
                                                                                                false,
                                                                                                new ArrayList<>());

            return PageUtils.ofPage(result, q.getPageIndex(), q.getPageSize());
        });
    }

    @Override
    public List<ProcessTrendVO> regionProcessTrends(ProcessStatAnalysisQueryDTO queryDTO) {
        dealFilterInfectCode(queryDTO);

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, ArrayList::new, q -> {
            List<GroupSumIntVO> regionTrendList = adsMulProcessStatMapper.regionTimeLineTrend(q);

            return multichannelUtilsService.getIndicatorTrends(q, regionTrendList);
        });
    }

    @Override
    public PageInfo<ProcessUrbanRuralDistributionVO> urbanAndRuralDistribution(ProcessStatAnalysisQueryDTO queryDTO) {

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, PageInfo::new, q -> {
            int population = commonUtilService.getPopulation(q);
            //传染病按城乡统计
            q.setDateDims(dimDateUtils.getDateDims(q.getDateDimType(), q.getStartDate(), q.getEndDate()));
            List<ProcessUrbanRuralDistributionVO> urbanRuralDistributionList = adsMulProcessStatMapper.urbanRuralDistributionStat(q);

            urbanRuralDistributionList.forEach(e -> {
                e.setUrbanProcessRatio(MathUtil.div(multichannelUtilsService.getMultiNum(e.getUrbanProcessCount()), population, 4));
                e.setRuralProcessRatio(MathUtil.div(multichannelUtilsService.getMultiNum(e.getRuralProcessCount()), population, 4));
                e.setSexRatio("1:" + MathUtil.div(e.getUrbanProcessCount(), e.getRuralProcessCount(), 2));
            });
            return PageUtils.ofPage(urbanRuralDistributionList, q.pageIndex, q.pageSize);
        });
    }

    @Override
    public List<ProcessTrendVO> urbanAndRuralProcessTrends(ProcessStatAnalysisQueryDTO queryDTO) {
        dealFilterInfectCode(queryDTO);

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, ArrayList::new, q -> {

            List<GroupSumIntVO> urbanRuralTrendList = adsMulProcessStatMapper.urbanRuralTimeLineTrend(q);

            return multichannelUtilsService.getIndicatorTrends(q, urbanRuralTrendList);
        });
    }

    @Override
    public List<ProcessAreaDistributionVO> areaDistribution(ProcessStatAnalysisQueryDTO queryDTO) {
        dealFilterInfectCode(queryDTO);

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, ArrayList::new, q -> {
            q.setDateDims(dimDateUtils.getDateDims(q.getDateDimType(), q.getStartDate(), q.getEndDate()));
            return adsMulProcessStatMapper.getAreaDistributionStat(q);
        });
    }

    @Override
    public List<DiseaseProcessTimeLineVO> yearOnYearGrowthByDate(ProcessStatAnalysisQueryDTO queryDTO) {

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, ArrayList::new, q -> {
            q.setDateDimType(DateDimEnum.MONTH.getCode());
            List<DateDim> dateDims = dimDateUtils.getDateDims(q.getDateDimType(), q.getStartDate(), q.getEndDate());
            q.setDateDims(dateDims);

            List<DiseaseProcessTimeLineVO> result = new ArrayList<>();
            List<ProcessTimeLineStatVO> statVOList = buildStat(q, adsMulProcessStatMapper::getInfectYearOnYearGrowthByDate,
                    ProcessTimeLineStatVO::getStatDate,
                    ProcessTimeLineStatVO::setStatDate,
                    ProcessTimeLineStatVO.buildOtherProperties());
            Map<Pair<String, String>, List<ProcessTimeLineStatVO>> statMap = commonUtilService.buildGroupMap(statVOList,
                                                                                                             ProcessTimeLineStatVO::getDisease,
                                                                                                             e -> StringUtils.isNotBlank(e.getDiseaseCode()) && StringUtils.isNotBlank(e.getDiseaseName()));

            List<Pair<String, String>> growthTop10Disease = this.getGrowthTop10Disease(statMap);
            statMap = statMap.entrySet().stream().filter(e -> growthTop10Disease.contains(e.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            statMap.forEach((k, v) -> {
                DiseaseProcessTimeLineVO vo = new DiseaseProcessTimeLineVO();
                vo.setDiseaseCode(k.getKey());
                vo.setDiseaseName(k.getValue());
                vo.setStatVOList(dimDateUtils.fillTimeData(DateDimEnum.MONTH.getCode(), ProcessTimeLineStatVO.class, dateDims, v));
                result.add(vo);
            });
            return result.stream().sorted(Comparator.nullsLast(Comparator.comparing(DiseaseProcessTimeLineVO::getDiseaseCode))).collect(Collectors.toList());
        });
    }

    private List<Pair<String, String>> getGrowthTop10Disease(Map<Pair<String, String>, List<ProcessTimeLineStatVO>> statMap){

        List<Map.Entry<Pair<String, String>, Double>> top10GrowthRates = statMap.entrySet().stream()
                                                                                .map(entry -> {
                                                                                    // 获取总计数据
                                                                                    int totalCurr = entry.getValue().stream()
                                                                                                                    .mapToInt(ProcessTimeLineStatVO::getProcessNewCnt)
                                                                                                                    .sum();
                                                                                    int totalLastY = entry.getValue().stream()
                                                                                                                     .mapToInt(ProcessTimeLineStatVO::getProcessNewCntLastY)
                                                                                                                     .sum();
                                                                                    // 计算增长率
                                                                                    double growthRate = MathUtil.getGrowthRate(totalLastY, totalCurr);
                                                                                    return new AbstractMap.SimpleEntry<>(entry.getKey(), growthRate);
                                                                                })
                                                                                .sorted((e1, e2) -> Double.compare(e2.getValue(), e1.getValue())) // 按增长率降序
                                                                                .limit(10) // 获取前10条
                                                                                .collect(Collectors.toList());
        return top10GrowthRates.stream().map(Map.Entry::getKey).collect(Collectors.toList());
    }

    @Override
    public List<DiseaseProcessAreaDistributionVO> yearOnYearGrowthByArea(ProcessStatAnalysisQueryDTO queryDTO) {

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, ArrayList::new, q -> {
            q.setDateDimType(DateDimEnum.MONTH.getCode());
            List<DateDim> dateDims = dimDateUtils.getDateDims(q.getDateDimType(), q.getStartDate(), q.getEndDate());
            q.setDateDims(dateDims);

            List<DoubleIndicatorStatVO> statVOList = buildDoubleIndicatorStat(q, adsMulProcessStatMapper::getInfectYearOnYearGrowthByArea,
                    DoubleIndicatorStatVO::getXIndicatorCode,
                    DoubleIndicatorStatVO::setXIndicatorCode,
                    DoubleIndicatorStatVO.buildOtherProperties());
            //过滤得到同比增长前10的疾病
            List<DoubleIndicatorStatVO> copyList = statVOList;
            List<Pair<String, String>> diseaseList = multichannelUtilsService.getTopNDisease(copyList, DoubleIndicatorStatVO::getXIndicator, Common.xIndicator, 10);
            List<String> codeList = diseaseList.stream().map(Pair::getKey).collect(Collectors.toList());
            statVOList = statVOList.stream().filter(e -> codeList.contains(e.getXIndicatorCode())).collect(Collectors.toList());
            Map<Pair<String, String>, List<DoubleIndicatorStatVO>> statVOMap = commonUtilService.buildGroupMap(statVOList, 
                                                                                                               DoubleIndicatorStatVO::getXIndicator,
                                                                                                               e -> StringUtils.isNotBlank(e.getXIndicatorName()) && StringUtils.isNotBlank(e.getXIndicatorCode()));

            List<DiseaseProcessAreaDistributionVO> result = statVOMap.entrySet().stream()
                                                                                .map(entry -> buildDiseaseDistribution(entry.getKey(), entry.getValue(), dateDims))
                                                                                .collect(Collectors.toList());
            return result.stream().sorted(Comparator.nullsLast(Comparator.comparing(DiseaseProcessAreaDistributionVO::getDiseaseCode))).collect(Collectors.toList());
        });
    }

    // 区域过滤条件
    private <T> boolean isValid(T t,
                                Function<T, String> codeFunction,
                                Function<T, String> nameFunction) {
        return StringUtils.isNotBlank(codeFunction.apply(t)) && StringUtils.isNotBlank(nameFunction.apply(t));
    }

    // 构建单个疾病分布数据
    private DiseaseProcessAreaDistributionVO buildDiseaseDistribution(Pair<String, String> diseaseKey,
                                                                      List<DoubleIndicatorStatVO> statVOList,
                                                                      List<DateDim> dateDims) {

        DiseaseProcessAreaDistributionVO distributionVO = new DiseaseProcessAreaDistributionVO();
        distributionVO.setDiseaseCode(diseaseKey.getKey());
        distributionVO.setDiseaseName(diseaseKey.getValue());

        // 按区域分组
        Map<Pair<String, String>, List<DoubleIndicatorStatVO>> areaGroupedMap = commonUtilService.buildGroupMap(statVOList,
                                                                                                                DoubleIndicatorStatVO::getYIndicator,
                                                                                                                e -> isValid(e, DoubleIndicatorStatVO::getYIndicatorCode, DoubleIndicatorStatVO::getYIndicatorName));

        // 构建区域分布数据
        List<DiseaseProcessAreaDistributionVO.AreaProcessDistribution> areaDistributions = areaGroupedMap.entrySet().stream()
                                                                                                                    .map(entry -> buildAreaDistribution(entry.getKey(), entry.getValue(), dateDims))
                                                                                                                    .collect(Collectors.toList());

        distributionVO.setAreaProcessDistributionList(areaDistributions);
        return distributionVO;
    }

    // 构建单个区域分布数据
    private DiseaseProcessAreaDistributionVO.AreaProcessDistribution buildAreaDistribution(Pair<String, String> areaKey,
                                                                                           List<DoubleIndicatorStatVO> statVOList,
                                                                                           List<DateDim> dateDims) {

        DiseaseProcessAreaDistributionVO.AreaProcessDistribution distribution = new DiseaseProcessAreaDistributionVO.AreaProcessDistribution();
        distribution.setAreaCode(areaKey.getKey());
        distribution.setAreaName(areaKey.getValue());

        // 构建时间分布数据
        List<DiseaseProcessAreaDistributionVO.ProcessDateDistribution> dateDistributions = statVOList.stream()
                                                                                                     .map(this::buildDateDistribution)
                                                                                                     .collect(Collectors.toList());
        // 填补时间数据
        distribution.setProcessDateDistributionList(
                dimDateUtils.fillTimeData(DateDimEnum.MONTH.getCode(), DiseaseProcessAreaDistributionVO.ProcessDateDistribution.class, dateDims, dateDistributions)
        );
        return distribution;
    }

    // 构建单个时间分布数据
    private DiseaseProcessAreaDistributionVO.ProcessDateDistribution buildDateDistribution(DoubleIndicatorStatVO statVO) {
        DiseaseProcessAreaDistributionVO.ProcessDateDistribution dateDistribution = new DiseaseProcessAreaDistributionVO.ProcessDateDistribution();
        BeanUtils.copyProperties(statVO, dateDistribution);
        dateDistribution.setProcessCount(statVO.getStatValue());
        return dateDistribution;
    }

    @Override
    public ProcessPositiveSituationVO listPositiveSituation(ProcessPathogenQueryDTO queryDTO) {

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, ProcessPositiveSituationVO::new, q -> {
            q.setDateDims(dimDateUtils.getDateDims(q.getDateDimType(), q.getStartDate(), q.getEndDate()));
            
            ProcessPositiveSituationVO situationVO = StatUtils.comparisonResult(q, adsMulProcessStatMapper::listPositiveSituation, ProcessPositiveSituationVO.buildCommonOperations());
            dimDateUtils.getOneLastDataByStatisticalTable(q,
                    adsMulProcessStatMapper::listPositiveSituation,
                    situationVO,
                    Arrays.asList(
                            Pair.of(ProcessPositiveSituationVO::getVirusDetectCount, ProcessPositiveSituationVO::setVirusDetectCountLast),
                            Pair.of(ProcessPositiveSituationVO::getBacteriaDetectCount, ProcessPositiveSituationVO::setBacteriaDetectCountLast),
                            Pair.of(ProcessPositiveSituationVO::getVirusPositiveCount, ProcessPositiveSituationVO::setVirusPositiveCountLast),
                            Pair.of(ProcessPositiveSituationVO::getBacteriaPositiveCount, ProcessPositiveSituationVO::setBacteriaPositiveCountLast),
                            Pair.of(ProcessPositiveSituationVO::getVirusAndBacteriaCount, ProcessPositiveSituationVO::setVirusAndBacteriaCountLast)));
            situationVO.setVirusDetectYearOnYear(MathUtil.getGrowthRate(situationVO.getVirusDetectCountLastY(), situationVO.getVirusDetectCount()));
            situationVO.setVirusDetectChain(MathUtil.getGrowthRate(situationVO.getVirusDetectCountLast(), situationVO.getVirusDetectCount()));

            situationVO.setBacteriaDetectYearOnYear(MathUtil.getGrowthRate(situationVO.getBacteriaDetectCountLastY(), situationVO.getBacteriaDetectCount()));
            situationVO.setBacteriaDetectChain(MathUtil.getGrowthRate(situationVO.getBacteriaDetectCountLast(), situationVO.getBacteriaDetectCount()));

            situationVO.setVirusPositiveYearOnYear(MathUtil.getGrowthRate(situationVO.getVirusPositiveCountLastY(), situationVO.getVirusPositiveCount()));
            situationVO.setVirusPositiveChain(MathUtil.getGrowthRate(situationVO.getVirusPositiveCountLast(), situationVO.getVirusPositiveCount()));

            situationVO.setBacteriaPositiveYearOnYear(MathUtil.getGrowthRate(situationVO.getBacteriaPositiveCountLastY(), situationVO.getBacteriaPositiveCount()));
            situationVO.setBacteriaPositiveChain(MathUtil.getGrowthRate(situationVO.getBacteriaPositiveCountLast(), situationVO.getBacteriaPositiveCount()));

            situationVO.setVirusAndBacteriaYearOnYear(MathUtil.getGrowthRate(situationVO.getVirusAndBacteriaCountLastY(), situationVO.getVirusAndBacteriaCount()));
            situationVO.setVirusAndBacteriaChain(MathUtil.getGrowthRate(situationVO.getVirusAndBacteriaCountLast(), situationVO.getVirusAndBacteriaCount()));

            return situationVO;
        });
    }

    @Override
    public List<ProcessPositiveSituationVO> listPositiveTimeLine(ProcessPathogenQueryDTO queryDTO) {

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, ArrayList::new, q -> {
            q.setDateDims(dimDateUtils.getDateDims(q.getDateDimType(), q.getStartDate(), q.getEndDate()));
            List<ProcessPositiveSituationVO> timeLine =  buildPathogenStat(q, adsMulProcessStatMapper::listPositiveTimeLine,
                    ProcessPositiveSituationVO::getStatDate,
                    ProcessPositiveSituationVO::setStatDate,
                    null);

            return dimDateUtils.fillTimeData(q.getDateDimType(), ProcessPositiveSituationVO.class, q.getDateDims(), timeLine);
        });
    }

    @Override
    public List<PositivePathogenVO> listTopPositiveRatioBy(ProcessPathogenQueryDTO queryDTO) {
        
        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, ArrayList::new, q -> {
            List<PathogenNameGroupVO> pathogenVOList = adsPdPathogenStatMapper.groupPositiveByName(PathogenAnalysisReqDTO.of(q));
            return pathogenVOList.stream()
                                 .map(PositivePathogenVO::of)
                                 .filter(e -> e.getPositiveRate() != null)
                                 .sorted(Comparator.comparing(PositivePathogenVO::getPositiveRate).reversed())
                                 .limit(20)
                                 .collect(Collectors.toList());
        });
    }

    @Override
    public List<PositivePathogenVO> listTopPositiveRatioGrowthBy(ProcessPathogenQueryDTO queryDTO) {
        
        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, ArrayList::new, q -> {
            List<PathogenNameGroupVO> pathogenVOList = adsPdPathogenStatMapper.groupPositiveByName(PathogenAnalysisReqDTO.of(q));
            return pathogenVOList.stream()
                                 .map(PositivePathogenVO::of)
                                 .filter(e -> e.getPositiveRatioGrowth() != null)
                                 .sorted(Comparator.comparing(PositivePathogenVO::getPositiveRatioGrowth).reversed())
                                 .limit(20)
                                 .collect(Collectors.toList());
        });
    }

    @Override
    public List<PathogenSpectrumCompareVO> listMicrobeAndVirusPathogen(ProcessPathogenQueryDTO queryDTO) {
        
        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, ArrayList::new, q -> {
            PathogenAnalysisReqDTO dto = PathogenAnalysisReqDTO.of(queryDTO);
            //查询当期以及去年同期，并进行数据填充
            List<PathogenSpectrumCompareVO> pathogenVOList = StatUtils.fulfillLastValueByDay(dto, CycleTypeEnum.LAST_YEAR,
                                                                                             PathogenSpectrumCompareVO::new,
                                                                                             adsPdPathogenStatMapper::listPathogenSpectrumResult,
                                                                                             PathogenSpectrumCompareVO::getPathogenName,
                                                                                             PathogenSpectrumCompareVO::setPathogenName,
                                                                                             PathogenSpectrumCompareVO.buildLastYOperation());

            pathogenVOList.forEach(PathogenSpectrumCompareVO::calculateAllRates);

            // 确定排序依据
            Comparator<PathogenSpectrumCompareVO> comparator;
            if (PathogenDataConstant.OrderIndexEnum.POSITIVE_YEAR_GROWTH.codeEquals(q.getOrderIndex())) {
                if (PathogenDataConstant.CompareDimTypeEnum.PNEUMONIA.typeEquals(q.getCompareDimType())) {
                    comparator = Comparator.comparing(PathogenSpectrumCompareVO::getPneumoniaPositiveRatioGrowth, Comparator.nullsLast(Comparator.reverseOrder()));
                } else {
                    comparator = Comparator.comparing(PathogenSpectrumCompareVO::getFeverPositiveRatioGrowth, Comparator.nullsLast(Comparator.reverseOrder()));
                }
            } else {
                if (PathogenDataConstant.CompareDimTypeEnum.PNEUMONIA.typeEquals(q.getCompareDimType())) {
                    comparator = Comparator.comparing(PathogenSpectrumCompareVO::getPneumoniaPositiveRate, Comparator.nullsLast(Comparator.reverseOrder()));
                } else {
                    comparator = Comparator.comparing(PathogenSpectrumCompareVO::getFeverPositiveRate, Comparator.nullsLast(Comparator.reverseOrder()));
                }
            }
            return pathogenVOList.stream().sorted(comparator).collect(Collectors.toList());
        });
    }

    @Override
    public Map<String, List<PathogenPositiveAgeAndSexVO>> listPositiveRatioByAgeAndSex(ProcessPathogenQueryDTO queryDTO) {
        
        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, HashMap::new, q -> {

            List<PathogenPositiveAgeAndSexVO> pathogenList = adsPdPathogenStatMapper.listPositiveRatioByAgeAndSex(PathogenAnalysisReqDTO.of(q));

            //找出阳性率前10的病原
            List<PathogenPositiveAgeAndSexVO> copyList = pathogenList;
            List<String> pathogenNameList = multichannelUtilsService.getTop10PathogenName(copyList, PathogenPositiveAgeAndSexVO::getPathogenName);
            pathogenList = pathogenList.stream().filter(e -> pathogenNameList.contains(e.getPathogenName())).collect(Collectors.toList());

            //过滤后计算男、女性的阳性率
            pathogenList.forEach(e -> {
                e.setMalePositiveRatio(MathUtil.div(e.getMalePositiveCount(), e.getDetectCount(), 4));
                e.setFemalePositiveRatio(MathUtil.div(e.getFemalePositiveCount(), e.getDetectCount(), 4));
            });
            //分组
            Map<String, List<PathogenPositiveAgeAndSexVO>> pathogenMap = pathogenList.stream().collect(Collectors.groupingBy(PathogenPositiveAgeAndSexVO::getPathogenName));

            //每种病原的性别分布数据补全
            pathogenMap.replaceAll((k, v) -> {
                Map<String, PathogenPositiveAgeAndSexVO> voMap = v.stream().collect(Collectors.toMap(PathogenPositiveAgeAndSexVO::getAgeGroup, Function.identity(), (a, b) -> a));
                return AgeUtil.PATHOGEN_POSITIVE_RANGE.stream()
                                                      .map(ageRange -> {
                                                          PathogenPositiveAgeAndSexVO vo = new PathogenPositiveAgeAndSexVO();
                                                          if (voMap.containsKey(ageRange.getDesc())) {
                                                              // 如果已有数据，拷贝属性
                                                              BeanUtils.copyProperties(voMap.get(ageRange.getDesc()), vo);
                                                          }
                                                          vo.setAgeGroup(ageRange.getDesc());
                                                          return vo;
                                                      })
                                                      .collect(Collectors.toList());
            });
            return pathogenMap;
        });
    }

    @Override
    public List<PathogenCombinationRateVO> listCombinationInfectionRate(ProcessPathogenQueryDTO queryDTO) {

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, ArrayList::new, q -> {

            // 每种组合的病例数
            List<PathogenTwofoldStatVO> statList = adsPdPathogenTwofoldStatMapper.groupTwofoldByName(PathogenAnalysisReqDTO.of(q));
            return statList.stream().map(PathogenCombinationRateVO::of).collect(Collectors.toList());
        });
    }

    @Override
    public PageInfo<PathogenResultVO> listPathogenInfectionSituation(ProcessPathogenQueryDTO queryDTO) {

        return multichannelUtilsService.setQueryDTOAndExecute(queryDTO, PageInfo::new, q -> {

        //病原及其大类map
        Map<String, TbCdcmrPathogenInfo> pathogenClassMap = cdcAdminServiceApi.getPathogenInfo()
                .stream()
                .collect(Collectors.toMap(TbCdcmrPathogenInfo::getPathogenName, Function.identity(), (oldValue, newValue) -> newValue));

            //按病原进行分组
            List<PathogenInfectionSituationVO> pathogenCheckList = adsPdPathogenMultiStatMapper.getInfectionSituation(PathogenAnalysisReqDTO.of(q));
            Map<String, List<PathogenInfectionSituationVO>> situationMap = pathogenCheckList.stream().collect(Collectors.groupingBy(PathogenInfectionSituationVO::getPathogenName));

            List<PathogenResultVO> resultVOList = new ArrayList<>();
            situationMap.forEach((k, v) -> {
                PathogenResultVO pathogenResultVO = new PathogenResultVO();
                TbCdcmrPathogenInfo info = pathogenClassMap.getOrDefault(k, new TbCdcmrPathogenInfo());
                if (info == null) {
                    return;
                }
                pathogenResultVO.setPathogenId(info.getId());
                pathogenResultVO.setPathogenName(info.getPathogenName());
                pathogenResultVO.setPathogenClassCode(info.getPathogenClassCode());
                pathogenResultVO.setPathogenClassName(info.getPathogenClassName());
                //设置全部、肺炎、非肺炎情况
                pathogenResultVO.setAllInfectionSituation(getDistributionBy(v, null));
                pathogenResultVO.setPneumoniaInfection(getDistributionBy(v, FlagEnum.YES.getCode()));
                pathogenResultVO.setNotPneumoniaInfection(getDistributionBy(v, FlagEnum.NO.getCode()));

                resultVOList.add(pathogenResultVO);
            });
            
            // 按照合计字段从高到低排序
            resultVOList.sort((a, b) -> b.getAllInfectionSituation().getTotal().compareTo(a.getAllInfectionSituation().getTotal()));
            
            return PageUtils.ofPage(resultVOList, q.getPageIndex() ,q.getPageSize());
        });
    }

    /**
     * 获取某一类病原的情况
     * */
    private PathogenResultVO.ProcessCountDistribution getDistributionBy(List<PathogenInfectionSituationVO> situationVOList,
                                                                        String isPneumonia){

        PathogenResultVO.ProcessCountDistribution distribution = new PathogenResultVO.ProcessCountDistribution();
        if(StringUtils.isNotBlank(isPneumonia)){
            situationVOList = situationVOList.stream()
                                             .filter(e -> Objects.equals(isPneumonia, e.getIsPneumonia()))
                                             .collect(Collectors.toList());
        }
        distribution.setTotal(commonUtilService.countByList(situationVOList, PathogenInfectionSituationVO::getTotal));
        distribution.setSingleInfection(commonUtilService.countByList(situationVOList, PathogenInfectionSituationVO::getSingleInfection));
        distribution.setDoubleInfection(commonUtilService.countByList(situationVOList, PathogenInfectionSituationVO::getDoubleInfection));
        distribution.setTripleInfection(commonUtilService.countByList(situationVOList, PathogenInfectionSituationVO::getTripleInfection));
        distribution.setQuadrupleInfection(commonUtilService.countByList(situationVOList, PathogenInfectionSituationVO::getQuadrupleInfection));

        return distribution;
    }

    private List<DoubleIndicatorStatVO> buildDoubleIndicatorStat(ProcessStatAnalysisQueryDTO reqDTO,
                                                                 Function<ProcessStatAnalysisQueryDTO, List<DoubleIndicatorStatVO>> queryFunction,
                                                                 Function<DoubleIndicatorStatVO, String> descriptionGetter,
                                                                 BiConsumer<DoubleIndicatorStatVO, String> descriptionSetter,
                                                                 List<Pair<Function<DoubleIndicatorStatVO, ?>, BiConsumer<DoubleIndicatorStatVO, ?>>> otherProperties) {

        return StatUtils.build(reqDTO,
                DoubleIndicatorStatVO::new,
                descriptionGetter,
                descriptionSetter,
                queryFunction,
                DoubleIndicatorStatVO.buildCommonOperations(),
                otherProperties);
    }

    private List<ProcessTimeLineStatVO> buildStat(ProcessStatAnalysisQueryDTO reqDTO,
                                                  Function<ProcessStatAnalysisQueryDTO, List<ProcessTimeLineStatVO>> queryFunction,
                                                  Function<ProcessTimeLineStatVO, String> descriptionGetter,
                                                  BiConsumer<ProcessTimeLineStatVO, String> descriptionSetter,
                                                  List<Pair<Function<ProcessTimeLineStatVO, ?>, BiConsumer<ProcessTimeLineStatVO, ?>>> otherProperties) {

        return StatUtils.build(reqDTO,
                ProcessTimeLineStatVO::new,
                descriptionGetter,
                descriptionSetter,
                queryFunction,
                ProcessTimeLineStatVO.buildCommonOperations(),
                otherProperties);
    }

    private List<ProcessPositiveSituationVO> buildPathogenStat(ProcessPathogenQueryDTO reqDTO,
                                                  Function<ProcessPathogenQueryDTO, List<ProcessPositiveSituationVO>> queryFunction,
                                                  Function<ProcessPositiveSituationVO, String> descriptionGetter,
                                                  BiConsumer<ProcessPositiveSituationVO, String> descriptionSetter,
                                                  List<Pair<Function<ProcessPositiveSituationVO, ?>, BiConsumer<ProcessPositiveSituationVO, ?>>> otherProperties) {

        return StatUtils.build(reqDTO,
                ProcessPositiveSituationVO::new,
                descriptionGetter,
                descriptionSetter,
                queryFunction,
                ProcessPositiveSituationVO.buildCommonOperations(),
                otherProperties);
    }

}
