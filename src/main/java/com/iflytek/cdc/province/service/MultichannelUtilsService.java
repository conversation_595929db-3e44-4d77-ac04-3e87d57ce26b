package com.iflytek.cdc.province.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.common.Common;
import com.iflytek.cdc.edr.enums.MasterDataEnum;
import com.iflytek.cdc.edr.utils.DataUtils;
import com.iflytek.cdc.edr.utils.DimDateUtils;
import com.iflytek.cdc.edr.utils.MathUtil;
import com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO;
import com.iflytek.cdc.province.constant.PathogenDataConstant;
import com.iflytek.cdc.province.enums.MultichannelTopicConfigEnum;
import com.iflytek.cdc.province.enums.PopulationTypes;
import com.iflytek.cdc.province.model.dto.CommonQuery;
import com.iflytek.cdc.province.model.dto.DateDim;
import com.iflytek.cdc.province.model.dto.DateDimQueryParam;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ProcessPathogenQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ProcessStatAnalysisQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.*;
import com.iflytek.cdc.province.model.vo.TreeNode;
import com.iflytek.cdc.province.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 多渠道查询工具
 * */
@Service
@Slf4j
public class MultichannelUtilsService {

    @Resource
    private CommonUtilService commonUtilService;

    @Resource
    private DimDateUtils dimDateUtils;

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    private <K extends DateDimQueryParam> List<AdsMsProcessRespVO> getPeriodData(K queryDTO,
                                                                                 Function<K, List<AdsMsProcessRespVO>> dataQueryFunction,
                                                                                 BiFunction<K, Class<K>, K> periodFunction) {
        K newQueryDTO = periodFunction.apply(queryDTO, (Class<K>) queryDTO.getClass());
        return dataQueryFunction.apply(newQueryDTO);
    }

    /**
     * 查询本期、上期
     * 去年同期 能在统计表中找到数据
     * */
    public <K extends DateDimQueryParam> List<AdsMsProcessRespVO> buildResult(K queryDTO,
                                                                              Function<K, List<AdsMsProcessRespVO>> dataQueryFunction,
                                                                              Function<AdsMsProcessRespVO, String> mapFunction,
                                                                              Predicate<AdsMsProcessRespVO> filter) {

        //查询当前周期数据 设置人口信息
        List<AdsMsProcessRespVO> currPeriodData = dataQueryFunction.apply(queryDTO);
        int population = commonUtilService.getPopulation(queryDTO);
        currPeriodData.forEach(e -> e.setPopulation(population));
        //将查询周期推到上个周期
        List<AdsMsProcessRespVO> previousPeriodData = this.getPeriodData(queryDTO, dataQueryFunction, DateUtils::getPrePeriod);

        return this.calRatio(currPeriodData, previousPeriodData, mapFunction, filter);
    }

    /**
     * 计算同环比
     * */
    public List<AdsMsProcessRespVO> calRatio(List<AdsMsProcessRespVO> currentPeriod,
                                             List<AdsMsProcessRespVO> previousPeriod,
                                             Function<AdsMsProcessRespVO, String> mapFunction,
                                             Predicate<AdsMsProcessRespVO> filter) {

        // 将当前和上周期数据转换为 Map，以疾病代码作为键
        Map<String, AdsMsProcessRespVO> currentRankingMap = commonUtilService.buildRankingMap(currentPeriod, mapFunction, filter);
        Map<String, AdsMsProcessRespVO> previousRankingMap = commonUtilService.buildRankingMap(previousPeriod, mapFunction, filter);

        // 所有key集合 确保不会因为本期没有数据导致数据缺失
        Set<String> indicators = new HashSet<>();
        indicators.addAll(currentRankingMap.keySet());
        indicators.addAll(previousRankingMap.keySet());

        // 计算同比和环比
        return indicators.stream()
                         .map(elem -> {
                             try {
                                 AdsMsProcessRespVO current = currentRankingMap.getOrDefault(elem, new AdsMsProcessRespVO());
                                 AdsMsProcessRespVO previous = previousRankingMap.getOrDefault(elem, new AdsMsProcessRespVO());

                                 //设置上期值
                                 current.setProcessNewCntLast(previous.getProcessNewCnt());
                                 current.setProcessDeadCntLast(previous.getProcessDeadCnt());
                                 //计算发病同环比
                                 current.setChainRatio(MathUtil.getGrowthRateStr(previous.getProcessNewCnt(), current.getProcessNewCnt()));
                                 current.setYearOnYear(MathUtil.getGrowthRateStr(current.getProcessNewCntLastY(), current.getProcessNewCnt()));
                                 //计算死亡数同环比
                                 current.setDeathChainRatio(MathUtil.getGrowthRateStr(previous.getProcessDeadCnt(), current.getProcessDeadCnt()));
                                 current.setDeathYearOnYear(MathUtil.getGrowthRateStr(current.getProcessDeadCntLastY(), current.getProcessDeadCnt()));
                                 //计算发病率、死亡率
                                 current.setCurrentRate(DataUtils.hundredThousandth(current.getProcessNewCnt(), current.getPopulation()));
                                 current.setDeathRate(DataUtils.hundredThousandth(current.getProcessDeadCnt(), current.getPopulation()));
                                 return current;
                             } catch (Exception e) {
                                 throw new RuntimeException(e);
                             }
                         })
                         .collect(Collectors.toList());
    }

    /**
     * 计算 双重统计指标
     * */
    public List<ProcessDistributionVO> getMultiIndicatorStat(List<DoubleIndicatorStatVO> list,
                                                             int denominator,
                                                             boolean isNeedFillYIndicator,
                                                             List<String> yIndicatorKeys) {

        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }

        Map<Pair<String, String>, List<DoubleIndicatorStatVO>> statVOMap = list.stream()
                                                                               .filter(e -> StringUtils.isNotBlank(e.getXIndicatorName()))
                                                                               .collect(Collectors.groupingBy(DoubleIndicatorStatVO::getXIndicator));

        // 遍历分组，生成结果
        return statVOMap.entrySet().stream()
                                   .map(entry -> createProcessDistribution(entry.getKey(), entry.getValue(), denominator, isNeedFillYIndicator, yIndicatorKeys))
                                   .collect(Collectors.toList());
    }

    private ProcessDistributionVO createProcessDistribution(Pair<String, String> key,
                                                            List<DoubleIndicatorStatVO> statList,
                                                            int denominator,
                                                            boolean isNeedFillYIndicator,
                                                            List<String> yIndicatorKeys) {
        ProcessDistributionVO vo = new ProcessDistributionVO();
        vo.setDescriptionCode(key.getKey());
        vo.setDescriptionName(key.getValue());

        Map<String, DoubleIndicatorStatVO> yIndicatorMap = statList.stream()
                                                                   .collect(Collectors.toMap(DoubleIndicatorStatVO::getYIndicatorName, Function.identity(), (oldValue, newValue) -> oldValue));

        // 填充分布列表
        List<GroupSumIntVO> distributionList = new ArrayList<>();
        if (isNeedFillYIndicator) {
            // 需要填充的情况
            yIndicatorKeys.forEach(yKey -> distributionList.add(createGroupSumIntVO(yKey, yIndicatorMap.getOrDefault(yKey, new DoubleIndicatorStatVO()), denominator)));
        }else {
            // 遍历原始数据，生成分布项
            statList.forEach(statVO -> distributionList.add(createGroupSumIntVO(statVO.getYIndicatorName(), statVO, denominator)));
        }
        vo.setDistributionList(distributionList);
        return vo;
    }

    private GroupSumIntVO createGroupSumIntVO(String name, DoubleIndicatorStatVO statVO, int denominator) {
        GroupSumIntVO groupSumIntVO = new GroupSumIntVO();
        groupSumIntVO.setName(name);
        int statValue = Optional.ofNullable(statVO.getStatValue()).orElse(0);
        groupSumIntVO.setValue(statValue);
        groupSumIntVO.setRatioValue(MathUtil.div(this.getMultiNum(statValue), denominator, 4));
        return groupSumIntVO;
    }

    /**
     * 分类 并计算每类下的病例数
     * */
    public <T> List<GroupSumIntVO> groupBy(List<T> data,
                                           Function<T, String> groupFunction,
                                           Function<T, Integer> valueMapFunction) {
        if (cn.hutool.core.collection.CollectionUtil.isEmpty(data)){
            return new ArrayList<>();
        }

        Map<String, List<T>> map = data.stream()
                .filter(d -> {
                    String key = groupFunction.apply(d);
                    return key != null && !key.trim().isEmpty();
                })
                .collect(Collectors.groupingBy(groupFunction));

        List<GroupSumIntVO> result = new ArrayList<>();
        map.forEach((k, v) -> {
            GroupSumIntVO vo = new GroupSumIntVO();
            vo.setName(k);
            vo.setValue(v.stream().mapToInt(valueMapFunction::apply).sum());
            result.add(vo);
        });
        return result;
    }


    /**
     * 指标 趋势图 填充时间
     * */
    public <T extends DateDimQueryParam> List<ProcessTrendVO> getIndicatorTrends(T dto,
                                                                                 List<GroupSumIntVO> data) {

        Map<String, List<GroupSumIntVO>> valueMap = data.stream().collect(Collectors.groupingBy(GroupSumIntVO::getName));
        List<DateDim> dateDims = dimDateUtils.getDateDims(dto.getDateDimType(), dto.getStartDate(), dto.getEndDate());
        List<ProcessTrendVO> ret = new ArrayList<>();
        valueMap.forEach((k, v) -> {
            ProcessTrendVO vo = new ProcessTrendVO();
            vo.setDescription(k);
            List<GroupSumIntVO> value = dimDateUtils.fillTimeData(dto.getDateDimType(), GroupSumIntVO.class, dateDims, v);
            vo.setDistributionList(value);
            ret.add(vo);
        });
        return ret;
    }

    public List<Pair<String, String>> getTopNDisease(List<DoubleIndicatorStatVO> statVOList,
                                                     Function<DoubleIndicatorStatVO, Pair<String, String>> groupFunction,
                                                     String indicatorDim,
                                                     int n) {
        //按分组计算每组的汇总结果
        List<DoubleIndicatorStatVO> groupedResults = calculateGroupedResults(statVOList, groupFunction, indicatorDim);

        //根据同比增长排序，并提取前N个结果
        return getTopNGroups(groupedResults, groupFunction, n);
    }

    private List<DoubleIndicatorStatVO> calculateGroupedResults(List<DoubleIndicatorStatVO> statVOList,
                                                                Function<DoubleIndicatorStatVO, Pair<String, String>> groupFunction,
                                                                String indicatorDim) {
        return statVOList.stream()
                         .filter(e -> isKeyAndValueNotEmpty(groupFunction.apply(e)))
                         .collect(Collectors.groupingBy(groupFunction, Collectors.reducing(new DoubleIndicatorStatVO(), (r1, r2) -> mergeIndicatorStats(r1, r2, indicatorDim))))
                         .values()
                         .stream()
                         .peek(result -> result.setYearOnYear(MathUtil.getGrowthRate(result.getStatValueLastY(), result.getStatValue())))
                         .collect(Collectors.toList());
    }

    private DoubleIndicatorStatVO mergeIndicatorStats(DoubleIndicatorStatVO r1, DoubleIndicatorStatVO r2, String indicatorDim) {

        DoubleIndicatorStatVO statVO = new DoubleIndicatorStatVO();
        if (Common.xIndicator.equals(indicatorDim)){
            statVO.setXIndicatorCode(StringUtils.isBlank(r1.getXIndicatorCode()) ? r2.getXIndicatorCode() : r1.getXIndicatorCode());
            statVO.setXIndicatorName(StringUtils.isBlank(r1.getXIndicatorName()) ? r2.getXIndicatorName() : r1.getXIndicatorName());
        }
        if (Common.yIndicator.equals(indicatorDim)){
            statVO.setYIndicatorCode(StringUtils.isBlank(r1.getYIndicatorCode()) ? r2.getYIndicatorCode() : r1.getYIndicatorCode());
            statVO.setYIndicatorName(StringUtils.isBlank(r1.getYIndicatorName()) ? r2.getYIndicatorName() : r1.getYIndicatorName());
        }
        statVO.setStatValue(commonUtilService.transData(r1.getStatValue()) + commonUtilService.transData(r2.getStatValue()));
        statVO.setStatValueLastY(commonUtilService.transData(r1.getStatValueLastY()) + commonUtilService.transData(r2.getStatValueLastY()));
        return statVO;
    }

    private List<Pair<String, String>> getTopNGroups(List<DoubleIndicatorStatVO> groupedResults,
                                                     Function<DoubleIndicatorStatVO, Pair<String, String>> groupFunction,
                                                     int n) {
        return groupedResults.stream()
                             .filter(r -> r.getYearOnYear() != null)
                             .sorted(Comparator.comparing(DoubleIndicatorStatVO::getYearOnYear).reversed())
                             .limit(n)
                             .map(groupFunction)
                             .collect(Collectors.toList());
    }

    public boolean isKeyAndValueNotEmpty(Pair<String, String> pair) {
        return pair.getKey() != null && !pair.getKey().isEmpty() && pair.getValue() != null && !pair.getValue().isEmpty();
    }

    /**
     * 病原检出结果 处理
     * */
    public List<VirusTypeDetectedRateVO> buildDetectResult(List<VirusCheckResultVO> resultVOList,
                                                           Function<VirusCheckResultVO, String> groupFunction){

        List<VirusTypeDetectedRateVO> result = new ArrayList<>();
        // 按病毒分型分组并进行聚合操作，确保不修改原始对象
        Map<String, VirusCheckResultVO> typeMap = resultVOList.stream()
                                                              .filter(e -> StringUtils.isNotBlank(groupFunction.apply(e)))
                                                              .collect(Collectors.groupingBy(groupFunction, Collectors.collectingAndThen(Collectors.toList(),
                                                                      list -> {
                                                                            VirusCheckResultVO aggregated = new VirusCheckResultVO();
                                                                            // 对每个分组内的元素进行累加
                                                                            for (VirusCheckResultVO vo : list) {
                                                                                if (StringUtils.isBlank(aggregated.getPathogenId()) || StringUtils.isBlank(aggregated.getPathogenName()) || StringUtils.isBlank(aggregated.getType())) {
                                                                                    aggregated.setPathogenName(vo.getPathogenName());
                                                                                    aggregated.setType(vo.getType());
                                                                                }
                                                                                aggregated.setPositiveCount(commonUtilService.transData(aggregated.getPositiveCount()) + commonUtilService.transData(vo.getPositiveCount()));
                                                                                aggregated.setSampleCount(commonUtilService.transData(aggregated.getSampleCount()) + commonUtilService.transData(vo.getSampleCount()));
                                                                                aggregated.setDetectedCaseCount(commonUtilService.transData(aggregated.getDetectedCaseCount()) + commonUtilService.transData(vo.getDetectedCaseCount()));
                                                                                aggregated.setPositiveCaseCount(commonUtilService.transData(aggregated.getPositiveCaseCount()) + commonUtilService.transData(vo.getPositiveCaseCount()));
                                                                            }
                                                                            return aggregated;
                                                                      })));
        typeMap.forEach((k, v) -> {
            VirusTypeDetectedRateVO resultVO = new VirusTypeDetectedRateVO();
            resultVO.setPathogenType(k);
            resultVO.setCheckRate(MathUtil.div(v.getPositiveCount(), v.getSampleCount(), 4));
            resultVO.setPositiveRate(MathUtil.div(v.getPositiveCaseCount(), v.getDetectedCaseCount(), 4));
            result.add(resultVO);
        });

        return result;
    }

    /**
     * 不同性别各年龄段呼吸道传染病病原阳性率对比
     * 取阳性率前10的病原
     * */
    public List<String> getTop10PathogenName(List<PathogenPositiveAgeAndSexVO> resultVOList,
                                             Function<PathogenPositiveAgeAndSexVO, String> groupFunction){

        // 分组并聚合统计数据
        Map<String, PathogenPositiveAgeAndSexVO> pathogenMap = resultVOList.stream()
                .collect(Collectors.toMap(
                        groupFunction,
                        vo -> {
                            PathogenPositiveAgeAndSexVO newVo = new PathogenPositiveAgeAndSexVO();
                            newVo.setPathogenName(vo.getPathogenName());
                            newVo.setDetectCount(commonUtilService.transData(vo.getDetectCount()));
                            newVo.setPositiveCount(commonUtilService.transData(vo.getPositiveCount()));
                            return newVo;
                        },
                        (existing, newVo) -> {
                            existing.setDetectCount(existing.getDetectCount() + newVo.getDetectCount());
                            existing.setPositiveCount(existing.getPositiveCount() + newVo.getPositiveCount());
                            return existing;
                        }
                ));

        // 计算阳性率并排序取前10
        return pathogenMap.values().stream()
                                   .peek(vo -> vo.setPositiveRate(MathUtil.div(vo.getPositiveCount(), vo.getDetectCount(), 4)))
                                   .filter(vo -> vo.getPositiveRate() != null)
                                   .sorted(Comparator.comparing(PathogenPositiveAgeAndSexVO::getPositiveRate).reversed()
                                           .thenComparing(PathogenPositiveAgeAndSexVO::getPathogenName))
                                   .limit(10)
                                   .map(PathogenPositiveAgeAndSexVO::getPathogenName)
                                   .collect(Collectors.toList());
    }

    /**
     * 计算发病率 分子需要先乘 100000
     * */
    public int getMultiNum(Integer n){
        return Optional.ofNullable(n).orElse(0) * 100000;
    }

    /**
     * 根据前端传参的病原id 查询该病原id以及所有子类的name
     * */
    public List<String> getAllPathogenNameBy(List<String> pathogenIdList){

        List<TreeNode> root = adminServiceApi.getMasterDataInfo(MasterDataEnum.PATHOGEN.getCode());
        if (CollectionUtil.isEmpty(pathogenIdList) || CollectionUtil.isEmpty(root)) {
            return null;
        }
        List<String> res = new ArrayList<>();
        pathogenIdList.forEach(e->{
            for (TreeNode node : root) {
                TreeNode.getAllNodeBy(res, node, e, TreeNode::getId, TreeNode::getLabel);
            }
        });
        return res.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 根据前端传参的传染病id 查询该传染病id以及所有子类的id
     * */
    public List<String> getAllInfectedIdsBy(List<String> infectedIdList){

        List<TreeNode> root = adminServiceApi.getMasterDataInfo(MasterDataEnum.INFECTED.getCode());
        if (CollectionUtil.isEmpty(infectedIdList) || CollectionUtil.isEmpty(root)) {
            return null;
        }
        List<String> res = new ArrayList<>();
        infectedIdList.forEach(e->{
            for (TreeNode node : root) {
                TreeNode.getAllNodeBy(res, node, e, TreeNode::getId, TreeNode::getId);
            }
        });
        return res.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 症候群只取topic配置的id
     * */
    public List<String> getAllSyndromeIdsBy(List<String> syndromeCodeList){

        List<TreeNode> root = adminServiceApi.getMasterDataInfo(MasterDataEnum.SYNDROME.getCode());
        if (CollectionUtil.isEmpty(syndromeCodeList) || CollectionUtil.isEmpty(root)) {
            return null;
        }
        List<String> res = new ArrayList<>();
        syndromeCodeList.forEach(e->{
            for (TreeNode node : root) {
                TreeNode.getAllNodeBy(res, node, e, TreeNode::getValue, TreeNode::getValue);
            }
        });
        return res.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 设置多渠道专题传染病 查询参数
     * */
    public <T> void setTopicInfectedAndSyndromeQuery(T t,
                                                     TopicConfigInfoVO configInfoVO,
                                                     Function<T, List<String>> infectCodeFunction,
                                                     BiConsumer<T, List<String>> infectCodeList,
                                                     Function<T, List<String>> syndromeCodeFunction,
                                                     BiConsumer<T, List<String>> syndromeCodeList){
        setTopicQuery(t, configInfoVO, infectCodeFunction, infectCodeList, syndromeCodeFunction, syndromeCodeList, null, null, null, null, null, null);
    }

    /**
     * 设置多渠道专题传病原 查询参数
     * */
    public <T> void setTopicPathogenQuery(T t,
                                          TopicConfigInfoVO configInfoVO,
                                          Function<T, List<String>> pathogenIdFunction,
                                          BiConsumer<T, List<String>> pathogenNameList){
        setTopicQuery(t, configInfoVO, null, null, null, null, pathogenIdFunction, pathogenNameList, null, null, null, null);
    }

    /**
     * 设置多渠道专题 关键词、药品 查询参数
     * */
    public <T> void setTopicKeysAndDrugsQuery(T t,
                                              TopicConfigInfoVO configInfoVO,
                                              Function<T, List<String>> keywordFunction,
                                              BiConsumer<T, List<String>> keywordList,
                                              Function<T, List<String>> drugFunction,
                                              BiConsumer<T, List<String>> drugList){
        setTopicQuery(t, configInfoVO, null, null, null, null, null, null,keywordFunction, keywordList, drugFunction, drugList);
    }

    /**
     * 根据专题id 设置查询的 传染病codeList、症候群codeList、病原nameList、药品nameList、关键词nameList
     * */
    public <T> void setTopicQuery(T t,
                                  TopicConfigInfoVO configInfoVO,
                                  Function<T, List<String>> infectCodeFunction,
                                  BiConsumer<T, List<String>> infectCodeList,
                                  Function<T, List<String>> syndromeCodeFunction,
                                  BiConsumer<T, List<String>> syndromeCodeList,
                                  Function<T, List<String>> pathogenIdFunction,
                                  BiConsumer<T, List<String>> pathogenNameList,
                                  Function<T, List<String>> keywordFunction,
                                  BiConsumer<T, List<String>> keywordList,
                                  Function<T, List<String>> drugFunction,
                                  BiConsumer<T, List<String>> drugList){

        // 处理并应用配置到各个专题查询
        processTopicQuery(t, configInfoVO, infectCodeFunction, infectCodeList, MultichannelTopicConfigEnum.INFECTED.getCode(), this::getAllInfectedIdsBy, TbCdcmrMultichannelTopicConfig::getDataId);

        processTopicQuery(t, configInfoVO, syndromeCodeFunction, syndromeCodeList, MultichannelTopicConfigEnum.SYNDROME.getCode(), null, TbCdcmrMultichannelTopicConfig::getDataId);

        processTopicQuery(t, configInfoVO, pathogenIdFunction, pathogenNameList, MultichannelTopicConfigEnum.PATHOGEN.getCode(), this::getAllPathogenNameBy, TbCdcmrMultichannelTopicConfig::getDataId);

        processTopicQuery(t, configInfoVO, keywordFunction, keywordList, MultichannelTopicConfigEnum.KEYWORDS.getCode(), null, TbCdcmrMultichannelTopicConfig::getDataName);

        processTopicQuery(t, configInfoVO, drugFunction, drugList, MultichannelTopicConfigEnum.DRUGS.getCode(), null, TbCdcmrMultichannelTopicConfig::getDataName);

    }

    private <T> void processTopicQuery(T t,
                                       TopicConfigInfoVO configInfoVO,
                                       Function<T, List<String>> queryFunction,
                                       BiConsumer<T, List<String>> queryListConsumer,
                                       String configKey,
                                       Function<List<String>, List<String>> dataProcessor,
                                       Function<TbCdcmrMultichannelTopicConfig, String> dataFunction) {
        List<String> configuredCodes = getTopicConfigDataIdsBy(configInfoVO, configKey, dataFunction);
        List<String> queryCodes = queryFunction != null ? queryFunction.apply(t) : new ArrayList<>();

        if (CollectionUtil.isNotEmpty(queryCodes)) {
            configuredCodes = CommonUtilService.intersection(configuredCodes, queryCodes);
        }

        if (queryListConsumer != null) {
            List<String> processedData = dataProcessor != null ? dataProcessor.apply(configuredCodes) : configuredCodes;
            queryListConsumer.accept(t, processedData);
        }
    }

    private List<String> getTopicConfigDataIdsBy(TopicConfigInfoVO configInfoVO,
                                                 String topicConfig,
                                                 Function<TbCdcmrMultichannelTopicConfig, String> dataFunction) {

        if(configInfoVO == null){
            return new ArrayList<>();
        }
        return configInfoVO.getTopicConfigMap()
                           .getOrDefault(topicConfig, new ArrayList<>())
                           .stream()
                           .map(dataFunction)
                           .collect(Collectors.toList());
    }

    private void setProcessStatDTO(ProcessStatAnalysisQueryDTO queryDTO, TopicConfigInfoVO configInfoVO){

        //设置区域查询参数
        CommonUtilService.setAreaQueryDTO(queryDTO);
        //设置多渠道专题查询参数
        this.setTopicInfectedAndSyndromeQuery(queryDTO,
                                                configInfoVO,
                                                ProcessStatAnalysisQueryDTO::getDiseaseCodeList,
                                                ProcessStatAnalysisQueryDTO::setDiseaseCodeList,
                                                null,
                                                ProcessStatAnalysisQueryDTO::setSyndromeCodeList);
    }

    //设置预警场景查询参数
    private <T extends CommonQuery> void setWarningScenarioQueryDTO(T queryDTO, 
                                                                    Function<T, String> warningScenarioTypeFunction, 
                                                                    Function<T, String> otherWarningScenarioTypeFunction,
                                                                    BiConsumer<T, List<String>> personTypeListConsumer) {

        //设置区域查询参数
        CommonUtilService.setAreaQueryDTO(queryDTO);
        //设置人群分类
        if (StringUtils.isNotBlank(warningScenarioTypeFunction.apply(queryDTO))) {
            personTypeListConsumer.accept(queryDTO, PopulationTypes.getPersonTypeList(warningScenarioTypeFunction.apply(queryDTO)));
        }
        if (StringUtils.isNotBlank(otherWarningScenarioTypeFunction.apply(queryDTO))) {
            personTypeListConsumer.accept(queryDTO, PopulationTypes.specialPersonTypeList());
        }
    }

    public <T> T setQueryDTOAndExecute(ProcessStatAnalysisQueryDTO queryDTO,
                                       Supplier<T> emptyResultSupplier,
                                       Function<ProcessStatAnalysisQueryDTO, T> executor) {

        if (StringUtils.isNotBlank(queryDTO.getTopicId())) {
            // 获取多渠道专题信息
            TopicConfigInfoVO configInfoVO = adminServiceApi.getTopicConfig(queryDTO.getTopicId());
            if (configInfoVO == null) {
                return emptyResultSupplier.get();
            }
            setProcessStatDTO(queryDTO, configInfoVO);
        } else {
            if (StringUtils.isBlank(queryDTO.getWarningScenarioType()) && StringUtils.isBlank(queryDTO.getOtherWarningScenarioType())) {
                return emptyResultSupplier.get();
            }
            //设置预警场景查询参数
            setWarningScenarioQueryDTO(queryDTO,
                                        ProcessStatAnalysisQueryDTO::getWarningScenarioType,
                                        ProcessStatAnalysisQueryDTO::getOtherWarningScenarioType,
                                        ProcessStatAnalysisQueryDTO::setPersonTypeList);
        }
        return executor.apply(queryDTO);
    }

    /**
     * 设置病原谱查询参数
     * */
    private void setPathogenQueryDTO(ProcessPathogenQueryDTO queryDTO, TopicConfigInfoVO configInfoVO){

        // 设置区域查询参数
        CommonUtilService.setAreaQueryDTO(queryDTO);
        // 设置多渠道专题查询参数
        this.setTopicPathogenQuery(queryDTO,
                                        configInfoVO,
                                        ProcessPathogenQueryDTO::getPathogenIdList,
                                        ProcessPathogenQueryDTO::setAllPathogenNameList);
        if(CollectionUtil.isNotEmpty(queryDTO.getPathogenClassCode())) {
            List<String> res = new ArrayList<>();
            queryDTO.getPathogenClassCode().forEach(e -> res.add(PathogenDataConstant.PathogenClassEnum.getDescByCode(e)));
            queryDTO.setPathogenClassCode(res);
        }
    }

    public <T> T setQueryDTOAndExecute(ProcessPathogenQueryDTO queryDTO,
                                       Supplier<T> emptyResultSupplier,
                                       Function<ProcessPathogenQueryDTO, T> executor) {
        queryDTO.dealDate(queryDTO);
        if (StringUtils.isNotBlank(queryDTO.getTopicId())) {
            // 获取多渠道专题信息
            TopicConfigInfoVO configInfoVO = adminServiceApi.getTopicConfig(queryDTO.getTopicId());
            if (configInfoVO == null) {
                return emptyResultSupplier.get();
            }
            setPathogenQueryDTO(queryDTO, configInfoVO);
        } else {
            if (StringUtils.isBlank(queryDTO.getWarningScenarioType()) && StringUtils.isBlank(queryDTO.getOtherWarningScenarioType())) {
                return emptyResultSupplier.get();
            }
            //设置预警场景查询参数
            setWarningScenarioQueryDTO(queryDTO,
                                       ProcessPathogenQueryDTO::getWarningScenarioType,
                                       ProcessPathogenQueryDTO::getOtherWarningScenarioType,
                                       ProcessPathogenQueryDTO::setPersonTypeList);
        }
        return executor.apply(queryDTO);
    }

    
}
