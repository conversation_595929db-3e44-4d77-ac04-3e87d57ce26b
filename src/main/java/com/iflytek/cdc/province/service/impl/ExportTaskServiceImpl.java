package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.common.Common;
import com.iflytek.cdc.edr.entity.TbCdcAttachment;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.service.ExportTaskService;
import com.iflytek.cdc.province.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.IntSupplier;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

@Service
@Slf4j
@EnableAsync
@RefreshScope
public class ExportTaskServiceImpl implements ExportTaskService {

    @Resource
    private FileService fileService;
    @Value("${storage.switch:swift}")
    private String storageSwitch;

    @Value("${swift.prefix:/file-obj}")
    private String swiftPrefix;

    @Value("${minio.prefix:/minIoFile}")
    private String minioPrefix;

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Override
    public <K, T> TbCdcmrExportTask addAndUploadFile(Object param,
                                                     Supplier<List<K>> dataSupplier,
                                                     IntSupplier countSupplier,
                                                     ExportTaskDTO taskDTO,
                                                     Class<T> excelClass,
                                                     Function<K, T> convertFunction) {
        String taskParams = JSONObject.toJSONString(param);
        taskDTO.setTaskParam(taskParams);
        String loginUserId = userInfo.get().getId();
        TbCdcmrExportTask existTask = adminServiceApi.checkExistTask(taskDTO, loginUserId);
        if (null != existTask) {
            return existTask;
        }
        adminServiceApi.checkExportTaskCount(loginUserId);
        int count = countSupplier.getAsInt();
        adminServiceApi.checkExportMax(count);
        taskDTO.setTotalCount(count);
        TbCdcmrExportTask exportTask = adminServiceApi.addExportTask(taskDTO, loginUserId);
        runTaskAndUploadFile(dataSupplier.get(), exportTask, excelClass, convertFunction);
        return exportTask;

    }

    @Override
    @Async
    public <M, N> void runTaskAndUploadFile(List<N> dataList, TbCdcmrExportTask exportTask, Class<M> excelClass) {
        runTaskAndUploadFile(dataList, exportTask, excelClass, d -> BeanUtil.copyProperties(d, excelClass));
    }

    private <M, N> void runTaskAndUploadFile(List<N> dataList, TbCdcmrExportTask exportTask, Class<M> excelClass, Function<N, M> convertFunction) {
        Runnable task = () -> {
            List<M> excelList = dataList.stream().map(convertFunction).collect(Collectors.toList());
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, excelClass)
                    .sheet().doWrite(excelList);
            byte[] bytes = outputStream.toByteArray();
            uploadAndRecord(exportTask, bytes);
        };

        runTask(exportTask, adminServiceApi.getMaxCsvTimeoutTask(), task);
    }

    @Override
    @Async
    public void runTaskAndUploadFile(byte[] bytes, TbCdcmrExportTask exportTask) {
        Runnable task = () -> uploadAndRecord(exportTask, bytes);

        runTask(exportTask, adminServiceApi.getMaxCsvTimeoutTask(), task);
    }

    @Override
    public void runTaskAndUploadFile(Supplier<byte[]> bytesSupplier, TbCdcmrExportTask exportTask) {
        Runnable task = () -> {
            byte[] bytes = bytesSupplier.get();
            uploadAndRecord(exportTask, bytes);
        };
        runTask(exportTask, adminServiceApi.getMaxCsvTimeoutTask(), task);
    }

    /**
     * 上传文件并记录上传成功
     *
     * @param exportTask 导出任务实体
     * @param bytes      文件数据
     */
    private void uploadAndRecord(TbCdcmrExportTask exportTask, byte[] bytes) {
        log.info("开始上传原始文件");
        TbCdcAttachment tbCdcAttachment = fileService.uploadFile(bytes, exportTask.getId() + ".xlsx");
        log.info("结束上传原始文件");

        String attachmentPath = tbCdcAttachment.getAttachmentPath();
        if ("swift".equals(storageSwitch)) {
            attachmentPath = swiftPrefix.concat(attachmentPath);
        } else {
            attachmentPath = minioPrefix.concat(attachmentPath);
        }
        exportTask.setAttachmentId(tbCdcAttachment.getId());
        exportTask.setAttachmentUrl(attachmentPath);
        exportTask.setAttachmentSize(bytes.length + "");
        exportTask.setUpdateTime(new Date());
        //启用状态 1进行中;2已完成;3任务中断
        exportTask.setStatus(Common.TASK_STATUS_DONE);
        adminServiceApi.updateExportTaskById(exportTask);
    }

    private void runTask(TbCdcmrExportTask exportTask, Long timeOut, Runnable task) {
        log.info("流程开始");
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        Future<?> future = executorService.submit(task);
        try {
            future.get(timeOut, TimeUnit.SECONDS);
            log.info("指定时间内完成了");
        } catch (Exception e) {
            log.error("写文件线程异常", e);
            String errorDesc = e.toString();
            // 修改文件上传任务状态
            exportTask.setUpdateTime(new Date());
            //启用状态 1进行中;2已完成;3任务中断
            exportTask.setStatus(Common.TASK_STATUS_ERROR);
            exportTask.setErrorDesc(errorDesc);
            adminServiceApi.updateExportTaskById(exportTask);
            executorService.shutdownNow();
            future.cancel(true);
        } finally {
            executorService.shutdown();
            log.info("流程finally exportTaskId#{}", exportTask.getId());
        }
        log.info("流程结束 exportTaskId#{}", exportTask.getId());
    }
}
