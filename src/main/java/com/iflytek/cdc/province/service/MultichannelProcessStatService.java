package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ProcessPathogenQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ProcessStatAnalysisQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.*;

import java.util.List;
import java.util.Map;

public interface MultichannelProcessStatService {

    /**
     * 呼吸道传染病发病概况
     * */
    PageInfo<AdsMsProcessRespVO> getEpidemiologicalProfile(ProcessStatAnalysisQueryDTO queryDTO);

    /**
     * 呼吸道传染病发病趋势
     * */
    List<ProcessTimeLineStatVO> getDiseaseTrend(ProcessStatAnalysisQueryDTO queryDTO);

    /**
     * 呼吸道症候群确诊情况
     * */
    List<SyndromeConfirmSituationVO> getConfirmationOfSyndrome(ProcessStatAnalysisQueryDTO queryDTO);

    /**
     * 呼吸道感染病例监测流向
     * */
    List<MonitorProcessFlowVO> getMonitorProcessFlow(ProcessStatAnalysisQueryDTO queryDTO);

    /**
     * 肺炎发生情况
     * */
    PneumoniaOccursSituationVO getPneumoniaOccursSituation(ProcessStatAnalysisQueryDTO queryDTO);

    /**
     * 现存病例量变化
     * */
    List<IndicatorDataVO> getProcessChanges(ProcessStatAnalysisQueryDTO queryDTO);

    /**
     * 性别分布
     * */
    PageInfo<ProcessSexDistributionVO> sexDistribution(ProcessStatAnalysisQueryDTO queryDTO);
    
    /**
     * 分性别发病趋势对比
     * */
    List<ProcessTimeLineStatVO> sexTrendsInDisease(ProcessStatAnalysisQueryDTO queryDTO);
    
    /**
     * 年龄分布
     * */
    PageInfo<ProcessDistributionVO> ageDistribution(ProcessStatAnalysisQueryDTO queryDTO);

    /**
     * 分年龄发病趋势对比
     * */
    List<ProcessTrendVO> ageTrendsInDisease(ProcessStatAnalysisQueryDTO queryDTO);

    /**
     * 发病数同比增长TOP5职业分布
     * */
    PageInfo<ProcessDistributionVO> jobDistribution(ProcessStatAnalysisQueryDTO queryDTO);

    /**
     * 病例人群特征分析
     * */
    PopulationStatVO populationCharacteristics(ProcessStatAnalysisQueryDTO queryDTO);

    /**
     * 行政区分布
     * */
    PageInfo<ProcessDistributionVO> regionDistribution(ProcessStatAnalysisQueryDTO queryDTO);

    /**
     * 分行政区发病趋势对比
     * */
    List<ProcessTrendVO> regionProcessTrends(ProcessStatAnalysisQueryDTO queryDTO);

    /**
     * 传染病城乡分布
     * */
    PageInfo<ProcessUrbanRuralDistributionVO> urbanAndRuralDistribution(ProcessStatAnalysisQueryDTO queryDTO);

    /**
     * 城乡发病趋势对比
     * */
    List<ProcessTrendVO> urbanAndRuralProcessTrends(ProcessStatAnalysisQueryDTO queryDTO);

    /**
     * 病例地区分布
     * */
    List<ProcessAreaDistributionVO> areaDistribution(ProcessStatAnalysisQueryDTO queryDTO);

    /**
     * 发病数同比增长TOP10呼吸道传染病发病季节变化趋势
     * */
    List<DiseaseProcessTimeLineVO> yearOnYearGrowthByDate(ProcessStatAnalysisQueryDTO queryDTO);

    /**
     * 发病数同比增长TOP10呼吸道传染病各地区发病变化趋势
     * */
    List<DiseaseProcessAreaDistributionVO> yearOnYearGrowthByArea(ProcessStatAnalysisQueryDTO queryDTO);

    /**
     * 呼吸道传染病病原阳性概况
     * */
    ProcessPositiveSituationVO listPositiveSituation(ProcessPathogenQueryDTO queryDTO);

    /**
     * 呼吸道传染病病原阳性 时间变化趋势
     * */
    List<ProcessPositiveSituationVO> listPositiveTimeLine(ProcessPathogenQueryDTO queryDTO);

    /**
     * 呼吸道病原阳性率TOP20
     * */
    List<PositivePathogenVO> listTopPositiveRatioBy(ProcessPathogenQueryDTO queryDTO);

    /**
     * 阳性率同比增长TOP20
     * */
    List<PositivePathogenVO> listTopPositiveRatioGrowthBy(ProcessPathogenQueryDTO queryDTO);

    /**
     * 呼吸道传染病 病毒/细菌 病原谱
     * */
    List<PathogenSpectrumCompareVO> listMicrobeAndVirusPathogen(ProcessPathogenQueryDTO queryDTO);

    /**
     * 不同性别各年龄段呼吸道传染病病原阳性率对比（病原范围：病原阳性率TOP10）
     * */
    Map<String, List<PathogenPositiveAgeAndSexVO>> listPositiveRatioByAgeAndSex(ProcessPathogenQueryDTO queryDTO);

    /**
     * 呼吸道传染病病原复合感染率
     * */
    List<PathogenCombinationRateVO> listCombinationInfectionRate(ProcessPathogenQueryDTO queryDTO);

    /**
     * 呼吸道传染病病原感染情况
     * */
    PageInfo<PathogenResultVO> listPathogenInfectionSituation(ProcessPathogenQueryDTO queryDTO);

}
