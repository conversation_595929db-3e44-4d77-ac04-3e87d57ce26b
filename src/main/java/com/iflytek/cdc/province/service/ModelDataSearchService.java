package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.province.model.dto.dm.CombinationQueryDTO;
import com.iflytek.cdc.province.model.dto.dm.KeyWordSearchDTO;
import com.iflytek.cdc.province.model.dto.dm.SuggestSearchDTO;
import com.iflytek.cdc.province.model.vo.SuggestWordVO;

public interface ModelDataSearchService {

    PageInfo<String> retrieveTableSearchByKey(KeyWordSearchDTO dto);

    PageInfo<String> retrieveTableSearchByConditions(CombinationQueryDTO dto);

    PageInfo<SuggestWordVO> getSuggestWords(SuggestSearchDTO dto);
}
