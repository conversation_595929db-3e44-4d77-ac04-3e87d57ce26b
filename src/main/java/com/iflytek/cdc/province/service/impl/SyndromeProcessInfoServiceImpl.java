package com.iflytek.cdc.province.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.adsMsProcess.*;
import com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo;
import com.iflytek.cdc.province.enums.ProcessInfoTypeEnum;
import com.iflytek.cdc.province.mapper.pg.AdsMsProcessInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsMulProcessCaseInfoMapper;
import com.iflytek.cdc.province.model.dto.MsProcessLogQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoConditionQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.vo.*;
import com.iflytek.cdc.province.service.ExportTaskService;
import com.iflytek.cdc.province.service.ProcessInfoServiceBase;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Service
public class SyndromeProcessInfoServiceImpl implements ProcessInfoServiceBase {
    @Resource
    private AdsMsProcessInfoMapper adsMsProcessInfoMapper;
    @Resource
    private ExportTaskService exportTaskService;

    @Resource
    private AdsMulProcessCaseInfoMapper adsMulProcessCaseInfoMapper;


    @Override
    public String getProcessInfoType() {
        return ProcessInfoTypeEnum.SYNDROME.getCode();
    }

    @Override
    public List<MedCntIndicatorVO> listMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listSyndromeMedCntIndicator(queryDTO);
    }

    @Override
    public List<MedCntIndicatorVO> listOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listSyndromeOutcomeIndicator(queryDTO);
    }

    @Override
    public List<AreaMedCntIndicatorVO> listAreaMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listSyndromeAreaMedCntIndicator(queryDTO);
    }

    @Override
    public List<AreaMedCntIndicatorVO> listAreaOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listSyndromeAreaOutcomeIndicator(queryDTO);
    }

    @Override
    public List<MsProcessInfoAddressVO> listAddressByIds(MsProcessSimpleInfoQueryDTO dto) {
        return adsMsProcessInfoMapper.listSyndromeAddressByIds(dto);
    }

    @Override
    public PageInfo<MsProcessSimpleInfoVO> simpleInfoPageList(MsProcessSimpleInfoQueryDTO queryDTO) {

        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return new PageInfo<>(adsMsProcessInfoMapper.listSyndromeSimpleInfo(queryDTO));
    }

    @Override
    public MsProcessSimpleInfoDetailVO loadProcessSimpleById(String id) {
        return adsMsProcessInfoMapper.loadSyndromeSimpleInfo(id);
    }

    @Override
    public TbCdcmrExportTask exportSimpleInfo(MsProcessSimpleInfoQueryDTO dto) {
        ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                dto.getTaskName(),
                dto.getTaskUrl(),
                null,
                dto.getModuleType(),
                dto.getExportType(),
                dto.getExportName());

        return exportTaskService.addAndUploadFile(dto,
                () -> adsMsProcessInfoMapper.listSyndromeSimpleInfo(dto),
                () -> Optional.ofNullable(adsMsProcessInfoMapper.countSyndrome(dto)).orElse(0),
                taskDTO,
                MsSyndromeProcessSimpleInfoExcelVO.class,
                MsSyndromeProcessSimpleInfoExcelVO::of);
    }

    @Override
    public List<MsPatientInfoVO> listPatientInfoByProcessIds(MsProcessSimpleInfoQueryDTO dto) {
        return adsMsProcessInfoMapper.listSynPatientInfoByIds(dto);
    }

    @Override
    public List<MsProcessSimpleInfoVO> listSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listSyndromeSimpleInfo(queryDTO);
    }

    @Override
    public Object listProcessModelSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listSyndromeModelSimpleInfo(queryDTO);
    }

    @Override
    public List<MsProcessLogVO> listProcessLog(MsProcessLogQueryDTO queryDTO) {
        return adsMulProcessCaseInfoMapper.listSyndromeProcessLog(queryDTO);
    }

    @Override
    public List<TbCdcewEmergencyEventProcessInfo> getProcessInfoBy(List<String> ids) {

        return adsMsProcessInfoMapper.getSyndromeProcessInfoBy(ids);
    }

    @Override
    public ProcessPathogenInfoVO getProcessPathogenInfoBy(List<String> ids) {

        return adsMsProcessInfoMapper.getSyndromeProcessPathogenInfoBy(ids);
    }

    @Override
    public PageInfo<MsProcessSimpleInfoVO> syndromeSimpleInfoPageListByConditions(MsProcessSimpleInfoConditionQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return new PageInfo<>(adsMulProcessCaseInfoMapper.syndromeSimpleInfoPageListByConditions(queryDTO));
    }

    @Override
    public PageInfo<PatientTreatmentInfoVO> pageListTreatment(MsProcessSimpleInfoQueryDTO dto) {
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        List<MsProcessSimpleInfoVO> msProcessSimpleInfoVOS = adsMsProcessInfoMapper.listSyndromeSimpleInfo(dto);
        return new PageInfo<>(msProcessSimpleInfoVOS).convert(m -> {
            PatientTreatmentInfoVO patientTreatmentInfoVO = new PatientTreatmentInfoVO();
            BeanUtils.copyProperties(m, patientTreatmentInfoVO);
            return patientTreatmentInfoVO;
        });
    }

    @Override
    public PageInfo<PatientDeathInfoVO> pageListDeath(MsProcessSimpleInfoQueryDTO dto) {
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        List<MsProcessSimpleInfoVO> msProcessSimpleInfoVOS = adsMsProcessInfoMapper.listSyndromeSimpleInfo(dto);
        return new PageInfo<>(msProcessSimpleInfoVOS).convert(m -> {
            PatientDeathInfoVO patientDeathInfoVO = new PatientDeathInfoVO();
            BeanUtils.copyProperties(m, patientDeathInfoVO);
            return patientDeathInfoVO;
        });
    }
}
