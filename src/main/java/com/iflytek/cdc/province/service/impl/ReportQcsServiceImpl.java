package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import com.iflytek.cdc.province.entity.ads.AdsMsInfectReportInfo;
import com.iflytek.cdc.province.entity.bu.ReportQcsCardInfo;
import com.iflytek.cdc.province.entity.bu.ReportQcsMainRecord;
import com.iflytek.cdc.province.entity.bu.ReportQcsStats;
import com.iflytek.cdc.province.entity.bu.ReportQcsUploadInfo;
import com.iflytek.cdc.province.mapper.bu.ReportQcsCardInfoMapper;
import com.iflytek.cdc.province.mapper.bu.ReportQcsMainRecordMapper;
import com.iflytek.cdc.province.mapper.bu.ReportQcsStatsMapper;
import com.iflytek.cdc.province.mapper.bu.ReportQcsUploadInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsMsReportInfoMapper;
import com.iflytek.cdc.province.model.dto.ReportQcsReqDTO;
import com.iflytek.cdc.province.model.vo.ReportQcsStatsVO;
import com.iflytek.cdc.province.service.ReportQcsService;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

@Slf4j
@Service
public class ReportQcsServiceImpl implements ReportQcsService {
    @Resource
    private AdsMsReportInfoMapper adsMsReportInfoMapper;

    @Resource
    private ReportQcsStatsMapper reportQcsStatsMapper;

    @Resource
    private ReportQcsMainRecordMapper reportQcsMainRecordMapper;

    @Resource
    private ReportQcsCardInfoMapper reportQcsCardInfoMapper;

    @Resource
    private ReportQcsUploadInfoMapper reportQcsUploadInfoMapper;

    @Resource
    private BatchUidService batchUidService;
    @Value("${fpva.cdc.batchSize:1000}")
    private int batchSize;

    @Override
    public Boolean compareData(ReportQcsMainRecord mainRecord) {
        //1.查询对比任务主表信息  app.tb_cdcew_report_qcs_main_record 与上传记录app.tb_cdcew_report_qcs_upload_info
        if (Objects.isNull(mainRecord) || Objects.isNull(mainRecord.getId())) {
            return Boolean.FALSE;
        }
        final String mainRecordId = mainRecord.getId();
        UapUserPo uapUserPo = userInfo.get();

        List<ReportQcsStatsVO> statsVOList = new ArrayList<>();

        List<ReportQcsUploadInfo> uploadInfos = reportQcsUploadInfoMapper.selectByMainRecordId(mainRecordId);
        for (ReportQcsUploadInfo uploadInfo : uploadInfos) {
            statsVOList.add(ReportQcsStatsVO.transformUploadInfo(uploadInfo));
        }

        Map<String, ReportQcsUploadInfo> uploadInfoEmpiMap = uploadInfos.stream()
                .filter(v -> StrUtil.isNotBlank(v.getEmpiId()) && StrUtil.isNotBlank(v.getDiseaseCode()))
                .collect(Collectors.toMap(info -> info.getEmpiId() + info.getDiseaseCode(), Function.identity(), (v1, v2) -> v1));

        Map<String, ReportQcsUploadInfo> uploadInfoIdNOMap = uploadInfos.stream()
                .filter(v -> StrUtil.isNotBlank(v.getIdentityNo()) && StrUtil.isNotBlank(v.getDiseaseCode()))
                .collect(Collectors.toMap(info -> info.getIdentityNo() + info.getDiseaseCode(), Function.identity(), (v1, v2) -> v1));



        //2.查询 ads.ads_ms_infect_report_info
        ReportQcsReqDTO reqDTO = new ReportQcsReqDTO();
        BeanUtils.copyProperties(mainRecord, reqDTO);
        reqDTO.setCompareStartDate(DateUtil.beginOfDay(mainRecord.getCompareStartDate()));
        reqDTO.setCompareStartEnd(DateUtil.endOfDay(mainRecord.getCompareStartEnd()));
        List<AdsMsInfectReportInfo> infectReportInfos = adsMsReportInfoMapper.getInfectedReportForQcs(reqDTO);

        //3.对比数据，如果ads.ads_ms_infect_process_info比app.tb_cdcew_report_qcs_upload_info多，则为漏报，保存到漏报表app.tb_cdcew_report_qcs_card_info
        List<ReportQcsCardInfo> cardInfos = new ArrayList<>();
        for (AdsMsInfectReportInfo reportInfo : infectReportInfos) {
            if (StrUtil.isNotBlank(reportInfo.getEmpiId())){
                String uid = reportInfo.getEmpiId() + reportInfo.getInfectCode();
                if (Objects.isNull(uploadInfoEmpiMap.get(uid))) {
                    String tableId = String.valueOf(batchUidService.getUid(ReportQcsCardInfo.TB_NAME));
                    ReportQcsCardInfo cardInfo = ReportQcsCardInfo.transformInfectReport(reportInfo, mainRecordId, uapUserPo, tableId);
                    cardInfos.add(cardInfo);
                    //漏报数量
                    statsVOList.add(ReportQcsStatsVO.transformQcsCardInfo(cardInfo));
                }
            } else if (StrUtil.isNotBlank(reportInfo.getPatientIdentityNo())){
                String uid = reportInfo.getPatientIdentityNo() + reportInfo.getInfectCode();
                if (Objects.isNull(uploadInfoIdNOMap.get(uid))) {
                    String tableId = String.valueOf(batchUidService.getUid(ReportQcsCardInfo.TB_NAME));
                    ReportQcsCardInfo cardInfo = ReportQcsCardInfo.transformInfectReport(reportInfo, mainRecordId, uapUserPo, tableId);
                    cardInfos.add(cardInfo);
                    //漏报数量
                    statsVOList.add(ReportQcsStatsVO.transformQcsCardInfo(cardInfo));
                }
            }

        }
        if (CollUtil.isNotEmpty(cardInfos)) {
            reportQcsCardInfoMapper.deleteByMainRecordId(mainRecordId);
            Lists.partition(cardInfos, batchSize).forEach(infos -> {
                reportQcsCardInfoMapper.mergeInto(infos);
            });
            reportQcsMainRecordMapper.updateLeakCntById(mainRecordId,cardInfos.size());
        }

        //4.统计数据插入到app.tb_cdcew_report_qcs_stats
        List<ReportQcsStats> statsList = new ArrayList<>();
        if (CollUtil.isNotEmpty(statsVOList)) {
            Map<String, List<ReportQcsStatsVO>> listMap = statsVOList.stream().collect(Collectors.groupingBy(ReportQcsStatsVO::generateGroupStr));

            listMap.forEach((k, v) -> {
                ReportQcsStatsVO statsVO = v.get(0);
                ReportQcsStats stats = new ReportQcsStats();
                String tableId = String.valueOf(batchUidService.getUid(ReportQcsStats.TB_NAME));
                stats.setId(tableId);
                if (Objects.nonNull(uapUserPo)){
                    stats.setCreatorId(uapUserPo.getId());
                    stats.setCreator(uapUserPo.getName());
                    stats.setUpdaterId(uapUserPo.getId());
                    stats.setUpdater(uapUserPo.getName());
                }
                stats.setCreateTime(new Date());
                stats.setUpdateTime(new Date());
                stats.setDeleteFlag("0");


                BeanUtils.copyProperties(statsVO, stats);
                Long delayCntLong = v.stream().filter(ReportQcsStatsVO::getDelayFlag).count();
                Long leakCntLong = v.stream().filter(ReportQcsStatsVO::getLeakFlag).count();
                stats.setDelayCnt(delayCntLong.intValue());
                stats.setLeakCnt(leakCntLong.intValue());
                stats.setReportCnt(v.size() - leakCntLong.intValue());
                stats.setIdentifyCnt(v.size());



                statsList.add(stats);
            });

            reportQcsStatsMapper.deleteByMainRecordId(mainRecordId);
            Lists.partition(statsList, batchSize).forEach(infos -> {
                reportQcsStatsMapper.mergeInto(infos);
            });
        }

        return Boolean.TRUE;
    }
}
