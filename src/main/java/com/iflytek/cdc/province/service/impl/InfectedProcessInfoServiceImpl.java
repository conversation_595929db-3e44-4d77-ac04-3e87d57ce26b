package com.iflytek.cdc.province.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.adsMsProcess.*;
import com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo;
import com.iflytek.cdc.province.enums.ProcessInfoTypeEnum;
import com.iflytek.cdc.province.mapper.pg.AdsMsProcessInfoMapper;
import com.iflytek.cdc.province.model.dto.MsProcessLogQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoConditionQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.vo.*;
import com.iflytek.cdc.province.service.ExportTaskService;
import com.iflytek.cdc.province.service.ProcessInfoServiceBase;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Service
public class InfectedProcessInfoServiceImpl implements ProcessInfoServiceBase {
    @Resource
    private AdsMsProcessInfoMapper adsMsProcessInfoMapper;

    @Resource
    private ExportTaskService exportTaskService;

    @Override
    public String getProcessInfoType() {
        return ProcessInfoTypeEnum.INFECTED.getCode();
    }

    @Override
    public List<MedCntIndicatorVO> listMedCntIndicator( MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listInfectedMedCntIndicator(queryDTO);
    }

    @Override
    public List<MedCntIndicatorVO> listOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listInfectedOutcomeIndicator(queryDTO);
    }

    @Override
    public List<AreaMedCntIndicatorVO> listAreaMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listInfectedAreaMedCntIndicator(queryDTO);
    }

    @Override
    public List<AreaMedCntIndicatorVO> listAreaOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listInfectedAreaOutcomeIndicator(queryDTO);
    }


    @Override
    public List<MsProcessInfoAddressVO> listAddressByIds(MsProcessSimpleInfoQueryDTO dto) {

        return adsMsProcessInfoMapper.listInfectedAddressByIds(dto);
    }

    @Override
    public PageInfo<MsProcessSimpleInfoVO> simpleInfoPageList(MsProcessSimpleInfoQueryDTO queryDTO) {

        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return new PageInfo<>(adsMsProcessInfoMapper.listInfectedSimpleInfo(queryDTO));
    }

    @Override
    public MsProcessSimpleInfoDetailVO loadProcessSimpleById(String id) {
        return adsMsProcessInfoMapper.loadInfectedSimpleInfo(id);
    }

    @Override
    public TbCdcmrExportTask exportSimpleInfo( MsProcessSimpleInfoQueryDTO dto) {
        ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                dto.getTaskName(),
                dto.getTaskUrl(),
                null,
                dto.getModuleType(),
                dto.getExportType(),
                dto.getExportName());

        return exportTaskService.addAndUploadFile(dto,
                () -> adsMsProcessInfoMapper.listInfectedSimpleInfo(dto),
                () -> Optional.ofNullable(adsMsProcessInfoMapper.countInfected(dto)).orElse(0),
                taskDTO,
                MsInfectedProcessSimpleInfoExcelVO.class,
                MsInfectedProcessSimpleInfoExcelVO::of);
    }

    @Override
    public List<MsPatientInfoVO> listPatientInfoByProcessIds(MsProcessSimpleInfoQueryDTO dto) {
        return adsMsProcessInfoMapper.listInfPatientInfoByIds(dto);
    }

    @Override
    public List<MsProcessSimpleInfoVO> listSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listInfectedSimpleInfo(queryDTO);
    }

    @Override
    public List<InfectedProcessModelSimpleInfo> listProcessModelSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listInfectedModelSimpleInfo(queryDTO);
    }

    @Override
    public List<MsProcessLogVO> listProcessLog(MsProcessLogQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listInfectedProcessLog(queryDTO);
    }

    @Override
    public List<TbCdcewEmergencyEventProcessInfo> getProcessInfoBy(List<String> ids) {

        return adsMsProcessInfoMapper.getInfectedProcessInfoBy(ids);
    }

    @Override
    public ProcessPathogenInfoVO getProcessPathogenInfoBy(List<String> ids) {

        return adsMsProcessInfoMapper.getInfectedProcessPathogenInfoBy(ids);
    }

    @Override
    public PageInfo<MsProcessSimpleInfoVO> syndromeSimpleInfoPageListByConditions(MsProcessSimpleInfoConditionQueryDTO queryDTO) {
        return null;
    }

}
