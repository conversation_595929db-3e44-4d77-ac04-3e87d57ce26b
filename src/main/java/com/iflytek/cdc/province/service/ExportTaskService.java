package com.iflytek.cdc.province.service;


import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;

import java.util.List;
import java.util.function.Function;
import java.util.function.IntSupplier;
import java.util.function.Supplier;

/**
 * <p>
 * 导出记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
public interface ExportTaskService {

    /**
     * 添加导出任务并执行，提前准备数据生成规则，先同步生成数据，后异步生成单 sheet 页的 excel 文件并上传。
     * 注意 <code>dataSupplier</code> 并没有异步执行。
     */
    <K, T> TbCdcmrExportTask addAndUploadFile(Object param, Supplier<List<K>> dataSupplier, IntSupplier count, ExportTaskDTO taskDTO, Class<T> excelClass, Function<K, T> convertFunction);

    /**
     * 执行导出任务，提前准备数据，异步生成单 sheet 页的 excel 文件并上传。
     */
    <M, N> void runTaskAndUploadFile(List<N> dataList, TbCdcmrExportTask exportTask, Class<M> excelClass);

    /**
     * 执行导出任务，提前准备任意格式的文件数据，异步上传。
     */
    void runTaskAndUploadFile(byte[] bytes, TbCdcmrExportTask exportTask);

    /**
     * 执行导出任务，提前准备文件数据生成规则，异步生成数据并上传。
     * 注意 <code>bytesSupplier</code> 会异步执行。
     * 适用于批量导出多Sheet页的模型明细 excel 文件。
     */
    void runTaskAndUploadFile(Supplier<byte[]> bytesSupplier, TbCdcmrExportTask exportTask);
}
