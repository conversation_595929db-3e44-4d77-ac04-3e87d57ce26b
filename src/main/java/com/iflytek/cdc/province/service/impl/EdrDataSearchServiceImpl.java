package com.iflytek.cdc.province.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.github.pagehelper.PageHelper;
import com.iflytek.cdc.province.model.vo.PatientTransferInfoVO;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.utils.DictUtils;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO;
import com.iflytek.cdc.province.mapper.pg.AdsEdrDataSearchMapper;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.edr.vo.ClinicalInfo;
import com.iflytek.cdc.province.model.edr.vo.DiagnosisResultInfo;
import com.iflytek.cdc.province.model.edr.vo.ExaminationInfo;
import com.iflytek.cdc.province.model.edr.vo.ImagingInfo;
import com.iflytek.cdc.province.model.edr.vo.LabTestInfo;
import com.iflytek.cdc.province.model.edr.vo.MedicalPatientDetailInfo;
import com.iflytek.cdc.province.model.edr.vo.SymptomInfo;
import com.iflytek.cdc.province.model.edr.vo.TherapeuticDrugInfo;
import com.iflytek.cdc.province.model.edr.vo.TherapeuticDrugInfoDetail;
import com.iflytek.cdc.province.model.vo.EdrIdentityVO;
import com.iflytek.cdc.province.service.CommonProcessInfoService;
import com.iflytek.cdc.province.service.EdrDataSearchService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;

@Service
public class EdrDataSearchServiceImpl implements EdrDataSearchService {

    @Resource
    private AdsEdrDataSearchMapper adsEdrDataSearchMapper;

    @Resource
    private DictUtils dictUtils;

    @Resource
    private CommonProcessInfoService commonProcessInfoService;

    @Override
    public PageInfo<MsProcessSimpleInfoVO> pageList(String processType, MsProcessSimpleInfoQueryDTO queryDTO) {
        return commonProcessInfoService.simpleInfoPageList(processType, queryDTO);
    }

    @Override
    public MedicalPatientDetailInfo loadByPatientId(String patientId, List<String> eventIds) {
        MedicalPatientDetailInfo medicalPatientDetailInfo = adsEdrDataSearchMapper.loadByPatientId(patientId);
        if (medicalPatientDetailInfo != null){
            dictUtils.mapFields(medicalPatientDetailInfo);
            List<ClinicalInfo> clinicalInfos = adsEdrDataSearchMapper.listClinicalInfos(eventIds);
            if (clinicalInfos.size() > 0){
                medicalPatientDetailInfo.setClinicalInfos(clinicalInfos);
            }
            fetchClinicalInfo(clinicalInfos);
        }
        return medicalPatientDetailInfo;
    }

    /**
     * 分组查询
     */
    private <T> Map<String, List<T>> groupingBy(List<T> data, Function<T, String> function){
        return data.stream().collect(Collectors.groupingBy(function));
    }

    /**
     * 异步执行，增加查询速度
     */
    public void fetchClinicalInfo(List<ClinicalInfo> clinicalInfos){
        List<String> eventIds = clinicalInfos.stream().map(ClinicalInfo::getEventId).collect(Collectors.toList());
        CompletableFuture<List<ExaminationInfo>> examinationInfoFuture = CompletableFuture.supplyAsync(() -> adsEdrDataSearchMapper.listExaminationInfoByEventIds(eventIds));
        CompletableFuture<List<SymptomInfo>> symptomInfoFuture = CompletableFuture.supplyAsync(() -> adsEdrDataSearchMapper.listSymptomInfoByEventIds(eventIds));
        CompletableFuture<List<ImagingInfo>> imagingInfoFuture = CompletableFuture.supplyAsync(() -> adsEdrDataSearchMapper.listImagingInfoByEventIds(eventIds));
        CompletableFuture<List<LabTestInfo>> labTestInfoFuture = CompletableFuture.supplyAsync(() -> adsEdrDataSearchMapper.listLabTestInfoByEventIds(eventIds));
        CompletableFuture<List<DiagnosisResultInfo>> diagnosisResultInfoFuture = CompletableFuture.supplyAsync(() -> adsEdrDataSearchMapper.listDiagnosisResultInfoByEventIds(eventIds));
        CompletableFuture<List<TherapeuticDrugInfo>> therapeuticDrugInfoFuture = CompletableFuture.supplyAsync(() -> adsEdrDataSearchMapper.listTherapeuticDrugInfoByEventIds(eventIds));
        CompletableFuture<List<TherapeuticDrugInfoDetail>> therapeuticDrugInfoDetailFuture = CompletableFuture.supplyAsync(() -> adsEdrDataSearchMapper.listTherapeuticDrugInfoDetailByPatientId(eventIds));

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(symptomInfoFuture,
                examinationInfoFuture,
                imagingInfoFuture,
                labTestInfoFuture, 
                diagnosisResultInfoFuture,
                therapeuticDrugInfoFuture, 
                therapeuticDrugInfoDetailFuture);

        allFutures.thenRun(() -> {
            try {
                // 获取并处理所有数据
                List<SymptomInfo> symptomInfos = symptomInfoFuture.get();
                Map<String, List<SymptomInfo>> symptomInfoMap = groupingBy(symptomInfos, SymptomInfo::getEventId);
                
                List<ImagingInfo> imagingInfos = imagingInfoFuture.get();
                Map<String, List<ImagingInfo>> imagingInfoMap = groupingBy(imagingInfos, ImagingInfo::getEventId);
                
                List<LabTestInfo> labTestInfos = labTestInfoFuture.get();
                Map<String, List<LabTestInfo>> labTestInfoMap = groupingBy(labTestInfos, LabTestInfo::getEventId);
                
                List<DiagnosisResultInfo> diagnosisResultInfos = diagnosisResultInfoFuture.get();
                Map<String, List<DiagnosisResultInfo>> diagnosisResultMap = groupingBy(diagnosisResultInfos, DiagnosisResultInfo::getEventId);

                List<TherapeuticDrugInfo> therapeuticDrugInfos = therapeuticDrugInfoFuture.get();
                Map<String, List<TherapeuticDrugInfo>> therapeuticDrugMap = groupingBy(therapeuticDrugInfos, TherapeuticDrugInfo::getEventId);

                List<TherapeuticDrugInfoDetail> therapeuticDrugInfoDetails = therapeuticDrugInfoDetailFuture.get();
                Map<String, List<TherapeuticDrugInfoDetail>> therapeuticDrugInfoDetailMap = groupingBy(therapeuticDrugInfoDetails, TherapeuticDrugInfoDetail::getEdrTreatmentDrugInfoId);

                clinicalInfos.forEach(c -> {
                    c.setSymptomInfos(symptomInfoMap.get(c.getEventId()));
                    c.setImagingInfos(imagingInfoMap.get(c.getEventId()));
                    c.setLabTestInfos(labTestInfoMap.get(c.getEventId()));
                    c.setDiagnosisResultInfos(diagnosisResultMap.get(c.getEventId()));
                    c.setTherapeuticDrugInfos(therapeuticDrugMap.get(c.getEventId()));
                    List<TherapeuticDrugInfoDetail> details = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(c.getTherapeuticDrugInfos())){
                        c.getTherapeuticDrugInfos().forEach(drugInfo -> {
                            if (therapeuticDrugInfoDetailMap.get(drugInfo.getEdrTreatmentDrugInfoId()) != null){
                                details.addAll(therapeuticDrugInfoDetailMap.get(drugInfo.getEdrTreatmentDrugInfoId()));
                            }
                        });
                    }
                    c.setTherapeuticDrugInfoDetails(details);
                });

            } catch (Exception e) {
                throw new MedicalBusinessException("获取诊疗信息数据异常");
            }
        }).join(); // 等待所有任务完成
    }
    
    @Override
    public EdrIdentityVO findEdrIdentityByEmpiId(String empiId, String residentIdCard) {
        return adsEdrDataSearchMapper.findEdrIdentityByEmpiId(empiId, residentIdCard);
    }

    @Override
    public PageInfo<PatientTransferInfoVO> pageListTransfer(MsProcessSimpleInfoQueryDTO dto) {
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return new PageInfo<>(adsEdrDataSearchMapper.listPatientTransferInfo(dto));
    }
}
