package com.iflytek.cdc.province.service.impl.es;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch.core.search.FieldCollapse;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.iflytek.cdc.edr.configuration.EsModelConfigProperties;
import com.iflytek.cdc.edr.enums.LogicEnum;
import com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormTemplate;
import com.iflytek.cdc.province.enums.OperatorTypeEnum;
import com.iflytek.cdc.province.es.ElasticsearchUtils;
import com.iflytek.cdc.province.es.EsQueryParam;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmDataFormTemplateMapper;
import com.iflytek.cdc.province.model.dto.dm.CombinationQueryDTO;
import com.iflytek.cdc.province.model.dto.dm.ConfigFieldsDTO;
import com.iflytek.cdc.province.model.dto.dm.DataSearchCommonQuery;
import com.iflytek.cdc.province.model.dto.dm.KeyWordSearchDTO;
import com.iflytek.cdc.province.model.dto.dm.SuggestSearchDTO;
import com.iflytek.cdc.province.model.vo.FieldsConfigAnalysisVO;
import com.iflytek.cdc.province.model.vo.SuggestWordVO;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.service.DataModelCommonUtils;
import com.iflytek.cdc.province.service.ModelDataSearchService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@ConditionalOnProperty(name = EsModelConfigProperties.PROP_ENABLED, havingValue = "true")
public class EsModelModelDataSearchServiceImpl implements ModelDataSearchService {

    @Resource
    private TbCdcdmDataFormTemplateMapper templateMapper;

    @Resource
    private ElasticsearchUtils elasticsearchUtils;

    @Resource
    private EsModelConfigProperties esModelConfigProperties;

    @Resource
    private DataModelCommonUtils dataModelCommonUtils;

    /**
     * 关键词搜索
     *
     * @param dto 包含关键字的KeyWordSearchDTO对象。
     * @return 返回一个PageInfo<String>对象，其中包含了根据关键字搜索到的结果。
     */
    @Override
    public PageInfo<String> retrieveTableSearchByKey(KeyWordSearchDTO dto) {
        final String keyWord = dto.getKeyWord();
        return getResult(dto, (commonQuery, template) -> {
            BoolQuery.Builder builder = createQueryBuilderByTemplate(commonQuery, template);
            if (StrUtil.isNotEmpty(keyWord)) {
                builder.must(ElasticsearchUtils.buildSimpleQuery(OperatorTypeEnum.INCLUDE, ElasticsearchUtils.FULL_TEXT_FIELD, keyWord));
            }
            return builder.build()._toQuery();
        });
    }

    /**
     * 组合条件搜索
     *
     * @param dto 组合查询数据传输对象，包含条件列表、模型ID、页面索引和页面大小等信息。
     * @return 返回一个PageInfo<String>对象，其中包含了根据给定条件检索到的表搜索结果。
     */
    @Override
    public PageInfo<String> retrieveTableSearchByConditions(CombinationQueryDTO dto) {
        List<ConfigFieldsDTO> configFields = dataModelCommonUtils.buildInputFilterListBy(dto.getConditionDTOList());
        configFields.forEach(c -> c.setField(String.join(".", c.getModelId(), c.getFormId(), c.getGroupId(), c.getFieldId())));

        return getResult(dto, (commonQuery, template) -> {
            BoolQuery.Builder builder = createQueryBuilderByTemplate(commonQuery, template);
            if (CollUtil.isNotEmpty(configFields)) {
                builder.must(buildQuery(configFields, template.getModelId()));
            }
            return builder.build()._toQuery();
        });
    }

    @Override
    public PageInfo<SuggestWordVO> getSuggestWords(SuggestSearchDTO dto) {
        String indexName = esModelConfigProperties.getSuggest().getIndex();

        // 使用普通查询而非es自动补全功能，方便自定义排序
        Query query = ElasticsearchUtils.buildSimpleQuery(OperatorTypeEnum.INCLUDE, SuggestWordVO.FIELD_TAG_VALUE, dto.getText());

        // 联想词展示排序策略：
        // 优先级1：按照搜索到的联想词的字数从小到大排序；
        // 优先级2：字数相同时，按照实体label排序，传染病>症候群>症状
        // 优先级3：字数相同&实体label相同时，按照搜索匹配的相似度从高到低排序。自定义排序
        List<SortOptions> sortOptions = new ArrayList<>(1);
        sortOptions.add(SortOptions.of(so -> so.field(fs -> fs.field(SuggestWordVO.FIELD_VALUE_LENGTH).order(SortOrder.Asc))));
        sortOptions.add(SortOptions.of(so -> so.field(fs -> fs.field(SuggestWordVO.FIELD_TAG_ORDER).order(SortOrder.Asc))));
        sortOptions.add(SortOptions.of(so -> so.score(ss -> ss.order(SortOrder.Desc))));

        return elasticsearchUtils.pageList(EsQueryParam.builder().indexName(indexName)
                        .query(query)
                        .sortOptions(sortOptions)
                        .searchAfter(dto.getSearchAfter())
                        .from((dto.getPageIndex() - 1) * dto.getPageSize())
                        .size(dto.getPageSize())
                        .build(),
                SuggestWordVO.class);
    }

    /**
     * 根据模板构建查询。
     *
     * @param commonQuery 通用查询条件。
     * @param template    数据表单模板。
     * @return 返回一个BoolQuery.Builder对象，该对象包含了根据模板解析出的查询条件。如果模板中的字段配置为空或者没有配置检索过滤器，则返回一个空的BoolQuery.Builder对象。
     */
    private BoolQuery.Builder createQueryBuilderByTemplate(DataSearchCommonQuery commonQuery, TbCdcdmDataFormTemplate template) {
        // 解析配置字段
        BoolQuery.Builder queryBuilder = new BoolQuery.Builder();
        // 附加权限过滤
        ElasticsearchUtils.appendRegionPrivilege(queryBuilder, CommonUtilService.buildPrivilegeParam(commonQuery));
        // 公共字段
        if (StrUtil.isNotBlank(commonQuery.getDiseaseCode())) {
            queryBuilder.filter(ElasticsearchUtils.buildTerm(ElasticsearchUtils.DISEASE_CODE, commonQuery.getDiseaseCode()));
        }
        if (StrUtil.isNotBlank(commonQuery.getProcessType())) {
            queryBuilder.filter(ElasticsearchUtils.buildTerm(ElasticsearchUtils.PROCESS_TYPE, commonQuery.getProcessType()));
        }
        queryBuilder.filter(ElasticsearchUtils.buildTerm(ElasticsearchUtils.DELETE_FLAG, "0"));

        Gson gson = new Gson();
        FieldsConfigAnalysisVO analysisVO = gson.fromJson(template.getFieldsConfig(), FieldsConfigAnalysisVO.class);
        if (analysisVO == null) {
            return queryBuilder;
        }
        List<Query> queries = new ArrayList<>();
        List<ConfigFieldsDTO> configFieldsDTOList = dataModelCommonUtils.buildConfigFilter(analysisVO.getRetrieveFilter());
        if (configFieldsDTOList.isEmpty()) {
            return queryBuilder;
        }
        configFieldsDTOList.forEach(e -> ElasticsearchUtils.appendQuery(queries, e.getOperator(), e.getField(), e.getValue()));
        return builderLogic(configFieldsDTOList.get(0).getLogic(), queryBuilder, queries);
    }

    /**
     * 根据配置列表构建查询。
     *
     * @param inputFilterList 输入的过滤条件列表，每个元素包含操作符、字段和值。
     * @return 返回根据输入过滤条件列表构建的查询对象。
     */
    private Query buildQueryByConfigs(List<ConfigFieldsDTO> inputFilterList) {
        List<Query> queries = new ArrayList<>();
        inputFilterList.forEach(e -> ElasticsearchUtils.appendQuery(queries, e.getOperator(), e.getField(), e.getValue()));
        return builderLogic(inputFilterList.get(0).getLogic(), new BoolQuery.Builder(), queries).build()._toQuery();
    }

    /**
     * 根据逻辑枚举值构建布尔查询。
     *
     * @param logicEnum        逻辑枚举值，包括AND和OR两种类型。
     * @param boolQueryBuilder 布尔查询构造器。
     * @param queries          查询列表。
     * @return 返回根据逻辑枚举值构建的布尔查询构造器。
     */
    private BoolQuery.Builder builderLogic(LogicEnum logicEnum,
                                           BoolQuery.Builder boolQueryBuilder,
                                           List<Query> queries) {
        switch (logicEnum) {
            case AND:
                boolQueryBuilder.must(queries);
                break;
            case OR:
                boolQueryBuilder.should(queries);
                break;
            case NONE:
                // 对于 NONE 逻辑，不添加任何查询条件
                break;
        }
        return boolQueryBuilder;
    }


    /**
     * 构建查询。
     *
     * @param configFieldsDTOS 配置字段DTO列表。
     * @param modelId          模型主键
     * @return 返回构建的查询对象。
     */
    private Query buildQuery(List<ConfigFieldsDTO> configFieldsDTOS, String modelId) {
        List<Query> queries = new ArrayList<>();
        Map<String, Map<String, List<ConfigFieldsDTO>>> configFieldMap = configFieldsDTOS.stream()
                .collect(Collectors.groupingBy(ConfigFieldsDTO::getFormId,
                        Collectors.groupingBy(ConfigFieldsDTO::getGroupId)));
        LogicEnum logicEnum = configFieldsDTOS.get(0).getLogic();
        configFieldMap.forEach((formId, groupMap) -> {
            List<Query> groupQueries = new ArrayList<>();
            groupMap.forEach((groupId, configs) -> {
                Query fieldQuery = QueryBuilders.nested(q -> q.path(String.join(".", modelId, formId, groupId))
                        .query(buildQueryByConfigs(configs)));
                groupQueries.add(fieldQuery);

            });
            Query query = builderLogic(logicEnum, new BoolQuery.Builder(), groupQueries).build()._toQuery();
            queries.add(QueryBuilders.nested(q -> q.path(String.join(".", modelId, formId))
                    .query(query)));
        });
        return builderLogic(logicEnum, new BoolQuery.Builder(), queries).build()._toQuery();
    }

    /**
     * 获取结果。
     *
     * @param dto        查询参数。
     * @param queryBuild 查询构建器。
     * @return 返回一个包含字符串的PageInfo对象，如果模板不存在或未配置ES索引，则抛出MedicalBusinessException异常。
     */
    private PageInfo<String> getResult(DataSearchCommonQuery dto, QueryBuild queryBuild) {
        final String modelId = dto.getModelId();
        TbCdcdmDataFormTemplate template = templateMapper.getModelConfigByModelId(modelId);
        if (template == null) {
            throw new MedicalBusinessException("模型不存在");
        }
        if (StrUtil.isEmpty(template.getEsIndexName())) {
            throw new MedicalBusinessException("模型未配置ES索引");
        }
        // 默认倒排
        List<SortOptions> sortOptions = new ArrayList<>(1);
        sortOptions.add(SortOptions.of(so -> so.score(ss -> ss.order(SortOrder.Desc))));
        sortOptions.add(SortOptions.of(so -> so.field(fs -> fs.field(ElasticsearchUtils.INGEST_TIME).order(SortOrder.Desc))));

        Gson gson = new Gson();
        Object[] searchAfter = dto.getSearchAfter();
        Integer pageIndex = dto.getPageIndex();
        Integer pageSize = dto.getPageSize();
        return elasticsearchUtils.pageList(EsQueryParam.builder().indexName(template.getEsIndexName())
                        .query(queryBuild.build(dto, template))
                        .sortOptions(sortOptions)
                        .collapse(FieldCollapse.of(fc -> fc.field(ElasticsearchUtils.INGEST_ID)))
                        .searchAfter(searchAfter)
                        .from((pageIndex - 1) * pageSize)
                        .size(pageSize)
                        .build(),
                Map.class).convert(e -> {
            // 删除工具字段
            ElasticsearchUtils.removeCommonFields(e);
            // 添加脱敏逻辑
            String json = gson.toJson(e);
            return dataModelCommonUtils.desensitizationJson(modelId, json);
        });
    }

    public interface QueryBuild {

        /**
         * 构建查询。
         *
         * @param commonQuery 通用查询条件。
         * @param template    数据表单模板。
         * @return 返回一个Query对象，该对象是根据输入的过滤列表和数据表单模板构建的。
         */
        Query build(DataSearchCommonQuery commonQuery, TbCdcdmDataFormTemplate template);
    }
}
