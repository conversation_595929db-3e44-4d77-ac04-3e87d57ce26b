package com.iflytek.cdc.province.service;

import java.util.List;

import org.springframework.http.ResponseEntity;

import com.iflytek.cdc.province.model.pathogen.PathogenAnalysisReqDTO;
import com.iflytek.cdc.province.model.pathogen.PathogenMultiClassStatVO;
import com.iflytek.cdc.province.model.pathogen.PathogenNameGroupVO;
import com.iflytek.cdc.province.model.pathogen.PathogenTwofoldStatVO;

/**
 * 病原相关分析统计接口类
 */
public interface PathogenAnalysisService {

    /**
     * 病原谱分析-病原阳性结果-按病原体名称分组
     * 
     * @param req           请求参数
     * @param loginUserName 登录用户名
     * @return 病原体名称分组统计结果
     */
    List<PathogenNameGroupVO> groupPositiveByName(PathogenAnalysisReqDTO req, String loginUserName);

    /**
     * 病原谱分析-病原复合感染-按病原体名称分组
     * 
     * @param req           请求参数
     * @param loginUserName 登录用户名
     * @return 病原体复合感染分组统计结果
     */
    List<PathogenTwofoldStatVO> groupTwofoldByName(PathogenAnalysisReqDTO req, String loginUserName);

    /**
     * 病原谱分析-病原多重感染情况-按病原体名称分组
     * 
     * @param req           请求参数
     * @param loginUserName 登录用户名
     * @return 病原体多重感染分组统计结果
     */
    List<PathogenMultiClassStatVO> groupMultiClassByName(PathogenAnalysisReqDTO req, String loginUserName);

    /**
     * 病原谱分析-病原多重感染情况-按病原体名称分组-导出
     * 
     * @param req           请求参数
     * @param loginUserName 登录用户名
     * @return 导出文件
     */
    ResponseEntity<byte[]> groupMultiClassByNameExport(PathogenAnalysisReqDTO req, String loginUserName);
}
