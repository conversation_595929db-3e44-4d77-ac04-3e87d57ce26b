package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.WarningSignalVO;
import com.iflytek.cdc.province.model.dto.dm.DataCollectionStatusDTO;
import com.iflytek.cdc.province.model.dto.dm.FavoriteQueryDTO;
import com.iflytek.cdc.province.model.dto.dm.FavoritesDetailsQueryDTO;
import com.iflytek.cdc.province.model.dto.dm.FavoritesEditDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.edr.vo.dm.FavoritesListVO;

public interface DataCollectionService {

    PageInfo<FavoritesListVO> getFavoritesList(FavoriteQueryDTO dto);

    void editFavorites(FavoritesEditDTO dto);

    void deleteFavorites(String favoritesIde);

    PageInfo<String> getFavoritesDetails(FavoritesDetailsQueryDTO dto);

    void batchDataCollection(DataCollectionStatusDTO dto);

    void batchDataUnCollection(DataCollectionStatusDTO dto);

    TbCdcmrExportTask getFavoritesDetailsExport(FavoritesDetailsQueryDTO dto);

    void checkDataAuth(String loginUserId, String id);

    PageInfo<WarningSignalVO> getSignalFavoritesDetails(FavoritesDetailsQueryDTO dto, String loginUserId);

    TbCdcmrExportTask signalFavoritesDetailsExport(FavoritesDetailsQueryDTO dto, String loginUserId);

}
