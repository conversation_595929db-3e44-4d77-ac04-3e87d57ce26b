package com.iflytek.cdc.province.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.cdc.province.mapper.pg.BizEderInfoMapper;
import com.iflytek.cdc.province.entity.ads.AdsBizEdrInfo;
import com.iflytek.cdc.province.service.BizEdrInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

@Service
@Slf4j
public class BizEdrInfoServiceImpl implements BizEdrInfoService {

    @Resource
    private BizEderInfoMapper bizEderInfoMapper;
    @Override
    public AdsBizEdrInfo getById(String archiveId) {
        return bizEderInfoMapper.selectById(archiveId);
    }

    @Override
    public List<HashMap<String,Object>> getBizEdrInfoByNameAndCard(List<HashMap<String, String>> dtoList) {
        return bizEderInfoMapper.getBizEdrInfoByNameAndCard(dtoList);
    }
}
