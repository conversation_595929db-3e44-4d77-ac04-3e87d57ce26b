package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.dto.DictParams;
import com.iflytek.cdc.province.model.vo.DictValueVO;

import java.util.List;

public interface DimDictService {
    DictValueVO getDictBy(String dictCode, String dictValueCode);

    List<DictValueVO> getDictValueBy(String dictCode);

    PageInfo<DictValueVO> searchDictValueBy(DictParams dictParams);
}
