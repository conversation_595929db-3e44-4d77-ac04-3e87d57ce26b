package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.InOrgStatusTtlVO;
import com.iflytek.cdc.edr.vo.OrgDetailInfoVO;
import com.iflytek.cdc.province.model.dto.DiInOrgQueryDTO;
import com.iflytek.cdc.province.entity.ads.AdsDiInorgInfo;
import com.iflytek.cdc.edr.vo.AreaInOrgCountVO;
import com.iflytek.cdc.edr.vo.AreaInOrgStatisticVO;
import com.iflytek.cdc.edr.vo.InOrgTypeCountVO;
import com.iflytek.cdc.province.model.vo.TimeTrendVO;
import org.springframework.http.ResponseEntity;

import java.util.Date;
import java.util.List;

/**
 * 接入机构服务
 */
public interface AdsDiInorgService {
    PageInfo<AdsDiInorgInfo> queryBy(DiInOrgQueryDTO queryDTO, String loginUserName);

    List<AreaInOrgCountVO> areaCountTop10(DiInOrgQueryDTO queryDTO, String loginUserName);

    PageInfo<AreaInOrgStatisticVO> areaStatistic(DiInOrgQueryDTO queryDTO, String loginUserName);

    /**
     * 查询区域下的具体机构信息
     */
    PageInfo<OrgDetailInfoVO> queryOrgDetails(DiInOrgQueryDTO queryDTO, String loginUserName);

    /**
     * 查询区域下的机构对接状态分组信息
     */
    InOrgStatusTtlVO queryOrgConnectionStatus(DiInOrgQueryDTO queryDTO, String loginUserName);

    /**
     * 导出各区域监测覆盖度
     * */
    ResponseEntity<byte[]> areaStatisticExport(DiInOrgQueryDTO queryDTO, String loginUserName);

    /**
     * 接入机构统计
     */
    List<InOrgTypeCountVO> inOrgTypeCount(DiInOrgQueryDTO queryDTO, String loginUserName);

    /**
     * 机构接入时间趋势统计
     */
    List<TimeTrendVO> inOrgTypeTimeTrend(DiInOrgQueryDTO queryDTO);

    /**
     * 获取统计的截止日期
     */
    Date getLastStatData();

    /**
     * 查询医疗机构类型(去重)
     */
    List<AdsDiInorgInfo> listInorgType(DiInOrgQueryDTO queryDTO, String loginUserName);

}
