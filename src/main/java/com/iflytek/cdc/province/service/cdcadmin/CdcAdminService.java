package com.iflytek.cdc.province.service.cdcadmin;


import com.iflytek.cdc.edr.common.Common;
import com.iflytek.cdc.province.cache.TableInfoCache;
import com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableColumnInfo;
import com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableInfo;
import com.iflytek.cdc.province.entity.bu.TbCdcdmDataModel;
import com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelForm;
import com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelVersion;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmDataModelMapper;
import com.iflytek.cdc.edr.vo.dm.DataModelFormVO;
import com.iflytek.cdc.edr.vo.dm.DataModelMapper;
import com.iflytek.cdc.edr.vo.dm.DataModelVO;
import com.iflytek.cdc.edr.vo.dm.FormGroupTmpl;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmDataModelVersionMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CdcAdminService {

    @Resource
    TableMetadataService tableMetadataService;

    @Resource
    TbCdcdmDataModelMapper tbCdcdmDataModelMapper;

    @Resource
    private TbCdcdmDataModelVersionMapper versionMapper;

    @Resource
    private TableInfoCache tableInfoCache;


    public TbCdcdmMetadataTableColumnInfo selectColumnInfo(String id) {
        return tableMetadataService.selectColumnInfo(id);
    }

    public DataModelVO getDataModelConfig(String modelId) {
        //数据模型信息
        TbCdcdmDataModel tbCdcdmDataModel = tbCdcdmDataModelMapper.selectByPrimaryKey(modelId);
        //一个数据模型 只有一个版本能处于发布状态
        TbCdcdmDataModelVersion modelLatestVersion = tbCdcdmDataModelMapper.getModelLatestVersion(modelId, Common.VERSION_STATUS_PUBLISHED);

        if (tbCdcdmDataModel == null || modelLatestVersion == null) {
            return null;
        }

        DataModelVO dataModelVO = DataModelMapper.INSTANCE.modelToVO(tbCdcdmDataModel, modelLatestVersion);

        //表单信息
        List<TbCdcdmDataModelForm> dataModelForms = tbCdcdmDataModelMapper.getFormListByModelVersionId(modelLatestVersion.getModelVersionId());

        List<DataModelFormVO> dataModelFormVOS = DataModelMapper.INSTANCE.formListToVOList(dataModelForms);

        //解析表单-组-字段信息 TODO 性能优化
        for (DataModelFormVO dataModelFormVO : dataModelFormVOS) {
            for (FormGroupTmpl groupTmpl : dataModelFormVO.getFormGroupList())  {
                for (FormGroupTmpl.FieldRule fieldRule : groupTmpl.getProps().getRules()) {
                    List<String> databaseField = fieldRule.getDatabaseField();
                    if (databaseField != null && databaseField.size() >= 2) {
                        String tableCode = databaseField.get(0);
                        String columnCode = databaseField.get(1);
                        // TODO meta 信息挪到 dm engine中
                        TbCdcdmMetadataTableInfo tableInfo = tableInfoCache.getTableInfoByCache(tableCode);
                        TbCdcdmMetadataTableColumnInfo columnInfo = tableInfoCache.getColumnInfoByCache(columnCode);
                        fieldRule.setTableInfo(tableInfo);
                        fieldRule.setTableColumnInfo(columnInfo);
                    }
                }
            }
        }

        dataModelVO.setFormList(dataModelFormVOS);

        return dataModelVO;
    }

    public DataModelVO getDataModelInfo(String modelId, String modelVersionId){

        //模型id和版本id同时为空 无法查询模型
        if(StringUtils.isBlank(modelId) && StringUtils.isBlank(modelVersionId)){
            return null;
        }
        //模型id为空时，则使用版本id置换模型id
        if(StringUtils.isBlank(modelId)){
            modelId = versionMapper.getModelIdByModelVersionId(modelVersionId);
        }

        //数据模型信息
        TbCdcdmDataModel tbCdcdmDataModel = tbCdcdmDataModelMapper.selectByPrimaryKey(modelId);
        //一个数据模型 只有一个版本能处于发布状态
        TbCdcdmDataModelVersion modelLatestVersion = tbCdcdmDataModelMapper.getModelLatestVersion(modelId, Common.VERSION_STATUS_PUBLISHED);
        if (tbCdcdmDataModel == null || modelLatestVersion == null) {
            return null;
        }

        DataModelVO dataModelVO = DataModelMapper.INSTANCE.modelToVO(tbCdcdmDataModel, modelLatestVersion);

        //表单信息
        List<TbCdcdmDataModelForm> dataModelForms = tbCdcdmDataModelMapper.getFormListByModelVersionId(modelLatestVersion.getModelVersionId());
        dataModelVO.setDataModelForms(dataModelForms);
        return dataModelVO;
    }
}
