package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsInfectCntVO;
import com.iflytek.cdc.province.model.brief.ManualBriefGenerateInfo;
import com.iflytek.cdc.province.model.brief.ManualBriefQueryDTO;
import com.iflytek.cdc.province.model.brief.ManualBriefRangeDTO;
import com.iflytek.cdc.province.model.brief.ManualBriefRecordVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 生成简报记录-服务接口类
 */
public interface ManualBriefRecordService {

    /**
     * 统计方式生成简报记录
     *
     * @param info               生成用的基本信息
     * @param statisticsData     系统内的统计数据
     * @param isGuangxi
     * @param notifiableDiseases 是否法定传染病
     * @return 生成的简报记录
     */
    ManualBriefRecordVO generateByStats(ManualBriefGenerateInfo info, List<MsInfectCntVO> statisticsData, Boolean isGuangxi, boolean notifiableDiseases);

    /**
     * 导入方式生成简报记录
     *
     * @param info       生成用的基本信息
     * @param uploadData 上传的统计文件
     * @return 生成的简报记录
     */
    ManualBriefRecordVO generateByImport(ManualBriefGenerateInfo info, MultipartFile uploadData);

    /**
     * 查询简报记录列表
     *
     * @param queryParam 查询参数
     * @return 简报记录列表
     */
    PageInfo<ManualBriefRecordVO> queryList(ManualBriefQueryDTO queryParam);

    /**
     * 获取指定范围内总的常住人口数
     *
     * @param range 范围查询条件
     * @return 总的常住人口数
     */
    int getTotalPopulation(ManualBriefRangeDTO range);
}
