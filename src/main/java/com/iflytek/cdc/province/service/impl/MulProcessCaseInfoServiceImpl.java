package com.iflytek.cdc.province.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.utils.DateFormatUtil;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsInfectedProcessSimpleInfoExcelVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoDetailVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO;
import com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo;
import com.iflytek.cdc.province.enums.ProcessInfoTypeEnum;
import com.iflytek.cdc.province.mapper.pg.AdsMulProcessCaseInfoMapper;
import com.iflytek.cdc.province.model.dto.MsProcessLogQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoConditionQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.vo.AreaMedCntIndicatorVO;
import com.iflytek.cdc.province.model.vo.InfectedProcessModelSimpleInfo;
import com.iflytek.cdc.province.model.vo.MedCntIndicatorVO;
import com.iflytek.cdc.province.model.vo.MsPatientInfoVO;
import com.iflytek.cdc.province.model.vo.ProcessPathogenInfoVO;
import com.iflytek.cdc.province.service.ExportTaskService;
import com.iflytek.cdc.province.service.ProcessInfoServiceBase;
import com.iflytek.cdc.province.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class MulProcessCaseInfoServiceImpl implements ProcessInfoServiceBase {
    @Autowired
    private AdsMulProcessCaseInfoMapper adsMsProcessInfoMapper;

    @Resource
    private ExportTaskService exportTaskService;

    @Override
    public String getProcessInfoType() {
        return ProcessInfoTypeEnum.MUL.getCode();
    }

    @Override
    public List<MedCntIndicatorVO> listMedCntIndicator( MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listMedCntIndicator(queryDTO);
    }

    @Override
    public List<MedCntIndicatorVO> listOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        if (ProcessInfoTypeEnum.INTEGRATED.getCode().equals(queryDTO.getProcessType())) return Collections.emptyList();
        queryDTO.setEndDate(queryDTO.getEndDate() == null ? null : DateFormatUtil.getTheLastSecondOfOneDay(DateUtils.formatDate(queryDTO.getEndDate())));
        return adsMsProcessInfoMapper.listOutcomeIndicator(queryDTO);
    }

    @Override
    public List<AreaMedCntIndicatorVO> listAreaMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        queryDTO.setEndDate(queryDTO.getEndDate() == null ? null : DateFormatUtil.getTheLastSecondOfOneDay(DateUtils.formatDate(queryDTO.getEndDate())));
        return adsMsProcessInfoMapper.listAreaMedCntIndicator(queryDTO);
    }

    @Override
    public List<AreaMedCntIndicatorVO> listAreaOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {
        queryDTO.setEndDate(queryDTO.getEndDate() == null ? null : DateFormatUtil.getTheLastSecondOfOneDay(DateUtils.formatDate(queryDTO.getEndDate())));
        return adsMsProcessInfoMapper.listAreaOutcomeIndicator(queryDTO);
    }


    @Override
    public List<MsProcessInfoAddressVO> listAddressByIds(MsProcessSimpleInfoQueryDTO dto) {

        return adsMsProcessInfoMapper.listAddressByIds(dto);
    }

    @Override
    public PageInfo<MsProcessSimpleInfoVO> simpleInfoPageList(MsProcessSimpleInfoQueryDTO queryDTO) {

        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return new PageInfo<>(adsMsProcessInfoMapper.listSimpleInfo(queryDTO));
    }

    @Override
    public MsProcessSimpleInfoDetailVO loadProcessSimpleById(String id) {
        return adsMsProcessInfoMapper.loadSimpleInfo(id);
    }

    @Override
    public TbCdcmrExportTask exportSimpleInfo( MsProcessSimpleInfoQueryDTO dto) {
        ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                dto.getTaskName(),
                dto.getTaskUrl(),
                null,
                dto.getModuleType(),
                dto.getExportType(),
                dto.getExportName());

        return exportTaskService.addAndUploadFile(dto,
                () -> adsMsProcessInfoMapper.listSimpleInfo(dto),
                () -> Optional.ofNullable(adsMsProcessInfoMapper.countByProcessId(dto)).orElse(0),
                taskDTO,
                MsInfectedProcessSimpleInfoExcelVO.class,
                MsInfectedProcessSimpleInfoExcelVO::of);
    }

    @Override
    public List<MsPatientInfoVO> listPatientInfoByProcessIds(MsProcessSimpleInfoQueryDTO dto) {
        return adsMsProcessInfoMapper.listPatientInfoByIds(dto);
    }

    @Override
    public List<MsProcessSimpleInfoVO> listSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listSimpleInfo(queryDTO);
    }

    @Override
    public List<InfectedProcessModelSimpleInfo> listProcessModelSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listModelSimpleInfo(queryDTO);
    }

    @Override
    public List<MsProcessLogVO> listProcessLog(MsProcessLogQueryDTO queryDTO) {
        return adsMsProcessInfoMapper.listInfectedProcessLog(queryDTO);
    }

    @Override
    public List<TbCdcewEmergencyEventProcessInfo> getProcessInfoBy(List<String> ids) {

        return adsMsProcessInfoMapper.getProcessInfoBy(ids);
    }

    @Override
    public ProcessPathogenInfoVO getProcessPathogenInfoBy(List<String> ids) {

        return adsMsProcessInfoMapper.getProcessPathogenInfoBy(ids);
    }

    @Override
    public PageInfo<MsProcessSimpleInfoVO> syndromeSimpleInfoPageListByConditions(MsProcessSimpleInfoConditionQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return new PageInfo<>(adsMsProcessInfoMapper.syndromeSimpleInfoPageListByConditions(queryDTO));
    }

}
