package com.iflytek.cdc.province.service;

import com.iflytek.cdc.province.entity.bu.SyndromeVerifyRecord;

import java.util.List;

/**
 * <p>
 * 症候群核实记录服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface SyndromeVerifyRecordService {

    /**
     * 根据ID查询
     */
    SyndromeVerifyRecord getById(String id);

    /**
     * 根据ID列表查询
     */
    List<SyndromeVerifyRecord> getByIds(List<String> ids);

    /**
     * 插入单条记录
     */
    boolean insert(SyndromeVerifyRecord record);

    /**
     * 根据ID更新
     */
    boolean updateById(SyndromeVerifyRecord record);

    /**
     * 根据ID删除（逻辑删除）
     */
    boolean deleteById(String id);
} 