package com.iflytek.cdc.province.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.iflytek.cdc.province.entity.dim.DimSymptomMulInfo;
import com.iflytek.cdc.province.mapper.pg.DimSymptomMulInfoMapper;
import com.iflytek.cdc.province.service.DimSymptomMulInfoService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DimSymptomMulInfoServiceImpl extends ServiceImpl<DimSymptomMulInfoMapper, DimSymptomMulInfo>
        implements DimSymptomMulInfoService {

    @Override
    public Map<String, List<String>> getTagInfosBy(List<String> dataSources) {
        List<DimSymptomMulInfo> dimSymptomMulInfos = this.getBaseMapper().getTagInfosBy(dataSources, null);

        return dimSymptomMulInfos.stream().collect(Collectors.groupingBy(DimSymptomMulInfo::getTag,
                                                                         Collectors.mapping(DimSymptomMulInfo::getTagValue,
                                                                                            Collectors.collectingAndThen(
                                                                                                    Collectors.toSet(),
                                                                                                    ArrayList::new))));
    }

    @Override
    public List<String> getTagsByDataSource(List<String> dataSources) {
        return this.getBaseMapper().getDistinctTagsByDataSource(dataSources);
    }

    @Override
    public List<String> getTagValuesBy(List<String> dataSources, String tag) {
        // FIXME: 先忽略数据源过滤
        dataSources = null;
        List<DimSymptomMulInfo> dimSymptomMulInfos = this.getBaseMapper().getTagInfosBy(dataSources, tag);

        return dimSymptomMulInfos.stream().map(DimSymptomMulInfo::getTagValue)
                                 .distinct().collect(Collectors.toList());
    }
}
