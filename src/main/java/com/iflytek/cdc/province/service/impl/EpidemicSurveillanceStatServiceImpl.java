package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.iflytek.cdc.edr.apiService.UapServiceApi;
import com.iflytek.cdc.edr.enums.DateDimEnum;
import com.iflytek.cdc.edr.utils.DateFormatUtil;
import com.iflytek.cdc.province.mapper.pg.EpidemicSurveillanceStatMapper;
import com.iflytek.cdc.province.model.dto.EpSurStatQueryDTO;
import com.iflytek.cdc.province.model.vo.OrgIndexStatVO;
import com.iflytek.cdc.province.model.vo.ReportCardStatVO;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.service.EpidemicSurveillanceStatService;
import com.iflytek.cdc.province.utils.StatUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;

@Service
public class EpidemicSurveillanceStatServiceImpl implements EpidemicSurveillanceStatService {

    @Resource
    private EpidemicSurveillanceStatMapper epidemicSurveillanceStatMapper;

    @Resource
    private UapServiceApi uapServiceApi;

    @Override
    public OrgIndexStatVO orgIndexStat(EpSurStatQueryDTO queryDTO) {

        OrgIndexStatVO vo = new OrgIndexStatVO();
        vo.setQualified(100);
        vo.setCollect(120);
        vo.setOutpatientUpload(113);
        vo.setLeaveHospitalUpload(234);
        vo.setCoreFieldNull(111);
        vo.setPkRepeat(232);
        vo.setRangeError(222);
        vo.setRelateNotMatch(122);
        vo.setNetReport(2343);
        vo.setQualifiedRate("30%");
        vo.setOutpatientUploadRate("26%");
        vo.setLeaveHospitalUploadRate("34%");
        vo.setCoreFieldNullRate("27%");
        vo.setPkRepeatRate("29%");
        vo.setRangeErrorRate("30%");
        vo.setRelateNotMatchRate("36%");
        vo.setNetReportRate("26%");

        return vo;
    }

    @Override
    public byte[] orgIndexStatExport(EpSurStatQueryDTO queryDTO) {
        OrgIndexStatVO vo = orgIndexStat(queryDTO);
        List<OrgIndexStatVO> voList = new ArrayList<>();
        voList.add(vo);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        EasyExcel.write(outputStream, OrgIndexStatVO.class)
                .sheet("医疗机构指标统计")
                .doWrite(voList);
        return outputStream.toByteArray();
    }

    @Override
    public ReportCardStatVO reportCardStat(EpSurStatQueryDTO queryDTO) {
        ReportCardStatVO vo = new ReportCardStatVO();
        vo.setCheck(1000);
        vo.setReportAccurate(120);
        vo.setReportFull(123);
        vo.setQualified(392);
        vo.setNumberFull(234);
        vo.setCheckRate("80%");
        vo.setReportAccurateRate("90%");
        vo.setReportFullRate("75%");
        vo.setQualifiedRate("78%");
        vo.setCompositeRate("87%");
        vo.setNumberFullRate("78%");

        return vo;
    }

    @Override
    public List<ReportCardStatVO> orgTypeReportStat(EpSurStatQueryDTO queryDTO) {
        List<ReportCardStatVO> voList = new ArrayList<>();

        ReportCardStatVO vo1 = new ReportCardStatVO();
        vo1.setOrgType("医院");
        vo1.setReportNum(32);

        ReportCardStatVO vo2 = new ReportCardStatVO();
        vo2.setOrgType("疾控中心");
        vo2.setReportNum(43);

        ReportCardStatVO vo3 = new ReportCardStatVO();
        vo3.setOrgType("卫健委");
        vo3.setReportNum(12);

        voList.add(vo1);
        voList.add(vo2);
        voList.add(vo3);

        return voList;
    }

    @Override
    public ReportCardStatVO reportWayStat(EpSurStatQueryDTO queryDTO) {
        ReportCardStatVO vo = new ReportCardStatVO();
        vo.setDirectReport(100);
        vo.setDirectReportRate("70%");
        vo.setAgentReport(76);
        vo.setDirectReportRate("30%");
        return vo;
    }

    @Override
    public ReportCardStatVO loginStat(EpSurStatQueryDTO queryDTO) {
        ReportCardStatVO vo = new ReportCardStatVO();
        vo.setLoginNum(100);
        vo.setLoginRate("80%");
        vo.setUnLoginRate("20%");
        return vo;
    }

    @Override
    public List<ReportCardStatVO> directReportStat(EpSurStatQueryDTO queryDTO) {
        List<ReportCardStatVO> voList = new ArrayList<>();

        ReportCardStatVO vo1 = new ReportCardStatVO();
        vo1.setAreaName("郑州市");
        vo1.setDirectReport(200);

        ReportCardStatVO vo2 = new ReportCardStatVO();
        vo2.setAreaName("开封市");
        vo2.setDirectReport(139);

        ReportCardStatVO vo3 = new ReportCardStatVO();
        vo3.setAreaName("洛阳市");
        vo3.setDirectReport(180);

        voList.add(vo1);
        voList.add(vo2);
        voList.add(vo3);
        return voList;
    }

    @Override
    public List<ReportCardStatVO> repeatReportStat(EpSurStatQueryDTO queryDTO) {
        List<ReportCardStatVO> voList = new ArrayList<>();

        ReportCardStatVO vo1 = new ReportCardStatVO();
        vo1.setAreaName("郑州市");
        vo1.setRepeatReport(12);

        ReportCardStatVO vo2 = new ReportCardStatVO();
        vo2.setAreaName("开封市");
        vo2.setRepeatReport(19);

        ReportCardStatVO vo3 = new ReportCardStatVO();
        vo3.setAreaName("洛阳市");
        vo3.setRepeatReport(9);

        voList.add(vo1);
        voList.add(vo2);
        voList.add(vo3);
        return voList;
    }


    /**
     * 设置区域信息
     */
    private void setAreaInfoList(EpSurStatQueryDTO queryDTO, String loginUserName){
        uapServiceApi.setProvinceAndCityAndDistrict(loginUserName, queryDTO,
                EpSurStatQueryDTO::setProvinceCodes,
                EpSurStatQueryDTO::setCityCodes,
                EpSurStatQueryDTO::setDistrictCodes,
                (d, areaLevel) -> {
                    if (d.getAreaLevel() == null){
                        d.setAreaLevel(areaLevel);
                    }
                });
        // 融合权限和过滤条件
        if (CollectionUtil.isNotEmpty(queryDTO.getDistrictCodeList())) {
            if (CollUtil.isEmpty(queryDTO.getDistrictCodes())) {
                queryDTO.setDistrictCodes(queryDTO.getDistrictCodeList());
            } else {
                queryDTO.setDistrictCodes(CommonUtilService.intersection(queryDTO.getDistrictCodeList(), queryDTO.getDistrictCodes()));
            }
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getCityCodeList())) {
            if (CollUtil.isEmpty(queryDTO.getCityCodes())) {
                queryDTO.setCityCodes(queryDTO.getCityCodeList());
            } else {
                queryDTO.setCityCodes(CommonUtilService.intersection(queryDTO.getCityCodeList(), queryDTO.getCityCodes()));
            }
        }
    }

    @Override
    public List<ReportCardStatVO> reportcardTimeTrend(EpSurStatQueryDTO queryParam, String loginUserName) {
        setAreaInfoList(queryParam, loginUserName);
        if (queryParam.getStartDate() != null && queryParam.getEndDate() != null) {
            queryParam.setStartDate(DateFormatUtil.getTheFistSecondOfOneDay(queryParam.getStartDate()));
            queryParam.setEndDate(DateFormatUtil.getTheLastSecondOfOneDay(queryParam.getEndDate()));
        }
        DateDimEnum dateDimEnum = DateDimEnum.getByCode(queryParam.getDateDimType());
        if(dateDimEnum == null){
            dateDimEnum = DateDimEnum.DAY;
        }

        return StatUtils.fulfillAndConvertDayDateData(
                StatUtils.fulfillPeriodValueByDay(queryParam, q -> epidemicSurveillanceStatMapper.reportcardTimeTrend(q),
                        ReportCardStatVO::getStatDate, ReportCardStatVO.currValueAndLastValueMappingList()),
                queryParam.getStartDate(),
                queryParam.getEndDate(),
                dateDimEnum,
                ReportCardStatVO::getStatDate,
                ReportCardStatVO::setStatDate,
                ReportCardStatVO.dataConverter
        );
    }

}
