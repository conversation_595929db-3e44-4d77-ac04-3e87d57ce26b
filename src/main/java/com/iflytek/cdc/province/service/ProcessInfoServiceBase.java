package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoDetailVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO;
import com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo;
import com.iflytek.cdc.province.model.dto.MsProcessLogQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoConditionQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.vo.*;

import java.util.List;

public interface ProcessInfoServiceBase {
    /**
     * 病程类型
     */
    String getProcessInfoType();

    /**
     * 病例数指标
     */
    List<MedCntIndicatorVO> listMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);


    /**
     * 病例数指标
     */
    List<MedCntIndicatorVO> listOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    List<AreaMedCntIndicatorVO> listAreaMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 病例数指标
     */
    List<AreaMedCntIndicatorVO> listAreaOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 根据id查询地址相关信息
     */
    List<MsProcessInfoAddressVO> listAddressByIds(MsProcessSimpleInfoQueryDTO dto);


    /**
     * 分页查询病程简单信息
     */
    PageInfo<MsProcessSimpleInfoVO> simpleInfoPageList(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 根据病程id 单条加载数据
     */
    MsProcessSimpleInfoDetailVO loadProcessSimpleById(String id);

    /**
     * 导出病程简单信息
     */
    TbCdcmrExportTask exportSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO);


    /**
     * 查询患者信息
     */
    List<MsPatientInfoVO> listPatientInfoByProcessIds(MsProcessSimpleInfoQueryDTO dto);

    /**
     * 查询简单病程信息
     */
    List<MsProcessSimpleInfoVO> listSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 病程模型简易信息
     */
    Object listProcessModelSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO);


    /**
     * 病程时间轴
     */
    List<MsProcessLogVO> listProcessLog(MsProcessLogQueryDTO queryDTO);

    /**
     * 获取信号病例
     * */
    List<TbCdcewEmergencyEventProcessInfo> getProcessInfoBy(List<String> ids);

    /**
     * 统计病例的病原检测结果
     * */
    ProcessPathogenInfoVO getProcessPathogenInfoBy(List<String> ids);


    PageInfo<MsProcessSimpleInfoVO> syndromeSimpleInfoPageListByConditions(MsProcessSimpleInfoConditionQueryDTO queryDTO);

    /**
     * 分页查询治疗情况
     */
    default PageInfo<PatientTreatmentInfoVO> pageListTreatment(MsProcessSimpleInfoQueryDTO dto){
        return new PageInfo<>();
    }

    /**
     * 分页查询死亡情况
     */
    default PageInfo<PatientDeathInfoVO> pageListDeath(MsProcessSimpleInfoQueryDTO dto){
        return new PageInfo<>();
    }
}
