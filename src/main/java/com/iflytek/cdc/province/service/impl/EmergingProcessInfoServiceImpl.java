package com.iflytek.cdc.province.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoDetailVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO;
import com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo;
import com.iflytek.cdc.province.enums.WarningTypeEnum;
import com.iflytek.cdc.province.mapper.pg.AdsMulProcessCaseInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsPdPathogenCheckInfoMapper;
import com.iflytek.cdc.province.mapper.pg.EmergingProcessInfoMapper;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoConditionQueryDTO;
import com.iflytek.cdc.province.model.dto.PathogenCombinationQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.pathogen.PathogenInfectionSituationVO;
import com.iflytek.cdc.province.model.vo.*;
import com.iflytek.cdc.province.service.EmergingProcessInfoService;
import org.springframework.stereotype.Service;

import com.iflytek.cdc.province.enums.ProcessInfoTypeEnum;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.service.ProcessInfoServiceBase;
import com.iflytek.cdc.province.model.dto.MsProcessLogQueryDTO;
import com.iflytek.cdc.province.service.ExportTaskService;

@Service
public class EmergingProcessInfoServiceImpl implements ProcessInfoServiceBase, EmergingProcessInfoService {

    @Resource
    private EmergingProcessInfoMapper emergingProcessInfoMapper;

    @Resource
    private AdsPdPathogenCheckInfoMapper adsPdPathogenCheckInfoMapper;

    @Resource
    private ExportTaskService exportTaskService;

    @Resource
    private AdsMulProcessCaseInfoMapper adsMulProcessCaseInfoMapper;

    @Override
    public String getProcessInfoType() {

        return ProcessInfoTypeEnum.EMERGING.getCode();
    }

    @Override
    public List<MedCntIndicatorVO> listMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {

        return emergingProcessInfoMapper.listMedCntIndicator(queryDTO);
    }

    @Override
    public List<MedCntIndicatorVO> listOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {

        return emergingProcessInfoMapper.listOutcomeCntIndicator(queryDTO);
    }

    @Override
    public List<AreaMedCntIndicatorVO> listAreaMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {

        return emergingProcessInfoMapper.listAreaMedCntIndicator(queryDTO);
    }

    @Override
    public List<AreaMedCntIndicatorVO> listAreaOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {

        return emergingProcessInfoMapper.listAreaOutcomeCntIndicator(queryDTO);
    }

    @Override
    public List<MsProcessInfoAddressVO> listAddressByIds(MsProcessSimpleInfoQueryDTO dto) {

        return emergingProcessInfoMapper.listAddressByIds(dto);
    }

    @Override
    public PageInfo<MsProcessSimpleInfoVO> simpleInfoPageList(MsProcessSimpleInfoQueryDTO queryDTO) {

        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return new PageInfo<>(emergingProcessInfoMapper.listSimpleInfo(queryDTO));
    }

    @Override
    public MsProcessSimpleInfoDetailVO loadProcessSimpleById(String id) {

        return emergingProcessInfoMapper.loadProcessSimpleById(id);
    }

    @Override
    public TbCdcmrExportTask exportSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {

        ExportTaskDTO taskDTO = ExportTaskDTO.of(queryDTO.getExportTaskId(),
                                                 queryDTO.getTaskName(),
                                                 queryDTO.getTaskUrl(),
                                                 null,
                                                 queryDTO.getModuleType(),
                                                 queryDTO.getExportType(),
                                                 queryDTO.getExportName());

        return exportTaskService.addAndUploadFile(queryDTO,
                                                  () -> emergingProcessInfoMapper.listSimpleInfo(queryDTO),
                                                  () -> Optional.ofNullable(emergingProcessInfoMapper.countEmerging(queryDTO)).orElse(0),
                                                  taskDTO,
                                                  MsEmergingProcessSimpleInfoExcelVO.class,
                                                  MsEmergingProcessSimpleInfoExcelVO::of);
    }

    @Override
    public List<MsPatientInfoVO> listPatientInfoByProcessIds(MsProcessSimpleInfoQueryDTO dto) {

        return emergingProcessInfoMapper.listPatientInfoByProcessIds(dto);
    }

    @Override
    public List<MsProcessSimpleInfoVO> listSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {

        return emergingProcessInfoMapper.listSimpleInfo(queryDTO);
    }

    @Override
    public Object listProcessModelSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {

        return emergingProcessInfoMapper.listProcessModelSimpleInfo(queryDTO);
    }

    @Override
    public List<MsProcessLogVO> listProcessLog(MsProcessLogQueryDTO queryDTO) {

        return emergingProcessInfoMapper.listProcessLog(queryDTO);
    }

    @Override
    public List<TbCdcewEmergencyEventProcessInfo> getProcessInfoBy(List<String> ids) {

        return emergingProcessInfoMapper.getProcessInfoBy(ids);
    }

    @Override
    public ProcessPathogenInfoVO getProcessPathogenInfoBy(List<String> ids) {

        return emergingProcessInfoMapper.getProcessPathogenInfoBy(ids);
    }

    @Override
    public PageInfo<MsProcessSimpleInfoVO> syndromeSimpleInfoPageListByConditions(MsProcessSimpleInfoConditionQueryDTO queryDTO) {

        return new PageInfo<>(new ArrayList<>());
    }

    @Override
    public List<PathogenCheckVO> listEmergingPathogenCheckResult(PathogenCombinationQueryDTO dto) {

        return adsMulProcessCaseInfoMapper.listPathogenCheckResult(dto);
    }

    @Override
    public List<PathogenCheckVO> listEmergingPositivePathogenCheckResult(PathogenCombinationQueryDTO dto) {

        if(CollectionUtil.isEmpty(dto.getSourceKeyList())){
            return new ArrayList<>();
        }
        dto.setWarningType(WarningTypeEnum.EMERGING.getCode());
        return adsPdPathogenCheckInfoMapper.listPositivePathogenCheckResult(dto);
    }

    @Override
    public List<PathogenInfectionSituationVO> listEmergingPathogenInfectionSituation(PathogenCombinationQueryDTO dto) {

        return adsPdPathogenCheckInfoMapper.getPathogenInfectionSituation(dto);
    }

}
