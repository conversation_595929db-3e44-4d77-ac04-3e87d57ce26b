package com.iflytek.cdc.province.service;

import cn.hutool.json.JSONUtil;
import com.iflytek.cdc.province.model.vo.DrugInfo;
import com.iflytek.cdc.province.utils.RegexesUtil;
import com.iflytek.zhyl.mdm.sdk.apiservice.DrugApi;
import com.iflytek.zhyl.mdm.sdk.apiservice.OrgApi;
import com.iflytek.zhyl.mdm.sdk.apiservice.PatientApi;
import com.iflytek.zhyl.mdm.sdk.apiservice.TermDictApi;
import com.iflytek.zhyl.mdm.sdk.pojo.OrgBaseInfo;
import com.iflytek.zhyl.mdm.sdk.pojo.PatientBaseInfoDto;
import com.iflytek.zhyl.mdm.sdk.pojo.PatientBestRecordDto;
import com.iflytek.zhyl.mdm.sdk.pojo.PatientRegisterRsp;
import com.iflytek.zhyl.mdm.sdk.pojo.drug.DrugMatchInfo;
import com.iflytek.zhyl.mdm.sdk.pojo.drug.DrugMatchRsp;
import com.iflytek.zhyl.mdm.sdk.pojo.termDict.MultipleCodedValueItem;
import com.iflytek.zhyl.mdm.sdk.pojo.termDict.MultipleDictItem;
import com.iflytek.zhyl.mdm.sdk.pojo.termDict.MultipleDictMapping;
import com.iflytek.zhyl.mdm.sdk.util.ResultData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class MdmRegisterService {

    @Value("${common.internalService.mdm.sdk.domain-code:CDC-SJPT}")
    private String mdmDomainCode;

    @Value("${zyzl.mdm.data-domain-code:DM_ZYZL_IFLTEK}")
    private String mdmDataDomainCode;


    @Resource
    PatientApi patientApi;

    @Resource
    OrgApi orgApi;

    @Resource
    TermDictApi termDictApi;

    @Resource
    DrugApi drugApi;

    public ResultData<PatientRegisterRsp> registerPatientInfo(PatientBaseInfoDto patientBaseInfoDto) {
        return patientApi.registerPatientInfo(patientBaseInfoDto);
    }

    public String registerGlobalPersonId(PatientBaseInfoDto patientBaseInfoDto) {

        // 必填项校验,校验不通过就不调用MDM
        if (StringUtils.isNotBlank(patientBaseInfoDto.getPatientName())
                && StringUtils.isNotBlank(patientBaseInfoDto.getLocalPatientId())
                && StringUtils.isNotBlank(patientBaseInfoDto.getDataUapId())) {
            try {
                log.info("调用MDM的请求参数: {}", JSONUtil.toJsonStr(patientBaseInfoDto));
                String globalPatientId = Optional.ofNullable(patientApi.registerPatientInfo(patientBaseInfoDto))
                        .map(ResultData::getData)
                        .map(PatientRegisterRsp::getGlobalPatientId)
                        .orElse(null);
                log.info("调用MDM获取的globalPatientId: {}", globalPatientId);
                return globalPatientId;
            } catch (Exception e) {
                log.error("调用MDM患者注册接口失败：", e);
            }
        } else {
            log.info("患者信息缺失: {}", JSONUtil.toJsonStr(patientBaseInfoDto));
        }

        return null;
    }

    public ResultData tbPatientBestRecordUpdate(PatientBestRecordDto patientBestRecordDto){
        return patientApi.updatePatientBestRecord(patientBestRecordDto);
    }

    /**
     * MDM机构注册
     * @param orgBaseInfo 机构信息
     */
    public String orgRegister(OrgBaseInfo orgBaseInfo) {
        try {
            log.info("调用MDM机构注册的请求参数: {}", JSONUtil.toJsonStr(orgBaseInfo));
            return Optional.of(orgApi.register(orgBaseInfo)).map(ResultData::getData).orElse("");
        } catch (Exception e) {
            log.error("调用MDM机构注册接口失败：", e);
        }
        return null;
    }


    /**
     * 字典标化
     */
    public MultipleCodedValueItem dictValueStandardize(String localOrgId, String dictCode, String dictValueCode, String dictValueName) {

        MultipleCodedValueItem dictCodeValue = new MultipleCodedValueItem();
        dictCodeValue.setCodedValue(dictValueCode);
        dictCodeValue.setDescription(dictValueName);

        if (!RegexesUtil.isMeaningfulText(dictValueName)) {
            return dictCodeValue;
        }

        MultipleDictMapping mappingFilter = new MultipleDictMapping();
        mappingFilter.setSystemDomainCode(mdmDomainCode);
        mappingFilter.setDataDomainCode(mdmDataDomainCode);
        mappingFilter.setDataUapId(localOrgId);

        MultipleDictItem dict = new MultipleDictItem();
        dict.setDictCode(dictCode);
        mappingFilter.addDict(dict);


        dict.addCodeValue(dictCodeValue);

        ResultData<MultipleDictMapping> multipleDictMappings = termDictApi.getMultipleDictMappings(mappingFilter);

        MultipleDictMapping mappingsData = multipleDictMappings.getData();

        List<MultipleDictItem> mappingsDataDicts = Optional.ofNullable(mappingsData).map(MultipleDictMapping::getDicts).orElse(new ArrayList<>());
        for (MultipleDictItem mappingsDataDict : mappingsDataDicts) {
            for (MultipleCodedValueItem codedValue : mappingsDataDict.getCodedValues()) {
                String stdDescription = codedValue.getStdDescription();
                if(StringUtils.isNotBlank(stdDescription)) {
                    return codedValue;
                }
            }
        }

        return dictCodeValue;
    }

    public DrugMatchRsp drugStandardize(String orgId, DrugInfo drugInfo) {

        DrugMatchInfo drugMatchInfo = new DrugMatchInfo();
        drugMatchInfo.setLocalOrgId(orgId);
        drugMatchInfo.setSystemDomainCode(mdmDomainCode);
        drugMatchInfo.setDataDomainCode(mdmDataDomainCode);

        drugMatchInfo.setDrugCode(drugInfo.getDrugCode());
        drugMatchInfo.setDrugName(drugInfo.getDrugName());
        drugMatchInfo.setDrugSpec(drugInfo.getDrugSpec());
        drugMatchInfo.setTradeName(drugInfo.getTradeName());
        drugMatchInfo.setApproveNum(drugInfo.getApproveNum());
        drugMatchInfo.setManufacturerName(drugInfo.getManufacturerName());
        drugMatchInfo.setDrugTypeName(drugInfo.getDrugTypeName());
        drugMatchInfo.setNationalCatalogCode(drugInfo.getNationalCatalogCode());

        ResultData<List<DrugMatchRsp>> resultData = drugApi.matchDrug(drugMatchInfo);

        List<DrugMatchRsp> drugMatchRsps = Optional.ofNullable(resultData).map(ResultData::getData).orElse(new ArrayList<>());

        for (DrugMatchRsp drugMatchRsp : drugMatchRsps) {
            String stdDrugName = drugMatchRsp.getStdDrugName();
            if(drugMatchRsp.getMatchFlag() == 1 && StringUtils.isNotBlank(stdDrugName)) {
                return drugMatchRsp;
            }
        }

        return drugMatchRsps.size() > 0 ? drugMatchRsps.get(0) : new DrugMatchRsp();
    }
}
