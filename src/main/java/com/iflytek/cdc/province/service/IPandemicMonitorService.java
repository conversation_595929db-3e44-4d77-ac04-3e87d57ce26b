package com.iflytek.cdc.province.service;

import com.iflytek.cdc.edr.dto.workbench.UserTodoListDTO;
import com.iflytek.cdc.edr.vo.dm.ProcessRecordVO;
import com.iflytek.cdc.province.model.pandemic.dto.MedicalQueryDTO;

import java.util.List;

public interface IPandemicMonitorService {

    List<ProcessRecordVO> getMedicalList(MedicalQueryDTO medicalQueryDTO);


    List<UserTodoListDTO> getMedicalListToDo(MedicalQueryDTO dto);
    
}
