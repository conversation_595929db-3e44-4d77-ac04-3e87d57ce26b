package com.iflytek.cdc.province.service;

import com.iflytek.cdc.edr.dto.DimInfectedInfoQuery;
import com.iflytek.cdc.edr.vo.ConstantVO;

import java.util.List;
import java.util.Map;

public interface DimSymptomMulInfoService {

    Map<String, List<String>> getTagInfosBy(List<String> dataSources);

    List<String> getTagsByDataSource(List<String> dataSources);

    List<String> getTagValuesBy(List<String> dataSources, String tag);
}
