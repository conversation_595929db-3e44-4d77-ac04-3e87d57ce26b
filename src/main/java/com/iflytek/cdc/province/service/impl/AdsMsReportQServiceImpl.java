package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.common.Common;
import com.iflytek.cdc.edr.utils.DataUtils;
import com.iflytek.cdc.edr.utils.DateFormatUtil;
import com.iflytek.cdc.edr.vo.adsMsQualityEval.QualityEvalInfectExcelVO;
import com.iflytek.cdc.edr.vo.adsMsQualityEval.QualityEvalRespVO;
import com.iflytek.cdc.edr.vo.adsMsQualityEval.QualityEvalSyndromeExcelVO;
import com.iflytek.cdc.edr.vo.adsMsQualityEval.ReportInfoInfectExcelVO;
import com.iflytek.cdc.edr.vo.adsMsQualityEval.ReportInfoSyndromeExcelVO;
import com.iflytek.cdc.province.enums.ReportCheckFlagEnum;
import com.iflytek.cdc.province.mapper.pg.AdsMsInfectReportQMapper;
import com.iflytek.cdc.province.mapper.pg.AdsMsReportInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsMsSyndromeReportQMapper;
import com.iflytek.cdc.province.model.dto.AdsMsReportReqDTO;
import com.iflytek.cdc.province.model.pandemic.dto.CheckTaskQueryDTO;
import com.iflytek.cdc.province.model.pandemic.vo.CheckTaskRecordVO;
import com.iflytek.cdc.province.service.AdsMsReportQService;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 诊疗(medical service)-传染病/症候群报告卡-监测业务质量（quality）- 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Service
@Slf4j
public class AdsMsReportQServiceImpl implements AdsMsReportQService {

    @Resource
    private AdsMsInfectReportQMapper infectReportQMapper;

    @Resource
    private AdsMsSyndromeReportQMapper syndromeReportQMapper;

    @Resource
    private AdsMsReportInfoMapper reportInfoMapper;

    @Resource
    private DataUtils dataUtils;

    @Resource
    private CommonUtilService commonUtilService;

    private void preprocess(AdsMsReportReqDTO reqDTO) {
        CommonUtilService.setAreaQueryDTO(reqDTO);
        if (StrUtil.isBlank(reqDTO.getSyndromeCode()) && StrUtil.isNotBlank(reqDTO.getDiseaseName())) {
            reqDTO.setSyndromeCode(commonUtilService.getSyndromeDiseaseCodeByName(reqDTO.getDiseaseName()));
        }
        if (reqDTO.getStartDate() != null && reqDTO.getEndDate() != null) {
            reqDTO.setStartDate(DateFormatUtil.getTheFistSecondOfOneDay(reqDTO.getStartDate()));
            reqDTO.setEndDate(DateFormatUtil.getTheLastSecondOfOneDay(reqDTO.getEndDate()));
        }
    }

    @Override
    public List<QualityEvalRespVO> evalStat(AdsMsReportReqDTO reqDTO, String loginUserName) {
        CommonUtilService.setAreaMultiChooseQueryDTO(reqDTO);
        if (StrUtil.isBlank(reqDTO.getSyndromeCode()) && StrUtil.isNotBlank(reqDTO.getDiseaseName())) {
            reqDTO.setSyndromeCode(commonUtilService.getSyndromeDiseaseCodeByName(reqDTO.getDiseaseName()));
        }
        List<QualityEvalRespVO> respVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> infectReportQMapper.dayEvalStat(reqDTO),
                () -> syndromeReportQMapper.dayEvalStat(reqDTO));

        QualityEvalRespVO amount = new QualityEvalRespVO();
        amount.setInfectType(Common.ALL);
        amount.setInfectName(Common.AMOUNT_TO);
        amount.setSyndromeName(Common.ALL);

        for (QualityEvalRespVO vo : respVOS) {
            vo.calculateAllRates();
            vo.amountTo(amount);
        }
        amount.calculateAllRates();
        respVOS.add(0, amount);

        return respVOS;
    }

    @Override
    public ResponseEntity<byte[]> evalStatExport(AdsMsReportReqDTO reqDTO, String loginUserName) {
        List<QualityEvalRespVO> respVOS = this.evalStat(reqDTO, loginUserName);
        return DataUtils.exportBytesByWarningType(reqDTO.getWarningType(), respVOS,
                QualityEvalInfectExcelVO.class,
                QualityEvalSyndromeExcelVO.class);
    }

    @Override
    public PageInfo<CheckTaskRecordVO> reportInfoList(AdsMsReportReqDTO reqDTO, String loginUserName) {
        this.preprocess(reqDTO);
        try (Page<CheckTaskRecordVO> ignored = PageHelper.startPage(reqDTO.getPageIndex(), reqDTO.getPageSize())) {
            return PageInfo.of(reportInfoMapper.getCheckTaskList(convertDetailRequest(reqDTO)));
        }
    }

    @Override
    public ResponseEntity<byte[]> reportInfoListExport(AdsMsReportReqDTO reqDTO, String loginUserName) {
        this.preprocess(reqDTO);
        List<CheckTaskRecordVO> list = reportInfoMapper.getCheckTaskList(convertDetailRequest(reqDTO));
        return DataUtils.exportBytesByWarningType(reqDTO.getWarningType(), list,
                ReportInfoInfectExcelVO.class,
                ReportInfoSyndromeExcelVO.class);
    }

    private CheckTaskQueryDTO convertDetailRequest(AdsMsReportReqDTO reqDTO) {
        // 转换类型
        CheckTaskQueryDTO query = new CheckTaskQueryDTO();
        // CommonQuery 父类字段
        query.setPageIndex(reqDTO.getPageIndex());
        query.setPageSize(reqDTO.getPageSize());
        query.setProvinceCode(reqDTO.getProvinceCode());
        query.setCityCode(reqDTO.getCityCode());
        query.setDistrictCode(reqDTO.getDistrictCode());
        query.setStreetCode(reqDTO.getStreetCode());
        query.setQueryKey(reqDTO.getQueryKey());
        query.setProperty(reqDTO.getProperty());
        query.setDirection(reqDTO.getDirection());
        query.setProvinceCodes(reqDTO.getProvinceCodes());
        query.setCityCodes(reqDTO.getCityCodes());
        query.setDistrictCodes(reqDTO.getDistrictCodes());
        query.setStreetCodes(reqDTO.getStreetCodes());
        query.setAreaLevel(reqDTO.getAreaLevel());
        query.setAreaCode(reqDTO.getAreaCode());
        // 本身类字段
        query.setDateType(reqDTO.getTimeType());
        query.setStartDate(reqDTO.getStartDate());
        query.setEndDate(reqDTO.getEndDate());
        query.setAddressType(reqDTO.getAddressType());
        query.setDiseaseType(reqDTO.getWarningType());
        query.setDiseaseCodeList(convertDiseaseCodeList(reqDTO));
        // 填充下钻用的过滤字段
        if (StrUtil.isNotBlank(reqDTO.getStatFlag())) {
            this.fillDrillConditions(query, reqDTO.getStatFlag());
        }
        return query;
    }

    private List<String> convertDiseaseCodeList(AdsMsReportReqDTO reqDTO) {
        List<String> infectTypes = StrUtil.isBlank(reqDTO.getInfectType()) ? ListUtil.empty() : ListUtil.of(reqDTO.getInfectType());
        List<String> infectCodes = StrUtil.isBlank(reqDTO.getInfectCode()) ? ListUtil.empty() : ListUtil.of(reqDTO.getInfectCode());
        List<String> syndromeCodes = StrUtil.isBlank(reqDTO.getSyndromeCode()) ? ListUtil.empty() : ListUtil.of(reqDTO.getSyndromeCode());
        return commonUtilService.getDiseaseCodeListBy(reqDTO.getWarningType(), reqDTO.getInfectClass(), infectTypes, infectCodes, syndromeCodes);
    }

    private void fillDrillConditions(CheckTaskQueryDTO query, String statFlag) {
        switch (statFlag) {
            case "processNewCnt":
                // 全部报告，无需状态过滤条件
                break;
            case "needCheckCnt":
                // check_status = 1 未审核
                query.setReportCheckFlag(ReportCheckFlagEnum.WAITING_CHECK.getCode());
                break;
            case "noneedCheckCnt":
                // check_status = 0 无需审核
                query.setReportCheckFlag(ReportCheckFlagEnum.NO_NEED_CHECK.getCode());
                break;
            case "checkCnt":
                // check_status = 2 审核完成
                query.setReportCheckFlag(ReportCheckFlagEnum.CHECKED.getCode());
                break;
            case "identifyIntimeCnt":
                // identify_intime_flag = 1 报告及时
                query.setIdentifyIntimeFlag(Common.FLAG_INTIME);
                break;
            case "identifyDelayCnt":
                // identify_intime_flag = 2 报告超时
                query.setIdentifyIntimeFlag(Common.FLAG_DELAY);
                break;
            case "checkIntimeCnt":
                // check_intime_flag = 1 审核及时
                query.setCheckIntimeFlag(Common.FLAG_INTIME);
                break;
            case "checkDelayCnt":
                // check_intime_flag = 2 审核超时
                query.setCheckIntimeFlag(Common.FLAG_DELAY);
                break;
            case "identifyTrueCnt":
                // checked_flag = 1 审核通过即正确识别
                query.setCheckedFlag(Common.STR_ONE);
                break;
            case "leakCnt":
                // leak_flag = 1 漏报
                query.setLeakFlag(Common.STR_ONE);
                break;
            case "trueOutCnt":
                // syndrome_out_flag = 1 症候群-正确排除
                query.setOutFlag(Common.STR_ONE);
                break;
            case "falseIdentifyCnt":
                // checked_flag = 0 审核不通过即误报
                query.setCheckedFlag(Common.STR_ZERO);
                break;
            case "fullCnt":
                // full_flag = 1 完整
                query.setFullFlag(Common.STR_ONE);
                break;
            case "unFullCnt":
                // full_flag = 0 不完整
                query.setFullFlag(Common.STR_ZERO);
                break;
            case "conformCnt":
                // conform_flag = 1 合格
                query.setConformFlag(Common.STR_ONE);
                break;
            case "unConformCnt":
                // conform_flag = 0 不合格
                query.setConformFlag(Common.STR_ZERO);
                break;
            default:
                log.warn("未知的钻取类型: {}", statFlag);
                throw new MedicalBusinessException("未知的钻取类型");
        }
    }
}
