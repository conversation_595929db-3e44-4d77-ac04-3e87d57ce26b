package com.iflytek.cdc.province.service;

import java.util.List;

import com.iflytek.cdc.province.entity.ads.AdsEdrVaccinateHistory;
import com.iflytek.cdc.province.model.vo.edrcase.*;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrDiseaseDetailVO;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrLisInfoVO;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrRisInfoVO;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrTreatmentDetailVO;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrPersonInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

public interface EDRCaseDetailService {


    /**
     * 根据empiId查询报卡信息
     * @param empiId
     * @return
     */
    List<AdsRepInfectReportInfoVO> findReportByempiIdList(String empiId);

    /**
     * 根据事件id查询患者edr疾病详情信息
     * @param eventId
     * @return
     */
    AdsEdrDiseaseDetailVO getAdsEdrDiseaseDetail(String eventId);

    /**
     * 根据事件id查询影像学检查信息
     * @param eventId
     * @return
     */
    List<AdsEdrRisInfoVO> getRisByEventId(String eventId);

    /**
     * 根据事件id查询实验室检测信息
     * @param eventId
     * @return
     */
    List<AdsEdrLisInfoVO> getLisByEventId(String eventId);


    Map<List<Object>, List<AdsEdrTreatmentDetailVO>> groupTreatment(String eventId);



    /**
     * 获取个人主数据
     * @param empiId
     * @return
     */
    public AdsEdrPersonInfoVO getAdsEdrPersonInfo(String empiId);

    /**
     * 获取个人登记情况管理信息
     * @param empiId
     * @param eventIds
     * @return
     */
    public List<AdsEdrRegisterManageInfoVO> getAdsEdrRegisterManageInfoList(String empiId, String[] eventIds);

    /**
     * 获取随访信息
     * @param empiId
     * @param eventIds
     * @return
     */
    public List<AdsEdrFollowInfoVO> getAdsEdrFollowInfoVOList(String empiId, String[] eventIds);

    /**
     * 获取死亡登记信息
     * @param empiId
     * @param eventIds
     * @return
     */
    public AdsEdrDeadInfoVO getAdsEdrDeadInfo(String empiId, String[] eventIds);

    /**
     * 获取预防接种史信息
     * @param empiId
     * @param eventIds
     * @return
     */
    List<AdsEdrVaccinateHistory> getAdsEdrVaccinateHistoryList(String empiId, String[] eventIds);

    /**
     * 查询病种列表
      * @param empiId
     * @return
     */
    List<AdsEdrProcessTagVO> getAdsEdrProcessTagList(String empiId);

    /**
     * 获取个人状态信息
      * @param empiId
     * @param eventIds
     * @return
     */
    List<AdsEdrPersonStatusInfoVO> getAdsEdrPersonStatusInfoList(String empiId,String[]  eventIds);

    /**
     * 获取全生命周期信息
     * @param empiId
     * @return
     */
    List<DwsProcessTagVO> getDwsProcessTagList(String empiId);
}
