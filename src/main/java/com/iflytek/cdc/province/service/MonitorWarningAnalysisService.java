package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.province.model.dto.MonitorWarningAnalysisQueryDTO;
import com.iflytek.cdc.province.model.vo.*;

import java.util.List;

public interface MonitorWarningAnalysisService {


    ThreeCatePeopleViewVO getThreeCatePeopleView(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName);


    List<ThreeCatePeopleViewVO> threeCatePeopleTimeTrend(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName);


    ThreeCatePeopleViewVO veenView(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName);


    ReportOverallVO reportOverall(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName);


    PageInfo<DiagnoseProcessVO> pageDiagnoseProcess(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName);

    PatientVisitDetailVO getPatientDetailByProcessId(String processId);

    PageInfo<ReportProcessVO> pageReportProcess(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName);

    ReportProcessVO getReportProcessDetailByProcessId(String processId);

    InvestigatedCaseVO investigatedCaseStat(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName);

    /**
     * 应排查病例列表
     */
    PageInfo<InvestigatedCaseVO> pageInvestigatedCase(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName);

    /**
     * 应排查任务统计
     */
    InvestigatedTaskVO investigatedTaskStat(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName);

    /**
     * 区域统计分页
     */
    PageInfo<InvestigatedTaskVO> pageTaskStatAreaGroup(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName);

    /**
     * 区域统计列表
     */
    List<InvestigatedTaskVO> taskStatAreaGroup(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName);

    /**
     * 区域统计导出
     */
    byte[] taskStatAreaGroupExport(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName);


    /**
     * 应排查任务数量按地区统计
     */
    List<InvestTaskStatVO> checkTaskStat(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName);

    PageInfo<InvestTaskStatVO> checkTaskStatPageList(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName);

    /**
     * 任务督办按地区统计
     */
    List<InvestTaskStatVO> investTaskStat(MonitorWarningAnalysisQueryDTO queryDTO, String loginUserName);



}


