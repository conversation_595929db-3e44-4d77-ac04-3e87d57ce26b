package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.WarningSignalVO;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.VirusCheckQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.*;

import java.util.List;
import java.util.Map;

public interface PathogenFeaturesService {

    /**
     * 呼吸道病毒各分型检出情况
     * */
    PageInfo<VirusCheckResultVO> virusCheckSituation(VirusCheckQueryDTO queryDTO);

    /**
     * 病原检出率TOP10
     * */
    List<VirusTypeDetectedRateVO> getTopDetectedRateBy(VirusCheckQueryDTO queryDTO);

    /**
     * 病例阳性率TOP10
     * */
    List<VirusTypeDetectedRateVO> getTopPositiveRateBy(VirusCheckQueryDTO queryDTO);

    /**
     * 流感病毒分型鉴定分析
     * */
    List<VirusTypeDetectedRateVO> getInfluenzaVirusAnalysis(VirusCheckQueryDTO queryDTO);

    /**
     * 抗原分析结果
     * */
    PageInfo<VirusAntigenResultVO> getAntigenAnalysisResult(VirusCheckQueryDTO queryDTO);

    /**
     * 耐药性分析结果
     * */
    PageInfo<VirusResistanceResultVO> getResistanceAnalysisResult(VirusCheckQueryDTO queryDTO);

    /**
     * 常见耐药菌检出情况
     * */
    PageInfo<DrugResistantBacteriaVO> getCommonDrugResistantBacteria(VirusCheckQueryDTO queryDTO);

    /**
     * 不同等级医院呼吸道耐药菌检出率分析
     * */
    Map<String, List<DrugResistantHospitalVO>> getMDRCheckRateByGradeHospital(VirusCheckQueryDTO queryDTO);

    /**
     * 不同地区呼吸道耐药菌检出情况
     * */
    PageInfo<MDRCheckAreaDistributionVO> getMDRCheckByDistrict(VirusCheckQueryDTO queryDTO);

    /**
     * 对常用抗菌药物的敏感性
     * */
    List<DrugSituationVO> getDrugSensitivityBy(VirusCheckQueryDTO queryDTO);

    /**
     * 流感病毒抗原性分析-分型
     * */
    List<VirusAntigenResultVO> getInfluenzaPathogenType();

    /**
     * 流感病毒抗原性分析-参考抗原
     * */
    List<VirusAntigenResultVO> getInfluenzaReferPathogen();

    /**
     * 流感病毒抗原性分析-实验室列表
     * */
    List<VirusAntigenResultVO> getInfluenzaLabList();

    List<VirusCheckResultVO> getVirusCheckSituation(VirusCheckQueryDTO queryDTO);
}
