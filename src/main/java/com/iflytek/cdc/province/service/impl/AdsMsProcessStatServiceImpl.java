package com.iflytek.cdc.province.service.impl;

import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

import java.io.ByteArrayOutputStream;
import java.time.LocalDate;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.ToIntFunction;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.lang.Pair;
import com.google.common.collect.Lists;
import com.iflytek.cdc.edr.vo.AdsMsProcessInfectGXExcelVO;
import com.iflytek.cdc.edr.vo.AdsMsProcessSyndromeGXExcelVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.*;
import com.iflytek.cdc.province.mapper.pg.*;
import com.iflytek.cdc.province.model.brief.ManualBriefRangeDTO;
import com.iflytek.cdc.province.model.dto.FieldOperation;
import com.iflytek.cdc.province.model.vo.ReportCardStatVO;
import com.iflytek.cdc.province.model.vo.SyndromeOrgProcessStatsVO;
import com.iflytek.cdc.province.service.PredictionService;
import com.iflytek.cdc.province.utils.StatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.apiService.CdcPlatformApi;
import com.iflytek.cdc.edr.apiService.UapServiceApi;
import com.iflytek.cdc.edr.common.Common;
import com.iflytek.cdc.edr.dto.AwarenessWithCalibrateDTO;
import com.iflytek.cdc.edr.enums.AreaLevelEnum;
import com.iflytek.cdc.edr.enums.DateDimEnum;
import com.iflytek.cdc.edr.utils.DataUtils;
import com.iflytek.cdc.edr.utils.DateFormatUtil;
import com.iflytek.cdc.edr.utils.DimDateUtils;
import com.iflytek.cdc.edr.vo.AwarenessResultVO;
import com.iflytek.cdc.edr.vo.PopulationDataInfoVO;
import com.iflytek.cdc.province.model.brief.ManualBriefGenerateInfo;
import com.iflytek.cdc.province.model.brief.ManualBriefRecordVO;
import com.iflytek.cdc.province.model.dto.AdsMsProcessReqDTO;
import com.iflytek.cdc.province.model.dto.DateDim;
import com.iflytek.cdc.province.service.AdsMsProcessStatService;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.service.ManualBriefRecordService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Configuration
@ConditionalOnProperty(value = "dataService.version", havingValue = "v2")
@Slf4j
public class AdsMsProcessStatServiceImpl implements AdsMsProcessStatService {

    @Resource
    private DataUtils dataUtils;

    @Resource
    private DimDateUtils dimDateUtils;

    @Resource
    private AdsMsInfectProcessStatMapper adsMsInfectProcessStatMapper;

    @Resource
    private AdsMsSyndromeProcessMapper adsMsSyndromeProcessMapper;

    @Resource
    private CdcAdminServiceApi cdcAdminServiceApi;

    @Resource
    private UapServiceApi uapServiceApi;

    @Resource
    private CdcPlatformApi cdcPlatformApi;
    
    @Resource
    private ManualBriefRecordService manualBriefRecordService;

    @Resource
    private PredictionService predictionService;

    @Resource
    private AdsMsProcessInfoMapper adsMsProcessInfoMapper;

    @Resource
    private DimDateMapper dimDateMapper;

    @Resource
    private AdsMulProcessCaseInfoMapper adsMulProcessCaseInfoMapper;

    /**
     * 计算百分比
     */
    private static void setRatio(List<AdsMsProcessRespVO> vos) {
        final int total = vos.stream().mapToInt(MsProcessStatVO::getProcessNewCnt).filter(Objects::nonNull).sum();
        for (AdsMsProcessRespVO vo : vos) {
            vo.calculateAllRates(total);
        }
    }

    @Override
    public AdsMsProcessRespVO processStat(AdsMsProcessReqDTO query, String loginUserName) {
        AdsMsProcessRespVO respVO = (AdsMsProcessRespVO) dataUtils.getDateDimDataByWarningType(query.getWarningType(),
                    () -> infectedProcessStat(query),
                    () -> syndromeProcessStat(query));

        respVO.calculateAllRates(null);

        return respVO;
    }

    private AdsMsProcessRespVO infectedProcessStat(AdsMsProcessReqDTO query) {

        // 创建FieldOperation列表
        List<FieldOperation<AdsMsProcessRespVO>> operations = new ArrayList<>();
        operations.add(FieldOperation.<AdsMsProcessRespVO>builder().getter(AdsMsProcessRespVO::getIdentifyCnt).yoySetter(AdsMsProcessRespVO::setIdentifyCntYearGrowth).chainSetter(AdsMsProcessRespVO::setIdentifyCntChain).build());
        operations.add(FieldOperation.<AdsMsProcessRespVO>builder().getter(AdsMsProcessRespVO::getProcessNewCnt).yoySetter(AdsMsProcessRespVO::setProcessNewCntYearGrowth).chainSetter(AdsMsProcessRespVO::setProcessNewCntChain).build());
        operations.add(FieldOperation.<AdsMsProcessRespVO>builder().getter(AdsMsProcessRespVO::getProcessDeadCnt).yoySetter(AdsMsProcessRespVO::setProcessDeadCntYearGrowth).chainSetter(AdsMsProcessRespVO::setProcessDeadCntChain).build());
        operations.add(FieldOperation.<AdsMsProcessRespVO>builder().getter(AdsMsProcessRespVO::getProcessNowCnt).yoySetter(AdsMsProcessRespVO::setProcessNowCntYearGrowth).chainSetter(AdsMsProcessRespVO::setProcessNowCntChain).build());
        operations.add(FieldOperation.<AdsMsProcessRespVO>builder().getter(AdsMsProcessRespVO::getProcessDeadIllnessCnt).yoySetter(AdsMsProcessRespVO::setProcessDeadIllnessCntYearGrowth).chainSetter(AdsMsProcessRespVO::setProcessDeadIllnessCntChain).build());
        operations.add(FieldOperation.<AdsMsProcessRespVO>builder().getter(AdsMsProcessRespVO::getProcessCureCnt).yoySetter(AdsMsProcessRespVO::setProcessCureCntYearGrowth).chainSetter(AdsMsProcessRespVO::setProcessCureCntChain).build());
        operations.add(FieldOperation.<AdsMsProcessRespVO>builder().getter(AdsMsProcessRespVO::getProcessSevereCnt).yoySetter(AdsMsProcessRespVO::setProcessSevereCntYearGrowth).chainSetter(AdsMsProcessRespVO::setProcessSevereCntChain).build());
        return StatUtils.comparisonResult(query, adsMsInfectProcessStatMapper::processStat, operations);
    }

    //症候群用name置换code
    private AdsMsProcessRespVO syndromeProcessStat(AdsMsProcessReqDTO queryParam) {

        setQueryParamDiseaseCode(queryParam);
        queryParam.dealDate(queryParam);
        return adsMsSyndromeProcessMapper.processStat(queryParam);
    }

    @Override
    public List<AdsMsProcessRespVO> processStatLineChart(AdsMsProcessReqDTO queryParam, String loginUserName) {
        setQueryParamDiseaseCode(queryParam);
        setAreaInfoList(queryParam, loginUserName);
        queryParam.dealDate(queryParam);
        List<AdsMsProcessRespVO> adsMsProcessRespVOList = dataUtils.getDateDimDataByWarningType(queryParam.getWarningType(),
                () -> infectedProcessStatLineChart(queryParam),
                () -> syndromeProcessStatLineChart(queryParam)
        );
        return adsMsProcessRespVOList;
    }

    /**
     * 传染病监测病例总览
     */
    private List<AdsMsProcessRespVO> infectedProcessStatLineChart(AdsMsProcessReqDTO queryParam) {
        List<AdsMsProcessRespVO> msProcessStatVOS = adsMsInfectProcessStatMapper.dayCntStatByDetailTable(queryParam);
        msProcessStatVOS = StatUtils.fulfillAndConvertDayDateData(msProcessStatVOS, queryParam.getStartDate(), queryParam.getEndDate(), DateDimEnum.getByCode(queryParam.getDateDimType()), AdsMsProcessRespVO::getStatDate, AdsMsProcessRespVO::setStatDate, AdsMsProcessRespVO.dataConverter);
        if (queryParam.getDateDimType().equals(DateDimEnum.DAY.getCode())) {
            //msProcessStatVOS.addAll(listNewCntPre(queryParam));
            List<AdsMsProcessRespVO> predictList = predictAdsMsProcessData(queryParam, msProcessStatVOS);
            msProcessStatVOS.addAll(predictList);
        }
        return msProcessStatVOS;
    }

    /**
     * 带预测的统计
     */
    private List<AdsMsProcessRespVO> listNewCntPre(AdsMsProcessReqDTO queryParam) {

        if (queryParam.getEndDate() != null && queryParam.getEndDate().getTime() > new Date().getTime()) {
            queryParam.setStartDate(DateUtils.addDays(DateFormatUtil.getTheFistSecondOfOneDay(new Date()), 1));
            queryParam.setEndDate(DateUtils.addDays(queryParam.getStartDate(), 2));
            return adsMsInfectProcessStatMapper.listNewCntPre(queryParam);
        }
        return new ArrayList<>();
    }

    /**
     * 症候群监测病例总览
     */
    private List<AdsMsProcessRespVO> syndromeProcessStatLineChart(AdsMsProcessReqDTO queryParam) {
        setQueryParamDiseaseCode(queryParam);
        List<AdsMsProcessRespVO> adsMsProcessRespVOList = adsMsSyndromeProcessMapper.timeTrendStat(queryParam);
        if (queryParam.getDateDimType().equals(DateDimEnum.DAY.getCode())) {
            // adsMsProcessRespVOList.addAll(listNewCntPre(queryParam));
            List<AdsMsProcessRespVO> predictList = predictAdsMsProcessData(queryParam, adsMsProcessRespVOList);
            adsMsProcessRespVOList.addAll(predictList);
        }
        return adsMsProcessRespVOList;
    }

    @Override
    public MsInfectTypeStatVO notifiableInfectTypeStat(AdsMsProcessReqDTO queryParam, String loginUserName) {
        setAreaInfoList(queryParam, loginUserName);
        queryParam.dealDate(queryParam);
        return adsMsInfectProcessStatMapper.notifiableInfectTypeStat(queryParam);
    }

    @Override
    public List<MsInfectNewCntVO> groupInfectNewCntTop10(AdsMsProcessReqDTO queryParam, String loginUserName) {
        setAreaInfoList(queryParam, loginUserName);
        try (Page<MsInfectNewCntVO> ignored = PageHelper.startPage(1, 10)) {
            queryParam.dealDate(queryParam);
            return adsMsInfectProcessStatMapper.groupInfectNewCnt(queryParam);
        }
    }

    @Override
    public List<MsInfectNewCntVO> groupInfectNewCntDeclineTop10(AdsMsProcessReqDTO queryParam, String loginUserName) {
        setAreaInfo(queryParam, loginUserName);
        try (Page<MsInfectNewCntVO> ignored = PageHelper.startPage(1, 10)) {
            queryParam.dealDate(queryParam);
            return adsMsInfectProcessStatMapper.groupInfectNewCntDecline(queryParam);
        }
    }

    @Override
    public List<MsInfectCntVO> groupInfectCnt(AdsMsProcessReqDTO queryParam, String loginUserName) {
        queryParam.dealDate(queryParam);
        return buildInfectCntVO(queryParam, loginUserName,  adsMsInfectProcessStatMapper::groupInfectCnt,
                adsMsInfectProcessStatMapper::infectCntTotal,true);
    }

    @Override
    public PageInfo<MsInfectCntVO> groupInfectCntPage(AdsMsProcessReqDTO queryParam, String loginUserName) {
        try (Page<MsInfectCntVO> ignored = PageHelper.startPage(queryParam.getPageIndex(), queryParam.getPageSize())) {
            return new PageInfo<>(groupInfectCnt(queryParam, loginUserName));
        }
    }

    /**
     * 设置区域信息
     * TODO: 和 CommonUtilService.setAreaQueryDTO 方法重复，可以优化
     */
    private void setAreaInfo(AdsMsProcessReqDTO queryDTO, String loginUserName) {
        uapServiceApi.setProvinceAndCityAndDistrict(loginUserName, queryDTO,
                AdsMsProcessReqDTO::setProvinceCodes,
                AdsMsProcessReqDTO::setCityCodes,
                AdsMsProcessReqDTO::setDistrictCodes,
                (d, areaLevel) -> {
                    if (d.getAreaLevel() == null) {
                        d.setAreaLevel(areaLevel);
                    }
                });
        // 融合权限和过滤条件
        if (StrUtil.isNotBlank(queryDTO.getDistrictCode())) {
            if (CollUtil.isEmpty(queryDTO.getDistrictCodes())) {
                queryDTO.setDistrictCodes(Collections.singletonList(queryDTO.getDistrictCode()));
            } else {
                queryDTO.setDistrictCodes(CommonUtilService.intersection(Collections.singletonList(queryDTO.getDistrictCode()), queryDTO.getDistrictCodes()));
            }
        }
        if (StrUtil.isNotBlank(queryDTO.getCityCode())) {
            if (CollUtil.isEmpty(queryDTO.getCityCodes())) {
                queryDTO.setCityCodes(Collections.singletonList(queryDTO.getCityCode()));
            } else {
                queryDTO.setCityCodes(CommonUtilService.intersection(Collections.singletonList(queryDTO.getCityCode()), queryDTO.getCityCodes()));
            }
        }
        if (StrUtil.isNotBlank(queryDTO.getStreetCode())) {
            if (CollUtil.isEmpty(queryDTO.getStreetCodes())) {
                queryDTO.setStreetCodes(Collections.singletonList(queryDTO.getStreetCode()));
            } else {
                queryDTO.setStreetCodes(CommonUtilService.intersection(Collections.singletonList(queryDTO.getStreetCode()), queryDTO.getStreetCodes()));
            }
        }
        else{
            queryDTO.setStreetCodes(new ArrayList<>());
        }
    }

    /**
     * 设置区域信息多选
     * TODO: 和 CommonUtilService.setAreaQueryDTO 方法重复，可以优化
     */
    private void setAreaInfoList(AdsMsProcessReqDTO queryDTO, String loginUserName) {
        uapServiceApi.setProvinceAndCityAndDistrict(loginUserName, queryDTO,
                AdsMsProcessReqDTO::setProvinceCodes,
                AdsMsProcessReqDTO::setCityCodes,
                AdsMsProcessReqDTO::setDistrictCodes,
                (d, areaLevel) -> {
                    if (d.getAreaLevel() == null) {
                        d.setAreaLevel(areaLevel);
                    }
                });
        // 融合权限和过滤条件
        if (CollectionUtil.isNotEmpty(queryDTO.getDistrictCodeList())) {
            if (CollUtil.isEmpty(queryDTO.getDistrictCodes())) {
                queryDTO.setDistrictCodes(queryDTO.getDistrictCodeList());
            } else {
                queryDTO.setDistrictCodes(CommonUtilService.intersection(queryDTO.getDistrictCodeList(), queryDTO.getDistrictCodes()));
            }
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getCityCodeList())) {
            if (CollUtil.isEmpty(queryDTO.getCityCodes())) {
                queryDTO.setCityCodes(queryDTO.getCityCodeList());
            } else {
                queryDTO.setCityCodes(CommonUtilService.intersection(queryDTO.getCityCodeList(), queryDTO.getCityCodes()));
            }
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getStreetCodeList())) {
            if (CollUtil.isEmpty(queryDTO.getStreetCodes())) {
                queryDTO.setStreetCodes(queryDTO.getStreetCodeList());
            } else {
                queryDTO.setStreetCodes(CommonUtilService.intersection(queryDTO.getStreetCodeList(), queryDTO.getStreetCodes()));
            }
        }
        else{
            queryDTO.setStreetCodes(new ArrayList<>());
        }
    }

    @Override
    public List<AdsMsProcessRespVO> groupAreaDetail(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setQueryParamDiseaseCode(reqDTO);
        setAreaInfoList(reqDTO, loginUserName);

        List<AdsMsProcessRespVO> respVOS = new ArrayList<>();
        //当前区域级别查询
        List<AdsMsProcessRespVO> currentRespVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> buildStat(reqDTO, adsMsInfectProcessStatMapper::groupAreaCurrentLevel, AdsMsProcessRespVO::getDescription, AdsMsProcessRespVO::setDescription, AdsMsProcessRespVO.buildOtherProperties()),
                () -> buildStat(reqDTO, adsMsSyndromeProcessMapper::groupAreaCurrentLevel, AdsMsProcessRespVO::getDescription, AdsMsProcessRespVO::setDescription, AdsMsProcessRespVO.buildOtherProperties())
        );
        //子区域级别查询
        List<AdsMsProcessRespVO> subRespVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> buildStat(reqDTO, adsMsInfectProcessStatMapper::groupArea, AdsMsProcessRespVO::getDescription, AdsMsProcessRespVO::setDescription, AdsMsProcessRespVO.buildOtherProperties()),
                () -> buildStat(reqDTO, adsMsSyndromeProcessMapper::groupArea, AdsMsProcessRespVO::getDescription, AdsMsProcessRespVO::setDescription, AdsMsProcessRespVO.buildOtherProperties())
                );
        respVOS.addAll(currentRespVOS);
        respVOS.addAll(subRespVOS);

        List<String> provinceCodes = reqDTO.getProvinceCodes();
        List<String> cityCodes = new ArrayList<>(reqDTO.getCityCodes());
        List<String> districtCodes = new ArrayList<>(reqDTO.getDistrictCodes());
        List<String> streetCodes = new ArrayList<>(reqDTO.getStreetCodes());

        List<String> dCodes = respVOS.stream().filter(v -> AreaLevelEnum.DISTRICT.getValue().equals(v.getAreaLevel())).map(AdsMsProcessRespVO::getAreaCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(dCodes)) {
            districtCodes.addAll(dCodes);
        }
        List<String> cCodes = respVOS.stream().filter(v -> AreaLevelEnum.CITY.getValue().equals(v.getAreaLevel())).map(AdsMsProcessRespVO::getAreaCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(cCodes)) {
            cityCodes.addAll(cCodes);
        }

        List<String> sCodes = respVOS.stream().filter(v -> AreaLevelEnum.STREET.getValue().equals(v.getAreaLevel())).map(AdsMsProcessRespVO::getAreaCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(sCodes)) {
            streetCodes.addAll(sCodes);
        }

        final Map<String, PopulationDataInfoVO> areaPopulations = cdcAdminServiceApi.groupByAreaCodesIncludeStreet(reqDTO.getEndDate(), provinceCodes, cityCodes, districtCodes, streetCodes);
        for (AdsMsProcessRespVO respVO : respVOS) {
            final PopulationDataInfoVO populationDataInfoVO = areaPopulations.get(respVO.getAreaCode());
            if (populationDataInfoVO != null) {
                respVO.setPopulation(populationDataInfoVO.getResidentPopulation());
            }
        }
        setRatio(respVOS);
        return respVOS;
    }

    @Override
    public List<AdsMsProcessRespVO> groupArea(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setQueryParamDiseaseCode(reqDTO);
        setAreaInfo(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> respVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> buildStat(reqDTO, adsMsInfectProcessStatMapper::groupArea,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription,
                        AdsMsProcessRespVO.buildOtherProperties()),
                () -> buildStat(reqDTO, adsMsSyndromeProcessMapper::groupArea,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription,
                        AdsMsProcessRespVO.buildOtherProperties())
        );
        List<String> provinceCodes = reqDTO.getProvinceCodes();
        List<String> cityCodes = new ArrayList<>(reqDTO.getCityCodes());
        List<String> districtCodes = new ArrayList<>(reqDTO.getDistrictCodes());
        List<String> streetCodes = new ArrayList<>(reqDTO.getStreetCodes());

        List<String> dCodes = respVOS.stream().filter(v -> AreaLevelEnum.DISTRICT.getValue().equals(v.getAreaLevel())).map(AdsMsProcessRespVO::getAreaCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(dCodes)) {
            districtCodes.addAll(dCodes);
        }
        List<String> cCodes = respVOS.stream().filter(v -> AreaLevelEnum.CITY.getValue().equals(v.getAreaLevel())).map(AdsMsProcessRespVO::getAreaCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(cCodes)) {
            cityCodes.addAll(cCodes);
        }
        List<String> sCodes = respVOS.stream().filter(v -> AreaLevelEnum.STREET.getValue().equals(v.getAreaLevel())).map(AdsMsProcessRespVO::getAreaCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(sCodes)) {
            streetCodes.addAll(sCodes);
        }

        final Map<String, PopulationDataInfoVO> areaPopulations = cdcAdminServiceApi.groupByAreaCodesIncludeStreet(reqDTO.getEndDate(), provinceCodes, cityCodes, districtCodes, streetCodes);
        for (AdsMsProcessRespVO respVO : respVOS) {
            final PopulationDataInfoVO populationDataInfoVO = areaPopulations.get(respVO.getAreaCode());
            if (populationDataInfoVO != null) {
                respVO.setPopulation(populationDataInfoVO.getResidentPopulation());
            }
        }
        setRatio(respVOS);
        return respVOS;
    }

    private List<AdsMsProcessRespVO> buildStat(AdsMsProcessReqDTO reqDTO,
                                               Function<AdsMsProcessReqDTO, List<AdsMsProcessRespVO>> queryFunction,
                                               Function<AdsMsProcessRespVO, String> descriptionGetter,
                                               BiConsumer<AdsMsProcessRespVO, String> descriptionSetter,
                                               List<Pair<Function<AdsMsProcessRespVO, ?>, BiConsumer<AdsMsProcessRespVO, ?>>> otherProperties) {

        return StatUtils.build(reqDTO,
                               AdsMsProcessRespVO::new,
                               descriptionGetter,
                               descriptionSetter,
                               queryFunction,
                               AdsMsProcessRespVO.buildCommonOperations(),
                               otherProperties);
    }

    private List<AdsMsProcessRespVO> buildStat(AdsMsProcessReqDTO reqDTO,
                                               Function<AdsMsProcessReqDTO, List<AdsMsProcessRespVO>> queryFunction,
                                               Function<AdsMsProcessRespVO, String> descriptionGetter,
                                               BiConsumer<AdsMsProcessRespVO, String> descriptionSetter) {

        return StatUtils.build(reqDTO,
                               AdsMsProcessRespVO::new,
                               descriptionGetter,
                               descriptionSetter,
                               queryFunction,
                               AdsMsProcessRespVO.buildCommonOperations(),
                               null);
    }



    @Override
    public List<AdsMsProcessRespVO> groupAreaDripDetail(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setQueryParamDiseaseCode(reqDTO);
        setAreaInfo(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> respVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> buildStat(reqDTO, adsMsInfectProcessStatMapper::groupAreaDripDetail,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription,
                        AdsMsProcessRespVO.buildOtherProperties()),
                () -> adsMsSyndromeProcessMapper.groupAreaDripDetail(reqDTO)
        );
        List<String> provinceCodes = reqDTO.getProvinceCodes();
        List<String> cityCodes = new ArrayList<>(reqDTO.getCityCodes());
        List<String> districtCodes = new ArrayList<>(reqDTO.getDistrictCodes());
        List<String> streetCodes = new ArrayList<>(reqDTO.getStreetCodes());

        List<String> dCodes = respVOS.stream().filter(v -> AreaLevelEnum.DISTRICT.getValue().equals(v.getAreaLevel())).map(AdsMsProcessRespVO::getAreaCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(dCodes)) {
            districtCodes.addAll(dCodes);
        }
        List<String> cCodes = respVOS.stream().filter(v -> AreaLevelEnum.CITY.getValue().equals(v.getAreaLevel())).map(AdsMsProcessRespVO::getAreaCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(cCodes)) {
            cityCodes.addAll(cCodes);
        }
        List<String> sCodes = respVOS.stream().filter(v -> AreaLevelEnum.STREET.getValue().equals(v.getAreaLevel())).map(AdsMsProcessRespVO::getAreaCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(sCodes)) {
            streetCodes.addAll(sCodes);
        }

        final Map<String, PopulationDataInfoVO> areaPopulations = cdcAdminServiceApi.groupByAreaCodesIncludeStreet(reqDTO.getEndDate(), provinceCodes, cityCodes, districtCodes, streetCodes);
        for (AdsMsProcessRespVO respVO : respVOS) {
            final PopulationDataInfoVO populationDataInfoVO = areaPopulations.get(respVO.getAreaCode());
            if (populationDataInfoVO != null) {
                respVO.setPopulation(populationDataInfoVO.getResidentPopulation());
            }
        }
        setRatio(respVOS);
        return respVOS;
    }

    @Override
    public ResponseEntity<byte[]> groupAreaExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.groupArea(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByWarningType(reqDTO.getWarningType(), vos,
                AdsMsProcessInfectAreaExcelVO.class,
                AdsMsProcessSyndromeAreaExcelVO.class);
    }

    @Override
    public ResponseEntity<byte[]> groupAreaDetailExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.groupAreaDripDetail(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByWarningType(reqDTO.getWarningType(), vos,
                AdsMsProcessInfectAreaExcelVO.class,
                AdsMsProcessSyndromeAreaExcelVO.class);
    }

    @Override
    public ManualBriefRecordVO groupInfectCntExport(AdsMsProcessReqDTO reqDTO, String loginUserName) {
        ManualBriefGenerateInfo info = ManualBriefGenerateInfo.of(reqDTO, userInfo.get().getId(), userInfo.get().getLoginName());
        List<MsInfectCntVO> data = groupInfectCnt(reqDTO, loginUserName);
        if (!CollectionUtils.isEmpty(data)) {
            data = dealGroupInfectCntExportChildren(data);
            StatUtils.replaceDashWithEmpty(data);
        }
        return manualBriefRecordService.generateByStats(info, data, reqDTO.getIsGuangxi() ,true);
    }

    @Override
    public ManualBriefRecordVO groupOtherInfectCntExport(AdsMsProcessReqDTO reqDTO, String loginUserName) {
        ManualBriefGenerateInfo info = ManualBriefGenerateInfo.of(reqDTO, userInfo.get().getId(), userInfo.get().getLoginName());
        List<MsInfectCntVO> data = groupInfectCnt(reqDTO, loginUserName);
        return manualBriefRecordService.generateByStats(info, data, reqDTO.getIsGuangxi(), false);
    }

    private List<MsInfectCntVO>  dealGroupInfectCntExportChildren(List<MsInfectCntVO> data){
        List<MsInfectCntVO> res = new ArrayList<>();
        data.forEach(vo-> processChildren(res,vo));
        return res;
    }
    private void processChildren(List<MsInfectCntVO> res,MsInfectCntVO parent) {
        if ("传染病病种".equals(parent.getLevelType())){
            parent.setInfectTypeName(parent.getInfectName());
            parent.setInfectName(null);
        }
        if (Common.AMOUNT_TO.equals(parent.getInfectType())){
            parent.setInfectName(null);
        }
        res.add(parent);
        List<MsInfectCntVO> children = parent.getChildren();
        if (!CollectionUtils.isEmpty(children)) {
            for (MsInfectCntVO child : children) {
                //业务代码 只处理三层级
                if ("传染病亚型".equals(child.getLevelType())){
                    child.setInfectSubtypeName(child.getInfectName());
                    child.setInfectName(parent.getInfectName());
                }
                child.setInfectTypeName(parent.getInfectTypeName());
                processChildren(res,child);
            }
        }
    }

    @Override
    public List<MsInfectTransmitTypeCntVO> groupInfectTransmitTypeCnt(AdsMsProcessReqDTO queryParam, String loginUserName) {
        setAreaInfoList(queryParam, loginUserName);
        queryParam.dealDate(queryParam);
        List<MsInfectTransmitTypeCntVO> msInfectTransmitTypeCntVOS =
                adsMsInfectProcessStatMapper.groupInfectTransmitTypeCnt(queryParam);
        msInfectTransmitTypeCntVOS.forEach(m -> {
            m.setProcessNewCntYearGrowth(DataUtils.getGrowthRateStr(m.getProcessNewCntLastY(), m.getProcessNewCnt()));
            m.setProcessNewCntChain(DataUtils.getGrowthRateStr(m.getProcessNewCntLast(), m.getProcessNewCnt()));

            m.setProcessDeadCntYearGrowth(DataUtils.getGrowthRateStr(m.getProcessDeadCntLastY(), m.getProcessDeadCnt()));
            m.setProcessDeadCntChain(DataUtils.getGrowthRateStr(m.getProcessDeadCntLast(), m.getProcessDeadCnt()));
        });
        return msInfectTransmitTypeCntVOS;
    }

    @Override
    public byte[] groupInfectTransmitTypeCntExport(AdsMsProcessReqDTO queryParam, String loginUserName) {
        List<MsInfectTransmitTypeCntVO> cntVOS = groupInfectTransmitTypeCnt(queryParam, loginUserName);

        MsInfectTransmitTypeCntVO typeCntVO = new MsInfectTransmitTypeCntVO();
        typeCntVO.setTransmitType("总计");
        typeCntVO.setProcessNewCnt(sum(cntVOS, MsInfectTransmitTypeCntVO::getProcessNewCnt));
        typeCntVO.setProcessNewCntLast(sum(cntVOS, MsInfectTransmitTypeCntVO::getProcessNewCntLast));
        typeCntVO.setProcessNewCntLastY(sum(cntVOS, MsInfectTransmitTypeCntVO::getProcessNewCntLastY));
        typeCntVO.setProcessNewCntYearGrowth(DataUtils.getGrowthRateStr(typeCntVO.getProcessNewCntLastY(), typeCntVO.getProcessNewCnt()));
        typeCntVO.setProcessNewCntChain(DataUtils.getGrowthRateStr(typeCntVO.getProcessNewCntLast(), typeCntVO.getProcessNewCnt()));

        typeCntVO.setProcessDeadCnt(sum(cntVOS, MsInfectTransmitTypeCntVO::getProcessDeadCnt));
        typeCntVO.setProcessDeadCntLast(sum(cntVOS, MsInfectTransmitTypeCntVO::getProcessNewCntLast));
        typeCntVO.setProcessDeadCntLastY(sum(cntVOS, MsInfectTransmitTypeCntVO::getProcessNewCntLastY));
        typeCntVO.setProcessDeadCntYearGrowth(DataUtils.getGrowthRateStr(typeCntVO.getProcessDeadCntLastY(), typeCntVO.getProcessDeadCnt()));
        typeCntVO.setProcessDeadCntChain(DataUtils.getGrowthRateStr(typeCntVO.getProcessDeadCntLast(), typeCntVO.getProcessDeadCnt()));
        cntVOS.add(0, typeCntVO);
        PopulationDataInfoVO populationDataInfoVO = cdcAdminServiceApi.statByAreaCodes(queryParam.getEndDate(), queryParam.getProvinceCodes(),
                queryParam.getCityCodes(), queryParam.getDistrictCodes());
        cntVOS.forEach(c -> {
            c.setResidentPopulation(populationDataInfoVO.getResidentPopulation());
            c.setNewRate(DataUtils.percent(DataUtils.divide(c.getProcessNewCnt(), c.getResidentPopulation())));
            c.setDeadRate(DataUtils.percent(DataUtils.divide(c.getProcessDeadCnt(), c.getResidentPopulation())));
        });
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        EasyExcel.write(outputStream, MsInfectTransmitTypeCntVO.class)
                .sheet().doWrite(cntVOS);
        return outputStream.toByteArray();
    }


    @Override
    public List<AdsMsProcessRespVO> groupAddrType(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> respVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> buildStat(reqDTO, adsMsInfectProcessStatMapper::groupAddrType,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription),
                () -> StatUtils.fulfillPeriodValue(reqDTO, adsMsSyndromeProcessMapper::groupAddrType, (last, curr) -> curr.getDescription().equals(last.getDescription()), AdsMsProcessRespVO.currValueAndLastValueMappingList())
        );
        setPopulation(reqDTO, respVOS);
        setRatio(respVOS);
        return respVOS;
    }

    /**
     * 设置人口数
     */
    private void setPopulation(AdsMsProcessReqDTO reqDTO, List<AdsMsProcessRespVO> respVOS) {
        if (StrUtil.isNotBlank(reqDTO.getStreetCode())) {
            // 街道无人口数据
            return;
        }
        final List<String> provinceCodes = reqDTO.getProvinceCodes();
        final List<String> cityCodes = reqDTO.getCityCodes();
        final List<String> districtCodes = reqDTO.getDistrictCodes();
        final List<PopulationDataInfoVO> areaPopulations = cdcAdminServiceApi.listByAreaCodes(reqDTO.getEndDate(), provinceCodes, cityCodes, districtCodes);
        final int sum = areaPopulations.stream().mapToInt(PopulationDataInfoVO::getResidentPopulation).sum();
        for (AdsMsProcessRespVO respVO : respVOS) {
            respVO.setPopulation(sum);
        }
    }

    @Override
    public ResponseEntity<byte[]> groupAddrTypeExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.groupAddrType(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByWarningType(reqDTO.getWarningType(), vos,
                AdsMsProcessInfectAddrTypeExcelVO.class,
                AdsMsProcessSyndromeAddrTypeExcelVO.class);
    }

    @Override
    public List<AdsMsProcessRespVO> groupAge(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setQueryParamDiseaseCode(reqDTO);
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> respVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> buildStat(reqDTO, adsMsInfectProcessStatMapper::groupAge,
                        AdsMsProcessRespVO::getPatientAgeGroup,
                        AdsMsProcessRespVO::setPatientAgeGroup,
                        AdsMsProcessRespVO.buildOtherProperties()),
                () -> buildStat(reqDTO, adsMsSyndromeProcessMapper::groupAge,
                        AdsMsProcessRespVO::getPatientAgeGroup,
                        AdsMsProcessRespVO::setPatientAgeGroup,
                        AdsMsProcessRespVO.buildOtherProperties())
               );
        respVOS = sortByAgeOrder(respVOS);
        setPopulation(reqDTO, respVOS);
        setRatio(respVOS);
        return respVOS;
    }

    private List<AdsMsProcessRespVO> sortByAgeOrder(List<AdsMsProcessRespVO> respVOS) {
        return respVOS.stream()
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(vo ->
                        vo.getOrder() == null ? Integer.MAX_VALUE : vo.getOrder()
                ))
                .collect(Collectors.toList());
    }

    @Override
    public ResponseEntity<byte[]> groupAgeExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.groupAge(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByWarningType(reqDTO.getWarningType(), vos,
                AdsMsProcessInfectAgeExcelVO.class,
                AdsMsProcessSyndromeAgeExcelVO.class);
    }

    private Integer sum(List<MsInfectTransmitTypeCntVO> cntVOS, ToIntFunction<MsInfectTransmitTypeCntVO> sumFunction) {
        return cntVOS.stream().mapToInt(sumFunction).sum();
    }

    @Override
    public List<AdsMsProcessRespVO> groupSex(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setQueryParamDiseaseCode(reqDTO);
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> respVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> buildStat(reqDTO, adsMsInfectProcessStatMapper::groupSex,
                        AdsMsProcessRespVO::getPatientSexName,
                        AdsMsProcessRespVO::setPatientSexName),
                () -> buildStat(reqDTO, adsMsSyndromeProcessMapper::groupSex,
                        AdsMsProcessRespVO::getPatientSexName,
                        AdsMsProcessRespVO::setPatientSexName)
               );
        setPopulation(reqDTO, respVOS);
        setRatio(respVOS);
        return respVOS;
    }

    @Override
    public ResponseEntity<byte[]> groupSexExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.groupSex(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByWarningType(reqDTO.getWarningType(), vos,
                AdsMsProcessInfectSexExcelVO.class, AdsMsProcessSyndromeSexExcelVO.class);
    }

    @Override
    public List<AdsMsProcessRespVO> groupJob(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setQueryParamDiseaseCode(reqDTO);
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> respVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> buildStat(reqDTO, adsMsInfectProcessStatMapper::groupJob,
                        AdsMsProcessRespVO::getPatientJob,
                        AdsMsProcessRespVO::setPatientJob),
                () -> buildStat(reqDTO, adsMsSyndromeProcessMapper::groupJob,
                        AdsMsProcessRespVO::getPatientJob,
                        AdsMsProcessRespVO::setPatientJob)
        );
        setPopulation(reqDTO, respVOS);
        setRatio(respVOS);
        return respVOS;
    }

    @Override
    public ResponseEntity<byte[]> groupJobExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.groupJob(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByWarningType(reqDTO.getWarningType(), vos,
                AdsMsProcessInfectJobExcelVO.class,
                AdsMsProcessSyndromeJobExcelVO.class);
    }

    @Override
    public List<AdsMsProcessRespVO> listNewCntPre(AdsMsProcessReqDTO queryParam, String loginUserName) {
        setAreaInfo(queryParam, loginUserName);
        return adsMsInfectProcessStatMapper.listNewCntPre(queryParam);
    }

    @Override
    public List<AdsMsProcessRespVO> monthTimeTrend(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        // 此处需求变更，起始月的时间会被过滤掉，现在的需求是要包含起始月
        reqDTO.setStartDate(DateUtils.addMonths(reqDTO.getStartDate(), -1));
        reqDTO.setDateDims(dimDateUtils.getDateDims(DateDimEnum.MONTH.getCode(), reqDTO.getStartDate(), reqDTO.getEndDate()));
        reqDTO.setDateDimType(DateDimEnum.MONTH.getCode());
        List<AdsMsProcessRespVO> monthDescVO = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> buildStat(reqDTO, adsMsInfectProcessStatMapper::findDistribution,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription),
                () -> buildStat(reqDTO, adsMsSyndromeProcessMapper::findDistribution,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription)
        );
        setPopulation(reqDTO, monthDescVO);
        setRatio(monthDescVO);
        return monthDescVO;
    }

    @Override
    public ResponseEntity<byte[]> monthTimeTrendExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.monthTimeTrend(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByWarningType(reqDTO.getWarningType(), vos,
                AdsMsProcessInfectMonthExcelVO.class,
                AdsMsProcessSyndromeMonthExcelVO.class);
    }

    @Override
    public List<AdsMsProcessRespVO> quarterTimeTrend(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        // 此处需求变更，起始月的时间会被过滤掉，现在的需求是要包含起始月
        reqDTO.setStartDate(DateUtils.addMonths(reqDTO.getStartDate(), -3));
        reqDTO.setDateDims(dimDateUtils.getDateDims(DateDimEnum.QUARTER.getCode(), reqDTO.getStartDate(), reqDTO.getEndDate()));
        reqDTO.setDateDimType(DateDimEnum.QUARTER.getCode());
        List<AdsMsProcessRespVO> quarterDescVO = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> buildStat(reqDTO, adsMsInfectProcessStatMapper::findDistribution,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription),
                () -> buildStat(reqDTO, adsMsSyndromeProcessMapper::findDistribution,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription)
        );
        setPopulation(reqDTO, quarterDescVO);
        setRatio(quarterDescVO);
        return quarterDescVO;
    }

    @Override
    public ResponseEntity<byte[]> quarterTimeTrendExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.quarterTimeTrend(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByWarningType(reqDTO.getWarningType(), vos,
                AdsMsProcessInfectQuarterExcelVO.class,
                AdsMsProcessSyndromeQuarterExcelVO.class);
    }

    @Override
    public List<AdsMsProcessRespVO> yearTimeTrend(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        reqDTO.setDateDims(dimDateUtils.getDateDims(DateDimEnum.YEAR.getCode(), reqDTO.getStartDate(), reqDTO.getEndDate()));
        reqDTO.setDateDimType(DateDimEnum.YEAR.getCode());
        List<AdsMsProcessRespVO> yearDescVO = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> buildStat(reqDTO, adsMsInfectProcessStatMapper::findDistribution,
                AdsMsProcessRespVO::getDescription,
                AdsMsProcessRespVO::setDescription),
                () -> buildStat(reqDTO, adsMsSyndromeProcessMapper::findDistribution,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription)
        );
        setPopulation(reqDTO, yearDescVO);
        setRatio(yearDescVO);
        return yearDescVO;
    }

    @Override
    public ResponseEntity<byte[]> yearTimeTrendExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.yearTimeTrend(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByWarningType(reqDTO.getWarningType(), vos,
                AdsMsProcessInfectYearExcelVO.class,
                AdsMsProcessSyndromeYearExcelVO.class);
    }

    @Override
    public List<AdsMsProcessRespVO> halfYearTimeTrend(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        resetDateRange(reqDTO);
        reqDTO.setDateDimType(DateDimEnum.HALF_YEAR.getCode());
        List<AdsMsProcessRespVO> monthDescVOList = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> buildStat(reqDTO, adsMsInfectProcessStatMapper::findDistribution,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription),
                () -> buildStat(reqDTO, adsMsSyndromeProcessMapper::findDistribution,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription)
        );
        setPopulation(reqDTO, monthDescVOList);
        setRatio(monthDescVOList);
        return monthDescVOList;
    }

    /**
     * 当日期范围选在一个月内时 对日期范围进行重新设置
     */
    private void resetDateRange(AdsMsProcessReqDTO reqDTO) {
        int startYear = DateUtil.year(reqDTO.getStartDate());
        int endYear = DateUtil.year(reqDTO.getEndDate());

        int startMonth = DateUtil.month(reqDTO.getStartDate()) + 1;
        int endMonth = DateUtil.month(reqDTO.getEndDate()) + 1;
        // 日期范围选在一个月内的情况
        if (startYear == endYear && startMonth == endMonth) {
            if (startMonth + endMonth <= 12) {
                Date startDate = reqDTO.getStartDate();
                Date newStartDate = com.iflytek.cdc.province.utils.DateUtils.addMonths(startDate, 1 - startMonth);
                reqDTO.setStartDate(newStartDate);
            } else {
                Date endDate = reqDTO.getEndDate();
                Date newEndDate = com.iflytek.cdc.province.utils.DateUtils.addMonths(endDate, 12 - endMonth);
                reqDTO.setEndDate(newEndDate);
            }
            //return true;
        }
//        return false;
    }


    @Override
    public ResponseEntity<byte[]> halfYearTimeTrendExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.halfYearTimeTrend(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByWarningType(reqDTO.getWarningType(), vos,
                AdsMsProcessInfectHalfYearExcelVO.class,
                AdsMsProcessSyndromeHalfYearExcelVO.class);
    }

    @Override
    public List<AdsMsProcessRespVO> overall(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        if (DateDimEnum.YEAR.getCode().equals(reqDTO.getDateDimType()) && null != reqDTO.getBeforeYear()) {
            return overallForYear(reqDTO, loginUserId, loginUserName);
        }
        setAreaInfoList(reqDTO, loginUserName);
        final Date reqStartDate = reqDTO.getStartDate();
        final Date reqEndDate = reqDTO.getEndDate();
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), reqStartDate, reqEndDate));
        List<AdsMsProcessRespVO> respVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> getInfectOverall(reqDTO),
                () -> getSyndromeOverall(reqDTO)
        );

        // 设置指定年份的同期值（在原统计时间范围内补充）
        if (reqDTO.getBeforeYear() != null && reqStartDate != null && reqEndDate != null && CollUtil.isNotEmpty(respVOS)) {
            this.coverBeforeYearNewCnt(reqDTO, reqStartDate, reqEndDate, respVOS);
        }

        // 设置预测值（在原统计时间范围后追加）
        DateDim.PreDate preDate = DateDim.getPreDate(reqDTO.getDateDimType(), reqEndDate, 3);
        reqDTO.setStartDate(preDate.getStartDate());
        reqDTO.setEndDate(preDate.getEndDate());
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), preDate.getStartDate(), preDate.getEndDate()));
        List<AdsMsProcessRespVO> preVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> getInfectOverall(reqDTO),
                () -> getSyndromeOverall(reqDTO)
        );

        for (AdsMsProcessRespVO preVO : preVOS) {
            preVO.setProcessNewCnt(preVO.getProcessNewCntPre());
        }
        respVOS.addAll(preVOS);

        List<DateDim> dateDims = dimDateUtils.getDateDims(reqDTO.getDateDimType(), reqStartDate, preDate.getEndDate());
        respVOS = dimDateUtils.fillTimeData(reqDTO.getDateDimType(), AdsMsProcessRespVO.class, dateDims, respVOS);
        return respVOS;
    }

    /**
     *
     * @param reqDTO
     * @param loginUserId
     * @param loginUserName
     * @return
     */
    private List<AdsMsProcessRespVO> overallForYear(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        final Date reqStartDate = reqDTO.getStartDate();
        Integer beforeYear = reqDTO.getBeforeYear();
        Date newStartDate;
        if (beforeYear >= 3 && beforeYear <= 10) {
            newStartDate = DateUtils.setYears(reqStartDate, DateUtil.year(new Date()) - beforeYear + 1);
        } else {
            newStartDate = DateUtils.setYears(reqStartDate, beforeYear);
        }
        final Date reqEndDate = reqDTO.getEndDate();
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), newStartDate, reqEndDate));
        List<AdsMsProcessRespVO> respVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> getInfectOverall(reqDTO),
                () -> getSyndromeOverall(reqDTO)
        );
        // 设置预测值（在原统计时间范围后追加）
        DateDim.PreDate preDate = DateDim.getPreDate(reqDTO.getDateDimType(), reqEndDate, 3);
        reqDTO.setStartDate(preDate.getStartDate());
        reqDTO.setEndDate(preDate.getEndDate());
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), preDate.getStartDate(), preDate.getEndDate()));
        List<AdsMsProcessRespVO> preVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> getInfectOverall(reqDTO),
                () -> getSyndromeOverall(reqDTO)
        );

        for (AdsMsProcessRespVO preVO : preVOS) {
            preVO.setProcessNewCnt(preVO.getProcessNewCntPre());
        }
        respVOS.addAll(preVOS);

        List<DateDim> dateDims = dimDateUtils.getDateDims(reqDTO.getDateDimType(), newStartDate, preDate.getEndDate());
        respVOS = dimDateUtils.fillTimeData(reqDTO.getDateDimType(), AdsMsProcessRespVO.class, dateDims, respVOS);
        return respVOS;
    }

    private void coverBeforeYearNewCnt(AdsMsProcessReqDTO reqDTO, Date reqStartDate, Date reqEndDate, List<AdsMsProcessRespVO> respVOS) {
        int offset = reqDTO.getBeforeYear() - DateUtil.year(reqStartDate);
        // 上一年的自带了，不需要额外查询
        if (offset != -1) {
            Date beforeYearStart = DateUtil.offset(reqStartDate, DateField.YEAR, offset);
            Date beforeYearEnd = DateUtil.offset(reqEndDate, DateField.YEAR, offset);
            beforeYearEnd = DateUtil.offset(beforeYearEnd, DateField.DAY_OF_YEAR, -1);

            // 重新设置时间范围查询
            reqDTO.setStartDate(beforeYearStart);
            reqDTO.setEndDate(beforeYearEnd);
            reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), beforeYearStart, beforeYearEnd));
            Map<String, AdsMsProcessRespVO> beforeMap = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                    () -> getInfectOverall(reqDTO),
                    () -> getSyndromeOverall(reqDTO)
            ).stream().collect(Collectors.toMap(vo ->{
                if ("year".equals(reqDTO.getDateDimType())){
                    return vo.getStatDate();
                }
                return vo.getStatDate().substring(4);} , Function.identity()));
            respVOS.forEach(respVO -> {
                AdsMsProcessRespVO beforeVO = beforeMap.get(respVO.getStatDate().substring(4));
                if (beforeVO != null) {
                    respVO.setProcessNewCntLastY(beforeVO.getProcessNewCnt());
                } else {
                    respVO.setProcessNewCntLastY(null);
                }
            });
        }
    }

    private List<AdsMsProcessRespVO> getInfectOverall(AdsMsProcessReqDTO reqDTO) {
        return adsMsInfectProcessStatMapper.overAll(reqDTO);
    }

    private List<AdsMsProcessRespVO> getSyndromeOverall(AdsMsProcessReqDTO reqDTO) {
        return adsMsSyndromeProcessMapper.overAll(reqDTO);
    }

    @Override
    public Collection<List<AdsMsProcessRespVO>> areaChange(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {

        //自选的6个code；areaLevel-1
        if (CollectionUtils.isEmpty(reqDTO.getProvinceCodes())
                && CollectionUtils.isEmpty(reqDTO.getCityCodes())
                && CollectionUtils.isEmpty(reqDTO.getDistrictCodes())) {
            setAreaInfoList(reqDTO, loginUserName);
        } else if (!CollectionUtils.isEmpty(reqDTO.getStreetCodes()) || CollectionUtil.isNotEmpty(reqDTO.getStreetCodeList())) {
            //街道按区域进行分组
            reqDTO.setAreaLevel(AreaLevelEnum.DISTRICT.getValue());
        } else {
            reqDTO.setAreaLevel(reqDTO.getAreaLevel() - 1);
        }

        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), reqDTO.getStartDate(), reqDTO.getEndDate()));
        List<AdsMsProcessRespVO> respVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> getInfectAreaChange(reqDTO),
                () -> getSyndromeAreaChange(reqDTO)
        );
        Date startDate = reqDTO.getStartDate();

        //设置预测值
        DateDim.PreDate preDate = DateDim.getPreDate(reqDTO.getDateDimType(), reqDTO.getEndDate(), 3);
        reqDTO.setStartDate(preDate.getStartDate());
        reqDTO.setEndDate(preDate.getEndDate());
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), preDate.getStartDate(), preDate.getEndDate()));
        List<AdsMsProcessRespVO> preVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> getInfectAreaChange(reqDTO),
                () -> getSyndromeAreaChange(reqDTO)
        );

        for (AdsMsProcessRespVO preVO : preVOS) {
            preVO.setProcessNewCnt(preVO.getProcessNewCntPre());
        }
        respVOS.addAll(preVOS);

        Collection<List<AdsMsProcessRespVO>> values = respVOS.stream().collect(Collectors.groupingBy(AdsMsProcessRespVO::getAreaCode)).values();
        List<DateDim> dateDims = dimDateUtils.getDateDims(reqDTO.getDateDimType(), startDate, preDate.getEndDate());
        Collection<List<AdsMsProcessRespVO>> ret = new ArrayList<>();
        for (List<AdsMsProcessRespVO> value : values) {
            String areaCode = value.get(0).getAreaCode();
            String areaName = value.get(0).getAreaName();
            List<AdsMsProcessRespVO> voList = dimDateUtils.fillTimeData(reqDTO.getDateDimType(), AdsMsProcessRespVO.class, dateDims, value);
            voList.forEach(vo -> {
                vo.setAreaName(areaCode);
                vo.setAreaName(areaName);
                vo.setDescription(areaName);
            });
            ret.add(voList);
        }
        return ret;
    }

    private List<AdsMsProcessRespVO> getInfectAreaChange(AdsMsProcessReqDTO reqDTO) {
        return adsMsInfectProcessStatMapper.areaChange(reqDTO);
    }

    private List<AdsMsProcessRespVO> getSyndromeAreaChange(AdsMsProcessReqDTO reqDTO) {
        return adsMsSyndromeProcessMapper.areaChange(reqDTO);
    }

    @Override
    public Collection<List<AdsMsProcessRespVO>> sexChange(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        Date startDate = reqDTO.getStartDate();
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), reqDTO.getStartDate(), reqDTO.getEndDate()));
        List<AdsMsProcessRespVO> respVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> getInfectSexChange(reqDTO),
                () -> getSyndromeSexChange(reqDTO)
        );
        //设置预测值
        DateDim.PreDate preDate = DateDim.getPreDate(reqDTO.getDateDimType(), reqDTO.getEndDate(), 3);
        reqDTO.setStartDate(preDate.getStartDate());
        reqDTO.setEndDate(preDate.getEndDate());
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), preDate.getStartDate(), preDate.getEndDate()));
        List<AdsMsProcessRespVO> preVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> getInfectSexChange(reqDTO),
                () -> getSyndromeSexChange(reqDTO)
        );
        for (AdsMsProcessRespVO preVO : preVOS) {
            preVO.setProcessNewCnt(preVO.getProcessNewCntPre());
        }
        respVOS.addAll(preVOS);
        Collection<List<AdsMsProcessRespVO>> values = respVOS.stream().filter(e -> Objects.nonNull(e.getPatientSexName())).collect(Collectors.groupingBy(AdsMsProcessRespVO::getPatientSexName)).values();
        List<DateDim> dateDims = dimDateUtils.getDateDims(reqDTO.getDateDimType(), startDate, preDate.getEndDate());
        Collection<List<AdsMsProcessRespVO>> ret = new ArrayList<>();
        for (List<AdsMsProcessRespVO> value : values) {
            String patientSexName = value.get(0).getPatientSexName();
            List<AdsMsProcessRespVO> voList = dimDateUtils.fillTimeData(reqDTO.getDateDimType(), AdsMsProcessRespVO.class, dateDims, value);
            voList.forEach(vo -> {
                vo.setPatientSexName(patientSexName);
                vo.setDescription(patientSexName);
            });
            ret.add(voList);
        }
        return ret;
    }

    private List<AdsMsProcessRespVO> getInfectSexChange(AdsMsProcessReqDTO reqDTO) {
        return adsMsInfectProcessStatMapper.sexChange(reqDTO);
    }

    private List<AdsMsProcessRespVO> getSyndromeSexChange(AdsMsProcessReqDTO reqDTO) {
        return adsMsSyndromeProcessMapper.sexChange(reqDTO);
    }

    @Override
    public Collection<List<AdsMsProcessRespVO>> ageChange(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        Date startDate = reqDTO.getStartDate();
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), reqDTO.getStartDate(), reqDTO.getEndDate()));
        List<AdsMsProcessRespVO> respVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> getInfectAgeChange(reqDTO),
                () -> getSyndromeAgeChange(reqDTO)
        );
        //设置预测值
        DateDim.PreDate preDate = DateDim.getPreDate(reqDTO.getDateDimType(), reqDTO.getEndDate(), 3);
        reqDTO.setStartDate(preDate.getStartDate());
        reqDTO.setEndDate(preDate.getEndDate());
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), preDate.getStartDate(), preDate.getEndDate()));
        List<AdsMsProcessRespVO> preVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> getInfectAgeChange(reqDTO),
                () -> getSyndromeAgeChange(reqDTO)
        );
        for (AdsMsProcessRespVO preVO : preVOS) {
            preVO.setProcessNewCnt(preVO.getProcessNewCntPre());
        }
        respVOS.addAll(preVOS);
        Map<String, List<AdsMsProcessRespVO>> map =  respVOS.stream().collect(Collectors.groupingBy(AdsMsProcessRespVO::getPatientAgeGroup));
        LinkedHashMap<String, List<AdsMsProcessRespVO>> listLinkedHashMap = map.entrySet().stream().sorted(Comparator.comparing(entry -> {
            String[] parts = entry.getKey().split("-");
            if (parts.length < 1){
                return Integer.MAX_VALUE;
            }
            try {
                return Integer.parseInt(parts[0]);
            } catch (NumberFormatException e) {
                return Integer.MAX_VALUE;
            }
        })).collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                (oldValue, newValue) -> oldValue,
                LinkedHashMap::new));
        Collection<List<AdsMsProcessRespVO>> values = listLinkedHashMap.values();
        List<DateDim> dateDims = dimDateUtils.getDateDims(reqDTO.getDateDimType(), startDate, preDate.getEndDate());
        Collection<List<AdsMsProcessRespVO>> ret = new ArrayList<>();
        for (List<AdsMsProcessRespVO> value : values) {
            String patientAgeGroup = value.get(0).getPatientAgeGroup();
            List<AdsMsProcessRespVO> voList = dimDateUtils.fillTimeData(reqDTO.getDateDimType(), AdsMsProcessRespVO.class, dateDims, value);
            voList.forEach(vo -> {
                vo.setPatientAgeGroup(patientAgeGroup);
                vo.setDescription(patientAgeGroup);
            });
            ret.add(voList);
        }
        return ret;
    }

    @Override
    public List<AdsMsProcessRespVO> areaMedDateCount(AdsMsProcessReqDTO reqDTO) {
        setAreaInfo(reqDTO, userInfo.get().getLoginName());
        return dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> getInfectAreaChange(reqDTO),
                () -> getSyndromeAreaChange(reqDTO)
        );
    }

    @Override
    public List<AdsMsProcessRespVO> groupIdentifyClass(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setQueryParamDiseaseCode(reqDTO);
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> resp = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> adsMsInfectProcessStatMapper.groupIdentifyClass(reqDTO),
                () -> adsMsSyndromeProcessMapper.groupIdentifyClass(reqDTO)
        );
        setPopulation(reqDTO, resp);
        setRatio(resp);
        return resp;
    }

    @Override
    public List<AdsMsProcessRespVO> groupOutcomeStatus(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setQueryParamDiseaseCode(reqDTO);
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> resp = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> adsMsInfectProcessStatMapper.groupOutcomeStatus(reqDTO),
                () -> adsMsSyndromeProcessMapper.groupOutcomeStatus(reqDTO)
        );
        setPopulation(reqDTO, resp);
        setRatio(resp);
        return resp;
    }

    @Override
    public List<AdsMsProcessRespVO> groupSymptomFirst(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setQueryParamDiseaseCode(reqDTO);
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> resp = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> adsMsInfectProcessStatMapper.groupSymptomFirst(reqDTO),
                () -> adsMsSyndromeProcessMapper.groupSymptomFirst(reqDTO)
        );
        setPopulation(reqDTO, resp);
        setRatio(resp);
        return resp;
    }

    @Override
    public List<AdsMsProcessRespVO> groupHistoryBefore(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setQueryParamDiseaseCode(reqDTO);
        setAreaInfoList(reqDTO, loginUserName);
        //页面只需要processNewCnt
        List<AdsMsProcessRespVO> resp = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> adsMsInfectProcessStatMapper.groupHistoryBefore(reqDTO),
                () -> adsMsSyndromeProcessMapper.groupHistoryBefore(reqDTO)
        );
        setPopulation(reqDTO, resp);
        setRatio(resp);
        return resp;
    }

    @Override
    public EpidemicDistributionRespVO groupPathogenResNominal(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setQueryParamDiseaseCode(reqDTO);
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> details = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> adsMsInfectProcessStatMapper.groupPathogenResNominal(reqDTO),
                () -> adsMsSyndromeProcessMapper.groupPathogenResNominal(reqDTO)
        );
        setPopulation(reqDTO, details);
        setRatio(details);
        // TODO 抽取病原学检测结果的值域，需要和数仓保持一致
        EpidemicDistributionRespVO respVO = new EpidemicDistributionRespVO();
        respVO.setDetails(details);
        respVO.setProcessTotal(details.stream().mapToInt(AdsMsProcessRespVO::getProcessNewCnt).sum());
        respVO.setPathogenTotal(details.stream().filter(vo -> !"无病原学结果".equals(vo.getDescription())).mapToInt(AdsMsProcessRespVO::getProcessNewCnt).sum());
        respVO.setPathogenPositiveCnt(details.stream().filter(vo -> "病原学结果阳性".equals(vo.getDescription())).mapToInt(AdsMsProcessRespVO::getProcessNewCnt).sum());
        respVO.setPathogenPositiveRate(DataUtils.divide(respVO.getPathogenPositiveCnt(), respVO.getPathogenTotal()));
        return respVO;
    }

    @Override
    public List<AdsMsProcessRespVO> groupDiseaseName(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> respVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> adsMsInfectProcessStatMapper.groupDiseaseName(reqDTO),
                () -> buildStat(reqDTO, adsMsSyndromeProcessMapper::groupDiseaseName,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription)
        );
        setPopulation(reqDTO, respVOS);
        setRatio(respVOS);
        StatUtils.replaceDashWithEmpty(respVOS);
        return respVOS;
    }

    @Override
    public ResponseEntity<byte[]> groupDiseaseNameExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.groupDiseaseName(reqDTO, loginUserId, loginUserName);
        if(reqDTO.getIsGuangxi()){
            return DataUtils.exportBytesByWarningType(reqDTO.getWarningType(), vos,
                    AdsMsProcessInfectGXExcelVO.class,
                    AdsMsProcessSyndromeGXExcelVO.class);
        }else {
            return DataUtils.exportBytesByWarningType(reqDTO.getWarningType(), vos,
                    AdsMsProcessInfectNameExcelVO.class,
                    AdsMsProcessSyndromeNameExcelVO.class);
        }
    }

    @Override
    public String overallAwareness(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        // 获取统计数据
        final String reqEndDate = DateFormatUtil.parseDate(reqDTO.getEndDate(), DateFormatUtil.SHORT_DATE_FORMAT);
        List<AdsMsProcessRespVO> respVOS = this.overall(reqDTO, loginUserId, loginUserName);
        if (respVOS.isEmpty()) {
            return null;
        }
        // 填充校准数据，用于生成实时再生数
        Integer startInfected = respVOS.get(0).getProcessNewCnt();
        Integer endInfected = null;
        String startDay = respVOS.get(0).getStatDate();
        String endDay = respVOS.get(respVOS.size() - 1).getStatDate();
        
        List<AwarenessWithCalibrateDTO.CalibrationData> calibrationData = new ArrayList<>(respVOS.size());
        boolean contentEmpty = true;
        for (AdsMsProcessRespVO vo : respVOS) {
            AwarenessWithCalibrateDTO.CalibrationData data = new AwarenessWithCalibrateDTO.CalibrationData();
            data.setDate(vo.getStatDate());
            data.setNewInfections(DataUtils.null2Zero(vo.getProcessNewCnt()));
            data.setNewDeaths(DataUtils.null2Zero(vo.getProcessDeadCnt()));
            calibrationData.add(data);
            
            if (data.getNewInfections() > 0 || data.getNewDeaths() > 0) {
                contentEmpty = false;
            }
            endInfected = vo.getProcessNewCnt();
            if (vo.getStatDate().equals(reqEndDate)) {
                break;
            }
        }
        // 没有内容就不调用算法
        if (contentEmpty) {
            log.debug("统计数据里没有大于0的值，不调用实时再生数相关算法");
            return null;
        }
        // 在 overall 方法里提前处理过区划信息了，这里直接调用
        PopulationDataInfoVO population = cdcAdminServiceApi.statByAreaCodes(reqDTO.getEndDate(), reqDTO.getProvinceCodes(),
                reqDTO.getCityCodes(), reqDTO.getDistrictCodes());
        // 设置尽可能少的算法参数，以后有需要再增加
        AwarenessWithCalibrateDTO req = new AwarenessWithCalibrateDTO();
        req.setStartDay(DateFormatUtil.formatDate(startDay, DateFormatUtil.SHORT_DATE_FORMAT));
        req.setEndDay(DateFormatUtil.formatDate(endDay, DateFormatUtil.SHORT_DATE_FORMAT));
        req.setPopSize(DataUtils.null2Zero(population.getResidentPopulation()));
        req.setCalibrationData(calibrationData);

        // 初始感染数尽量不为 0
        req.setPopInfected(DataUtils.firstNonzero(endInfected, startInfected, 100));
        req.setBeta(0.016d);
        req.setAsympFactor(1d);
        req.setIfCalibrate(true);
        req.setIfContour(false);
        req.setPredDay(endDay);

        return cdcPlatformApi.awareness(req, loginUserId);
    }

    @Override
    public List<ProcessAwarenessRespVO> loadAwarenessResult(AdsMsProcessReqDTO reqDTO, String loginUserId) {
        AwarenessResultVO resp;
        try {
            AwarenessWithCalibrateDTO req = new AwarenessWithCalibrateDTO(reqDTO.getAwarenessTaskId(), reqDTO.getStartDate(), reqDTO.getEndDate());
            resp = cdcPlatformApi.loadAwarenessResult(req, loginUserId);
        } catch (Exception ex) {
            log.error("加载预警结果失败", ex);
            throw new MedicalBusinessException("获取结果错误");
        }
        if (resp == null) {
            return null;
        }
        return resp.getCalibrateResult().stream()
                .map(data -> {
                    ProcessAwarenessRespVO vo = new ProcessAwarenessRespVO();
                    vo.setStatDate(data.getDate());
                    vo.setRtValue(data.getRtValue());
                    vo.setRtCiLowerBound(data.getRtCiLowerBound());
                    vo.setRtCiUpperBound(data.getRtCiUpperBound());
                    return vo;
                })
                .collect(Collectors.toList());
    }

    private List<AdsMsProcessRespVO> getInfectAgeChange(AdsMsProcessReqDTO reqDTO) {
        return adsMsInfectProcessStatMapper.ageChange(reqDTO);
    }

    private List<AdsMsProcessRespVO> getSyndromeAgeChange(AdsMsProcessReqDTO reqDTO) {
        return adsMsSyndromeProcessMapper.ageChange(reqDTO);
    }

    private List<AdsMsProcessRespVO> getSyndromeQOQTrendChart(AdsMsProcessReqDTO reqDTO) {
        return adsMsSyndromeProcessMapper.qOQTrendChart(reqDTO);
    }

    private void setQueryParamDiseaseCode(AdsMsProcessReqDTO queryParam) {
        if (StringUtils.hasText(queryParam.getSyndromeCode())) {
            return;
        }
        List<String> diseaseCodeList = cdcAdminServiceApi.getSyndromeDiseaseCodeByName(queryParam.getDiseaseName());
        queryParam.setSyndromeCode(CollectionUtils.isEmpty(diseaseCodeList) ? null : diseaseCodeList.get(0));
    }

    @Override
    public List<MsInfectCntVO> groupInfectSexCnt(AdsMsProcessReqDTO queryParam, String loginUserName) {

        return buildInfectCntVO(queryParam, loginUserName, adsMsInfectProcessStatMapper::groupInfectSexCnt, adsMsInfectProcessStatMapper::infectCntTotal,false);
    }

    @Override
    public List<MsInfectCntVO> groupInfectAgeCnt(AdsMsProcessReqDTO queryParam, String loginUserName) {

        return buildInfectCntVO(queryParam, loginUserName, adsMsInfectProcessStatMapper::groupInfectAgeCnt, adsMsInfectProcessStatMapper::infectCntTotal,false);
    }

    private List<MsInfectCntVO> buildInfectCntVO(AdsMsProcessReqDTO queryParam,
                                                 String loginUserName,
                                                 Function<AdsMsProcessReqDTO, List<MsInfectCntVO>> msInfectCntFunction,
                                                 Function<AdsMsProcessReqDTO, MsInfectCntVO> msInfectCntVOFunction,
                                                 boolean flag){
        setAreaInfoList(queryParam, loginUserName);
        PopulationDataInfoVO populationDataInfoVO = cdcAdminServiceApi.statByAreaCodes(queryParam.getEndDate(), queryParam.getProvinceCodes(),
                queryParam.getCityCodes(), queryParam.getDistrictCodes());
        List<MsInfectCntVO> msInfectCntVOS = msInfectCntFunction.apply(queryParam);
        List<MsInfectCntVO> res = new ArrayList<>(msInfectCntVOS);
        if (flag){
            res = dealDownBox(res);
        }

        // 根据排序字段对父子节点排序
        MsInfectCntVO.sortTreeBy(res, MsInfectCntVO::getOrderFlag);

        // 添加合计节点
        MsInfectCntVO totalVO = msInfectCntVOFunction.apply(queryParam);
        totalVO.setInfectClass(Common.AMOUNT_TO);
        totalVO.setInfectType(Common.AMOUNT_TO);
        totalVO.setInfectName(Common.AMOUNT_TO);
        res.add(0, totalVO);
        msInfectCntVOS.forEach(m -> {
            m.setResidentPopulation(populationDataInfoVO.getResidentPopulation());
            m.calculateAllRates();
        });
        StatUtils.replaceDashWithEmpty(res);
        return res;
    }


    private List<MsInfectCntVO> dealDownBox(List<MsInfectCntVO> msInfectCntVOS) {
        // 初始化 parentMap，将所有节点存入其中
        Map<String, MsInfectCntVO> parentMap = msInfectCntVOS.stream()
                .peek(vo -> vo.setChildren(new ArrayList<>()))
                .collect(Collectors.toMap(MsInfectCntVO::getInfectCode, Function.identity()));

        List<MsInfectCntVO> toRemove = new ArrayList<>();
        // 构建树形结构
        for (MsInfectCntVO vo : msInfectCntVOS) {
            if (vo.getManageInfectId() != null) {
                MsInfectCntVO parent = parentMap.get(vo.getManageInfectId());
                if (parent != null) {
                    parent.getChildren().add(vo);
                    toRemove.add(vo);
                }
            }
        }

        msInfectCntVOS.removeAll(toRemove);
        // 递归累加子节点的统计数据到父节点
        msInfectCntVOS.forEach(this::accumulateStats);
        return msInfectCntVOS;
    }

    /**
     * 递归累加子节点的统计数据到父节点
     * @param parent 父节点
     * @return 该节点及其所有子节点的统计数据总和
     */
    private MsInfectCntVO accumulateStats(MsInfectCntVO parent) {
        if (CollectionUtils.isEmpty(parent.getChildren())) {
            return parent;
        }

        for (MsInfectCntVO child : parent.getChildren()) {
            MsInfectCntVO vo = accumulateStats(child);

            // 累加基础统计数据到父对象
            parent.setProcessNewCnt(getSum(parent.getProcessNewCnt(), vo.getProcessNewCnt()));
            parent.setProcessDeadCnt(getSum(parent.getProcessDeadCnt(), vo.getProcessDeadCnt()));
            parent.setProcessNowCnt(getSum(parent.getProcessNowCnt(), vo.getProcessNowCnt()));
            parent.setProcessDeadIllnessCnt(getSum(parent.getProcessDeadIllnessCnt(), vo.getProcessDeadIllnessCnt()));
            parent.setProcessCureCnt(getSum(parent.getProcessCureCnt(), vo.getProcessCureCnt()));
            parent.setProcessSevereCnt(getSum(parent.getProcessSevereCnt(), vo.getProcessSevereCnt()));

            // 累加 Last 后缀字段数据到父对象
            parent.setProcessNewCntLast(getSum(parent.getProcessNewCntLast(), vo.getProcessNewCntLast()));
            parent.setProcessDeadCntLast(getSum(parent.getProcessDeadCntLast(), vo.getProcessDeadCntLast()));
            parent.setProcessNowCntLast(getSum(parent.getProcessNowCntLast(), vo.getProcessNowCntLast()));
            parent.setProcessDeadIllnessCntLast(getSum(parent.getProcessDeadIllnessCntLast(), vo.getProcessDeadIllnessCntLast()));
            parent.setProcessCureCntLast(getSum(parent.getProcessCureCntLast(), vo.getProcessCureCntLast()));
            parent.setProcessSevereCntLast(getSum(parent.getProcessSevereCntLast(), vo.getProcessSevereCntLast()));

            // 累加 LastY 后缀字段数据到父对象
            parent.setProcessNewCntLastY(getSum(parent.getProcessNewCntLastY(), vo.getProcessNewCntLastY()));
            parent.setProcessDeadCntLastY(getSum(parent.getProcessDeadCntLastY(), vo.getProcessDeadCntLastY()));
            parent.setProcessNowCntLastY(getSum(parent.getProcessNowCntLastY(), vo.getProcessNowCntLastY()));
            parent.setProcessDeadIllnessCntLastY(getSum(parent.getProcessDeadIllnessCntLastY(), vo.getProcessDeadIllnessCntLastY()));
            parent.setProcessCureCntLastY(getSum(parent.getProcessCureCntLastY(), vo.getProcessCureCntLastY()));
            parent.setProcessSevereCntLastY(getSum(parent.getProcessSevereCntLastY(), vo.getProcessSevereCntLastY()));
        }
        return parent;
    }

    private Integer getSum(Integer a, Integer b) {
        int result = 0;
        if (a != null) {
            result += a;
        }
        if (b != null) {
            result += b;
        }
        return result;
    }

    @Override
    public List<AdsMsProcessRespVO> getDiseaseDistributionBox(String loginUserName) {
        AdsMsProcessReqDTO reqDTO = new AdsMsProcessReqDTO();
        setAreaInfo(reqDTO, loginUserName);
        Date endDate = new Date();
        DateTime startDate = DateUtil.offset(endDate, DateField.YEAR, -1);
        reqDTO.setStartDate(startDate);
        reqDTO.setEndDate(endDate);
        return adsMsSyndromeProcessMapper.groupDiseaseCodeAndName(reqDTO);
    }

    @Override
    public List<AdsMsProcessRespVO> qOQTrendChart(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        List<DateDim> dateDims = dimDateUtils.getDateDims(reqDTO.getDateDimType(), reqDTO.getStartDate(), reqDTO.getEndDate());
        reqDTO.setDateDims(dateDims);
        //查询趋势图
        List<AdsMsProcessRespVO> syndromeQOQTrendChart = getSyndromeQOQTrendChart(reqDTO);
        // 填充数据
        List<AdsMsProcessRespVO>  respVOS = dimDateUtils.fillTimeData(reqDTO.getDateDimType(), AdsMsProcessRespVO.class, dateDims, syndromeQOQTrendChart);
        if (respVOS.isEmpty()) {
            return new ArrayList<>();
        }
        for (int i = 0; i < respVOS.size(); i++) {
            if (i == 0) {
                respVOS.get(i).calQOQRates(respVOS.get(i).getProcessNewCnt(), 0);
            } else {
                respVOS.get(i).calQOQRates(respVOS.get(i).getProcessNewCnt(), respVOS.get(i - 1).getProcessNewCnt());
            }
        }
        return respVOS;
    }

    @Override
    public PageInfo<SyndromeOrgProcessStatsVO> listSyndromeOrgProcessStats(AdsMsProcessReqDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        setAreaInfo(queryDTO, userInfo.get().getLoginName());
        return new PageInfo<>(adsMulProcessCaseInfoMapper.listSyndromeOrgProcessStats(queryDTO));
    }


    /**
     * 传染病三间分布统计数据预测未来3天数据
     */
    private List<AdsMsProcessRespVO> predictAdsMsProcessData(AdsMsProcessReqDTO reqDTO, List<AdsMsProcessRespVO> adsMsProcessRespVOList) {

        List<Double> processNewCntInputList = adsMsProcessRespVOList.stream()
                .map(item -> item.getProcessNewCnt() == null ? 0 : new Double(item.getProcessNewCnt().toString()))
                .collect(Collectors.toList());
        List<Double> processNewCntPredictList = predictionService.predict(processNewCntInputList, 3);

        List<Double> processNewCntAvgSInput = adsMsProcessRespVOList.stream()
                .map(item -> item.getProcessNewCntAvgS() == null ? 0 : new Double(item.getProcessNewCntAvgS().toString()))
                .collect(Collectors.toList());
        List<Double> processNewCntAvgSPredictList = predictionService.predict(processNewCntAvgSInput, 3);

        List<Double> processNewCntLastYInputList = adsMsProcessRespVOList.stream()
                .map(item -> item.getProcessNewCntLastY() == null ? 0 : new Double(item.getProcessNewCntLastY().toString()))
                .collect(Collectors.toList());
        List<Double> processNewCntLastYPredictList = predictionService.predict(processNewCntLastYInputList, 3);

        List<AdsMsProcessRespVO> predictDataList = new ArrayList<>();       //
        Date preStartDate = DateUtils.addDays(reqDTO.getEndDate(), 1);
        Date preEndDate = DateUtils.addDays(preStartDate, 2);
        List<DateDim> dateDims = dimDateUtils.getDateDims(DateDimEnum.DAY.getCode(), preStartDate, preEndDate);

        for (int i = 0; i < dateDims.size(); i++) {
            DateDim dateDim = dateDims.get(i);
            AdsMsProcessRespVO predictAdsMsProcessRespVO = new AdsMsProcessRespVO();
            if (processNewCntPredictList.size()>i)  predictAdsMsProcessRespVO.setProcessNewCnt(processNewCntPredictList.get(i).intValue());
            if (processNewCntAvgSPredictList.size()>i) predictAdsMsProcessRespVO.setProcessNewCntAvgS(processNewCntAvgSPredictList.get(i).intValue());
            if (processNewCntLastYPredictList.size()>i) predictAdsMsProcessRespVO.setProcessNewCntLastY(processNewCntLastYPredictList.get(i).intValue());
            predictAdsMsProcessRespVO.setStatDate(dateDim.getDate());
            predictAdsMsProcessRespVO.setHalfYear(dateDim.getHalfYear());
            predictDataList.add(predictAdsMsProcessRespVO);
        }

        return predictDataList;
    }

    /**
     * 按病种统计发病数, 发病率, 病死率
     */
    @Override
    public List<MsInfectCntVO> groupInfectDiseaseCnt(AdsMsProcessReqDTO queryParam, String loginUserName) {
        setAreaInfoList(queryParam, loginUserName);
        List<DateDim> dateDims = dimDateUtils.getDateDims(queryParam.getDateDimType(), queryParam.getStartDate(), queryParam.getEndDate());
        queryParam.setDateDims(dateDims);
        List<MsInfectCntVO> msInfectCntVOS = adsMsInfectProcessStatMapper.groupInfectDiseaseCnt(queryParam);
        if (!CollectionUtils.isEmpty(queryParam.getProvinceCodeList())) {
            queryParam.setProvinceCode(queryParam.getProvinceCodeList().get(0));
        }
        if (!CollectionUtils.isEmpty(queryParam.getProvinceCodes())) {
            queryParam.setProvinceCode(queryParam.getProvinceCodes().get(0));
        }
        ManualBriefRangeDTO range = new ManualBriefRangeDTO();
        range.setStartDate(queryParam.getStartDate());
        range.setEndDate(queryParam.getEndDate());
        range.setProvinceCode(queryParam.getProvinceCode());
        range.setCityCode(queryParam.getCityCode());
        range.setDistrictCode(queryParam.getDistrictCode());
        if (range.isBrokenRange()) {
            throw new MedicalBusinessException("数据范围选择不完整！");
        }
        int totalPopulation = manualBriefRecordService.getTotalPopulation(range);
        String statFlag = queryParam.getStatFlag();
        msInfectCntVOS = msInfectCntVOS.stream().peek(vo -> {
            vo.setResidentPopulation(totalPopulation);
            vo.calculateAllRates();
        }).sorted((n, o) -> {
            if ("newCnt".equals(statFlag) || "newRate".equals(statFlag)) {
                return o.getProcessNewCnt() - n.getProcessNewCnt();
            } else {
                return (o.getProcessDeadIllnessCnt() / totalPopulation) - (n.getProcessDeadIllnessCnt() / totalPopulation);
            }
        }).collect(Collectors.toList());
        return msInfectCntVOS;
    }


    @Override
    public List<AdsMsProcessRespVO> groupInfectAreaCnt(AdsMsProcessReqDTO queryParam, String loginUserName) {
        setAreaInfoList(queryParam, loginUserName);
        List<AdsMsProcessRespVO> voList = adsMsInfectProcessStatMapper.groupInfectAreaCnt(queryParam);
        if (!queryParam.getProvinceCodes().isEmpty()) {
            queryParam.setProvinceCode(queryParam.getProvinceCodes().get(0));
        }
        if (!queryParam.getCityCodes().isEmpty()) {
            queryParam.setCityCode(queryParam.getCityCodes().get(0));
        }
        if (!queryParam.getDistrictCodes().isEmpty()) {
            queryParam.setDiseaseCode(queryParam.getDistrictCodes().get(0));
        }
        ManualBriefRangeDTO range = new ManualBriefRangeDTO();
        range.setStartDate(queryParam.getStartDate());
        range.setEndDate(queryParam.getEndDate());
        range.setProvinceCode(queryParam.getProvinceCode());
        range.setCityCode(queryParam.getCityCode());
        range.setDistrictCode(queryParam.getDistrictCode());
        if (range.isBrokenRange()) {
            throw new MedicalBusinessException("数据范围选择不完整！");
        }
        int totalPopulation = manualBriefRecordService.getTotalPopulation(range);
        return voList.stream().peek(vo -> {
            vo.setPopulation(totalPopulation);
            vo.calculateAllRates(totalPopulation);
            vo.setRateStr(DataUtils.hundredThousandthSecond(vo.getProcessNewCnt(), vo.getPopulation()));
        }).collect(Collectors.toList());
    }

    @Override
    public List<AdsMsProcessRespVO> groupInfectAreaOrgCnt(AdsMsProcessReqDTO queryParam, String loginUserName) {
        setAreaInfoList(queryParam, loginUserName);
        List<AdsMsProcessRespVO> voList = adsMsInfectProcessStatMapper.groupInfectAreaOrgCnt(queryParam);
        if (!queryParam.getProvinceCodes().isEmpty()) {
            queryParam.setProvinceCode(queryParam.getProvinceCodes().get(0));
        }
        if (!queryParam.getCityCodes().isEmpty()) {
            queryParam.setCityCode(queryParam.getCityCodes().get(0));
        }
        if (!queryParam.getDistrictCodes().isEmpty()) {
            queryParam.setDiseaseCode(queryParam.getDistrictCodes().get(0));
        }
        ManualBriefRangeDTO range = new ManualBriefRangeDTO();
        range.setStartDate(queryParam.getStartDate());
        range.setEndDate(queryParam.getEndDate());
        range.setProvinceCode(queryParam.getProvinceCode());
        range.setCityCode(queryParam.getCityCode());
        range.setDistrictCode(queryParam.getDistrictCode());
        if (range.isBrokenRange()) {
            throw new MedicalBusinessException("数据范围选择不完整！");
        }
        int totalPopulation = manualBriefRecordService.getTotalPopulation(range);
        return voList.stream().peek(vo -> {
            vo.setPopulation(totalPopulation);
            vo.calculateAllRates(totalPopulation);
        }).collect(Collectors.toList());
    }

    /**
     * 时间研判模型 追加两周预测数据
     */
    @Override
    public List<AdsMsProcessRespVO> timeJudgeModelTrend(AdsMsProcessReqDTO reqDTO, String loginUserName) {

        setAreaInfoList(reqDTO, loginUserName);
        final Date reqStartDate = reqDTO.getStartDate();
        final Date reqEndDate = reqDTO.getEndDate();
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), reqStartDate, reqEndDate));
        List<AdsMsProcessRespVO> respVOS = dataUtils.getDateDimDataByWarningType(reqDTO.getWarningType(),
                () -> getInfectOverall(reqDTO),
                () -> getSyndromeOverall(reqDTO)
        );

        List<DateDim> dateDims = dimDateUtils.getDateDims(reqDTO.getDateDimType(), reqStartDate, reqEndDate);
        respVOS = dimDateUtils.fillTimeData(reqDTO.getDateDimType(), AdsMsProcessRespVO.class, dateDims, respVOS);

        // 设置指定年份的同期值（在原统计时间范围内补充）
        if (reqDTO.getBeforeYear() != null && reqStartDate != null && reqEndDate != null && CollUtil.isNotEmpty(respVOS)) {
            this.coverBeforeYearNewCnt(reqDTO, reqStartDate, reqEndDate, respVOS);
        }

        List<Double> processNewCntInputList = respVOS.stream()
                .map(item -> item.getProcessNewCnt() == null ? 0 : new Double(item.getProcessNewCnt().toString()))
                .collect(Collectors.toList());
        List<Double> processNewCntPredictList = predictionService.predict(processNewCntInputList, 14);

        List<AdsMsProcessRespVO> predictDataList = new ArrayList<>();       //
        Date preStartDate = DateUtils.addDays(reqDTO.getEndDate(), 1);
        Date preEndDate = DateUtils.addDays(preStartDate, 13);
        List<DateDim> dateDimsPre = dimDateUtils.getDateDims(DateDimEnum.DAY.getCode(), preStartDate, preEndDate);

        for (int i = 0; i < dateDimsPre.size(); i++) {
            DateDim dateDim = dateDimsPre.get(i);
            AdsMsProcessRespVO predictAdsMsProcessRespVO = new AdsMsProcessRespVO();
            predictAdsMsProcessRespVO.setProcessNewCnt((int)Math.floor(processNewCntPredictList.get(i)));
            predictAdsMsProcessRespVO.setStatDate(dateDim.getDate());
            predictAdsMsProcessRespVO.setHalfYear(dateDim.getHalfYear());
            predictDataList.add(predictAdsMsProcessRespVO);
        }
        respVOS.addAll(predictDataList);
        return respVOS;
    }

    @Override
    public byte[] groupInfectAreaCntExport(AdsMsProcessReqDTO queryParam) {
        String loginName = userInfo.get().getLoginName();
        List<AdsMsProcessRespVO> voList = groupInfectAreaCnt(queryParam, loginName);
        String statFlag = queryParam.getStatFlag();
        List<InfectAreaCntExcelVO> exportDataList = voList.stream().map(vo -> {
            InfectAreaCntExcelVO.InfectAreaCntExcelVOBuilder builder = InfectAreaCntExcelVO
                    .builder().description(vo.getDescription());
            InfectAreaCntExcelVO excelVO = null;
            if ("newCnt".equals(statFlag)) {
                excelVO = builder.value(vo.getProcessNewCnt() == null ? "" : vo.getProcessNewCnt().toString())
                        .build();
            } else if ("newRate".equals(statFlag)) {
                excelVO = builder.value(vo.getRateStr())
                        .build();
            } else {
                excelVO = builder.value(vo.getProcessDeadIllnessRate())
                        .build();
            }
            return excelVO;
        }).collect(Collectors.toList());
        String valueTitle = "";
        if ("newCnt".equals(statFlag)) {
            valueTitle = "发病数";
        } else if ("newRate".equals(statFlag)) {
            valueTitle = "发病率";
        } else {
            valueTitle = "病死率";
        }
        List<List<String>> headList = new ArrayList<>();
        headList.add(Collections.singletonList("地区"));
        headList.add(Collections.singletonList(valueTitle));
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        EasyExcel.write(outputStream, InfectAreaCntExcelVO.class)
                .head(headList)
                .sheet("按地区医疗机构统计数据")
                .doWrite(exportDataList);
        return outputStream.toByteArray();
    }


    @Override
    public byte[] groupInfectAreaOrgCntExport(AdsMsProcessReqDTO queryParam) {
        String loginName = userInfo.get().getLoginName();
        List<AdsMsProcessRespVO> voList = groupInfectAreaOrgCnt(queryParam, loginName);
        String statFlag = queryParam.getStatFlag();
        List<InfectAreaCntExcelVO> exportDataList = voList.stream().map(vo -> {
            InfectAreaCntExcelVO.InfectAreaCntExcelVOBuilder builder = InfectAreaCntExcelVO
                    .builder().description(vo.getDescription());
            InfectAreaCntExcelVO excelVO = null;
            if ("newCnt".equals(statFlag)) {
                excelVO = builder.value(vo.getProcessNewCnt() == null ? "" : vo.getProcessNewCnt().toString())
                        .build();
            } else if ("newRate".equals(statFlag)) {
                excelVO = builder.value(vo.getRateStr())
                        .build();
            } else {
                excelVO = builder.value(vo.getProcessDeadIllnessRate())
                        .build();
            }
            return excelVO;
        }).collect(Collectors.toList());
        String valueTitle = "";
        if ("newCnt".equals(statFlag)) {
            valueTitle = "发病数";
        } else if ("newRate".equals(statFlag)) {
            valueTitle = "发病率";
        } else {
            valueTitle = "病死率";
        }
        List<List<String>> headList = new ArrayList<>();
        headList.add(Collections.singletonList("医疗机构名称"));
        headList.add(Collections.singletonList(valueTitle));
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        EasyExcel.write(outputStream, InfectAreaCntExcelVO.class)
                .head(headList)
                .sheet("按地区医疗机构统计数据")
                .doWrite(exportDataList);
        return outputStream.toByteArray();
    }


    @Override
    public List<AdsMsProcessRespVO> highPrevalenceAreaTop3(AdsMsProcessReqDTO reqDTO, String loginUserName) {

        setQueryParamDiseaseCode(reqDTO);
        reqDTO.setAreaLevel(3);     // 设置为区县级别
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> respVOS = new ArrayList<>();
        List<AdsMsProcessRespVO> currentRespVOS = adsMsInfectProcessStatMapper.groupAreaCurrentLevel(reqDTO);
        currentRespVOS = currentRespVOS.stream().sorted(Comparator.comparingInt(AdsMsProcessRespVO::getProcessNewCnt)).collect(Collectors.toList());
        if (currentRespVOS.isEmpty()) return currentRespVOS;
        if ("newCnt".equals(reqDTO.getStatFlag())) {
            int end = Math.min(currentRespVOS.size(), 3);
            respVOS = CollectionUtil.sub(currentRespVOS, 0, end);
            return respVOS;
        }
        List<String> districtCodes = currentRespVOS.stream().filter(v -> AreaLevelEnum.DISTRICT.getValue().equals(v.getAreaLevel()))
                        .map(AdsMsProcessRespVO::getAreaCode).collect(Collectors.toList());

        final Map<String, PopulationDataInfoVO> areaPopulations = cdcAdminServiceApi.groupByAreaCodesIncludeStreet(reqDTO.getEndDate(), Lists.newArrayList(), Lists.newArrayList(), districtCodes, Lists.newArrayList());
        for (AdsMsProcessRespVO respVO : currentRespVOS) {
            PopulationDataInfoVO populationDataInfoVO = areaPopulations.get(respVO.getAreaCode());
            if (populationDataInfoVO != null) {
                respVO.setPopulation(populationDataInfoVO.getResidentPopulation());
            }
        }
        setRatio(currentRespVOS);
        currentRespVOS = currentRespVOS.stream().sorted(Comparator.comparingDouble(AdsMsProcessRespVO::calcCurrentRateValue).reversed()).collect(Collectors.toList());
        respVOS = CollectionUtil.sub(currentRespVOS, 0, Math.min(currentRespVOS.size(), 3));
        return respVOS;
    }


    @Override
    public List<AdsMsProcessRespVO> groupDeath(AdsMsProcessReqDTO queryParam, String loginUserName) {
        setAreaInfoList(queryParam, loginUserName);
        if (queryParam.getStartDate() != null && queryParam.getEndDate() != null) {
            queryParam.setStartDate(DateFormatUtil.getTheFistSecondOfOneDay(queryParam.getStartDate()));
            queryParam.setEndDate(DateFormatUtil.getTheLastSecondOfOneDay(queryParam.getEndDate()));
        }
        DateDimEnum dateDimEnum = DateDimEnum.getByCode(queryParam.getDateDimType());
        if(dateDimEnum == null){
            dateDimEnum = DateDimEnum.DAY;
        }

        List<AdsMsProcessRespVO> voList = StatUtils.fulfillAndConvertDayDateData(
                StatUtils.fulfillPeriodValueByDay(queryParam, q -> adsMsInfectProcessStatMapper.groupDeath(q),
                        AdsMsProcessRespVO::getStatDate, AdsMsProcessRespVO.currValueAndLastValueMappingList()),
                queryParam.getStartDate(),
                queryParam.getEndDate(),
                dateDimEnum,
                AdsMsProcessRespVO::getStatDate,
                AdsMsProcessRespVO::setStatDate,
                AdsMsProcessRespVO.dataConverter
        );
        // 计算比率
        ManualBriefRangeDTO range = new ManualBriefRangeDTO();
        range.setStartDate(queryParam.getStartDate());
        range.setEndDate(queryParam.getEndDate());
        range.setProvinceCode(queryParam.getProvinceCode());
        range.setCityCode(queryParam.getCityCode());
        range.setDistrictCode(queryParam.getDistrictCode());
        if (range.isBrokenRange()) {
            throw new MedicalBusinessException("数据范围选择不完整！");
        }
        int totalPopulation = manualBriefRecordService.getTotalPopulation(range);

        return voList.stream().peek(vo -> {
            vo.setPopulation(totalPopulation);
            vo.calculateAllRates(totalPopulation);
        }).collect(Collectors.toList());
    }

}
