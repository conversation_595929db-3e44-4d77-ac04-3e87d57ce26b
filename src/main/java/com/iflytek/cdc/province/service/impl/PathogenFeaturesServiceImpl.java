package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.entity.TbCdcmrPathogenInfo;
import com.iflytek.cdc.edr.utils.DimDateUtils;
import com.iflytek.cdc.edr.utils.MathUtil;
import com.iflytek.cdc.province.constant.PathogenDataConstant;
import com.iflytek.cdc.province.enums.PathogenProcessTypeEnum;
import com.iflytek.cdc.province.mapper.pg.*;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.VirusCheckQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.*;
import com.iflytek.cdc.province.model.pathogen.PathogenAnalysisReqDTO;
import com.iflytek.cdc.province.model.pathogen.PathogenNameGroupVO;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.service.IntegratedUtilsService;
import com.iflytek.cdc.province.service.MultichannelUtilsService;
import com.iflytek.cdc.province.service.PathogenFeaturesService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
    public class PathogenFeaturesServiceImpl implements PathogenFeaturesService {

    @Resource
    private CommonUtilService commonUtilService;
    
    @Resource
    private MultichannelUtilsService multichannelUtilsService;

    @Resource
    private DimDateUtils dimDateUtils;

    @Resource
    private AdsPdPathogenStatMapper adsPdPathogenStatMapper;

    @Resource
    private AdsPdVirusGeneStatMapper adsPdVirusGeneStatMapper;

    @Resource
    private AdsPdVirusGeneInfoMapper adsPdVirusGeneInfoMapper;

    @Resource
    private AdsPdBacteriaAstStatMapper adsPdBacteriaAstStatMapper;

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Resource
    private IntegratedUtilsService integratedUtilsService;
    
    @Resource
    private AdsMulProcessInfoMapper adsMulProcessInfoMapper;

    @Resource
    private CdcAdminServiceApi cdcAdminServiceApi;

    @Resource
    private AdsIntegratedProcessInfoMapper adsIntegratedProcessInfoMapper;

    /**
     * 统一设置病原的查询条件
     * */
    private void setPathogenQueryDTO(VirusCheckQueryDTO queryDTO, TopicConfigInfoVO configInfoVO){

        //设置查询区域
        CommonUtilService.setAreaQueryDTO(queryDTO);

        //根据传入的病原id集合 查询对应的病原以及所有子类id集合
        multichannelUtilsService.setTopicPathogenQuery(queryDTO,
                                                       configInfoVO,
                                                       VirusCheckQueryDTO::getPathogenIdList,
                                                       VirusCheckQueryDTO::setAllPathogenNameList);
        //设置时间条件
        queryDTO.setDateDims(dimDateUtils.getDateDims(queryDTO.getDateDimType(), queryDTO.getStartDate(), queryDTO.getEndDate()));
        //设置病原种类 （病毒、细菌、真菌、其他）
        if(CollectionUtils.isNotEmpty(queryDTO.getPathogenClassCode())){
            List<String> classList = new ArrayList<>();
            for (String s : queryDTO.getPathogenClassCode()) {
                classList.add(PathogenDataConstant.PathogenClassEnum.getDescByCode(s));
            }
            queryDTO.setPathogenClassCode(classList);
        }
        if (StrUtil.isBlank(queryDTO.getProcessType())) {
            queryDTO.setProcessType(PathogenProcessTypeEnum.INFECT.getCode());
        }
    }

    @Override
    public PageInfo<VirusCheckResultVO> virusCheckSituation(VirusCheckQueryDTO queryDTO) {
        // 获取多渠道专题信息
        TopicConfigInfoVO configInfoVO = adminServiceApi.getTopicConfig(queryDTO.getTopicId());
        if (configInfoVO == null) {
            return new PageInfo<>(new ArrayList<>());
        }
        setPathogenQueryDTO(queryDTO, configInfoVO);

        try (Page<?> ignored = PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize())) {
            queryDTO.dealDate(queryDTO);
            List<VirusCheckResultVO> resultList;
            // 病毒区分分型，细菌不区分分型
            if (PathogenDataConstant.PathogenClassEnum.VIRUS.anyEquals(queryDTO.getPathogenClassCode())) {
                resultList = adsPdPathogenStatMapper.getVirusCheckStat(PathogenAnalysisReqDTO.of(queryDTO));
            } else {
                resultList = adsPdPathogenStatMapper.groupPositiveByName(PathogenAnalysisReqDTO.of(queryDTO))
                    .stream().map(VirusCheckResultVO::of).collect(Collectors.toList());
            }
            return new PageInfo<>(resultList);
        }
    }

    @Override
    public List<VirusCheckResultVO> getVirusCheckSituation(VirusCheckQueryDTO queryDTO) {
        setIntegratedPathogenQueryDTO(queryDTO);
        List<VirusCheckResultVO> resultList;
        queryDTO.dealDate(queryDTO);
        // 病毒区分分型，细菌不区分分型
        if (PathogenDataConstant.PathogenClassEnum.VIRUS.anyEquals(queryDTO.getPathogenClassCode())) {
            resultList = adsPdPathogenStatMapper.getVirusCheckStat(PathogenAnalysisReqDTO.of(queryDTO));
        } else {
            resultList = adsPdPathogenStatMapper.groupPositiveByName(PathogenAnalysisReqDTO.of(queryDTO))
                    .stream().map(VirusCheckResultVO::of).collect(Collectors.toList());
        }
        return resultList;
    }

    private void setIntegratedPathogenQueryDTO(VirusCheckQueryDTO queryDTO) {
        //设置查询区域
        CommonUtilService.setAreaQueryDTO(queryDTO);

        //根据传入的病原id集合 查询对应的病原以及所有子类id集合
        integratedUtilsService.setIntegratedPathogenQuery(queryDTO,
                VirusCheckQueryDTO::getPathogenIdList,
                VirusCheckQueryDTO::setAllPathogenNameList);
        //设置时间条件
        queryDTO.setDateDims(dimDateUtils.getDateDims(queryDTO.getDateDimType(), queryDTO.getStartDate(), queryDTO.getEndDate()));

        //设置病原种类 （病毒、细菌、真菌、其他）
        if(CollectionUtils.isNotEmpty(queryDTO.getPathogenClassCode())){
            List<String> classList = new ArrayList<>();
            for (String s : queryDTO.getPathogenClassCode()) {
                classList.add(PathogenDataConstant.PathogenClassEnum.getDescByCode(s));
            }
            queryDTO.setPathogenClassCode(classList);
        }
        if (StrUtil.isBlank(queryDTO.getProcessType())) {
            queryDTO.setProcessType(PathogenProcessTypeEnum.INFECT.getCode());
        }
    }

    @Override
    public List<VirusTypeDetectedRateVO> getTopDetectedRateBy(VirusCheckQueryDTO queryDTO) {

        //获取多渠道专题信息
        TopicConfigInfoVO configInfoVO = adminServiceApi.getTopicConfig(queryDTO.getTopicId());
        if(configInfoVO == null){
            return new ArrayList<>();
        }
        setPathogenQueryDTO(queryDTO, configInfoVO);

        List<VirusCheckResultVO> resultVOList = adsPdPathogenStatMapper.getVirusCheckStat(PathogenAnalysisReqDTO.of(queryDTO));
        
        List<VirusTypeDetectedRateVO> result = multichannelUtilsService.buildDetectResult(resultVOList, VirusCheckResultVO::getType);
        return result.stream().sorted(Comparator.comparing(VirusTypeDetectedRateVO::getCheckRate).reversed()).limit(10).collect(Collectors.toList());
    }

    @Override
    public List<VirusTypeDetectedRateVO> getTopPositiveRateBy(VirusCheckQueryDTO queryDTO) {

        //获取多渠道专题信息
        TopicConfigInfoVO configInfoVO = adminServiceApi.getTopicConfig(queryDTO.getTopicId());
        if(configInfoVO == null){
            return new ArrayList<>();
        }
        setPathogenQueryDTO(queryDTO, configInfoVO);

        List<VirusCheckResultVO> resultVOList = adsPdPathogenStatMapper.getVirusCheckStat(PathogenAnalysisReqDTO.of(queryDTO));

        List<VirusTypeDetectedRateVO> result = multichannelUtilsService.buildDetectResult(resultVOList, VirusCheckResultVO::getType);
        return result.stream().sorted(Comparator.comparing(VirusTypeDetectedRateVO::getPositiveRate).reversed()).limit(10).collect(Collectors.toList());
    }

    @Override
    public List<VirusTypeDetectedRateVO> getInfluenzaVirusAnalysis(VirusCheckQueryDTO queryDTO) {

        //获取多渠道专题信息
        TopicConfigInfoVO configInfoVO = adminServiceApi.getTopicConfig(queryDTO.getTopicId());
        if (configInfoVO == null) {
            return new ArrayList<>();
        }
        setPathogenQueryDTO(queryDTO, configInfoVO);

        List<VirusCheckResultVO> resultVOList = adsPdPathogenStatMapper.getVirusCheckStat(PathogenAnalysisReqDTO.of(queryDTO));

        return multichannelUtilsService.buildDetectResult(resultVOList, VirusCheckResultVO::getType);
    }

    @Override
    public PageInfo<VirusAntigenResultVO> getAntigenAnalysisResult(VirusCheckQueryDTO queryDTO) {

        //获取多渠道专题信息
        TopicConfigInfoVO configInfoVO = adminServiceApi.getTopicConfig(queryDTO.getTopicId());
        if (configInfoVO == null) {
            return new PageInfo<>(new ArrayList<>());
        }
        setPathogenQueryDTO(queryDTO, configInfoVO);

        try (Page<?> ignored = PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize())) {
            List<VirusAntigenResultVO> resultVOList = adsPdVirusGeneStatMapper.getAntigenAnalysisResult(queryDTO);
            resultVOList.forEach(e -> {
                e.setSimilarStrainRate(MathUtil.div(e.getSimilarStrainCount(), e.getTotalCount(), 4));
                e.setLowResponseStrainRate(MathUtil.div(e.getLowResponseStrainCount(), e.getTotalCount(), 4));
            });
            return new PageInfo<>(resultVOList);
        }
    }

    @Override
    public PageInfo<VirusResistanceResultVO> getResistanceAnalysisResult(VirusCheckQueryDTO queryDTO) {
        // 获取多渠道专题信息
        TopicConfigInfoVO configInfoVO = adminServiceApi.getTopicConfig(queryDTO.getTopicId());
        if (configInfoVO == null) {
            return PageInfo.emptyPageInfo();
        }
        setPathogenQueryDTO(queryDTO, configInfoVO);

        // 重要：新表缺少基于表型的耐药检测结果（IC50）相关内容，后续等数仓完成设计再添加对应实现
        try (Page<?> ignored = PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize())) {
            List<VirusResistanceResultVO> resultVOList = adsPdVirusGeneInfoMapper.getResistanceAnalysisResult(queryDTO);
            return new PageInfo<>(resultVOList);
        }
    }

    @Override
    public PageInfo<DrugResistantBacteriaVO> getCommonDrugResistantBacteria(VirusCheckQueryDTO queryDTO) {
        // 获取多渠道专题信息
        TopicConfigInfoVO configInfoVO = adminServiceApi.getTopicConfig(queryDTO.getTopicId());
        if(configInfoVO == null){
            return new PageInfo<>(new ArrayList<>());
        }
        setPathogenQueryDTO(queryDTO, configInfoVO);

        try (Page<?> ignored = PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize())) {
            queryDTO.dealDate(queryDTO);
            List<PathogenNameGroupVO> pathogenVOList = adsPdPathogenStatMapper.groupPositiveByName(PathogenAnalysisReqDTO.of(queryDTO));
            return PageInfo.of(pathogenVOList).convert(DrugResistantBacteriaVO::of);
        }
    }

    @Override
    public Map<String, List<DrugResistantHospitalVO>> getMDRCheckRateByGradeHospital(VirusCheckQueryDTO queryDTO) {

        //获取多渠道专题信息
        TopicConfigInfoVO configInfoVO = adminServiceApi.getTopicConfig(queryDTO.getTopicId());
        if(configInfoVO == null){
            return new HashMap<>();
        }
        setPathogenQueryDTO(queryDTO, configInfoVO);

        List<DrugResistantHospitalVO> resultVOList = adsPdPathogenStatMapper.getMDRCheckRateByGradeHospital(PathogenAnalysisReqDTO.of(queryDTO));
        if (CollectionUtil.isEmpty(resultVOList)){
            return new HashMap<>();
        }
        List<String> hospitalList = Arrays.stream(PathogenDataConstant.GradeHospitalTypeEnum.values()).map(PathogenDataConstant.GradeHospitalTypeEnum::getDesc).collect(Collectors.toList());
        resultVOList.forEach(e -> e.setRatio(MathUtil.div(e.getPositiveCount(), e.getSampleCount(), 4)));
        Map<String, List<DrugResistantHospitalVO>> resultMap = resultVOList.stream().collect(Collectors.groupingBy(DrugResistantHospitalVO::getPathogenName));

        resultMap.replaceAll((k, v) -> {

            Map<String, DrugResistantHospitalVO> valueMap = v.stream()
                                                             .filter(e -> StringUtils.isNotBlank(e.getOrgType()))
                                                             .collect(Collectors.toMap(DrugResistantHospitalVO::getOrgType, Function.identity(), (a, b) -> a));
            List<DrugResistantHospitalVO> values = new ArrayList<>();
            hospitalList.forEach(e -> {
                DrugResistantHospitalVO vo = new DrugResistantHospitalVO();
                if(valueMap.containsKey(e)){
                    BeanUtils.copyProperties(valueMap.get(e), vo);
                }
                vo.setOrgType(e);
                vo.setPathogenName(k);
                values.add(vo);
            });
            return values;
        });
        return resultMap;
    }

    @Override
    public PageInfo<MDRCheckAreaDistributionVO> getMDRCheckByDistrict(VirusCheckQueryDTO queryDTO) {

        //获取多渠道专题信息
        TopicConfigInfoVO configInfoVO = adminServiceApi.getTopicConfig(queryDTO.getTopicId());
        if(configInfoVO == null){
            return new PageInfo<>(new ArrayList<>());
        }
        setPathogenQueryDTO(queryDTO, configInfoVO);

        try (Page<?> ignored = PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize())) {
            List<MDRCheckAreaDistributionVO> resultVOList = adsPdPathogenStatMapper.getMDRCheckByDistrict(PathogenAnalysisReqDTO.of(queryDTO));
            resultVOList.forEach(e -> e.setRate(MathUtil.div(e.getPositiveCount(), e.getSampleCount(), 4)));
            return new PageInfo<>(resultVOList);
        }
    }

    @Override
    public List<DrugSituationVO> getDrugSensitivityBy(VirusCheckQueryDTO queryDTO) {

        //获取多渠道专题信息
        TopicConfigInfoVO configInfoVO = adminServiceApi.getTopicConfig(queryDTO.getTopicId());
        if(configInfoVO == null){
            return new ArrayList<>();
        }
        setPathogenQueryDTO(queryDTO, configInfoVO);

        List<DrugSituationVO> resultVOList = adsPdBacteriaAstStatMapper.getDrugSensitivityBy(queryDTO);
        resultVOList = commonUtilService.fulfillRatio(resultVOList, DrugSituationVO::getDrugResistance, DrugSituationVO::setDrugResistanceRatio);
        resultVOList = commonUtilService.fulfillRatio(resultVOList, DrugSituationVO::getMediumSensitivity, DrugSituationVO::setMediumSensitivityRatio);
        resultVOList = commonUtilService.fulfillRatio(resultVOList, DrugSituationVO::getSensitivity, DrugSituationVO::setSensitivityRatio);
        return resultVOList;
    }

    @Override
    public List<VirusAntigenResultVO> getInfluenzaPathogenType() {
        // 20250416: 流感病毒抗原性分析-分型，临时从统计表里取，以后要改为维表
        return adsPdVirusGeneStatMapper.getInfluenzaPathogenType();
    }

    @Override
    public List<VirusAntigenResultVO> getInfluenzaReferPathogen() {
        // 20250416: 流感病毒抗原性分析-参考抗原，临时从统计表里取，以后要改为维表
        return adsPdVirusGeneStatMapper.getInfluenzaReferPathogen();
    }

    @Override
    public List<VirusAntigenResultVO> getInfluenzaLabList() {
        // 20250416: 流感病毒抗原性分析-实验室列表，临时从统计表里取，以后要改为维表
        return adsPdPathogenStatMapper.getInfluenzaLabList();
    }
}
