package com.iflytek.cdc.province.service;

import com.iflytek.cdc.edr.vo.dm.DataModelDetailVO;
import com.iflytek.cdc.edr.vo.dm.DataModelVO;
import com.iflytek.cdc.edr.vo.dm.IdentifyRecordVO;
import com.iflytek.cdc.province.model.dto.IdentifyRecordDTO;
import java.util.List;
import java.util.Map;

public interface DataModelDataService {

    DataModelVO requestModelData(String modelId, Map<String, Object> params);

    List<DataModelDetailVO> getDataModelByCode(String formTemplateCode, String configInfo, String modelVersionId);

    List<IdentifyRecordVO> getIdentifyAndRecordId(IdentifyRecordDTO dto);

    DataModelVO getDataModelInfo(String modelId, String modelVersionId);

    String getDetailDataByModel(String loginUserId, String modelId, Map<String, Object> params);

    /**
     * 根据查询条件获取填报表内容
     * */
    DataModelVO getFormReportBy(String modelId, Map<String, Object> params);

}
