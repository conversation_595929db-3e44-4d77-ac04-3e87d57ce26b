package com.iflytek.cdc.province.service;

import com.iflytek.cdc.province.model.dto.DiIndataQueryParam;
import com.iflytek.cdc.edr.vo.DiIndataStatVO;

import java.util.List;

public interface AdsDiIndataStatService {

    /**
     * 获取截止统计日期
     */
    DiIndataStatVO getLastCount(DiIndataQueryParam queryParam);

    /**
     * 统计折线图
     */
    List<DiIndataStatVO> statLineChart(DiIndataQueryParam queryParam);
}

