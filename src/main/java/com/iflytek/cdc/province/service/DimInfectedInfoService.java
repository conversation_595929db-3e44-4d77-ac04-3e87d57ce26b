package com.iflytek.cdc.province.service;

import com.iflytek.cdc.edr.dto.DimInfectedInfoQuery;
import com.iflytek.cdc.edr.vo.ConstantVO;

import java.util.List;

public interface DimInfectedInfoService {
    /**
     * 传染病类别查询
     */
    List<ConstantVO> listInfectedClass();

    /**
     * 传染病类型查询
     */
    List<ConstantVO> listInfectedType(DimInfectedInfoQuery query);

    /**
     * 传染病疾病信息查询
     */
    List<ConstantVO> listInfectedInfoBy(DimInfectedInfoQuery query);

    List<String> getInfectedCodeByNames(List<String> names);
}
