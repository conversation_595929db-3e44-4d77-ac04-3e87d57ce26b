package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.province.model.dto.PatientQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.vo.PatientListVO;
import com.iflytek.cdc.province.model.vo.PatientMedicalInfoVO;

import java.util.List;

public interface PatientMasterDataService {

    /**
     * 获取患者列表
     * */
    PageInfo<PatientListVO> getPatientList(PatientQueryDTO dto);

    /**
     * 查询单个患者的病历列表
     * */
    List<PatientMedicalInfoVO> getPatientMedicalList(String patientId);

    /**
     * 患者列表导出
     * */
    TbCdcmrExportTask patientListExport(PatientQueryDTO dto);
}
