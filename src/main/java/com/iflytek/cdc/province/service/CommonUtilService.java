package com.iflytek.cdc.province.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import com.iflytek.cdc.edr.enums.AreaLevelEnum;
import com.iflytek.cdc.edr.enums.WarningTypeCodeEnum;
import com.iflytek.cdc.edr.utils.MathUtil;
import com.iflytek.cdc.edr.vo.PopulationDataInfoVO;
import com.iflytek.cdc.province.dm.PrivilegeParam;
import com.iflytek.cdc.province.model.dto.CommonQuery;
import com.iflytek.cdc.province.model.dto.DateDimQueryParam;
import com.iflytek.cdc.province.model.vo.TreeNode;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.ToIntFunction;
import java.util.stream.Collectors;

import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

@Service
@Slf4j
public class CommonUtilService {

    @Resource
    private CdcAdminServiceApi cdcAdminServiceApi;

    /**
     * 取两个list的交集
     */
    public static <T> List<T> intersection(List<T> list1, List<T> list2) {
        Set<T> set = new HashSet<>(list1);
        List<T> result = new ArrayList<>();
        if (list2 != null) {
            for (T t : list2) {
                if (set.contains(t)) {
                    result.add(t);
                }
            }
        }
        return result;
    }

    /**
     * 通用的分组函数，接收提取分组键和映射值的函数
     */
    public static <T, K, V> List<List<V>> getGroupValuesBy(List<T> itemList,
                                                           Function<T, K> groupByFunction,
                                                           Function<T, V> valueMapper) {

        // 创建一个存放分组后的结果列表
        List<List<V>> groupedValuesList = new ArrayList<>();

        // 使用传入的 groupByFunction 进行分组
        Map<K, List<T>> groupedMap = itemList.stream()
                .collect(Collectors.groupingBy(groupByFunction));

        for (Map.Entry<K, List<T>> entry : groupedMap.entrySet()) {
            List<T> group = entry.getValue();
            // 使用传入的 valueMapper 将分组中的元素映射为需要的值
            List<V> mappedValues = group.stream()
                    .map(valueMapper)
                    .collect(Collectors.toList());
            groupedValuesList.add(mappedValues);
        }
        return groupedValuesList;
    }

    public static <T extends CommonQuery> PrivilegeParam buildPrivilegeParam(T dto) {
        // 附加当前登录用户的区划权限
        setAreaQueryDTO(dto);
        return new PrivilegeParam(dto);
    }

    /**
     * CommonQuery
     * 集成该类的查询参数 统一设置区划查询
     */
    public static <T extends CommonQuery> void setAreaQueryDTO(T dto) {
        setUserAreaCode(dto,
                T::setProvinceCodes,
                T::setCityCodes,
                T::setDistrictCodes,
                T::setStreetCodes,
                T::setAreaLevel);
        // 融合权限和过滤条件
        if (StringUtils.isNotBlank(dto.getProvinceCode())) {
            dto.setAreaLevel(AreaLevelEnum.PROVINCE.getValue());
            if (CollUtil.isEmpty(dto.getProvinceCodes())) {
                dto.setProvinceCodes(Collections.singletonList(dto.getProvinceCode()));
            } else {
                dto.setProvinceCodes(intersection(Collections.singletonList(dto.getProvinceCode()), dto.getProvinceCodes()));
            }
        }
        if (StringUtils.isNotBlank(dto.getCityCode())) {
            dto.setAreaLevel(AreaLevelEnum.CITY.getValue());
            if (CollUtil.isEmpty(dto.getCityCodes())) {
                dto.setCityCodes(Collections.singletonList(dto.getCityCode()));
            } else {
                dto.setCityCodes(intersection(Collections.singletonList(dto.getCityCode()), dto.getCityCodes()));
            }
        }
        if (StringUtils.isNotBlank(dto.getDistrictCode())) {
            dto.setAreaLevel(AreaLevelEnum.DISTRICT.getValue());
            if (CollUtil.isEmpty(dto.getDistrictCodes())) {
                dto.setDistrictCodes(Collections.singletonList(dto.getDistrictCode()));
            } else {
                dto.setDistrictCodes(intersection(Collections.singletonList(dto.getDistrictCode()), dto.getDistrictCodes()));
            }
        }
        if (StringUtils.isNotBlank(dto.getStreetCode())) {
            dto.setAreaLevel(AreaLevelEnum.STREET.getValue());
            if (CollUtil.isEmpty(dto.getStreetCodes())) {
                dto.setStreetCodes(Collections.singletonList(dto.getStreetCode()));
            } else {
                dto.setStreetCodes(intersection(Collections.singletonList(dto.getStreetCode()), dto.getStreetCodes()));
            }
        }
    }



    /**
     * CommonQuery
     * 集成该类的查询参数 统一设置区划查询
     */
    public static <T extends CommonQuery> void setAreaMultiChooseQueryDTO(T dto) {
        setUserAreaCode(dto,
                T::setProvinceCodes,
                T::setCityCodes,
                T::setDistrictCodes,
                T::setStreetCodes,
                T::setAreaLevel);
        // 融合权限和过滤条件
        if (CollectionUtil.isNotEmpty(dto.getProvinceCodeList())) {
            dto.setAreaLevel(AreaLevelEnum.PROVINCE.getValue());
            if (CollUtil.isEmpty(dto.getProvinceCodes())) {
                dto.setProvinceCodes(dto.getProvinceCodeList());
            } else {
                dto.setProvinceCodes(intersection(dto.getProvinceCodeList(), dto.getProvinceCodes()));
            }
        }
        if (CollectionUtil.isNotEmpty(dto.getCityCodeList())) {
            dto.setAreaLevel(AreaLevelEnum.CITY.getValue());
            if (CollUtil.isEmpty(dto.getCityCodes())) {
                dto.setCityCodes(dto.getCityCodeList());
            } else {
                dto.setCityCodes(intersection(dto.getCityCodeList(), dto.getCityCodes()));
            }
        }
        if (CollectionUtil.isNotEmpty(dto.getDistrictCodeList())) {
            dto.setAreaLevel(AreaLevelEnum.DISTRICT.getValue());
            if (CollUtil.isEmpty(dto.getDistrictCodes())) {
                dto.setDistrictCodes(dto.getDistrictCodeList());
            } else {
                dto.setDistrictCodes(intersection(dto.getDistrictCodeList(), dto.getDistrictCodes()));
            }
        }
        if (CollectionUtil.isNotEmpty(dto.getStreetCodeList())) {
            dto.setAreaLevel(AreaLevelEnum.STREET.getValue());
            if (CollUtil.isEmpty(dto.getStreetCodes())) {
                dto.setStreetCodes(dto.getStreetCodeList());
            } else {
                dto.setStreetCodes(intersection(dto.getStreetCodeList(), dto.getStreetCodes()));
            }
        }
    }

    /**
     * 设置用户的区划code
     */
    private static <T> void setUserAreaCode(T t,
                                            BiConsumer<T, List<String>> province,
                                            BiConsumer<T, List<String>> city,
                                            BiConsumer<T, List<String>> district,
                                            BiConsumer<T, List<String>> street,
                                            BiConsumer<T, Integer> areaLevel) {

        UapUserPo uapUserPo = userInfo.get();
        if (uapUserPo == null) {
            log.error("当前用户信息为空");
            return;
        }
        List<String> provinceCodes = new ArrayList<>();
        List<String> cityCodes = new ArrayList<>();
        List<String> districtCodes = new ArrayList<>();
        List<String> streetCodes = new ArrayList<>();
        Integer targetLevel = null;
        if (StringUtils.isNotBlank(uapUserPo.getOrgProvinceCode())) {
            provinceCodes = getSplitList(uapUserPo.getOrgProvinceCode());
            targetLevel = AreaLevelEnum.PROVINCE.getValue();
        }
        if (StringUtils.isNotBlank(uapUserPo.getOrgCityCode())) {
            cityCodes = getSplitList(uapUserPo.getOrgCityCode());
            targetLevel = AreaLevelEnum.CITY.getValue();
        }
        if (StringUtils.isNotBlank(uapUserPo.getOrgDistrictCode())) {
            districtCodes = getSplitList(uapUserPo.getOrgDistrictCode());
            targetLevel = AreaLevelEnum.DISTRICT.getValue();
        }
        if (StringUtils.isNotBlank(uapUserPo.getOrgStreetCode())) {
            streetCodes = getSplitList(uapUserPo.getOrgStreetCode());
            targetLevel = AreaLevelEnum.STREET.getValue();
        }
        if (province != null) {
            province.accept(t, provinceCodes);
        }
        if (city != null) {
            city.accept(t, cityCodes);
        }
        if (district != null) {
            district.accept(t, districtCodes);
        }
        if (street != null) {
            street.accept(t, streetCodes);
        }
        if (areaLevel != null) {
            areaLevel.accept(t, targetLevel);
        }
    }

    private static List<String> getSplitList(String source) {
        if (StringUtils.isEmpty(source)) {
            return new ArrayList<>();
        }
        return new ArrayList<>(Arrays.asList(source.split(",")));
    }

    /**
     * 判断两个list是否有交集
     */
    public <T> boolean hasIntersection(List<T> list1, List<T> list2) {
        Set<T> set = new HashSet<>(list1);
        for (T item : list2) {
            if (set.contains(item)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 合并两个list
     */
    public <T> List<T> mergeLists(List<T> list1, List<T> list2) {
        if (list1 == null) return list2;
        if (list2 == null) return list1;
        list1.addAll(list2);
        return list1;
    }

    /**
     * 通用方法，list对象转树结构
     */
    public <T> List<TreeNode> groupByList(List<T> list,
                                          Function<T, String> labelFunction,
                                          Function<T, String> valueFunction,
                                          Function<Map.Entry<TreeNode, List<T>>, List<TreeNode>> childFunction) {
        return list.stream()
                .collect(Collectors.groupingBy(e -> TreeNode.fromArea(labelFunction.apply(e), valueFunction.apply(e))))
                .entrySet()
                .stream()
                .map(entry -> {
                    entry.getKey().setChildren(childFunction == null ? null : childFunction.apply(entry));
                    return entry.getKey();
                })
                .collect(Collectors.toList());
    }

    /**
     * 取两个list的交集，以第一个表为主
     */
    public <T> List<T> getIntersection(List<T> list1, List<T> list2) {

        List<T> res = new ArrayList<>();
        Set<T> set1 = new HashSet<>(list1);
        for (T t : list2) {
            if (set1.contains(t)) {
                res.add(t);
            }
        }
        return res;
    }

    /**
     * 转换分页
     */
    public <T> PageInfo<T> fromPageInfo(PageInfo<?> pageInfo, List<T> recordList) {

        PageInfo<T> pageData = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, pageData);
        pageData.setList(recordList);
        return pageData;
    }

    /**
     * 统计list列表中 某个字段值的和
     */
    public <T> Integer countByList(List<T> entities,
                                   Function<T, Integer> statFunction) {

        return entities.stream()
                .map(statFunction)
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
    }

    /**
     * 设置人口数据
     *
     * @param reqDTO 请求数据传输对象，包含街道代码、省份代码、城市代码和区县代码等信息。
     */
    public <T extends DateDimQueryParam> int getPopulation(T reqDTO) {

        List<String> provinceCodes = this.getQueryCodeList(reqDTO.getProvinceCode(), reqDTO.getProvinceCodes());
        List<String> cityCodes = this.getQueryCodeList(reqDTO.getCityCode(), reqDTO.getCityCodes());
        List<String> districtCodes = this.getQueryCodeList(reqDTO.getDistrictCode(), reqDTO.getDistrictCodes());
        List<PopulationDataInfoVO> areaPopulations = cdcAdminServiceApi.listByAreaCodes(reqDTO.getEndDate(), provinceCodes, cityCodes, districtCodes);
        final long sum = areaPopulations.stream().mapToLong(PopulationDataInfoVO::getResidentPopulation).sum();
        return (int) sum;
    }

    private List<String> getQueryCodeList(String code, List<String> codeList) {

        if (StringUtils.isBlank(code) && CollectionUtil.isEmpty(codeList)) {
            return new ArrayList<>();
        }
        if (CollectionUtil.isEmpty(codeList)) {
            return Collections.singletonList(code);
        }
        return codeList;
    }

    /**
     * 先过滤 再转map
     */
    public <T, K> Map<K, T> buildRankingMap(List<T> list, Function<T, K> mapFunction, Predicate<T> filter) {

        return list.stream()
                .filter(filter)
                .collect(Collectors.toMap(mapFunction, Function.identity(), (existing, replacement) -> existing));
    }

    /**
     * 先过滤 再group
     */
    public <T, K> Map<K, List<T>> buildGroupMap(List<T> list, Function<T, K> groupFunction, Predicate<T> filter) {

        return list.stream()
                .filter(filter)
                .collect(Collectors.groupingBy(groupFunction));

    }

    /**
     * 计算占比
     */
    public <T> List<T> fulfillRatio(List<T> items,
                                    ToIntFunction<T> valueExtractor,
                                    BiConsumer<T, Double> ratioValueSetter) {

        int total = items.stream().mapToInt(valueExtractor).sum();
        if (total != 0) {
            items.forEach(item -> {
                double ratioValue = MathUtil.div(valueExtractor.applyAsInt(item), total, 4);
                ratioValueSetter.accept(item, ratioValue);
            });
        }
        return items;
    }

    /**
     * 数字类型转化
     */
    public int transData(Integer num) {
        return Optional.ofNullable(num).orElse(0);
    }
    
    public List<String> getDiseaseCodeListBy(String warningTypeCode, String infectClassCode, List<String> infectTypeList, List<String> infectCodeList, List<String> syndromeCodeList) {
        WarningTypeCodeEnum warningType = WarningTypeCodeEnum.getByName(warningTypeCode);
        if (warningType == null) {
            throw new MedicalBusinessException("warningType有误 " + warningTypeCode);
        }
        switch (warningType) {
            case SYNDROME:
                return getSyndromeCodesBy(syndromeCodeList);
            case INFECTIOUS:
                return getInfectCodesBy(infectClassCode, infectTypeList, infectCodeList);
            default:
                return ListUtil.empty();
        }
    }

    /**
     * 传染病code处理、根据种类以及子类code得到最后需要查询的codeList
     * */
    public List<String> getInfectCodesBy(String infectClassCode, List<String> infectTypeList, List<String> infectCodeList) {

        List<TreeNode> diseaseCodeTree = cdcAdminServiceApi.getInfectedInfo(infectClassCode);

        //如果选择大类，则获取大类下所有子类
        List<String> classDiseaseCodeList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(infectTypeList)) {

            infectTypeList.forEach(e -> {
                for (TreeNode node : diseaseCodeTree) {
                    TreeNode.getAllNodeBy(classDiseaseCodeList, node, e, TreeNode::getValue, TreeNode::getValue);
                }
            });
        }

        //如果选择了子类则获取子类下所有code
        List<String> diseaseCodeList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(infectCodeList)) {

            infectCodeList.forEach(e -> {
                for (TreeNode node : diseaseCodeTree) {
                    TreeNode.getAllNodeBy(diseaseCodeList, node, e, TreeNode::getValue, TreeNode::getValue);
                }
            });
        }
        //如果只选择大类，则返回大类下所有子类的code
        if (CollectionUtil.isEmpty(diseaseCodeList)) {
            return classDiseaseCodeList;
        }
        if (CollectionUtil.isEmpty(classDiseaseCodeList)) {
            return diseaseCodeList;
        }
        return intersection(classDiseaseCodeList, diseaseCodeList);
    }

    /**
     * 症候群code处理、根据种类以及子类code得到最后需要查询的codeList
     * */
    public List<String> getSyndromeCodesBy(List<String> syndromeCodeList) {

        List<TreeNode> diseaseCodeTree = cdcAdminServiceApi.getSyndromeInfo();

        //如果选择了子类则获取子类下所有code
        List<String> diseaseCodeList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(syndromeCodeList)) {
            syndromeCodeList.forEach(e -> {
                for (TreeNode node : diseaseCodeTree) {
                    TreeNode.getAllNodeBy(diseaseCodeList, node, e, TreeNode::getValue, TreeNode::getValue);
                }
            });
        } else {
            for (TreeNode node : diseaseCodeTree) {
                TreeNode.getAllNodeBy(diseaseCodeList, node, "", item -> "", TreeNode::getValue);
            }
        }
        return diseaseCodeList;
    }
    
    public String getSyndromeDiseaseCodeByName(String diseaseName) {
        if (StringUtils.isBlank(diseaseName)) {
            return null;
        }
        List<String> diseaseCodeList = cdcAdminServiceApi.getSyndromeDiseaseCodeByName(diseaseName);
        return CollectionUtils.isEmpty(diseaseCodeList) ? null : diseaseCodeList.get(0);
    }
}
