package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.io.FileUtil;

import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.entity.TbCdcAttachment;
import com.iflytek.cdc.province.service.FileService;
import com.iflytek.cdc.edr.utils.FileUtils;
import com.iflytek.cdc.edr.utils.fileUpload.StorageClientUtil;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.storage.model.StorageObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 文件服务类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileServiceImpl implements FileService {

    @Autowired
    private StorageClientUtil storageClientUtil;

    @Autowired
    private CdcAdminServiceApi adminServiceApi;

    @Autowired
    private BatchUidService batchUidService;

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public List<String> upload(MultipartFile file) {
        return Arrays.asList(executeUpload(file));
    }

    @Override
    public String upload(byte[] bytes, String fileName) {
        String uid = String.valueOf(batchUidService.getUid("tb_cdcew_attachment"));
        String filePath = "";
        try {
            //0, 构建存储对象名称
            String objectName = storageClientUtil.getObjectName(uid, fileName);

            //1，上传文件到SWIFT服务器
            filePath = storageClientUtil.putObject(bytes, objectName, false);

            //2，将文件路径存储到数据库
            TbCdcAttachment tbCdcAttachment = TbCdcAttachment.builder()
                    .id(uid)
                    .attachmentName(fileName)
                    .attachmentPath(filePath).build();
           TbCdcAttachment save = adminServiceApi.saveAttachmentRecord(tbCdcAttachment);
           return save.getId();
        } catch (Exception e) {
            log.error("文件上传错误", e);
            throw new MedicalBusinessException("11458001", "文件上传错误");
        }
    }
    @Override
    public TbCdcAttachment uploadFile(byte[] bytes, String fileName) {
        String uid = String.valueOf(batchUidService.getUid("tb_cdcew_attachment"));
        String filePath = "";
        try {
            //0, 构建存储对象名称
            String objectName = storageClientUtil.getObjectName(uid, fileName);

            //1，上传文件到SWIFT服务器
            filePath = storageClientUtil.putObject(bytes, objectName, false);

            //2，将文件路径存储到数据库
            TbCdcAttachment tbCdcAttachment = TbCdcAttachment.builder()
                    .id(uid)
                    .attachmentName(fileName)
                    .attachmentPath(filePath).build();
            TbCdcAttachment save = adminServiceApi.saveAttachmentRecord(tbCdcAttachment);
            return save;
        } catch (Exception e) {
            log.error("文件上传错误", e);
            throw new MedicalBusinessException("11458001", "文件上传错误");
        }
    }
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public List<String> uploads(MultipartFile[] files) {
        List<String> idList = new ArrayList<>();
        for (MultipartFile file : files) {
            Set<String> fileType = Stream.of("rar", "zip", "doc", "docx", "pdf", "jpg").collect(Collectors.toSet());
            try {
                if (!fileType.contains(Objects.requireNonNull(file.getOriginalFilename()).substring(file.getOriginalFilename().lastIndexOf(".") + 1).toLowerCase(Locale.ROOT))) {
                    throw new MedicalBusinessException("11458002", "文件格式错误！");
                }
            } catch (Exception e) {
                throw new MedicalBusinessException("11458002", "文件格式错误！");
            }
            idList.add(executeUpload(file));
        }
        return idList;
    }



    @Override
    public String storeMediaToFileServer(String fileUrl) {
        String filePath = "";
        try {
            log.info("存储文件到swift服务器，原始地址：{}", fileUrl);
            URL url = new URL(fileUrl);
            String uid = String.valueOf(batchUidService.getUid("tb_cdcew_attachment"));
            HttpHeaders httpHeaders = FileUtils.getHttpHeaders(fileUrl);
            URLConnection conn = url.openConnection();
            InputStream inStream = conn.getInputStream();
            String objectName = storageClientUtil.getObjectName(uid, FileUtil.getName(fileUrl));
            filePath = storageClientUtil.putObject(inStream, objectName, httpHeaders.toSingleValueMap(), false);
        } catch (Exception e) {
            log.error("存储文件失败：{}", e);
        }
        return filePath;
    }

    /**
     * 文件上传处理
     *
     * @param file
     * @return
     */
    private String executeUpload(MultipartFile file) {
        String uid = String.valueOf(batchUidService.getUid("tb_cdcew_attachment"));
        String filePath = "";
        try {
            //0, 构建存储对象名称
            String objectName = storageClientUtil.getObjectName(uid, file.getOriginalFilename());

            //1，上传文件到SWIFT服务器
            filePath = storageClientUtil.putObject(file, objectName, false);

            //2，将文件路径存储到数据库
            TbCdcAttachment tbCdcAttachment = TbCdcAttachment.builder()
                    .id(uid)
                    .attachmentName(file.getOriginalFilename())
                    .attachmentPath(filePath).build();
            TbCdcAttachment save = adminServiceApi.saveAttachmentRecord(tbCdcAttachment);
            return save.getId();
        } catch (Exception e) {
            log.error("文件上传错误", e);
            throw new MedicalBusinessException("11458001", "文件上传错误");
        }
    }

    /**
     * 文件下载
     *
     * @param objName 文件名称
     */
    public ByteArrayOutputStream executeDownload(String objName) {
        StorageObject storageObject = storageClientUtil.getObject(objName);
        if (Objects.isNull(storageObject)) {
            throw new MedicalBusinessException("11458003", "文件不存在");
        }

        InputStream inputStream = storageObject.getInputStream();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = inputStream.read(buffer)) > -1) {
                baos.write(buffer, 0, len);
            }
            baos.flush();
        } catch (IOException e) {
            log.error("文件流转换失败", e);
            throw new MedicalBusinessException("11458004", "文件下载失败");
        }
        try {
            inputStream.close();
        } catch (IOException e) {
            log.error("关闭文件流失败", e);
            throw new MedicalBusinessException("11458004", "文件下载失败");
        }

        return baos;
    }
}
