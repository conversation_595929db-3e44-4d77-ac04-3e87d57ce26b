package com.iflytek.cdc.province.service.impl;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.apiService.UapServiceApi;
import com.iflytek.cdc.edr.dto.edr.CheckPermissionDTO;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import com.iflytek.cdc.edr.enums.DataSourceKey;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO;
import com.iflytek.cdc.province.cache.TableInfoCache;
import com.iflytek.cdc.province.entity.ads.AdsMsEdrOperateRecord;
import com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableInfo;
import com.iflytek.cdc.province.entity.bu.TbCdcewIllnessRecordBrowseLogs;
import com.iflytek.cdc.province.enums.*;
import com.iflytek.cdc.province.mapper.bu.TbCdcewIllnessRecordBrowseLogsMapper;
import com.iflytek.cdc.province.mapper.mpp.DataModelMPPBaseMapper;
import com.iflytek.cdc.province.mapper.pg.*;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.edr.dto.MaintenanceLogsQueryDTO;
import com.iflytek.cdc.province.model.edr.dto.RetrievalLogsQueryDTO;
import com.iflytek.cdc.province.model.edr.vo.*;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.service.EDRService;
import com.iflytek.cdc.province.service.ExportTaskService;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

@Service
@Slf4j
public class EDRServiceImpl extends ServiceImpl<TbCdcewIllnessRecordBrowseLogsMapper, TbCdcewIllnessRecordBrowseLogs> implements EDRService {

    private static final String BROWSE_LOG_TABLE_NAME = "tb_cdcew_illness_record_browse_logs";

    private static final String MAINTENANCE_LOG_TABLE_NAME = "ads_illness_record_update_logs";

    private static final String EDR_INFO = "ads_fs_edr_info";

    @Resource
    private CommonUtilService commonUtilService;

    @Resource
    private TbCdcewIllnessRecordBrowseLogsMapper browseLogsMapper;

    @Resource
    private AdsMsEdrOperateRecordMapper adsMsEdrOperateRecordMapper;

    @Resource
    private AdsEdrLifeInfoMapper recordLifeCycleMapper;

    @Resource
    private AdsFsEdrInfoMapper adsFsEdrInfoMapper;

    @Resource
    private EDRPersonInfoMapper edrPersonInfoMapper;

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private ExportTaskService exportTaskService;

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Resource
    private TableInfoCache tableInfoCache;

    @Resource
    private UapServiceApi uapServiceApi;

    @Resource
    private DataModelMPPBaseMapper dataModelMPPBaseMapper;

    @Override
    public List<MsProcessLogVO> getRecordLifeCycle(String id) {
        if (StringUtils.isBlank(id)) {
            return new ArrayList<>();
        }
        TbCdcdmMetadataTableInfo tableInfo = tableInfoCache.getTableInfoByCache(EDR_INFO);
        if (tableInfo != null && DataSourceKey.MPP.getKey().equals(tableInfo.getDataSourceKey())) {
            // TODO 临时使用标签替代生命周期，且注销档案暂未考虑
            log.debug("查询 mpp 数据库，档案ID: {}", id);
            return dataModelMPPBaseMapper.getRecordLifeCycle(id);
        }
        log.debug("查询 pg 数据库，档案ID: {}", id);
        return recordLifeCycleMapper.getRecordLifeCycle(id);
    }

    /**
     * 编辑个人疾病档案 - 写入日志表
     * */
    @Override
    @Transactional
    public void editIllnessRecord(String loginUserId, CheckPermissionDTO dto) {

        //插入更新日志
        insertMaintenanceLog(loginUserId, dto);
    }

    @Override
    public void insertRetrievalLog(String id, Integer status) {

        UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
        TbCdcewIllnessRecordBrowseLogs browseLog = new TbCdcewIllnessRecordBrowseLogs();
        
        TbCdcdmMetadataTableInfo tableInfo = tableInfoCache.getTableInfoByCache(EDR_INFO);
        if (tableInfo != null && DataSourceKey.MPP.getKey().equals(tableInfo.getDataSourceKey())) {
            log.debug("查询 mpp 数据库，档案ID: {}", id);
            CommonRecordLogVO vo =  dataModelMPPBaseMapper.getCommonRecordLogById(id);
            if (vo != null) {
                BeanUtils.copyProperties(vo, browseLog);
                browseLog.setPermitLivingAreaCode(vo.getPermitLivingAreaCode());
                browseLog.setPermitCompanyAreaCode(vo.getPermitCompanyAreaCode());
                browseLog.setPermitOrgAreaCode(vo.getPermitOrgAreaCode());
            }
        } else {
            CommonRecordLogVO vo = edrPersonInfoMapper.getCommonRecordLogById(id);
            if (vo != null) {
                BeanUtils.copyProperties(vo, browseLog);
                browseLog.setArchiveId(vo.getArchiveId());
            }
            CommonRecordLogVO permitCodeVO = adsFsEdrInfoMapper.getPermitCodeById(id);
            if (permitCodeVO != null) {
                browseLog.setPermitLivingAreaCode(permitCodeVO.getPermitLivingAreaCode());
                browseLog.setPermitCompanyAreaCode(permitCodeVO.getPermitCompanyAreaCode());
                browseLog.setPermitOrgAreaCode(permitCodeVO.getPermitOrgAreaCode());
            }
        }
        browseLog.setArchiveId(id);


        browseLog.setId(String.valueOf(batchUidService.getUid(BROWSE_LOG_TABLE_NAME)));
        browseLog.setBrowseDate(new Date());
        browseLog.setBrowsePersonId(uapUserPo.getId());
        browseLog.setBrowsePerson(uapUserPo.getName());
        browseLog.setStatus(status);
        browseLog.setCreateTime(new Date());
        browseLog.setCreatorId(uapUserPo.getId());
        browseLog.setCreator(uapUserPo.getName());
        
        baseMapper.insert(browseLog);
    }

    @Override
    public void insertMaintenanceLog(String loginUserId, CheckPermissionDTO dto) {

        UapUserPo uapUserPo = Optional.ofNullable(uapServiceApi.getUser(loginUserId)).orElse(new UapUserPo());
        AdsMsEdrOperateRecord operateLog = new AdsMsEdrOperateRecord();
        
        TbCdcdmMetadataTableInfo tableInfo = tableInfoCache.getTableInfoByCache(EDR_INFO);
        if (tableInfo != null && DataSourceKey.MPP.getKey().equals(tableInfo.getDataSourceKey())) {
            log.debug("查询 mpp 数据库，档案ID: {}", dto.getId());
            CommonRecordLogVO vo =  dataModelMPPBaseMapper.getCommonRecordLogById(dto.getId());
            if (vo != null) {
                BeanUtils.copyProperties(vo, operateLog);
                operateLog.setPermitLivingAreaCode(vo.getPermitLivingAreaCode());
                operateLog.setPermitCompanyAreaCode(vo.getPermitCompanyAreaCode());
                operateLog.setPermitOrgAreaCode(vo.getPermitOrgAreaCode());
            }
        } else {
            CommonRecordLogVO vo = edrPersonInfoMapper.getCommonRecordLogById(dto.getId());
            if (vo != null) {
                BeanUtils.copyProperties(vo, operateLog);
            }
            //设置权限code
            CommonRecordLogVO permitCodeVO = adsFsEdrInfoMapper.getPermitCodeById(dto.getId());
            if (permitCodeVO != null) {
                operateLog.setPermitLivingAreaCode(permitCodeVO.getPermitLivingAreaCode());
                operateLog.setPermitCompanyAreaCode(permitCodeVO.getPermitCompanyAreaCode());
                operateLog.setPermitOrgAreaCode(permitCodeVO.getPermitOrgAreaCode());
            }
        }
        operateLog.setArchiveId(dto.getId());

        operateLog.setId(String.valueOf(batchUidService.getUid(MAINTENANCE_LOG_TABLE_NAME)));
        //todo 默认成功
        operateLog.setSuccessFlag(String.valueOf(StatusEnum.SUCCESS.getCode()));
        operateLog.setOperateType(dto.getOperatingType());
        operateLog.setUpdateTime(new Date());
        operateLog.setUpdateId(uapUserPo.getId());
        operateLog.setUpdater(uapUserPo.getName());
        operateLog.setOperateClass(OperateClassEnum.PERSON_OPERATE.getDesc());
        operateLog.setContentJson(dto.getContentJson());

        adsMsEdrOperateRecordMapper.insertOnce(operateLog);
    }

    @Override
    public RetrievalLogsStatVO retrievalLogsStat(RetrievalLogsQueryDTO dto) {

        //根据用户信息设置省市区
        CommonUtilService.setAreaQueryDTO(dto);

        RetrievalLogsStatVO statVO = browseLogsMapper.retrievalLogsStat(dto);
        statVO.setRecordCount(adsFsEdrInfoMapper.getTotalCount());
        return statVO;
    }

    @Override
    public RecordMaintenanceStatVO maintenanceLogsStat(MaintenanceLogsQueryDTO dto) {

        //根据用户信息设置省市区
        CommonUtilService.setAreaQueryDTO(dto);

        RecordMaintenanceStatVO statVO = adsMsEdrOperateRecordMapper.maintenanceLogsStat(dto);
        statVO.setRecordCount(adsFsEdrInfoMapper.getTotalCount());
        return statVO;
    }

    @Override
    public PageInfo<TbCdcewIllnessRecordBrowseLogs> getRetrievalLogs(RetrievalLogsQueryDTO dto) {

        //根据用户信息设置省市区
        CommonUtilService.setAreaQueryDTO(dto);

        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        List<TbCdcewIllnessRecordBrowseLogs> browseLogsList = browseLogsMapper.getRetrievalLogs(dto);

        return new PageInfo<>(browseLogsList);
    }

    @Override
    public TbCdcmrExportTask exportRetrievalLogs(RetrievalLogsQueryDTO dto) {

        ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                                                 dto.getTaskName(),
                                                 dto.getTaskUrl(),
                                                 null,
                                                 dto.getModuleType(),
                                                 dto.getExportType());

        return exportTaskService.addAndUploadFile(dto,
                                                  () -> browseLogsMapper.getRetrievalLogs(dto),
                                                  () -> Optional.ofNullable(browseLogsMapper.countRetrievalLogs(dto)).orElse(0),
                                                  taskDTO,
                                                  RetrievalLogExcelVO.class,
                                                  TbCdcewIllnessRecordBrowseLogs::of);

    }

    @Override
    public PageInfo<AdsMsEdrOperateRecord> getMaintenanceLogs(MaintenanceLogsQueryDTO dto) {

        //根据用户信息设置省市区
        CommonUtilService.setAreaQueryDTO(dto);

        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        List<AdsMsEdrOperateRecord> maintenanceLogsList = adsMsEdrOperateRecordMapper.getMaintenanceLogs(dto);

        return new PageInfo<>(maintenanceLogsList);
    }

    @Override
    public TbCdcmrExportTask exportMaintenanceLogs(MaintenanceLogsQueryDTO dto) {

        ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                                                 dto.getTaskName(),
                                                 dto.getTaskUrl(),
                                                 null,
                                                 dto.getModuleType(),
                                                 dto.getExportType());

        return exportTaskService.addAndUploadFile(dto,
                                                 () -> adsMsEdrOperateRecordMapper.getMaintenanceLogs(dto),
                                                 () -> Optional.ofNullable(adsMsEdrOperateRecordMapper.countMaintenanceLogs(dto)).orElse(0),
                                                 taskDTO,
                                                 MaintenanceLogExcelVO.class,
                                                 AdsMsEdrOperateRecord::of);
    }
}
