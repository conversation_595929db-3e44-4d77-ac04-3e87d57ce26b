package com.iflytek.cdc.province.service;

import com.iflytek.cdc.province.entity.bu.TbCoronavirusSampleCollection;
import com.iflytek.cdc.province.model.dto.CommonQuery;
import com.iflytek.cdc.province.model.dto.CoronavirusSampleQueryDTO;
import com.iflytek.cdc.province.model.vo.CoronavirusSampleCollectionVO;

import java.util.Date;
import java.util.List;

public interface CoronavirusSampleCollectionService {


    /**
     *  查列表
     */
    List<TbCoronavirusSampleCollection> listAll(CoronavirusSampleQueryDTO queryDTO);


    /**
     * 亚型数量分组统计
     */
    List<CoronavirusSampleCollectionVO> groupSubtype();

}
