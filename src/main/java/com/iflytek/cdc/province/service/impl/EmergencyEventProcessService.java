package com.iflytek.cdc.province.service.impl;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoDetailVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO;
import com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo;
import com.iflytek.cdc.province.enums.ProcessInfoTypeEnum;
import com.iflytek.cdc.province.mapper.bu.TbCdcewEmergencyEventProcessInfoMapper;
import com.iflytek.cdc.province.model.dto.MsProcessLogQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoConditionQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.vo.AreaMedCntIndicatorVO;
import com.iflytek.cdc.province.model.vo.MedCntIndicatorVO;
import com.iflytek.cdc.province.model.vo.MsPatientInfoVO;
import com.iflytek.cdc.province.model.vo.ProcessPathogenInfoVO;
import com.iflytek.cdc.province.service.ProcessInfoServiceBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class EmergencyEventProcessService implements ProcessInfoServiceBase {

    @Resource
    private TbCdcewEmergencyEventProcessInfoMapper emergencyEventProcessInfoMapper;

    @Override
    public String getProcessInfoType() {
        return ProcessInfoTypeEnum.EMERGENCY.getCode();
    }

    @Override
    public List<MedCntIndicatorVO> listMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {

        return emergencyEventProcessInfoMapper.listEventMedCntIndicator(queryDTO);
    }

    @Override
    public List<MedCntIndicatorVO> listOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {

        return emergencyEventProcessInfoMapper.listEventOutcomeIndicator(queryDTO);
    }

    @Override
    public List<MsProcessInfoAddressVO> listAddressByIds(MsProcessSimpleInfoQueryDTO dto) {

        return emergencyEventProcessInfoMapper.listEventAddressByIds(dto);
    }

    @Override
    public List<MsPatientInfoVO> listPatientInfoByProcessIds(MsProcessSimpleInfoQueryDTO dto) {

        return emergencyEventProcessInfoMapper.listEventPatientInfoByIds(dto);
    }

    @Override
    public List<AreaMedCntIndicatorVO> listAreaMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {

        return Collections.emptyList();
    }

    @Override
    public List<AreaMedCntIndicatorVO> listAreaOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO) {

        return Collections.emptyList();
    }

    @Override
    public PageInfo<MsProcessSimpleInfoVO> simpleInfoPageList(MsProcessSimpleInfoQueryDTO queryDTO) {

        return new PageInfo<>(Collections.emptyList());
    }

    @Override
    public MsProcessSimpleInfoDetailVO loadProcessSimpleById(String id) {

        return null;
    }

    @Override
    public TbCdcmrExportTask exportSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {

        return null;
    }

    @Override
    public List<MsProcessSimpleInfoVO> listSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {
        return emergencyEventProcessInfoMapper.listEventSimpleInfo(queryDTO);
    }

    @Override
    public Object listProcessModelSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO) {

        return null;
    }

    @Override
    public List<MsProcessLogVO> listProcessLog(MsProcessLogQueryDTO queryDTO) {

        return Collections.emptyList();
    }

    @Override
    public List<TbCdcewEmergencyEventProcessInfo> getProcessInfoBy(List<String> ids) {

        return Collections.emptyList();
    }

    @Override
    public ProcessPathogenInfoVO getProcessPathogenInfoBy(List<String> ids) {

        return null;
    }

    @Override
    public PageInfo<MsProcessSimpleInfoVO> syndromeSimpleInfoPageListByConditions(MsProcessSimpleInfoConditionQueryDTO queryDTO) {
        return null;
    }

}
