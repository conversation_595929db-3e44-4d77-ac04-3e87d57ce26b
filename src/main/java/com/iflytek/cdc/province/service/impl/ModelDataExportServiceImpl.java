package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.common.BooleanEnum;
import com.iflytek.cdc.edr.common.Common;
import com.iflytek.cdc.edr.enums.SensitiveTypeEnum;
import com.iflytek.cdc.edr.utils.DesensitizedUtils;
import com.iflytek.cdc.edr.utils.ExcelUtils;
import com.iflytek.cdc.edr.vo.dm.DataModelFormVO;
import com.iflytek.cdc.edr.vo.dm.DataModelVO;
import com.iflytek.cdc.edr.vo.dm.FormGroupTmpl;
import com.iflytek.cdc.province.cache.TableInfoCache;
import com.iflytek.cdc.province.dm.FilterParam;
import com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormTemplate;
import com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableColumnInfo;
import com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableInfo;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmDataFormTemplateMapper;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmMetadataTableColumnInfoMapper;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.service.DataModelCommonUtils;
import com.iflytek.cdc.province.service.ExportTaskService;
import com.iflytek.cdc.province.service.ModelDataExportService;
import com.iflytek.cdc.province.service.MonitorCommonUtils;
import com.iflytek.cdc.province.service.cdcadmin.CdcAdminService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.iflytek.cdc.CdcDataServiceApplication.threadLocal;
import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

/**
 * 基于模型的数据导出服务实现类，用于导出明细数据
 *
 * @see DataCollectionServiceImpl
 */
@Service
@Slf4j
public class ModelDataExportServiceImpl implements ModelDataExportService {

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Resource
    private ExportTaskService exportTaskService;

    @Resource
    private TbCdcdmDataFormTemplateMapper templateMapper;

    @Resource
    private TbCdcdmMetadataTableColumnInfoMapper columnInfoMapper;

    @Resource
    private TableInfoCache tableInfoCache;

    @Resource
    private CdcAdminService cdcAdminService;

    @Resource
    private DataModelCommonUtils dataModelCommonUtils;

    @Resource
    private MonitorCommonUtils monitorCommonUtils;

    /**
     * 根据导出任务条件执行导出。
     * <p/>
     * 存在两种导出：
     * <ul>
     *     <li>选择导出 - 会选择 recordIds 存在 taskParam 中，导出时根据主键去对应模型获取记录；</li>
     *     <li>批量导出 - 会将条件存在 taskParam 中，导出时根据条件检索记录。</li>
     * </ul>
     * 目前仅实现 <em>选择导出</em>。
     *
     * @param dto 导出任务请求
     */
    @Override
    public TbCdcmrExportTask addAndRunDetailExportTask(ExportTaskDTO dto) {
        String creatorId = StrUtil.isNotBlank(dto.getCreatorId()) ? dto.getCreatorId() : userInfo.get().getId();
        // 前置任务校验
        try {
            this.checkTaskParam(dto, creatorId);
        } catch (MedicalBusinessException e) {
            TbCdcmrExportTask error = new TbCdcmrExportTask();
            error.setErrorDesc(e.getMessage());
            return error;
        }
        // 先新增任务
        TbCdcmrExportTask task = adminServiceApi.addExportTask(dto, creatorId);

        // 导出文件，数据处理也放到线程中，占用时间过长时可以抛出异常中断任务，而不占用服务主线程
        exportTaskService.runTaskAndUploadFile(() -> buildBytes(task), task);

        return task;
    }

    private void checkTaskParam(ExportTaskDTO dto, String creatorId) {
        JSONObject taskParamMap = JSON.parseObject(dto.getTaskParam());
        final String modelId = taskParamMap.getString("modelId");
        if (StrUtil.isBlank(modelId)) {
            throw new MedicalBusinessException("导出条件不全，缺少模型标识");
        }
        final List<String> recordIds = taskParamMap.getObject("recordIds", new TypeReference<List<String>>() {
        });
        if (CollUtil.isEmpty(recordIds)) {
            throw new MedicalBusinessException("导出条件不全，缺少数据标识");
        }
        final Map<String, Object> fieldSelection = taskParamMap.getObject("fieldSelection", new TypeReference<Map<String, Object>>() {
        });
        if (CollUtil.isEmpty(fieldSelection)) {
            throw new MedicalBusinessException("导出条件不全，缺少导出字段");
        }

        TbCdcmrExportTask existTask = adminServiceApi.checkExistTask(dto, creatorId);
        if (null != existTask) {
            throw new MedicalBusinessException("当前导出任务已存在");
        }
        adminServiceApi.checkExportTaskCount(creatorId);
        // 如果执行前能确定数量就提前拦截
        if (dto.getTotalCount() != null) {
            adminServiceApi.checkExportMax(dto.getTotalCount());
        }
    }

    public byte[] buildBytes(TbCdcmrExportTask task) {
        // 提取参数
        // 参见 cdc-admin-service 的 ExportApplicationServiceImpl.getExportApplicationRecord
        JSONObject taskParamMap = JSON.parseObject(task.getTaskParam());
        final String modelId = taskParamMap.getString("modelId");
        final List<String> recordIds = taskParamMap.getObject("recordIds", new TypeReference<List<String>>() {
        });
        final Map<String, Object> fieldSelection = taskParamMap.getObject("fieldSelection", new TypeReference<Map<String, Object>>() {
        });

        // 查询数据
        List<JsonNode> originalData = this.queryDetailData(modelId, recordIds);
        if (originalData.isEmpty()) {
            throw new MedicalBusinessException("查询数据为空");
        }
        task.setTotalCount(originalData.size());

        // 根据模型结构拆分 sheet 页和解析每页循环内容
        List<ExportSheet> multiSheet = this.parseMultiSheetData(modelId, fieldSelection.keySet());
        multiSheet.forEach(sheet -> sheet.fillJsonData(originalData));
        XSSFWorkbook workbook = this.buildExcel(multiSheet);
        return ExcelUtils.generateOutputStream(workbook).toByteArray();
    }

    private List<JsonNode> queryDetailData(String modelId, List<String> recordIds) {
        //数据模型配置
        TbCdcdmDataFormTemplate template = templateMapper.getModelConfigByModelId(modelId);
        if (null == template) {
            throw new RuntimeException("数据配置错误！");
        }
        //数据模型的主键
        TbCdcdmMetadataTableColumnInfo columnInfo = columnInfoMapper.selectByTableAndBusiColName(template.getMasterTableId(), template.getKeyField());
        //数据模型的业务主键
        String businessKey = template.getBusinessKey();
        String primaryKey = (businessKey != null && tableInfoCache.getColumnInfoByCache(businessKey) != null)
                ? tableInfoCache.getColumnInfoByCache(businessKey).getColumnName() : columnInfo.getColumnName();
        FilterParam filterParam = new FilterParam().put(primaryKey, recordIds);

        TbCdcdmMetadataTableInfo tableInfo = tableInfoCache.getTableInfoByCache(template.getRetrieveTable());
        if (tableInfo == null) {
            return new ArrayList<>(0);
        }
        String sql = dataModelCommonUtils.buildRetrieveQuerySql(template, tableInfo, filterParam);
        List<Map<String, Object>> result = monitorCommonUtils.queryByTableSchema(tableInfo.getDataSourceKey(), tableInfo.getSchema(), sql);

        final ObjectMapper root = new ObjectMapper();
        return result.stream()
                .map(map -> map != null ? map.get(Common.CONTENT_JSON) : null)
                .filter(Objects::nonNull)
                .map(Object::toString)
                .map(jsonStr -> {
                    try {
                        return root.readTree(jsonStr).get(modelId);
                    } catch (JsonProcessingException e) {
                        log.error("解析查询结果 json 失败: " + jsonStr, e);
                        throw new MedicalBusinessException("解析查询结果 json 失败");
                    }
                })
                .collect(Collectors.toList());
    }

    public List<ExportSheet> parseMultiSheetData(String modelId, Set<String> exportFields) {
        List<ExportSheet> result = new ArrayList<>();

        // form 不循环，group 不循环，但是有其他 form 或 group 循环：所有 sheet 的公共列
        // form 不循环，group 不循环，也没有其他 form 或 group 循环：唯一 model sheet 的专有列
        List<SheetCell> modelCommonHeaders = new ArrayList<>();
        // form 不循环，group 循环：单独 group sheet 的专有列
        // form 循环，group 循环：单独 group sheet 的专有列
        Map<String, List<SheetCell>> groupHeaders = new LinkedHashMap<>();
        // form 循环，group 不循环，但是有其他 group 循环：该 form 下所有 group sheet 的公共列
        // form 循环，group 不循环，也没有其他 group 循环：单独 form sheet 的专有列
        Map<String, List<SheetCell>> formCommonHeaders = new LinkedHashMap<>();

        DataModelVO dataModel = cdcAdminService.getDataModelConfig(modelId);

        Map<String, String> allIdNames = new HashMap<>();
        allIdNames.put(modelId, dataModel.getModelName());

        for (DataModelFormVO form : dataModel.getFormList()) {
            String formId = form.getModelFormId();
            boolean formRepeat = BooleanEnum.isTrue(form.getIsRepeat());

            allIdNames.put(formId, form.getFormName());
            Map<String, List<SheetCell>> tempGroupHeaders = new LinkedHashMap<>();

            for (FormGroupTmpl group : form.getFormGroupList()) {
                String groupId = group.getField();
                allIdNames.put(groupId, form.getFormName() + "-" + group.getTitle());
                boolean groupRepeat = BooleanEnum.isTrue(group.getRepeat());

                for (FormGroupTmpl.FieldRule field : group.getProps().getRules()) {
                    String fieldId = field.getField();
                    // 只导出选中字段
                    if (exportFields.contains(fieldId)) {

                        SheetCell headerCell = new SheetCell();
                        headerCell.setHeaderName(field.getTitle());
                        headerCell.setFormRepeat(formRepeat);
                        headerCell.setGroupRepeat(groupRepeat);
                        headerCell.setFormId(formId);
                        headerCell.setGroupId(groupId);
                        headerCell.setFieldId(fieldId);
                        headerCell.setDesensitizedType(field.getDesensitizedType());

                        if (groupRepeat) {
                            tempGroupHeaders.computeIfAbsent(groupId, key -> new ArrayList<>()).add(headerCell);
                        } else if (formRepeat) {
                            formCommonHeaders.computeIfAbsent(formId, key -> new ArrayList<>()).add(headerCell);
                        } else {
                            modelCommonHeaders.add(headerCell);
                        }
                    }
                }
            }
            // 给该 form 下所有 group sheet 添加公共列，如果没有就留在 formCommonHeaders 等后续创建 form 级别 sheet
            if (!tempGroupHeaders.isEmpty()) {
                List<SheetCell> commonHeaders = formCommonHeaders.remove(formId);
                if (commonHeaders != null) {
                    tempGroupHeaders.forEach((k, v) -> v.addAll(0, commonHeaders));
                }
                groupHeaders.putAll(tempGroupHeaders);
                tempGroupHeaders.clear();
            }
        }
        // 给所有 form 或 group sheet 添加公共列，如果没有就留在 modelCommonHeaders 等后续创建唯一 sheet
        if (!groupHeaders.isEmpty() || !formCommonHeaders.isEmpty()) {
            groupHeaders.forEach((k, v) -> v.addAll(0, modelCommonHeaders));
            formCommonHeaders.forEach((k, v) -> v.addAll(0, modelCommonHeaders));
            modelCommonHeaders.clear();
        }
        // 最后将 list 转为 sheet 对象
        if (CollUtil.isNotEmpty(modelCommonHeaders)) {
            ExportSheet modelSheet = new ExportSheet(allIdNames.get(modelId));
            modelSheet.setHeaderRow(new SheetRow(modelCommonHeaders, false));
            result.add(modelSheet);
        }
        if (CollUtil.isNotEmpty(formCommonHeaders)) {
            formCommonHeaders.forEach((k, v) -> {
                ExportSheet formSheet = new ExportSheet(allIdNames.get(k));
                formSheet.setHeaderRow(new SheetRow(v, false));
                result.add(formSheet);
            });
        }
        if (CollUtil.isNotEmpty(groupHeaders)) {
            groupHeaders.forEach((k, v) -> {
                ExportSheet groupSheet = new ExportSheet(allIdNames.get(k));
                groupSheet.setHeaderRow(new SheetRow(v, false));
                result.add(groupSheet);
            });
        }

        return result;
    }

    private XSSFWorkbook buildExcel(List<ExportSheet> multiSheetData) {
        XSSFWorkbook workbook = new XSSFWorkbook();

        for (ExportSheet exportSheet : multiSheetData) {
            XSSFSheet sheet = ExcelUtils.createSheet(workbook, exportSheet.getSheetName());

            int rowCount = exportSheet.getRowCount();
            int columnCount = exportSheet.getColumnCount();

            for (int i = 0; i < rowCount; i++) {
                Row row = sheet.createRow(i);
                boolean isHeader = i == 0;

                SheetRow exportRow = exportSheet.getRow(i);

                for (int j = 0; j < columnCount; j++) {
                    SheetCell exportCell = exportRow.getCell(j);

                    String cellValue = isHeader ? exportCell.getHeaderName() : exportCell.getValue();
                    ExcelUtils.createCellAndSetValue(row, cellValue, j);
                }
            }
        }
        return workbook;
    }

    @Data
    public static final class ExportSheet {

        private String sheetName;
        private SheetRow headerRow;
        private List<SheetRow> dataRows;

        public ExportSheet(String sheetName) {
            this.sheetName = sheetName;
            this.dataRows = new ArrayList<>();
        }

        public SheetRow getRow(int rowIndex) {
            if (rowIndex == 0) {
                return headerRow;
            }
            return dataRows.get(rowIndex - 1);
        }

        public int getColumnCount() {
            return headerRow.getColumnCount();
        }

        public int getRowCount() {
            return dataRows.size() + 1;
        }

        public void fillJsonData(List<JsonNode> originalData) {
            List<SheetCell> headers = headerRow.getCells().stream()
                    .sorted(Comparator.comparingInt(SheetCell::priority)
                            .thenComparing(SheetCell::getGroupId)
                            .thenComparing(SheetCell::getFormId))
                    .collect(Collectors.toList());

            // json 节点直接从 model 数据对象开始的，不需要再解一层
            for (JsonNode dataJson : originalData) {
                int totalRows = this.parseTotalRows(dataJson, headers);
                int totalCols = headers.size();

                SheetCell[][] dataCells = new SheetCell[totalRows][totalCols];

                for (int colIndex = 0; colIndex < totalCols; colIndex++) {
                    SheetCell header = headers.get(colIndex);
                    JsonNode formArray = dataJson.get(header.getFormId());
                    int formArraySize = formArray != null ? formArray.size() : 0;

                    int formIndex = 0;
                    int groupIndex = 0;

                    for (int rowIndex = 0; rowIndex < totalRows; rowIndex++) {
                        SheetCell dataCell = new SheetCell();
                        dataCell.setOrder(header.getOrder());

                        int groupArraySize = 0;
                        if (formArray != null && !formArray.isEmpty()) {
                            JsonNode formObject = formArray.get(formIndex);
                            if (formObject != null) {
                                JsonNode groupArray = formObject.get(header.getGroupId());
                                if (groupArray != null && !groupArray.isEmpty()) {
                                    groupArraySize = groupArray.size();
                                    JsonNode groupObject = groupArray.get(groupIndex);
                                    dataCell.setValue(this.fetchValue(groupObject, header.getFieldId(), header.getDesensitizedType()));
                                }
                            }
                        }
                        dataCells[rowIndex][colIndex] = dataCell;

                        groupIndex++;

                        if (groupIndex >= groupArraySize) {
                            formIndex++;
                            groupIndex = 0;
                        }
                        if (formIndex >= formArraySize) {
                            formIndex = 0;
                            groupIndex = 0;
                        }
                    }
                }

                for (SheetCell[] rowData : dataCells) {
                    List<SheetCell> dataRow = Arrays.stream(rowData).sorted(Comparator.comparing(SheetCell::getOrder)).collect(Collectors.toList());
                    this.dataRows.add(new SheetRow(dataRow, true));
                }
            }
        }

        private int parseTotalRows(JsonNode dataJson, List<SheetCell> headers) {
            int totalRows = 0;
            // 用数据对象中实际存在的、最深循环的列，计算总行数
            for (SheetCell anchorHeader : headers) {
                JsonNode formArray = dataJson.get(anchorHeader.getFormId());
                if (formArray == null || formArray.isEmpty()) {
                    continue;
                }
                for (int i = 0, len = formArray.size(); i < len; i++) {
                    JsonNode formObject = formArray.get(i);
                    JsonNode groupArray = formObject.get(anchorHeader.getGroupId());
                    if (groupArray == null || groupArray.isEmpty()) {
                        continue;
                    }
                    totalRows = totalRows + groupArray.size();
                }
                // 如果找到第一个不为空的列，则直接返回总行数
                if (totalRows > 0) {
                    break;
                }
            }
            return totalRows;
        }

        private String fetchValue(JsonNode groupObject, String fieldId, String desensitizedType) {
            if (groupObject == null) {
                return null;
            }
            JsonNode valueNode = groupObject.get(fieldId);
            if (valueNode == null || valueNode.isNull()) {
                return null;
            }
            String value = valueNode.asText();
            SensitiveTypeEnum type = SensitiveTypeEnum.getByCode(desensitizedType);
            // 判断该用户是否需要脱敏
            if (type != null && threadLocal.get() != null && threadLocal.get().getNormalFlag()) {
                value = DesensitizedUtils.desensitizedValue(type, String.valueOf(value), 1, 0, "*");
            }
            return value;
        }
    }

    @Data
    public static final class SheetRow {

        private List<SheetCell> cells;

        public SheetRow(List<SheetCell> cells, boolean isOrdered) {
            this.cells = new ArrayList<>();
            if (isOrdered) {
                this.cells.addAll(cells);
            } else {
                for (int i = 0, size = cells.size(); i < size; i++) {
                    SheetCell cell = cells.get(i);
                    cell.setOrder(i);
                    this.cells.add(cell);
                }
            }
        }

        public SheetCell getCell(int colIndex) {
            return cells.get(colIndex);
        }

        public int getColumnCount() {
            return cells.size();
        }
    }

    @Data
    public static final class SheetCell {
        private int order;
        private boolean formRepeat;
        private boolean groupRepeat;
        private String formId;
        private String groupId;
        private String fieldId;
        private String headerName;
        private String desensitizedType;
        private String value;

        /**
         * @return 优先级，用于排序；优先级高的节点用于锚定数据重复点
         */
        public int priority() {
            int priority = 0;
            if (groupRepeat) {
                priority = priority - 2;
            }
            if (formRepeat) {
                priority = priority - 1;
            }
            return priority;
        }
    }
}
