package com.iflytek.cdc.province.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.iflytek.cdc.CdcDataServiceApplication;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.dto.CommonMasterData;
import com.iflytek.cdc.edr.dto.workbench.UserTodoListDTO;
import com.iflytek.cdc.edr.entity.app.TbCdcmrCheckAuthorityConfig;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import com.iflytek.cdc.edr.serializer.SensitiveSerialize;
import com.iflytek.cdc.edr.utils.DesensitizedUtils;
import com.iflytek.cdc.edr.vo.dm.ProcessRecordVO;
import com.iflytek.cdc.edr.vo.workbench.UserTodoListVO;
import com.iflytek.cdc.edr.vo.workbench.WorkbenchFieldVO;
import com.iflytek.cdc.province.cache.DimRegionCache;
import com.iflytek.cdc.province.entity.bu.TbCdcewAdditionProcessInfo;
import com.iflytek.cdc.province.entity.bu.TbCdcewCorrectionRecord;
import com.iflytek.cdc.province.entity.bu.TbCdcewProcessReportCard;
import com.iflytek.cdc.province.entity.bu.TbCdcewProcessReportCheckLog;
import com.iflytek.cdc.province.entity.dim.DimRegionNation;
import com.iflytek.cdc.province.enums.*;
import com.iflytek.cdc.province.mapper.bu.TbCdcewAdditionProcessInfoMapper;
import com.iflytek.cdc.province.mapper.bu.TbCdcewCorrectionRecordMapper;
import com.iflytek.cdc.province.mapper.bu.TbCdcewProcessReportCardMapper;
import com.iflytek.cdc.province.mapper.bu.TbCdcewProcessReportCheckLogMapper;
import com.iflytek.cdc.province.mapper.pg.AdsMsProcessInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsMsReportInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsMulProcessCaseInfoMapper;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.pandemic.dto.*;
import com.iflytek.cdc.edr.enums.ModuleTypeEnum;
import com.iflytek.cdc.edr.utils.DateFormatUtil;
import com.iflytek.cdc.province.model.pandemic.dto.message.MessageAddDTO;
import com.iflytek.cdc.province.model.pandemic.dto.message.MessageConfig;
import com.iflytek.cdc.province.model.pandemic.dto.message.MessageDTO;
import com.iflytek.cdc.province.model.pandemic.vo.*;
import com.iflytek.cdc.province.model.vo.TreeNode;
import com.iflytek.cdc.province.utils.DateUtils;
import com.iflytek.cdc.province.utils.ExcelUtils;
import com.iflytek.medicalboot.core.id.BatchUidService;
import com.iflytek.zhyl.mdm.sdk.pojo.PatientBaseInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;


@Service
@Slf4j
public class PandemicMonitorCommonService {

    public static final String TB_CDCEW_CORRECTION_RECORD = "tb_cdcew_correction_record";

    public static final String TB_CDCEW_PROCESS_REPORT_CHECK_LOG = "tb_cdcew_process_report_check_log";

    public static final String TB_CDCEW_ADDITION_PROCESS_INFO = "tb_cdcew_addition_process_info";

    public static final String PROCESS = "process";

    public static final String REPORT = "report";

    public static final String LEVEL_1 = "1";

    public static final String LEVEL_2 = "2";

    @Resource
    private BatchUidService batchUidService;

    @Resource
    private TbCdcewProcessReportCheckLogMapper checkLogMapper;

    @Resource
    private MdmRegisterService mdmRegisterService;

    @Resource
    private TbCdcewCorrectionRecordMapper correctionRecordMapper;

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Resource
    private ExportTaskService exportTaskService;

    @Resource
    private TbCdcewProcessReportCardMapper processReportCardMapper;

    @Resource
    private AdsMsProcessInfoMapper adsMsProcessInfoMapper;

    @Resource
    private TbCdcewAdditionProcessInfoMapper additionProcessInfoMapper;

    @Resource
    private AdsMsReportInfoMapper adsMsReportInfoMapper;

    @Resource
    private DimRegionCache dimRegionCache;

    @Resource
    private AdsMulProcessCaseInfoMapper adsMulProcessCaseInfoMapper;

    @Value("${check.reject.flag:false}")
    private boolean checkRejectFlag;

    /**
     * 获取病例列表
     * */
    public PageInfo<ProcessRecordVO> getMedicalPageList(MedicalQueryDTO dto){

        String diseaseTypeCode = ModuleTypeEnum.getTypeByCode(dto.getModuleType());
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        IPandemicMonitorService monitorService = PandemicMonitorFactory.getMedicalService(diseaseTypeCode);
        CommonUtilService.setAreaQueryDTO(dto);
        return new PageInfo<>(monitorService.getMedicalList(dto));
    }

    /**
     * 获取病例列表集合
     */
    public List<ProcessRecordVO> getMedicalList(MedicalQueryDTO dto){
        String diseaseTypeCode = ModuleTypeEnum.getTypeByCode(dto.getModuleType());
        IPandemicMonitorService monitorService = PandemicMonitorFactory.getMedicalService(diseaseTypeCode);
        CommonUtilService.setAreaQueryDTO(dto);
        return monitorService.getMedicalList(dto);
    }
    
    
    /**
     * 工作台代办、已办 不分页
     * @param reqVO
     * @return
     */
    public List<UserTodoListDTO> getMedicalListToDo(UserTodoListVO reqVO,Integer limitNum) {
//        MedicalQueryDTO dto = new MedicalQueryDTO();
//        dto.setModuleType(reqVO.getAppCode());
//
//        if (StrUtil.isNotEmpty(reqVO.getMinStartDate())) {
//            dto.setStartDate(DateUtil.parseDateTime(reqVO.getMinStartDate()) );
//            dto.setEndDate(DateUtil.parseDateTime(reqVO.getMaxEndDate()) );
//        }
//        dto.setKeyword(reqVO.getKeyword());
//        dto.setLimitNum(limitNum);
//        WorkbenchFieldVO.fillMedicalQueryDTO(reqVO.getRequestParam(), dto);
//
//        String diseaseTypeCode = ModuleTypeEnum.getTypeByCode(dto.getModuleType());
//        IPandemicMonitorService monitorService = PandemicMonitorFactory.getMedicalService(diseaseTypeCode);
//        UapUserPo uapUserPo = CdcDataServiceApplication.userInfo.get();
//        if (uapUserPo != null){
//            dto.setProvinceCode(uapUserPo.getOrgProvinceCode());
//            dto.setCityCode(uapUserPo.getOrgCityCode());
//            dto.setDistrictCode(uapUserPo.getOrgDistrictCode());
//        }

        CheckTaskQueryDTO dto = new CheckTaskQueryDTO();
        if (StrUtil.isNotEmpty(reqVO.getMinStartDate())) {
            dto.setStartDate(DateUtil.parseDateTime(reqVO.getMinStartDate()) );
            dto.setEndDate(DateUtil.parseDateTime(reqVO.getMaxEndDate()) );
        }
        dto.setKeyword(reqVO.getKeyword());
        dto.setPageIndex(1);
        dto.setPageSize(1000);
        if (limitNum != null && limitNum>0){
            dto.setPageSize(limitNum);
        }
        WorkbenchFieldVO.fillMedicalQueryDTO(reqVO.getRequestParam(), dto);
        UapUserPo uapUserPo = CdcDataServiceApplication.userInfo.get();
        if (uapUserPo != null){
            dto.setProvinceCode(uapUserPo.getOrgProvinceCode());
            dto.setCityCode(uapUserPo.getOrgCityCode());
            dto.setDistrictCode(uapUserPo.getOrgDistrictCode());
        }
        List<CheckTaskRecordVO> list = this.getCheckTaskList(dto).getList();
        if (CollUtil.isNotEmpty(list)){
            List<UserTodoListDTO> ret = list.stream().map(UserTodoListDTO::of).collect(Collectors.toList());
            return ret;
        }

        return new ArrayList<>(0);
    }

    /**
     * 查看报卡历史的订正信息
     * */
    public PageInfo<CorrectionRecordVO> getReportCorrectHistory(CorrectionRecordQueryDTO dto) {

        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return new PageInfo<>(correctionRecordMapper.getCorrectionRecordByReportId(dto.getDiseaseType(), dto.getReportId()));
    }

    /**
     * 查看报卡历史的审核信息
     * */
    public PageInfo<TbCdcewProcessReportCheckLog> getReportCheckHistory(CheckTaskQueryDTO dto) {

        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return new PageInfo<>(checkLogMapper.getCheckLogsByReportId(dto.getDiseaseType(), dto.getReportId()));
    }

    /**
     * 查看审核任务列表
     * */
    public PageInfo<CheckTaskRecordVO> getCheckTaskList(CheckTaskQueryDTO dto) {

        CommonUtilService.setAreaQueryDTO(dto);
        //设置查询的疾病code
        setDiseaseCodeList(dto);

        if(ReportCheckFlagEnum.WAITING_CHECK.getCode().equals(dto.getReportCheckFlag()) ||
                ReportCheckFlagEnum.CHECKED.getCode().equals(dto.getReportCheckFlag())) {
            //待审核、已审核查应用侧表
            dealQueryDtoBy(dto);
            //如果两个层级都为空，没有查询处理中的报卡权限
            if (CollUtil.isEmpty(dto.getLevel1DiseaseCodeList()) && CollUtil.isEmpty(dto.getLevel2DiseaseCodeList())){
                return new PageInfo<>(new ArrayList<>());
            }
            PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
            List<CheckTaskRecordVO> reportCardList = processReportCardMapper.getCheckTaskList(dto);
            reportCardList.forEach(e->{
                e.setDiagnoseStatus(MainDiagnoseTypeEnum.getDescByCodeOrDesc(e.getDiagnoseStatus()));
                DimRegionNation regionNation = e.getPermitLivingAreaCode() == null ? null : dimRegionCache.getByCode(e.getPermitLivingAreaCode());
                e.setDistrictName(regionNation != null ? regionNation.getDistrictName() : null);
            });
            return new PageInfo<>(reportCardList);
        }else {
            //另外两个状态查数仓侧表
            PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
            List<CheckTaskRecordVO> reportCardList = adsMsReportInfoMapper.getCheckTaskList(dto);

            //取应用侧 审核列表中的数据 使用应用侧状态覆盖数仓侧状态
            if (CollUtil.isNotEmpty(reportCardList)) {
                List<String> idList = reportCardList.stream().map(CheckTaskRecordVO::getReportId).collect(Collectors.toList());
                reportCardList.forEach(e -> {
                    e.setDiagnoseStatus(MainDiagnoseTypeEnum.getDescByCodeOrDesc(e.getDiagnoseStatus()));
                    DimRegionNation regionNation = e.getPermitLivingAreaCode() == null ? null : dimRegionCache.getByCode(e.getPermitLivingAreaCode());
                    e.setDistrictName(regionNation != null ? regionNation.getDistrictName() : null);
                    e.setCheckIdentifyStatus(CheckIdentifyStatusEnum.NO_NEED_CHECK.getCode());
                    e.setCheckProcessStatus(CheckProcessStatusEnum.NOT_STARTED.getCode());
                });
                //应用侧只存储待审核和已审核报卡 故在更新全部数据时只会更新待审核和已审核的报卡状态
                List<CheckTaskRecordVO> buRecordList = processReportCardMapper.getTaskRecordVOListByIdList(dto.getDiseaseType(), idList);
                if (CollUtil.isNotEmpty(buRecordList)){
                    buRecordList.forEach(buRecord -> {
                        reportCardList.forEach(report -> {
                            if (report.getReportId().equals(buRecord.getReportId())){
                                //使用应用侧状态覆盖数仓侧状态
                                report.setCheckIdentifyStatus(buRecord.getCheckIdentifyStatus());
                                report.setCheckProcessStatus(buRecord.getCheckProcessStatus());
                            }
                        });
                    });
                }
            }
            

            return new PageInfo<>(reportCardList);
        }
    }

    /**
     * 设置列表查询时的疾病code列表
     * */
    private void setDiseaseCodeList(CheckTaskQueryDTO dto) {

        //症候群特殊传参处理 - name置换code
        if (ProcessInfoTypeEnum.SYNDROME.getCode().equals(dto.getDiseaseType())){
            List<String> code = adminServiceApi.getSyndromeDiseaseCodeByName(dto.getDiseaseName());
            if (CollUtil.isNotEmpty(code)) {
                dto.setDiseaseCodeList(code);
            }
        }
        //传染病处理diseaseCodeList传参
        if (ProcessInfoTypeEnum.INFECTED.getCode().equals(dto.getDiseaseType())){
            if (CollUtil.isNotEmpty(dto.getDiseaseCodeList())) {
                List<String> res = new ArrayList<>();
                List<TreeNode> infectedTreeList = adminServiceApi.getInfectedInfo(null);
                dto.getDiseaseCodeList().forEach(e -> {
                    infectedTreeList.forEach(tree -> {
                        TreeNode.getAllNodeBy(res, tree, e, TreeNode::getValue, TreeNode::getValue);
                    });
                });
                if (CollUtil.isNotEmpty(res)){
                    dto.setDiseaseCodeList(new ArrayList<>(new HashSet<>(res)));
                }
            }
        }
    }

    /**
     * 根据登录人 处理 待审核、已审核 的查询参数
     * 登录人不存在 既是该疾病的一级审核人又是该疾病的二级审核人的情况
     * */
    private void dealQueryDtoBy(CheckTaskQueryDTO dto) {

        UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
        //获取登录人配置的审核权限信息
        List<TbCdcmrCheckAuthorityConfig> configList = adminServiceApi.getCheckConfigByLoginUser(uapUserPo.getId(), dto.getDiseaseType());
        Map<String, Set<String>> levelDiseaseMap = getLevelDiseaseMap(configList, uapUserPo.getId());
        //待审核
        if(ReportCheckFlagEnum.WAITING_CHECK.getCode().equals(dto.getReportCheckFlag())) {
            if (levelDiseaseMap.containsKey(LEVEL_1)){
                dto.setLevel1DiseaseCodeList(new ArrayList<>(levelDiseaseMap.get(LEVEL_1)));
                dto.setLevel1IdentifyStatusList(Arrays.asList(CheckIdentifyStatusEnum.NOT_CHECK.getCode(), CheckIdentifyStatusEnum.DISTRICT_CHECK_REJECT.getCode(), CheckIdentifyStatusEnum.CITY_CHECK_REJECT.getCode()));
            }
            if (levelDiseaseMap.containsKey(LEVEL_2)) {
                dto.setLevel2DiseaseCodeList(new ArrayList<>(levelDiseaseMap.get(LEVEL_2)));
                dto.setLevel2IdentifyStatusList(Collections.singletonList(CheckIdentifyStatusEnum.DISTRICT_CHECK_PASS.getCode()));
            }
        }
        //如果用户是该疾病的二级审核人
        if(ReportCheckFlagEnum.CHECKED.getCode().equals(dto.getReportCheckFlag())) {
            if (levelDiseaseMap.containsKey(LEVEL_1)){
                dto.setLevel1DiseaseCodeList(new ArrayList<>(levelDiseaseMap.get(LEVEL_1)));
                dto.setLevel1IdentifyStatusList(Collections.singletonList(CheckIdentifyStatusEnum.DISTRICT_CHECK_PASS.getCode()));
            }
            if (levelDiseaseMap.containsKey(LEVEL_2)){
                dto.setLevel2DiseaseCodeList(new ArrayList<>(levelDiseaseMap.get(LEVEL_2)));
                dto.setLevel2IdentifyStatusList(Collections.singletonList(CheckIdentifyStatusEnum.CITY_CHECK_PASS.getCode()));
            }
        }
    }

    private Map<String, Set<String>> getLevelDiseaseMap(List<TbCdcmrCheckAuthorityConfig> configList, String userId){

        Map<String, Set<String>> resultMap = new HashMap<>();
        
        if (CollUtil.isNotEmpty(configList)) {
            //解析配置，处理该用户 各审核层级的疾病配置情况
            Gson gson = new Gson();
            configList.forEach(e -> {
                List<CheckPersonInfo> personInfoList = Arrays.asList(gson.fromJson(e.getCheckPerson(), CheckPersonInfo[].class));
                personInfoList.forEach(person -> {
                    if (userId.equals(person.getId()))
                        resultMap.computeIfAbsent(person.getLevel(), k -> new HashSet<>()).add(e.getDiseaseCode());
                });
            });
        }
        return resultMap;
    }

    /**
     * 查看某病例是否有待审核报卡
     * */
    public CheckTaskRecordVO getProcessWaitCheckReport(CheckTaskQueryDTO dto) {

        return processReportCardMapper.getProcessWaitCheckReport(dto);
    }

    public void addProcessInfo(TbCdcewAdditionProcessInfo processInfo) {

        long uid = batchUidService.getUid(TB_CDCEW_ADDITION_PROCESS_INFO);
        String id = String.valueOf(uid);
        UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
        String empiId;
        //查询病例中是否存在该患者
        PersonProcessQueryDTO dto = PersonProcessQueryDTO.builder()
                                                         .diseaseType(processInfo.getDiseaseType())
                                                         .identityNo(processInfo.getIdentityNo())
                                                         .patientName(processInfo.getPatientName())
                                                         .birthDay(processInfo.getBirthDay())
                                                         .sexName(processInfo.getSexName())
                                                         .build();
        List<PersonProcessInfo> processInfoList = adsMulProcessCaseInfoMapper.getProcessInfoByPerson(dto);

        //病程中不存在该患者信息，则先注册mdm
        if(CollUtil.isEmpty(processInfoList)) {
            PatientBaseInfoDto patientBaseInfoDto = new PatientBaseInfoDto();
            patientBaseInfoDto.setPatientName(processInfo.getPatientName());
            patientBaseInfoDto.setBirthday(processInfo.getBirthDay());
            patientBaseInfoDto.setSexName(processInfo.getSexName());
            patientBaseInfoDto.setResidentIdCard(processInfo.getIdentityNo());
            patientBaseInfoDto.setLocalPatientId(id);
            patientBaseInfoDto.setDataUapId(uapUserPo.getOrgId());
            empiId = mdmRegisterService.registerGlobalPersonId(patientBaseInfoDto);
        }else {
            empiId = processInfoList.get(0).getEmpiId();
        }

        //处理疾病名称传参
        CommonMasterData data = adminServiceApi.getDiseaseInfoByCode(processInfo.getDiseaseType(), processInfo.getDiseaseCode());
        processInfo.setDiseaseName(data == null ? null : data.getMasterDataName());

        processInfo.setEmpiId(empiId);
        processInfo.setId(id);
        processInfo.setProcessId(StringUtils.isBlank(processInfo.getInfectProcessId()) ? processInfo.getSyndromeProcessId() : processInfo.getInfectProcessId());
        processInfo.setCreateTime(new Date());
        processInfo.setCreator(uapUserPo.getName());
        processInfo.setCreatorId(uapUserPo.getId());
        //如果新增病程需要将
        if (PROCESS.equals(processInfo.getAdditionType())) {
            processInfo.setProcessId(DateUtil.format(new Date(), "yyyyMMdd") + "11" + uid % 1000000);
            processInfo.setPermitOrgAreaCode(StringUtils.isNotBlank(uapUserPo.getOrgDistrictCode()) ? uapUserPo.getOrgDistrictCode() :
                    StringUtils.isNotBlank(uapUserPo.getOrgCityCode()) ? uapUserPo.getOrgCityCode() : uapUserPo.getOrgProvinceCode());
        }

        //插入新增病例表
        additionProcessInfoMapper.insert(processInfo);
    }

    public PageInfo<PersonProcessInfo> getProcessInfoByPerson(PersonProcessQueryDTO dto) {

        dto.setBirthDay(DateUtils.formatDate(dto.getBirthDay()));
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return new PageInfo<>(adsMulProcessCaseInfoMapper.getProcessInfoByPerson(dto));
    }

    /**
     * 保存订正信息
     * */
    public void saveCaseCorrection(List<TbCdcewCorrectionRecord> recordList){

        if (CollUtil.isNotEmpty(recordList)) {
            //单次订正 某个病例的传染病报卡
            String diseaseType = recordList.get(0).getDiseaseType();
            String reportId = recordList.get(0).getReportId();
            CheckTaskRecordVO vo = Optional.ofNullable(adsMsReportInfoMapper.getReportCardBy(diseaseType, reportId)).orElse(new CheckTaskRecordVO());
            UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
            recordList.forEach(record -> {
                record.setId(String.valueOf(batchUidService.getUid(TB_CDCEW_CORRECTION_RECORD)));
                record.setCreateTime(new Date());
                record.setCreator(uapUserPo.getName());
                record.setCreatorId(uapUserPo.getId());
                record.setPermitLivingAreaCode(vo.getPermitLivingAreaCode());
                record.setPermitCompanyAreaCode(vo.getPermitCompanyAreaCode());
                record.setPermitOrgAreaCode(vo.getPermitOrgAreaCode());
            });
            correctionRecordMapper.saveBatchCorrection(recordList);
        }
    }

    /**
     * 保存审核信息
     * */
    @Transactional(transactionManager = "buPgTransactionManager")
    public void saveCheckRecord(CheckTaskParamDTO dto) {

        UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
        //获取登录人的审核配置信息
        List<TbCdcmrCheckAuthorityConfig> configList = adminServiceApi.getCheckConfigByLoginUser(uapUserPo.getId(), dto.getDiseaseType());
        TbCdcmrCheckAuthorityConfig diseaseConfig = configList.stream()
                                                              .filter(e -> Objects.equals(dto.getDiseaseCode(), e.getDiseaseCode()))
                                                              .findFirst()
                                                              .orElse(null);
        if (diseaseConfig == null){
            throw new RuntimeException("用户无该疾病审核权限");
        }
        //获取报卡最新的一条审核记录
        TbCdcewProcessReportCheckLog checkLog = checkLogMapper.getLatestCheckLogByReportId(dto.getDiseaseType(), dto.getReportId());
        //获取当前审核报卡信息
        TbCdcewProcessReportCard reportCard = processReportCardMapper.getReportCardBy(dto.getDiseaseType(), dto.getReportId());
        //处理报卡审核状态 并更新
        reportCard = getCheckIdentifyStatus(reportCard, diseaseConfig, checkLog, dto.getCheckResult());
        reportCard.setCheckTime(new Date());
        reportCard.setUpdater(uapUserPo.getName());
        reportCard.setUpdaterId(uapUserPo.getId());
        reportCard.setUpdateTime(new Date());
        processReportCardMapper.updateReportBy(reportCard);

        //新增审核日志
        insertCheckLog(dto, reportCard, uapUserPo);
    }

    /**
     * 根据报卡 最新审核状态 & 疾病审核层级 & 审核结果 -> 审核识别状态&&审核流程状态
     * */
    private TbCdcewProcessReportCard getCheckIdentifyStatus(TbCdcewProcessReportCard reportCard,
                                                            TbCdcmrCheckAuthorityConfig diseaseConfig,
                                                            TbCdcewProcessReportCheckLog checkLog,
                                                            String checkResult){

        CheckResultEnum resultEnum = CheckResultEnum.getByCode(checkResult);
        if (resultEnum == null){
            throw new RuntimeException("审核结果有误");
        }

        if (CheckResultEnum.CHECK_PASS.equals(resultEnum)){
            return handleCheckPass(reportCard, diseaseConfig, checkLog);
        }

        if (CheckResultEnum.CHECK_REJECT.equals(resultEnum)){
            if (checkRejectFlag) sendMessage(reportCard);
            return handleCheckReject(reportCard, diseaseConfig, checkLog);
        }
        return reportCard;
    }

    private void sendMessage(TbCdcewProcessReportCard reportCard){
        MessageConfig messageConfig;
        String messageContent;
        String reportId = reportCard.getReportId();
        if ("infected".equals(reportCard.getDiseaseType())){
            messageConfig = adminServiceApi.getMessageConfigById(MessageEnum.INFECT_CASE_REVIEW_REJECT.getCode());
            messageContent="病例审核驳回,传染病报告卡ID:"+ reportId;
        }else {
            messageConfig = adminServiceApi.getMessageConfigById(MessageEnum.SYNDROME_CASE_REVIEW_REJECT.getCode());
            messageContent="病例审核驳回,病例监测信息ID:"+ reportId;
        }
        if (Objects.nonNull(messageConfig)) {
            MessageAddDTO addDTO = new MessageAddDTO();
            try {
                addDTO = MessageAddDTO.generateVO(messageConfig);
                addDTO.setMessageContent(messageContent);
                addDTO.setSenderId("");
                addDTO.setSender("系统");
                addDTO.setReceiverId(reportCard.getUpdaterId());
                addDTO.setReceiver(reportCard.getUpdater());
                addDTO.setRequestParam(MessageAddDTO.buildParam(reportId));
                addDTO.setMessageConfigId(messageConfig.getId());
                adminServiceApi.saveMessage(addDTO);
            } catch (Exception e) {
                log.error("病例审核驳回消息提醒出错#{}", JSONObject.toJSONString(addDTO), e);
            }
        }
    }

    // 处理审核通过的逻辑
    private TbCdcewProcessReportCard handleCheckPass(TbCdcewProcessReportCard reportCard,
                                                     TbCdcmrCheckAuthorityConfig diseaseConfig,
                                                     TbCdcewProcessReportCheckLog checkLog) {
        // 审核通过 && 审核最新日志为空
        if (checkLog == null) {
            reportCard.setCheckIdentifyStatus(CheckIdentifyStatusEnum.DISTRICT_CHECK_PASS.getCode());
            reportCard.setCheckProcessStatus(Objects.equals(diseaseConfig.getCheckLevel(), LEVEL_1) ?
                    CheckProcessStatusEnum.CHECKED_COMPLETED.getCode() : CheckProcessStatusEnum.IS_CHECKING.getCode());
            return reportCard;
        }

        // 根据审核层级处理
        if (Objects.equals(diseaseConfig.getCheckLevel(), LEVEL_1)) {
            reportCard.setCheckIdentifyStatus(CheckIdentifyStatusEnum.DISTRICT_CHECK_PASS.getCode());
            reportCard.setCheckProcessStatus(CheckProcessStatusEnum.CHECKED_COMPLETED.getCode());
        } else if (Objects.equals(diseaseConfig.getCheckLevel(), LEVEL_2)) {
            // 二级审核
            return handleLevel2CheckPass(reportCard, checkLog);
        }
        return reportCard;
    }

    // 处理二级审核通过 审核中的状态包括（县级审核退回，县级审核通过，市级审核退回）
    private TbCdcewProcessReportCard handleLevel2CheckPass(TbCdcewProcessReportCard reportCard,
                                                           TbCdcewProcessReportCheckLog checkLog) {
        // 最新审核状态是 县级审核退回 -> 审核通过则状态为 县级审核通过/审核中
        if (Objects.equals(checkLog.getCheckIdentifyStatus(), CheckIdentifyStatusEnum.DISTRICT_CHECK_REJECT.getCode())) {
            reportCard.setCheckIdentifyStatus(CheckIdentifyStatusEnum.DISTRICT_CHECK_PASS.getCode());
            reportCard.setCheckProcessStatus(CheckProcessStatusEnum.IS_CHECKING.getCode());
        } else if (Objects.equals(checkLog.getCheckIdentifyStatus(), CheckIdentifyStatusEnum.DISTRICT_CHECK_PASS.getCode())) {
            // 最新审核状态是 县级审核通过 -> 审核通过则状态为 市级审核通过/审核完成
            reportCard.setCheckIdentifyStatus(CheckIdentifyStatusEnum.CITY_CHECK_PASS.getCode());
            reportCard.setCheckProcessStatus(CheckProcessStatusEnum.CHECKED_COMPLETED.getCode());
        } else {
            // 最新审核状态是 市级审核退回 -> 审核通过则状态为 县级审核通过/审核中
            reportCard.setCheckIdentifyStatus(CheckIdentifyStatusEnum.DISTRICT_CHECK_PASS.getCode());
            reportCard.setCheckProcessStatus(CheckProcessStatusEnum.IS_CHECKING.getCode());
        }
        return reportCard;
    }

    // 处理审核退回的逻辑
    private TbCdcewProcessReportCard handleCheckReject(TbCdcewProcessReportCard reportCard,
                                                       TbCdcmrCheckAuthorityConfig diseaseConfig,
                                                       TbCdcewProcessReportCheckLog checkLog) {
        // 一/二级审核 && 审核退回 && 审核最新日志为空
        if (checkLog == null) {
            reportCard.setCheckIdentifyStatus(CheckIdentifyStatusEnum.DISTRICT_CHECK_REJECT.getCode());
        } else {
            // 一/二级审核 && 审核退回 && 审核最新日志不为空
            reportCard.setCheckIdentifyStatus(Objects.equals(diseaseConfig.getCheckLevel(), LEVEL_2) ?
                    CheckIdentifyStatusEnum.CITY_CHECK_REJECT.getCode() : CheckIdentifyStatusEnum.DISTRICT_CHECK_REJECT.getCode());
        }
        reportCard.setCheckProcessStatus(CheckProcessStatusEnum.IS_CHECKING.getCode());
        return reportCard;
    }

    private void insertCheckLog(CheckTaskParamDTO dto, TbCdcewProcessReportCard reportCard, UapUserPo uapUserPo){

        TbCdcewProcessReportCheckLog log = new TbCdcewProcessReportCheckLog();
        log.setId(String.valueOf(batchUidService.getUid(TB_CDCEW_PROCESS_REPORT_CHECK_LOG)));
        log.setReportId(dto.getReportId());
        log.setProcessId(dto.getProcessId());
        log.setDiseaseType(dto.getDiseaseType());
        log.setCheckIdentifyStatus(reportCard.getCheckIdentifyStatus());
        log.setRejectReason(dto.getRejectReason());
        log.setCreator(uapUserPo.getName());
        log.setCreatorId(uapUserPo.getId());
        log.setCreateTime(new Date());
        checkLogMapper.insert(log);
    }

    /**
     * 查询订正记录
     * */
    public PageInfo<CorrectionRecordVO> getCorrectionRecordList(CorrectionRecordQueryDTO dto){
        
        CommonUtilService.setAreaQueryDTO(dto);
        if (dto.getEndDate() != null) {
            dto.setEndDate(DateFormatUtil.getTheLastSecondOfOneDay(DateUtils.formatDate(dto.getEndDate())));
        }
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        List<CorrectionRecordVO> recordVOList = correctionRecordMapper.getCorrectionRecordList(dto);
        return new PageInfo<>(recordVOList);
    }
    
    public List<String> getCorrectionFieldList(CorrectionRecordQueryDTO dto) {

        CommonUtilService.setAreaQueryDTO(dto);
        return correctionRecordMapper.getCorrectionFieldList(dto);
    }

    public TbCdcmrExportTask correctionRecordListExport(CorrectionRecordQueryDTO dto) {

        UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
        String taskParams = JSONObject.toJSONString(dto);
        ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                                                 dto.getTaskName(),
                                                 dto.getTaskUrl(),
                                                 taskParams,
                                                 dto.getModuleType(),
                                                 dto.getExportType());

        TbCdcmrExportTask existTask = adminServiceApi.checkExistTask(taskDTO, uapUserPo.getId());
        if (null != existTask){
            return existTask;
        }
        adminServiceApi.checkExportTaskCount(uapUserPo.getId());

        CommonUtilService.setAreaQueryDTO(dto);
        List<CorrectionRecordVO> recordVOList = correctionRecordMapper.getCorrectionRecordList(dto);

        Integer count = recordVOList.size();
        adminServiceApi.checkExportMax(count);
        taskDTO.setTotalCount(count);
        TbCdcmrExportTask exportTask = adminServiceApi.addExportTask(taskDTO, uapUserPo.getId());
        byte[] bytes = ExcelUtils.exportToExcel(recordVOList, CorrectionRecordVO.class);
        exportTaskService.runTaskAndUploadFile(bytes,exportTask);
        return exportTask;
    }

    public List<CorrectFieldVO> getCorrectFieldRanking(CorrectionRecordQueryDTO dto) {

        List<CorrectFieldVO> fieldVOList = correctionRecordMapper.getCorrectFieldRanking(dto);
        //按照订正次数排序
        return fieldVOList.stream().sorted(Comparator.comparingInt(CorrectFieldVO::getCount).reversed()).collect(Collectors.toList());
    }

    public List<CorrectFieldVO> getCorrectFieldStat(CorrectionRecordQueryDTO dto) {

        if (CollUtil.isEmpty(dto.getFieldNameList())){
            return new ArrayList<>();
        }
        return correctionRecordMapper.getCorrectFieldStat(dto);
    }


    /**
     *  查询入群日志报告记录
     */
    public PageInfo<SyndromeEnterLogReportVO> listSyndromeEnterLogReport(SyndromeEnterLogReportDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        List<SyndromeEnterLogReportVO> enterLogReportList = adsMsReportInfoMapper.selectEnterLogReport(queryDTO);
        // 脱敏
        enterLogReportList.forEach(r -> {
            String patientName = r.getPatientName();
            r.setPatientName(DesensitizedUtils.replacePatientNameX(patientName));
        });
        return new PageInfo<>(enterLogReportList);
    }


    public List<String> getMedicalDept() {
       return adsMulProcessCaseInfoMapper.getMedicalDept();
    }

    public void verifyInformationPush(MessageDTO dto) {
        MessageConfig messageConfig;
        String caseId = dto.getCaseId();
        if ("infected".equals(dto.getDiseaseType())){
            messageConfig = adminServiceApi.getMessageConfigById(MessageEnum.INFECT_CASE_VERIFY.getCode());
        }else {
            messageConfig = adminServiceApi.getMessageConfigById(MessageEnum.SYNDROME_CASE_VERIFY.getCode());
        }
        String messageContent ="病例待核实,ID:"+ caseId;
        if (Objects.nonNull(messageConfig)) {
            MessageAddDTO addDTO = new MessageAddDTO();
            UapUserPo uapUserPo = Optional.ofNullable(userInfo.get()).orElse(new UapUserPo());
            try {
                addDTO = MessageAddDTO.generateVO(messageConfig);
                addDTO.setMessageContent(messageContent);
                addDTO.setSenderId(uapUserPo.getId());
                addDTO.setSender(uapUserPo.getName());
                addDTO.setReceiverId(dto.getReceiverId());
                addDTO.setReceiver(dto.getReceiver());
                addDTO.setRequestParam(MessageAddDTO.buildParam(caseId));
                addDTO.setMessageConfigId(messageConfig.getId());
                adminServiceApi.saveMessage(addDTO);
            } catch (Exception e) {
                log.error("病例待核实消息提醒出错#{}", JSONObject.toJSONString(addDTO), e);
            }
        }
    }
}
