package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.dto.edr.CheckPermissionDTO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO;
import com.iflytek.cdc.province.entity.ads.AdsMsEdrOperateRecord;
import com.iflytek.cdc.province.entity.bu.TbCdcewIllnessRecordBrowseLogs;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.edr.dto.MaintenanceLogsQueryDTO;
import com.iflytek.cdc.province.model.edr.dto.RetrievalLogsQueryDTO;
import com.iflytek.cdc.province.model.edr.vo.RecordMaintenanceStatVO;
import com.iflytek.cdc.province.model.edr.vo.RetrievalLogsStatVO;

import java.util.List;

public interface EDRService {

    /**
     * 查询个人档案记录的生命周期时间轴
     * */
    List<MsProcessLogVO> getRecordLifeCycle(String id);

    /**
     * 编辑疾病档案 插入更新日志
     * 编辑包括更新、注销操作
     * */
    void editIllnessRecord(String loginUserId, CheckPermissionDTO dto);

    /**
     * 根据调阅档案的id 插入调阅日志
     * */
    void insertRetrievalLog(String id, Integer status);

    /**
     * 根据调阅档案的id 插入档案更新日志
     * */
    void insertMaintenanceLog(String loginUserId, CheckPermissionDTO dto);

    /**
     * 调阅日志统计
     * */
    RetrievalLogsStatVO retrievalLogsStat(RetrievalLogsQueryDTO dto);

    /**
     * 维护更新记录统计
     * */
    RecordMaintenanceStatVO maintenanceLogsStat(MaintenanceLogsQueryDTO dto);

    /**
     * 查询调阅日志
     * */
    PageInfo<TbCdcewIllnessRecordBrowseLogs> getRetrievalLogs(RetrievalLogsQueryDTO dto);

    /**
     * 导出调阅日志
     * */
    TbCdcmrExportTask exportRetrievalLogs(RetrievalLogsQueryDTO dto);

    /**
     * 查询维护更新日志
     * */
    PageInfo<AdsMsEdrOperateRecord> getMaintenanceLogs(MaintenanceLogsQueryDTO dto);

    /**
     * 导出维护更新日志
     * */
    TbCdcmrExportTask exportMaintenanceLogs(MaintenanceLogsQueryDTO dto);

}
