package com.iflytek.cdc.province.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.province.entity.ads.DyqwEvent;
import com.iflytek.cdc.province.mapper.pg.DyqwEventMapper;
import com.iflytek.cdc.province.model.dto.DyqwEventDTO;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.service.EmergencyEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class EmergencyEventServiceImpl implements EmergencyEventService {

    @Autowired
    private DyqwEventMapper dyqwEventMapper;

    @Override
    public DyqwEvent getEventByEventId(String id) {
        return dyqwEventMapper.getEventByEventId(id);
    }

    @Override
    public PageInfo<DyqwEvent> getEmergencyEvents(DyqwEventDTO dto) {
        CommonUtilService.setAreaMultiChooseQueryDTO(dto);
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return new PageInfo<>(dyqwEventMapper.getEmergencyEvents(dto));
    }
    @Override
    public Map<String, List<String>> getDownBox() {
        Map<String, List<String>> resMap = new HashMap<>();
        DyqwEventDTO dto = new DyqwEventDTO();
        CommonUtilService.setAreaMultiChooseQueryDTO(dto);
        List<DyqwEvent> res = dyqwEventMapper.getDownBox(dto);
        if (!CollectionUtils.isEmpty(res)){
            resMap = res.stream().collect(Collectors.groupingBy(DyqwEvent::getType, Collectors.mapping(DyqwEvent::getValue, Collectors.toList())));
        }
        dealResMap(resMap);
        return resMap;
    }

    private void dealResMap(Map<String, List<String>> resMap) {
        String[] keys = {"eventType", "severityGrade", "eventStatus"};
        for (String key : keys) {
            resMap.computeIfAbsent(key, k -> new ArrayList<>());
        }
    }
}
