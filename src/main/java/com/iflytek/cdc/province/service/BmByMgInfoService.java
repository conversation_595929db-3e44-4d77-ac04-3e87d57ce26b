package com.iflytek.cdc.province.service;


import com.iflytek.cdc.province.model.dto.BmMonitorResultDTO;
import com.iflytek.cdc.province.model.dto.ByMonitorResultDTO;

public interface BmByMgInfoService {

    BmMonitorResultDTO getBmMonitorResultByRelateTaskId(String relateTaskId, String monitorType);

    ByMonitorResultDTO getByMonitorResultByRelateTaskId(String relateTaskId, String sampleType);
}
