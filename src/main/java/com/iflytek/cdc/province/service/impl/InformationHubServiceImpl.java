package com.iflytek.cdc.province.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.province.model.dto.dm.InformationHubQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.edr.mapper.pg.v2.InfectionsReportLogMapper;
import com.iflytek.cdc.province.service.ExportTaskService;
import com.iflytek.cdc.province.service.InformationHubService;
import com.iflytek.cdc.edr.vo.FieldVO;
import com.iflytek.cdc.edr.vo.dm.*;
import com.iflytek.cdc.edr.vo.pandemic.MedicalInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class InformationHubServiceImpl implements InformationHubService {

    @Resource
    private InfectionsReportLogMapper infectionsReportLogMapper;
    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Resource
    private ExportTaskService exportTaskService;

    @Override
    public PageInfo<MedicalInfoVO> getOriginMedicalInfo(InformationHubQueryDTO dto) {

        List<MedicalInfoVO> result = new ArrayList<>();
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        List<OutpatientOriginMedicalVO> medicalVOList = infectionsReportLogMapper.getOriginOutpatientMedicalList(dto);
        //每个病程需要的字段以 key-value 结构返回
        medicalVOList.forEach(e -> {
            MedicalInfoVO medicalInfoVO = new MedicalInfoVO();
            medicalInfoVO.setRecordId(e.getMedicalId());
            medicalInfoVO.setFieldVOList(FieldVO.convert(e));
            result.add(medicalInfoVO);
        });
        return new PageInfo<>(result);
    }

    @Override
    public TbCdcmrExportTask getOutpatientMedicalInfoExport(String loginUserId, InformationHubQueryDTO dto) {
        String taskParams = JSONObject.toJSONString(dto);
        ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                dto.getTaskName(),
                dto.getTaskUrl(),
                taskParams,
                dto.getModuleType(),
                dto.getExportType());

        TbCdcmrExportTask existTask = adminServiceApi.checkExistTask(taskDTO,loginUserId);
        if (null != existTask){
            return existTask;
        }
        adminServiceApi.checkExportTaskCount(loginUserId);

        Integer count = Optional.ofNullable(infectionsReportLogMapper.countOriginOutpatientMedicalList(dto)).orElse(0) ;
        adminServiceApi.checkExportMax(count);
        taskDTO.setTotalCount(count);
        TbCdcmrExportTask exportTask = adminServiceApi.addExportTask(taskDTO, loginUserId);

        List<OutpatientOriginMedicalVO> medicalVOList = infectionsReportLogMapper.getOriginOutpatientMedicalList(dto);
        exportTaskService.runTaskAndUploadFile(medicalVOList,exportTask, OutpatientOriginMedicalExcelVO.class);
        return exportTask;
    }


    @Override
    public PageInfo<MedicalInfoVO> getInpatientMedicalInfo(InformationHubQueryDTO dto) {

        List<MedicalInfoVO> result = new ArrayList<>();
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        List<InpatientOriginMedicalVO> medicalVOList = infectionsReportLogMapper.getOriginInpatientMedicalList(dto);
        //每个病程需要的字段以 key-value 结构返回
        medicalVOList.forEach(e -> {
            MedicalInfoVO medicalInfoVO = new MedicalInfoVO();
            medicalInfoVO.setRecordId(e.getMedicalId());
            medicalInfoVO.setFieldVOList(FieldVO.convert(e));
            result.add(medicalInfoVO);
        });
        return new PageInfo<>(result);
    }
    @Override
    public TbCdcmrExportTask getInpatientMedicalInfoExport(String loginUserId, InformationHubQueryDTO dto) {
        String taskParams = JSONObject.toJSONString(dto);
        ExportTaskDTO taskDTO = ExportTaskDTO.of(dto.getExportTaskId(),
                dto.getTaskName(),
                dto.getTaskUrl(),
                taskParams,
                dto.getModuleType(),
                dto.getExportType());

        TbCdcmrExportTask existTask = adminServiceApi.checkExistTask(taskDTO,loginUserId);
        if (null != existTask){
            return existTask;
        }
        adminServiceApi.checkExportTaskCount(loginUserId);

        Integer count = Optional.ofNullable(infectionsReportLogMapper.countOriginInpatientMedicalList(dto)).orElse(0) ;
        adminServiceApi.checkExportMax(count);
        taskDTO.setTotalCount(count);
        TbCdcmrExportTask exportTask = adminServiceApi.addExportTask(taskDTO, loginUserId);

        List<InpatientOriginMedicalVO> medicalVOList = infectionsReportLogMapper.getOriginInpatientMedicalList(dto);
        exportTaskService.runTaskAndUploadFile(medicalVOList,exportTask, InpatientOriginMedicalExcelVO.class);
        return exportTask;
    }

}
