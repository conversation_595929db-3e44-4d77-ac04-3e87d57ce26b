package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.dto.workbench.UserTodoListDTO;
import com.iflytek.cdc.edr.vo.dm.ProcessRecordVO;
import com.iflytek.cdc.province.model.pandemic.dto.MedicalQueryDTO;
import com.iflytek.cdc.province.mapper.pg.AdsMsProcessInfoMapper;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.service.IPandemicMonitorService;
import com.iflytek.cdc.province.service.MonitorCommonUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service("syndromeMonitorCategory")
public class SyndromePandemicMonitorService implements IPandemicMonitorService {

    @Resource
    private AdsMsProcessInfoMapper adsMsProcessInfoMapper;

    @Resource
    private MonitorCommonUtils monitorCommonUtils;

    @Resource
    private CommonUtilService commonUtilService;

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Override
    public List<ProcessRecordVO> getMedicalList(MedicalQueryDTO dto) {

        //获取疾病code
        List<String> code = adminServiceApi.getSyndromeDiseaseCodeByName(dto.getDiseaseName());
        if(CollectionUtil.isNotEmpty(code)){
            dto.setDiseaseCodeList(code);
        }
        //根据给定条件查询 症候群 病例列表
        return adsMsProcessInfoMapper.getSyndromeProcessListBy(dto);
    }

    @Override
    public List<UserTodoListDTO> getMedicalListToDo(MedicalQueryDTO dto) {
        //获取疾病code
        List<String> code = adminServiceApi.getSyndromeDiseaseCodeByName(dto.getDiseaseName());
        if(CollectionUtil.isEmpty(code)){
            return new ArrayList<>();
        }
        dto.setDiseaseCodeList(code);
        //根据给定条件查询 症候群 病例列表
        List<ProcessRecordVO> adsMsProcessInfoList = adsMsProcessInfoMapper.getSyndromeProcessListBy(dto);

        List<UserTodoListDTO> ret = adsMsProcessInfoList.stream()
                .map(UserTodoListDTO::buildFromSyndromeMedical)
                .collect(Collectors.toList());
        return ret;
    }
}
