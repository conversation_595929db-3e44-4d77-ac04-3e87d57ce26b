package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.apiService.UapServiceApi;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import com.iflytek.cdc.edr.enums.DateDimEnum;
import com.iflytek.cdc.edr.utils.ExcelUtils;
import com.iflytek.cdc.edr.vo.AreaInOrgStatisticExcelVO;
import com.iflytek.cdc.province.model.dto.DiInOrgQueryDTO;
import com.iflytek.cdc.province.entity.ads.AdsDiInorgInfo;
import com.iflytek.cdc.edr.enums.AreaLevelEnum;
import com.iflytek.cdc.province.mapper.pg.AdsDiInorgInfoMapper;
import com.iflytek.cdc.province.model.vo.TimeTrendVO;
import com.iflytek.cdc.province.service.AdsDiInorgService;
import com.iflytek.cdc.edr.vo.AreaInOrgCountVO;
import com.iflytek.cdc.edr.vo.AreaInOrgStatisticVO;
import com.iflytek.cdc.edr.vo.InOrgTypeCountVO;
import com.iflytek.cdc.edr.vo.OrgDetailInfoVO;
import com.iflytek.cdc.edr.vo.InOrgStatusTtlVO;
import com.iflytek.cdc.province.utils.StatUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

@Service
@Configuration
@ConditionalOnProperty(value = "dataService.version", havingValue = "v2")
public class AdsDiInorgServiceImpl implements AdsDiInorgService {
    @Resource
    private UapServiceApi uapServiceApi;
    @Resource
    private AdsDiInorgInfoMapper adsDiInorgInfoMapper;
    @Override
    public PageInfo<AdsDiInorgInfo> queryBy(DiInOrgQueryDTO queryDTO, String loginUserName) {
        setAreaInfo(queryDTO, loginUserName);
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return new PageInfo<>(adsDiInorgInfoMapper.queryBy(queryDTO));
    }

    @Override
    public List<AreaInOrgCountVO> areaCountTop10(DiInOrgQueryDTO queryDTO, String loginUserName) {
        setAreaInfo(queryDTO, loginUserName);
        setStatDateMaxDay(queryDTO);
        return adsDiInorgInfoMapper.areaCount(queryDTO);
    }

    @Override
    public PageInfo<AreaInOrgStatisticVO> areaStatistic(DiInOrgQueryDTO queryDTO, String loginUserName) {
        setAreaInfoList(queryDTO, loginUserName);
        setStatDateMaxDay(queryDTO);
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return new PageInfo<>(adsDiInorgInfoMapper.areaStatistic(queryDTO));
    }

    @Override
    public PageInfo<OrgDetailInfoVO> queryOrgDetails(DiInOrgQueryDTO queryDTO, String loginUserName) {
        setAreaInfoList(queryDTO, loginUserName);
        setStatDateMaxDay(queryDTO);
        PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize());
        return new PageInfo<>(adsDiInorgInfoMapper.queryOrgDetails(queryDTO));
    }

    @Override
    public InOrgStatusTtlVO queryOrgConnectionStatus(DiInOrgQueryDTO queryDTO, String loginUserName) {
        setAreaInfoList(queryDTO, loginUserName);
        setStatDateMaxDay(queryDTO);
        
        // 查询所有机构信息
        List<OrgDetailInfoVO> allOrgs = adsDiInorgInfoMapper.queryOrgDetailsByStatus(queryDTO);
        
        // 按对接状态分组
        List<OrgDetailInfoVO> connectedOrgs = new ArrayList<>();
        List<OrgDetailInfoVO> unconnectedOrgs = new ArrayList<>();
        List<OrgDetailInfoVO> connectingOrgs = new ArrayList<>();
        List<OrgDetailInfoVO> notConnectingOrgs = new ArrayList<>();
        
        for (OrgDetailInfoVO org : allOrgs) {
            switch (org.getInStatus()) {
                case "已对接":
                    connectedOrgs.add(org);
                    break;
                case "未对接":
                    unconnectedOrgs.add(org);
                    break;
                case "正在对接":
                    connectingOrgs.add(org);
                    break;
                case "不对接":
                    notConnectingOrgs.add(org);
                    break;
                default:
                    unconnectedOrgs.add(org);
                    break;
            }
        }
        
        // 构建返回对象
        InOrgStatusTtlVO result = new InOrgStatusTtlVO();
        result.setConnectedOrgs(connectedOrgs);
        result.setUnconnectedOrgs(unconnectedOrgs);
        result.setConnectingOrgs(connectingOrgs);
        result.setNotConnectingOrgs(notConnectingOrgs);
        
        // 计算各状态机构数量
        result.calculateCounts();
        
        return result;
    }

    @Override
    public ResponseEntity<byte[]> areaStatisticExport(DiInOrgQueryDTO queryDTO, String loginUserName) {

        setAreaInfoList(queryDTO, loginUserName);
        setStatDateMaxDay(queryDTO);

        List<AreaInOrgStatisticExcelVO> excelVOList = new ArrayList<>();
        //直接查询各区域的机构覆盖度
        List<AreaInOrgStatisticVO> statVO = adsDiInorgInfoMapper.areaStatisticBy(queryDTO);
        //cityCode字段非空则查询省一级 需要将市一级的机构统计并添加到结果中
        if (StringUtils.isEmpty(queryDTO.getCityCode())) {
            List<AreaInOrgStatisticVO> cityStatVO = adsDiInorgInfoMapper.areaStatistic(queryDTO);
            statVO.addAll(cityStatVO);
        }
        statVO = statVO.stream().sorted(Comparator.nullsLast(Comparator.comparing(AreaInOrgStatisticVO::getAreaCode))).collect(Collectors.toList());
        statVO.forEach(e -> {
            AreaInOrgStatisticExcelVO excelVO = AreaInOrgStatisticExcelVO.from(e);
            excelVOList.add(excelVO);
        });

        return ExcelUtils.exportByte(excelVOList, AreaInOrgStatisticExcelVO.class);
    }

    @Override
    public List<InOrgTypeCountVO> inOrgTypeCount(DiInOrgQueryDTO queryDTO, String loginUserName) {
        setAreaInfo(queryDTO, loginUserName);
        setStatDateMaxDay(queryDTO);
        return adsDiInorgInfoMapper.inOrgTypeCount(queryDTO);
    }

    @Override
    public List<TimeTrendVO> inOrgTypeTimeTrend(DiInOrgQueryDTO queryDTO) {
        DateDimEnum dateDimEnum = DateDimEnum.getByCode(queryDTO.getDateDimType());
        if(dateDimEnum == null){
            dateDimEnum = DateDimEnum.DAY;
        }
        Date lastStatDataMaxDay = getLastStatDataMaxDay(new Date());
        queryDTO.setStartDate(lastStatDataMaxDay == null ? new Date() : lastStatDataMaxDay);
        queryDTO.setEndDate(new Date());
        return StatUtils.fulfillAndConvertDayDateData(
                StatUtils.fulfillPeriodValueByDay(queryDTO, q -> adsDiInorgInfoMapper.inOrgTypeTimeTrend(q), TimeTrendVO::getStatDate, TimeTrendVO.currValueAndLastValueMappingList()),
                queryDTO.getStartDate(),
                queryDTO.getEndDate(),
                dateDimEnum,
                TimeTrendVO::getStatDate,
                TimeTrendVO::setStatDate,
                TimeTrendVO.dataConverter
        );
    }

    @Override
    public Date getLastStatData() {
        UapUserPo uapUserPo = userInfo.get();
        return adsDiInorgInfoMapper.getLastStatDate(uapUserPo.getOrgProvinceCode(), uapUserPo.getOrgCityCode(), uapUserPo.getOrgDistrictCode());
    }

    public Date getLastStatDataMaxDay(Date date) {
        UapUserPo uapUserPo = userInfo.get();
        return adsDiInorgInfoMapper.getLastStatDateMaxDay(date, uapUserPo.getOrgProvinceCode(), uapUserPo.getOrgCityCode(), uapUserPo.getOrgDistrictCode());
    }

    /**
     * 设置区域信息
     */
    private void setAreaInfo(DiInOrgQueryDTO queryDTO, String loginUserName){
        uapServiceApi.setProvinceAndCityAndDistrict(loginUserName, queryDTO,
            DiInOrgQueryDTO::setProvinceCodes,
            DiInOrgQueryDTO::setCityCodes,
            DiInOrgQueryDTO::setDistrictCodes,
            (d, areaLevel) -> {
                if (d.getAreaLevel() == null){
                    d.setAreaLevel(areaLevel);
                }
                if (StringUtils.isNotEmpty(d.getProvinceCode())){
                    d.setAreaLevel(AreaLevelEnum.PROVINCE.getValue());
                }
                if (StringUtils.isNotEmpty(d.getCityCode())){
                    d.setAreaLevel(AreaLevelEnum.CITY.getValue());
                }
                if (StringUtils.isNotEmpty(d.getDistrictCode())){
                    d.setAreaLevel(AreaLevelEnum.DISTRICT.getValue());
                }

            });
    }

    /**
     * 设置区域信息多选
     */
    private void setAreaInfoList(DiInOrgQueryDTO queryDTO, String loginUserName){
        // 先合并多选和单选
        if(StringUtils.isNotBlank(queryDTO.getProvinceCode())) {
            if (queryDTO.getProvinceCodeList() == null) {
                queryDTO.setProvinceCodeList(new ArrayList<>());
            }
            queryDTO.getProvinceCodeList().add(queryDTO.getProvinceCode());
            queryDTO.setProvinceCodeList(new ArrayList<>(new HashSet<>(queryDTO.getProvinceCodeList())));
        }
        if(StringUtils.isNotBlank(queryDTO.getCityCode())) {
            if (queryDTO.getCityCodeList() == null) {
                queryDTO.setCityCodeList(new ArrayList<>());
            }
            queryDTO.getCityCodeList().add(queryDTO.getCityCode());
            queryDTO.setCityCodeList(new ArrayList<>(new HashSet<>(queryDTO.getCityCodeList())));
        }
        if(StringUtils.isNotBlank(queryDTO.getDistrictCode())) {
            if (queryDTO.getDistrictCodeList() == null) {
                queryDTO.setDistrictCodeList(new ArrayList<>());
            }
            queryDTO.getDistrictCodeList().add(queryDTO.getDistrictCode());
            queryDTO.setDistrictCodeList(new ArrayList<>(new HashSet<>(queryDTO.getDistrictCodeList())));
        }
        uapServiceApi.setProvinceAndCityAndDistrict(loginUserName, queryDTO,
                DiInOrgQueryDTO::setProvinceCodes,
                DiInOrgQueryDTO::setCityCodes,
                DiInOrgQueryDTO::setDistrictCodes,
                (d, areaLevel) -> {
                    if (d.getAreaLevel() == null){
                        d.setAreaLevel(areaLevel);
                    }
                    if (CollectionUtil.isNotEmpty(d.getProvinceCodeList())){
                        d.setAreaLevel(AreaLevelEnum.PROVINCE.getValue());
                    }
                    if (CollectionUtil.isNotEmpty(d.getCityCodeList())){
                        d.setAreaLevel(AreaLevelEnum.CITY.getValue());
                    }
                    if (CollectionUtil.isNotEmpty(d.getDistrictCodeList())){
                        d.setAreaLevel(AreaLevelEnum.DISTRICT.getValue());
                    }
                });
        // 融合权限和过滤条件 要过滤多选和用户本身的权限
//        if (CollectionUtil.isNotEmpty(queryDTO.getProvinceCodeList())) {
//            if (CollUtil.isEmpty(queryDTO.getProvinceCodes())) {
//                queryDTO.setProvinceCodes(queryDTO.getProvinceCodeList());
//            } else {
//                queryDTO.setProvinceCodes(CommonUtilService.
//                        intersection(queryDTO.getProvinceCodeList(), queryDTO.getProvinceCodes()));
//            }
//        }
//        if (CollectionUtil.isNotEmpty(queryDTO.getDistrictCodeList())) {
//            if (CollUtil.isEmpty(queryDTO.getDistrictCodes())) {
//                queryDTO.setDistrictCodes(queryDTO.getDistrictCodeList());
//            } else {
//                queryDTO.setDistrictCodes((CommonUtilService.intersection(queryDTO.getDistrictCodeList(),
//                        queryDTO.getDistrictCodes())));
//            }
//        }
//        if (CollectionUtil.isNotEmpty(queryDTO.getCityCodeList())) {
//            if (CollUtil.isEmpty(queryDTO.getCityCodes())) {
//                queryDTO.setCityCodes(queryDTO.getCityCodeList());
//            } else {
//                queryDTO.setCityCodes(CommonUtilService.intersection(queryDTO.getCityCodeList(),
//                        queryDTO.getCityCodes()));
//            }
//        }
//        else{
//            queryDTO.setDistrictCodes(new ArrayList<>());
//        }
    }

    /**
     * 设置统计日期
     */
    private void setStatDate(DiInOrgQueryDTO queryDTO){
        if (queryDTO.getStatDate() == null){
            queryDTO.setStatDate(getLastStatData() == null ? new Date() : getLastStatData());
        }
    }

    private void setStatDateMaxDay(DiInOrgQueryDTO queryDTO){
        Date lastStatDataMaxDay = getLastStatDataMaxDay(queryDTO.getStatDate());
        queryDTO.setStatDate(lastStatDataMaxDay == null ? new Date() : lastStatDataMaxDay);
    }

    /**
     * 查询医疗机构类型(去重)
     */
    @Override
    public List<AdsDiInorgInfo> listInorgType(DiInOrgQueryDTO queryDTO, String loginUserName) {
        setAreaInfo(queryDTO, loginUserName);
        return adsDiInorgInfoMapper.selectDiInorgType(queryDTO);
    }

}
