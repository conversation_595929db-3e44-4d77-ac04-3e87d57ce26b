package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.adsMsQualityEval.QualityEvalRespVO;
import com.iflytek.cdc.province.model.dto.AdsMsReportReqDTO;
import com.iflytek.cdc.province.model.pandemic.vo.CheckTaskRecordVO;
import org.springframework.http.ResponseEntity;

import java.util.List;

/**
 * <p>
 * 诊疗(medical service)-传染病/症候群报告卡-监测业务质量（quality）- 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
public interface AdsMsReportQService {

    /**
     * 质量评估统计
     *
     * @param reqDTO        请求参数
     * @param loginUserName 登录用户名
     * @return 质量评估统计结果
     */
    List<QualityEvalRespVO> evalStat(AdsMsReportReqDTO reqDTO, String loginUserName);

    /**
     * 质量评估统计-导出
     *
     * @param reqDTO        请求参数
     * @param loginUserName 登录用户名
     * @return 导出EXCEL文件
     */
    ResponseEntity<byte[]> evalStatExport(AdsMsReportReqDTO reqDTO, String loginUserName);

    /**
     * 病例列表（报告卡）
     *
     * @param reqDTO        请求参数
     * @param loginUserName 登录用户名
     * @return 病例列表（报告卡）
     */
    PageInfo<CheckTaskRecordVO> reportInfoList(AdsMsReportReqDTO reqDTO, String loginUserName);

    /**
     * 病例列表（报告卡）-导出
     *
     * @param reqDTO        请求参数
     * @param loginUserName 登录用户名
     * @return 导出EXCEL文件
     */
    ResponseEntity<byte[]> reportInfoListExport(AdsMsReportReqDTO reqDTO, String loginUserName);
}
