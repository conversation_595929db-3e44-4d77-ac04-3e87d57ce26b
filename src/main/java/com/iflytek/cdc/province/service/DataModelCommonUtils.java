package com.iflytek.cdc.province.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.Gson;
import com.iflytek.cdc.edr.common.Common;
import com.iflytek.cdc.edr.enums.LogicEnum;
import com.iflytek.cdc.edr.enums.SensitiveTypeEnum;
import com.iflytek.cdc.edr.utils.DateFormatUtil;
import com.iflytek.cdc.edr.utils.DesensitizedUtils;
import com.iflytek.cdc.edr.vo.FieldVO;
import com.iflytek.cdc.edr.vo.dm.DataModelDetailVO;
import com.iflytek.cdc.edr.vo.dm.DataModelVO;
import com.iflytek.cdc.edr.vo.dm.FormGroupTmpl;
import com.iflytek.cdc.edr.vo.pandemic.MedicalInfoVO;
import com.iflytek.cdc.province.cache.TableInfoCache;
import com.iflytek.cdc.province.dm.DataModel;
import com.iflytek.cdc.province.dm.FilterParam;
import com.iflytek.cdc.province.dm.FormGroup;
import com.iflytek.cdc.province.dm.ModelForm;
import com.iflytek.cdc.province.dm.engine.AnsiSqlBuilder;
import com.iflytek.cdc.province.dm.engine.Column;
import com.iflytek.cdc.province.dm.engine.Table;
import com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormTemplate;
import com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableColumnInfo;
import com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableInfo;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmDataFormTemplateMapper;
import com.iflytek.cdc.province.mapper.bu.TbCdcdmMetadataTableColumnInfoMapper;
import com.iflytek.cdc.province.model.dto.dm.*;
import com.iflytek.cdc.province.model.vo.FieldsConfigAnalysisVO;
import com.iflytek.cdc.province.model.vo.ModelFieldsConfig;
import com.iflytek.cdc.province.service.cdcadmin.CdcAdminService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.iflytek.cdc.CdcDataServiceApplication.threadLocal;

@Service
@Slf4j
public class DataModelCommonUtils {

    @Resource
    private CdcAdminService cdcAdminService;

    @Resource
    private TableInfoCache tableInfoCache;

    @Resource
    private TbCdcdmDataFormTemplateMapper tbCdcdmDataFormTemplateMapper;

    @Resource
    private TbCdcdmMetadataTableColumnInfoMapper tbCdcdmMetadataTableColumnInfoMapper;

    @Resource
    private MonitorCommonUtils monitorCommonUtils;

    @Resource
    private TbCdcdmDataFormTemplateMapper templateMapper;

    @Resource
    private TbCdcdmMetadataTableColumnInfoMapper columnInfoMapper;

    @Resource
    private CommonUtilService commonUtilService;

    /**
     * 根据数据模型查询列表显示字段
     * */
    private List<FormGroupTmpl.FieldRule> getListFieldsByModelId(String modelId) {

        //查询模型配置
        DataModelVO modelVO = cdcAdminService.getDataModelConfig(modelId);
        //筛选需要展示在列表的字段
        List<FormGroupTmpl.FieldRule> fields = new ArrayList<>();
        if (modelVO != null) {
            //循环表单
            modelVO.getFormList().forEach(form -> {
                //表单中分组
                form.getFormGroupList().forEach(group -> {
                    //组中字段
                    FormGroupTmpl.GroupProps props = group.getProps();
                    props.getRules().forEach(e -> {
                        //当字段配置成在列表显示时 将该字段添加到fields中
                        if (e.isListKey()){
                            fields.add(e);
                        }
                    });
                });
            });
        }
        return fields;
    }

    /**
     * 构造显示字段
     * */
    public List<ConfigFieldsDTO> buildShowFieldsDTO(String modelId) {

        List<FormGroupTmpl.FieldRule> fieldRuleList = getListFieldsByModelId(modelId);
        if(CollectionUtil.isEmpty(fieldRuleList)){
            return new ArrayList<>();
        }
        List<ConfigFieldsDTO> showFieldsDTOList = new ArrayList<>();
        for (FormGroupTmpl.FieldRule fieldRule : fieldRuleList) {
            //展示字段配置的表以及字段信息
            TbCdcdmMetadataTableInfo tableInfo = Optional.ofNullable(fieldRule.getTableInfo()).orElse(new TbCdcdmMetadataTableInfo());
            TbCdcdmMetadataTableColumnInfo columnInfo = Optional.ofNullable(fieldRule.getTableColumnInfo()).orElse(new TbCdcdmMetadataTableColumnInfo());
            ConfigFieldsDTO dto = ConfigFieldsDTO.builder()
                                                 .title(fieldRule.getListLabel())
                                                 .schema(tableInfo.getSchema())
                                                 .table(tableInfo.getTableName())
                                                 .tableId(tableInfo.getId())
                                                 .tableCode(tableInfo.getTableCode())
                                                 .field(columnInfo.getColumnName())
                                                 .fieldId(fieldRule.getField())
                                                 .fieldCode(columnInfo.getColumnCode())
                                                 .fieldType(columnInfo.getDataType())
                                                 .orderFlag(fieldRule.getListSort())
                                                 .isDisplay(fieldRule.isListDisplay() ? "1" : "0")
                                                 .desensitizedType(fieldRule.getDesensitizedType())
                                                 .build();
            showFieldsDTOList.add(dto);
        }
        return showFieldsDTOList;
    }

    /**
     * 根据配置的过滤 处理配置字段
     * */
    public List<ConfigFieldsDTO> buildConfigFilter(List<FieldsConfigAnalysisVO.FilterFields> filterFieldsList){
        if(CollectionUtil.isEmpty(filterFieldsList)){
            return new ArrayList<>();
        }
        //处理父类逻辑条件 只有一层
        FieldsConfigAnalysisVO.FilterFields logicCondition = filterFieldsList.stream()
                                                                             .filter(e -> StringUtils.isBlank(e.getParentItemId()) && Common.LOGIC.equals(e.getType()))
                                                                             .findFirst()
                                                                             .orElse(null);
        List<ConfigFieldsDTO> filterFieldsDTOList = new ArrayList<>();
        for (FieldsConfigAnalysisVO.FilterFields filterFields : filterFieldsList) {
            TbCdcdmMetadataTableColumnInfo columnInfo = tableInfoCache.getColumnInfoByCache(filterFields.getItem());
            if(columnInfo == null){
                continue;
            }
            ConfigFieldsDTO dto = ConfigFieldsDTO.builder()
                                                 .logic(logicCondition == null ? LogicEnum.OR : LogicEnum.getByName(logicCondition.getItem()))
                                                 .field(columnInfo.getColumnName())
                                                 .fieldCode(filterFields.getItem())
                                                 .operator(filterFields.getOperator())
                                                 .value(filterFields.getValue())
                                                 .build();
            if(!Objects.equals(Common.LOGIC, filterFields.getType())) {
                filterFieldsDTOList.add(dto);
            }
        }
        return filterFieldsDTOList;
    }

    public List<ConfigFieldsDTO> buildIdentifyConfig(List<FieldsConfigAnalysisVO.DimFields> dimFieldsList, String masterTable){

        if(CollectionUtil.isEmpty(dimFieldsList)){
            return new ArrayList<>();
        }
        List<ConfigFieldsDTO> dimFieldsDTOList = new ArrayList<>();
        for (FieldsConfigAnalysisVO.DimFields dimFields : dimFieldsList) {
            ConfigFieldsDTO dto = ConfigFieldsDTO.builder()
                                                 .title(dimFields.getTitle())
                                                 .table(dimFields.getTable())
                                                 .tableId(dimFields.getTableId())
                                                 .tableCode(dimFields.getTableCode())
                                                 .field(dimFields.getField())
                                                 .fieldId(dimFields.getFieldId())
                                                 .fieldCode(dimFields.getFieldCode())
                                                 .fieldType(dimFields.getFieldType())
                                                 .build();
            dimFieldsDTOList.add(dto);
        }
        dimFieldsDTOList = dimFieldsDTOList.stream().filter(e -> masterTable.equals(e.getTable())).collect(Collectors.toList());
        return dimFieldsDTOList;
    }

    /**
     * 根据具体 filterField 筛选条件过滤字段
     * */
    public List<ConfigFieldsDTO> getFieldsBy(DataModelDetailVO detail, String filterField){

        //查询配置表中该模型需要展示的字段，并处理
        Gson gson = new Gson();
        //解析配置字段
        FieldsConfigAnalysisVO analysisVO = gson.fromJson(detail.getFieldsConfig(), FieldsConfigAnalysisVO.class);
        switch (filterField){
            case Common.FIELD:
                //展示字段 从数据模型过滤需要展示的字段
                return this.buildShowFieldsDTO(detail.getModelId());

            case Common.MODEL_FILTER:
                //过滤条件
                List<FieldsConfigAnalysisVO.FilterFields> filterFieldsList = analysisVO.getFilterFields();
                return this.buildConfigFilter(filterFieldsList);

            case Common.IDENTIFY:
                //维度标识
                List<FieldsConfigAnalysisVO.DimFields> dimFieldsList = analysisVO.getDimFields();
                return this.buildIdentifyConfig(dimFieldsList, detail.getMasterTable());

            case Common.RETRIEVE_FILTER:
                //检索表过滤条件
                List<FieldsConfigAnalysisVO.FilterFields> retrieveFilterList = analysisVO.getRetrieveFilter();
                return this.buildConfigFilter(retrieveFilterList);

            default:
                return new ArrayList<>();
        }
    }

    /**
     * 查询唯一标识字段
     * */
    public Map<String, DataModelVO> getIdentifyFieldByRecordId(DataModelDetailVO detail, TbCdcdmMetadataTableColumnInfo columnInfo, List<String> recordIds){

        Map<String, DataModelVO> resMap = new HashMap<>();
        //配置表中的过滤条件
        List<ConfigFieldsDTO> identifyFlag = this.getFieldsBy(detail, Common.IDENTIFY);
        //过滤条件为唯一键
        ConfigFieldsDTO dto = identifyFlag.stream().findFirst().orElse(null);
        if(Objects.isNull(dto)){
            return null;
        }
        //找到该模型配置的过滤条件
        List<ConfigFieldsDTO> filterConditions = this.getFieldsBy(detail, Common.MODEL_FILTER);
        filterConditions.forEach(e -> {
            TbCdcdmMetadataTableColumnInfo info = tableInfoCache.getColumnInfoByCache(e.getFieldCode());
            e.setField(info.getColumnName());
            e.setFieldType(info.getDataType());
        });

        //查询唯一标识的表信息以及字段信息
        TbCdcdmMetadataTableInfo tableInfo = tableInfoCache.getTableInfoByCache(dto.getTableCode());
        TbCdcdmMetadataTableColumnInfo identifyColumn = tbCdcdmMetadataTableColumnInfoMapper.selectByTableAndBusiColName(dto.getTableCode(), dto.getField());

        String fullTableName = tableInfo.getSchema() + "." + tableInfo.getTableName();
        String sql = AnsiSqlBuilder.buildQueryIdentifySql(fullTableName, identifyColumn, columnInfo.getColumnName(), recordIds, filterConditions);

        List<Map<String, Object>> res = monitorCommonUtils.queryByTableSchema(tableInfo.getDataSourceKey(), tableInfo.getSchema(), sql);
        res.forEach(e -> {
            DataModelVO dataModelVO = new DataModelVO();
            dataModelVO.setIdentifyFieldTitle(dto.getTitle());
            String dateStr = String.valueOf(Optional.ofNullable(e.get(identifyColumn.getColumnName())).orElse(""));
            if(Common.TIMESTAMP.equals(dto.getFieldType()) && StringUtils.isNotBlank(dateStr)){
                dateStr = DateFormatUtil.parseDate(dateStr, DateFormatUtil.DATE_FORMAT, DateFormatUtil.DATE_FORMAT);
            }
            dataModelVO.setIdentifyFieldValue(dateStr);
            resMap.put(String.valueOf(e.get(columnInfo.getColumnName())), dataModelVO);
        });
        return resMap;
    }

    /**
     * 根据模型配置的展示字段进行处理
     * 时间格式的字段需要格式化
     * */
    private List<FieldVO> buildFieldVOList(Map<String, Object> record, Map<String, ConfigFieldsDTO> fieldsMap){

        List<FieldVO> fieldVOList = new ArrayList<>();
        Boolean flag = threadLocal.get().getNormalFlag();
        //以展示的字段为主，避免record中字段缺少问题
        for (Map.Entry<String, ConfigFieldsDTO> entry: fieldsMap.entrySet()){
            Object recordValue = record.get(entry.getKey());
            //字段匹配则构建新MedicalInfoVO对象
            FieldVO fieldVO = new FieldVO();
            ConfigFieldsDTO fieldsDTO = entry.getValue();
            fieldVO.setLabel(fieldsDTO.getTitle());
            fieldVO.setName(fieldsDTO.getField() == null ? null : StrUtil.toCamelCase(fieldsDTO.getField()));
            fieldVO.setOrderFlag(fieldsDTO.getOrderFlag());
            fieldVO.setIsDisplay(fieldsDTO.getIsDisplay());
            Object dealValue = recordValue;
            if(Common.TIMESTAMP.equals(fieldsDTO.getFieldType()) && Objects.nonNull(recordValue)){
                LocalDateTime dateTime = LocalDateTime.parse((String) recordValue);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateFormatUtil.FORMAT_DATE_HH_MM);
                dealValue = dateTime.format(formatter);
            }
            //字段回显脱敏
            if(flag && StringUtils.isNotBlank(fieldsDTO.getDesensitizedType()) && Objects.nonNull(recordValue)){
                SensitiveTypeEnum type = SensitiveTypeEnum.getByCode(fieldsDTO.getDesensitizedType());
                dealValue = type == null ? recordValue : DesensitizedUtils.desensitizedValue(type, recordValue.toString(), 0, 0, "*");
            }
            fieldVO.setValue(dealValue == null ? "--" : dealValue);
            fieldVOList.add(fieldVO);
        }
        fieldVOList = fieldVOList.stream().sorted(Comparator.comparing(FieldVO::getOrderFlag)).collect(Collectors.toList());
        return fieldVOList;
    }

    /***************************************************分割线******************************************************/

    //收藏夹数据导出 列表
    public List<MedicalInfoVO> getRecordList(SearchQueryDTO dto, List<String> recordIds, boolean isFilter, boolean isBusiness){

        List<MedicalInfoVO> result = new ArrayList<>();

        DataModelDetailVO detail = tbCdcdmDataFormTemplateMapper.getDataModelByModelId(dto.getModelId(), Common.VERSION_STATUS_PUBLISHED);
        if(null == detail){
            throw new RuntimeException("数据配置错误！");
        }
        //需要展示的字段
        List<ConfigFieldsDTO> configFieldsDTOS = this.getFieldsBy(detail, Common.FIELD);

        //数据模型配置
        TbCdcdmDataFormTemplate template = templateMapper.getModelConfigByModelId(dto.getModelId());
        //数据模型的主键
        TbCdcdmMetadataTableColumnInfo columnInfo = columnInfoMapper.selectByTableAndBusiColName(template.getMasterTableId(), template.getKeyField());
        //数据模型的业务主键
        String businessKey = template.getBusinessKey();
        String key = (businessKey != null && tableInfoCache.getColumnInfoByCache(businessKey) != null)
                ? tableInfoCache.getColumnInfoByCache(businessKey).getColumnName() : columnInfo.getColumnName();
        FilterParam filterParam = new FilterParam().put(key, recordIds);

        TbCdcdmMetadataTableInfo tableInfo = tableInfoCache.getTableInfoByCache(template.getRetrieveTable());
        if(tableInfo == null){
            return new ArrayList<>();
        }
        String sql = buildRetrieveQuerySql(template, tableInfo, filterParam);
        //根据拼接sql查询收藏数据
        List<Map<String, Object>> collectionData = monitorCommonUtils.queryByTableSchema(tableInfo.getDataSourceKey(), tableInfo.getSchema(), sql);
        List<String> jsonList = collectionData.stream()
                                              .map(map -> map != null ? map.get(Common.CONTENT_JSON) : null)
                                              .filter(Objects::nonNull)
                                              .map(Object::toString).collect(Collectors.toList());

        //需要展示的字段
        Map<String, ConfigFieldsDTO> fieldsMap = configFieldsDTOS.stream().collect(Collectors.toMap(ConfigFieldsDTO::getFieldId, a -> a));
        List<Map<String, Object>> dataFieldsMapList = new ArrayList<>();
        //解析json 处理需要展示的字段
        if(CollectionUtil.isNotEmpty(jsonList)) {
            jsonList.forEach(json -> {
                ObjectMapper mapper = new ObjectMapper();
                try {
                    JsonNode root = mapper.readTree(json);
                    Map<String, Object> fieldMap = new HashMap<>();
                    fieldsMap.forEach((k, v) -> getMapByTarget(root, k, fieldMap));
                    dataFieldsMapList.add(fieldMap);
                }catch (Exception e){
                    throw new RuntimeException();
                }
            });
            //根据每条记录展示字段的map 转换
            dataFieldsMapList.forEach(e -> {
                MedicalInfoVO medicalInfoVO = new MedicalInfoVO();
                medicalInfoVO.setRecordFieldMap(e);
                List<FieldVO> fieldVOList = buildFieldVOList(e, fieldsMap);
                medicalInfoVO.setFieldVOList(fieldVOList);
                result.add(medicalInfoVO);
            });
        }
        return result;
    }

    /**
     * 从jsonNode中取出 对应的 key-value
     * */
    private void getMapByTarget(JsonNode node, String targetKey, Map<String, Object> recordFieldMap){
        if (node.isObject()) {
            ObjectNode objectNode = (ObjectNode) node;
            objectNode.fieldNames().forEachRemaining(key -> {
                JsonNode childNode = objectNode.get(key);
                if (key.equals(targetKey)) {
                    //找到对应值后 put对应的key-value
                    recordFieldMap.put(targetKey, childNode.asText());
                } else {
                    getMapByTarget(childNode, targetKey, recordFieldMap);
                }
            });
        } else if (node.isArray()) {
            for (JsonNode arrayItem : node) {
                getMapByTarget(arrayItem, targetKey, recordFieldMap);
            }
        }
    }

    /**
     * 拼接检索表查询sql
     * */
    public String buildRetrieveQuerySql(TbCdcdmDataFormTemplate template,
                                        TbCdcdmMetadataTableInfo tableInfo,
                                        FilterParam filterParam){
        //查询检索表字段 根据表中字段对过滤条件进行进一步过滤
        FilterParam retrieveFilter = new FilterParam();

        Gson gson = new Gson();
        //解析配置字段
        FieldsConfigAnalysisVO analysisVO = gson.fromJson(template.getFieldsConfig(), FieldsConfigAnalysisVO.class);
        List<ConfigFieldsDTO> configFieldsDTOList = new ArrayList<>();
        if(analysisVO != null) {
            //查看该模型的检索表是否配置了过滤信息 有则需要将过滤条件拼接到sql中
            configFieldsDTOList = this.buildConfigFilter(analysisVO.getRetrieveFilter());
        }
        List<TbCdcdmMetadataTableColumnInfo> retrieveTableColumns = tableInfoCache.getTableColumnInfoByCache(template.getRetrieveTable());
        filterParam.getParam().forEach((k, v) -> {
            // 找到匹配的列
            Optional<TbCdcdmMetadataTableColumnInfo> matchedColumn = retrieveTableColumns.stream()
                    .filter(e -> e.getColumnName().equalsIgnoreCase(k) || e.getBusinessColumn().equalsIgnoreCase(k))
                    .findFirst();
            // 如果匹配到，使用 column_name 作为 key
            matchedColumn.ifPresent(column -> retrieveFilter.put(column.getColumnName(), v));
        });
        //前端传参被过滤完 -> 传参有问题直接返回空
        if(retrieveFilter.getParam().isEmpty()){
            return null;
        }
        //拼接检索表查询sql
        return AnsiSqlBuilder.buildRetrieveSearchSqlByFilter(tableInfo, retrieveFilter, configFieldsDTOList);
    }

    /**
     * 根据检索表配置 以及前端传参进行结果过滤 (查询检索表构造的大json)
     * */
    public String getModelDetailDataByRetrieveTable(TbCdcdmDataFormTemplate template,
                                                    FilterParam filterParam){

        TbCdcdmMetadataTableInfo tableInfo = tableInfoCache.getTableInfoByCache(template.getRetrieveTable());
        if(tableInfo == null){
            return null;
        }
        String sql = buildRetrieveQuerySql(template, tableInfo, filterParam);
        List<Map<String, Object>> result = monitorCommonUtils.queryByTableSchema(tableInfo.getDataSourceKey(), tableInfo.getSchema(), sql);

        return CollectionUtil.isNotEmpty(result) ? result.stream()
                                                         .map(map -> map != null ? map.get(Common.CONTENT_JSON) : null)
                                                         .filter(Objects::nonNull)
                                                         .findFirst()
                                                         .map(Object::toString)
                                                         .orElse(null) : null;
    }

    /**
     * 根据数据模型配置 查询数据并按照指定格式返回 (全景视图改版 -> 与大json返回格式一致)
     * */
    public String getDetailDataByModelAndFilter(String modelId,
                                                FilterParam filterParam){

        DataModel dataModel = monitorCommonUtils.buildDmEngineModel(cdcAdminService.getDataModelConfig(modelId));
        //拦截器判断当前用户的脱敏情况
        Boolean flag = threadLocal.get().getNormalFlag();
        //模型表单
        List<ModelForm> forms = dataModel.getFormList();
        //查询时，过滤掉模型中套其他模型的表单（嵌套模型由前端重新调用该接口）
        forms = forms.stream().filter(e -> StringUtils.isBlank(e.getQuoteModel())).collect(Collectors.toList());

        for (ModelForm modelForm : forms) {
            List<FormGroup> groups = modelForm.getGroups();
            Column formIdentityColumn = modelForm.getFormIdentityColumn();
            String modelFormId = modelForm.getModelFormId();
            // 将每个 FormGroup 与其对应的 Table 映射在一起
            Map<Table, List<FormGroup>> tableGroupMap = groups.stream()
                                                              .flatMap(group -> group.getTables().stream().map(table -> new AbstractMap.SimpleEntry<>(table, group)))
                                                              .collect(Collectors.groupingBy(Map.Entry::getKey, Collectors.mapping(Map.Entry::getValue, Collectors.toList())));

            // 遍历每个 Table 对应的 FormGroup
            for (Map.Entry<Table, List<FormGroup>> entry : tableGroupMap.entrySet()) {
                Table table = entry.getKey();
                List<FormGroup> formGroups = entry.getValue();
                // 找出与当前 Table 相关的所有 Column
                List<Column> columnList = formGroups.stream()
                                                    .map(FormGroup::getColumns)
                                                    .flatMap(List::stream)
                                                    .filter(e -> e.getTable() != null && Objects.equals(table.getTableId(), e.getTable().getTableId()))
                                                    .collect(Collectors.toList());
                // 查询数据
                List<Map<String, Object>> dataMapList = selectDataByFilter(table, columnList, filterParam);
                // 以表单唯一标识分组
                String identityFieldName = Optional.ofNullable(formIdentityColumn)
                                                   .map(Column::getFieldName)
                                                   .orElse(modelFormId);
                Map<Object, List<Map<String, Object>>> formIdentityDataMap = dataMapList.stream()
                                                                                        .filter(Objects::nonNull)
                                                                                        .collect(Collectors.groupingBy(row -> getFormIdentityValue(row, identityFieldName, modelFormId)));
                if (CollectionUtil.isNotEmpty(formIdentityDataMap)) {
                    Integer formMaxRepeatCnt = modelForm.getMaxRepeatCnt();
                    // 对于每个可重复表单，最多处理 maxRepeatCnt 次
                    for (Map.Entry<Object, List<Map<String, Object>>> identityEntry : formIdentityDataMap.entrySet()) {
                        if (formMaxRepeatCnt-- == 0) break;
                        Object identityValue = identityEntry.getKey();
                        List<Map<String, Object>> identityMapList = identityEntry.getValue();
                        // 获取或创建对应的表单值
                        ModelForm.FormValue formValue = modelForm.putValueIfAbsent(identityValue, new ModelForm.FormValue(modelFormId, identityValue));
                        // 处理每个 FormGroup 的数据
                        for (FormGroup formGroup : formGroups) {
                            List<Column> columns = formGroup.getColumns();
                            FormGroup.GroupValue groupValue = new FormGroup.GroupValue(formGroup.getId());
                            long groupMaxRepeatTimes = formGroup.getMaxRepeatTimes();
                            for (Map<String, Object> rowMap : identityMapList) {
                                if (groupMaxRepeatTimes-- == 0) break;
                                // 组装每个字段的值，支持脱敏
                                Map<String, Object> fieldValues = new HashMap<>();
                                for (Column column : columns) {
                                    Object value = rowMap.get(column.getFieldName());
                                    // 判断是否需要脱敏
                                    if (flag && StringUtils.isNotBlank(column.getDesensitizedType())) {
                                        SensitiveTypeEnum type = SensitiveTypeEnum.getByCode(column.getDesensitizedType());
                                        value = (type != null) ? DesensitizedUtils.desensitizedValue(type, String.valueOf(value), 1, 0, "*") : value;
                                    }
                                    fieldValues.put(column.getId(), value);
                                }
                                groupValue.getValues().add(fieldValues);
                            }
                            formValue.getGroupValues().add(groupValue);
                        }
                    }
                }
            }
        }

        Gson gson = new Gson();
        return gson.toJson(buildDataStruct(modelId, forms));
    }

    private Object getFormIdentityValue(Map<String, Object> row, String identityFieldName, String modelFormId) {
        if(Objects.nonNull(row)) {
            Object identityValue = row.getOrDefault(identityFieldName, modelFormId);
            if (identityValue instanceof Date) {
                return DateUtil.formatDate((Date) identityValue);
            }
            return identityValue;
        }
        return modelFormId;
    }

    private List<Map<String, Object>> selectDataByFilter(Table table,
                                                         List<Column> columnList,
                                                         FilterParam filterParam){
        if (CollectionUtil.isEmpty(columnList)) {
            return new ArrayList<>();
        }
        //处理前端段传入过滤条件
        Map<String, Object> filterMap = new HashMap<>();
        //查询该表的列
        List<TbCdcdmMetadataTableColumnInfo> columnInfoList = tableInfoCache.getTableColumnInfoByCache(table.getTableCode());
        //根据查询表中的字段信息过滤条件（添加容错）
        for (Map.Entry<String, Object> entry : filterParam.getParam().entrySet()) {
            columnInfoList.forEach(column -> {
                if(column.getBusinessColumn().equals(entry.getKey()) || column.getColumnName().equals(entry.getKey())){
                    filterMap.put(column.getColumnName(), entry.getValue());
                }
            });
        }
        //前端无过滤条件适配，直接返回空，不允许详情模块查全量数据
        if (filterMap.isEmpty()) {
            return new ArrayList<>();
        }
        //拼接sql
        String sql = AnsiSqlBuilder.buildSql(columnList, table, new FilterParam(filterMap));
        return monitorCommonUtils.queryByTableSchema(null, table.getSchema(), sql);
    }

    /**
     * 构建数据结构
     * */
    private Map<String, Map<String, List<Map<String, List<Map<String, Object>>>>>> buildDataStruct(String modelId,
                                                                                                   List<ModelForm> forms){
        Map<String, Map<String, List<Map<String, List<Map<String, Object>>>>>> result = new HashMap<>();
        // 处理 forms 数据，生成嵌套结构
        Map<String, List<Map<String, List<Map<String, Object>>>>> formMap = new HashMap<>();
        // 遍历每个 form
        forms.forEach(form -> {
            // 用于存储可重复表单数据
            List<Map<String, List<Map<String, Object>>>> multiFormList = new ArrayList<>();
            // 遍历 formValue
            form.getFormValues().forEach(formValue -> {
                // 处理 groupValues，并生成嵌套结构
                Map<String, List<Map<String, Object>>> groupMap = formValue.getGroupValues().stream()
                        .map(groupValue -> {
                            // 对每个 groupValue 生成 Map<String, List<Map<String, Object>>>，其中 key 是 groupId，value 是 groupValue 的 values
                            Map<String, List<Map<String, Object>>> groupMapEntry = new HashMap<>();
                            groupMapEntry.put(groupValue.getGroupId(), new ArrayList<>(groupValue.getValues()));
                            return groupMapEntry;
                        })
                        .flatMap(e -> e.entrySet().stream())
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                multiFormList.add(groupMap);
            });
            // 将 formValue 的 ModelFormId 作为 key，将对应的 multiFormList 放入 formMap
            formMap.put(form.getModelFormId(), multiFormList);
        });
        // 将 modelId 和 formMap 放入最终的 result Map
        result.put(modelId, formMap);

        return result;
    }

    /**
     * 组合搜索 通过传入的条件处理sql拼接
     * */
    public List<ConfigFieldsDTO> buildInputFilterListBy(List<CombinationConditionDTO> conditionDTOList){

        if(CollectionUtil.isEmpty(conditionDTOList)){
            return new ArrayList<>();
        }
        //最外层组与组之间的关系节点
        CombinationConditionDTO searchDTO = conditionDTOList.stream()
                .filter(e -> StringUtils.isBlank(e.getParentItemId()))
                .findFirst()
                .orElse(null);
        //缺少最外层节点之间关系直接返回
        if(searchDTO == null){
            throw new RuntimeException("条件组间关系不明确，无法查询");
        }
        LogicEnum logicType = LogicEnum.getByName(searchDTO.getLogicType());
        if(LogicEnum.NONE.equals(logicType)){
            throw new RuntimeException("条件组间关系不明确，无法查询");
        }
        List<ConfigFieldsDTO> fieldsDTOS = new ArrayList<>();
        conditionDTOList.forEach(e -> {
            if(Common.CONDITION.equals(e.getType())) {
                ConfigFieldsDTO dto = CombinationConditionDTO.convertToConfigField(e);
                dto.setLogic(logicType);
                fieldsDTOS.add(dto);
            }
        });
        return fieldsDTOS;
    }

    /**
     * 解析json 并处理字段的脱敏情况
     * */
    public String desensitizationJson(String modelId, String jsonStr){

        if(StringUtils.isBlank(jsonStr)) {
            return null;
        }
        //获取数据模型中 配置的脱敏字段
        List<ModelFieldsConfig> fieldsConfig = getDesensitizationFieldsBy(modelId);
        //解析json 并根据字段的key 以及 脱敏类型，对大json中对应的字段进行脱敏
        ObjectMapper root = new ObjectMapper();
        try {
            JsonNode jsonNode = root.readTree(jsonStr);
            fieldsConfig.forEach(e -> desensitizationFieldBy(jsonNode, e.getKey(), e.getDesensitizationType()));
            return root.writeValueAsString(jsonNode);
        } catch (Exception e) {
            log.error("json脱敏失败 ", e);
        }
        return null;
    }

    /**
     * 获取模型中配置的 需要脱敏字段的信息以及展示字段的信息
     * */
    private List<ModelFieldsConfig> getDesensitizationFieldsBy(String modelId){

        List<ModelFieldsConfig> fieldsConfig = new ArrayList<>();
        //根据模型id获取模型表单配置
        DataModel dataModel = monitorCommonUtils.buildDmEngineModel(cdcAdminService.getDataModelConfig(modelId));

        List<ModelForm> forms = dataModel.getFormList();
        //查询时，过滤掉模型中套其他模型的表单（嵌套模型由前端重新调用该接口）
        forms = forms.stream().filter(e -> StringUtils.isBlank(e.getQuoteModel())).collect(Collectors.toList());

        for (ModelForm modelForm : forms) {
            //遍历组
            modelForm.getGroups().forEach(group -> {
                //遍历组内字段
                group.getColumns().forEach(e -> {
                    //字段脱敏配置非空时 存储为脱敏字段
                    if(e.getDesensitizedType() != null){
                        ModelFieldsConfig config = new ModelFieldsConfig();
                        config.setKey(e.getId());
                        config.setDesensitizationType(e.getDesensitizedType());
                        fieldsConfig.add(config);
                    }
                });
            });
        }
        return fieldsConfig;
    }

    /**
     * 递归找json 中对应的key
     * 将其value按照对应的配置脱敏
     * */
    private void desensitizationFieldBy(JsonNode node, String targetKey, String desensitizationType) {
        if (node.isObject()) {
            ObjectNode objectNode = (ObjectNode) node;
            objectNode.fieldNames().forEachRemaining(key -> {
                JsonNode childNode = objectNode.get(key);
                if (key.equals(targetKey)) {
                    //找到对应值后 按照给定规则脱敏
                    String value = childNode.asText();
                    SensitiveTypeEnum type = SensitiveTypeEnum.getByCode(desensitizationType);
                    //判断该用户是否需要脱敏
                    if(threadLocal.get().getNormalFlag()) {
                        value = (type != null) ? DesensitizedUtils.desensitizedValue(type, String.valueOf(value), 1, 0, "*") : value;
                        objectNode.put(key, value);
                    }
                } else {
                    desensitizationFieldBy(childNode, targetKey, desensitizationType);
                }
            });
        } else if (node.isArray()) {
            for (JsonNode arrayItem : node) {
                desensitizationFieldBy(arrayItem, targetKey, desensitizationType);
            }
        }
    }

}
