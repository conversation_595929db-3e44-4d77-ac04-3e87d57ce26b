package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.province.entity.ads.AdsMsMedicalInfo;
import com.iflytek.cdc.province.model.dto.MsMedicalQuery;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsMedicalVisitStatVO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.vo.VisitPersonInfoVO;

import java.util.List;

public interface AdsMsMedicalService {

    /**
     * 就诊人次概况
     */
    MsMedicalVisitStatVO medicalVisitStat(MsMedicalQuery query, String loginUserName);

    /**
     * 门诊就诊人次统计
     */
    List<MsMedicalVisitStatVO> visitStatLineChart(MsMedicalQuery queryParam, String loginUserName);

    /**
     * 查询病历信息
     */
    List<AdsMsMedicalInfo> listMsMedicalInfo(MsMedicalQuery queryParam);

    /**
     * 就诊人信息概览
     * */
    PageInfo<VisitPersonInfoVO> visitPersonDetail(MsMedicalQuery query);

    /**
     * 就诊人信息概览 导出
     * */
    TbCdcmrExportTask visitPersonDetailExport(MsMedicalQuery query);

}
