package com.iflytek.cdc.province.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.utils.DataUtils;
import com.iflytek.cdc.province.entity.dim.AdsMonitorClueInfo;
import com.iflytek.cdc.province.enums.MultichannelTopicConfigEnum;
import com.iflytek.cdc.province.mapper.pg.AdsDsDrugStatMapper;
import com.iflytek.cdc.province.mapper.pg.AdsMonitorClueInfoMapper;
import com.iflytek.cdc.province.mapper.pg.AdsPoIndexInfoMapper;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ClueDrugSalesQueryDto;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ClueIndexQueryDto;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ClueMonitorQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.DrugSalesQueryDto;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.AdsPoIndexDailyVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.DrugDailySalesVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorClueInfoVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.AdsPoIndexInfoVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.TbCdcmrMultichannelTopicConfig;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.TopicConfigInfoVO;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.service.CooMonitorClueService;
import com.iflytek.cdc.province.service.MultichannelUtilsService;
import com.iflytek.cdc.province.utils.DateUtils;
import com.iflytek.cdc.province.utils.ListsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CooMonitorClueServiceImpl implements CooMonitorClueService {

    @Resource
    private AdsDsDrugStatMapper adsDsDrugStatMapper;

    @Resource
    private AdsPoIndexInfoMapper adsPoIndexInfoMapper;

    @Resource
    private AdsMonitorClueInfoMapper adsMonitorClueInfoMapper;

    @Resource
    private MultichannelUtilsService multichannelUtilsService;

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Override
    public List<String> listClueSources() {
        return adsMonitorClueInfoMapper.selectClueSources();
    }

    @Override
    public PageInfo<MonitorClueInfoVO> clueSearch(ClueMonitorQueryDTO queryDTO) {
        //设置区域查询参数
        CommonUtilService.setAreaQueryDTO(queryDTO);
        //获取多渠道专题信息
        TopicConfigInfoVO configInfoVO = adminServiceApi.getTopicConfig(queryDTO.getTopicId());
        if (configInfoVO != null) {
            //Topic配置信息关键词和药品影响线索查询
            List<String> topicKeywords = configInfoVO.getTopicConfigMap()
                    .getOrDefault(MultichannelTopicConfigEnum.KEYWORDS.getCode(), new ArrayList<>())
                    .stream()
                    .map(TbCdcmrMultichannelTopicConfig::getDataName)
                    .collect(Collectors.toList());
            List<String> topicDrugs = configInfoVO.getTopicConfigMap().getOrDefault(MultichannelTopicConfigEnum.DRUGS.getCode(), new ArrayList<>())
                    .stream().map(TbCdcmrMultichannelTopicConfig::getDataName).collect(Collectors.toList());
            topicKeywords.addAll(topicDrugs);
            queryDTO.setTopicKeywords(topicKeywords);
        }
        try (Page<?> ignored = PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize())) {
            return new PageInfo<>(adsMonitorClueInfoMapper.clueSearch(queryDTO));
        }
    }

    @Override
    public List<DrugDailySalesVO> drugSalesDetail(ClueDrugSalesQueryDto queryDTO) {
        AdsMonitorClueInfo clueInfo = adsMonitorClueInfoMapper.selectByClueId(queryDTO.getClueId(), queryDTO.getType());
        Date startDate = DateUtil.offsetDay(clueInfo.getDay(), -7);
        Date endDate = DateUtils.min(DateUtil.offsetDay(clueInfo.getDay(), 7), new Date());
        queryDTO.mergeClueParam(clueInfo, startDate, endDate);

        //设置区域查询参数
        CommonUtilService.setAreaQueryDTO(queryDTO);
        //获取多渠道专题信息
        TopicConfigInfoVO configInfoVO = adminServiceApi.getTopicConfig(queryDTO.getTopicId());
        if (configInfoVO != null) {
            //设置关键词以及药品查询参数
            multichannelUtilsService.setTopicKeysAndDrugsQuery(queryDTO,
                    configInfoVO,
                    queryDTO.getKeyword() == null ? null : t -> Lists.newArrayList(t.getKeyword()),
                    DrugSalesQueryDto::setTopicKeywords,
                    queryDTO.getKeyword() == null ? null : t -> Lists.newArrayList(t.getKeyword()),
                    DrugSalesQueryDto::setTopicDrugNames);
        }
        List<DrugDailySalesVO> drugDailySalesVOS = adsDsDrugStatMapper.drugDailySales(queryDTO);
        List<Date> dates = DateUtils.getDatesBetween(startDate, endDate);
        return ListsUtils.expandList(drugDailySalesVOS, dates, DrugDailySalesVO::getDay, day -> new DrugDailySalesVO(queryDTO.getKeyword(), day));
    }

    @Override
    public AdsPoIndexInfoVO searchIndexDetail(ClueIndexQueryDto queryDTO) {
        AdsMonitorClueInfo clueInfo = adsMonitorClueInfoMapper.selectByClueId(queryDTO.getClueId(), queryDTO.getType());
        Date startDate = DateUtil.offsetDay(clueInfo.getDay(), -7);
        Date endDate = DateUtils.min(DateUtil.offsetDay(clueInfo.getDay(), 7), new Date());
        queryDTO.mergeClueParam(clueInfo, startDate, endDate);
        // 设置区域查询参数
        CommonUtilService.setAreaQueryDTO(queryDTO);
        if (StrUtil.isBlank(queryDTO.getIndexClass())) {
            queryDTO.setIndexClass("搜索指数");
        }

        //获取多渠道专题信息
        TopicConfigInfoVO configInfoVO = adminServiceApi.getTopicConfig(queryDTO.getTopicId());
        if (configInfoVO != null) {
            //设置关键词以及药品查询参数
            multichannelUtilsService.setTopicKeysAndDrugsQuery(queryDTO,
                    configInfoVO,
                    queryDTO.getKeyword() == null ? null : t -> Lists.newArrayList(t.getKeyword()),
                    ClueIndexQueryDto::setTopicKeywords,
                    null,
                    ClueIndexQueryDto::setTopicDrugNames);
        }


        AdsPoIndexInfoVO adsPoIndexInfoVO = new AdsPoIndexInfoVO();
        // 当期+当期的同期
        List<AdsPoIndexDailyVO> dailyList = adsPoIndexInfoMapper.dailyIndexWithLastYear(queryDTO);

        int ttlIndex = dailyList.stream().mapToInt(AdsPoIndexDailyVO::getIndexValue).sum();
        int ttlLyIndex = dailyList.stream().mapToInt(AdsPoIndexDailyVO::getIndexLyValue).sum();
        double growthRate = DataUtils.getGrowthRate(ttlLyIndex, ttlIndex);
        adsPoIndexInfoVO.setTtlIndexYoy(growthRate);

        // 上期+上期的同期
        int gapDays = DateUtils.getGapDays(queryDTO.getStartDate(), queryDTO.getEndDate());
        queryDTO.setStartDate(DateUtils.prePeriodDate(queryDTO.getStartDate(), gapDays));
        queryDTO.setEndDate(DateUtils.prePeriodDate(queryDTO.getEndDate(), gapDays));
        List<AdsPoIndexDailyVO> lyDailyList = adsPoIndexInfoMapper.dailyIndexWithLastYear(queryDTO);
        int lastTtlIndex = lyDailyList.stream().mapToInt(AdsPoIndexDailyVO::getIndexValue).sum();
        adsPoIndexInfoVO.setTtlIndexMom(DataUtils.getGrowthRate(lastTtlIndex, ttlIndex));

        List<Date> dates = DateUtils.getDatesBetween(startDate, endDate);
        List<AdsPoIndexDailyVO> expandList = ListsUtils.expandList(dailyList,
                dates,
                AdsPoIndexDailyVO::getDay,
                t -> new AdsPoIndexDailyVO(queryDTO.getKeyword(), t));
        adsPoIndexInfoVO.setDailyList(expandList);

        return adsPoIndexInfoVO;
    }

    @Override
    public AdsPoIndexInfoVO consultIndexDetail(ClueIndexQueryDto queryDTO) {
        AdsMonitorClueInfo clueInfo = adsMonitorClueInfoMapper.selectByClueId(queryDTO.getClueId(), queryDTO.getType());
        Date startDate = DateUtil.offsetDay(clueInfo.getDay(), -7);
        Date endDate = DateUtils.min(DateUtil.offsetDay(clueInfo.getDay(), 7), new Date());
        queryDTO.mergeClueParam(clueInfo, startDate, endDate);

        //设置区域查询参数
        CommonUtilService.setAreaQueryDTO(queryDTO);
        if (StrUtil.isBlank(queryDTO.getIndexClass())) {
            queryDTO.setIndexClass("资讯指数");
        }

        //获取多渠道专题信息
        TopicConfigInfoVO configInfoVO = adminServiceApi.getTopicConfig(queryDTO.getTopicId());
        if (configInfoVO != null) {
            //设置关键词以及药品查询参数
            multichannelUtilsService.setTopicKeysAndDrugsQuery(queryDTO,
                    configInfoVO,
                    queryDTO.getKeyword() == null ? null : t -> Lists.newArrayList(t.getKeyword()),
                    ClueIndexQueryDto::setTopicKeywords,
                    null,
                    ClueIndexQueryDto::setTopicDrugNames);
        }

        AdsPoIndexInfoVO consultIndexInfoVO = new AdsPoIndexInfoVO();
        // 当期+当期的同期
        List<AdsPoIndexDailyVO> dailyList = adsPoIndexInfoMapper.dailyIndexWithLastYear(queryDTO);

        int ttlIndex = dailyList.stream().mapToInt(AdsPoIndexDailyVO::getIndexValue).sum();
        int ttlLyIndex = dailyList.stream().mapToInt(AdsPoIndexDailyVO::getIndexLyValue).sum();
        double growthRate = DataUtils.getGrowthRate(ttlLyIndex, ttlIndex);
        consultIndexInfoVO.setTtlIndexYoy(growthRate);

        // 上期+上期的同期
        int gapDays = DateUtils.getGapDays(queryDTO.getStartDate(), queryDTO.getEndDate());
        queryDTO.setStartDate(DateUtils.prePeriodDate(queryDTO.getStartDate(), gapDays));
        queryDTO.setEndDate(DateUtils.prePeriodDate(queryDTO.getEndDate(), gapDays));
        List<AdsPoIndexDailyVO> lyDailyList = adsPoIndexInfoMapper.dailyIndexWithLastYear(queryDTO);
        int lastTtlIndex = lyDailyList.stream().mapToInt(AdsPoIndexDailyVO::getIndexValue).sum();
        consultIndexInfoVO.setTtlIndexMom(DataUtils.getGrowthRate(lastTtlIndex, ttlIndex));

        List<Date> dates = DateUtils.getDatesBetween(startDate, endDate);
        List<AdsPoIndexDailyVO> expandList = ListsUtils.expandList(dailyList,
                dates,
                AdsPoIndexDailyVO::getDay,
                t -> new AdsPoIndexDailyVO(queryDTO.getKeyword(), t));
        consultIndexInfoVO.setDailyList(expandList);

        return consultIndexInfoVO;
    }
}
