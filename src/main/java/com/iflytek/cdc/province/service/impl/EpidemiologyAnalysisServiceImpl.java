package com.iflytek.cdc.province.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.province.mapper.pg.EpidemiologyAnalysisMapper;
import com.iflytek.cdc.province.model.epidemiology.CritiaclDeathProcessVO;
import com.iflytek.cdc.province.model.epidemiology.PatientVisitSituationVO;
import com.iflytek.cdc.province.model.pathogen.EpidemiologyReqDTO;
import com.iflytek.cdc.province.service.EpidemiologyAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class EpidemiologyAnalysisServiceImpl implements EpidemiologyAnalysisService {

    private static final String ADMISS_FLAG = "admiss";

    private static final String LEAVE_FLAG = "leave";


    @Resource
    private EpidemiologyAnalysisMapper epidemiologyAnalysisMapper;


    /**
     * 患者就诊情况
     */
    @Override
    public List<PatientVisitSituationVO> patientVisitSituationChart(EpidemiologyReqDTO reqDTO) {

        return epidemiologyAnalysisMapper.groupPatientVisitType(reqDTO);
    }


    /**
     * 患者就诊科室分布图
     */
    @Override
    public List<PatientVisitSituationVO> patientVisitDeptChart(EpidemiologyReqDTO reqDTO) {
        return epidemiologyAnalysisMapper.groupPatientVisitDept(reqDTO);
    }

    /**
     * 入院患者就诊情况
     */
    @Override
    public PatientVisitSituationVO admissPatientVisitChart(EpidemiologyReqDTO reqDTO) {
        reqDTO.setInOutFlag(ADMISS_FLAG);
        PatientVisitSituationVO result = epidemiologyAnalysisMapper.groupOutcomeStatus(reqDTO);
        result.calculateAllRates();
        return result;
    }

    /**
     * 出院患者就诊情况
     */
    @Override
    public PatientVisitSituationVO leavePatientVisitChart(EpidemiologyReqDTO reqDTO) {
        reqDTO.setInOutFlag(LEAVE_FLAG);
        PatientVisitSituationVO result = epidemiologyAnalysisMapper.groupOutcomeStatus(reqDTO);
        result.calculateAllRates();
        return result;
    }

    /**
     * 转重症死亡病例分页列表
     */
    @Override
    public PageInfo<CritiaclDeathProcessVO> pageCritiaclDeathProcess(EpidemiologyReqDTO reqDTO) {
        PageHelper.startPage(reqDTO.getPageIndex(), reqDTO.getPageSize());
        List<CritiaclDeathProcessVO> voList = epidemiologyAnalysisMapper.selectCritiaclDeathProcessBy(reqDTO);
        return new PageInfo<>(voList);
    }
}

