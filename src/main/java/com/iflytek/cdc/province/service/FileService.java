package com.iflytek.cdc.province.service;

import com.iflytek.cdc.edr.entity.TbCdcAttachment;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件服务类
 * <AUTHOR>
 */
public interface FileService {

    /**
     * 文件上传-单文件
     * @param file
     * @return
     */
    List<String> upload(MultipartFile file);

    /**
     * 文件上传
     * @param bytes
     * @param fileName
     * @return
     */
    String upload(byte[] bytes, String fileName);
    TbCdcAttachment uploadFile(byte[] bytes, String fileName);

    /**
     * 文件上传-多文件
     * @param files
     * @return
     */
    List<String> uploads(MultipartFile[] files);



    /**
     * 存储流媒体文件到文件服务器
     * @param fileUrl
     * @return
     */
    String storeMediaToFileServer(String fileUrl);

}
