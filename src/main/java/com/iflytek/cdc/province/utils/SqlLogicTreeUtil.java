package com.iflytek.cdc.province.utils;

import com.alibaba.druid.sql.visitor.functions.Right;
import com.google.gson.Gson;
import com.iflytek.cdc.edr.common.Common;
import com.iflytek.cdc.edr.enums.LogicEnum;
import com.iflytek.cdc.province.model.dto.logic.SqlLogicTree;
import com.iflytek.cdc.province.model.dto.logic.SqlLogicTreeNode;
import com.iflytek.cdc.province.model.vo.FieldsConfigAnalysisVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class SqlLogicTreeUtil {

    private static final String LEFT = " ( ";

    private static final String RIGHT = " ) ";

    /**
     * 将过滤字段的自定义条件转成树节点
     * */
    public List<SqlLogicTreeNode> transFilterFieldsTo(List<FieldsConfigAnalysisVO.FilterFields> filterFieldsList){

        List<SqlLogicTreeNode> treeNodeList  = new ArrayList<>();
        filterFieldsList.forEach(e -> {
            SqlLogicTreeNode treeNode = SqlLogicTreeNode.builder()
                                                  .id(e.getItemId())
                                                  .parentId(e.getParentItemId())
                                                  .type(e.getType())
                                                  .operator(e.getOperator())
                                                  .field(e.getItem())
                                                  .fieldType(e.getFieldType())
                                                  .value(e.getValue())
                                                  .build();
            treeNodeList.add(treeNode);
        });
        return treeNodeList;
    }

    /**
     * 自定义的过滤条件转逻辑树 -> 根据树生成对应的sql
     * */
    public void generateConditionSqlBy(StringBuilder filterSql, SqlLogicTree sqlLogicTree){

        //获取根节点的逻辑处理类型
        LogicEnum logic = LogicEnum.getByName(sqlLogicTree.getRoot().getField());
        filterSql.append(LEFT);
        Map<String, SqlLogicTreeNode> currChildNode = sqlLogicTree.getRoot().getChildren();
        Iterator<Map.Entry<String, SqlLogicTreeNode>> iterator = currChildNode.entrySet().iterator();
        while (iterator.hasNext()){
            Map.Entry<String, SqlLogicTreeNode> entry = iterator.next();
            SqlLogicTreeNode node = entry.getValue();
            if(Objects.equals(Common.LOGIC, node.getType())){
                generateConditionSqlBy(filterSql, new SqlLogicTree(node));
            }else{
                String sql = node.buildKeyValue();
                filterSql.append(sql);
                if(iterator.hasNext()) {
                    filterSql.append(logic.getCode());
                }
            }
        }
        filterSql.append(RIGHT);
    }

    public static void main(String[] args) {

        Gson gson = new Gson();
        StringBuilder stringBuilder = new StringBuilder();
        String config = "{\"filterFields\":[{\"itemId\":\"1\",\"type\":\"condition\",\"item\":\"202271\",\"operator\":\"=\",\"value\":\"investedTask\",\"parentItemId\":\"2\",\"unit\":\"\"},{\"itemId\":\"2\",\"type\":\"logic\",\"item\":\"或\",\"operator\":\"\",\"code\":\"\",\"value\":\"\",\"parentItemId\":\"\"},{\"itemId\":\"3\",\"type\":\"condition\",\"item\":\"2321\",\"operator\":\"=\",\"code\":\"\",\"value\":\"111\",\"parentItemId\":\"2\"},{\"itemId\":\"4\",\"type\":\"logic\",\"item\":\"且\",\"operator\":\"\",\"code\":\"\",\"value\":\"\",\"parentItemId\":\"2\"},{\"itemId\":\"5\",\"type\":\"condition\",\"item\":\"0202020\",\"operator\":\"=\",\"code\":\"\",\"value\":\"101010\",\"parentItemId\":\"4\"},{\"itemId\":\"6\",\"type\":\"condition\",\"item\":\"0202021\",\"operator\":\"=\",\"code\":\"\",\"value\":\"101011\",\"parentItemId\":\"4\"}]}";
        FieldsConfigAnalysisVO analysisVO = gson.fromJson(config, FieldsConfigAnalysisVO.class);

        List<FieldsConfigAnalysisVO.FilterFields> filterFields = analysisVO.getFilterFields();
//        SqlLogicTree sqlLogicTree = SqlLogicTree.buildLogicTree(transFilterFieldsTo(filterFields));
//        generateConditionSqlBy(stringBuilder, sqlLogicTree);
//        System.out.println(stringBuilder.toString());
    }
}
