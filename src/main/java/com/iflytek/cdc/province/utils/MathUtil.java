package com.iflytek.cdc.province.utils;

import cn.hutool.core.util.NumberUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class MathUtil {

    public static final String ZERO_RATE = "0.00";

    private MathUtil() {
    }

    public static Double div(Number div1, Number div2) {
        if (null == div1 || null == div2 || 0.0 == div1.doubleValue()) {
            return 0.0;
        } else if (0.0 == div2.doubleValue()) {
            return null;
        }
        return NumberUtil.div(div1, div2).doubleValue();
    }

    public static Double div(Number div1, Number div2, int roundScale) {
        if (null == div2 || 0.0 == div2.doubleValue()) {
            return null;
        } else if (null == div1 || 0.0 == div1.doubleValue()) {
            return 0.0;
        }
        return NumberUtil.div(div1, div2).setScale(roundScale, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 转换成百分比
     */
    public static String percent(Double input) {
        if (input == null) {
            return null;
        }
        DecimalFormat decimalFormat = new DecimalFormat("0.##%");
        return decimalFormat.format(input);
    }

    /**
     * 转换成百分比
     */
    public static String percent(Integer count, Integer total) {
        return percent(div(transData(count), transData(total)));
    }

    /**
     * 累计
     */
    public static int sum(Integer count, Integer total) {
        return transData(count) + transData(total);
    }

    /**
     * 计算同期 乘100保留2位小数
     * @param currentCount  今年数量
     * @param lastCount  去年数量
     * @return
     */
    public static Double getPeriodRate(Long currentCount, Long lastCount) {
        return (lastCount == null || lastCount == 0) ? 1d : NumberUtil.div((double) currentCount, (double)lastCount, 2) ;
    }

    /**
     * 计算占比  乘100保留2位小数
     * @param count  数量
     * @param totalCount 总数
     * @return
     */
    public static String getRate(Long count, Long totalCount) {

        return BigDecimal.ZERO.equals(new BigDecimal(totalCount)) ? ZERO_RATE : new BigDecimal(count).multiply(BigDecimal.TEN).multiply(BigDecimal.TEN).divide(new BigDecimal(totalCount), 2, BigDecimal.ROUND_HALF_UP).toString() ;
    }

    /**
     * 计算同比增长
     * (本期数值-去年同期数值) / 去年同期数值 * 100%
     * */
    public static String getGrowthRateStr(Integer last, Integer cur){
        return percent(getGrowthRate(last, cur));
    }

    /**
     * 计算同比增长
     * (本期数值-去年同期数值) / 去年同期数值 * 100%
     * */
    public static double getGrowthRate(Integer last, Integer cur){

        if (transData(last) == 0 && transData(cur) == 0){
            return 0.0;
        }
        if (transData(last) == 0 && transData(cur) != 0){
            return 1.0;
        }
        return NumberUtil.div(transData(cur) - transData(last), transData(last), 4);
    }

    private static int transData(Integer n){
        return Optional.ofNullable(n).orElse(0);
    }

    /**
     * 计算三个数据的平均值
     */
    public static List<Integer> calculateAverages(List<Integer> list1, List<Integer> list2, List<Integer> list3) {
        List<Integer> averages = new ArrayList<>();

        // 获取三个列表的最大长度
        int maxLength = Math.max(list1.size(), Math.max(list2.size(), list3.size()));

        for (int i = 0; i < maxLength; i++) {
            int sum = 0;
            int count = 0;

            // 检查每个列表的当前索引是否有效
            if (i < list1.size()) {
                sum += list1.get(i);
                count++;
            }
            if (i < list2.size()) {
                sum += list2.get(i);
                count++;
            }
            if (i < list3.size()) {
                sum += list3.get(i);
                count++;
            }

            // 计算平均值并向上取整
            double average = count == 0 ? 0 : (double) sum / count;
            int ceilAverage = (int) Math.ceil(average);
            averages.add(ceilAverage);
        }

        return averages;
    }

}
