package com.iflytek.cdc.province.utils;

import com.iflytek.cdc.province.model.multichannelMonitor.dto.AgeRange;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.GroupSumIntVO;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class AgeUtil {
    public static final List<AgeRange> SCHOOL_AGE_RANGE;//学龄分段

    public static final List<AgeRange> AVG_AGE_RANGE;//平均分段，10岁 一分段

    public static final List<AgeRange> ALGORITHM_RANGE;//算法侧分段

    public static final List<AgeRange> PATHOGEN_POSITIVE_RANGE ;//病原谱阳性对比率分组

    public static final List<AgeRange> RESPIRATORY_INFECTED_RANGE ;//呼吸道传染病年龄分布

    static {
        List<AgeRange> schoolRange = new ArrayList<>();
        schoolRange.add(new AgeRange("0-3", 0 ,3));
        schoolRange.add(new AgeRange("4-6", 4 ,6));
        schoolRange.add(new AgeRange("7-12",7 ,12));
        schoolRange.add(new AgeRange("13-15", 13 ,15));
        schoolRange.add(new AgeRange("16-19", 16 ,19));
        schoolRange.add(new AgeRange("20-25", 20 ,25));
        schoolRange.add(new AgeRange("26-70", 26 ,70));
        schoolRange.add(new AgeRange(">70", 71 ,999));
        SCHOOL_AGE_RANGE = schoolRange;
        
        List<AgeRange> avgRange = new ArrayList<>();
        avgRange.add(new AgeRange("0-10", 0 ,10));
        avgRange.add(new AgeRange("11-20", 11 ,20));
        avgRange.add(new AgeRange("21-30", 21 ,30));
        avgRange.add(new AgeRange("31-40", 31 ,40));
        avgRange.add(new AgeRange("41-50", 41 ,50));
        avgRange.add(new AgeRange("51-60", 51 ,60));
        avgRange.add(new AgeRange("61-70", 61 ,70));
        avgRange.add(new AgeRange(">70", 71 ,999));
        AVG_AGE_RANGE = avgRange;

        List<AgeRange> algorithmRange = new ArrayList<>();
        algorithmRange.add(new AgeRange("0-3", 0, 4));
        algorithmRange.add(new AgeRange("4-6", 4, 7));
        algorithmRange.add(new AgeRange("7-18", 7, 19));
        algorithmRange.add(new AgeRange("19-60", 19, 60));
        algorithmRange.add(new AgeRange(">60", 61, 999));
        ALGORITHM_RANGE = algorithmRange;

        List<AgeRange> pathogenRange = new ArrayList<>();
        pathogenRange.add(new AgeRange("<28天", 0, 0.0768));
        pathogenRange.add(new AgeRange("28天~1岁", 0.0768, 2));
        pathogenRange.add(new AgeRange("1~4", 2, 5));
        pathogenRange.add(new AgeRange("5~9", 5, 10));
        pathogenRange.add(new AgeRange("10~14", 10, 15));
        pathogenRange.add(new AgeRange("15~19", 15, 20));
        pathogenRange.add(new AgeRange("20~24", 20, 25));
        pathogenRange.add(new AgeRange("25~29", 25, 30));
        pathogenRange.add(new AgeRange("30~34", 30, 35));
        pathogenRange.add(new AgeRange("35~39", 35, 40));
        pathogenRange.add(new AgeRange("40~44", 40, 45));
        pathogenRange.add(new AgeRange("45~49", 45, 50));
        pathogenRange.add(new AgeRange("50~54", 50, 55));
        pathogenRange.add(new AgeRange("55~59", 55, 60));
        pathogenRange.add(new AgeRange("60~64", 60, 65));
        pathogenRange.add(new AgeRange("65~69", 65, 70));
        pathogenRange.add(new AgeRange("70~74", 70, 75));
        pathogenRange.add(new AgeRange("75~79", 75, 80));
        pathogenRange.add(new AgeRange("80~84", 80, 85));
        pathogenRange.add(new AgeRange("85~89", 85, 90));
        PATHOGEN_POSITIVE_RANGE = pathogenRange;

        List<AgeRange> respiratoryInfectedRange = new ArrayList<>();
        respiratoryInfectedRange.add(new AgeRange("<10岁", 0, 10));
        respiratoryInfectedRange.add(new AgeRange("10~20岁", 10, 20));
        respiratoryInfectedRange.add(new AgeRange("20~30岁", 20, 30));
        respiratoryInfectedRange.add(new AgeRange("30~40岁", 30, 40));
        respiratoryInfectedRange.add(new AgeRange("40~50岁", 40, 50));
        respiratoryInfectedRange.add(new AgeRange("50~60岁", 50, 60));
        respiratoryInfectedRange.add(new AgeRange("60~70岁", 60, 70));
        respiratoryInfectedRange.add(new AgeRange("≥70岁", 70, 999));
        RESPIRATORY_INFECTED_RANGE = respiratoryInfectedRange;
    }

    /**
     * 年龄分组
     */
    public static List<GroupSumIntVO> groupAgeSum(List<Double> ages, List<AgeRange> ageRanges) {
        List<GroupSumIntVO> collect = ageRanges.stream().map(ageRange -> {
            GroupSumIntVO groupSumIntVO = new GroupSumIntVO();
            groupSumIntVO.setName(ageRange.getDesc());
            groupSumIntVO.setValue((int) ages.stream().filter(age -> age != null
                    && age >= ageRange.getMin() && age < ageRange.getMax()).count());
            return groupSumIntVO;
        }).collect(Collectors.toList());
        GroupSumIntVO.fulfillRatio(collect);
        return collect;
    }

}
