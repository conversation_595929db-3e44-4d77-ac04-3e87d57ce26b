package com.iflytek.cdc.province.utils;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 文件处理工具
 * */
public class FieldUtils {

    /**
     * 根据列表、导出头返回字节流
     * */
    public static <T> byte[] exportExcelByFormConfig(List<T> dataList, Class<T> c, boolean returnHeaderOnceNoData, List<ExcelUtils.PropertyMeta> propertyMetaList) {
        ByteArrayOutputStream outputStream;
        if (returnHeaderOnceNoData || CollectionUtils.isNotEmpty(dataList)) {
            //解析数据为excel格式，返回流
            outputStream = ExcelUtils.writeExcelStream(dataList, c, propertyMetaList);
            return outputStream.toByteArray();
        }
        return new byte[0];
    }
}
