package com.iflytek.cdc.province.utils;

import cn.hutool.core.util.StrUtil;

import java.util.regex.Pattern;

public class RegexesUtil {

    public static final String UNICODE_WORD_REGEX = "[\\p{L}\\p{M}\\p{N}]";
    public static final Pattern UNICODE_WORD_PATTERN = Pattern.compile(UNICODE_WORD_REGEX);

    /**
     * 包含数字, 字母或文字
     */
    public static boolean isMeaningfulText(String text) {
        return StrUtil.isNotBlank(text) && UNICODE_WORD_PATTERN.matcher(text).find();
    }
}
