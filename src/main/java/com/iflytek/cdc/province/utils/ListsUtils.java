package com.iflytek.cdc.province.utils;

import com.google.common.collect.Lists;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class ListsUtils {

    public static <T> List<T> filter(List<T> list, Predicate<T> predicate) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        return list.stream().filter(predicate).collect(Collectors.toList());
    }


    public static <E, K> Map<K, E> toMap(List<E> list, Function<E, K> keyExtractor) {
        return toMap(list, keyExtractor, Function.identity());
    }

    public static <E, K, V> Map<K, V> toMap(List<E> list, Function<E, K> keyExtractor, Function<E, V> valueExtractor) {
        if (list == null || list.isEmpty()) {
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(keyExtractor, valueExtractor, (a, b) -> a));
    }

    public static <E, V> List<V> expandList(List<V> dataList,
                                            List<E> keys,
                                            Function<V, E> keyExtract,
                                            Function<E, V> nullValueCreator) {
        return new ArrayList<>(expand(dataList, keys, keyExtract, nullValueCreator).values());
    }

    public static <E, V> LinkedHashMap<E, V> expand(List<V> dataList,
                                                    List<E> keys,
                                                    Function<V, E> keyExtract,
                                                    Function<E, V> nullValueCreator) {
        Map<E, V> map = toMap(dataList, keyExtract);
        LinkedHashMap<E, V> result = new LinkedHashMap<>();
        for (E k : keys) {
            V v = map.get(k);
            if (v == null) {
                v = nullValueCreator.apply(k);
            }
            result.put(k, v);
        }
        return result;
    }

    /**
     *  根据逻辑条件取交集或者并集
     * */
    public static <T> List<T> combineLists(List<List<T>> listOfLists, boolean intersection) {
        if (listOfLists == null || listOfLists.isEmpty()) {
            return new ArrayList<>();
        }
        // 初始化结果集
        Set<T> resultSet = new LinkedHashSet<>();
        // 初始化交集集合作为第一个列表的副本
        if (intersection) {
            resultSet.addAll(listOfLists.get(0));
        }
        // 遍历每个列表
        for (List<T> currentList : listOfLists) {
            // 如果取交集
            if (intersection) {
                resultSet.retainAll(currentList);
            }
            // 如果取并集
            else {
                resultSet.addAll(currentList);
            }
            // 提前返回
            if (intersection && resultSet.isEmpty()) {
                return new ArrayList<>();
            }
        }
        return new ArrayList<>(resultSet);
    }
}
