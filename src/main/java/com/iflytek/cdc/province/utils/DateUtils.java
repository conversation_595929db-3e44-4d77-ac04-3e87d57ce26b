package com.iflytek.cdc.province.utils;

import com.iflytek.cdc.province.model.dto.DateDimQueryParam;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.stream.Collectors;

@Slf4j
public class DateUtils {

    public static final String SHORT_DATE_FORMAT = "yyyy-MM-dd";

    public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm";

    public static Date formatDate(String dateStr) {
        return formatDate(dateStr, SHORT_DATE_FORMAT);
    }

    public static Date formatDate(String dateStr, String pattern) {
        if (StringUtils.hasText(dateStr)) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
            try {
                return simpleDateFormat.parse(dateStr);
            } catch (Exception e) {
                log.error("日期转换错误！", e);
            }
        }
        return null;
    }

    public static Date formatDate(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(SHORT_DATE_FORMAT);
        String dateStr = simpleDateFormat.format(date);
        return formatDate(dateStr, SHORT_DATE_FORMAT);
    }

    public static String formatDate(Date date, String format) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        return simpleDateFormat.format(date);
    }

    /**
     * 将时间往前推一年
     * */
    public static Date setPreYear(Date date){

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(formatDate(date));
        calendar.add(Calendar.YEAR, -1);
        return formatDate(calendar.getTime());
    }

    /**
     * 计算上期 的日期。
     * @param date 需要计算的基准日期。
     * @param offset 偏移量，表示要回溯的天数。
     * @return 返回计算后的日期，该日期为基准日期之前offset+1天的日期。
     */
    public static Date prePeriodDate(Date date, int offset) {
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT+8"));
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_YEAR, -(offset + 1));
        return calendar.getTime();
    }

    /**
     * 获取两个date之间的天数差
     * */
    public static Integer getGapDays(Date startDate, Date endDate) {

        LocalDate startLocalDate = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endLocalDate = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        return Math.toIntExact(ChronoUnit.DAYS.between(startLocalDate, endLocalDate));
    }

    /**
     * 将查询时间推到 上个周期
     * */
    public static <K extends DateDimQueryParam> K getPrePeriod(K dto, Class<K> clazz) {

        try {
            K lastQueryDto = clazz.newInstance();
            BeanUtils.copyProperties(dto, lastQueryDto);
            lastQueryDto.setStartDate(DateUtils.prePeriodDate(dto.getStartDate(), DateUtils.getGapDays(dto.getStartDate(), dto.getEndDate())));
            lastQueryDto.setEndDate(DateUtils.prePeriodDate(dto.getEndDate(), DateUtils.getGapDays(dto.getStartDate(), dto.getEndDate())));

            return lastQueryDto;
        }catch (InstantiationException | IllegalAccessException e){
            throw new MedicalBusinessException(e.getMessage());
        }
    }

    /**
     * 将查询时间推到 去年同期
     * */
    public static <K extends DateDimQueryParam> K getSamePeriodLastY(K dto, Class<K> clazz) {

        try {
            K lastQueryDto = clazz.newInstance();
            BeanUtils.copyProperties(dto, lastQueryDto);
            lastQueryDto.setStartDate(DateUtils.setPreYear(dto.getStartDate()));
            lastQueryDto.setEndDate(DateUtils.setPreYear(dto.getEndDate()));

            return lastQueryDto;
        }catch (InstantiationException | IllegalAccessException e){
            throw new MedicalBusinessException(e.getMessage());
        }
    }

    public static List<String> getDatesBetweenAsStr(Date startDate, Date endDate) {
        List<Date> datesBetween = getDatesBetween(startDate, endDate);
        SimpleDateFormat sdf = new SimpleDateFormat(SHORT_DATE_FORMAT);
        return datesBetween.stream().map(sdf::format).collect(Collectors.toList());
    }

    public static List<Date> getDatesBetween(Date startDate, Date endDate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(startDate);

        List<Date> dates = new ArrayList<>();
        Date currDate = cal.getTime();
        while (currDate.before(endDate) || currDate.equals(endDate)) {
            dates.add(currDate);
            cal.add(Calendar.DATE, 1);
            currDate = cal.getTime();
        }

        return dates;
    }

    public static Date min(Date date1, Date date2) {
        if (date1 == null) {
            return date2;
        }
        if (date2 == null) {
            return date1;
        }
        return date1.compareTo(date2) < 0 ? date1 : date2;
    }


    public static Date max(Date date1, Date date2) {
        if (date1 == null) {
            return date2;
        }
        if (date2 == null) {
            return date1;
        }
        return date1.compareTo(date2) > 0 ? date1 : date2;
    }

    /**
     * 给日期增加指定的年数
     * @param date 原始日期
     * @param years 要增加的年数(可以为负数表示减去年数)
     * @return 增加年数后的日期
     */
    public static Date addYears(Date date, int years) {
        return add(date, Calendar.YEAR, years);
    }

    /**
     * 给日期增加指定的月数
     * @param date 原始日期
     * @param months 要增加的月数(可以为负数表示减去月数)
     * @return 增加月数后的日期
     */
    public static Date addMonths(Date date, int months) {
        return add(date, Calendar.MONTH, months);
    }

    /**
     * 给日期增加指定的天数
     * @param date 原始日期
     * @param days 要增加的天数(可以为负数表示减去天数)
     * @return 增加天数后的日期
     */
    public static Date addDays(Date date, int days) {
        return add(date, Calendar.DAY_OF_MONTH, days);
    }

    /**
     * 给日期增加指定的小时数
     * @param date 原始日期
     * @param hours 要增加的小时数(可以为负数表示减去小时数)
     * @return 增加小时数后的日期
     */
    public static Date addHours(Date date, int hours) {
        return add(date, Calendar.HOUR_OF_DAY, hours);
    }

    /**
     * 给日期增加指定的分钟数
     * @param date 原始日期
     * @param minutes 要增加的分钟数(可以为负数表示减去分钟数)
     * @return 增加分钟数后的日期
     */
    public static Date addMinutes(Date date, int minutes) {
        return add(date, Calendar.MINUTE, minutes);
    }

    /**
     * 给日期增加指定的秒数
     * @param date 原始日期
     * @param seconds 要增加的秒数(可以为负数表示减去秒数)
     * @return 增加秒数后的日期
     */
    public static Date addSeconds(Date date, int seconds) {
        return add(date, Calendar.SECOND, seconds);
    }

    /**
     * 给日期增加指定的毫秒数
     * @param date 原始日期
     * @param milliseconds 要增加的毫秒数(可以为负数表示减去毫秒数)
     * @return 增加毫秒数后的日期
     */
    public static Date addMilliseconds(Date date, int milliseconds) {
        return add(date, Calendar.MILLISECOND, milliseconds);
    }

    /**
     * 日期加减通用方法
     * @param date 原始日期
     * @param calendarField 日历字段(如Calendar.YEAR, Calendar.MONTH等)
     * @param amount 要增加的数量(可以为负数表示减去)
     * @return 计算后的日期
     */
    private static Date add(Date date, int calendarField, int amount) {
        if (date == null) {
            throw new IllegalArgumentException("日期不能为空");
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(calendarField, amount);
        return calendar.getTime();
    }




}
