package com.iflytek.cdc.province.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.iflytek.cdc.edr.annotation.ExcelColumn;
import com.iflytek.cdc.edr.common.Common;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import java.beans.FeatureDescriptor;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class ExcelUtils {

    @Data
    public static class PropertyMeta {

        /**
         * 列规则定义
         */
        private ExcelColumn excelColumn;

        /**
         * 属性名
         */
        private String propertyName;

        /**
         * 属性访问对象
         */
        private PropertyDescriptor propertyDescriptor;

        public String getColumnName() {
            return excelColumn.name();
        }

        public int getColumnIndex() {
            return excelColumn.column();
        }

        public boolean isIndexed() {
            return getColumnIndex() > 0;
        }
    }

    /**
     * 通过用户配置的病例列表的表头字段以及顺序  获取导出表头的样式
     * */
    public static <T> List<PropertyMeta> getPropertyMetaListBy(Class<T> clazz, String configDesc){

        // 属性列表
        List<PropertyMeta> propertyMetaList;
        if(configDesc == null){
            propertyMetaList = loadClassProperties(clazz).stream()
                                                         .filter(PropertyMeta::isIndexed)
                                                         .sorted(Comparator.comparingInt(PropertyMeta::getColumnIndex))
                                                         .collect(Collectors.toList());
        }else{
            List<String> configList = Arrays.asList(configDesc.split(","));
            propertyMetaList = loadClassPropertiesByConfigList(clazz, configList).stream()
                                                                                 .filter(PropertyMeta::isIndexed)
                                                                                 .collect(Collectors.toList());
        }
        return propertyMetaList;
    }

    /**
     * 直接以实体类作为表头
     * */
    private static <T> List<PropertyMeta> loadClassProperties(Class<T> clazz) {
        List<PropertyMeta> propertyMetaList = new ArrayList<>();
        try {
            // 所有模板类属性的访问对象
            Map<String, PropertyDescriptor> properties = Arrays.stream(Introspector.getBeanInfo(clazz).getPropertyDescriptors())
                                                               .collect(Collectors.toMap(FeatureDescriptor::getName, Function.identity()));
            // Excel列对应的模板类属性信息
            for (Field field : clazz.getDeclaredFields()) {
                ExcelColumn annotation = field.getAnnotation(ExcelColumn.class);
                if (annotation == null || annotation.name().length() == 0) {
                    continue;
                }
                String propertyName = field.getName();
                PropertyDescriptor descriptor = properties.get(propertyName);
                if (descriptor == null) {
                    throw new MedicalBusinessException("11459011", "模板类属性 [" + propertyName + "] 未定义访问方法");
                }
                PropertyMeta meta = new PropertyMeta();
                meta.setExcelColumn(annotation);
                meta.setPropertyName(propertyName);
                meta.setPropertyDescriptor(descriptor);

                propertyMetaList.add(meta);
            }
        } catch (IntrospectionException e) {
            throw new MedicalBusinessException("11459012", "解析模板类失败，" + e.getMessage());
        }
        return propertyMetaList;
    }

    /**
     * 根据用户自定义配置表头导出数据
     * */
    private static <T> List<PropertyMeta> loadClassPropertiesByConfigList(Class<T> clazz, List<String> configList) {
        List<PropertyMeta> propertyMetaList = new ArrayList<>();
        try {
            // 所有模板类属性的访问对象
            Map<String, PropertyDescriptor> properties = Arrays.stream(Introspector.getBeanInfo(clazz).getPropertyDescriptors())
                                                               .collect(Collectors.toMap(FeatureDescriptor::getName, Function.identity()));

            configList.forEach(config -> {
                // Excel列对应的模板类属性信息
                for (Field field : clazz.getDeclaredFields()) {
                    ExcelColumn annotation = field.getAnnotation(ExcelColumn.class);
                    if (annotation == null || annotation.name().length() == 0) {
                        continue;
                    }
                    String propertyName = field.getName();
                    if(Objects.equals(config, propertyName)){
                        PropertyDescriptor descriptor = properties.get(propertyName);
                        if (descriptor == null) {
                            throw new MedicalBusinessException("11459011", "模板类属性 [" + propertyName + "] 未定义访问方法");
                        }
                        PropertyMeta meta = new PropertyMeta();
                        meta.setExcelColumn(annotation);
                        meta.setPropertyName(propertyName);
                        meta.setPropertyDescriptor(descriptor);

                        propertyMetaList.add(meta);
                    }
                }
            });
        } catch (IntrospectionException e) {
            throw new MedicalBusinessException("11459012", "解析模板类失败，" + e.getMessage());
        }
        return propertyMetaList;
    }

    public static <T> ByteArrayOutputStream writeExcelStream(List<T> list, Class<T> clazz, List<PropertyMeta> propertyMetaList) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Workbook workbook = ExcelUtils.writeExcel(list, clazz, propertyMetaList);
        try {
            workbook.write(outputStream);
        } catch (IOException e) {
            log.error("excel工作本写入流失败:", e);
            throw new MedicalBusinessException("11459010", "excel工作本写入流失败");
        } finally {
            //关闭流
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("关闭输出流失败:", e);
            }

            try {
                workbook.close();
            } catch (IOException e) {
                log.error("关闭Excel输出流失败:", e);
            }
        }
        return outputStream;
    }

    public static <T> Workbook writeExcel(List<T> dataList, Class<T> clazz, List<PropertyMeta> propertyMetaList) {

        Workbook wb = new SXSSFWorkbook();
        CellStyle cellStyle = wb.createCellStyle();
        Sheet sheet = wb.createSheet("Sheet1");
        AtomicInteger rowIndex = new AtomicInteger();
        // 初始化表头
        writeDataHeader(propertyMetaList, wb, sheet, rowIndex);
        // 写入数据
        writeDataRows(dataList, propertyMetaList, sheet, rowIndex, cellStyle);
        //冻结窗格
        wb.getSheet("Sheet1").createFreezePane(0, 1, 0, 1);

        return wb;
    }

    private static void writeDataHeader(List<PropertyMeta> properties, Workbook wb, Sheet sheet,
                                        AtomicInteger rowIndex) {
        Row row = sheet.createRow(rowIndex.getAndIncrement());
        AtomicInteger columnIndex = new AtomicInteger();

        CellStyle cellStyle = wb.createCellStyle();

        // 写入头部
        properties.forEach(propertyMeta -> {
            Cell cell = row.createCell(columnIndex.getAndIncrement());
            cell.setCellStyle(cellStyle);
            cell.setCellValue(propertyMeta.getColumnName());
        });
    }

    private static <T> void writeDataRows(List<T> data, List<PropertyMeta> properties, Sheet sheet,
                                          AtomicInteger rowIndex, CellStyle style) {
        if (data == null || data.isEmpty()) {
            return;
        }
        AtomicInteger columnIndex = new AtomicInteger();
        data.forEach(t -> {
            Row row1 = sheet.createRow(rowIndex.getAndIncrement());
            properties.forEach(propertyMeta -> {
                Object value = "";
                try {
                    value = propertyMeta.getPropertyDescriptor().getReadMethod().invoke(t);
                } catch (Exception e) {
                    log.info(e.getMessage());
                }
                Cell cell = row1.createCell(columnIndex.getAndIncrement());
                if (value != null) {
                    cell.setCellValue(value.toString());

                    if (value.equals(Common.EXCEL_DATA_FAIL_REASON)) {
                        style.setFillForegroundColor(IndexedColors.YELLOW.index);
                        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                        cell.setCellStyle(style);
                    }
                }
            });
            columnIndex.set(0);
        });
    }

    /**
     * 动态生成表头并导出 Excel
     *
     * @param data  数据列表
     * @param clazz 数据实体类
     * @return Excel 文件的字节数组
     */
    public static <T> byte[] exportToExcel(List<T> data, Class<T> clazz) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        // 动态生成表头
        List<List<String>> header = generateHeader(clazz);

        // 使用 EasyExcel 导出
        EasyExcel.write(outputStream)
                 .head(header) // 设置动态表头
                 .sheet("Sheet1")
                 .doWrite(data);

        return outputStream.toByteArray();
    }

    /**
     * 生成动态表头
     *
     * @param clazz 数据实体类
     * @return 表头列表
     */
    private static <T> List<List<String>> generateHeader(Class<T> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        return Arrays.stream(fields)
                     .filter(field -> field.isAnnotationPresent(ExcelProperty.class))
                     .map(field -> {
                         ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                         return Arrays.asList(excelProperty.value());
                     })
                     .collect(Collectors.toList());
    }

}
