package com.iflytek.cdc.province.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;

import java.lang.reflect.Type;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class GsonUtils {

    // 定义多个日期格式
    private static final String[] DATE_FORMATS = {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd HH:mm:ss.SSS",  // 格式1: 2024-08-16 08:01:59.772
            "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", // 格式2: 2024-08-16T08:01:59.772Z
            "yyyy-MM-dd", // 格式3: 2024-08-16
            "HH:mm:ss.SSS", // 格式4: 08:01:59.772
    };

    // 自定义的日期解析器
    private static final JsonDeserializer<Date> DATE_DESERIALIZER = (JsonElement jsonElement, java.lang.reflect.Type typeOfT, com.google.gson.JsonDeserializationContext context) -> {
        String dateString = jsonElement.getAsString();
        for (String format : DATE_FORMATS) {
            SimpleDateFormat dateFormat = new SimpleDateFormat(format);
            try {
                return dateFormat.parse(dateString);
            } catch (ParseException e) {
                // 继续尝试下一个格式
            }
        }
        try {
            throw new ParseException("Unparseable date: " + dateString, 0); // 如果没有格式能解析，抛出异常
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    };

    // 创建Gson实例，注册全局日期解析器
    private static final Gson GSON = new GsonBuilder()
            .registerTypeAdapter(Date.class, DATE_DESERIALIZER) // 注册日期解析器
            .create();

    // 公共的反序列化方法
    public static <T> T fromJson(String json, Class<T> classOfT) {
        return GSON.fromJson(json, classOfT);
    }

    public static <T> T fromJson(String json, Type typeOfT) {
        return GSON.fromJson(json, typeOfT);
    }

    // 公共的序列化方法
    public static String toJson(Object src) {
        return GSON.toJson(src);
    }
}
