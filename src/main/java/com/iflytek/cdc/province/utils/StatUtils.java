package com.iflytek.cdc.province.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import com.google.gson.Gson;
import com.iflytek.cdc.edr.enums.DateDimEnum;
import com.iflytek.cdc.edr.utils.DateFormatUtil;
import com.iflytek.cdc.province.enums.CycleTypeEnum;
import com.iflytek.cdc.province.model.dto.CommonQuery;
import com.iflytek.cdc.province.model.dto.FieldOperation;
import com.iflytek.cdc.province.model.vo.MedCntIndicatorVO;
import com.iflytek.cdc.province.model.vo.TimeTrendVO;

import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.Data;
import org.apache.commons.lang.time.DateUtils;

import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 信号统计工具类
 */
public class StatUtils {

    /**
     * 查询本期、上期、去年同期，填充同环比
     * */
    public static <T, Q extends CommonQuery> T comparisonResult(Q query,
                                                                Function<Q, T> dataFunction,
                                                                List<FieldOperation<T>> fieldOperations) {

        query.setStartDate(DateFormatUtil.getTheFistSecondOfOneDay(query.getStartDate()));
        query.setEndDate(DateFormatUtil.getTheLastSecondOfOneDay(query.getEndDate()));
        //查询当前数据
        T curr = dataFunction.apply(query);

        //查询上期和去年同期
        Q lastPeriodQuery = cloneAndAdjustQuery(query, CycleTypeEnum.LAST_PERIOD.getCode());
        Q lastYearQuery = cloneAndAdjustQuery(query, CycleTypeEnum.LAST_YEAR.getCode());

        T lastPeriodData = dataFunction.apply(lastPeriodQuery);
        T lastYearData = dataFunction.apply(lastYearQuery);
        for (FieldOperation<T> operation : fieldOperations) {
            Function<T, Integer> getter = operation.getGetter();
            int currentValue = curr == null ? 0 : getter.apply(curr) == null ? 0 : getter.apply(curr);
            // 处理同比(YoY)
            if (operation.getYoySetter() != null) {
                int lastYearValue = lastYearData == null ? 0 : getter.apply(lastYearData) == null ? 0 : getter.apply(lastYearData);
                double yoy = MathUtil.getGrowthRate(currentValue, lastYearValue);
                operation.getYoySetter().accept(curr, String.valueOf(yoy));
            }
            // 处理环比(Chain)
            if (operation.getChainSetter() != null) {
                int lastPeriodValue = lastPeriodData == null ? 0 : getter.apply(lastPeriodData) == null ? 0 : getter.apply(lastPeriodData);
                double chain = MathUtil.getGrowthRate(currentValue, lastPeriodValue);
                operation.getChainSetter().accept(curr, String.valueOf(chain));
            }
        }
        return curr;
    }

    public static <Q extends CommonQuery> Q cloneAndAdjustQuery(Q original, String adjustment) {

        Q adjustQuery = new QueryHolder<>(original).copyQuery();
        CycleTypeEnum typeEnum = CycleTypeEnum.of(adjustment);
        switch (typeEnum) {
            case LAST_PERIOD:
                long offset = original.getEndDate().getTime() - original.getStartDate().getTime() + 1;
                adjustQuery.setStartDate(new Date(original.getStartDate().getTime() - offset));
                adjustQuery.setEndDate(new Date(original.getEndDate().getTime() - offset));
                break;

            case LAST_YEAR:
                // 计算去年同期时间范围
                adjustQuery.setStartDate(DateUtils.addYears(original.getStartDate(), -1));
                adjustQuery.setEndDate(DateUtils.addYears(original.getEndDate(), -1));
                break;

            default:
                throw new IllegalArgumentException("时间转化类型错误: " + adjustment);
        }
        return adjustQuery;
    }

    /**
     * 时间分布计算同期
     */
    public static <T extends CommonQuery> List<TimeTrendVO> timeTrendPeriod(T query,
                                                                            Function<T, List<TimeTrendVO>> timeTrendFunction)  {
        List<TimeTrendVO> currData =  timeTrendFunction.apply(query);
        if (currData.size() > 0 ){
            if (query.getStartDate() == null){
                query.setStartDate(CdcDateUtil.getDateFromString(currData.get(0).getStatDate(), "yyyy-MM-dd"));
            }
            if (query.getEndDate() == null){
                query.setEndDate(CdcDateUtil.getDateFromString(currData.get(currData.size() - 1).getStatDate(), "yyyy-MM-dd"));
            }
        }
        if (query.getStartDate() == null || query.getEndDate() == null){
            return currData;
        }
        T lastYearQuery = new QueryHolder<>(query).copyQuery();
        List<TimeTrendVO> lastYearData = getLastYearData(lastYearQuery, timeTrendFunction);
        Map<String, TimeTrendVO> lastYearMap = lastYearData.stream().collect(Collectors.toMap(t -> {
            Date dateFromString = CdcDateUtil.getDateFromString(t.getStatDate(), "yyyy-MM-dd");
            return CdcDateUtil.getStringFromDate(CdcDateUtil.addYears(dateFromString, 1), "yyyy-MM-dd");
        }, t-> t));
        currData.forEach(c -> {
            c.setLastYearValue(lastYearMap.getOrDefault(c.getStatDate(), new TimeTrendVO()).getValue());
        });
        return currData;
    }

    /**
     * 获取去年数据
     */
    public static <T, Q extends CommonQuery> List<T> getLastYearData(Q query, Function<Q, List<T>> dataGetFunction){
        query.setStartDate(DateUtils.addYears(query.getStartDate(), -1));
        query.setEndDate(DateUtils.addYears(query.getEndDate(), -1));
        return dataGetFunction.apply(query);
    }

    /**
     * 获取上期数据
     */
    public static <T, Q extends CommonQuery>  List<T> getLastPeriodData(Q query, Function<Q, List<T>> dataGetFunction){
        long offset = query.getEndDate().getTime() - query.getStartDate().getTime() + 1;
        query.setStartDate(new Date(query.getStartDate().getTime() - offset));
        query.setEndDate(new Date(query.getEndDate().getTime() - offset));
        return dataGetFunction.apply(query);
    }

     /**
     * 获取上期数据
     */
    public static <T, Q extends CommonQuery>  T getOneLastPeriodData(Q query, Function<Q, T> dataGetFunction){
        long offset = query.getEndDate().getTime() - query.getStartDate().getTime() + 1;;
        query.setStartDate(new Date(query.getStartDate().getTime() - offset));
        query.setEndDate(new Date(query.getEndDate().getTime() - offset));
        return dataGetFunction.apply(query);
    }

    /**
     * 占比计算
     */
    public static  <T> void radio(List<T> data, Function<T, Integer> valueFunc, BiConsumer<T, String> radioConsumer){
        int total = data.stream().mapToInt(d -> valueFunc.apply(d) == null ? 0 : valueFunc.apply(d)).sum();
        data.forEach(d -> {
            radioConsumer.accept(d, MathUtil.getRate((long)valueFunc.apply(d), (long)total));
        });
    }


    /**
     * 填充数据，并转换数据
     */
    public static <T> List<T> fulfillAndConvertDayDateData(List<T> data,
                                                           Date startDate,
                                                           Date endDate,
                                                           DateDimEnum dateDimEnum,
                                                           Function<T, String> dateStrFunction,
                                                           BiConsumer<T, String> dateStrConsumer,
                                                           DataConverter<T> dataConverter){
        List<T> convertedData = convertDataByDateDim(data, dateStrFunction, dateDimEnum, dataConverter, dateStrConsumer);
        return fulfillDateData(convertedData, startDate, endDate, dateDimEnum, dateStrFunction, dateStrConsumer, dataConverter);
    }

    /**
     * 将数据补满，数据连贯
     */
    public static <T> List<T> fulfillDateData(List<T> data, 
                                              Date startDate,
                                              Date endDate,
                                              DateDimEnum dateDimEnum,
                                              Function<T, String> dateStrFunction,
                                              BiConsumer<T, String> dateStrConsumer,
                                              DataConverter<T> dataConverter){
        List<String> dateRange = CdcDateUtil.generateDateRange(startDate, endDate, dateDimEnum);
        Map<String, T> dataMap = data.stream().collect(Collectors.toMap(dateStrFunction, Function.identity()));
        List<T> results = new ArrayList<>();
        dateRange.forEach(d -> {
            T t = dataMap.get(d);
            if (t == null){
                t = dataConverter.init();
                dateStrConsumer.accept(t, d);
            }
            results.add(t);
        });
        return results;
    }

    /**
     * 将数据转换成对应的日期类型
     */
    public static <T> List<T> convertDataByDateDim(List<T> data,
                                                   Function<T, String> dateStrFunction,
                                                   DateDimEnum dateDimEnum,
                                                   DataConverter<T> dataConverter,
                                                   BiConsumer<T, String> dateStrConsumer){
        Map<String, List<T>> groupMap =data.stream()
                .collect(Collectors.groupingBy(
                        d -> {
                            String dateStr = dateStrFunction.apply(d);
                            return CdcDateUtil.formatDate(dateStr,  dateDimEnum);
                        },
                        LinkedHashMap::new,  // 指定Map工厂为LinkedHashMap
                        Collectors.toList()   // 下游收集器保持默认的List结构
                ));
        List<T> result = new ArrayList<>();
        groupMap.forEach((date, groupData) -> {
            T sum = dataConverter.sum(groupData);
            dateStrConsumer.accept(sum, date);
            result.add(sum);
        });
        return result;
    }

    /**
     * 数据累加，计算每天的累计值
     */
    public static <T> List<T> sumTotal(List<T> data, DataConverter<T> dataConverter ){
        List<T> history = new ArrayList<>();
        data.forEach(t -> {
            history.add(t);
            dataConverter.sumTotal(history, t);
        });
        return data;
    }


    /**
     * 根据数据中的某个值求和
     */
    public static <T> Integer sumInteger(List<T> data,
                                         Function<T, Integer> function){
        return data.stream().filter(t -> function.apply(t) != null).mapToInt(function::apply).sum();
    }

    public static List<MedCntIndicatorVO> convertMedCntByDateDim(List<MedCntIndicatorVO> data, String dateDimType){
        return StatUtils.convertDataByDateDim(data,
                MedCntIndicatorVO::getStatDate,
                DateDimEnum.getByCode(dateDimType),
                MedCntIndicatorVO.dataConverter,
                MedCntIndicatorVO::setStatDate
        );
    }

    @Data
    public static class QueryHolder<T extends CommonQuery> {
        private T query;
        private static final Gson gson = new Gson();

        public QueryHolder(T query) {
            this.query = query;
        }

        // 复制 query
        public T copyQuery() {
            if (query == null) {
                return null;
            }
            try {
                return gson.fromJson(gson.toJson(query), (Class<T>) query.getClass());
            } catch (Exception e) {
                throw new RuntimeException("Copy failed", e);
            }
        }
    }


    public interface DataConverter<T>{
        T sum(List<T> data);

        T init();
        
        void sumTotal(List<T> history, T t);
    }

    /**
     * 设置同期值的通用方法
     * @param query 查询条件
     * @param dataFunction 获取数据的函数
     * @param <T> 查询条件类型
     * @param <R> 返回数据类型
     * @return 设置了上期值的数据
     */
    public static <T extends CommonQuery, R> List<R> fulfillPeriodValueByDay(T query,
                                                                             Function<T, List<R>> dataFunction,
                                                                             Function<R, String> groupKeyFunction,
                                                                             List<Pair<Function<R, Integer>, BiConsumer<R, Integer>>> converters) {
        if (query.getStartDate() == null || query.getEndDate() == null) {
            return new ArrayList<>();
        }
        List<R> currData = dataFunction.apply(query);
        fulfillPeriodValue(query, currData, dataFunction, (last, curr) -> groupKeyFunction.apply(last).equals(CdcDateUtil.getLastPeriodDateStr(groupKeyFunction.apply(curr), query.getStartDate(), query.getEndDate())), converters);
        return currData;
    }

    /**
     * 设置同期值的通用方法
     * @param query 查询条件
     * @param dataFunction 获取数据的函数
     * @param <T> 查询条件类型
     * @param <R> 返回数据类型
     * @return 设置了上期值的数据
     */
    public static <T extends CommonQuery, R> List<R> fulfillPeriodValue(T query,
                                                                        Function<T, List<R>> dataFunction,
                                                                        BiPredicate<R, R> matchLastPredicate,
                                                                        List<Pair<Function<R, Integer>, BiConsumer<R, Integer>>> converters) {
        if (query.getStartDate() == null || query.getEndDate() == null) {
            return new ArrayList<>();
        }
        List<R> currData = dataFunction.apply(query);
        return fulfillPeriodValue(query, currData, dataFunction, matchLastPredicate, converters);
    }


     private static <T extends CommonQuery, R> List<R> fulfillPeriodValue(T query,
                                                                          List<R> currData,
                                                                          Function<T, List<R>> dataFunction,
                                                                          BiPredicate<R, R> matchLastPredicate,
                                                                          List<Pair<Function<R, Integer>, BiConsumer<R, Integer>>> converters) {

        
        T lastYearQuery = new QueryHolder<>(query).copyQuery();
        List<R> lastYearData = getLastPeriodData(lastYearQuery, dataFunction);
        currData.forEach(c -> {
            R last = lastYearData.stream().filter(l -> matchLastPredicate.test(l, c)).findFirst().orElse(null);
            converters.forEach(converter -> {
                Integer lastValue = last == null ? 0 : converter.getKey().apply(last);
                converter.getValue().accept(c, lastValue);
            });

        });
        return currData;
    }

    public static <T extends CommonQuery, R> R fulfillOnePeriodValue(T query,
                                                            Function<T, R> dataFunction,
                                                            List<Pair<Function<R, Integer>, BiConsumer<R, Integer>>> converters) {
    
        R currData = dataFunction.apply(query);
        if (currData == null) {
            return null;
        }
        T lastYearQuery = new QueryHolder<>(query).copyQuery();
    
        R lastYearData = getOneLastPeriodData(lastYearQuery, dataFunction);
        converters.forEach(converter -> {
            Integer lastValue = lastYearData == null ? 0 : converter.getKey().apply(lastYearData);
            converter.getValue().accept(currData, lastValue);
        });
        return currData;
    }

    public static <T> void replaceDashWithEmpty(List<T> list) {
        if (list == null || list.isEmpty()) return;
        for (T root : list) {
            deepReplace(root);
        }
    }

    private static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null && clazz != Object.class) {
            Collections.addAll(fields, clazz.getDeclaredFields());
            clazz = clazz.getSuperclass();
        }
        return fields;
    }

    private static <T> void deepReplace(T obj) {
        if (obj == null) return;

        // 1. 处理当前对象
        replaceInObject(obj);

        // 2. 取出 children 字段（List 或数组均可）
        List<Field> fields = getAllFields(obj.getClass());
        for (Field f : fields) {
            if (f.getName().equals("children")) {
                f.setAccessible(true);
                try {
                    Object children = f.get(obj);
                    if (children == null) continue;

                    if (children instanceof Collection) {
                        for (Object child : (Collection<?>) children) {
                            deepReplace(child);          // 递归
                        }
                    } else if (children.getClass().isArray()) {
                        int len = Array.getLength(children);
                        for (int i = 0; i < len; i++) {
                            deepReplace(Array.get(children, i));  // 递归
                        }
                    }
                } catch (IllegalAccessException ignored) {
                    // log
                }
                break; // children 字段只会有一个，取到即可退出
            }
        }
    }

    /**
     * 仅对单个对象做字符串替换
     */
    private static void replaceInObject(Object obj) {
        List<Field> fields = getAllFields(obj.getClass());
        for (Field f : fields) {
            if (f.getType() != String.class) continue;
            f.setAccessible(true);
            try {
                Object val = f.get(obj);
                if ("--".equals(val)) {
                    f.set(obj, "");
                } else if (val != null && String.valueOf(val).contains("0.0")) {
                    f.set(obj, "0");
                }
            } catch (IllegalAccessException ignored) {
                // log
            }
        }
    }

    /**
     * 查询list对象的本期、同期、上期
     * 计算同环比
     * */
    public static <T, Q extends CommonQuery> List<T> build(Q query,
                                                           Supplier<T> instanceSupplier,
                                                           Function<T, String> statIndicator,
                                                           BiConsumer<T, String> statIndicatorSetter,
                                                           Function<Q, List<T>> dataFunction,
                                                           List<FieldOperation<T>> operations,
                                                           List<Pair<Function<T, ?>, BiConsumer<T, ?>>> otherProperties) {

        List<T> result = new ArrayList<>();
        if (query.getStartDate() != null && query.getEndDate() != null) {
            query.setStartDate(DateFormatUtil.getTheFistSecondOfOneDay(query.getStartDate()));
            query.setEndDate(DateFormatUtil.getTheLastSecondOfOneDay(query.getEndDate()));
        }
        //查询当前数据
        List<T> currentData = dataFunction.apply(query);

        //查询上期和去年同期
        Q lastPeriodQuery = cloneAndAdjustQuery(query, CycleTypeEnum.LAST_PERIOD.getCode());
        Q lastYearQuery = cloneAndAdjustQuery(query, CycleTypeEnum.LAST_YEAR.getCode());
        List<T> lastPeriodData = dataFunction.apply(lastPeriodQuery);
        List<T> lastYearData = dataFunction.apply(lastYearQuery);

        List<String> currObj = currentData.stream().map(statIndicator).filter(Objects::nonNull).collect(Collectors.toList());
        List<String> lastPeriodObj = lastPeriodData.stream().map(statIndicator).filter(Objects::nonNull).collect(Collectors.toList());
        List<String> lastYearObj = lastYearData.stream().map(statIndicator).filter(Objects::nonNull).collect(Collectors.toList());

        //根据统计的对象进行
        Map<String, T> currentMap = currentData.stream().collect(Collectors.toMap(statIndicator, Function.identity(), (a, b) -> a));
        Map<String, T> lastPeriodMap = lastPeriodData.stream().collect(Collectors.toMap(statIndicator, Function.identity(), (a, b) -> a));
        Map<String, T> lastYearMap = lastYearData.stream().collect(Collectors.toMap(statIndicator, Function.identity(), (a, b) -> a));

        List<String> allObj = Stream.of(currObj, lastPeriodObj, lastYearObj).flatMap(List::stream).distinct().collect(Collectors.toList());

        for (String objKey : allObj) {
            // 创建新对象
            T newObj = instanceSupplier.get();
            // 设置statIndicator字段
            statIndicatorSetter.accept(newObj, objKey);
            // 本期、上期、同期原数据（可能为null）
            T curr = currentMap.get(objKey);
            T lastPeriod = lastPeriodMap.get(objKey);
            T lastYear = lastYearMap.get(objKey);
            for (FieldOperation<T> operation : operations) {
                Function<T, Integer> getter = operation.getGetter();
                BiConsumer<T, Integer> valueCurrSetter = operation.getValueCurr();
                BiConsumer<T, Integer> valueLastSetter = operation.getValueLast();
                BiConsumer<T, Integer> valueLastYSetter = operation.getValueLastY();
                BiConsumer<T, String> yoySetter = operation.getYoySetter();
                BiConsumer<T, String> chainSetter = operation.getChainSetter();

                // 本期值
                int currValue = curr == null ? 0 : getter.apply(curr) == null ? 0 : getter.apply(curr);
                // 上期值
                int lastValue = lastPeriod == null ? 0 : getter.apply(lastPeriod) == null ? 0 : getter.apply(lastPeriod);
                // 去年同期值
                int lastYValue = lastYear == null ? 0 : getter.apply(lastYear) == null ? 0 : getter.apply(lastYear);

                valueCurrSetter.accept(newObj, currValue);
                valueLastSetter.accept(newObj, lastValue);
                valueLastYSetter.accept(newObj, lastYValue);

                if (yoySetter != null) {
                    String yoyStr = MathUtil.getGrowthRateStr(lastYValue, currValue);
                    yoySetter.accept(newObj, yoyStr);
                }
                if (chainSetter != null) {
                    String chainStr = MathUtil.getGrowthRateStr(lastValue, currValue);
                    chainSetter.accept(newObj, chainStr);
                }
            }
            //对象中需要set的额外属性
            if (CollUtil.isNotEmpty(otherProperties)) {
                otherProperties.forEach(property -> {
                    Function<T, ?> getProperty = property.getKey();
                    Optional<T> firstNonEmptyObj = findFirstNotEmpty(getProperty, curr, lastPeriod, lastYear);
                    firstNonEmptyObj.ifPresent(foundObj -> {
                        Object value = getProperty.apply(foundObj);
                        // 使用原始类型进行设置
                        @SuppressWarnings("unchecked")
                        BiConsumer<T, Object> consumer = (BiConsumer<T, Object>) property.getValue();
                        consumer.accept(newObj, value);
                    });
                });
            }
            result.add(newObj);
        }
        return result;
    }

    /**
     * 找某个属性非空的对象
     * */
    @SafeVarargs
    private static <T> Optional<T> findFirstNotEmpty(Function<T, ?> extractor,
                                                     T... objects) {
        if (objects == null) {
            return Optional.empty();
        }
        return Arrays.stream(objects)
                     .filter(Objects::nonNull)
                     .filter(obj -> {
                         Object value = extractor.apply(obj);
                         if (value instanceof String) return !((String)value).trim().isEmpty();
                         return value != null;
                     }).findFirst();
    }

    /**
     * 根据周期条件设置去年同期或者上期数据
     * */
    public static <T extends CommonQuery, R> List<R> fulfillLastValueByDay(T query,
                                                                           CycleTypeEnum cycleType,
                                                                           Supplier<R> instanceSupplier,
                                                                           Function<T, List<R>> dataFunction,
                                                                           Function<R, String> statIndicator,
                                                                           BiConsumer<R, String> statIndicatorSetter,
                                                                           List<FieldOperation<R>> operations) {
        if (query.getStartDate() == null || query.getEndDate() == null) {
            return new ArrayList<>();
        }
        List<R> result = new ArrayList<>();
        List<R> currData = dataFunction.apply(query);

        T cycleTypeQuery = new QueryHolder<>(query).copyQuery();
        List<R> cycleData;
        switch (cycleType) {
            case LAST_PERIOD:
                cycleData = getLastPeriodData(cycleTypeQuery, dataFunction);
                break;
            case LAST_YEAR:
                cycleData = getLastYearData(cycleTypeQuery, dataFunction);
                break;
            default:
                throw new MedicalBusinessException("周期类型错误");
        }

        List<String> currObj = currData.stream().map(statIndicator).filter(Objects::nonNull).collect(Collectors.toList());
        List<String> lastPeriodObj = cycleData.stream().map(statIndicator).filter(Objects::nonNull).collect(Collectors.toList());

        Map<String, R> currentMap = currData.stream().collect(Collectors.toMap(statIndicator, Function.identity(), (a, b) -> a));
        Map<String, R> cycleMap = cycleData.stream().collect(Collectors.toMap(statIndicator, Function.identity(), (a, b) -> a));

        List<String> allObj = Stream.of(currObj, lastPeriodObj).flatMap(List::stream).distinct().collect(Collectors.toList());

        for (String objKey : allObj) {
            // 创建新对象
            R newObj = instanceSupplier.get();
            // 设置statIndicator字段
            statIndicatorSetter.accept(newObj, objKey);
            // 本期、上期、同期原数据（可能为null）
            R curr = currentMap.get(objKey);
            R cycle = cycleMap.get(objKey);
            for (FieldOperation<R> operation : operations) {
                Function<R, Integer> getter = operation.getGetter();
                BiConsumer<R, Integer> valueCurrSetter = operation.getValueCurr();
                // 本期值
                int currValue = curr == null ? 0 : getter.apply(curr) == null ? 0 : getter.apply(curr);
                valueCurrSetter.accept(newObj, currValue);
                if (Objects.equals(CycleTypeEnum.LAST_PERIOD, cycleType)) {
                    BiConsumer<R, Integer> valueLastSetter = operation.getValueLast();
                    // 上期值
                    int lastValue = cycle == null ? 0 : getter.apply(cycle) == null ? 0 : getter.apply(cycle);
                    valueLastSetter.accept(newObj, lastValue);
                }
                if (Objects.equals(CycleTypeEnum.LAST_YEAR, cycleType)) {
                    BiConsumer<R, Integer> valueLastYSetter = operation.getValueLastY();
                    // 去年同期值
                    int lastYValue = cycle == null ? 0 : getter.apply(cycle) == null ? 0 : getter.apply(cycle);
                    valueLastYSetter.accept(newObj, lastYValue);
                }
            }
            result.add(newObj);
        }
        return result;
    }

}
