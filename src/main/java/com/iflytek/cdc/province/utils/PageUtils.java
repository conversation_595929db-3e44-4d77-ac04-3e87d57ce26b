package com.iflytek.cdc.province.utils;

import com.github.pagehelper.PageInfo;

import java.util.Collections;
import java.util.List;

public class PageUtils {

    /**
     * 手动分页函数
     * */
    public static <T> PageInfo<T> ofPage(List<T> recordList, Integer pageIndex, Integer pageSize){

        List<T> subList;
        int size = recordList.size();
        if (pageIndex == null) {
            pageIndex = 1;
            pageSize = size;
        }
        int pageNum = size / pageSize;
        //是不是整除
        int surplus = size % pageSize;
        if (surplus > 0) {
            pageNum = pageNum + 1;
        }
        int pageStart = pageIndex == 1 ? 0 : (pageIndex - 1) * pageSize;
        int pageEnd = Math.min(pageIndex * pageSize, size);

        if (pageStart > pageEnd) {
            subList = Collections.emptyList();
        } else {
            subList = recordList.subList(pageStart, pageEnd);
        }
        PageInfo<T> pageData = new PageInfo<>();
        pageData.setPages(pageNum);
        pageData.setPageNum(pageIndex);
        pageData.setPageSize(pageSize);
        pageData.setTotal(size);
        pageData.setList(subList);
        return pageData;
    }

}
