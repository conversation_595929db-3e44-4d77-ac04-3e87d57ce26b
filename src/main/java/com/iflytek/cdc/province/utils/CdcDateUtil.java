package com.iflytek.cdc.province.utils;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.iflytek.cdc.edr.enums.DateDimEnum;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.*;

@Slf4j
public class CdcDateUtil extends DateUtils {

    public static final String DATE_FORMAT = "yyyy-MM-dd";

    public static final String DATE_FORMAT_TIMESTAMP = "yyyy-MM-dd HH:m:ss";

    /**
     * 相差小时数，返回：小时数+h
     * 不足一小时返回小数
     */
    public static String getBetweenHourStr(Date fromDate, Date toDate){

        double gapTime = DateUtil.between(fromDate, toDate, DateUnit.MINUTE) / 60d;
        return String.format("%.1f", gapTime) + "h";
    }

    /**
     * Date->String
     *
     * @param date
     * @param pattern 转换格式
     * @return
     */
    public static String getStringFromDate(Date date, String pattern) {
        try {
            if (date == null) {
                return "";
            }
            SimpleDateFormat formatter = new SimpleDateFormat(pattern);
            return formatter.format(date);
        } catch (Exception e) {
            log.error("=========日期格式转换失败，{}", e.getLocalizedMessage());
            return "";
        }
    }

    /**
     * String->Date
     *
     * @param dateStr
     * @param pattern
     * @return String
     */
    public static Date getDateFromString(String dateStr, String pattern) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            return sdf.parse(dateStr);
        } catch (Exception e) {
            throw new MedicalBusinessException("日期格式错误");
        }
    }

    /**
     * 格式化结束时间
     */
    public static Date fomatEndDate(Date endDate){
        if (endDate != null){
            endDate = DateUtils.setHours(endDate, 23);
            endDate = DateUtils.setMinutes(endDate, 59);
            endDate = DateUtils.setSeconds(endDate, 59);
            endDate = DateUtils.setMilliseconds(endDate, 999);
        }
        return endDate;
    }

    /**
     * 判断两个日期是否是同一天
     * */
    public static boolean isSameDay(Date date1, Date date2) {
        // 使用 Calendar 获取日期的年、月、日部分
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();

        // 设置日期
        cal1.setTime(date1);
        cal2.setTime(date2);

        // 比较年、月、日
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH) &&
                cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);
    }


    public static String formatDate(String dateStr, DateDimEnum dateType) {
        LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ISO_DATE);

        switch (dateType) {
            case DAY:
                return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            case WEEK:
                int weekNumber = date.get(WeekFields.of(Locale.getDefault()).weekOfWeekBasedYear());
                return date.getYear() + "年第" + weekNumber + "周";
            case MEADOW:
                int dayOfMonth = date.getDayOfMonth();
                String meadow;
                if (dayOfMonth <= 10) {
                    meadow = "上旬";
                } else if (dayOfMonth <= 20) {
                    meadow = "中旬";
                } else {
                    meadow = "下旬";
                }
                return date.getYear() + "年" + String.format("%02d", date.getMonthValue()) + meadow;
            case MONTH:
                return date.format(DateTimeFormatter.ofPattern("yyyy年MM月"));
            case QUARTER:
                int quarter = (date.getMonthValue() - 1) / 3 + 1;
                return date.getYear() + "年第" + quarter + "季度";
            case HALF_YEAR:
                int halfYear = (date.getMonthValue() - 1) / 6 + 1;
                return date.getYear() + "年" + (halfYear == 1 ? "上" : "下") + "半年";
            case YEAR:
                return date.getYear() + "年";
            default:
                throw new IllegalArgumentException("Unsupported date type: " + dateType);
        }
    }

    /**
     * 根据开始时间、结束时间和日期类型，生成对应格式的日期列表
     *
     * @param startDate 开始时间（格式：yyyy-MM-dd）
     * @param endDate   结束时间（格式：yyyy-MM-dd）
     * @param dateType  日期类型
     * @return 格式化后的日期列表
     */
    public static List<String> generateDateRange(Date startDate, Date endDate, DateDimEnum dateType) {
        LocalDate start = toLocalDate(startDate);
        LocalDate end = toLocalDate(endDate);
        end = getLastDayOfPeriod(end, dateType);
        List<String> result = new ArrayList<>();

        switch (dateType) {
            case DAY:
                for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
                    result.add(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                }
                break;
            case WEEK:
                // 调整起始日期到周开始（周一）
                LocalDate weekStart = start.with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY));
                for (LocalDate date = weekStart; !date.isAfter(end); date = date.plusWeeks(1)) {
                    int weekNumber = date.get(WeekFields.of(Locale.getDefault()).weekOfWeekBasedYear());
                    result.add(date.getYear() + "年第" + weekNumber + "周");
                }
                break;
            case MEADOW:
                // 调整起始日期到旬开始
                LocalDate meadowStart = start;
                int dayOfMonth = start.getDayOfMonth();
                if (dayOfMonth > 20) {
                    meadowStart = start.withDayOfMonth(21);
                } else if (dayOfMonth > 10) {
                    meadowStart = start.withDayOfMonth(11);
                } else {
                    meadowStart = start.withDayOfMonth(1);
                }
                for (LocalDate date = meadowStart; !date.isAfter(end); date = date.plusDays(10)) {
                    int currentDayOfMonth = date.getDayOfMonth();
                    String meadow;
                    if (currentDayOfMonth <= 10) {
                        meadow = "上旬";
                    } else if (currentDayOfMonth <= 20) {
                        meadow = "中旬";
                    } else {
                        meadow = "下旬";
                    }
                    result.add(date.getYear() + "年" + String.format("%02d", date.getMonthValue()) + meadow);
                }
                break;
            case MONTH:
                // 调整起始日期到月份开始
                LocalDate monthStart = start.withDayOfMonth(1);
                for (LocalDate date = monthStart; !date.isAfter(end); date = date.plusMonths(1)) {
                    result.add(date.format(DateTimeFormatter.ofPattern("yyyy年MM月")));
                }
                break;
            case QUARTER:
                // 调整起始日期到季度开始
                LocalDate quarterStart = start.withMonth((start.getMonthValue() - 1) / 3 * 3 + 1).withDayOfMonth(1);
                for (LocalDate date = quarterStart; !date.isAfter(end); date = date.plusMonths(3)) {
                    int quarter = (date.getMonthValue() - 1) / 3 + 1;
                    result.add(date.getYear() + "年第" + quarter + "季度");
                }
                break;
            case HALF_YEAR:
                // 调整起始日期到半年开始
                LocalDate halfYearStart = start.withMonth((start.getMonthValue() - 1) / 6 * 6 + 1).withDayOfMonth(1);
                for (LocalDate date = halfYearStart; !date.isAfter(end); date = date.plusMonths(6)) {
                    int halfYear = (date.getMonthValue() - 1) / 6 + 1;
                    result.add(date.getYear() + "年" + (halfYear == 1 ? "上" : "下") + "半年");
                }
                break;
            case YEAR:
                // 调整起始日期到年份开始
                LocalDate yearStart = start.withDayOfYear(1);
                for (LocalDate date = yearStart; !date.isAfter(end); date = date.plusYears(1)) {
                    result.add(date.getYear() + "年");
                }
                break;
            default:
                throw new IllegalArgumentException("Unsupported date type: " + dateType);
        }

        return result;
    }

    public static LocalDate toLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static LocalDate getLastDayOfPeriod(LocalDate endDate, DateDimEnum timeType) {
        switch (timeType) {
            case DAY:
                return endDate;
            case WEEK:
                return endDate.with(TemporalAdjusters.nextOrSame(java.time.DayOfWeek.SUNDAY));
            case MEADOW:
                int day = endDate.getDayOfMonth();
                if (day <= 10) {
                    return endDate.withDayOfMonth(10);
                } else if (day <= 20) {
                    return endDate.withDayOfMonth(20);
                } else {
                    return endDate.with(TemporalAdjusters.lastDayOfMonth());
                }
            case MONTH:
                return endDate.with(TemporalAdjusters.lastDayOfMonth());
            case QUARTER:
                int month = endDate.getMonthValue();
                int quarterEndMonth;
                if (month <= 3) {
                    quarterEndMonth = 3;
                } else if (month <= 6) {
                    quarterEndMonth = 6;
                } else if (month <= 9) {
                    quarterEndMonth = 9;
                } else {
                    quarterEndMonth = 12;
                }
                return endDate.withMonth(quarterEndMonth).with(TemporalAdjusters.lastDayOfMonth());
            case HALF_YEAR:
                int halfYearMonth = (endDate.getMonthValue() - 1) / 6 + 1;
                return endDate.withMonth(halfYearMonth).with(TemporalAdjusters.lastDayOfMonth());
            case YEAR:
                return endDate.with(TemporalAdjusters.lastDayOfYear());
            default:
                throw new IllegalArgumentException("不支持的时间类型: " + timeType);
        }
    }

    public static void main(String[] args) {
        System.out.println(getNextPeriod("2025-02-28", DateDimEnum.DAY));
        System.out.println(getNextPeriod("2025年第5周", DateDimEnum.WEEK));
    }


    /**
     * 根据输入的日期格式和常量类型，输出下一个周期的数据
     *
     * @param dateStr 日期字符串（格式与常量类型匹配）
     * @param dateType 日期类型
     * @return 下一个周期的日期字符串
     */
    public static String getNextPeriod(String dateStr, DateDimEnum  dateType) {
        switch (dateType) {
            case DAY:
                LocalDate day = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                return day.plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            case WEEK:
                String[] weekParts = dateStr.split("年第|周");
                int year = Integer.parseInt(weekParts[0]);
                int week = Integer.parseInt(weekParts[1]);
                LocalDate weekDate = LocalDate.of(year, 1, 1)
                        .with(WeekFields.of(Locale.getDefault()).weekOfWeekBasedYear(), week)
                        .plusWeeks(1);
                int nextWeek = weekDate.get(WeekFields.of(Locale.getDefault()).weekOfWeekBasedYear());
                return weekDate.getYear() + "年第" + nextWeek + "周";
            case MEADOW:
                String[] meadowParts = dateStr.split("[年月旬]");
                int meadowYear = Integer.parseInt(meadowParts[0]);
                int meadowMonth = Integer.parseInt(meadowParts[1]);
                String meadowPart = meadowParts[2];
                LocalDate meadowDate = LocalDate.of(meadowYear, meadowMonth, 1);
                if (meadowPart.equals("上旬")) {
                    meadowDate = meadowDate.withDayOfMonth(11);
                } else if (meadowPart.equals("中旬")) {
                    meadowDate = meadowDate.withDayOfMonth(21);
                } else {
                    meadowDate = meadowDate.plusMonths(1).withDayOfMonth(1);
                }
                return formatMeadow(meadowDate);
            case MONTH:
                LocalDate month = LocalDate.parse(dateStr + "01", DateTimeFormatter.ofPattern("yyyy年MM月dd"));
                return month.plusMonths(1).format(DateTimeFormatter.ofPattern("yyyy年MM月"));
            case QUARTER:
                String[] quarterParts = dateStr.split("年第|季度");
                int quarterYear = Integer.parseInt(quarterParts[0]);
                int quarter = Integer.parseInt(quarterParts[1]);
                LocalDate quarterDate = LocalDate.of(quarterYear, (quarter - 1) * 3 + 1, 1);
                return formatQuarter(quarterDate.plusMonths(3));
            case HALF_YEAR:
                String[] halfYearParts = dateStr.split("年|半年");
                int halfYearYear = Integer.parseInt(halfYearParts[0]);
                String halfYearPart = halfYearParts[1];
                LocalDate halfYearDate = LocalDate.of(halfYearYear, halfYearPart.equals("上") ? 1 : 7, 1);
                return formatHalfYear(halfYearDate.plusMonths(6));
            case YEAR:
                int yearValue = Integer.parseInt(dateStr.replace("年", ""));
                return (yearValue + 1) + "年";
            default:
                throw new IllegalArgumentException("Unsupported date type: " + dateType);
        }
    }

    /**
     * 格式化旬
     */
    private static String formatMeadow(LocalDate date) {
        int dayOfMonth = date.getDayOfMonth();
        if (dayOfMonth <= 10) {
            return date.getYear() + "年" + String.format("%02d", date.getMonthValue()) + "月上旬";
        } else if (dayOfMonth <= 20) {
            return date.getYear() + "年" + String.format("%02d", date.getMonthValue()) + "月中旬";
        } else {
            return date.getYear() + "年" + String.format("%02d", date.getMonthValue()) + "月下旬";
        }
    }

    /**
     * 格式化季度
     */
    private static String formatQuarter(LocalDate date) {
        int quarter = (date.getMonthValue() - 1) / 3 + 1;
        return date.getYear() + "年第" + quarter + "季度";
    }

    /**
     * 格式化半年
     */
    private static String formatHalfYear(LocalDate date) {
        int halfYear = (date.getMonthValue() - 1) / 6 + 1;
        return date.getYear() + "年" + (halfYear == 1 ? "上" : "下") + "半年";
    }

    /**
     * 获取上一期的日期字符串
     * @param currDateStr 当前日期字符串 (格式: yyyy-MM-dd)
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 上一期对应的日期字符串
     */
    public static String getLastPeriodDateStr(String currDateStr, Date startDate, Date endDate) {
        if (currDateStr == null || startDate == null || endDate == null) {
            return null;
        }
        Date currDate = CdcDateUtil.getDateFromString(currDateStr, "yyyy-MM-dd");
        if (currDate == null) {
            return null;
        }

        long periodLength = endDate.getTime() - startDate.getTime();
        Date lastPeriodDate = new Date(currDate.getTime() - periodLength);
        return CdcDateUtil.getStringFromDate(lastPeriodDate, "yyyy-MM-dd");
    }

}
