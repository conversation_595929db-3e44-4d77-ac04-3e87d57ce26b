package com.iflytek.cdc.province.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Getter
public enum CheckIdentifyStatusEnum {

    NO_NEED_CHECK("noNeedCheck", "无需审核"),

    NOT_CHECK("notCheck", "未审核"),

    DISTRICT_CHECK_REJECT("districtCheckReject", "县级审核退回"),

    DISTRICT_CHECK_PASS("districtCheckPass", "县级审核通过"),

    CITY_CHECK_REJECT("cityCheckReject", "市级审核退回"),

    CITY_CHECK_PASS("cityCheckPass", "市级审核通过"),

    PROVINCE_CHECK_PASS("cityCheckPass", "省级审核/终审"),
    ;

    private final String code;
    private final String desc;

    CheckIdentifyStatusEnum(String code, String desc) {

        this.code = code;
        this.desc = desc;
    }

    public  static Map<String, Object> mapValues() {
        return Arrays.stream(values()).collect(Collectors.toMap(CheckIdentifyStatusEnum::getDesc, CheckIdentifyStatusEnum::getCode));
    }

}
