package com.iflytek.cdc.province.enums;

import lombok.Getter;

@Getter
public enum ProcessInfoTypeEnum {

    SYNDROME("syndrome", "症候群"),

    INFECTED("infected", "传染病"),

    MULTICHANNEL("multichannel", "多渠道"),

    EMERGENCY("emergency", "突发公共卫生事件"),

    INTEGRATED("integrated", "多渠道综合预警"),

    EMERGING("emerging", "新发突发传染病"),

    ENDEMIC("endemic", "地方性传染病"),

    MUL("mul", "多渠道"),

    ;

    private final String code;

    private final String desc;

    ProcessInfoTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
