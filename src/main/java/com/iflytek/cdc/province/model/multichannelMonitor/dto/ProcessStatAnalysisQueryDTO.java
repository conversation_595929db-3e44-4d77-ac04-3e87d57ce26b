package com.iflytek.cdc.province.model.multichannelMonitor.dto;

import com.iflytek.cdc.province.model.dto.DateDimQueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "多渠道态势感知查询参数")
public class ProcessStatAnalysisQueryDTO extends DateDimQueryParam {

    @ApiModelProperty(value = "疾病类型")
    private String diseaseTypeCode;

    @ApiModelProperty(value = "疾病code列表")
    private List<String> diseaseCodeList;

    @ApiModelProperty(value = "症候群code列表")
    private List<String> syndromeCodeList;

    @ApiModelProperty("专题id")
    private String topicId;

    @ApiModelProperty("区域查询类型 病例现住址-livingAddress;监测单位-orgAddress")
    private String addressType;

    @ApiModelProperty("时间查询类型  系统报告时间-identifyTime; 报告审核时间-approveTime; 病例发病时间-onsetTime")
    private String timeType;

    @ApiModelProperty("预警场景类型")
    private String warningScenarioType;
    @ApiModelProperty("其他预警场景类型")
    private String otherWarningScenarioType;

    @ApiModelProperty(value = "人群分类", hidden = true)
    private List<String> personTypeList;

    @ApiModelProperty("过滤的传染病code")
    private List<String> filterInfectCodes;

    @ApiModelProperty("过滤的传染病名称")
    private List<String> filterInfectNames;
}
