package com.iflytek.cdc.province.model.multichannelMonitor.dto;

import com.iflytek.cdc.province.model.dto.CommonQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
public class MultichannelQueryDTO extends CommonQuery {

    @ApiModelProperty("多渠道Topic id")
    private String topicId;

    @ApiModelProperty(hidden = true)
    private List<String> topicInfectIds;
    @ApiModelProperty(hidden = true)
    private List<String> topicSyndromeIds;
    @ApiModelProperty(hidden = true)
    private List<String> topicPathogenIds;
    @ApiModelProperty(hidden = true)
    private List<String> topicKeywords;
    @ApiModelProperty(hidden = true)
    private List<String> topicDrugNames;

}
