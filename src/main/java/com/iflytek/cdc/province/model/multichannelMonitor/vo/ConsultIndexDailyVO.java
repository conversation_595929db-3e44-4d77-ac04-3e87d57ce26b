package com.iflytek.cdc.province.model.multichannelMonitor.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ConsultIndexDailyVO {

    @ApiModelProperty("监测关键词")
    private String keyword;

    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date day;

    @ApiModelProperty(value = "资讯指数日均值")
    private Integer indexValue;

    @ApiModelProperty(value = "资讯指数同期日均值")
    private Integer indexLyValue;

    @ApiModelProperty(value = "资讯指数数据数量")
    private Integer count;

    public ConsultIndexDailyVO() {
    }

    public ConsultIndexDailyVO(String keyword, Date day) {
        this.keyword = keyword;
        this.day = day;
        indexLyValue=0;
        indexValue=0;
        count = 0;
    }
}
