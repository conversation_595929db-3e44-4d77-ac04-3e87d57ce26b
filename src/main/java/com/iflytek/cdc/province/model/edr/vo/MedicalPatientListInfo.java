package com.iflytek.cdc.province.model.edr.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("病例列表")
public class MedicalPatientListInfo {

    @ApiModelProperty("就诊事件id")
    private String eventId;

    @ApiModelProperty("ID")
    private String Id;

    @ApiModelProperty("主索引ID")
    private String mpiId;

    @ApiModelProperty("患者身份唯一标识")
    private String patientId;

    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("身份证号")
    private String idCard;

    @ApiModelProperty("出生日期")
    private String birthDate;

    @ApiModelProperty("性别")
    private String genderCode;

    @ApiModelProperty("国籍")
    private String nationalityCode;

    @ApiModelProperty("民族")
    private String nationCode;

    @ApiModelProperty("户籍地区编码")
    private String domicileAddrCode;

    @ApiModelProperty("户籍地区省编码")
    private String domicileAddrProvinceCode;

    @ApiModelProperty("户籍地区市编码")
    private String domicileAddrCityCode;

    @ApiModelProperty("户籍地区区编码")
    private String domicileAddrDistrictCode;

    @ApiModelProperty("户籍地区街道编码")
    private String domicileAddrStreetCode;

    @ApiModelProperty("户籍详细地址")
    private String domicileAddrDetail;

    @ApiModelProperty("现住地区编码")
    private String currentAddrCode;

    @ApiModelProperty("现住地区省编码")
    private String currentAddrProvinceCode;

    @ApiModelProperty("现住地区市编码")
    private String currentAddrCityCode;

    @ApiModelProperty("现住地区区编码")
    private String currentAddrDistrictCode;

    @ApiModelProperty("现住地区街道编码")
    private String currentAddrStreetCode;

    @ApiModelProperty("现住地区编码")
    private String currentAddrLongitude;

    @ApiModelProperty("现住地区编码")
    private String currentAddrLatitude;

    @ApiModelProperty("现住详细地区")
    private String currentAddrDetail;

    @ApiModelProperty("婚姻状况编码")
    private String maritalStatusCode;

    @ApiModelProperty("婚姻状况名称")
    private String maritalStatusName;

    @ApiModelProperty("联系电话")
    private String contactsTel;
}
