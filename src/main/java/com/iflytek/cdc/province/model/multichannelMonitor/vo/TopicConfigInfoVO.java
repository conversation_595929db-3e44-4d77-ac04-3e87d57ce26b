package com.iflytek.cdc.province.model.multichannelMonitor.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 专题配置信息
 * */
@Data
public class TopicConfigInfoVO {

    @ApiModelProperty(value = "专题id")
    private String topicId;

    @ApiModelProperty(value = "专题名称")
    private String topicName;

    @ApiModelProperty(value = "专题说明")
    private String notes;

    @ApiModelProperty(value = "专题配置")
    private Map<String, List<TbCdcmrMultichannelTopicConfig>> topicConfigMap;

}
