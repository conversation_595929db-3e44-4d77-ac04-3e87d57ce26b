package com.iflytek.cdc.province.model.multichannelMonitor.vo;

import com.iflytek.cdc.edr.vo.CommonDateStatVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 病例区域分布
 * */
@Data
public class DiseaseProcessAreaDistributionVO {

    private String diseaseCode;

    private String diseaseName;

    private List<AreaProcessDistribution> areaProcessDistributionList;

    @ApiModel("区域情况")
    @Data
    public static class AreaProcessDistribution {

        private String areaCode;

        private String areaName;

        private List<ProcessDateDistribution> processDateDistributionList;

    }

    @EqualsAndHashCode(callSuper = true)
    @ApiModel("区域病例数统计")
    @Data
    public static class ProcessDateDistribution extends CommonDateStatVO {

        @ApiModelProperty(value = "病例数")
        private Integer processCount = 0;

    }

}
