package com.iflytek.cdc.province.model.multichannelMonitor.dto;

import com.alibaba.nacos.api.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collections;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class CollaborMonitorQueryDTO extends MultichannelQueryDTO {

    @ApiModelProperty(value = "药品大类（抗菌、抗病毒等）")
    private String drugType;
    
    @ApiModelProperty(value = "监测关键词")
    private String keyword;

    @ApiModelProperty("专题id")
    private String topicId;

    @ApiModelProperty("指数类型（搜索指数、资讯指数等）")
    private String indexClass;

    @ApiModelProperty("预警场景类型")
    private String warningScenarioType;
    @ApiModelProperty("其他预警场景类型")
    private String otherWarningScenarioType;

    @ApiModelProperty(value = "人群分类", hidden = true)
    private List<String> personTypeList;

    public List<String> getKeywordList(){
        if (StringUtils.isBlank(keyword)){
            return null;
        }
        return Collections.singletonList(keyword);
    }

}
