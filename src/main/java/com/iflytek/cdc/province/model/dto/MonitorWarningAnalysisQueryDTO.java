package com.iflytek.cdc.province.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "监测预警分析查询入参")
public class MonitorWarningAnalysisQueryDTO extends AdsMsProcessReqDTO {

    /**
     * visitTypeName
     */
    @ApiModelProperty(value = "就诊类型")
    private String visitTypeName;

    /**
     * diagStartTime
     */
    @ApiModelProperty(value = "诊断起始日期")
    private Date diagStartTime;

    /**
     * diagEndTime
     */
    @ApiModelProperty(value = "诊断结束日期")
    private Date diagEndTime;

    /**
     * 24小时内是否报卡 1 是 0 否
     */
    @ApiModelProperty(value = "24小时内是否报卡")
    private String hasReportInDay;

    /**
     * 就诊科室
     */
    @ApiModelProperty(value = "就诊科室")
    private String deptName;

    /**
     * 诊断类型
     */
    @ApiModelProperty(value = "诊断类型")
    private String diagTypeName;

    /**
     * 病例分类
     */
    @ApiModelProperty("病例分类")
    private String casesCategory;

    /**
     * 人群分类
     */
    @ApiModelProperty("人群分类")
    private String personType;

    /**
     * 报卡起始日期
     */
    @ApiModelProperty(value = "报卡起始日期")
    private Date repStartTime;

    /**
     * 报卡结束日期
     */
    @ApiModelProperty(value = "报卡结束日期")
    private Date repEndTime;

    /**
     * 已选id列表 - 用于应排查任务统计导出
     */
    @ApiModelProperty(value = "已选id列表")
    private List<String> checkedIdList;

    @ApiModelProperty(value = "传染病种类")
    private List<String> infectTypeList;

    /**
     *  应排查任务: invest_task; 预警信号: warning_signal; 任务督办: task_supervise
     */
    @ApiModelProperty(value = "应排查任务: invest_task; 预警信号: warning_signal; 任务督办: task_supervise")
    private String reportType;

    @ApiModelProperty(value = "排查结果")
    private String checkResult;

    @ApiModelProperty(value = "严重程度")
    private String clinicalSeverity;

    @ApiModelProperty(value = "补卡结束时间")
    private Date supReportEndTime;

    @ApiModelProperty(value = "补卡开始时间")
    private Date supReportStartTime;

    @ApiModelProperty(value = "推送开始时间")
    private Date taskPushStartTime;

    @ApiModelProperty(value = "推送结束时间")
    private Date taskPushEndTime;


    @ApiModelProperty("报告所在省")
    private String reportProvinceCode;

    @ApiModelProperty( "报告所在市")
    private String reportCityCode;

    @ApiModelProperty( "报告所在县区")
    private String reportDistrictCode;

    @ApiModelProperty("病人所在省")
    private String patientProvinceCode;

    @ApiModelProperty( "病人所在市")
    private String patientCityCode;

    @ApiModelProperty( "病人所在县区")
    private String patientDistrictCode;

}

