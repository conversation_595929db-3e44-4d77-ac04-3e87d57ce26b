package com.iflytek.cdc.province.model.multichannelMonitor.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ClueMonitorQueryDTO extends MultichannelQueryDTO {

    @ApiModelProperty(value = "线索ID")
    private String clueId;

    @ApiModelProperty(value = "监测关键词")
    private String keyword;

    @ApiModelProperty(value = "类型")
    private String clueType;

    @ApiModelProperty(value = "信息来源")
    private String clueSource;

    public List<String> getKeywordList(){
        return Collections.singletonList(keyword);
    }


}
