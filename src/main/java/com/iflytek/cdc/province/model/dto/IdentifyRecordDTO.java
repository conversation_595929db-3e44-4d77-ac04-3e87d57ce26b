package com.iflytek.cdc.province.model.dto;

import com.iflytek.cdc.province.enums.ProcessInfoTypeEnum;
import lombok.Data;

import java.util.Objects;

/**
 * 病程病历记录识别dto
 * */
@Data
public class IdentifyRecordDTO {

    private String modelId;
    private String diseaseType;
    private String infectProcessId; // 数仓历史问题
    private String syndromeProcessId; // 数仓历史问题
    private String processId; // 根据diseaseType转化

    public void buildProcessId(){
        if (Objects.equals(ProcessInfoTypeEnum.INFECTED.getCode(), this.diseaseType)) {
            this.processId = this.infectProcessId;
        }
        if (Objects.equals(ProcessInfoTypeEnum.SYNDROME.getCode(), this.diseaseType)) {
            this.processId = this.syndromeProcessId;
        }
    }

}
