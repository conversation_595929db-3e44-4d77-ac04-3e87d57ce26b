package com.iflytek.cdc.province.model.epidemiology;

import com.iflytek.cdc.edr.utils.DataUtils;
import com.iflytek.cdc.province.utils.MathUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "患者就诊情况VO")
public class PatientVisitSituationVO {


    /**
     * 就诊类别
     */
    @ApiModelProperty(value = "就诊类别")
    private String visitTypeName;

    /**
     * 就诊科室
     */
    @ApiModelProperty(value = "就诊科室")
    private String deptName;

    /**
     * 患者数量
     */
    @ApiModelProperty(value = "患者数量")
    private Integer patientCnt;

    /**
     * 入院患者数量
     */
    @ApiModelProperty(value = "入院患者数量")
    private Integer admissCnt;

    /**
     * 非危重症患者数量
     */
    @ApiModelProperty(value = "非危重症患者数量")
    private Integer notCriticalCnt;

    /**
     * 危重症未死亡患者数量
     */
    @ApiModelProperty(value = "危重症未死亡患者数量")
    private Integer criticalNotDeadCnt;

    /**
     * 死亡患者数量
     */
    @ApiModelProperty(value = "死亡患者数量")
    private Integer deathCnt;

    /**
     * 稳定患者数量
     */
    @ApiModelProperty(value = "稳定患者数量")
    private Integer stableCnt;

    /**
     * 治愈患者数量
     */
    @ApiModelProperty(value = "治愈患者数量")
    private Integer cureCnt;

    /**
     * 恶化患者数量
     */
    @ApiModelProperty(value = "恶化患者数量")
    private Integer worsenCnt;

    /**
     * 好转患者数量
     */
    @ApiModelProperty(value = "好转患者数量")
    private Integer improveCnt;

    /**
     * 其他患者数量
     */
    @ApiModelProperty(value = "其他患者数量")
    private Integer otherCnt;

    /**
     * 稳定率
     */
    @ApiModelProperty(value = "稳定率")
    private Double stableRate;

    /**
     * 治愈率
     */
    @ApiModelProperty(value = "治愈率")
    private Double cureRate;

    /**
     * 恶化率
     */
    @ApiModelProperty(value = "恶化率")
    private Double worsenRate;

    /**
     * 好转率
     */
    @ApiModelProperty(value = "好转率")
    private Double improveRate;

    /**
     * 死亡率
     */
    @ApiModelProperty(value = "死亡率")
    private Double deathRate;


    /**
     * 非危重症患者数量
     */
    @ApiModelProperty(value = "非危重症患者数量")
    private Double notCriticalRate;

    /**
     * 危重症未死亡患者数量
     */
    @ApiModelProperty(value = "危重症未死亡患者数量")
    private Double criticalNotDeadRate;


    /**
     * 其他比率
     */
    @ApiModelProperty(value = "其他比率")
    private Double otherRate;


    /**
     * 计算所有比率
     */
    public void calculateAllRates() {
        this.setStableRate(MathUtil.div(this.getStableCnt(), this.getAdmissCnt(), 4));
        this.setCureRate(MathUtil.div(this.getCureCnt(), this.getAdmissCnt(), 4));
        this.setDeathRate(MathUtil.div(this.getDeathCnt(), this.getAdmissCnt(), 4));
        this.setImproveRate(MathUtil.div(this.getImproveCnt(), this.getAdmissCnt(), 4));
        this.setWorsenRate(MathUtil.div(this.getWorsenCnt(), this.getAdmissCnt(), 4));
        this.setCriticalNotDeadRate(MathUtil.div(this.getCriticalNotDeadCnt(), this.getAdmissCnt(), 4));
        this.setNotCriticalRate(MathUtil.div(this.getNotCriticalCnt(), this.getAdmissCnt(), 4));
        this.setOtherRate(MathUtil.div(this.getOtherCnt(), this.getAdmissCnt(), 4));
    }



}
