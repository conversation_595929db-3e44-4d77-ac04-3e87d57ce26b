package com.iflytek.cdc.province.model.pathogen;

import com.iflytek.cdc.province.model.dto.DateDimQueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("流行病学分析请求参数")
public class EpidemiologyReqDTO extends DateDimQueryParam  {

    /**
     * 时间类型
     */
    @ApiModelProperty("时间查询类型: 病例首次识别报告时间-identifyTime; 检测报告时间-reportTime; 发病时间-onsetTime")
    private String timeType;

    /**
     * 区域类型
     */
    @ApiModelProperty("区域查询类型: 病例现住址-livingAddress; 首次监测报告机构-orgAddress")
    private String addressType;

    /**
     * 入院出院标志(admiss入院,leave出院)
     */
    @ApiModelProperty(value = "入院出院标志(admiss入院,leave出院)")
    private String inOutFlag;

    /**
     * 症候群编码
     */
    @ApiModelProperty(value = "症候群编码")
    private String syndromeCode;

    /**
     * 症候群名称
     */
    @ApiModelProperty(value = "症候群名称")
    private String syndromeName;


}
