package com.iflytek.cdc.province.model.epidemiology;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 转重症死亡病例
 */
@Data
public class CritiaclDeathProcessVO {

    /**
     * 所属区域
     */
    @ApiModelProperty(value = "所属区域")
    private String areaName;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String orgName;

    /**
     * 患者姓名
     */
    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    /**
     * 就诊科室
     */
    @ApiModelProperty(value = "就诊科室")
    private String deptName;

    /**
     * 诊断名称
     */
    @ApiModelProperty(value = "诊断名称")
    private String diagName;

    /**
     * 传染病类型
     */
    @ApiModelProperty(value = "传染病类型")
    private String diseaseName;

    /**
     * 转归情况
     */
    @ApiModelProperty(value = "转归情况")
    private String outcomeStatusName;

}
