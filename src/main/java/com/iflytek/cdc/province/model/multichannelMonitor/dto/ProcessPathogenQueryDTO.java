package com.iflytek.cdc.province.model.multichannelMonitor.dto;

import com.iflytek.cdc.province.model.dto.DateDimQueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "病原查询条件")
public class ProcessPathogenQueryDTO extends DateDimQueryParam {

    @ApiModelProperty("专题id")
    private String topicId;

    @ApiModelProperty(value = "是否肺炎: 1-是; 0-否; 不传查全部")
    private String isPneumonia;
    
    @ApiModelProperty(value = "病原类型")
    private List<String> pathogenClassCode;

    @ApiModelProperty(value = "病原id列表")
    private List<String> pathogenIdList;

    @ApiModelProperty(value = "所有病原集合 (根据pathogenIdList查询所有子类) ", hidden = true)
    private List<String> allPathogenNameList;

    @ApiModelProperty(value = "对比维度")
    private String compareDimType;

    @ApiModelProperty(value = "排序指标")
    private String orderIndex;

    @ApiModelProperty("区域查询类型 病例现住址-livingAddress;监测单位-orgAddress")
    private String addressType;

    @ApiModelProperty("时间查询类型  系统报告时间-identifyTime; 报告审核时间-approveTime; 病例发病时间-onsetTime")
    private String timeType;

    @ApiModelProperty("预警场景类型")
    private String warningScenarioType;
    @ApiModelProperty("其他预警场景类型")
    private String otherWarningScenarioType;

    @ApiModelProperty(value = "人群分类", hidden = true)
    private List<String> personTypeList;

    @ApiModelProperty("病程id")
    private List<String> ids;
}
