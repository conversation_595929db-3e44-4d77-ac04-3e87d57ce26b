package com.iflytek.cdc.province.model.multichannelMonitor.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class IndexQueryDto extends MultichannelQueryDTO {

    @ApiModelProperty(value = "监测关键词")
    private String keyword;

    @ApiModelProperty("指数类型（搜索指数、资讯指数等）")
    private String indexClass;
}
