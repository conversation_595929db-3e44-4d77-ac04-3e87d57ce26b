package com.iflytek.cdc.province.model.multichannelMonitor.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class IncidentCaseNoticeVO {

    @ApiModelProperty(value = "日期")
    private Date curr;

    @ApiModelProperty(value = "本期数量")
    private Integer currCount;

    @ApiModelProperty(value = "同期数量")
    private Integer contemporaryCount;
}
