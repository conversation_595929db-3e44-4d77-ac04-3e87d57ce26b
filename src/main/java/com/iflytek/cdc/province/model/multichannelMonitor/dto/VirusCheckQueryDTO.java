package com.iflytek.cdc.province.model.multichannelMonitor.dto;

import com.iflytek.cdc.province.model.dto.DateDimQueryParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class VirusCheckQueryDTO extends DateDimQueryParam {

    @ApiModelProperty(value = "病原分类")
    private List<String> pathogenClassCode;

    @ApiModelProperty(value = "实验室id集合")
    private List<String> labIdList;

    @ApiModelProperty(value = "病原id集合")
    private List<String> pathogenIdList;
    
    @ApiModelProperty(value = "所有病原集合 (根据pathogenIdList查询所有子类) ", hidden = true)
    private List<String> allPathogenNameList;

    @ApiModelProperty(value = "病毒分型")
    private List<String> virusType;

    @ApiModelProperty(value = "参考病毒分型")
    private List<String> referAntigenId;

    @ApiModelProperty(value = "采样开始时间")
    private Date sampleStartDate;

    @ApiModelProperty(value = "采样结束时间")
    private Date sampleEndDate;

    @ApiModelProperty("专题id")
    private String topicId;

    @ApiModelProperty(value = "病原name集合")
    private List<String> pathogenNameList;

    /**
     * 病程类型
     *
     * @see com.iflytek.cdc.province.enums.PathogenProcessTypeEnum
     */
    private String processType;

    @ApiModelProperty("区域查询类型 病例现住址-livingAddress;监测单位-orgAddress")
    private String addressType;

    @ApiModelProperty("预警场景类型")
    private String warningScenarioType;
    @ApiModelProperty("其他预警场景类型")
    private String otherWarningScenarioType;

    @ApiModelProperty(value = "人群分类", hidden = true)
    private List<String> personTypeList;

}
