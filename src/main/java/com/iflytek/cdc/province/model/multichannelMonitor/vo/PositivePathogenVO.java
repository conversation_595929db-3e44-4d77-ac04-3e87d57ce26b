package com.iflytek.cdc.province.model.multichannelMonitor.vo;

import cn.hutool.core.lang.Pair;
import com.iflytek.cdc.province.model.pathogen.PathogenNameGroupVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PositivePathogenVO {

    @ApiModelProperty(value = "病原id")
    private String pathogenId;

    @ApiModelProperty(value = "病原名称")
    private String pathogenName;

    @ApiModelProperty(value = "是否肺炎: 1-是; 0-否;")
    private String isPneumonia;

    @ApiModelProperty(value = "是否高热: 1-是; 0-否;")
    private String isFever;

    @ApiModelProperty(value = "肺炎病例数")
    private String pneumoniaCount;

    @ApiModelProperty(value = "非肺炎病例数")
    private String notPneumoniaCount;

    @ApiModelProperty(value = "高热病例数")
    private String feverCount;

    @ApiModelProperty(value = "非高热病例数")
    private String notFeverCount;

    @ApiModelProperty(value = "该病原检测病例数")
    private Integer detectCount;

    @ApiModelProperty(value = "阳性病例数")
    private Integer positiveCount;

    @ApiModelProperty(value = "阳性率")
    private Double positiveRate;

    @ApiModelProperty(value = "去年同期该病原检测病例数")
    private Integer detectCountLastY;

    @ApiModelProperty(value = "去年同期阳性病例数")
    private Integer positiveCountLastY;

    @ApiModelProperty(value = "去年同期阳性率")
    private Double positiveRateLastY;

    @ApiModelProperty(value = "阳性率同比")
    private Double positiveRatioGrowth;

    public Pair<String, String> getPneumoniaPair() {
        return new Pair<>(pathogenName, isPneumonia);
    }

    public Pair<String, String> getFeverPair() {
        return new Pair<>(pathogenName, isFever);
    }

    public static PositivePathogenVO of(PathogenNameGroupVO group) {
        group.calculateAllRates();

        PositivePathogenVO vo = new PositivePathogenVO();
        vo.setPathogenName(group.getPathogenName());
        vo.setDetectCount(group.getProcessTestCnt());
        vo.setDetectCountLastY(group.getProcessTestCntLastY());
        vo.setPositiveCount(group.getProcessPositiveCnt());
        vo.setPositiveCountLastY(group.getProcessPositiveCntLastY());
        vo.setPositiveRate(group.getProcessPositiveRate());
        vo.setPositiveRateLastY(group.getProcessPositiveRateLastY());
        vo.setPositiveRatioGrowth(group.getProcessPositiveYearGrowth());

        return vo;
    }
}
