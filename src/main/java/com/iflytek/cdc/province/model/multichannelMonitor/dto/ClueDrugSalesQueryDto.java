package com.iflytek.cdc.province.model.multichannelMonitor.dto;

import cn.hutool.core.date.DateTime;
import com.iflytek.cdc.province.entity.dim.AdsMonitorClueInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class ClueDrugSalesQueryDto extends DrugSalesQueryDto {

    @ApiModelProperty("线索ID")
    private String clueId;

    @ApiModelProperty("类型")
    private String type;

    public void mergeClueParam(AdsMonitorClueInfo clueInfo, Date startDate, Date endDate) {
        if (clueInfo != null) {
            if (StringUtils.isBlank(getKeyword())) {
                setKeyword(clueInfo.getKeyWord());
            }
            if (getStartDate() == null) {
                setStartDate(startDate);
            }
            if (getEndDate() == null) {
                setEndDate(endDate);
            }
            if (StringUtils.isBlank(getProvinceCode())) {
                setProvinceCode(clueInfo.getProvinceCode());
            }
            if (StringUtils.isBlank(getCityCode())) {
                setCityCode(clueInfo.getCityCode());
            }
            if (StringUtils.isBlank(getDistrictCode())) {
                setDistrictCode(clueInfo.getDistrictCode());
            }
        }
    }
}
