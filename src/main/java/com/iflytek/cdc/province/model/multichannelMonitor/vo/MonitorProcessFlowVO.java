package com.iflytek.cdc.province.model.multichannelMonitor.vo;

import com.iflytek.cdc.edr.vo.CommonDateStatVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 监测病例流向
 * */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "监测病例流向")
public class MonitorProcessFlowVO extends CommonDateStatVO {

    @ApiModelProperty(value = "起始")
    private String source;

    @ApiModelProperty(value = "起始")
    private String sourceCode;
    
    @ApiModelProperty(value = "目标")
    private String target;

    @ApiModelProperty(value = "起始")
    private String targetCode;

    @ApiModelProperty(value = "起始->目标 映射的数量")
    private Integer value;
    
}
