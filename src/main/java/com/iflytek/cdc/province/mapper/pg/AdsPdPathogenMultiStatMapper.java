package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.model.pathogen.PathogenInfectionSituationVO;
import com.iflytek.cdc.province.model.pathogen.PathogenAnalysisReqDTO;
import com.iflytek.cdc.province.model.pathogen.PathogenMultiClassStatVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 病原复合感染统计Mapper
 */
@Mapper
public interface AdsPdPathogenMultiStatMapper {

    /**
     * 按病原体名称分组统计复合感染情况
     *
     * @param req 查询参数
     * @return 统计结果
     */
    List<PathogenMultiClassStatVO> groupMultiClassByName(PathogenAnalysisReqDTO req);

    /**
     * 按病原体名称和是否肺炎分组统计复合感染情况
     *
     * @param req 查询参数
     * @return 统计结果，区分肺炎和非肺炎
     */
    List<PathogenInfectionSituationVO> getInfectionSituation(PathogenAnalysisReqDTO req);
}
