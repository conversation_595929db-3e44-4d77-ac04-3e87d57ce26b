package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.entity.bu.TbCdcdmDataAttr;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TbCdcdmDataAttrMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcdmDataAttr record);

    int insertSelective(TbCdcdmDataAttr record);

    TbCdcdmDataAttr selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcdmDataAttr record);

    int updateByPrimaryKey(TbCdcdmDataAttr record);
}