package com.iflytek.cdc.province.mapper.bu;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.model.pandemic.dto.CorrectionRecordQueryDTO;
import com.iflytek.cdc.province.entity.bu.TbCdcewCorrectionRecord;
import com.iflytek.cdc.province.model.pandemic.vo.CorrectionRecordVO;
import com.iflytek.cdc.province.model.pandemic.vo.CorrectFieldVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TbCdcewCorrectionRecordMapper extends BaseMapper<TbCdcewCorrectionRecord> {

    void saveCaseCorrection(TbCdcewCorrectionRecord tbCdcewCorrectionRecord);

    /**
     * 批量插入订正记录
     * */
    void saveBatchCorrection(List<TbCdcewCorrectionRecord> recordList);

    /**
     * 查询订正字段
     * */
    List<String> getCorrectionFieldList(CorrectionRecordQueryDTO dto);

    /**
     * 查询订正记录
     * */
    List<CorrectionRecordVO> getCorrectionRecordList(CorrectionRecordQueryDTO dto);
    
    /**
     * 根据报卡id查询报卡订正记录
     * */
    List<CorrectionRecordVO> getCorrectionRecordByReportId(@Param("diseaseType") String diseaseType,
                                                           @Param("reportId") String reportId);

    /**
     * 订正统计 - 订正字段排序
     * */
    List<CorrectFieldVO> getCorrectFieldRanking(CorrectionRecordQueryDTO dto);

    /**
     * 订正统计 - 字段订正统计
     * */
    List<CorrectFieldVO> getCorrectFieldStat(CorrectionRecordQueryDTO dto);

}
