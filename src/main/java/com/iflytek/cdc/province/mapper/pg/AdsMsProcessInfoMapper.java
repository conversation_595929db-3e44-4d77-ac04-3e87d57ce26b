package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.edr.vo.adsMsProcess.*;
import com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo;
import com.iflytek.cdc.province.model.dto.AdsMsProcessReqDTO;
import com.iflytek.cdc.province.model.dto.MsProcessLogQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoConditionQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.pandemic.dto.MedicalQueryDTO;
import com.iflytek.cdc.edr.vo.dm.ProcessRecordVO;
import com.iflytek.cdc.province.model.pandemic.dto.PersonProcessQueryDTO;
import com.iflytek.cdc.province.model.pandemic.vo.PersonProcessInfo;
import com.iflytek.cdc.province.model.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsMsProcessInfoMapper {
    /**
     * 传染病病例数指标
     */
    List<MedCntIndicatorVO> listInfectedMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 传染病转归数指标
     */
    List<MedCntIndicatorVO> listInfectedOutcomeIndicator(MsProcessSimpleInfoQueryDTO queryDTO);


    /**
     * 区域分组传染病病例数指标
     */
    List<AreaMedCntIndicatorVO> listInfectedAreaMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 区域分组传染病转归数指标
     */
    List<AreaMedCntIndicatorVO> listInfectedAreaOutcomeIndicator(MsProcessSimpleInfoQueryDTO queryDTO);


    /**
     * 症候群病例数指标
     */
    List<MedCntIndicatorVO> listSyndromeMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 症候群转归数指标
     */
    List<MedCntIndicatorVO> listSyndromeOutcomeIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 区域分组症候群病例数指标
     */
    List<AreaMedCntIndicatorVO> listSyndromeAreaMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 区域分组症候群转归数指标
     */
    List<AreaMedCntIndicatorVO> listSyndromeAreaOutcomeIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    List<ProcessRecordVO> getInfectedMedicalList(MedicalQueryDTO medicalQueryDTO);

    List<ProcessRecordVO> getSyndromeProcessListBy(MedicalQueryDTO medicalQueryDTO);

    /**
     * 根据id查询地址相关信息
     */
    List<MsProcessInfoAddressVO> listInfectedAddressByIds(MsProcessSimpleInfoQueryDTO dto);
    
    List<MsProcessInfoAddressVO> listSyndromeAddressByIds(MsProcessSimpleInfoQueryDTO dto);

    /**
     * 根据id查询传染病简单病程信息
     */
    List<MsProcessSimpleInfoVO> listInfectedSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 根据id单条查询传染病简单病程信息
     */
    MsProcessSimpleInfoDetailVO loadInfectedSimpleInfo(String id);


    /**
     * 根据id查询症候群简单病程信息
     */
    List<MsProcessSimpleInfoVO> listSyndromeSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 根据id查询症候群简单病程信息
     */
    MsProcessSimpleInfoDetailVO loadSyndromeSimpleInfo(String id);

    /**
     * 传染病总计
     */
    Integer countInfected(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 症候群总计
     */
    Integer countSyndrome(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 根据病程id获取症候群受检者信息
     */
    List<MsPatientInfoVO> listSynPatientInfoByIds(MsProcessSimpleInfoQueryDTO dto);

    /**
     * 根据病程id获取传染病受检者信息
     */
    List<MsPatientInfoVO> listInfPatientInfoByIds(MsProcessSimpleInfoQueryDTO dto);

    /**
     * 症候群病例 重点场所以及监测症状信息
     */
    List<MsProcessMonitorInfoVO> listSyndromeProcessDetailInfo(MsProcessSimpleInfoQueryDTO dto);
    
    /**
     * 根据人的信息查询对应病例
     * */
    List<PersonProcessInfo> getProcessInfoByPerson(PersonProcessQueryDTO dto);

    /**
     * 查询传染病模型简易信息
     */
    List<InfectedProcessModelSimpleInfo> listInfectedModelSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO);


    /**
     * 查询症候群模型简易信息
     */
    List<SyndromeProcessModelSimpleInfo> listSyndromeModelSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 传染病病程时间轴
     */
    List<MsProcessLogVO> listInfectedProcessLog(MsProcessLogQueryDTO queryDTO);

    /**
     * 症候群病程时间轴
     */
    List<MsProcessLogVO> listSyndromeProcessLog(MsProcessLogQueryDTO queryDTO);

    /**
     * 症候群病程时间轴
     */
    String getMedicalByProcessId(@Param("diseaseType") String diseaseType,
                                 @Param("processId") String processId);

    /**
     * 根据id查询传染病病例
     * */
    List<TbCdcewEmergencyEventProcessInfo> getInfectedProcessInfoBy(@Param("ids") List<String> ids);

    /**
     * 根据id查询症候群病例
     * */
    List<TbCdcewEmergencyEventProcessInfo> getSyndromeProcessInfoBy(@Param("ids") List<String> ids);

    /**
     * 统计传染病病例的病原检测情况
     * */
    ProcessPathogenInfoVO getInfectedProcessPathogenInfoBy(@Param("ids") List<String> ids);

    /**
     * 统计症候群病例的病原检测情况
     * */
    ProcessPathogenInfoVO getSyndromeProcessPathogenInfoBy(@Param("ids") List<String> ids);

    List<MsProcessSimpleInfoVO> syndromeSimpleInfoPageListByConditions(MsProcessSimpleInfoConditionQueryDTO queryDTO);

    /**
     * 症候群机构病程统计
     */
    List<SyndromeOrgProcessStatsVO> listSyndromeOrgProcessStats(AdsMsProcessReqDTO queryDTO);

    /**
     * 查看新发突发传染病病例信息
     * */
    List<ProcessRecordVO> getEmergingMedicalList(MedicalQueryDTO dto);

    /**
     * 查看地方病病病例信息
     * */
    List<ProcessRecordVO> getEndemicMedicalList(MedicalQueryDTO dto);

    List<String> getMedicalDept();
}
