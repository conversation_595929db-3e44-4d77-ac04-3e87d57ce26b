package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.entity.bu.TbCdcewProcessReportCheckLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcewProcessReportCheckLogMapper {

    /**
     * 根据报卡id以及病例类型查询该报卡的历史审核记录
     * */
    List<TbCdcewProcessReportCheckLog> getCheckLogsByReportId(@Param("diseaseType") String diseaseType,
                                                              @Param("reportId") String reportId);

    /**
     * 查询报卡最新的一条审核记录
     * */
    TbCdcewProcessReportCheckLog getLatestCheckLogByReportId(@Param("diseaseType") String diseaseType,
                                                             @Param("reportId") String reportId);

    /**
     * 新增数据
     *
     * @param tbCdcewProcessReportCheckLog 实例对象
     * @return 影响行数
     */
    int insert(TbCdcewProcessReportCheckLog tbCdcewProcessReportCheckLog);

}
