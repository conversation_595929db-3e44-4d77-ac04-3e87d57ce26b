package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.model.dto.DateDim;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface DimDateMapper {


    List<DateDim> day(@Param("startDate") Date startDate,
                      @Param("endDate") Date endDate );

    List<DateDim> yearAndWeek(@Param("startDate") Date startDate,
                               @Param("endDate") Date endDate );

    List<DateDim> yearAndMonth(@Param("startDate") Date startDate,
                               @Param("endDate") Date endDate );

    List<DateDim> yearAndMonthAndTenDays(@Param("startDate") Date startDate,
                                         @Param("endDate") Date endDate );

    List<DateDim> yearAndQuarter(@Param("startDate") Date startDate,
                                 @Param("endDate") Date endDate );

    List<DateDim> year(@Param("startDate") Date startDate,
                                 @Param("endDate") Date endDate );

}
