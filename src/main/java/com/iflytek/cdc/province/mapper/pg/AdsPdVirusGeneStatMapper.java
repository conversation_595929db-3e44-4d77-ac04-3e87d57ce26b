package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.model.multichannelMonitor.dto.VirusCheckQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.VirusAntigenResultVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 检出病原体-基因测序信息
 */
@Mapper
public interface AdsPdVirusGeneStatMapper {

    /**
     * 多渠道态势感知 - 流感病毒抗原性分析结果
     */
    List<VirusAntigenResultVO> getAntigenAnalysisResult(VirusCheckQueryDTO queryDTO);
    
    /**
     * 流感病毒抗原性分析-分型
     */
    List<VirusAntigenResultVO> getInfluenzaPathogenType();
    
    /**
     * 流感病毒参考株分析
     */
    List<VirusAntigenResultVO> getInfluenzaReferPathogen();
}
