package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.entity.ads.AdsRepInfectReportInfo;
import com.iflytek.cdc.province.model.vo.edrcase.AdsRepInfectReportInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsRepInfectReportInfoMapper extends BaseMapper<AdsRepInfectReportInfo> {

    /**
     * 根据empiId查询报卡信息
     * @param empiId
     * @return
     */
    List<AdsRepInfectReportInfoVO> findReportByempiIdList(@Param("empiId") String empiId);
}