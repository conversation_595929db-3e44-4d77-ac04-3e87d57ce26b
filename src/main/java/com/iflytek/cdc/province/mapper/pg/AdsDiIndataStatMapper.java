package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.model.dto.DiIndataQueryParam;
import com.iflytek.cdc.edr.vo.DiIndataStatVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AdsDiIndataStatMapper {
    /**
     * 获取最后的统计
     */
    DiIndataStatVO getLastCount(@Param("startDate") Date startDate,
                                @Param("endDate") Date endDate,
                                DiIndataQueryParam queryParam);

    /**
     * 查询统计最大日期
     * */
    Date getLastStatDate(DiIndataQueryParam queryParam);

    /**
     * 数据统计 根据时间类型查询不同维度
     * */
    List<DiIndataStatVO> dataStat(DiIndataQueryParam queryParam);

    /**
     * 日统计
     */
    List<DiIndataStatVO> dayStat(DiIndataQueryParam queryParam);

    /**
     * 周统计
     */
    List<DiIndataStatVO> weekStat(DiIndataQueryParam queryParam);

    /**
     * 旬统计
     */
    List<DiIndataStatVO> meadowStat(DiIndataQueryParam queryParam);

    /**
     * 月统计
     */
    List<DiIndataStatVO> monthStat(DiIndataQueryParam queryParam);

    /**
     * 季度统计
     */
    List<DiIndataStatVO> quarterStat(DiIndataQueryParam queryParam);

    /**
     * 年统计
     */
    List<DiIndataStatVO> yearStat(DiIndataQueryParam queryParam);

}
