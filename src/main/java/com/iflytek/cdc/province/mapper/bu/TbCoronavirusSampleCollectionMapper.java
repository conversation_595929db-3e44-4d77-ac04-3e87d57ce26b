package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.entity.bu.TbCoronavirusSampleCollection;
import com.iflytek.cdc.province.model.dto.CoronavirusSampleQueryDTO;
import com.iflytek.cdc.province.model.vo.CoronavirusSampleCollectionVO;

import java.util.List;

public interface TbCoronavirusSampleCollectionMapper {


    /**
     *  查列表
     */
    List<TbCoronavirusSampleCollection> selectAllList(CoronavirusSampleQueryDTO queryDto);


    /**
     * 亚型数量分组统计
     */
    List<CoronavirusSampleCollectionVO> groupSubtype();


    /**
     * 统计总数量
     */
    Integer countTotal();


}
