package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrLisInfoVO;
import com.iflytek.cdc.province.entity.ads.AdsEdrLisInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 实验室检测信息
 */
public interface AdsEdrLisInfoMapper extends BaseMapper<AdsEdrLisInfo> {

    /**
     * 根据事件id获取实验室检测信息
     * @param eventId
     * @return
     */
    List<AdsEdrLisInfoVO> getLisByEventId(@Param("eventId") String eventId);

}