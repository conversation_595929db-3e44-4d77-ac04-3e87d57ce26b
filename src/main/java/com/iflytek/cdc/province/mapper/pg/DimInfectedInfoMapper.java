package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.edr.dto.DimInfectedInfoQuery;
import com.iflytek.cdc.edr.vo.ConstantVO;
import com.iflytek.cdc.province.entity.dim.DimInfectedInfo;
import com.iflytek.cdc.reportcard.dto.ReportUploadQueryDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DimInfectedInfoMapper {

    /**
     * 传染病类别查询
     */
    List<ConstantVO> listInfectedClass();

    /**
     * 传染病类型查询
     */
    List<ConstantVO> listInfectedType(DimInfectedInfoQuery query);

    /**
     * 传染病疾病信息查询
     */
    List<ConstantVO> listInfectedInfoBy(DimInfectedInfoQuery query);

    /**
     * 查询所有传染病
     * */
    List<DimInfectedInfo> listAll();

    List<String> getInfectedCodeByNames(List<String> names);

    List<String> getInfectCodeList(ReportUploadQueryDTO queryDTO);
}
