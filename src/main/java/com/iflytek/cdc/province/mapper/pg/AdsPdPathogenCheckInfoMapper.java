package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.model.dto.PathogenCombinationQueryDTO;
import com.iflytek.cdc.province.model.pathogen.PathogenInfectionSituationVO;
import com.iflytek.cdc.province.model.vo.PathogenCheckVO;
import com.iflytek.cdc.province.model.vo.PatientMedicalInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 病原检测-病原检测详情
 */
@Mapper
public interface AdsPdPathogenCheckInfoMapper {

    /**
     * 查询患者病原检测信息
     */
    List<PatientMedicalInfoVO> getPatientPathogenList(@Param("patientId") String patientId);

    /**
     * 查询多渠道病例病原检测结果
     */
    List<PathogenCheckVO> listPositivePathogenCheckResult(PathogenCombinationQueryDTO dto);

    /**
     * 查询多渠道病例病原感染情况
     */
    List<PathogenInfectionSituationVO> getPathogenInfectionSituation(PathogenCombinationQueryDTO dto);
}
