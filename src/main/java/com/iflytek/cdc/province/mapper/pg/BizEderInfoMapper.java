package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.entity.ads.AdsBizEdrInfo;
import com.iflytek.cdc.province.model.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

@Mapper
public interface BizEderInfoMapper extends BaseMapper<AdsBizEdrInfo> {
    List<HashMap<String,Object>> getBizEdrInfoByNameAndCard(@Param("dtoList") List<HashMap<String, String>> dtoList);
}
