package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.entity.bu.TbCdcewProcessReportCard;
import com.iflytek.cdc.province.model.pandemic.dto.CheckTaskQueryDTO;
import com.iflytek.cdc.province.model.pandemic.vo.CheckTaskRecordVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcewProcessReportCardMapper {

    /**
     * 查看审核任务列表
     * */
    List<CheckTaskRecordVO> getCheckTaskList(CheckTaskQueryDTO dto);

    /**
     * 查看病程待审核任务
     * */
    CheckTaskRecordVO getProcessWaitCheckReport(CheckTaskQueryDTO dto);

    /**
     * 根据报卡id 查询审核报卡任务
     * */
    TbCdcewProcessReportCard getReportCardBy(@Param("diseaseType") String diseaseType,
                                             @Param("reportId") String reportId);
    
    /**
     * 报卡审核状态更新
     * */
    void updateReportBy(TbCdcewProcessReportCard reportCard);

    /**
     * 根据id以及类型查询记录
     * */
    List<CheckTaskRecordVO> getTaskRecordVOListByIdList(@Param("diseaseType") String diseaseType,
                                                        @Param("idList") List<String> idList);

}
