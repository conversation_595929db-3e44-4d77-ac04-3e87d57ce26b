package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.dm.FilterParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 病历查询
 *
 * <AUTHOR>
 * @date 2022-07-15 14:57
 */
@Repository("data_bu_data_model_base")
public interface DataModelBUBaseMapper {

    List<Map<String, Object>> selectDataBySql(String customSql);

    List<Map<String, Object>> selectDataDynamical(@Param("fieldColumns") List<String> fieldColumns,
                                                  @Param("table") String table,
                                                  @Param("filterParam") FilterParam filterParam);
}
