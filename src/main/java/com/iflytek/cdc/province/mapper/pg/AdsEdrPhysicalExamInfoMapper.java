package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrPhysicalExamInfoVO;
import com.iflytek.cdc.province.entity.ads.AdsEdrPhysicalExamInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsEdrPhysicalExamInfoMapper extends BaseMapper<AdsEdrPhysicalExamInfo> {


    /**
     * 根据事件id查询患者体格检查信息
     * @param eventId
     * @return
     */
    List<AdsEdrPhysicalExamInfoVO> getPhysicalExamByEventId(@Param("eventId") String eventId);
}