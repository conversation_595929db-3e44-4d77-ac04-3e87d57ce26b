package com.iflytek.cdc.province.mapper.pg;

import java.util.List;

import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoDetailVO;
import com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo;
import com.iflytek.cdc.province.model.dto.MsProcessLogQueryDTO;
import com.iflytek.cdc.province.model.dto.PathogenCombinationQueryDTO;
import com.iflytek.cdc.province.model.vo.*;
import org.apache.ibatis.annotations.Mapper;

import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface EmergingProcessInfoMapper {

    /**
     * 病例数指标
     */
    List<MedCntIndicatorVO> listMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 转归数指标
     */
    List<MedCntIndicatorVO> listOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 区域分组病例数指标
     */
    List<AreaMedCntIndicatorVO> listAreaMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 区域分组转归数指标
     */
    List<AreaMedCntIndicatorVO> listAreaOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 根据id查询地址相关信息
     */
    List<MsProcessInfoAddressVO> listAddressByIds(MsProcessSimpleInfoQueryDTO dto);

    /**
     * 查询新发突发模型简易信息
     */
    List<MsProcessSimpleInfoVO> listSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 根据id查询新发突发简单病程信息
     */
    MsProcessSimpleInfoDetailVO loadProcessSimpleById(String id);

    /**
     * 根据病程id获取传染病受检者信息
     */
    List<MsPatientInfoVO> listPatientInfoByProcessIds(MsProcessSimpleInfoQueryDTO dto);

    /**
     * 病例总计
     */
    Integer countEmerging(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 查询模型简易信息
     * */
    List<EmergingProcessModelSimpleInfo> listProcessModelSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 病程时间轴
     * */
    List<MsProcessLogVO> listProcessLog(MsProcessLogQueryDTO queryDTO);

    /**
     * 根据id查询病例
     * */
    List<TbCdcewEmergencyEventProcessInfo> getProcessInfoBy(@Param("ids") List<String> ids);

    /**
     * 统计病例的病原检测情况
     * */
    ProcessPathogenInfoVO getProcessPathogenInfoBy(@Param("ids") List<String> ids);

    /**
     * 查询新发突发病例病原检测结果
     * */
    List<PathogenCheckVO> listEmergingPathogenCheckResult(PathogenCombinationQueryDTO dto);

}
