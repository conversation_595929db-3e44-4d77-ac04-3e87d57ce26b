package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrHospitalInfoVO;
import com.iflytek.cdc.province.entity.ads.AdsEdrHospitalInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsEdrHospitalInfoMapper extends BaseMapper<AdsEdrHospitalInfo> {
    /**
     * 根据事件id获取edr患者出入院信息
     * @param eventId
     * @return
     */
    List<AdsEdrHospitalInfoVO> getHospitalByEventId(@Param("eventId") String eventId);

}