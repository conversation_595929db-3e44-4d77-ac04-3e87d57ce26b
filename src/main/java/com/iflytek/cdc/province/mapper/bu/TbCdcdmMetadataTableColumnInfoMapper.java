package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableColumnInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TbCdcdmMetadataTableColumnInfoMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcdmMetadataTableColumnInfo record);

    int insertSelective(TbCdcdmMetadataTableColumnInfo record);

    TbCdcdmMetadataTableColumnInfo selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcdmMetadataTableColumnInfo record);

    int updateByPrimaryKey(TbCdcdmMetadataTableColumnInfo record);

    TbCdcdmMetadataTableColumnInfo selectByTableAndBusiColName(@Param("tableCode") String tableCode, @Param("name") String name);

    List<TbCdcdmMetadataTableColumnInfo> getAllColumnInfo();

    List<TbCdcdmMetadataTableColumnInfo> getTableColumnInfoByTableCode(@Param("tableCode") String tableCode);
}