package com.iflytek.cdc.province.mapper.bu;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.entity.bu.TbCdcbrManualBriefRecord;
import com.iflytek.cdc.province.model.brief.ManualBriefQueryDTO;
import com.iflytek.cdc.province.model.brief.ManualBriefRecordVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 生成简报记录-DAO
 */
@Mapper
public interface TbCdcbrManualBriefRecordMapper extends BaseMapper<TbCdcbrManualBriefRecord> {

    /**
     * 查询简报记录列表
     *
     * @param queryParam 查询参数
     * @return 简报记录列表
     */
    List<ManualBriefRecordVO> queryList(ManualBriefQueryDTO queryParam);
}
