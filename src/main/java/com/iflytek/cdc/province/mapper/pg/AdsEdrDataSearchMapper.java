package com.iflytek.cdc.province.mapper.pg;

import java.util.List;

import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.edr.dto.EdrQueryDTO;
import com.iflytek.cdc.province.model.edr.vo.ClinicalInfo;
import com.iflytek.cdc.province.model.edr.vo.DiagnosisResultInfo;
import com.iflytek.cdc.province.model.edr.vo.ExaminationInfo;
import com.iflytek.cdc.province.model.edr.vo.ExposureHistoryInfo;
import com.iflytek.cdc.province.model.edr.vo.ImagingInfo;
import com.iflytek.cdc.province.model.edr.vo.LabTestInfo;
import com.iflytek.cdc.province.model.edr.vo.MedicalPatientDetailInfo;
import com.iflytek.cdc.province.model.edr.vo.MedicalPatientListInfo;
import com.iflytek.cdc.province.model.edr.vo.ResidenceHistoryInfo;
import com.iflytek.cdc.province.model.edr.vo.SymptomInfo;
import com.iflytek.cdc.province.model.edr.vo.TherapeuticDrugInfo;
import com.iflytek.cdc.province.model.edr.vo.TherapeuticDrugInfoDetail;
import com.iflytek.cdc.province.model.vo.EdrIdentityVO;
import com.iflytek.cdc.province.model.vo.PatientTransferInfoVO;

public interface AdsEdrDataSearchMapper {

    /**
     * EDR 患者信息分页列表接口
     */
    List<MedicalPatientListInfo> pageList(EdrQueryDTO queryDTO);

    /**
     * 根据patientId 查询
     */
    MedicalPatientDetailInfo loadByPatientId(String patientId);

    /**
     * 查询暴露史信息
     */
    List<ExposureHistoryInfo> listExposureHistoryInfoByPatientId(String patientId);

    /**
     * 查询旅居史信息
     */
    List<ResidenceHistoryInfo> listResidenceHistoryInfoByPatientId(String patientId);

    /**
     * 首次诊断信息
     */
    List<ClinicalInfo> listClinicalInfos(List<String> eventIds);

    /**
     * 查询症状信息
     */
    List<SymptomInfo> listSymptomInfoByEventIds(List<String> eventIds);

    /**
     * 体格检测信息
     */
    List<ExaminationInfo> listExaminationInfoByEventIds(List<String> eventIds);

    /**
     * 查询影像学信息
     */
    List<ImagingInfo> listImagingInfoByEventIds(List<String> eventIds);

    /**
     * 查询实验室检测信息
     */
    List<LabTestInfo> listLabTestInfoByEventIds(List<String> eventIds);

    /**
     * 确诊结果信息
     */
    List<DiagnosisResultInfo> listDiagnosisResultInfoByEventIds(List<String> eventIds);

    /**
     * 治疗用药信息
     */
    List<TherapeuticDrugInfo> listTherapeuticDrugInfoByEventIds(List<String> eventIds);

    /**
     * 治疗用药明细信息
     */
    List<TherapeuticDrugInfoDetail> listTherapeuticDrugInfoDetailByPatientId(List<String> eventIds);

    /**
     * 根据empiId查询个人档案标识信息
     * @param empiId EMPI ID
     * @param residentIdCard 身份证号
     * @return 个人档案标识信息
     */
    EdrIdentityVO findEdrIdentityByEmpiId(String empiId, String residentIdCard);

    /**
     * 查询出入院信息
     */
    List<PatientTransferInfoVO> listPatientTransferInfo(MsProcessSimpleInfoQueryDTO queryDTO);
}
