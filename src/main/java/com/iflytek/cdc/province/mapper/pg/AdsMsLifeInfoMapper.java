package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.model.dto.PatientQueryDTO;
import com.iflytek.cdc.province.model.vo.PatientListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsMsLifeInfoMapper {

    /**
     * 根据查询条件查询患者列表
     * */
    List<PatientListVO> getPatientList(PatientQueryDTO dto);

    /**
     * 根据记录id查询患者列表
     * */
    List<PatientListVO> getPatientListByRecordIds(@Param("recordIds") List<String> recordIds);
}
