package com.iflytek.cdc.province.mapper.pg;

import cn.hutool.core.date.DateUtil;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.CollaborMonitorQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.IndexQueryDto;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorKeywordVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.AdsPoIndexDailyVO;
import org.apache.ibatis.annotations.Mapper;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 舆情指数信息表表
 */
@Mapper
public interface AdsPoIndexInfoMapper {

    /**
     * 舆情事件分析-监测关键词搜索指数情况
     */
    List<MonitorKeywordVO> selectKeywordCountBy(CollaborMonitorQueryDTO queryDTO);

    /**
     * 协调监测数据池-搜索指数暴增（不带同期数据）
     */
    List<AdsPoIndexDailyVO> dailyIndex(IndexQueryDto queryDTO);

    /**
     * 通过两次查询 dailyIndex 获取同期数据
     *
     * @param queryDTO 查询条件
     * @return 带同期数据的指数查询结果
     */
    default List<AdsPoIndexDailyVO> dailyIndexWithLastYear(IndexQueryDto queryDTO) {
        final Date originalStartDate = queryDTO.getStartDate();
        final Date originalEndDate = queryDTO.getEndDate();
        // 本期
        List<AdsPoIndexDailyVO> currData = dailyIndex(queryDTO);
        // 同期
        queryDTO.setStartDate(DateUtil.offsetMonth(queryDTO.getStartDate(), -12));
        queryDTO.setEndDate(DateUtil.offsetMonth(queryDTO.getEndDate(), -12));
        List<AdsPoIndexDailyVO> lastYearData = dailyIndex(queryDTO);

        if (lastYearData.isEmpty()) {
            return currData;
        }
        final SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        Map<String, AdsPoIndexDailyVO> lastYearMap = lastYearData.stream()
                .collect(Collectors.toMap(vo -> vo.getKeyword() + format.format(vo.getDay()), Function.identity(), (v1, v2) -> v1));
        currData.forEach(curr -> {
            String lastKey = curr.getKeyword() + format.format(DateUtil.offsetMonth(curr.getDay(), -12));
            AdsPoIndexDailyVO last = lastYearMap.get(lastKey);
            if (last != null) {
                curr.setIndexLyValue(last.getIndexValue());
            }
        });
        // 还原配置
        queryDTO.setStartDate(originalStartDate);
        queryDTO.setEndDate(originalEndDate);
        return currData;
    }
}
