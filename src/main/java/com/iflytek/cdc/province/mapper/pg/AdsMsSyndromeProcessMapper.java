package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.model.dto.AdsMsProcessReqDTO;
import com.iflytek.cdc.edr.vo.adsMsProcess.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface AdsMsSyndromeProcessMapper {

    List<AdsMsProcessRespVO> findDistribution(AdsMsProcessReqDTO reqDTO);

    /**
     * 不同行政区疾病特征
     */
    List<AdsMsProcessRespVO> groupArea(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupAddrType(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupAge(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupSex(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupJob(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> overAll(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> areaChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> sexChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> ageChange(AdsMsProcessReqDTO reqDTO);

    /**
     * 症候群病例监测量总览统计
     */
    AdsMsProcessRespVO processStat(AdsMsProcessReqDTO reqDTO);

    /**
     * 时间趋势统计
     */
    List<AdsMsProcessRespVO> timeTrendStat(AdsMsProcessReqDTO reqDTO);


    List<AdsMsProcessRespVO> groupIdentifyClass(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupOutcomeStatus(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupSymptomFirst(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupHistoryBefore(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupPathogenResNominal(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupDiseaseName(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupAreaCurrentLevel(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupAreaDripDetail(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupDiseaseCodeAndName(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> qOQTrendChart(AdsMsProcessReqDTO reqDTO);

}
