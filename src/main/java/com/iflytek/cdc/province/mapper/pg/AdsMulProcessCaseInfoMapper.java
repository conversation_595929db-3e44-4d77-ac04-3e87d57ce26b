package com.iflytek.cdc.province.mapper.pg;


import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoDetailVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO;
import com.iflytek.cdc.edr.vo.dm.ProcessRecordVO;
import com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo;
import com.iflytek.cdc.province.model.dto.*;
import com.iflytek.cdc.province.model.pandemic.dto.MedicalQueryDTO;
import com.iflytek.cdc.province.model.pandemic.dto.PersonProcessQueryDTO;
import com.iflytek.cdc.province.model.pandemic.vo.PersonProcessInfo;
import com.iflytek.cdc.province.model.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@Mapper
public interface AdsMulProcessCaseInfoMapper {

    /**
     * 病例数指标
     */
    List<MedCntIndicatorVO> listMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 转归数指标
     */
    List<MedCntIndicatorVO> listOutcomeIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 区域分组病例数指标
     */
    List<AreaMedCntIndicatorVO> listAreaMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 区域分组转归数指标
     */
    List<AreaMedCntIndicatorVO> listAreaOutcomeIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    List<ProcessRecordVO> getMedicalList(MedicalQueryDTO medicalQueryDTO);

    /**
     * 根据id查询地址相关信息
     */
    List<MsProcessInfoAddressVO> listAddressByIds(MsProcessSimpleInfoQueryDTO dto);

    /**
     * 根据id查询简单病程信息
     */
    List<MsProcessSimpleInfoVO> listSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 根据id单条查询简单病程信息
     */
    MsProcessSimpleInfoDetailVO loadSimpleInfo(String id);

    /**
     * 总计
     */
    Integer countByProcessId(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 根据病程id获取受检者信息
     */
    List<MsPatientInfoVO> listPatientInfoByIds(MsProcessSimpleInfoQueryDTO dto);

    /**
     * 重点场所以及监测症状信息
     */
    List<MsProcessMonitorInfoVO> listSyndromeProcessDetailInfo(MsProcessSimpleInfoQueryDTO dto);

    /**
     * 根据人的信息查询对应病例
     * */
    List<PersonProcessInfo> getProcessInfoByPerson(PersonProcessQueryDTO dto);

    /**
     * 查询模型简易信息
     */
    List<InfectedProcessModelSimpleInfo> listModelSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 传染病病程时间轴
     */
    List<MsProcessLogVO> listInfectedProcessLog(MsProcessLogQueryDTO queryDTO);

    /**
     * 症候群病程时间轴
     */
    List<MsProcessLogVO> listSyndromeProcessLog(MsProcessLogQueryDTO queryDTO);

    /**
     * 统计病例的病原检测情况
     * */
    ProcessPathogenInfoVO getProcessPathogenInfoBy(@Param("ids") List<String> ids);

    List<MsProcessSimpleInfoVO> syndromeSimpleInfoPageListByConditions(MsProcessSimpleInfoConditionQueryDTO queryDTO);

    /**
     * 症候群机构病程统计
     */
    List<SyndromeOrgProcessStatsVO> listSyndromeOrgProcessStats(AdsMsProcessReqDTO queryDTO);

    List<String> getMedicalDept();

    /**
     * 查询病例病原检测结果
     */
    List<PathogenCheckVO> listPathogenCheckResult(PathogenCombinationQueryDTO dto);

    List<TbCdcewEmergencyEventProcessInfo> getProcessInfoBy(@Param("ids") List<String> ids);
}
