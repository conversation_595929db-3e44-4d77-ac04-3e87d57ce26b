package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelVersion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TbCdcdmDataModelVersionMapper {
    int deleteByPrimaryKey(String modelVersionId);

    int insert(TbCdcdmDataModelVersion record);

    int insertSelective(TbCdcdmDataModelVersion record);

    TbCdcdmDataModelVersion selectByPrimaryKey(String modelVersionId);

    int updateByPrimaryKeySelective(TbCdcdmDataModelVersion record);

    int updateByPrimaryKey(TbCdcdmDataModelVersion record);

    /**
     * 根据版本id查询对应的数据模型id
     * */
    String getModelIdByModelVersionId(@Param("modelVersionId") String modelVersionId);
}