package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.edr.vo.DiIndataStatVO;
import com.iflytek.cdc.province.entity.ads.AdsDiIndataStatTotal;
import com.iflytek.cdc.province.model.dto.DiIndataQueryParam;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AdsDiIndataStatTotalMapper {

    AdsDiIndataStatTotal getTotalPatientCount(@Param("statDate") Date statDate, @Param("param") DiIndataQueryParam param);

    Date getLastStatDate(@Param("param") DiIndataQueryParam param);

    List<DiIndataStatVO> patientStat(@Param("param") DiIndataQueryParam queryParam);

}