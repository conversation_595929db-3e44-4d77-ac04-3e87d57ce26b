package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.model.dto.MsMedicalQuery;
import com.iflytek.cdc.province.model.vo.PatientMedicalInfoVO;
import com.iflytek.cdc.province.model.vo.VisitPersonInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsMsMedicalInfoMapper {

    List<PatientMedicalInfoVO> getPatientMedicalList(@Param("patientId") String patientId);

    /**
     * 查询就诊人信息
     * */
    List<VisitPersonInfoVO> getVisitPersonDetailBy(MsMedicalQuery query);

    /**
     * 用event_id置换medical_id
     * */
    List<String> getMedicalIdByEventIdList(@Param("eventIdList") List<String> eventIdList);

}
