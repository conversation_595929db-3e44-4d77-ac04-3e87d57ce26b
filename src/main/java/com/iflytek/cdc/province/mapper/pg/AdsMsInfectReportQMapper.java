package com.iflytek.cdc.province.mapper.pg;


import com.iflytek.cdc.edr.vo.adsMsQualityEval.QualityEvalRespVO;
import com.iflytek.cdc.province.model.dto.AdsMsReportReqDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 诊疗(medical service)-传染病报告卡-监测业务质量（quality）- DAO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Mapper
public interface AdsMsInfectReportQMapper {

    List<QualityEvalRespVO> dayEvalStat(AdsMsReportReqDTO reqDTO);

}
