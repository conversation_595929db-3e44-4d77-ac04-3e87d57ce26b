package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrTreatmentDetailVO;
import com.iflytek.cdc.province.entity.ads.AdsEdrTreatmentDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 获取治疗药物详情信息
 */
public interface AdsEdrTreatmentDetailMapper extends BaseMapper<AdsEdrTreatmentDetail> {
    /**
     * 根据事件id获取治疗药物详情信息
     * @param eventId
     * @return
     */
    List<AdsEdrTreatmentDetailVO> getTreatmentDetaiByEventId(@Param("eventId") String eventId);

}