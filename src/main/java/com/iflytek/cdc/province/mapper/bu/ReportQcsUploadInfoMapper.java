package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.entity.bu.ReportQcsUploadInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Mapper
public interface ReportQcsUploadInfoMapper extends BaseMapper<ReportQcsUploadInfo> {

    List<ReportQcsUploadInfo> selectByMainRecordId(@Param("mainRecordId") String mainRecordId);
}
