package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.entity.bu.SyndromeVerifyRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 症候群核实记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Mapper
public interface SyndromeVerifyRecordMapper extends BaseMapper<SyndromeVerifyRecord> {

    /**
     * 根据ID查询
     */
    SyndromeVerifyRecord selectById(@Param("id") String id);

    /**
     * 根据ID列表查询
     */
    List<SyndromeVerifyRecord> selectByIds(@Param("ids") List<String> ids);

    /**
     * 插入单条记录
     */
    int insert(SyndromeVerifyRecord record);

    /**
     * 根据ID更新
     */
    int updateById(SyndromeVerifyRecord record);
} 