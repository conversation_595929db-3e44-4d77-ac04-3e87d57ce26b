package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.entity.ads.AdsMsMedicalInfo;
import com.iflytek.cdc.province.model.dto.MsMedicalQuery;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsMedicalVisitStatVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface AdsMsMedicalMapper {

    /**
     * 就诊人次概况
     */
    MsMedicalVisitStatVO medicalVisitStat(MsMedicalQuery queryParam);

    /**
     * 数据统计
     */
    List<MsMedicalVisitStatVO> dataStat(MsMedicalQuery queryParam);

    /**
     * 日统计
     */
    List<MsMedicalVisitStatVO> dayStat(MsMedicalQuery queryParam);

    /**
     * 周统计
     */
    List<MsMedicalVisitStatVO> weekStat(MsMedicalQuery queryParam);

    /**
     * 旬统计
     */
    List<MsMedicalVisitStatVO> meadowStat(MsMedicalQuery queryParam);

    /**
     * 月统计
     */
    List<MsMedicalVisitStatVO> monthStat(MsMedicalQuery queryParam);

    /**
     * 季度统计
     */
    List<MsMedicalVisitStatVO> quarterStat(MsMedicalQuery queryParam);

    /**
     * 年统计
     */
    List<MsMedicalVisitStatVO> yearStat(MsMedicalQuery queryParam);

    /**
     * 病历数据查询 TODO:待确定具体的业务数据字段
     */
    List<AdsMsMedicalInfo> listMsMedicalInfo(MsMedicalQuery queryParam);
}
