package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.dto.PathogenCombinationQueryDTO;
import com.iflytek.cdc.province.model.pathogen.PathogenInfectionSituationVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.PathogenPositiveAgeAndSexVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.VirusCheckResultVO;
import com.iflytek.cdc.province.model.pathogen.PathogenAnalysisReqDTO;
import com.iflytek.cdc.province.model.pathogen.PathogenTwofoldStatVO;
import com.iflytek.cdc.province.model.vo.AreaMedCntIndicatorVO;
import com.iflytek.cdc.province.model.vo.MedCntIndicatorVO;
import com.iflytek.cdc.province.model.vo.MsPatientInfoVO;
import com.iflytek.cdc.province.model.vo.PathogenCheckVO;

import java.util.List;

public interface AdsIntegratedProcessInfoMapper {

    List<MedCntIndicatorVO> listIntegratedMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    List<MsProcessInfoAddressVO> listIntegratedAddressByIds(MsProcessSimpleInfoQueryDTO dto);

    List<MsPatientInfoVO> listIntegratedPatientInfo(MsProcessSimpleInfoQueryDTO dto);

    List<MsProcessSimpleInfoVO> listIntegratedProcessInfo(MsProcessSimpleInfoQueryDTO queryDTO);

    List<AreaMedCntIndicatorVO> listIntegratedAreaMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    List<AreaMedCntIndicatorVO> listIntegrateAreaOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    Integer countIntegratedProcess(MsProcessSimpleInfoQueryDTO queryDTO);

    List<PathogenCheckVO> listIntegratedPathogenCheckResult(PathogenCombinationQueryDTO dto);

}
