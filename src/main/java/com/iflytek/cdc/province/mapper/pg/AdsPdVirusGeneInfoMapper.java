package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.model.multichannelMonitor.dto.VirusCheckQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.VirusResistanceResultVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 检出病原体-基因测序信息
 */
@Mapper
public interface AdsPdVirusGeneInfoMapper {

    /**
     * 多渠道态势感知 - 流感病毒耐药性分析结果明细
     */
    List<VirusResistanceResultVO> getResistanceAnalysisResult(VirusCheckQueryDTO queryDTO);
}
