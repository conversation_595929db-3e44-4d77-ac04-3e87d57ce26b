package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoDetailVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO;
import com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.dto.PathogenCombinationQueryDTO;
import com.iflytek.cdc.province.model.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsMulProcessInfoMapper {

    /**
     * 查询多渠道预警信号病例
     */
    List<MsProcessSimpleInfoVO> listMultichannelProcessInfo(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 查询多渠道预警信号病例
     */
    MsProcessSimpleInfoDetailVO loadMultichannelSimpleInfo(String id);

    /**
     * 病例总计
     */
    Integer countMultichannelProcess(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 查询多渠道病例地址信息
     */
    List<MsProcessInfoAddressVO> listMultichannelMsProcessInfoAddress(MsProcessSimpleInfoQueryDTO dto);

    /**
     * 查询多渠道病例病人信息
     */
    List<MsPatientInfoVO> listMultichannelPatientInfo(MsProcessSimpleInfoQueryDTO dto);

    /**
     * 多渠道病例数指标
     */
    List<MedCntIndicatorVO> listMultichannelMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 多渠道死亡数指标
     */
    List<MedCntIndicatorVO> listMultichannelOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 多渠道病例数指标
     */
    List<AreaMedCntIndicatorVO> listMultichannelAreaMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 多渠道死亡数指标
     */
    List<AreaMedCntIndicatorVO> listMultichannelAreaOutcomeCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    /**
     * 查询多渠道病例病原检测结果
     */
    List<PathogenCheckVO> listMultichannelPathogenCheckResult(PathogenCombinationQueryDTO dto);

    /**
     * 根据id查询多渠道病例
     * */
    List<TbCdcewEmergencyEventProcessInfo> getMultichannelProcessInfoBy(@Param("ids") List<String> ids);

    /**
     * 统计多渠道病例的病原检测情况
     * */
    ProcessPathogenInfoVO getMultichannelProcessPathogenInfoBy(@Param("ids") List<String> ids);

}
