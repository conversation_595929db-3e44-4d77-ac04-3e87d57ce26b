package com.iflytek.cdc.province.mapper.bu;

//import com.iflytek.cdc.edr.entity.bu.DmDataModelForm;
import com.iflytek.cdc.province.entity.bu.TbCdcdmDataModel;
import com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelForm;
import com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelVersion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TbCdcdmDataModelMapper {
    int deleteByPrimaryKey(String modelId);

    int insert(TbCdcdmDataModel record);

    int insertSelective(TbCdcdmDataModel record);

    TbCdcdmDataModel selectByPrimaryKey(String modelId);

    int updateByPrimaryKeySelective(TbCdcdmDataModel record);

    int updateByPrimaryKey(TbCdcdmDataModel record);

    TbCdcdmDataModelVersion getModelLatestVersion(@Param("modelId") String modelId, @Param("status") String status);

    List<TbCdcdmDataModelForm> getFormListByModelVersionId(@Param("modelVersionId") String modelVersionId);
}