package com.iflytek.cdc.province.mapper.pg;

import cn.hutool.core.date.DateUtil;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.CollaborMonitorQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.IncidentCaseNoticeVO;
import org.apache.ibatis.annotations.Mapper;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 传染病-官方通告详情-疫情传播数据信息表(来源-各省卫健委）
 */
@Mapper
public interface AdsPoInfectTransmitInfoMapper {

    /**
     * 输入病例公告情况（不含同期数据）
     */
    List<IncidentCaseNoticeVO> selectIncidentCountBy(CollaborMonitorQueryDTO queryDTO);

    /**
     * 通过两次查询 selectIncidentCountBy 获取同期数据
     */
    default List<IncidentCaseNoticeVO> selectIncidentCountWithLastYearBy(CollaborMonitorQueryDTO queryDTO) {
        final Date originalStartDate = queryDTO.getStartDate();
        final Date originalEndDate = queryDTO.getEndDate();
        // 本期
        List<IncidentCaseNoticeVO> currData = selectIncidentCountBy(queryDTO);
        // 同期
        queryDTO.setStartDate(DateUtil.offsetMonth(queryDTO.getStartDate(), -12));
        queryDTO.setEndDate(DateUtil.offsetMonth(queryDTO.getEndDate(), -12));
        List<IncidentCaseNoticeVO> lastYearData = selectIncidentCountBy(queryDTO);

        if (lastYearData.isEmpty()) {
            return currData;
        }
        final SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        Map<String, IncidentCaseNoticeVO> lastYearMap = lastYearData.stream()
                .collect(Collectors.toMap(vo -> format.format(vo.getCurr()), Function.identity(), (v1, v2) -> v1));
        currData.forEach(curr -> {
            String lastKey = format.format(DateUtil.offsetMonth(curr.getCurr(), -12));
            IncidentCaseNoticeVO last = lastYearMap.get(lastKey);
            if (last != null) {
                curr.setContemporaryCount(last.getCurrCount());
            }
        });
        // 还原配置
        queryDTO.setStartDate(originalStartDate);
        queryDTO.setEndDate(originalEndDate);
        return currData;
    }
}
