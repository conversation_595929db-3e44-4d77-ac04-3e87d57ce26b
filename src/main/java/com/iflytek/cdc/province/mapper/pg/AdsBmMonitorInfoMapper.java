package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.model.dto.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface AdsBmMonitorInfoMapper {

    List<MiFoOvitrapDTO> getMiFoOvitrapDTOList(@Param("relationTaskId") String relationTaskId);

    List<MiFoBreteauIndexDTO> getMiFoBreteauIndexDTOList(@Param("relationTaskId") String relationTaskId);

    List<MiFoDoubleMosquitoDTO> getMiFoDoubleMosquitoDTOList(@Param("relationTaskId") String relationTaskId);

    List<PdPopulationSampleDTO> getPopulationSampleDTOList(@Param("relationTaskId") String relationTaskId);

    List<PdObjectSampleDTO> getObjectSampleDTOList(@Param("relationTaskId") String relationTaskId);

    List<PdBiologicalSampleDTO> getBiologicalSampleDTOList(@Param("relationTaskId") String relationTaskId);

    List<PdFoodSampleDTO> getFoodSampleDTOList(@Param("relationTaskId") String relationTaskId);

    List<PdEnvironmentalSampleDTO> getEnvironmentalSampleDTOList(@Param("relationTaskId") String relationTaskId);
}
