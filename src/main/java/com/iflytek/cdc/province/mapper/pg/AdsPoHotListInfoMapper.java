package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.model.multichannelMonitor.dto.CollaborMonitorQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorKeywordVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 疾控关键词-热搜排行榜
 */
@Mapper
public interface AdsPoHotListInfoMapper {

    /**
     * 监测关键词上榜情况
     */
    List<MonitorKeywordVO> selectKeywordCountBy(CollaborMonitorQueryDTO queryDTO);

    /**
     * 监测关键词上榜详细查询
     */
    List<MonitorKeywordVO> searchKeywordSituation(CollaborMonitorQueryDTO queryDTO);
}
