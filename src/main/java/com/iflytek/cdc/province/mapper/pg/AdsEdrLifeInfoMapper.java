package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO;
import com.iflytek.cdc.province.entity.ads.AdsEdrLifeInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsEdrLifeInfoMapper extends BaseMapper<AdsEdrLifeInfo> {

    /**
     * 根据档案id查询生命周期
     */
    List<MsProcessLogVO> getRecordLifeCycle(@Param("id") String id);
}