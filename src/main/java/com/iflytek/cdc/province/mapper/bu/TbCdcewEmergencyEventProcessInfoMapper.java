package com.iflytek.cdc.province.mapper.bu;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO;
import com.iflytek.cdc.province.entity.bu.TbCdcewEmergencyEventProcessInfo;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.vo.MedCntIndicatorVO;
import com.iflytek.cdc.province.model.vo.MsPatientInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcewEmergencyEventProcessInfoMapper extends BaseMapper<TbCdcewEmergencyEventProcessInfo> {

    List<MedCntIndicatorVO> listEventMedCntIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    List<MedCntIndicatorVO> listEventOutcomeIndicator(MsProcessSimpleInfoQueryDTO queryDTO);

    List<MsProcessInfoAddressVO> listEventAddressByIds(MsProcessSimpleInfoQueryDTO dto);

    List<MsPatientInfoVO> listEventPatientInfoByIds(MsProcessSimpleInfoQueryDTO dto);

    int insertBatch(@Param("entities") List<TbCdcewEmergencyEventProcessInfo> entities);

    List<MsProcessSimpleInfoVO> listEventSimpleInfo(MsProcessSimpleInfoQueryDTO queryDTO);
}
