package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.entity.ads.AdsFsEdrInfo;
import com.iflytek.cdc.province.model.edr.vo.CommonRecordLogVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsFsEdrInfoMapper extends BaseMapper<AdsFsEdrInfo> {

    /**
     * 获取疾病档案总数
     * */
    Integer getTotalCount();

    /**
     * 根据档案id查询省市区权限code
     * */
    CommonRecordLogVO getPermitCodeById(@Param("id") String id);

    /**
     * 根据用户的权限判断 该用户是否有操作该条数据的权限
     * */
    AdsFsEdrInfo getEdrInfoBy(@Param("id") String id,
                              @Param("provinceCodes") List<String> provinceCodes,
                              @Param("cityCodes") List<String> cityCodes,
                              @Param("districtCodes") List<String> districtCodes);

}
