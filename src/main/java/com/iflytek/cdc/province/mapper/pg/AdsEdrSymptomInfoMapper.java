package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrSymptomInfoVO;
import com.iflytek.cdc.province.entity.ads.AdsEdrSymptomInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsEdrSymptomInfoMapper extends BaseMapper<AdsEdrSymptomInfo> {


    /**
     * 根据事件id获取患者症状信息
     * @param eventId
     * @return
     */
    List<AdsEdrSymptomInfoVO> getSymptomByEventId(@Param("eventId") String eventId);

}