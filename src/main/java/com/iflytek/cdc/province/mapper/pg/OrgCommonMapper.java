package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.edr.dto.org.OrgParams;
import com.iflytek.cdc.province.model.dto.dm.OrgQueryDTO;
import com.iflytek.cdc.province.model.vo.OrgInfoVO;
import com.iflytek.cdc.province.model.vo.TreeNode;
import com.iflytek.cdc.province.model.vo.ValueDomainVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrgCommonMapper {

    /**
     * 查询所有机构
     * */
    List<ValueDomainVO> getAllOrgInfo();

    List<OrgInfoVO> getAllOrgList(OrgParams orgParams, List<String> sourceType);

    /**
     * 查询所有机构
     * */
    List<TreeNode> getOrgListBy(OrgQueryDTO dto);
}
