package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.model.multichannelMonitor.dto.ProcessStatAnalysisQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorProcessFlowVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.SyndromeRelateInfectedProcessVO;

import java.util.List;

/**
 * 疾病特征分析-传染病病例-萌发分析
 */
public interface AdsMsInfectEvolveStatMapper {

    List<SyndromeRelateInfectedProcessVO> getSyndromeInfectDayDiagnose(ProcessStatAnalysisQueryDTO queryDTO);
    List<SyndromeRelateInfectedProcessVO> getSyndromeInfectWeekDiagnose(ProcessStatAnalysisQueryDTO queryDTO);
    List<SyndromeRelateInfectedProcessVO> getSyndromeInfectMeadowDiagnose(ProcessStatAnalysisQueryDTO queryDTO);
    List<SyndromeRelateInfectedProcessVO> getSyndromeInfectMonthDiagnose(ProcessStatAnalysisQueryDTO queryDTO);
    List<SyndromeRelateInfectedProcessVO> getSyndromeInfectQuarterDiagnose(ProcessStatAnalysisQueryDTO queryDTO);
    List<SyndromeRelateInfectedProcessVO> getSyndromeInfectYearDiagnose(ProcessStatAnalysisQueryDTO queryDTO);

    //多渠道态势感知 - 病例监测流向
    List<MonitorProcessFlowVO> getMonitorProcessFlowDayStat(ProcessStatAnalysisQueryDTO queryDTO);
    List<MonitorProcessFlowVO> getMonitorProcessFlowWeekStat(ProcessStatAnalysisQueryDTO queryDTO);
    List<MonitorProcessFlowVO> getMonitorProcessFlowMeadowStat(ProcessStatAnalysisQueryDTO queryDTO);
    List<MonitorProcessFlowVO> getMonitorProcessFlowMonthStat(ProcessStatAnalysisQueryDTO queryDTO);
    List<MonitorProcessFlowVO> getMonitorProcessFlowQuarterStat(ProcessStatAnalysisQueryDTO queryDTO);
    List<MonitorProcessFlowVO> getMonitorProcessFlowYearStat(ProcessStatAnalysisQueryDTO queryDTO);
}
