package com.iflytek.cdc.province.mapper.mpp;

import org.springframework.stereotype.Repository;

import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO;
import com.iflytek.cdc.province.model.edr.vo.CommonRecordLogVO;

import java.util.List;
import java.util.Map;

/**
 * 病历查询
 *
 * <AUTHOR>
 * @date 2025-06-05 13:57
 */
@Repository("data_mpp_data_model_base")
public interface DataModelMPPBaseMapper {

    List<Map<String, Object>> selectDataBySql(String customSql);

    List<MsProcessLogVO> getRecordLifeCycle(String archiveId);

    CommonRecordLogVO getCommonRecordLogById(String archiveId);
}
