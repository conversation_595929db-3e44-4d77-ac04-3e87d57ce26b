package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.entity.dim.DimSymptomMulInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DimSymptomMulInfoMapper extends BaseMapper<DimSymptomMulInfo> {

    List<String> getDistinctTagsByDataSource(@Param("dataSources") List<String> dataSources);

    List<DimSymptomMulInfo> getTagInfosBy(@Param("dataSources") List<String> dataSources, @Param("tag") String tag);
}