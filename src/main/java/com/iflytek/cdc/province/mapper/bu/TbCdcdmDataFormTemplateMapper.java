package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormTemplate;
import com.iflytek.cdc.edr.vo.dm.DataModelDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TbCdcdmDataFormTemplateMapper {
    int deleteByPrimaryKey(String formTemplateDetailId);

    int insert(TbCdcdmDataFormTemplate record);

    int insertSelective(TbCdcdmDataFormTemplate record);

    TbCdcdmDataFormTemplate selectByPrimaryKey(String formTemplateDetailId);

    int updateByPrimaryKeySelective(TbCdcdmDataFormTemplate record);

    int updateByPrimaryKey(TbCdcdmDataFormTemplate record);

    List<DataModelDetailVO> getDataModelByCode(@Param("formTemplateCode") String formTemplateCode,
                                               @Param("configInfoList") List<String> configInfoList,
                                               @Param("status") String status,
                                               @Param("modelVersionId") String modelVersionId);

    /**
     * 根据数据模型id查询模型详情
     * */
    DataModelDetailVO getDataModelByModelId(@Param("modelId") String modelId,
                                            @Param("status") String status);


    /**
     * 每个数据模型有且仅有一条独立的配置
     * */
    TbCdcdmDataFormTemplate getModelConfigByModelId(@Param("modelId") String modelId);
}