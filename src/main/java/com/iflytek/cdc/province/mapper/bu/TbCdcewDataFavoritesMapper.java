package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.entity.bu.TbCdcewDataFavorites;
import com.iflytek.cdc.edr.vo.dm.FavoritesListVO;
import com.iflytek.cdc.province.model.dto.dm.FavoriteQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbCdcewDataFavoritesMapper {

    /**
     * 查询收藏文件夹
     * */
    TbCdcewDataFavorites getFavoritesInfo(@Param("favoritesId") String favoritesId);

    /**
     * 查询收藏文件夹列表
     * */
    List<FavoritesListVO> getFavoritesList(FavoriteQueryDTO dto);

    /**
     * 编辑（新增、编辑）收藏文件夹
     * */
    void editFavorites(TbCdcewDataFavorites tbCdcewDataFavorites);

    /**
     * 删除某个收藏文件夹
     * */
    void updateFavorites(@Param("favoritesId") String favoritesId,
                         @Param("loginUserId") String loginUserId,
                         @Param("loginUserName") String loginUserName);

    /**
     * 更新收藏文件夹 收藏文件数量
     * */
    void updateFavoritesDataCount(@Param("loginUserId") String loginUserId,
                                  @Param("loginUserName") String loginUserName,
                                  @Param("favoritesId") String favoritesId);

    void updateFavoritesModelVersion(@Param("loginUserId") String loginUserId,
                                     @Param("loginUserName") String loginUserName,
                                     @Param("modelVersionId") String modelVersionId,
                                     @Param("favoritesId") String favoritesId,
                                     @Param("moduleType") String moduleType);

    TbCdcewDataFavorites getByIdAndUser(@Param("favoritesId") String favoritesId,
                                        @Param("loginUserId") String loginUserId);
}
