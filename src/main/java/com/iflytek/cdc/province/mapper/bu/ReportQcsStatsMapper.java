package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.entity.bu.ReportQcsStats;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Mapper
public interface ReportQcsStatsMapper extends BaseMapper<ReportQcsStats> {

    void mergeInto(@Param("list") List<ReportQcsStats> list);

    void deleteByMainRecordId(@Param("mainRecordId") String mainRecordId);
}
