package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.entity.ads.AdsMsInfectReportInfo;
import com.iflytek.cdc.province.model.dto.ReportQcsReqDTO;
import com.iflytek.cdc.province.model.pandemic.dto.CheckTaskQueryDTO;
import com.iflytek.cdc.province.model.pandemic.dto.SyndromeEnterLogReportDTO;
import com.iflytek.cdc.province.model.pandemic.vo.CheckTaskRecordVO;
import com.iflytek.cdc.province.model.pandemic.vo.SyndromeEnterLogReportVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsMsReportInfoMapper {

    /**
     * 查看审核任务列表
     * */
    List<CheckTaskRecordVO> getCheckTaskList(CheckTaskQueryDTO dto);

    /**
     * 查看指定疾病类型的的报卡
     * */
    CheckTaskRecordVO  getReportCardBy(@Param("diseaseType") String diseaseType,
                                       @Param("reportId") String reportId);

    /**
     * 报卡督导查询数据
     * @param reqDTO
     * @return
     */
    List<AdsMsInfectReportInfo> getInfectedReportForQcs(ReportQcsReqDTO reqDTO);


    /**
     * 查询症候群 入群日志报告
     */
    List<SyndromeEnterLogReportVO> selectEnterLogReport(SyndromeEnterLogReportDTO queryParams);
}
