package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.entity.bu.TbCdcdmMetadataTableInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TbCdcdmMetadataTableInfoMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcdmMetadataTableInfo record);

    int insertSelective(TbCdcdmMetadataTableInfo record);

    TbCdcdmMetadataTableInfo selectByPrimaryKey(String id);

    TbCdcdmMetadataTableInfo selectByCode(String code);

    int updateByPrimaryKeySelective(TbCdcdmMetadataTableInfo record);

    int updateByPrimaryKey(TbCdcdmMetadataTableInfo record);

    List<TbCdcdmMetadataTableInfo> getAllTableInfo();
}