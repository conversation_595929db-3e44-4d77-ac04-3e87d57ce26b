package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.entity.ads.AdsEdrPersonStatusInfo;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrPersonStatusInfoVO;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrProcessTagVO;

import java.util.List;

public interface AdsEdrPersonStatusInfoMapper extends BaseMapper<AdsEdrPersonStatusInfo> {
    List<AdsEdrProcessTagVO> getAdsEdrProcessTagList(String empiId);

    List<AdsEdrPersonStatusInfoVO> getAdsEdrPersonStatusInfoList(String empiId,String[] eventIds);
}