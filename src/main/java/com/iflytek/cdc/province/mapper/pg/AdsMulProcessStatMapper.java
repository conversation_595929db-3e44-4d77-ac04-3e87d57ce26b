package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ProcessPathogenQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ProcessStatAnalysisQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.*;

import java.util.List;

/**
 * 多渠道-病原分析-DAO
 */
public interface AdsMulProcessStatMapper {

    /**
     * 多渠道态势感知 - 呼吸道传染病发病概况
     */
    List<AdsMsProcessRespVO> diseaseOverAll(ProcessStatAnalysisQueryDTO reqDTO);

    /**
     * 多渠道态势感知 - 呼吸道传染病发病趋势
     */
    List<ProcessTimeLineStatVO> diseaseOnsetTrend(ProcessStatAnalysisQueryDTO reqDTO);

    /**
     * 多渠道态势感知 - 肺炎发生情况
     */
    List<PneumoniaOccursSituationVO> pneumoniaSituation(ProcessStatAnalysisQueryDTO reqDTO);

    /**
     * 多渠道态势感知 - 现存病例量统计
     */
    List<IndicatorDataVO> existsProcessStat(ProcessStatAnalysisQueryDTO reqDTO);

    /**
     * 多渠道态势感知 - 呼吸道传染病性别分布
     */
    List<ProcessSexDistributionVO> sexDistributionStat(ProcessStatAnalysisQueryDTO reqDTO);

    /**
     * 多渠道态势感知 - 性别发病趋势对比
     */
    List<ProcessTimeLineStatVO> sexTimeLineTrend(ProcessStatAnalysisQueryDTO reqDTO);

    /**
     * 多渠道态势感知 - 呼吸道传染病年龄分布
     */
    List<DoubleIndicatorStatVO> ageDistributionStat(ProcessStatAnalysisQueryDTO reqDTO);

    //多渠道态势感知 - 年龄趋势对比
    List<GroupSumIntVO> ageTimeLineTrend(ProcessStatAnalysisQueryDTO reqDTO);

    /**
     * 多渠道态势感知 - 呼吸道传染病职业分布
     */
    List<DoubleIndicatorStatVO> jobDistributionStat(ProcessStatAnalysisQueryDTO reqDTO);

    //多渠道态势感知 - 人群特征分布

    List<ProcessInfoVO> characterDistributionStat(ProcessStatAnalysisQueryDTO reqDTO);
    List<ProcessInfoVO> characterDistributionDayStat(ProcessStatAnalysisQueryDTO reqDTO);
    List<ProcessInfoVO> characterDistributionWeekStat(ProcessStatAnalysisQueryDTO reqDTO);
    List<ProcessInfoVO> characterDistributionMeadowStat(ProcessStatAnalysisQueryDTO reqDTO);
    List<ProcessInfoVO> characterDistributionMonthStat(ProcessStatAnalysisQueryDTO reqDTO);
    List<ProcessInfoVO> characterDistributionQuarterStat(ProcessStatAnalysisQueryDTO reqDTO);
    List<ProcessInfoVO> characterDistributionYearStat(ProcessStatAnalysisQueryDTO reqDTO);

    /**
     * 多渠道态势感知 - 呼吸道传染病区域分布
     */
    List<DoubleIndicatorStatVO> regionDistributionStat(ProcessStatAnalysisQueryDTO reqDTO);

    //多渠道态势感知 - 行政区发病趋势对比
    List<GroupSumIntVO> regionTimeLineTrend(ProcessStatAnalysisQueryDTO reqDTO);

    /**
     * 多渠道态势感知 - 呼吸道传染病城乡分布
     */
    List<ProcessUrbanRuralDistributionVO> urbanRuralDistributionStat(ProcessStatAnalysisQueryDTO reqDTO);

    //多渠道态势感知 - 城乡发病趋势对比
    List<GroupSumIntVO> urbanRuralTimeLineTrend(ProcessStatAnalysisQueryDTO reqDTO);

    /**
     * 多渠道态势感知 - 呼吸道传染病地区分布
     */
    List<ProcessAreaDistributionVO> getAreaDistributionStat(ProcessStatAnalysisQueryDTO reqDTO);

    //多渠道态势感知 - 发病数同比增长TOP10呼吸道传染病发病季节变化趋势
    List<ProcessTimeLineStatVO> getInfectYearOnYearGrowthByDate(ProcessStatAnalysisQueryDTO queryDTO);

    //多渠道态势感知 - 发病数同比增长TOP10呼吸道传染病发病各地区发病变化趋势
    List<DoubleIndicatorStatVO> getInfectYearOnYearGrowthByArea(ProcessStatAnalysisQueryDTO queryDTO);

    ProcessPositiveSituationVO listPositiveSituation(ProcessPathogenQueryDTO queryDTO);

    List<ProcessPositiveSituationVO> listPositiveTimeLine(ProcessPathogenQueryDTO queryDTO);
}
