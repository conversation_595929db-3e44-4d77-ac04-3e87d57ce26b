package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.entity.bu.TbCdcdmDataAttrValue;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TbCdcdmDataAttrValueMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcdmDataAttrValue record);

    int insertSelective(TbCdcdmDataAttrValue record);

    TbCdcdmDataAttrValue selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcdmDataAttrValue record);

    int updateByPrimaryKey(TbCdcdmDataAttrValue record);

    List<String> selectAttrsBydataAttrId(String datAttrId);
}