package com.iflytek.cdc.province.mapper.pg;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.iflytek.cdc.province.model.multichannelMonitor.vo.DrugResistantHospitalVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.MDRCheckAreaDistributionVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.PathogenPositiveAgeAndSexVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.PathogenSpectrumCompareVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.VirusAntigenResultVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.VirusCheckResultVO;
import com.iflytek.cdc.province.model.pathogen.PathogenAnalysisReqDTO;
import com.iflytek.cdc.province.model.pathogen.PathogenNameGroupVO;

/**
 * 病原监测-检出病原体-统计表-查询类：临时开的新类，后续改造旧统计表查询类时再和旧统计表查询类合并 AdsPdPathogenStatMapper
 */
@Mapper
public interface AdsPdPathogenStatMapper {

    /**
     * 查询病原体名称分组统计结果
     *
     * @param req 请求参数
     * @return 病原体名称分组统计结果
     */
    List<PathogenNameGroupVO> groupPositiveByName(PathogenAnalysisReqDTO req);

    /**
     * 多渠道态势感知 - 不同性别各年龄段呼吸道传染病病原阳性率对比
     */
    List<PathogenPositiveAgeAndSexVO> listPositiveRatioByAgeAndSex(PathogenAnalysisReqDTO req);

    /**
     * 多渠道态势感知 - 呼吸道传染病病原谱 - 区分是否肺炎和是否发热
     */
    List<PathogenSpectrumCompareVO> listPathogenSpectrumResult(PathogenAnalysisReqDTO req);

    /**
     * 多渠道态势感知 - 呼吸道病毒各分型检出情况 - 区分分型
     */
    List<VirusCheckResultVO> getVirusCheckStat(PathogenAnalysisReqDTO req);

    /**
     * 多渠道态势感知 - 不同等级医院呼吸道耐药菌检出率分析 - 区分机构
     */
    List<DrugResistantHospitalVO> getMDRCheckRateByGradeHospital(PathogenAnalysisReqDTO req);

    /**
     * 多渠道态势感知 - 不同地区呼吸道耐药菌检出情况 - 区分区域
     */
    List<MDRCheckAreaDistributionVO> getMDRCheckByDistrict(PathogenAnalysisReqDTO req);

    /**
     * 流感病毒实验室列表
     */
    List<VirusAntigenResultVO> getInfluenzaLabList();
}
