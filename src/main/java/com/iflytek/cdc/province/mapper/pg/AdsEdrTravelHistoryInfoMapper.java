package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrTravelHistoryInfoVO;
import com.iflytek.cdc.province.entity.ads.AdsEdrTravelHistoryInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * edr旅居史信息
 */
public interface AdsEdrTravelHistoryInfoMapper extends BaseMapper<AdsEdrTravelHistoryInfo> {




    List<AdsEdrTravelHistoryInfoVO> getTravelHistoryByEventId(@Param("eventId") String eventId);
}