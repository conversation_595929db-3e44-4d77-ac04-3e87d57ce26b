package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.model.dto.AdsMsIdentifyInfoReqDTO;
import com.iflytek.cdc.province.entity.ads.AdsMsMedicalIdentifyInfo;

import java.util.List;

/**
 * <p>
 * 诊疗(medical service)-病历识别详情信息（只要重新识别了都要有记录） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-23
 */
public interface AdsMsMedicalIdentifyInfoMapper extends BaseMapper<AdsMsMedicalIdentifyInfo> {


}
