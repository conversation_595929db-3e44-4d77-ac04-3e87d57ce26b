package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.entity.bu.TbCdcdmDataModelForm;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TbCdcdmDataModelFormMapper {
    int deleteByPrimaryKey(String modelFormId);

    int insert(TbCdcdmDataModelForm record);

    int insertSelective(TbCdcdmDataModelForm record);

    TbCdcdmDataModelForm selectByPrimaryKey(String modelFormId);

    int updateByPrimaryKeySelective(TbCdcdmDataModelForm record);

    int updateByPrimaryKey(TbCdcdmDataModelForm record);
}