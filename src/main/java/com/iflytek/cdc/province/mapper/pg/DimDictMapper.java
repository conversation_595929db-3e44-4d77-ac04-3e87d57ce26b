package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.entity.dim.DimDict;
import com.iflytek.cdc.province.model.vo.DictValueVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DimDictMapper extends BaseMapper<DimDict> {

    DimDict getOneBy(@Param("dictCode") String dictCode, @Param("valueCode") String valueCode);

    List<DictValueVO> searchBy(@Param("dictCode") String dictCode, @Param("searchValue") String searchValue);

    List<DimDict> searchRecurBy(@Param("dictCode") String dictCode, @Param("searchValue") String searchValue);
}