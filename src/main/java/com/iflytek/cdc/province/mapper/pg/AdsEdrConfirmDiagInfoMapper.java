package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrConfirmDiagInfoVO;
import com.iflytek.cdc.province.entity.ads.AdsEdrConfirmDiagInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsEdrConfirmDiagInfoMapper extends BaseMapper<AdsEdrConfirmDiagInfo> {
    /**
     * 根据事件id查询edr患者确诊结果信息
     * @param eventId
     * @return
     */
    List<AdsEdrConfirmDiagInfoVO> getDiagInfoByEventId(@Param("eventId") String eventId);
}