package com.iflytek.cdc.province.mapper.pg;


import com.iflytek.cdc.edr.vo.adsMsQualityEval.QualityEvalRespVO;
import com.iflytek.cdc.province.model.dto.AdsMsReportReqDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 诊疗(medical service)-症候群报告卡-监测业务质量（quality）- DAO
 * </p>
 *
 * @since 2025-03-12
 */
@Mapper
public interface AdsMsSyndromeReportQMapper {

    List<QualityEvalRespVO> dayEvalStat(AdsMsReportReqDTO reqDTO);

}
