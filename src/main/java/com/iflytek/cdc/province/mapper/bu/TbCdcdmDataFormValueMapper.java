package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.entity.bu.TbCdcdmDataFormValue;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TbCdcdmDataFormValueMapper {
    int deleteByPrimaryKey(String id);

    int insert(TbCdcdmDataFormValue record);

    int insertSelective(TbCdcdmDataFormValue record);

    TbCdcdmDataFormValue selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TbCdcdmDataFormValue record);

    int updateByPrimaryKey(TbCdcdmDataFormValue record);
}