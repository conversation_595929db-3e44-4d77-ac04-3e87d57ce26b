package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.model.dto.AdsMsProcessReqDTO;
import com.iflytek.cdc.edr.vo.adsMsProcess.*;
import com.iflytek.cdc.province.model.vo.MedCntIndicatorVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 传染病病程统计
 */
@Mapper
public interface AdsMsInfectProcessStatMapper {

    /**
     * 传染病统计
     */
    AdsMsProcessRespVO processStat(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> dayCntStatByDetailTable(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> findDistribution(AdsMsProcessReqDTO reqDTO);

    /**
     * 法定传染病类型新发病例数、死亡病例数统计
     */
    MsInfectTypeStatVO notifiableInfectTypeStat(AdsMsProcessReqDTO reqDTO);

    /**
     * 传染病疾病发病数统计
     */
    List<MsInfectNewCntVO> groupInfectNewCnt(AdsMsProcessReqDTO reqDTO);

    /**
     * 传染病疾病发病数统计 同比下降
     */
    List<MsInfectNewCntVO> groupInfectNewCntDecline(AdsMsProcessReqDTO reqDTO);

    /**
     * 传染病数量分组统计
     */
    List<MsInfectCntVO> groupInfectCnt(AdsMsProcessReqDTO reqDTO);

    /**
     * 传染病数量总数
     */
    MsInfectCntVO infectCntTotal(AdsMsProcessReqDTO reqDTO);

    /**
     * 传染病传播途径分组统计
     */
    List<MsInfectTransmitTypeCntVO> groupInfectTransmitTypeCnt(AdsMsProcessReqDTO reqDTO);

    /**
     * 不同行政区疾病特征
     */
    List<AdsMsProcessRespVO> groupArea(AdsMsProcessReqDTO reqDTO);

    /**
     * 返回选择地区的疾病特征
     * @param reqDTO
     * @return
     */
    List<AdsMsProcessRespVO> groupAreaCurrentLevel(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupAddrType(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupAge(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupSex(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupJob(AdsMsProcessReqDTO reqDTO);

    /**
     * 查询预计数据
     */
    List<AdsMsProcessRespVO> listNewCntPre(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> overAll(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> areaChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> sexChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> ageChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupIdentifyClass(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupOutcomeStatus(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupSymptomFirst(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupHistoryBefore(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupPathogenResNominal(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupDiseaseName(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupAreaDripDetail(AdsMsProcessReqDTO reqDTO);

    /**
     * 传染病数量性别分组统计 - 大模型演示使用
     */
    List<MsInfectCntVO> groupInfectSexCnt(AdsMsProcessReqDTO reqDTO);

    /**
     * 传染病数量年龄分组统计 - 大模型演示使用
     */
    List<MsInfectCntVO> groupInfectAgeCnt(AdsMsProcessReqDTO reqDTO);


    /**
     * 按病种统计发病数, 发病率, 病死率
     */
    List<MsInfectCntVO> groupInfectDiseaseCnt(AdsMsProcessReqDTO reqDTO);

    /**
     * 按地区统计发病数, 发病率, 病死率
     */
    List<AdsMsProcessRespVO> groupInfectAreaCnt(AdsMsProcessReqDTO reqDTO);

    /**
     * 按地区下的医疗机构统计 发病数, 发病率, 病死率 (统计原表 ads_ms_infect_process_info)
     */
    List<AdsMsProcessRespVO> groupInfectAreaOrgCnt(AdsMsProcessReqDTO reqDTO);

    /**
     * 发病死亡统计
     */
    List<AdsMsProcessRespVO> groupDeath(AdsMsProcessReqDTO reqDTO);

}
