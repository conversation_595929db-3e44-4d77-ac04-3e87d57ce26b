package com.iflytek.cdc.province.mapper.pg;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.iflytek.cdc.province.model.pathogen.PathogenAnalysisReqDTO;
import com.iflytek.cdc.province.model.pathogen.PathogenTwofoldStatVO;

/**
 * 病原复合感染统计Mapper
 */
@Mapper
public interface AdsPdPathogenTwofoldStatMapper {

    /**
     * 按病原体名称分组统计复合感染情况
     *
     * @param req 查询参数
     * @return 统计结果
     */
    List<PathogenTwofoldStatVO> groupTwofoldByName(PathogenAnalysisReqDTO req);
}
