package com.iflytek.cdc.province.mapper.bu;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.entity.bu.TbCdcewIllnessRecordBrowseLogs;
import com.iflytek.cdc.province.model.edr.dto.RetrievalLogsQueryDTO;
import com.iflytek.cdc.province.model.edr.vo.RetrievalLogsStatVO;

import java.util.List;

public interface TbCdcewIllnessRecordBrowseLogsMapper extends BaseMapper<TbCdcewIllnessRecordBrowseLogs> {

    /**
     * 调阅日志统计
     * */
    RetrievalLogsStatVO retrievalLogsStat(RetrievalLogsQueryDTO dto);

    /**
     * 查询调阅日志
     * */
    List<TbCdcewIllnessRecordBrowseLogs> getRetrievalLogs(RetrievalLogsQueryDTO dto);

    /**
     * 统计对应条件下日志数量
     * */
    Integer countRetrievalLogs(RetrievalLogsQueryDTO dto);

}
