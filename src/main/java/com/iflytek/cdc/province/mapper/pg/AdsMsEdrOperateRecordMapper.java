package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.entity.ads.AdsMsEdrOperateRecord;
import com.iflytek.cdc.province.model.edr.dto.MaintenanceLogsQueryDTO;
import com.iflytek.cdc.province.model.edr.vo.RecordMaintenanceStatVO;

import java.util.List;

public interface AdsMsEdrOperateRecordMapper extends BaseMapper<AdsMsEdrOperateRecord> {

    void insertOnce(AdsMsEdrOperateRecord log);

    RecordMaintenanceStatVO maintenanceLogsStat(MaintenanceLogsQueryDTO dto);

    /**
     * 查询维护更新日志
     * */
    List<AdsMsEdrOperateRecord> getMaintenanceLogs(MaintenanceLogsQueryDTO dto);

    /**
     * 统计维护更新日志 数量
     * */
    Integer countMaintenanceLogs(MaintenanceLogsQueryDTO dto);

}
