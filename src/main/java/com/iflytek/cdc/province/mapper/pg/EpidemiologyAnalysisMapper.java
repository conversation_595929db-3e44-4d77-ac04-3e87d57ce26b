package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.model.epidemiology.CritiaclDeathProcessVO;
import com.iflytek.cdc.province.model.epidemiology.PatientVisitSituationVO;
import com.iflytek.cdc.province.model.pathogen.EpidemiologyReqDTO;

import java.util.List;

/**
 * 流行病学分析
 */
public interface EpidemiologyAnalysisMapper {


    /**
     * 患者就诊情况
     */
    List<PatientVisitSituationVO> groupPatientVisitType(EpidemiologyReqDTO reqDTO);


    /**
     * 患者就诊科室分类
     */
    List<PatientVisitSituationVO> groupPatientVisitDept(EpidemiologyReqDTO reqDTO);


    /**
     * 转归状态 入院患者 和 出院患者
     */
    PatientVisitSituationVO groupOutcomeStatus(EpidemiologyReqDTO reqDTO);


    /**
     * 转重症死亡病例列表
     */
    List<CritiaclDeathProcessVO> selectCritiaclDeathProcessBy(EpidemiologyReqDTO reqDTO);


}
