package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.entity.bu.TbCdcewDataFavoritesDetail;
import com.iflytek.cdc.province.model.dto.dm.FavoritesDetailsQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface TbCdcewDataFavoritesDetailMapper {

    List<TbCdcewDataFavoritesDetail> getFavoritesDetails(FavoritesDetailsQueryDTO dto);

    void batchInsertCollection(@Param("details") List<TbCdcewDataFavoritesDetail> details);

    /**
     * 取消收藏 即更新数据收藏状态
     * */
    void updateCollectionStatus(@Param("favoritesId") String favoritesId,
                                @Param("dataRecordIds") List<String> dataRecordIds);
}
