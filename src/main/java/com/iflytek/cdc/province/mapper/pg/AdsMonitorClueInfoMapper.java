package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.entity.dim.AdsMonitorClueInfo;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ClueMonitorQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorClueInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsMonitorClueInfoMapper extends BaseMapper<AdsMonitorClueInfo> {
    List<String> selectClueSources();

    List<MonitorClueInfoVO> clueSearch(ClueMonitorQueryDTO queryDTO);

    AdsMonitorClueInfo selectByClueId(@Param("clueId") String clueId, @Param("type") String type);
}