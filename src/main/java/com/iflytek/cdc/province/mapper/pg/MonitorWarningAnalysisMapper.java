package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.model.dto.MonitorWarningAnalysisQueryDTO;
import com.iflytek.cdc.province.model.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.*;

/**
 * 监测预警分析
 */
public interface MonitorWarningAnalysisMapper {


    /**
     * 三类人群分布
     */
    ThreeCatePeopleViewVO threeCatePeopleView(MonitorWarningAnalysisQueryDTO queryDTO);


    /**
     * 三类人群时间趋势
     */
    List<ThreeCatePeopleViewVO> threeCatePeopleTimeTrend(MonitorWarningAnalysisQueryDTO queryDTO);

    /**
     * 韦恩图
     */
    ThreeCatePeopleViewVO vennView(MonitorWarningAnalysisQueryDTO queryDTO);


    /**
     * 报告情况总览
     */
    ReportOverallVO reportOverall(MonitorWarningAnalysisQueryDTO queryDTO);


    /**
     * 诊断病例列表
     */
    List<DiagnoseProcessVO> selectDiagnoseProcess(MonitorWarningAnalysisQueryDTO queryDTO);

    /**
     * 患者就诊详情
     */
    PatientVisitDetailVO selectPatientDetailByProcessId(@Param("processId") String processId);


    /**
     * 报卡病例列表
     */
    List<ReportProcessVO> selectReportProcess(MonitorWarningAnalysisQueryDTO queryDTO);

    /**
     * 应排查病例统计
     */
    InvestigatedCaseVO investigatedCaseStat(MonitorWarningAnalysisQueryDTO queryDTO);

    /**
     * 应排查病例列表 - 监测预警
     */
    List<InvestigatedCaseVO> selectInvestigatedCase(MonitorWarningAnalysisQueryDTO queryDTO);

    /**
     * 应排查任务统计
     */
    InvestigatedTaskVO investigatedTaskStat(MonitorWarningAnalysisQueryDTO queryDTO);


    /**
     * 应排查任务区域统计
     */
    List<InvestigatedTaskVO> taskStatAreaGroup(MonitorWarningAnalysisQueryDTO queryDTO);


    /**
     * 应排查任务统计 - 任务督办处置报告列表
     */
    List<InvestTaskStatVO> checkTaskStat(MonitorWarningAnalysisQueryDTO queryDTO);


    /**
     * 传染病-任务督办情况统计
     */
    List<InvestTaskStatVO> investTaskStatInfect(MonitorWarningAnalysisQueryDTO queryDTO);


    /**
     * 报告卡-任务督办情况统计
     */
    List<InvestTaskStatVO> investTaskStatReport(MonitorWarningAnalysisQueryDTO queryDTO);


    ReportProcessVO selectReportProcessByProcessId(@Param("processId") String processId);
}
