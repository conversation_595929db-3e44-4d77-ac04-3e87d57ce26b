package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrExposureHistoryInfoVO;
import com.iflytek.cdc.province.entity.ads.AdsEdrExposureHistoryInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsEdrExposureHistoryInfoMapper extends BaseMapper<AdsEdrExposureHistoryInfo> {


    /**
     * 根据事件id查询edr暴露史信息
     * @param eventId
     * @return
     */
    List<AdsEdrExposureHistoryInfoVO> getExposureHistoryByEventId(@Param("eventId") String eventId);
}