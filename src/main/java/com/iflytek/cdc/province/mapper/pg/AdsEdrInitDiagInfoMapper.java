package com.iflytek.cdc.province.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrInitDiagInfoVO;
import com.iflytek.cdc.province.entity.ads.AdsEdrInitDiagInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdsEdrInitDiagInfoMapper extends BaseMapper<AdsEdrInitDiagInfo> {
    
    /**
     * 查询edr患者初步诊断信息
     * @param eventId
     * @return
     */
    List<AdsEdrInitDiagInfoVO> getDiagInfoByEventId(@Param("eventId") String eventId);

}