package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.model.multichannelMonitor.dto.VirusCheckQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.DrugSituationVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 检出病原体-细菌培养-药敏统计信息
 */
@Mapper
public interface AdsPdBacteriaAstStatMapper {

    /**
     * 对常用抗菌药物的敏感性
     */
    List<DrugSituationVO> getDrugSensitivityBy(VirusCheckQueryDTO queryDTO);
}
