package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.province.model.multichannelMonitor.dto.CollaborMonitorQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.DrugSalesQueryDto;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.DrugDailySalesVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.DrugSalesVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 社会购药信息-统计天表
 */
@Mapper
public interface AdsDsDrugStatMapper {

    /**
     * 院外诊疗分析-销售同比增长排行
     */
    List<DrugSalesVO> selectDrugSalesBy(CollaborMonitorQueryDTO queryDTO);

    /**
     * 协调监测数据池-药品销量暴增
     */
    List<DrugDailySalesVO> drugDailySales(DrugSalesQueryDto queryDTO);
}
