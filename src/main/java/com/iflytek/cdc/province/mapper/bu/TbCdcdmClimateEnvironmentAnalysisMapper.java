package com.iflytek.cdc.province.mapper.bu;

import com.iflytek.cdc.province.model.vo.ClimateEnvironmentAnalysisVo;
import com.iflytek.cdc.reportcard.dto.common.CommonQueryDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TbCdcdmClimateEnvironmentAnalysisMapper {

    List<ClimateEnvironmentAnalysisVo> queryClimateEnvironmentAnalysis(CommonQueryDTO commonQuery);
}