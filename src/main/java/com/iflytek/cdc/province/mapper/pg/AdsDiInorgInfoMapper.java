package com.iflytek.cdc.province.mapper.pg;

import com.iflytek.cdc.edr.vo.OrgDetailInfoVO;
import com.iflytek.cdc.province.model.dto.DiInOrgQueryDTO;
import com.iflytek.cdc.province.entity.ads.AdsDiInorgInfo;
import com.iflytek.cdc.edr.vo.AreaInOrgCountVO;
import com.iflytek.cdc.edr.vo.AreaInOrgStatisticVO;
import com.iflytek.cdc.edr.vo.InOrgTypeCountVO;
import com.iflytek.cdc.province.model.vo.TimeTrendVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface AdsDiInorgInfoMapper {

    /**
     * 获取最后的统计日期
     */
    Date getLastStatDate(@Param("provinceCode") String provinceCode,
                         @Param("cityCode") String cityCode,
                         @Param("districtCode") String districtCode);

    /**
     * 机构查询
     */
    List<AdsDiInorgInfo> queryBy(DiInOrgQueryDTO queryDTO);

    /**
     * 接入机构统计
     */
    List<InOrgTypeCountVO> inOrgTypeCount(DiInOrgQueryDTO queryDTO);

    /**
     * 区域接入数量统计
     */
    List<AreaInOrgCountVO> areaCount(DiInOrgQueryDTO queryDTO);

    /**
     * 区域的各个指标统计
     */
    List<AreaInOrgStatisticVO> areaStatistic(DiInOrgQueryDTO queryDTO);

    /**
     * 查询区域下的具体机构信息
     */
    List<OrgDetailInfoVO> queryOrgDetails(DiInOrgQueryDTO queryDTO);

    /**
     * 查询区域下的机构对接状态分组信息
     */
    List<OrgDetailInfoVO> queryOrgDetailsByStatus(DiInOrgQueryDTO queryDTO);

    /**
     *查询各区域下的机构覆盖度
     * */
    List<AreaInOrgStatisticVO> areaStatisticBy(DiInOrgQueryDTO queryDTO);


    /**
     * 查询医疗机构类型(去重)
     */
    List<AdsDiInorgInfo> selectDiInorgType(DiInOrgQueryDTO queryDTO);

    /**
     * 查询小于等于当前天的最大时间
     * @param date
     * @param provinceCode
     * @param cityCode
     * @param districtCode
     * @return
     */
    Date getLastStatDateMaxDay(@Param("date") Date date,
                               @Param("provinceCode") String provinceCode,
                               @Param("cityCode") String cityCode,
                               @Param("districtCode") String districtCode);

    /**
     * 查询小于等于当前天的最大时间
     * @param date
     * @param provinceCode
     * @param cityCode
     * @param districtCode
     * @return
     */
    Date getLastStatDateMinDay(@Param("date") Date date,
                               @Param("provinceCode") String provinceCode,
                               @Param("cityCode") String cityCode,
                               @Param("districtCode") String districtCode);
    /**
     * 时间趋势统计
     */
    List<TimeTrendVO> inOrgTypeTimeTrend(DiInOrgQueryDTO queryDTO);

}

