package com.iflytek.cdc.province.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.annotation.OperationLogAnnotation;
import com.iflytek.cdc.province.entity.bu.TbCdcewAdditionProcessInfo;
import com.iflytek.cdc.province.entity.bu.TbCdcewCorrectionRecord;
import com.iflytek.cdc.province.entity.bu.TbCdcewProcessReportCheckLog;
import com.iflytek.cdc.province.enums.*;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.edr.vo.dm.ProcessRecordVO;
import com.iflytek.cdc.province.model.pandemic.dto.*;
import com.iflytek.cdc.province.model.pandemic.dto.message.MessageDTO;
import com.iflytek.cdc.province.model.pandemic.vo.CheckTaskRecordVO;
import com.iflytek.cdc.province.model.pandemic.vo.CorrectFieldVO;
import com.iflytek.cdc.province.model.pandemic.vo.PersonProcessInfo;
import com.iflytek.cdc.province.model.pandemic.vo.SyndromeEnterLogReportVO;
import com.iflytek.cdc.province.service.PandemicMonitorCommonService;
import com.iflytek.cdc.province.model.pandemic.vo.CorrectionRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 疫情监测
 * */
@RestController
@Api(tags = "监测病例管理（疫情监测）")
@RequestMapping("/pt/{version}/pandemicMonitor")
public class PandemicMonitorController {

    @Resource
    private PandemicMonitorCommonService pandemicMonitorCommonService;

    /**
     * 查询病例分页列表（传染病/症候群/新发突发传染病）
     * */
    @PostMapping("/getMedicalList")
    @ApiOperation("查询病例列表")
    public PageInfo<ProcessRecordVO> getMedicalList(@RequestBody MedicalQueryDTO medicalQueryDTO) {

        return pandemicMonitorCommonService.getMedicalPageList(medicalQueryDTO);
    }

    @GetMapping("/getMedicalDept")
    @ApiOperation("查询病例列表门诊部门")
    public List<String> getMedicalDept() {
        return pandemicMonitorCommonService.getMedicalDept();
    }

    /**
     * 保存病例订正信息
     * */
    @OperationLogAnnotation(operationName = "保存病例订正信息")
    @PostMapping("/saveCaseCorrection")
    @ApiOperation("保存病例订正信息")
    public void saveCaseCorrection(@RequestBody List<TbCdcewCorrectionRecord> recordList) {

        pandemicMonitorCommonService.saveCaseCorrection(recordList);
    }

    /**
     * 保存审核信息
     * */
    @OperationLogAnnotation(operationName = "保存审核信息")
    @PostMapping("/saveCheckRecord")
    @ApiOperation("保存审核信息")
    public void saveCheckRecord(@RequestBody CheckTaskParamDTO dto) {

        pandemicMonitorCommonService.saveCheckRecord(dto);
    }

    /**
     * 查看报卡历史的订正信息
     * */
    @PostMapping("/getReportCorrectHistory")
    @ApiOperation("查看报卡历史的订正信息")
    public PageInfo<CorrectionRecordVO> getReportCorrectHistory(@RequestBody CorrectionRecordQueryDTO dto) {

        return pandemicMonitorCommonService.getReportCorrectHistory(dto);
    }

    /**
     * 查看报卡历史的审核信息
     * */
    @PostMapping("/getReportCheckHistory")
    @ApiOperation("查看报卡历史的审核信息")
    public PageInfo<TbCdcewProcessReportCheckLog> getReportCheckHistory(@RequestBody CheckTaskQueryDTO dto) {

        return pandemicMonitorCommonService.getReportCheckHistory(dto);
    }

    /**
     * 查看审核任务列表
     * */
    @PostMapping("/getCheckTaskList")
    @ApiOperation("查看审核任务列表")
    public PageInfo<CheckTaskRecordVO> getCheckTaskList(@RequestBody CheckTaskQueryDTO dto){

        return pandemicMonitorCommonService.getCheckTaskList(dto);
    }

    /**
     * 查看某病例是否有待审核报卡
     * */
    @PostMapping("/getProcessWaitCheckReport")
    @ApiOperation("查看某病例是否有待审核报卡")
    public CheckTaskRecordVO getProcessWaitCheckReport(@RequestBody CheckTaskQueryDTO dto){

        return pandemicMonitorCommonService.getProcessWaitCheckReport(dto);
    }
    
    /**
     * 新增病例 or 新增报卡
     * */
    @PostMapping("/addProcessInfo")
    @ApiOperation("新增病例 or 新增报卡")
    public void addProcessInfo(@RequestBody TbCdcewAdditionProcessInfo processInfo) {

        pandemicMonitorCommonService.addProcessInfo(processInfo);
    }

    /**
     * 根据给定信息查询 病人是否有相关病例
     * */
    @PostMapping("/getProcessInfoByPerson")
    @ApiOperation("根据给定信息查询 病人是否有相关病例")
    public PageInfo<PersonProcessInfo> getProcessInfoByPerson(@RequestBody PersonProcessQueryDTO dto) {

        return pandemicMonitorCommonService.getProcessInfoByPerson(dto);
    }

    /**
     * 查看订正记录
     * */
    @PostMapping("/getCorrectionRecordList")
    @ApiOperation("查看订正记录")
    public PageInfo<CorrectionRecordVO> getCorrectionRecordList(@RequestBody CorrectionRecordQueryDTO dto){

        return pandemicMonitorCommonService.getCorrectionRecordList(dto);
    }

    /**
     * 查看订正字段
     * */
    @PostMapping("/getCorrectionFieldList")
    @ApiOperation("查看订正字段")
    public List<String> getCorrectionFieldList(@RequestBody CorrectionRecordQueryDTO dto){

        return pandemicMonitorCommonService.getCorrectionFieldList(dto);
    }

    /**
     * 订正记录导出
     * */
    @PostMapping("/correctionRecordListExport")
    @ApiOperation("订正记录导出")
    public TbCdcmrExportTask correctionRecordListExport(@RequestBody CorrectionRecordQueryDTO dto){

        return pandemicMonitorCommonService.correctionRecordListExport(dto);
    }

    /**
     * 订正统计 - 订正字段排序
     * */
    @PostMapping("/getCorrectFieldRanking")
    @ApiOperation("订正统计 - 订正字段排序")
    public List<CorrectFieldVO> getCorrectFieldRanking(@RequestBody CorrectionRecordQueryDTO dto){

        return pandemicMonitorCommonService.getCorrectFieldRanking(dto);
    }

    /**
     * 订正统计 - 订正字段排序
     * */
    @PostMapping("/getCorrectFieldStat")
    @ApiOperation("订正统计 - 字段订正统计")
    public List<CorrectFieldVO> getCorrectFieldStat(@RequestBody CorrectionRecordQueryDTO dto){

        return pandemicMonitorCommonService.getCorrectFieldStat(dto);
    }

    @PostMapping("/getConstants")
    @ApiOperation("获取静态变量")
    public Map<String, Object> getConstants(){

        Map<String, Object> constants = new HashMap<>();
        constants.put("checkIdentifyStatus", CheckIdentifyStatusEnum.values());
        constants.put("checkProcessStatus", CheckProcessStatusEnum.values());
        constants.put("checkResult", CheckResultEnum.values());
        constants.put("reportCheckFlag", ReportCheckFlagEnum.values());
        constants.put("outcomeStatus", LoOutcomeStatusEnum.values());
        return constants;
    }


    @PostMapping("/listEnterLogReport")
    @ApiOperation("查询入群日志报告列表")
    public PageInfo<SyndromeEnterLogReportVO> listEnterLogReport(@RequestBody SyndromeEnterLogReportDTO queryDTO) {
        return pandemicMonitorCommonService.listSyndromeEnterLogReport(queryDTO);
    }

    @PostMapping("verifyInformationPush")
    @ApiOperation("核实信息推送")
    public void verifyInformationPush(@RequestBody MessageDTO dto) {
        pandemicMonitorCommonService.verifyInformationPush(dto);
    }

}
