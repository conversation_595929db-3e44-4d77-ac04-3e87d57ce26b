package com.iflytek.cdc.province.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.province.entity.ads.DyqwEvent;
import com.iflytek.cdc.province.model.dto.DyqwEventDTO;
import com.iflytek.cdc.province.service.EmergencyEventService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


/**
 * 突发公共卫生事件
 * */
@RestController
@Api(tags = "突发公共卫生事件")
@RequestMapping("/pt/{version}/emergencyEvent")
public class EmergencyEventController {


    @Autowired
    private EmergencyEventService emergencyEventService;

    @GetMapping("/getEventByEventId/{id}")
    @ApiOperation("根据id查事件详情")
    public DyqwEvent getEventByEventId(@PathVariable String id){

        return emergencyEventService.getEventByEventId(id);
    }

    @PostMapping("/getEmergencyEvents")
    @ApiOperation("查询事件列表")
    public PageInfo<DyqwEvent> getEmergencyEvents(@RequestBody DyqwEventDTO dto){
        return emergencyEventService.getEmergencyEvents(dto);
    }

    @PostMapping("/getDownBox")
    @ApiOperation("获取下拉框")
    public Map<String, List<String>> getDownBox(){
        return emergencyEventService.getDownBox();
    }


}
