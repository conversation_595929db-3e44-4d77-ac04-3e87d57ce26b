package com.iflytek.cdc.province.controller;

import com.iflytek.cdc.province.model.vo.ClimateEnvironmentAnalysisVo;
import com.iflytek.cdc.province.service.SignalService;
import com.iflytek.cdc.reportcard.dto.common.CommonQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@Api(tags = "信号查询处理")
@RequestMapping("/pt/{version}/signal")
public class SignalController {

    @Autowired
    private SignalService signalService;

    @PostMapping("/queryClimateEnvironmentAnalysis")
    @ApiOperation("根据区划获取气候环境分析结果")
    public List<ClimateEnvironmentAnalysisVo> queryClimateEnvironmentAnalysis(@RequestBody CommonQueryDTO commonQuery) {
        return signalService.queryClimateEnvironmentAnalysis(commonQuery);
    }


}
