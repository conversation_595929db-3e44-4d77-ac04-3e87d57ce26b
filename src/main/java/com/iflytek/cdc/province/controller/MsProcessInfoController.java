package com.iflytek.cdc.province.controller;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoDetailVO;
import com.iflytek.cdc.province.cache.SensitiveWordCache;
import com.iflytek.cdc.province.enums.ProcessInfoTypeEnum;
import com.iflytek.cdc.province.model.dto.MsProcessLogQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoConditionQueryDTO;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.vo.*;
import com.iflytek.cdc.province.service.AdsMsProcessInfoService;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessInfoAddressVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO;
import com.iflytek.cdc.province.service.CommonProcessInfoService;
import com.iflytek.cdc.province.service.ModelDataSearchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/pt/{version}/ms/processInfo")
@Api(tags = "病程信息")
public class MsProcessInfoController {
    @Resource
    private AdsMsProcessInfoService adsMsProcessInfoService;

    @Resource
    private CommonProcessInfoService commonProcessInfoService;

    @Resource
    private ModelDataSearchService dataSearchService;

    @Resource
    private SensitiveWordCache sensitiveWordCache;

    @PostMapping("/listInfectedMedCntIndicator")
    @ApiOperation("传染病-病例数指标")
    public List<MedCntIndicatorVO> listInfectedMedCntIndicator(@RequestBody MsProcessSimpleInfoQueryDTO queryDTO){
        return commonProcessInfoService.listMedCntIndicator(ProcessInfoTypeEnum.MUL.getCode(), queryDTO);
    }

    @PostMapping("/listSyndromeMedCntIndicator")
    @ApiOperation("症候群-病例数指标")
    public List<MedCntIndicatorVO> listSyndromeMedCntIndicator(@RequestBody MsProcessSimpleInfoQueryDTO queryDTO){
        return commonProcessInfoService.listMedCntIndicator(ProcessInfoTypeEnum.MUL.getCode(), queryDTO);
    }

    @PostMapping("/listAreaMedCntIndicator")
    @ApiOperation("区域病例数指标统计")
    public List<AreaMedCntIndicatorVO> listAreaMedCntIndicator(@RequestParam String processType, @RequestBody MsProcessSimpleInfoQueryDTO queryDTO){
        return commonProcessInfoService.listAreaMedCntIndicator(processType, queryDTO);
    }

    @PostMapping("/infectedSimpleInfoPageList")
    @ApiOperation("分页获取传染病病例的简单信息")
    public PageInfo<MsProcessSimpleInfoVO> infectedSimpleInfoPageList(@RequestBody MsProcessSimpleInfoQueryDTO queryDTO){

        return commonProcessInfoService.simpleInfoPageList(ProcessInfoTypeEnum.MUL.getCode(), queryDTO);
    }
    @PostMapping("/exportInfectedSimpleInfo")
    @ApiOperation("导出传染病简易信息")
    public TbCdcmrExportTask exportInfectedSimpleInfo(@RequestBody MsProcessSimpleInfoQueryDTO queryDTO, @RequestParam String loginUserId){
        return commonProcessInfoService.exportSimpleInfo(ProcessInfoTypeEnum.MUL.getCode(), queryDTO);
    }

    @PostMapping("/syndromeSimpleInfoPageList")
    @ApiOperation("分页获取症候群病例的简单信息")
    public PageInfo<MsProcessSimpleInfoVO> syndromeSimpleInfoPageList(@RequestBody MsProcessSimpleInfoQueryDTO queryDTO){

        return commonProcessInfoService.simpleInfoPageList(ProcessInfoTypeEnum.MUL.getCode(), queryDTO);
    }


    @PostMapping("/syndromeSimpleInfoPageListByConditions")
    @ApiOperation("分页获取症候群病例的简单信息高级搜索")
    public PageInfo<MsProcessSimpleInfoVO> syndromeSimpleInfoPageListByConditions(@RequestBody MsProcessSimpleInfoConditionQueryDTO queryDTO){
        return commonProcessInfoService.syndromeSimpleInfoPageListByConditions(ProcessInfoTypeEnum.MUL.getCode(), queryDTO);
    }

    @PostMapping("/exportSyndromeSimpleInfo")
    @ApiOperation("导出症候群简易信息")
    public TbCdcmrExportTask exportSyndromeSimpleInfo(@RequestBody MsProcessSimpleInfoQueryDTO queryDTO, @RequestParam String loginUserId){
        return commonProcessInfoService.exportSimpleInfo(ProcessInfoTypeEnum.MUL.getCode(), queryDTO);
    }

    @PostMapping("/listProcessLog")
    @ApiOperation("病程日志查询")
    public List<MsProcessLogVO> listProcessLog(@RequestBody MsProcessLogQueryDTO queryDTO){
        return commonProcessInfoService.listProcessLog(queryDTO);
    }

    /**
     * 传染病病程简易信息
     */
    @PostMapping("/listInfectedSimpleInfo")
    @ApiOperation("传染病病程简易信息")
    public List<MsProcessSimpleInfoVO> listInfectedSimpleInfo(@RequestBody MsProcessSimpleInfoQueryDTO queryDTO){
        return adsMsProcessInfoService.listInfectedSimpleInfo(queryDTO);
    }

    /**
     * 症候群病例 重点场所以及监测症状信息
     */
    @PostMapping("/listSyndromeProcessDetailInfo")
    @ApiOperation("症候群病例 重点场所以及监测症状信息")
    public List<MsProcessMonitorInfoVO> listSyndromeProcessDetailInfo(@RequestBody MsProcessSimpleInfoQueryDTO dto){
        return adsMsProcessInfoService.listSyndromeProcessDetailInfo(dto);
    }

    /**
     * 病例数指标
     */
    @PostMapping("/listMedCntIndicator")
    @ApiOperation("病例数指标")
    public List<MedCntIndicatorVO> listMedCntIndicator(@RequestParam String processType,
                                                       @RequestBody MsProcessSimpleInfoQueryDTO queryDTO){
        return commonProcessInfoService.listMedCntIndicator(processType, queryDTO);
    }

    /**
     * 根据id查询地址相关信息
     */
    @PostMapping("/listAddressBySourceKeys")
    @ApiOperation("根据id查询地址相关信息")
    public List<MsProcessInfoAddressVO> listAddressBySourceKeys(@RequestParam String processType,
                                                                @RequestBody MsProcessSimpleInfoQueryDTO dto){

        return commonProcessInfoService.listAddressByIds(processType, dto);
    }


    /**
     * 分页查询病程简单信息
     */
    @PostMapping("/simpleInfoPageList")
    @ApiOperation("分页查询病程简单信息")
    public PageInfo<MsProcessSimpleInfoVO> simpleInfoPageList(@RequestParam String processType, @RequestBody MsProcessSimpleInfoQueryDTO queryDTO){
        return commonProcessInfoService.simpleInfoPageList(processType,queryDTO);
    }
    
    @PostMapping("/loadSimpleInfo")
    @ApiOperation("单条查询病程简单信息")
    public MsProcessSimpleInfoDetailVO loadProcessSimpleInfo(@RequestParam String processType, @RequestParam String id){
        return commonProcessInfoService.loadProcessSimple(processType, id);
    }

    /**
     * 导出病程简单信息
     */
    @PostMapping("/exportSimpleInfo")
    @ApiOperation("导出病程简单信息")
    public TbCdcmrExportTask exportSimpleInfo(@RequestParam String processType, @RequestBody MsProcessSimpleInfoQueryDTO queryDTO){
        return commonProcessInfoService.exportSimpleInfo(processType, queryDTO);
    }


    /**
     * 查询患者信息
     */
    @PostMapping("/listPatientInfoByProcessIds")
    @ApiOperation("查询患者信息")
    public List<MsPatientInfoVO> listPatientInfoByProcessIds(@RequestParam String processType,
                                                             @RequestBody MsProcessSimpleInfoQueryDTO dto){
        if (CollUtil.isEmpty(dto.getIds())) {
            return new ArrayList<>(0);
        }
        return commonProcessInfoService.listPatientInfoByProcessIds(processType, dto);
    }

    /**
     * 查询简单病程信息
     */
    @PostMapping("/listSimpleInfo")
    @ApiOperation("查询简单病程信息")
    public List<MsProcessSimpleInfoVO> listSimpleInfo(@RequestParam String processType,  @RequestBody MsProcessSimpleInfoQueryDTO queryDTO){
        return commonProcessInfoService.listSimpleInfo(processType,queryDTO);
    }

    /**
     * 查询简单病程信息
     */
    @PostMapping("/listProcessModelSimpleInfo")
    @ApiOperation("查询简单病程信息")
    public Object listProcessModelSimpleInfo(@RequestParam String processType,  @RequestBody MsProcessSimpleInfoQueryDTO queryDTO){
        return commonProcessInfoService.listProcessModelSimpleInfo(processType,queryDTO);
    }

    @PostMapping("/loadSurveillanceReportVO")
    @ApiOperation("监测信息")
    public SurveillanceReportVO loadSurveillanceReportVO(@RequestParam String processType,  @RequestBody MsProcessSimpleInfoQueryDTO queryDTO){
        return commonProcessInfoService.loadSurveillanceReportVO(processType, queryDTO);
    }

    @PostMapping("/syncProcessInfoBy")
    @ApiOperation("同步信号的病例信息")
    public void syncProcessInfoBy(@RequestParam String processType,
                                  @RequestBody List<String> ids){

        commonProcessInfoService.syncProcessInfoBy(processType, ids);
    }

    @PostMapping("/getProcessPathogenInfoBy")
    @ApiOperation("统计病例的病原检测结果")
    public ProcessPathogenInfoVO getProcessPathogenInfoBy(@RequestParam String processType,
                                                          @RequestBody List<String> ids){

        return commonProcessInfoService.getProcessPathogenInfoBy(processType, ids);
    }

    /**
     * 分页查询治疗情况
     */
    @PostMapping("/syndrome/pageListTreatment")
    @ApiOperation("分页查询治疗情况")
    public PageInfo<PatientTreatmentInfoVO> pageListSyndromeTreatment(@RequestBody  MsProcessSimpleInfoQueryDTO dto){
        return commonProcessInfoService.pageListTreatment(ProcessInfoTypeEnum.MUL.getCode(), dto);
    }

    /**
     * 分页查询死亡情况
     */
    @PostMapping("/syndrome/pageListDeath")
    @ApiOperation("分页查询死亡情况")
    public PageInfo<PatientDeathInfoVO> pageListSyndromeDeath(@RequestBody  MsProcessSimpleInfoQueryDTO dto){
        return commonProcessInfoService.pageListDeath(ProcessInfoTypeEnum.MUL.getCode(), dto);

    }

}
