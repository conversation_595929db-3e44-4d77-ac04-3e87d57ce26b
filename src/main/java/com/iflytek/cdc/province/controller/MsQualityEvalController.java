package com.iflytek.cdc.province.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.annotation.OperationLogAnnotation;
import com.iflytek.cdc.edr.vo.adsMsQualityEval.QualityEvalRespVO;
import com.iflytek.cdc.province.model.dto.AdsMsReportReqDTO;
import com.iflytek.cdc.province.model.pandemic.vo.CheckTaskRecordVO;
import com.iflytek.cdc.province.service.AdsMsReportQService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 诊疗(medical service)-传染病/症候群报告卡-监测业务质量（quality）- 接口类
 *
 * <AUTHOR>
 * @since 2024-04-23
 */
@RestController
@Api(tags = "监测质量评估")
public class MsQualityEvalController {

    @Resource
    private AdsMsReportQService reportQService;

    @PostMapping("/pt/{version}/msQualityEval/evalStat")
    @ApiOperation("质量评估统计")
    public List<QualityEvalRespVO> evalStat(@RequestBody AdsMsReportReqDTO reqDTO, @RequestParam String loginUserName) {
        return reportQService.evalStat(reqDTO, loginUserName);
    }

    @PostMapping("/pt/{version}/msQualityEval/evalStatExport")
    @ApiOperation("质量评估统计-导出")
    @OperationLogAnnotation(operationName = "监测质量评估-质量评估统计-导出")
    public ResponseEntity<byte[]> evalStatExport(@RequestBody AdsMsReportReqDTO reqDTO, @RequestParam String loginUserName) {
        return reportQService.evalStatExport(reqDTO, loginUserName);
    }

    @PostMapping("/pt/{version}/msQualityEval/reportInfoList")
    @ApiOperation("病例列表")
    public PageInfo<CheckTaskRecordVO> reportInfoList(@RequestBody AdsMsReportReqDTO reqDTO, @RequestParam String loginUserName) {
        return reportQService.reportInfoList(reqDTO, loginUserName);
    }

    @PostMapping("/pt/{version}/msQualityEval/reportInfoListExport")
    @ApiOperation("病例列表-导出")
    @OperationLogAnnotation(operationName = "监测质量评估-病例列表-导出")
    public ResponseEntity<byte[]> reportInfoListExport(@RequestBody AdsMsReportReqDTO reqDTO, @RequestParam String loginUserName) {
        return reportQService.reportInfoListExport(reqDTO, loginUserName);
    }
}
