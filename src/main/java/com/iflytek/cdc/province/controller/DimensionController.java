package com.iflytek.cdc.province.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.dto.DictParams;
import com.iflytek.cdc.province.cache.DimRegionCache;
import com.iflytek.cdc.province.model.vo.DictValueVO;
import com.iflytek.cdc.province.model.vo.TreeNode;
import com.iflytek.cdc.province.service.DimDictService;
import com.iflytek.cdc.province.service.DimStreetInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/pt/{version}/dim")
@Api(tags = "维度处理")
public class DimensionController {

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Resource
    private DimDictService dimDictService;

    @Resource
    private DimStreetInfoService dimStreetInfoService;

    @Resource
    private DimRegionCache regionCache;

    @PostMapping("/getInfectedInfo")
    @ApiOperation("传染病病种级联")
    public List<TreeNode> getInfectedInfo(@RequestParam(required = false) String infectClassCode) {

        //默认查询法定传染病级联关系
        return adminServiceApi.getInfectedInfo(infectClassCode);
    }

    @PostMapping("/getOrgInfo")
    @ApiOperation("获取机构级联")
    public List<TreeNode> getOrgInfo() {

        return null;
    }

    @PostMapping("/getSyndromeInfo")
    @ApiOperation("获取症候群级联")
    public List<TreeNode> getSyndromeInfo() {

        return adminServiceApi.getSyndromeInfo();
    }

    @PostMapping("/getAreaInfo")
    @ApiOperation("获取区域级联 - 省市区街道级别（根据登录用户的机构进行判断）")
    public List<TreeNode> getAreaInfo() {

        return regionCache.getAreaInfo();
    }

    @PostMapping("/getDictBy")
    @ApiOperation("根据字典代码获取字典值域")
    public DictValueVO getDictBy(String dictCode, String dictValueCode) {
        return dimDictService.getDictBy(dictCode, dictValueCode);
    }

    @PostMapping("/getDictValueBy")
    @ApiOperation("根据字典代码获取字典值域")
    public List<DictValueVO> getDictValueBy(String dictCode) {

        return dimDictService.getDictValueBy(dictCode);
    }

    @PostMapping("/searchDictValueBy")
    @ApiOperation("根据字典代码获取字典值域")
    public PageInfo<DictValueVO> searchDictValueBy(@RequestBody DictParams dictParams) {
        return dimDictService.searchDictValueBy(dictParams);
    }

    @PostMapping("/getInfectedInitDiagnose")
    @ApiOperation("获取传染病初步诊断（传染病+新发突发传染病）")
    public List<TreeNode> getInfectedInitDiagnose() {
        return adminServiceApi.getInfectedInitDiagnose();
    }

}
