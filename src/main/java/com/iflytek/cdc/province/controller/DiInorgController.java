package com.iflytek.cdc.province.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.annotation.OperationLogAnnotation;
import com.iflytek.cdc.province.model.dto.DiInOrgQueryDTO;
import com.iflytek.cdc.province.entity.ads.AdsDiInorgInfo;
import com.iflytek.cdc.province.model.vo.TimeTrendVO;
import com.iflytek.cdc.province.service.AdsDiInorgService;
import com.iflytek.cdc.edr.vo.AreaInOrgCountVO;
import com.iflytek.cdc.edr.vo.AreaInOrgStatisticVO;
import com.iflytek.cdc.edr.vo.InOrgTypeCountVO;
import com.iflytek.cdc.edr.vo.OrgDetailInfoVO;
import com.iflytek.cdc.edr.vo.InOrgStatusTtlVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

@RestController
@Api(tags = "接入机构统计")
public class DiInorgController {

    @Resource
    private AdsDiInorgService adsDiInorgService;

    @PostMapping("/pt/{version}/di/inorg/query")
    @ApiOperation("机构列表查询")
    public PageInfo<AdsDiInorgInfo> queryBy(@RequestBody DiInOrgQueryDTO queryDTO, @RequestParam String loginUserName) {
        return adsDiInorgService.queryBy(queryDTO, loginUserName);
    }

    @PostMapping("/pt/{version}/di/inorg/areaCountTop10")
    @ApiOperation("区域覆盖度top10统计")
    public List<AreaInOrgCountVO> areaCountTop10(@RequestBody  DiInOrgQueryDTO queryDTO, @RequestParam  String loginUserName) {
        return adsDiInorgService.areaCountTop10(queryDTO, loginUserName);
    }

    @PostMapping("/pt/{version}/di/inorg/areaStatistic")
    @ApiOperation("接入详细统计")
    public PageInfo<AreaInOrgStatisticVO> areaStatistic(@RequestBody DiInOrgQueryDTO queryDTO, @RequestParam String loginUserName) {
        return adsDiInorgService.areaStatistic(queryDTO, loginUserName);
    }

    @PostMapping("/pt/{version}/di/inorg/areaStatisticExport")
    @ApiOperation("监测覆盖度统计-各区域监测覆盖度-导出（下钻到区县）")
    @OperationLogAnnotation(operationName = "监测覆盖度统计-各区域监测覆盖度-导出")
    public ResponseEntity<byte[]> areaStatisticExport(@RequestBody DiInOrgQueryDTO queryDTO,
                                                      @RequestParam String loginUserName) {

        return adsDiInorgService.areaStatisticExport(queryDTO, loginUserName);
    }

    @PostMapping("/pt/{version}/di/inorg/inOrgTypeCount")
    @ApiOperation("接入机构统计")
    public List<InOrgTypeCountVO> inOrgTypeCount(@RequestBody DiInOrgQueryDTO queryDTO, @RequestParam String loginUserName) {
        return adsDiInorgService.inOrgTypeCount(queryDTO, loginUserName);
    }

    @PostMapping("/pt/{version}/di/inorg/queryOrgDetails")
    @ApiOperation("查询区域下的具体机构信息")
    public PageInfo<OrgDetailInfoVO> queryOrgDetails(@RequestBody DiInOrgQueryDTO queryDTO, @RequestParam String loginUserName) {
        return adsDiInorgService.queryOrgDetails(queryDTO, loginUserName);
    }

    @PostMapping("/pt/{version}/di/inorg/queryOrgConnectionStatus")
    @ApiOperation("查询区域下的机构对接状态分组信息")
    public InOrgStatusTtlVO queryOrgConnectionStatus(@RequestBody DiInOrgQueryDTO queryDTO, @RequestParam String loginUserName) {
        return adsDiInorgService.queryOrgConnectionStatus(queryDTO, loginUserName);
    }

    @PostMapping("/pt/{version}/di/inorg/inOrgTypeTimeTrend")
    @ApiOperation("接入机构时间趋势统计")
    public List<TimeTrendVO> inOrgTypeTimeTrend(@RequestBody DiInOrgQueryDTO queryDTO) {
        return adsDiInorgService.inOrgTypeTimeTrend(queryDTO);
    }


    @PostMapping("/pt/{version}/di/inorg/getLastStatDate")
    @ApiOperation("获取统计截止日期")
    public Date getLastStatDate() {
        return adsDiInorgService.getLastStatData();
    }

    @PostMapping("/pt/{version}/di/inorg/getConstants")
    @ApiOperation("获取常量")
    public Map<String, Object> getConstants() {
        Map<String, Object> constants = new HashMap<>();
        constants.put("orgType", DiInOrgQueryDTO.OrgTypeEnum.values());
        constants.put("inStatus", DiInOrgQueryDTO.InStatusEnum.values());
        return constants;
    }

    @PostMapping("/pt/{version}/di/inorg/listInorgType")
    @ApiOperation("查询机构类型")
    public List<AdsDiInorgInfo> listInorgType(@RequestBody DiInOrgQueryDTO queryDTO, @RequestParam String loginUserName) {
        return adsDiInorgService.listInorgType(queryDTO, loginUserName);
    }


}
