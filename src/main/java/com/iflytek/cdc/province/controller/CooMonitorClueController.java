package com.iflytek.cdc.province.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.province.constant.PathogenDataConstant;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ClueDrugSalesQueryDto;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ClueMonitorQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ClueIndexQueryDto;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.DrugDailySalesVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorClueInfoVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.AdsPoIndexInfoVO;
import com.iflytek.cdc.province.service.CooMonitorClueService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 协同监测分析
 * */
@RestController
@Api(tags = "协同监测数据池")
@RequestMapping("/pt/{version}/cooperation/monitor/clue")
public class CooMonitorClueController {

    @Resource
    private CooMonitorClueService cooMonitorClueService;

    @GetMapping("/constants")
    @ApiOperation("协同监测数据池常量")
    public Map<String, List<String>> getClueConstants() {
        Map<String, List<String>> constants = new HashMap<>();
        constants.put("clueTypes", PathogenDataConstant.clueTypeEnum.types());

        List<String> clueSources = cooMonitorClueService.listClueSources();
        constants.put("clueSources", clueSources);

        return constants;
    }

    @PostMapping("/search")
    @ApiOperation("监测线索检索")
    public PageInfo<MonitorClueInfoVO> clueSearch(@RequestBody ClueMonitorQueryDTO queryDTO) {

        return cooMonitorClueService.clueSearch(queryDTO);
    }

    @PostMapping("/drug/detail")
    @ApiOperation("药品销量详情")
    public List<DrugDailySalesVO> drugSalesDetail(@RequestBody ClueDrugSalesQueryDto queryDTO) {

        return cooMonitorClueService.drugSalesDetail(queryDTO);
    }

    @PostMapping("/searchIndex/detail")
    @ApiOperation("搜索指数详情")
    public AdsPoIndexInfoVO searchIndexDetail(@RequestBody ClueIndexQueryDto queryDTO) {

        return cooMonitorClueService.searchIndexDetail(queryDTO);
    }

    @PostMapping("/consultIndex/detail")
    @ApiOperation("资讯指数详情")
    public AdsPoIndexInfoVO consultIndexDetail(@RequestBody ClueIndexQueryDto queryDTO) {

        return cooMonitorClueService.consultIndexDetail(queryDTO);
    }

}
