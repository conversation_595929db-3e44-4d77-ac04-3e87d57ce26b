package com.iflytek.cdc.province.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.utils.FileUtils;
import com.iflytek.cdc.province.enums.BriefDateTypeEnum;
import com.iflytek.cdc.province.enums.BriefStatisticsSourceEnum;
import com.iflytek.cdc.province.model.brief.BriefRecordDownloadTemplate;
import com.iflytek.cdc.province.model.brief.ManualBriefGenerateInfo;
import com.iflytek.cdc.province.model.brief.ManualBriefQueryDTO;
import com.iflytek.cdc.province.model.brief.ManualBriefRangeDTO;
import com.iflytek.cdc.province.model.brief.ManualBriefRecordVO;
import com.iflytek.cdc.province.service.ManualBriefRecordService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 生成简报记录-接口控制类
 */
@RestController
@Api(tags = "简报数据导入及手动生成简报接口")
@RequestMapping("/pt/{version}/brief/manual")
public class ManualBriefRecordController {

    @Resource
    private ManualBriefRecordService manualBriefRecordService;

    /**
     * 通过导入的方式手动生成简报记录
     *
     * @return 生成简报记录
     */
    @PostMapping("/import")
    @ApiOperation("通过导入的方式手动生成简报记录")
    public ManualBriefRecordVO importBriefRecord(@RequestParam String startTime,
                                                 @RequestParam String endTime,
                                                 @RequestParam String provinceCode,
                                                 @RequestParam(required = false) String cityCode,
                                                 @RequestParam(required = false) String districtCode,
                                                 @RequestParam MultipartFile file,
                                                 @RequestParam String loginUserId,
                                                 @RequestParam String loginUserName) {
        ManualBriefRangeDTO range = new ManualBriefRangeDTO();
        range.setStartDate(DateUtil.parse(startTime));
        range.setEndDate(DateUtil.parse(endTime));
        range.setProvinceCode(provinceCode);
        range.setCityCode(cityCode);
        range.setDistrictCode(districtCode);
        if (range.isBrokenRange()) {
            throw new MedicalBusinessException("数据范围选择不完整！");
        }
        ManualBriefGenerateInfo info = ManualBriefGenerateInfo.of(range, loginUserId, loginUserName);
        return manualBriefRecordService.generateByImport(info, file);
    }

    @GetMapping("/template/download")
    @ApiOperation("下载导入模板")
    public ResponseEntity<byte[]> downloadTemplate() {
        HttpHeaders httpHeaders = FileUtils.getHttpHeaders("法定传染病疫情概况统计数据模板.xlsx");
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        EasyExcel.write(outputStream, BriefRecordDownloadTemplate.class).sheet().doWrite(BriefRecordDownloadTemplate.sampleData());
        return new ResponseEntity<>(outputStream.toByteArray(), httpHeaders, HttpStatus.CREATED);
    }

    /**
     * 查询简报记录列表
     *
     * @param queryParam  查询参数
     * @param loginUserId 登录用户id
     * @return 简报记录列表
     */
    @PostMapping("/list")
    @ApiOperation("查询简报记录列表")
    public PageInfo<ManualBriefRecordVO> list(@RequestBody ManualBriefQueryDTO queryParam,
                                              @RequestParam String loginUserId) {
        queryParam.setCreatorId(loginUserId);
        return manualBriefRecordService.queryList(queryParam);
    }

    /**
     * @return 获取指定区域和统计截止时间内的总人口
     */
    @PostMapping("/population/total")
    @ApiOperation("获取指定区域和统计截止时间内的总人口")
    public int getTotalPopulation(@RequestBody ManualBriefRangeDTO range) {
        return manualBriefRecordService.getTotalPopulation(range);
    }

    /**
     * @return 获取枚举值列表
     */
    @GetMapping("/enums")
    @ApiOperation("获取枚举值列表")
    public Map<String, Object> getEnums() {
        Map<String, Object> constants = new HashMap<>();
        constants.put("statisticsSource", BriefStatisticsSourceEnum.asMap());
        constants.put("dateDimType", BriefDateTypeEnum.asMap());
        return constants;
    }
}
