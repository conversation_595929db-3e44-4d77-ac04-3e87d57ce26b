package com.iflytek.cdc.province.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.province.model.epidemiology.CritiaclDeathProcessVO;
import com.iflytek.cdc.province.model.epidemiology.PatientVisitSituationVO;
import com.iflytek.cdc.province.model.pathogen.EpidemiologyReqDTO;
import com.iflytek.cdc.province.service.EpidemiologyAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 流行病学分析
 */
@RestController
@Api(tags = "流行病学分析")
@RequestMapping("/pt/{version}/epidemiology/analysis")
public class EpidemiologyAnalysisController {


    @Resource
    private EpidemiologyAnalysisService epidemiologyAnalysisService;

    /**
     * 病人就诊情况
     */
    @PostMapping("/patientVisitSituationChart")
    @ApiOperation(value = "病人就诊情况")
    public List<PatientVisitSituationVO> patientVisitSituationChart(@RequestBody EpidemiologyReqDTO reqDTO, @RequestParam String loginUserName) {

        return  epidemiologyAnalysisService.patientVisitSituationChart(reqDTO);
    }


    /**
     * 病人就诊科室
     */
    @PostMapping("/patientVisitDeptChart")
    @ApiOperation(value = "病人就诊科室")
    public List<PatientVisitSituationVO> patientVisitDeptChart(@RequestBody EpidemiologyReqDTO reqDTO, @RequestParam String loginUserName) {
        return epidemiologyAnalysisService.patientVisitDeptChart(reqDTO);
    }

    /**
     * 入院患者就诊情况
     */
    @PostMapping("/admissPatientVisitChart")
    @ApiOperation(value = "入院患者就诊情况")
    public PatientVisitSituationVO admissPatientVisitChart(@RequestBody EpidemiologyReqDTO reqDTO, @RequestParam String loginUserName) {
        return epidemiologyAnalysisService.admissPatientVisitChart(reqDTO);
    }

    /**
     * 出院患者情况
     */
    @PostMapping("/leavePatientVisitChart")
    @ApiOperation(value = "出院患者情况")
    public PatientVisitSituationVO leavePatientVisitChart(@RequestBody EpidemiologyReqDTO reqDTO, @RequestParam String loginUserName) {
        return epidemiologyAnalysisService.leavePatientVisitChart(reqDTO);
    }

    /**
     * 转重症死亡病例分页列表
     */
    @PostMapping("/pageCritiaclDeathProcess")
    @ApiOperation(value = "转重症死亡病例分页列表")
    public PageInfo<CritiaclDeathProcessVO> pageCritiaclDeathProcess(@RequestBody EpidemiologyReqDTO reqDTO, @RequestParam String loginUserName) {
        return epidemiologyAnalysisService.pageCritiaclDeathProcess(reqDTO);
    }

}
