package com.iflytek.cdc.province.controller;


import com.iflytek.cdc.edr.annotation.OperationLogAnnotation;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.EpidemicDistributionRespVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsInfectCntVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.ProcessAwarenessRespVO;
import com.iflytek.cdc.province.model.dto.AdsMsProcessReqDTO;
import com.iflytek.cdc.province.service.AdsMsProcessStatService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 诊疗(medical service)-传染病-病程分析-按审核时间-月表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-28
 */
@RestController
@Api(tags = "疾病专题分析")
public class MsProcessDiseaseAnalysisController {

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Resource
    private AdsMsProcessStatService msInfectProcessService;

    @PostMapping("/pt/{version}/zt/intensityDescription/monthTimeTrend")
    @ApiOperation("流行强度描述-时间分布-月度")
    public List<AdsMsProcessRespVO> monthTimeTrend(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                   @RequestParam() String loginUserId,
                                                   @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.monthTimeTrend(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/intensityDescription/monthTimeTrendExport")
    @ApiOperation("流行强度描述-时间分布-月度-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> monthTimeTrendExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                       @RequestParam String loginUserId,
                                                       @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.monthTimeTrendExport(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/intensityDescription/quarterTimeTrend")
    @ApiOperation("流行强度描述-时间分布-季度")
    public List<AdsMsProcessRespVO> quarterTimeTrend(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                     @RequestParam String loginUserId,
                                                     @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.quarterTimeTrend(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/intensityDescription/quarterTimeTrendExport")
    @ApiOperation("流行强度描述-时间分布-季度-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> quarterTimeTrendExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                         @RequestParam String loginUserId,
                                                         @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.quarterTimeTrendExport(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/intensityDescription/yearTimeTrend")
    @ApiOperation("流行强度描述-时间分布-年度")
    public List<AdsMsProcessRespVO> yearTimeTrend(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                  @RequestParam String loginUserId,
                                                  @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.yearTimeTrend(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/intensityDescription/yearTimeTrendExport")
    @ApiOperation("流行强度描述-时间分布-年度-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> yearTimeTrendExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                      @RequestParam String loginUserId,
                                                      @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.yearTimeTrendExport(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/intensityDescription/halfYearTimeTrend")
    @ApiOperation("流行强度描述-时间分布-半年")
    public List<AdsMsProcessRespVO> halfYearTimeTrend(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                  @RequestParam String loginUserId,
                                                  @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.halfYearTimeTrend(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/intensityDescription/halfYearTimeTrendExport")
    @ApiOperation("流行强度描述-时间分布-半年-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> halfYearTimeTrendExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                      @RequestParam String loginUserId,
                                                      @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.halfYearTimeTrendExport(reqDTO, loginUserId, loginUserName);
    }



    @PostMapping("/pt/{version}/zt/intensityDescription/groupArea")
    @ApiOperation("流行强度描述-地区分布-不同行政区疾病特征")
    public List<AdsMsProcessRespVO> groupArea(@RequestBody AdsMsProcessReqDTO reqDTO,
                                              @RequestParam String loginUserId,
                                              @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.groupArea(reqDTO, loginUserId, loginUserName);
    }
    @PostMapping("/pt/{version}/zt/intensityDescription/groupAreaDetail")
    @ApiOperation("流行强度描述-地区分布-地区分布（下钻到区县）")
    public List<AdsMsProcessRespVO> processStatGroupAreaDetail(@RequestBody AdsMsProcessReqDTO queryParam,
                                                               @RequestParam String loginUserId,
                                                               @RequestParam String loginUserName){
        adminServiceApi.setSyndromeCode(queryParam);
        return msInfectProcessService.groupAreaDetail(queryParam, loginUserId, loginUserName);
    }
    @PostMapping("/pt/{version}/zt/intensityDescription/groupAreaExport")
    @ApiOperation("流行强度描述-地区分布-不同行政区疾病特征-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> groupAreaExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                  @RequestParam String loginUserId,
                                                  @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.groupAreaExport(reqDTO, loginUserId, loginUserName);
    }
    @PostMapping("/pt/{version}/zt/intensityDescription/groupAreaDetailExport")
    @ApiOperation("流行强度描述-地区分布-不同行政区疾病特征-导出（下钻到区县）")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> groupAreaDetailExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                  @RequestParam String loginUserId,
                                                  @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.groupAreaDetailExport(reqDTO, loginUserId, loginUserName);
    }
    @PostMapping("/pt/{version}/zt/intensityDescription/groupAddrType")
    @ApiOperation("流行强度描述-地区分布-城乡分布特征")
    public List<AdsMsProcessRespVO> groupAddrType(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                  @RequestParam String loginUserId,
                                                  @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return new ArrayList<>();
        // TODO: 城乡分布 - 当前无法计算
    }

    @PostMapping("/pt/{version}/zt/intensityDescription/groupAddrTypeExport")
    @ApiOperation("流行强度描述-地区分布-城乡分布特征-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> groupAddrTypeExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                      @RequestParam String loginUserId,
                                                      @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.groupAddrTypeExport(reqDTO, loginUserId, loginUserName);
        // TODO: 城乡分布 - 当前无法计算
    }

    @PostMapping("/pt/{version}/zt/intensityDescription/groupAge")
    @ApiOperation("流行强度描述-人群分布-不同年龄段疾病特征")
    public List<AdsMsProcessRespVO> groupAge(@RequestBody AdsMsProcessReqDTO reqDTO,
                                             @RequestParam String loginUserId,
                                             @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.groupAge(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/intensityDescription/groupAgeExport")
    @ApiOperation("流行强度描述-人群分布-不同年龄段疾病特征-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> groupAgeExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                 @RequestParam String loginUserId,
                                                 @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.groupAgeExport(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/intensityDescription/groupSex")
    @ApiOperation("流行强度描述-人群分布-不同性别疾病特征")
    public List<AdsMsProcessRespVO> groupSex(@RequestBody AdsMsProcessReqDTO reqDTO,
                                      @RequestParam String loginUserId,
                                      @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.groupSex(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/intensityDescription/groupSexExport")
    @ApiOperation("流行强度描述-人群分布-不同性别疾病特征-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> groupSexExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                 @RequestParam String loginUserId,
                                                 @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.groupSexExport(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/intensityDescription/groupJob")
    @ApiOperation("流行强度描述-人群分布-不同职业疾病特征")
    public List<AdsMsProcessRespVO> groupJob(@RequestBody AdsMsProcessReqDTO reqDTO,
                                      @RequestParam String loginUserId,
                                      @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.groupJob(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/intensityDescription/groupJobExport")
    @ApiOperation("流行强度描述-人群分布-不同职业疾病特征-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> groupJobExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                 @RequestParam String loginUserId,
                                                 @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.groupJobExport(reqDTO, loginUserId, loginUserName);
    }


    @PostMapping("/pt/{version}/zt/change/overall")
    @ApiOperation("流行动态变化-总体变化趋势")
    public List<AdsMsProcessRespVO> overall(@RequestBody AdsMsProcessReqDTO reqDTO,
                                            @RequestParam String loginUserId,
                                            @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.overall(reqDTO, loginUserId, loginUserName);
    }

    /**
     * 流行动态变化-态势感知推演（实时再生数，无缓存）
     */
    @PostMapping("/pt/{version}/zt/change/overall/awareness")
    @ApiOperation("流行动态变化-态势感知推演（实时再生数，无缓存）")
    public String overallAwareness(@RequestBody AdsMsProcessReqDTO reqDTO,
                                   @RequestParam String loginUserId,
                                   @RequestParam String loginUserName) {
        return msInfectProcessService.overallAwareness(reqDTO, loginUserId, loginUserName);
    }

    /**
     * 流行动态变化-获取态势感知结果（实时再生数）
     */
    @PostMapping("/pt/{version}/zt/change/overall/loadAwarenessResult")
    @ApiOperation("流行动态变化-获取态势感知结果（实时再生数）")
    public List<ProcessAwarenessRespVO> loadAwarenessResult(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                      @RequestParam String loginUserId) {
        if (reqDTO.getAwarenessTaskId() == null || reqDTO.getAwarenessTaskId().isEmpty()) {
            throw new MedicalBusinessException("任务ID不可为空");
        }
        return msInfectProcessService.loadAwarenessResult(reqDTO, loginUserId);
    }

    @PostMapping("/pt/{version}/zt/change/area")
    @ApiOperation("流行动态变化-分地区变化趋势")
    public Collection<List<AdsMsProcessRespVO>> area(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                     @RequestParam String loginUserId,
                                                     @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.areaChange(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/change/sex")
    @ApiOperation("流行动态变化-分性别变化趋势")
    public Collection<List<AdsMsProcessRespVO>> sex(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                    @RequestParam String loginUserId,
                                                    @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.sexChange(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/change/age")
    @ApiOperation("流行动态变化-分年龄变化趋势")
    public Collection<List<AdsMsProcessRespVO>> age(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                    @RequestParam String loginUserId,
                                                    @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.ageChange(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/change/qOQTrendChart")
    @ApiOperation("流行动态变化-环比趋势图")
    public List<AdsMsProcessRespVO> qOQTrendChart(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                    @RequestParam String loginUserId,
                                                    @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.qOQTrendChart(reqDTO, loginUserId, loginUserName);
    }

    /**
     * 各个区域的病例数相关统计
     */
    @PostMapping("/pt/{version}/zt/change/areaMedDateCount")
    @ApiOperation("各个区域的病例数相关统计")
    public List<AdsMsProcessRespVO> areaMedDateCount(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserId) {
        return msInfectProcessService.areaMedDateCount(reqDTO);
    }

    @PostMapping("/pt/{version}/zt/epidemicDistribution/identifyClass")
    @ApiOperation("病情分布分析-诊断类型（确诊类型）")
    public List<AdsMsProcessRespVO> groupIdentifyClass(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserId,
                                                       @RequestParam String loginUserName) {
        return msInfectProcessService.groupIdentifyClass(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/epidemicDistribution/outcomeStatus")
    @ApiOperation("病情分布分析-病情转归（转归类型）")
    public List<AdsMsProcessRespVO> groupOutcomeStatus(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserId,
                                                       @RequestParam String loginUserName) {
        return msInfectProcessService.groupOutcomeStatus(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/epidemicDistribution/symptomFirst")
    @ApiOperation("病情分布分析-首发症状")
    public List<AdsMsProcessRespVO> groupSymptomFirst(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserId,
                                                      @RequestParam String loginUserName) {
        return msInfectProcessService.groupSymptomFirst(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/epidemicDistribution/historyBefore")
    @ApiOperation("病情分布分析-既往疾病")
    public List<AdsMsProcessRespVO> groupHistoryBefore(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserId,
                                                       @RequestParam String loginUserName) {
        return msInfectProcessService.groupHistoryBefore(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/epidemicDistribution/pathogenResNominal")
    @ApiOperation("病情分布分析-病原阳性分析（病原检测定性结果）")
    public EpidemicDistributionRespVO groupPathogenResNominal(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserId,
                                                              @RequestParam String loginUserName) {
        return msInfectProcessService.groupPathogenResNominal(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/diseaseDistribution")
    @ApiOperation("不同疾病占比分析（该接口不接受疾病过滤条件）")
    public List<AdsMsProcessRespVO> groupDiseaseName(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserId,
                                                     @RequestParam String loginUserName) {
        // 清空疾病过滤条件
        reqDTO.setInfectCode(null);
        return msInfectProcessService.groupDiseaseName(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/diseaseDistribution/export")
    @ApiOperation("不同疾病占比分析（该接口不接受疾病过滤条件）-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> groupDiseaseNameExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                         @RequestParam String loginUserId,
                                                         @RequestParam String loginUserName) {
        reqDTO.setInfectCode(null);
        return msInfectProcessService.groupDiseaseNameExport(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/getDiseaseDistributionBox")
    @ApiOperation("症候群占比分析下拉框")
    public List<AdsMsProcessRespVO> getDiseaseDistributionBox( @RequestParam String loginUserName ) {
        return msInfectProcessService.getDiseaseDistributionBox(loginUserName);
    }


    @PostMapping("/pt/{version}/zt/timeJudgeModelTrend")
    @ApiOperation("时间研判模型-折线图")
    public List<AdsMsProcessRespVO> timeJudgeModelTrend(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        return msInfectProcessService.timeJudgeModelTrend(reqDTO, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/highPrevalenceAreaTop3")
    @ApiOperation("高发地区排名前三")
    public List<AdsMsProcessRespVO> highPrevalenceAreaTop3(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserName) {

        return msInfectProcessService.highPrevalenceAreaTop3(reqDTO, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/groupDeath")
    @ApiOperation("发病死亡统计")
    public List<AdsMsProcessRespVO> groupDeath(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserName) {
        return msInfectProcessService.groupDeath(reqDTO, loginUserName);
    }



}
