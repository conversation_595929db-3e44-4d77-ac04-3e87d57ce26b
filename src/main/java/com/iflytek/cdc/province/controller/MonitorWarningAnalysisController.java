package com.iflytek.cdc.province.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.utils.FileUtils;
import com.iflytek.cdc.province.model.dto.MonitorWarningAnalysisQueryDTO;
import com.iflytek.cdc.province.model.vo.*;
import com.iflytek.cdc.province.service.MonitorWarningAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 监测预警分析
 */
@RestController
@Api(tags = "监测预警分析", value = "监测预警分析")
@RequestMapping("/pt/{version}/monitorWarningAnalysis")
public class MonitorWarningAnalysisController {


    @Resource
    private MonitorWarningAnalysisService monitorWarningAnalysisService;

    /**
     * 传染病三类人群总览
     */
    @PostMapping("/threeCatePeopleView")
    @ApiOperation(value = "传染病三类人群总览")
    public ThreeCatePeopleViewVO threeCatePeopleView(@RequestBody MonitorWarningAnalysisQueryDTO queryDTO, @RequestParam String loginUserName) {
        return monitorWarningAnalysisService.getThreeCatePeopleView(queryDTO, loginUserName);
    }

    /**
     * 三类人群时间分布趋势
     */
    @PostMapping("/threeCatePeopleTimeTrend")
    @ApiOperation(value = "三类人群时间分布趋势")
    public List<ThreeCatePeopleViewVO> threeCatePeopleTimeTrend(@RequestBody MonitorWarningAnalysisQueryDTO queryDTO, @RequestParam String loginUserName) {
        return monitorWarningAnalysisService.threeCatePeopleTimeTrend(queryDTO, loginUserName);
    }

    /**
     * 三类人群分布韦恩图
     */
    @PostMapping("/veenView")
    @ApiOperation(value = "三类人群韦恩图")
    public ThreeCatePeopleViewVO veenView(@RequestBody MonitorWarningAnalysisQueryDTO queryDTO, @RequestParam String loginUserName) {
        return monitorWarningAnalysisService.veenView(queryDTO, loginUserName);
    }

    /**
     * 报卡情况总览
     */
    @PostMapping("/reportOverall")
    @ApiOperation(value = "报卡情况总览")
    public ReportOverallVO reportOverall(@RequestBody MonitorWarningAnalysisQueryDTO queryDTO, @RequestParam String loginUserName) {
        return monitorWarningAnalysisService.reportOverall(queryDTO, loginUserName);
    }

    /**
     * 诊断病例列表-分页
     */
    @PostMapping("/pageDiagnoseProcess")
    @ApiOperation(value = "诊断病例列表-分页")
    public PageInfo<DiagnoseProcessVO> pageDiagnoseProcess(@RequestBody MonitorWarningAnalysisQueryDTO queryDTO, @RequestParam String loginUserName) {
        return monitorWarningAnalysisService.pageDiagnoseProcess(queryDTO, loginUserName);
    }

    /**
     * 患者就诊详情
     */
    @GetMapping("/getPatientDetailByProcessId")
    @ApiOperation(value = "患者就诊详情")
    public PatientVisitDetailVO getPatientDetailByProcessId(@RequestParam String processId) {
        return monitorWarningAnalysisService.getPatientDetailByProcessId(processId);
    }

    /**
     * 报卡病例列表-分页
     */
    @PostMapping("/pageReportProcess")
    @ApiOperation(value = "报卡病例列表-分页")
    public PageInfo<ReportProcessVO> pageReportProcess(@RequestBody MonitorWarningAnalysisQueryDTO queryDTO, @RequestParam String loginUserName) {
        return monitorWarningAnalysisService.pageReportProcess(queryDTO, loginUserName);
    }


    @GetMapping("/getReportProcessDetailByProcessId")
    @ApiOperation(value = "报卡详情")
    public ReportProcessVO getReportProcessDetailByProcessId(@RequestParam String processId) {
        return monitorWarningAnalysisService.getReportProcessDetailByProcessId(processId);
    }

    /**
     * 应排查病例统计
     */
    @PostMapping("/investigatedCaseStat")
    @ApiOperation(value = "应排查病例统计")
    public InvestigatedCaseVO investigatedCaseStat(@RequestBody MonitorWarningAnalysisQueryDTO queryDTO, @RequestParam String loginUserName) {
        return monitorWarningAnalysisService.investigatedCaseStat(queryDTO, loginUserName);
    }

    /**
     * 应排查病例列表
     */
    @PostMapping("/pageInvestigatedCase")
    @ApiOperation(value = "应排查病例列表")
    public PageInfo<InvestigatedCaseVO> pageInvestigatedCase(@RequestBody MonitorWarningAnalysisQueryDTO queryDTO, @RequestParam String loginUserName) {
        return monitorWarningAnalysisService.pageInvestigatedCase(queryDTO, loginUserName);
    }

    /**
     * 应排查任务统计
     */
    @PostMapping("/investigatedTaskStat")
    @ApiOperation(value = "应排查任务统计")
    public InvestigatedTaskVO investigatedTaskStat(@RequestBody MonitorWarningAnalysisQueryDTO queryDTO, @RequestParam String loginUserName) {
        return monitorWarningAnalysisService.investigatedTaskStat(queryDTO, loginUserName);
    }

    /**
     * 应排查任务区域统计-分页列表
     */
    @PostMapping("/pageTaskStatAreaGroup")
    @ApiOperation(value = "应排查任务区域统计-分页列表")
    public PageInfo<InvestigatedTaskVO> pageTaskStatAreaGroup(@RequestBody MonitorWarningAnalysisQueryDTO queryDTO, @RequestParam String loginUserName) {
        return monitorWarningAnalysisService.pageTaskStatAreaGroup(queryDTO, loginUserName);
    }

    /**
     * 应排查任务区域统计导出
     */
    @PostMapping("/taskStatAreaGroupExport")
    @ApiOperation(value = "应排查任务区域统计导出")
    public ResponseEntity<byte[]> taskStatAreaGroupExport(@RequestBody MonitorWarningAnalysisQueryDTO queryDTO, @RequestParam String loginUserName) {
        HttpHeaders httpHeaders = FileUtils.getHttpHeaders("应排查任务区域统计导出.xlsx");
        return new ResponseEntity<>(monitorWarningAnalysisService.taskStatAreaGroupExport(queryDTO, loginUserName), httpHeaders, HttpStatus.CREATED);
    }



    /**
     * 应排查任务处置报告-应排查任务区域统计
     */
    @PostMapping("/taskStatAreaGroup")
    @ApiOperation(value = "应排查任务处置报告-应排查任务区域统计")
    public PageInfo<InvestTaskStatVO> taskStatAreaGroup(@RequestBody MonitorWarningAnalysisQueryDTO queryDTO, @RequestParam String loginUserName) {
        return monitorWarningAnalysisService.checkTaskStatPageList(queryDTO, loginUserName);
    }

    /**
     * 应排查任务处置报告-任务督办区域统计
     */
    @PostMapping("/investTaskStat")
    @ApiOperation(value = "应排查任务处置报告-任务督办区域统计")
    public List<InvestTaskStatVO> investTaskStat(@RequestBody MonitorWarningAnalysisQueryDTO queryDTO, @RequestParam String loginUserName) {
        return monitorWarningAnalysisService.investTaskStat(queryDTO, loginUserName);
    }

}
