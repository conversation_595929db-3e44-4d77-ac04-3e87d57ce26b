package com.iflytek.cdc.province.controller;

import com.iflytek.cdc.province.model.multichannelMonitor.dto.CollaborMonitorQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.DrugSalesVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.IncidentCaseNoticeVO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.MonitorKeywordVO;
import com.iflytek.cdc.province.service.CooperationMonitorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 协同监测分析
 * */
@RestController
@Api(tags = "协同监测分析")
@RequestMapping("/pt/{version}/cooperation/monitor")
public class CooperationMonitorController {
    
    @Resource
    private CooperationMonitorService cooperationMonitorService;

    @PostMapping("/listSalesGrowthRanking")
    @ApiOperation("销售同比增长排行")
    public List<DrugSalesVO> listSalesGrowthRanking(@RequestBody CollaborMonitorQueryDTO queryDTO){

        return cooperationMonitorService.listSalesGrowthRanking(queryDTO);
    }

    @PostMapping("/listIntegratedSalesGrowthRanking")
    @ApiOperation("销售同比增长排行")
    public List<DrugSalesVO> listIntegratedSalesGrowthRanking(@RequestBody CollaborMonitorQueryDTO queryDTO){

        return cooperationMonitorService.listIntegratedSalesGrowthRanking(queryDTO);
    }


    @PostMapping("/listSearchIndexSituation")
    @ApiOperation("监测关键词搜索指数情况")
    public List<MonitorKeywordVO> listSearchIndexSituation(@RequestBody CollaborMonitorQueryDTO queryDTO) {

        return cooperationMonitorService.listSearchIndexSituation(queryDTO);
    }

    @PostMapping("/listIntegratedSearchIndexSituation")
    @ApiOperation("监测关键词搜索指数情况")
    public List<MonitorKeywordVO> listIntegratedSearchIndexSituation(@RequestBody CollaborMonitorQueryDTO queryDTO) {

        return cooperationMonitorService.listIntegratedSearchIndexSituation(queryDTO);
    }

    @PostMapping("/listKeywordSituation")
    @ApiOperation("监测关键词上榜情况")
    public List<MonitorKeywordVO> listKeywordSituation(@RequestBody CollaborMonitorQueryDTO queryDTO) {

        return cooperationMonitorService.listKeywordSituation(queryDTO);
    }

    @PostMapping("/searchKeywordSituation")
    @ApiOperation("监测关键词上榜详细查询")
    public List<MonitorKeywordVO> searchKeywordSituation(@RequestBody CollaborMonitorQueryDTO queryDTO) {

        return cooperationMonitorService.searchKeywordSituation(queryDTO);
    }

    @PostMapping("/listIncidentCaseNotice")
    @ApiOperation("输入病例公告情况")
    public List<IncidentCaseNoticeVO> listIncidentCaseNotice(@RequestBody CollaborMonitorQueryDTO queryDTO) {

        return cooperationMonitorService.listIncidentCaseNotice(queryDTO);
    }



}
