package com.iflytek.cdc.province.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.WarningSignalVO;
import com.iflytek.cdc.province.model.dto.dm.DataCollectionStatusDTO;
import com.iflytek.cdc.province.model.dto.dm.FavoriteQueryDTO;
import com.iflytek.cdc.province.model.dto.dm.FavoritesDetailsQueryDTO;
import com.iflytek.cdc.province.model.dto.dm.FavoritesEditDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.service.DataCollectionService;
import com.iflytek.cdc.edr.vo.dm.FavoritesListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@Api(tags = "数据收藏")
public class DataCollectionController {
    
    @Resource
    private DataCollectionService dataCollectionService;

    /**
     * 查看收藏夹
     * */
    @PostMapping("pt/{version}/dataCollection/getFavoritesList")
    @ApiOperation("查看收藏夹")
    public PageInfo<FavoritesListVO> getFavoritesList(@RequestBody FavoriteQueryDTO dto){

        return dataCollectionService.getFavoritesList(dto);
    }

    /**
     * 编辑收藏夹（包括新建、编辑）
     * */
    @PostMapping("pt/{version}/dataCollection/editFavorites")
    @ApiOperation("编辑收藏夹")
    public void editFavorites(@RequestBody FavoritesEditDTO dto){

        dataCollectionService.editFavorites(dto);
    }

    /**
     * 删除收藏夹
     * */
    @GetMapping("pt/{version}/dataCollection/deleteFavorites")
    @ApiOperation("删除收藏夹")
    public void deleteFavorites(@RequestParam String favoritesId){

        dataCollectionService.deleteFavorites(favoritesId);
    }

    /**
     * 查看收藏夹详情
     * */
    @PostMapping("pt/{version}/dataCollection/getFavoritesDetails")
    @ApiOperation("查看收藏夹详情")
    public PageInfo<String> getFavoritesDetails(@RequestBody FavoritesDetailsQueryDTO dto){

        return dataCollectionService.getFavoritesDetails(dto);
    }

    @PostMapping("pt/{version}/dataCollection/getFavoritesDetailsExport")
    @ApiOperation("查看收藏夹详情-导出")
    public TbCdcmrExportTask getFavoritesDetailsExport(@RequestBody FavoritesDetailsQueryDTO dto){

        return dataCollectionService.getFavoritesDetailsExport(dto);
    }

    /**
     * 数据批量收藏
     * */
    @PostMapping("pt/{version}/dataCollection/batchDataCollection")
    @ApiOperation("数据批量收藏")
    public void batchDataCollection(@RequestBody DataCollectionStatusDTO dto){

        dataCollectionService.batchDataCollection(dto);
    }

    /**
     * 数据批量取消收藏
     * */
    @PostMapping("pt/{version}/dataCollection/batchDataUnCollection")
    @ApiOperation("数据批量取消收藏")
    public void batchDataUnCollection(@RequestBody DataCollectionStatusDTO dto){

        dataCollectionService.batchDataUnCollection(dto);
    }

    /**
     * 查看收藏夹详情
     * */
    @PostMapping("pt/{version}/dataCollection/getSignalFavoritesDetails")
    @ApiOperation("查看信号收藏夹详情")
    public PageInfo<WarningSignalVO> getSignalFavoritesDetails(@RequestBody FavoritesDetailsQueryDTO dto,
                                                               @RequestParam String loginUserId){

        return dataCollectionService.getSignalFavoritesDetails(dto, loginUserId);
    }

    /**
     * 信号收藏夹详情导出
     * */
    @PostMapping("pt/{version}/dataCollection/signalFavoritesDetailsExport")
    @ApiOperation("信号收藏夹详情")
    public TbCdcmrExportTask signalFavoritesDetailsExport(@RequestBody FavoritesDetailsQueryDTO dto,
                                                          @RequestParam String loginUserId){

        return dataCollectionService.signalFavoritesDetailsExport(dto, loginUserId);
    }

}
