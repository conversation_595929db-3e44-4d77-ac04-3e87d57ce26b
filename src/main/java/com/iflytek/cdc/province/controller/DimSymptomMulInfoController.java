package com.iflytek.cdc.province.controller;


import com.iflytek.cdc.province.service.DimSymptomMulInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/pt/{version}/dim/symptom")
@Api(tags = "解析症状维表-维度数据")
public class DimSymptomMulInfoController {

    @Resource
    private DimSymptomMulInfoService dimSymptomMulInfoService;

    @PostMapping("/getTagsByDataSource")
    @ApiOperation("根据数据模型字段过滤标签名称")
    public List<String> getTagsByDataSource(@RequestBody List<String> dataSources) {
        return dimSymptomMulInfoService.getTagsByDataSource(dataSources);
    }

    @PostMapping("/getTagValuesByTag")
    @ApiOperation("根据数据模型字段&标签名称过滤标签值")
    public List<String> getTagValuesBy(String tag, @RequestBody List<String> dataSources) {
        return dimSymptomMulInfoService.getTagValuesBy(dataSources, tag);
    }
}
