package com.iflytek.cdc.province.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
public class ExportTaskController {
    @Resource
    private CdcAdminServiceApi cdcAdminServiceApi;

    @PostMapping("/pt/{version}/exportTask/queryList")
    @ApiOperation("查询导出任务List")
    @ApiImplicitParam(name = "version", paramType = "path",  required = true)
    public PageInfo<TbCdcmrExportTask> queryList(@PathVariable String version,
                                                 @RequestParam String loginUserId,
                                                 @RequestBody ExportTaskQueryDTO queryDTO){
        queryDTO.setLoginUserId(loginUserId);
        return cdcAdminServiceApi.queryExportTaskList(queryDTO);
    }

}
