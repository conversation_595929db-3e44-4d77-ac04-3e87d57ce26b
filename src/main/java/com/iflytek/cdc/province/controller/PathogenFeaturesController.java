package com.iflytek.cdc.province.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.WarningSignalVO;
import com.iflytek.cdc.province.constant.PathogenDataConstant;
import com.iflytek.cdc.province.entity.bu.TbCoronavirusSampleCollection;
import com.iflytek.cdc.province.model.dto.CommonQuery;
import com.iflytek.cdc.province.model.dto.CoronavirusSampleQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.VirusCheckQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.*;
import com.iflytek.cdc.province.model.vo.CoronavirusSampleCollectionVO;
import com.iflytek.cdc.province.service.CoronavirusSampleCollectionService;
import com.iflytek.cdc.province.service.PathogenFeaturesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@Api(tags = "病原特征分析")
@RequestMapping("/pt/{version}/pathogen/features")
public class PathogenFeaturesController {

    @Resource
    private PathogenFeaturesService pathogenFeaturesService;

    @Resource
    private CoronavirusSampleCollectionService coronavirusSampleCollectionService;

    @PostMapping("/virusCheckSituation")
    @ApiOperation("病原各分型检出情况")
    public PageInfo<VirusCheckResultVO> virusCheckSituation(@RequestBody VirusCheckQueryDTO queryDTO){

        return pathogenFeaturesService.virusCheckSituation(queryDTO);
    }

    @PostMapping("/getVirusCheckSituation")
    @ApiOperation("查询病原各分型检出情况")
    public List<VirusCheckResultVO> getVirusCheckSituation(@RequestBody VirusCheckQueryDTO queryDTO){

        return pathogenFeaturesService.getVirusCheckSituation(queryDTO);
    }

    @PostMapping("/getTopDetectedRateBy")
    @ApiOperation("病原检出率TOP10")
    public List<VirusTypeDetectedRateVO> getTopDetectedRateBy(@RequestBody VirusCheckQueryDTO queryDTO){

        return pathogenFeaturesService.getTopDetectedRateBy(queryDTO);
    }

    @PostMapping("/getTopPositiveRateBy")
    @ApiOperation("病例阳性率TOP10")
    public List<VirusTypeDetectedRateVO> getTopPositiveRateBy(@RequestBody VirusCheckQueryDTO queryDTO){

        return pathogenFeaturesService.getTopPositiveRateBy(queryDTO);
    }
    
    @PostMapping("/getInfluenzaVirusAnalysis")
    @ApiOperation("流感病毒分型鉴定分析")
    public List<VirusTypeDetectedRateVO> getInfluenzaVirusAnalysis(@RequestBody VirusCheckQueryDTO queryDTO){

        return pathogenFeaturesService.getInfluenzaVirusAnalysis(queryDTO);
    }

    @PostMapping("/getAntigenAnalysisResult")
    @ApiOperation("抗原分析结果")
    public PageInfo<VirusAntigenResultVO> getAntigenAnalysisResult(@RequestBody VirusCheckQueryDTO queryDTO) {

        return pathogenFeaturesService.getAntigenAnalysisResult(queryDTO);
    }

    @PostMapping("/getResistanceAnalysisResult")
    @ApiOperation("耐药性分析结果明细")
    public PageInfo<VirusResistanceResultVO> getResistanceAnalysisResult(@RequestBody VirusCheckQueryDTO queryDTO) {

        return pathogenFeaturesService.getResistanceAnalysisResult(queryDTO);
    }

    @PostMapping("/getCommonDrugResistantBacteria")
    @ApiOperation("常见耐药菌检出情况")
    public PageInfo<DrugResistantBacteriaVO> getCommonDrugResistantBacteria(@RequestBody VirusCheckQueryDTO queryDTO) {

        return pathogenFeaturesService.getCommonDrugResistantBacteria(queryDTO);
    }

    @PostMapping("/getMDRCheckRateByGradeHospital")
    @ApiOperation("不同等级医院呼吸道耐药菌检出率分析")
    public Map<String, List<DrugResistantHospitalVO>> getMDRCheckRateByGradeHospital(@RequestBody VirusCheckQueryDTO queryDTO) {

        return pathogenFeaturesService.getMDRCheckRateByGradeHospital(queryDTO);
    }

    @PostMapping("/getMDRCheckRateByDistrict")
    @ApiOperation("不同地区呼吸道耐药菌检出情况")
    public PageInfo<MDRCheckAreaDistributionVO> getMDRCheckRateByDistrict(@RequestBody VirusCheckQueryDTO queryDTO) {

        return pathogenFeaturesService.getMDRCheckByDistrict(queryDTO);
    }

    @PostMapping("/getDrugSensitivityBy")
    @ApiOperation("对常用抗菌药物的敏感性")
    public List<DrugSituationVO> getDrugSensitivityBy(@RequestBody VirusCheckQueryDTO queryDTO) {

        return pathogenFeaturesService.getDrugSensitivityBy(queryDTO);
    }

    @PostMapping("/getInfluenzaPathogenType")
    @ApiOperation("流感病毒抗原性分析-分型")
    public List<VirusAntigenResultVO> getInfluenzaPathogenType(){

        return pathogenFeaturesService.getInfluenzaPathogenType();
    }

    @PostMapping("/getInfluenzaReferPathogen")
    @ApiOperation("流感病毒抗原性分析-参考抗原")
    public List<VirusAntigenResultVO> getInfluenzaReferPathogen(){

        return pathogenFeaturesService.getInfluenzaReferPathogen();
    }

    @PostMapping("/getInfluenzaLabList")
    @ApiOperation("流感病毒抗原性分析-实验室列表")
    public List<VirusAntigenResultVO> getInfluenzaLabList(){

        return pathogenFeaturesService.getInfluenzaLabList();
    }

    @PostMapping("/getConstants")
    @ApiOperation("获取静态变量")
    public PathogenDataConstant getConstants(){

        return new PathogenDataConstant();
    }

    @PostMapping("/getCoroSampleCollects")
    @ApiOperation("新冠病毒序列测定明细")
    public List<TbCoronavirusSampleCollection> getCoroSampleCollects(@RequestBody CoronavirusSampleQueryDTO queryDTO) {
        return coronavirusSampleCollectionService.listAll(queryDTO);
    }

    @PostMapping("/groupCoroSubType")
    @ApiOperation("新冠病毒变异亚型监测")
    public List<CoronavirusSampleCollectionVO> groupCoroSubType() {
        return coronavirusSampleCollectionService.groupSubtype();
    }

}