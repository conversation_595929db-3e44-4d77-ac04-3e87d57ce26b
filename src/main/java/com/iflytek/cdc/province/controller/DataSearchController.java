package com.iflytek.cdc.province.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.annotation.DataQueryDtoAnnotation;
import com.iflytek.cdc.province.cache.SensitiveWordCache;
import com.iflytek.cdc.province.model.dto.dm.CombinationQueryDTO;
import com.iflytek.cdc.province.model.dto.dm.KeyWordSearchDTO;
import com.iflytek.cdc.province.model.dto.dm.SuggestSearchDTO;
import com.iflytek.cdc.province.model.vo.SuggestWordVO;
import com.iflytek.cdc.province.service.ModelDataSearchService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Api(tags = "数据搜索（病例池检索）")
@Slf4j
@RequestMapping("/pt/{version}/dataSearch")
public class DataSearchController {

    @Resource
    private ModelDataSearchService dataSearchService;

    @Resource
    private SensitiveWordCache sensitiveWordCache;

    @PostMapping("/retrieveTableSearchByKey")
    @ApiOperation("关键词搜索")
    @DataQueryDtoAnnotation()
    public PageInfo<String> retrieveTableSearchByKey(@RequestBody KeyWordSearchDTO dto, @RequestParam String loginUserId) {
        if (sensitiveWordCache.contains(dto.getKeyWord())) {
            throw new MedicalBusinessException("输入内容包含敏感词，请重新输入");
        }
        return dataSearchService.retrieveTableSearchByKey(dto);
    }

    /**
     * 组合搜索
     */
    @PostMapping("/retrieveTableSearchByConditions")
    @ApiOperation("组合搜索")
    @DataQueryDtoAnnotation()
    public PageInfo<String> retrieveTableSearchByConditions(@RequestBody CombinationQueryDTO dto, @RequestParam String loginUserId) {
        if (CollUtil.isNotEmpty(dto.getConditionDTOList())) {
            dto.getConditionDTOList().forEach(condition -> {
                if (condition != null && sensitiveWordCache.contains(condition.getValue())) {
                    throw new MedicalBusinessException("输入内容包含敏感词，请重新输入");
                }
            });
        }
        return dataSearchService.retrieveTableSearchByConditions(dto);
    }

    /**
     * 获取联想词
     */
    @PostMapping("/suggest")
    @ApiOperation("获取联想词")
    public PageInfo<SuggestWordVO> getSuggestWords(@RequestBody SuggestSearchDTO dto) {
        if (StrUtil.isBlank(dto.getText())) {
            return PageInfo.emptyPageInfo();
        }
        return dataSearchService.getSuggestWords(dto);
    }

    /**
     * 检查敏感词
     */
    @GetMapping("/sensitive/check")
    @ApiOperation("检查敏感词")
    public Boolean checkSensitiveWords(@RequestParam String text) {
        return sensitiveWordCache.contains(text);
    }

    /**
     * 刷新敏感词
     */
    @PostMapping("/sensitive/refresh")
    @ApiOperation("刷新敏感词")
    public void refreshSensitiveWords() {
        sensitiveWordCache.refreshCache();
    }
}
