package com.iflytek.cdc.province.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ProcessPathogenQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.dto.ProcessStatAnalysisQueryDTO;
import com.iflytek.cdc.province.model.multichannelMonitor.vo.*;
import com.iflytek.cdc.province.service.MultichannelProcessStatService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/pt/{version}/multichannel/processStat")
public class MultichannelProcessStatController {

    @Resource
    private MultichannelProcessStatService multichannelProcessStatService;

    /*************************************疾病特征分析*******************************************/
    @PostMapping("/getEpidemiologicalProfile")
    @ApiOperation("呼吸道传染病发病概况")
    public PageInfo<AdsMsProcessRespVO> getEpidemiologicalProfile(@RequestBody ProcessStatAnalysisQueryDTO queryDTO){

        return multichannelProcessStatService.getEpidemiologicalProfile(queryDTO);
    }

    @PostMapping("/getDiseaseTrend")
    @ApiOperation("呼吸道传染病发病趋势")
    public List<ProcessTimeLineStatVO> getDiseaseTrend(@RequestBody ProcessStatAnalysisQueryDTO queryDTO){

        return multichannelProcessStatService.getDiseaseTrend(queryDTO);
    }

    @PostMapping("/getConfirmationOfSyndrome")
    @ApiOperation("呼吸道症候群确诊情况")
    public List<SyndromeConfirmSituationVO> getConfirmationOfSyndrome(@RequestBody ProcessStatAnalysisQueryDTO queryDTO){

        return multichannelProcessStatService.getConfirmationOfSyndrome(queryDTO);
    }

    @PostMapping("/getMonitorProcessFlow")
    @ApiOperation("呼吸道感染病例监测流向")
    public List<MonitorProcessFlowVO> getMonitorProcessFlow(@RequestBody ProcessStatAnalysisQueryDTO queryDTO){

        return multichannelProcessStatService.getMonitorProcessFlow(queryDTO);
    }

    @PostMapping("/getPneumoniaOccursSituation")
    @ApiOperation("肺炎发生情况")
    public PneumoniaOccursSituationVO getPneumoniaOccursSituation(@RequestBody ProcessStatAnalysisQueryDTO queryDTO){

        return multichannelProcessStatService.getPneumoniaOccursSituation(queryDTO);
    }

    @PostMapping("/getProcessChanges")
    @ApiOperation("现存病例量变化")
    public List<IndicatorDataVO> getProcessChanges(@RequestBody ProcessStatAnalysisQueryDTO queryDTO){

        return multichannelProcessStatService.getProcessChanges(queryDTO);
    }

    /*************************************人群分布*******************************************/
    @PostMapping("/sexDistribution")
    @ApiOperation("性别分布")
    public PageInfo<ProcessSexDistributionVO> sexDistribution(@RequestBody ProcessStatAnalysisQueryDTO queryDTO){

        return multichannelProcessStatService.sexDistribution(queryDTO);
    }

    @PostMapping("/sexTrendsInDisease")
    @ApiOperation("分性别发病趋势对比")
    public List<ProcessTimeLineStatVO> sexTrendsInDisease(@RequestBody ProcessStatAnalysisQueryDTO queryDTO){

        return multichannelProcessStatService.sexTrendsInDisease(queryDTO);
    }

    @PostMapping("/ageDistribution")
    @ApiOperation("年龄分布")
    public PageInfo<ProcessDistributionVO> ageDistribution(@RequestBody ProcessStatAnalysisQueryDTO queryDTO){

        return multichannelProcessStatService.ageDistribution(queryDTO);
    }

    @PostMapping("/ageTrendsInDisease")
    @ApiOperation("分年龄发病趋势对比")
    public List<ProcessTrendVO> ageTrendsInDisease(@RequestBody ProcessStatAnalysisQueryDTO queryDTO){

        return multichannelProcessStatService.ageTrendsInDisease(queryDTO);
    }


    @PostMapping("/jobDistribution")
    @ApiOperation("发病数同比增长TOP5职业分布")
    public PageInfo<ProcessDistributionVO> jobDistribution(@RequestBody ProcessStatAnalysisQueryDTO queryDTO){

        return multichannelProcessStatService.jobDistribution(queryDTO);
    }

    @PostMapping("/populationCharacteristics")
    @ApiOperation("病例人群特征分析")
    public PopulationStatVO populationCharacteristics(@RequestBody ProcessStatAnalysisQueryDTO queryDTO){

        return multichannelProcessStatService.populationCharacteristics(queryDTO);
    }

    /*************************************地区分布*******************************************/
    @PostMapping("/regionDistribution")
    @ApiOperation("行政区分布")
    public PageInfo<ProcessDistributionVO> regionDistribution(@RequestBody ProcessStatAnalysisQueryDTO queryDTO){

        return multichannelProcessStatService.regionDistribution(queryDTO);
    }

    @PostMapping("/regionProcessTrends")
    @ApiOperation("分行政区发病趋势对比")
    public List<ProcessTrendVO> regionProcessTrends(@RequestBody ProcessStatAnalysisQueryDTO queryDTO){

        return multichannelProcessStatService.regionProcessTrends(queryDTO);
    }

    @PostMapping("/urbanAndRuralDistribution")
    @ApiOperation("传染病城乡分布")
    public PageInfo<ProcessUrbanRuralDistributionVO> urbanAndRuralDistribution(@RequestBody ProcessStatAnalysisQueryDTO queryDTO){

        return multichannelProcessStatService.urbanAndRuralDistribution(queryDTO);
    }

    @PostMapping("/urbanAndRuralProcessTrends")
    @ApiOperation("城乡发病趋势对比")
    public List<ProcessTrendVO> urbanAndRuralProcessTrends(@RequestBody ProcessStatAnalysisQueryDTO queryDTO){

        return multichannelProcessStatService.urbanAndRuralProcessTrends(queryDTO);
    }

    @PostMapping("/areaDistribution")
    @ApiOperation("病例地区分布")
    public List<ProcessAreaDistributionVO> areaDistribution(@RequestBody ProcessStatAnalysisQueryDTO queryDTO){

        return multichannelProcessStatService.areaDistribution(queryDTO);
    }

    /*************************************时间分布*******************************************/
    @PostMapping("/yearOnYearGrowthByDate")
    @ApiOperation("发病数同比增长TOP10呼吸道传染病发病季节变化趋势")
    public List<DiseaseProcessTimeLineVO> yearOnYearGrowthByDate(@RequestBody ProcessStatAnalysisQueryDTO queryDTO) {

        return multichannelProcessStatService.yearOnYearGrowthByDate(queryDTO);
    }

    @PostMapping("/yearOnYearGrowthByArea")
    @ApiOperation("发病数同比增长TOP10呼吸道传染病各地区发病变化趋势")
    public List<DiseaseProcessAreaDistributionVO> yearOnYearGrowthByArea(@RequestBody ProcessStatAnalysisQueryDTO queryDTO) {

        return multichannelProcessStatService.yearOnYearGrowthByArea(queryDTO);
    }

    /*************************************病原谱分析*******************************************/
    @PostMapping("/listPositiveSituation")
    @ApiOperation("呼吸道传染病病原阳性概况")
    public ProcessPositiveSituationVO listPositiveSituation(@RequestBody ProcessPathogenQueryDTO queryDTO) {

        return multichannelProcessStatService.listPositiveSituation(queryDTO);
    }

    @PostMapping("/listPositiveTimeLine")
    @ApiOperation("呼吸道传染病病原阳性概况 趋势")
    public List<ProcessPositiveSituationVO> listPositiveTimeLine(@RequestBody ProcessPathogenQueryDTO queryDTO) {

        return multichannelProcessStatService.listPositiveTimeLine(queryDTO);
    }

    @PostMapping("/listTopPositiveRatioBy")
    @ApiOperation("呼吸道病原阳性率TOP20")
    public List<PositivePathogenVO> listTopPositiveRatioBy(@RequestBody ProcessPathogenQueryDTO queryDTO) {

        return multichannelProcessStatService.listTopPositiveRatioBy(queryDTO);
    }

    @PostMapping("/listTopPositiveRatioGrowthBy")
    @ApiOperation("阳性率同比增长TOP20")
    public List<PositivePathogenVO> listTopPositiveRatioGrowthBy(@RequestBody ProcessPathogenQueryDTO queryDTO) {

        return multichannelProcessStatService.listTopPositiveRatioGrowthBy(queryDTO);
    }

    @PostMapping("/listMicrobeAndVirusPathogen")
    @ApiOperation("呼吸道传染病 病毒/细菌 病原谱")
    public List<PathogenSpectrumCompareVO> listMicrobeAndVirusPathogen(@RequestBody ProcessPathogenQueryDTO queryDTO) {
        return multichannelProcessStatService.listMicrobeAndVirusPathogen(queryDTO);
    }

    @PostMapping("/listPositiveRatioByAgeAndSex")
    @ApiOperation("不同性别各年龄段呼吸道传染病病原阳性率对比（病原范围：病原阳性率TOP10）")
    public Map<String, List<PathogenPositiveAgeAndSexVO>> listPositiveRatioByAgeAndSex(@RequestBody ProcessPathogenQueryDTO queryDTO) {

        return multichannelProcessStatService.listPositiveRatioByAgeAndSex(queryDTO);
    }

    @PostMapping("/listCombinationInfectionRate")
    @ApiOperation("呼吸道传染病病原复合感染率")
    public List<PathogenCombinationRateVO> listCombinationInfectionRate(@RequestBody ProcessPathogenQueryDTO queryDTO) {

        return multichannelProcessStatService.listCombinationInfectionRate(queryDTO);
    }

    @PostMapping("/listPathogenInfectionSituation")
    @ApiOperation("呼吸道传染病病原感染情况")
    public PageInfo<PathogenResultVO> listPathogenInfectionSituation(@RequestBody ProcessPathogenQueryDTO queryDTO) {

        return multichannelProcessStatService.listPathogenInfectionSituation(queryDTO);
    }

}
