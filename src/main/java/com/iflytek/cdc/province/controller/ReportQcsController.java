package com.iflytek.cdc.province.controller;

import com.iflytek.cdc.province.entity.bu.ReportQcsMainRecord;
import com.iflytek.cdc.province.service.ReportQcsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@Api(tags = "省统筹-报卡督导管理")
public class ReportQcsController {

    @Resource
    private ReportQcsService reportQcsService;

    @PostMapping("pt/{version}/reportQcs/compareData")
    @ApiOperation("报卡督导-对比数据")
    @Transactional(rollbackFor = Exception.class)
    public Boolean compareQcsData(@RequestBody ReportQcsMainRecord mainRecord,
                                  @RequestParam String loginUserId) {
        return reportQcsService.compareData(mainRecord);
    }
}
