package com.iflytek.cdc.province.controller;

import com.iflytek.cdc.province.model.dto.IdentifyRecordDTO;
import com.iflytek.cdc.province.service.DataModelDataService;
import com.iflytek.cdc.edr.vo.dm.DataModelDetailVO;
import com.iflytek.cdc.edr.vo.dm.DataModelVO;
import com.iflytek.cdc.edr.vo.dm.IdentifyRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


@Slf4j
@Api(tags = "数据模型-数据查询")
@RequestMapping("/pt/{version}/dataModel")
@RestController
public class DataModelServiceController {

    @Resource
    DataModelDataService dataModelDataService;


    @ApiOperation("根据模型和条件查询数据")
    @Deprecated
    @PostMapping("/requestDataByModel")
    public DataModelVO requestDataByModel(@RequestParam String modelId, @RequestBody Map<String, Object> params) {
        return  dataModelDataService.requestModelData(modelId, params);
    }

    @ApiOperation("查询此处详情应该使用哪一个数据模型展示")
    @GetMapping("/getDataModelBySystem")
    public List<DataModelDetailVO> getDataModelBySystem(@RequestParam(required = false) String formTemplateCode,
                                                        @RequestParam(required = false) String configInfo,
                                                        @RequestParam(required = false) String modelVersionId) {

        return  dataModelDataService.getDataModelByCode(formTemplateCode, configInfo, modelVersionId);
    }

    @ApiOperation("根据病程id查询该病程下的病历 唯一标识以及该标识对应的recordId")
    @PostMapping("/getIdentifyAndRecordId")
    public List<IdentifyRecordVO> getIdentifyAndRecordId(@RequestBody IdentifyRecordDTO dto) {

        return dataModelDataService.getIdentifyAndRecordId(dto);
    }

    @ApiOperation("提供外部调用，查询modelId对应模型的结构")
    @GetMapping("/getDataModelInfo")
    public DataModelVO getDataModelInfo(@RequestParam(required = false) String modelId,
                                        @RequestParam(required = false) String modelVersionId) {

        return  dataModelDataService.getDataModelInfo(modelId, modelVersionId);
    }

    @ApiOperation("根据模型配置展示数据模型详情")
    @PostMapping("/getDetailDataByModel")
    public String getDetailDataByModel(@RequestParam String loginUserId,
                                       @RequestParam String modelId,
                                       @RequestBody Map<String, Object> params) {

        return dataModelDataService.getDetailDataByModel(loginUserId, modelId, params);
    }

    @ApiOperation("查询填报表内容")
    @PostMapping("/getFormReportBy")
    public DataModelVO getFormReportBy(@RequestParam String modelId,
                                           @RequestBody Map<String, Object> params) {

        return dataModelDataService.getFormReportBy(modelId, params);
    }

}
