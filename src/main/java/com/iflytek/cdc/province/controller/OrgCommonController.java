package com.iflytek.cdc.province.controller;

import com.iflytek.cdc.edr.dto.org.OrgParams;
import com.iflytek.cdc.province.model.dto.dm.OrgQueryDTO;
import com.iflytek.cdc.province.model.vo.OrgInfoVO;
import com.iflytek.cdc.province.model.vo.TreeNode;
import com.iflytek.cdc.province.model.vo.ValueDomainVO;
import com.iflytek.cdc.province.service.OrgCommonService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class OrgCommonController {

    @Resource
    private OrgCommonService orgCommonService;

    @PostMapping("pt/{version}/getAllOrgInfo")
    @ApiOperation("值域查询机构使用")
    public List<ValueDomainVO> getAllOrgInfo() {
        return orgCommonService.getAllOrgInfo();
    }

    @PostMapping("pt/{version}/edr/getAllOrgList")
    @ApiOperation("查询机构列表")
    public List<OrgInfoVO> getAllOrgList(@RequestBody OrgParams orgParams) {
        return orgCommonService.getAllOrgList(orgParams);
    }

    @PostMapping("pt/{version}/getOrgList")
    @ApiOperation("查询机构列表")
    public List<TreeNode> getOrgList(@RequestBody OrgQueryDTO dto) {

        return orgCommonService.getOrgList(dto);
    }
}
