package com.iflytek.cdc.province.controller;

import com.iflytek.cdc.province.service.EpidemicSurveillanceStatService;
import com.iflytek.cdc.edr.utils.FileUtils;
import com.iflytek.cdc.province.model.dto.EpSurStatQueryDTO;
import com.iflytek.cdc.province.model.vo.OrgIndexStatVO;
import com.iflytek.cdc.province.model.vo.ReportCardStatVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "疫情监测统计")
@RequestMapping("/pt/{version}/ep/surveillance")
public class EpidemicSurveillanceStatController {


    @Resource
    private EpidemicSurveillanceStatService epidemicSurveillanceStatService;


    @PostMapping("/orgIndexStat")
    @ApiOperation(value = "医疗机构指标统计")
    public OrgIndexStatVO orgIndexStat(@RequestBody EpSurStatQueryDTO queryDTO, @RequestParam String loginUserId) {

        return epidemicSurveillanceStatService.orgIndexStat(queryDTO);
    }


    @PostMapping("/orgIndexStatExport")
    @ApiOperation(value = "医疗机构指标统计-导出")
    public ResponseEntity<byte[]> orgIndexStatExport(@RequestBody EpSurStatQueryDTO queryDTO, @RequestParam String loginUserId) {
        HttpHeaders httpHeaders = FileUtils.getHttpHeaders("医疗机构指标统计.xlsx");
        return new ResponseEntity<>(epidemicSurveillanceStatService.orgIndexStatExport(queryDTO), httpHeaders, HttpStatus.CREATED);
    }

    @PostMapping("/reportCardStat")
    @ApiOperation(value = "报告卡统计")
    public ReportCardStatVO reportCardStat(@RequestBody EpSurStatQueryDTO queryDTO, @RequestParam String loginUserId) {

        return epidemicSurveillanceStatService.reportCardStat(queryDTO);
    }


    @PostMapping("/orgTypeReportStat")
    @ApiOperation(value = "单位类型报告卡构成")
    public List<ReportCardStatVO> orgTypeReportStat(@RequestBody EpSurStatQueryDTO queryDTO, @RequestParam String loginUserId) {

        return epidemicSurveillanceStatService.orgTypeReportStat(queryDTO);
    }


    @PostMapping("/reportWayStat")
    @ApiOperation(value = "单位报告卡直报代报统计")
    public ReportCardStatVO reportWayStat(@RequestBody EpSurStatQueryDTO queryDTO, @RequestParam String loginUserId) {

        return epidemicSurveillanceStatService.reportWayStat(queryDTO);
    }


    @PostMapping("/loginStat")
    @ApiOperation(value = "单位用户登录统计")
    public ReportCardStatVO loginStat(@RequestBody EpSurStatQueryDTO queryDTO, @RequestParam String loginUserId) {

        return epidemicSurveillanceStatService.loginStat(queryDTO);
    }


    @PostMapping("/directReportStat")
    @ApiOperation(value = "直报情况")
    public List<ReportCardStatVO> directReportStat(@RequestBody EpSurStatQueryDTO queryDTO, @RequestParam String loginUserId) {

        return epidemicSurveillanceStatService.directReportStat(queryDTO);
    }


    @PostMapping("/repeatReportStat")
    @ApiOperation(value = "重卡统计")
    public List<ReportCardStatVO> repeatReportStat(@RequestBody EpSurStatQueryDTO queryDTO, @RequestParam String loginUserId) {

        return epidemicSurveillanceStatService.repeatReportStat(queryDTO);
    }


    @PostMapping("/reportcardTimeTrend")
    @ApiOperation(value = "报卡采集量按时间统计")
    public List<ReportCardStatVO> reportcardTimeTrend(@RequestBody EpSurStatQueryDTO queryDTO, @RequestParam String loginUserName) {

        return epidemicSurveillanceStatService.reportcardTimeTrend(queryDTO, loginUserName);
    }


}
