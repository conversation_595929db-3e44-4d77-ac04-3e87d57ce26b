package com.iflytek.cdc.province.controller;

import com.iflytek.cdc.province.entity.ads.AdsBizEdrInfo;
import com.iflytek.cdc.province.service.BizEdrInfoService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/pt/{version}/bizEdrInfo")
public class BizEdrInfoController {
    @Resource
    private BizEdrInfoService bizEdrInfoService;
    /**
     * 根据id获取ads_biz_edr_info对象
     */
    @GetMapping("/getBizEdrInfoById")
    public AdsBizEdrInfo getById(@RequestParam String archiveId) {
        return bizEdrInfoService.getById(archiveId);
    }

    /**
     * 根据姓名，身份证号码 获取ads_biz_edr_info对象
     */
    @PostMapping("/getBizEdrInfoByNameAndCard")
    public List<HashMap<String,Object>> getBizEdrInfoByNameAndCard(@RequestBody List<HashMap<String, String>> dtoList) {
        return bizEdrInfoService.getBizEdrInfoByNameAndCard(dtoList);
    }
}
