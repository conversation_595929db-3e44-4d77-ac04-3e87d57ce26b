package com.iflytek.cdc.province.controller;

import com.iflytek.cdc.edr.dto.workbench.UserTodoListDTO;
import com.iflytek.cdc.edr.vo.workbench.UserTodoListVO;
import com.iflytek.cdc.province.service.PandemicMonitorCommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "工作台-代办、已办")
public class WorkbenchController {
    @Resource
    private PandemicMonitorCommonService pandemicMonitorCommonService;
    @PostMapping("pt/{version}/workbench/pandemicMonitor/getMedicalListToDo")
    @ApiOperation("查询病例列表代办")
    public List<UserTodoListDTO> getMedicalList(@RequestBody UserTodoListVO reqVO,
                                          @RequestParam(required = false) Integer limitNum,
                                          @RequestParam String loginUserId) {

        return pandemicMonitorCommonService.getMedicalListToDo(reqVO,limitNum);
    }

}
