package com.iflytek.cdc.province.controller;

import com.iflytek.cdc.province.model.dto.BmMonitorResultDTO;
import com.iflytek.cdc.province.model.dto.ByMonitorResultDTO;
import com.iflytek.cdc.province.service.BmByMgInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 病媒/病原/免规controller
 */
@RestController
@Api(tags = "病媒/病原/免归信息")
@RequestMapping("/pt/{version}/bmbymg")
public class BmByMgInfoController {

    @Resource
    private BmByMgInfoService bmByMgInfoService;

    @GetMapping("getBmMonitorResultByRelateTaskId")
    @ApiOperation("根据relateTaskId查询病媒监测结果")
    public BmMonitorResultDTO getBmMonitorResultByRelateTaskId(@RequestParam String relateTaskId, @RequestParam String monitorType) {
        return bmByMgInfoService.getBmMonitorResultByRelateTaskId(relateTaskId, monitorType);
    }

    @GetMapping("getByMonitorResultByRelateTaskId")
    @ApiOperation("根据relateTaskId查询病原监测结果")
    public ByMonitorResultDTO getByMonitorResultByRelateTaskId(@RequestParam String relateTaskId, @RequestParam String sampleType) {
        return bmByMgInfoService.getByMonitorResultByRelateTaskId(relateTaskId, sampleType);
    }
}
