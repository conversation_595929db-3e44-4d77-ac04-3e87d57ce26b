package com.iflytek.cdc.province.controller;

import cn.hutool.core.collection.CollUtil;
import com.iflytek.cdc.province.entity.bu.SyndromeVerifyRecord;
import com.iflytek.cdc.province.service.SyndromeVerifyRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 症候群核实记录控制类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@RestController
@Api(tags = "省统筹-症候群核实记录管理")
@RequestMapping("/pt/{version}/syndrome/verify")
public class SyndromeVerifyRecordController {

    @Resource
    private SyndromeVerifyRecordService syndromeVerifyRecordService;
//
//    /**
//     * 根据ID查询症候群核实记录
//     *
//     * @param id 记录ID
//     * @param loginUserId 登录用户ID
//     * @return 症候群核实记录
//     */
//    @GetMapping("/getById")
//    @ApiOperation("根据ID查询症候群核实记录")
//    public SyndromeVerifyRecord getById(
//            @ApiParam(value = "记录ID", required = true) @RequestParam String id,
//            @ApiParam(value = "登录用户ID", required = true) @RequestParam String loginUserId) {
//        return syndromeVerifyRecordService.getById(id);
//    }

    /**
     * 根据ID列表查询症候群核实记录
     *
     * @param ids ID列表
     * @param loginUserId 登录用户ID
     * @return 症候群核实记录列表
     */
    @PostMapping("/getByIds")
    @ApiOperation("根据ID列表查询症候群核实记录")
    public List<String> getByIds(
            @ApiParam(value = "ID列表", required = true) @RequestBody List<String> ids,
            @ApiParam(value = "登录用户ID", required = true) @RequestParam String loginUserId) {
        List<SyndromeVerifyRecord> records = syndromeVerifyRecordService.getByIds(ids);
        if (CollUtil.isNotEmpty(records)){
            return records.stream().map(SyndromeVerifyRecord::getId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 插入症候群核实记录
     *
     * @param  id
     * @param loginUserId 登录用户ID
     * @return 操作结果
     */
    @PostMapping("/insert")
    @ApiOperation("插入症候群核实记录")
    public Boolean insert(
            @ApiParam(value = "症候群核实记录", required = true) @RequestParam String id,
            @ApiParam(value = "登录用户ID", required = true) @RequestParam String loginUserId) {
        SyndromeVerifyRecord record = new SyndromeVerifyRecord();
        record.setId(id);
        return syndromeVerifyRecordService.insert(record);
    }



} 