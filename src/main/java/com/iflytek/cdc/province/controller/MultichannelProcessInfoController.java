package com.iflytek.cdc.province.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO;
import com.iflytek.cdc.province.enums.ProcessInfoTypeEnum;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.dto.PathogenCombinationQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.vo.MedCntIndicatorVO;
import com.iflytek.cdc.province.model.vo.PathogenCheckVO;
import com.iflytek.cdc.province.model.pathogen.PathogenInfectionSituationVO;
import com.iflytek.cdc.province.service.CommonProcessInfoService;
import com.iflytek.cdc.province.service.MultichannelProcessInfoService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @deprecated 未找到使用模块或子系统
 */
@RestController
@RequestMapping("/pt/{version}/multichannel/processInfo")
public class MultichannelProcessInfoController {

    @Resource
    private MultichannelProcessInfoService processInfoService;

    @Resource
    private CommonProcessInfoService commonProcessInfoService;

    @PostMapping("/listMultichannelProcessInfo")
    @ApiOperation("分页获取多渠道病例查询")
    public PageInfo<MsProcessSimpleInfoVO> listMultichannelProcessInfo(@RequestBody MsProcessSimpleInfoQueryDTO queryDTO){

        return commonProcessInfoService.simpleInfoPageList(ProcessInfoTypeEnum.MULTICHANNEL.getCode(), queryDTO);
    }

    @PostMapping("/exportMultichannelProcessInfo")
    @ApiOperation("导出多渠道病例查询")
    public TbCdcmrExportTask exportMultichannelProcessInfo(@RequestBody MsProcessSimpleInfoQueryDTO queryDTO,
                                                           @RequestParam String loginUserId){
        return commonProcessInfoService.exportSimpleInfo(ProcessInfoTypeEnum.MULTICHANNEL.getCode(), queryDTO);
    }

    @PostMapping("/listMultichannelMedCntIndicatorFromDw")
    @ApiOperation("查询多渠道病例病人信息")
    public List<MedCntIndicatorVO> listMultichannelMedCntIndicatorFromDw(@RequestBody MsProcessSimpleInfoQueryDTO queryDTO){

        return commonProcessInfoService.listMedCntIndicator(ProcessInfoTypeEnum.MULTICHANNEL.getCode(), queryDTO);
    }

    @PostMapping("/listMultichannelPathogenCheckResult")
    @ApiOperation("查询多渠道病例病原检测结果")
    public List<PathogenCheckVO> listMultichannelPathogenCheckResult(@RequestBody PathogenCombinationQueryDTO dto){

        return processInfoService.listMultichannelPathogenCheckResult(dto);
    }

    @PostMapping("/listPositivePathogenCheckResult")
    @ApiOperation("查询多渠道病例阳性病原检测")
    public List<PathogenCheckVO> listPositivePathogenCheckResult(@RequestBody PathogenCombinationQueryDTO dto){

        return processInfoService.listPositivePathogenCheckResult(dto);
    }

    @PostMapping("/getPathogenInfectionSituation")
    @ApiOperation("查询多渠道病例病原感染情况")
    public List<PathogenInfectionSituationVO> getPathogenInfectionSituation(@RequestBody PathogenCombinationQueryDTO dto){

        return processInfoService.getPathogenInfectionSituation(dto);
    }

}
