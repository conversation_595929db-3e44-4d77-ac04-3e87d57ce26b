package com.iflytek.cdc.province.controller;

import com.iflytek.cdc.province.mapper.pg.AdsPdPathogenCheckInfoMapper;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.dto.PathogenCombinationQueryDTO;
import com.iflytek.cdc.province.model.pathogen.PathogenInfectionSituationVO;
import com.iflytek.cdc.province.model.vo.MedCntIndicatorVO;
import com.iflytek.cdc.province.model.vo.PathogenCheckVO;
import com.iflytek.cdc.province.service.IntegratedProcessInfoService;
import com.iflytek.cdc.province.service.impl.IntegratedProcessInfoServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api(tags = "多渠道综合预警病原谱分析")
@RequestMapping("/pt/{version}/integrated/processInfo")
public class IntegratedProcessInfoController {

    @Resource
    private IntegratedProcessInfoService integratedProcessInfoService;

    @Resource
    private IntegratedProcessInfoServiceImpl integratedProcessInfoServiceImpl;

    @PostMapping("/getPositiveRatioByAgeAndSex")
    @ApiOperation("不同性别各年龄段呼吸道传染病病原阳性率对比（病原范围：病原阳性率TOP10）")
    public List<PathogenCheckVO> getPositiveRatioByAgeAndSex(@RequestBody PathogenCombinationQueryDTO queryDTO) {

        return integratedProcessInfoService.listIntegratedPathogenCheckResult(queryDTO);
    }

    @PostMapping("/listIntegratedCombinationInfectionRate")
    @ApiOperation("多渠道综合预警病原复合感染率")
    public List<PathogenCheckVO> listIntegratedCombinationInfectionRate(@RequestBody PathogenCombinationQueryDTO queryDTO) {

        return integratedProcessInfoService.listIntegratedPositivePathogenCheckResult(queryDTO);
    }

    @PostMapping("/listIntegratedPathogenInfectionSituation")
    @ApiOperation("呼吸道传染病病原感染情况")
    public List<PathogenInfectionSituationVO> listIntegratedPathogenInfectionSituation(@RequestBody PathogenCombinationQueryDTO queryDTO) {

        return integratedProcessInfoService.listIntegratedPathogenInfectionSituation(queryDTO);
    }

    @PostMapping("/listIntegratedMedCntIndicatorFromDw")
    @ApiOperation("查询多渠道病例病人信息")
    public List<MedCntIndicatorVO> listMultichannelMedCntIndicatorFromDw(@RequestBody MsProcessSimpleInfoQueryDTO queryDTO){

        return integratedProcessInfoServiceImpl.listIntegratedMedCntIndicator(queryDTO);
    }

}
