package com.iflytek.cdc.province.controller;

import com.alibaba.druid.pool.DruidDataSource;
import com.iflytek.cdc.edr.enums.DateDimEnum;
import com.iflytek.cdc.province.cache.DimRegionCache;
import com.iflytek.cdc.province.entity.dim.DimRegionNation;
import com.iflytek.cdc.province.enums.DesensitizedTypeEnum;
import com.iflytek.cdc.province.model.vo.DruidPoolInfo;
import com.iflytek.cdc.province.model.vo.TreeNode;
import com.iflytek.cdc.province.service.MdmRegisterService;
import com.iflytek.zhyl.mdm.sdk.pojo.PatientBaseInfoDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.aop.framework.Advised;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.Arrays;
import java.util.List;

@RestController
@Api("通用获取接口")
public class CommonController {
    
    @Resource
    MdmRegisterService mdmRegisterService;

    @Resource
    BeanFactory beanFactory;

    @Resource
    private DimRegionCache dimRegionCache;

    @PostMapping("/pt/{version}/getDateDimEnums")
    @ApiOperation("查询日期指标常量")
    public List<DateDimEnum> getDateDimEnums(){
        return Arrays.asList(DateDimEnum.values());
    }

    @PostMapping("/pt/{version}/getDesensitizedType")
    @ApiOperation("获取脱敏类型字典")
    public List<DesensitizedTypeEnum> getDesensitizedType(){
        return Arrays.asList(DesensitizedTypeEnum.values());
    }

    @PostMapping("/pt/{version}/registerGlobalPersonId")
    @ApiOperation("生成empiId")
    public String registerGlobalPersonId(@RequestBody PatientBaseInfoDto patientBaseInfoDto){
        return mdmRegisterService.registerGlobalPersonId(patientBaseInfoDto);
    }

    @PostMapping("/druid/info")
    @ApiOperation("数据库连接池")
    public DruidPoolInfo printPoolStats(String mdDataSource) throws Exception {
        DataSource dataSource = beanFactory.getBean(mdDataSource, DataSource.class);
        DruidDataSource druidDataSource;
        try {
            // 如果是Spring代理，先获取目标对象
            if (dataSource instanceof Advised) {
                Object target = ((Advised) dataSource).getTargetSource().getTarget();
                if (target instanceof DataSource) {
                    dataSource = (DataSource) target;
                }
            }

            // 如果是Druid数据源，直接返回
            if (dataSource instanceof DruidDataSource) {
                druidDataSource = (DruidDataSource) dataSource;
            } else {
                throw new IllegalStateException("数据源不是Druid类型: " + dataSource.getClass().getName());
            }

        } catch (Exception e) {
            throw new IllegalStateException("无法解包数据源", e);
        }

        DruidPoolInfo poolInfo = new DruidPoolInfo();
        poolInfo.setUrl(druidDataSource.getUrl());
        poolInfo.setUsername(druidDataSource.getUsername());
        poolInfo.setInitialSize(druidDataSource.getInitialSize());
        poolInfo.setMinIdle(druidDataSource.getMinIdle());
        poolInfo.setMaxActive(druidDataSource.getMaxActive());
        poolInfo.setActiveCount(druidDataSource.getActiveCount());
        poolInfo.setPoolingCount(druidDataSource.getPoolingCount());
        poolInfo.setWaitThreadCount(druidDataSource.getWaitThreadCount());
        poolInfo.setMaxWait(druidDataSource.getMaxWait());
        poolInfo.setValidationQuery(druidDataSource.getValidationQuery());

        return poolInfo;
    }

    /**
     * 从数仓维表中获取区划信息
     * 
     * @param code 区划编码
     * @return 区划信息
     */
    @GetMapping("/pt/{version}/getRegionByCode")
    @ApiOperation("从数仓维表中获取区划信息")
    public DimRegionNation getRegionByCode(@RequestParam String code) {
        return dimRegionCache.getByCode(code);
    }

    /**
     * 从缓存中获取全部区域数据
     */
    @GetMapping("/pt/{version}/getAllRegions")
    @ApiOperation("从缓存中获取全部区域数据")
    public List<DimRegionNation> getAllRegions() {
        return dimRegionCache.getAllRegions();
    }

    /**
     * 从缓存中获取省市区街道的层级数据
     */
    @GetMapping("/pt/{version}/getAreaInfo")
    @ApiOperation("从缓存中获取省市区街道的层级数据")
    public List<TreeNode> getAreaInfo() {
        return dimRegionCache.getAreaInfo();
    }

    /**
     * 手动刷新区域缓存
     */
    @PostMapping("/pt/{version}/refreshRegionCache")
    @ApiOperation("手动刷新区域缓存")
    public String refreshRegionCache() {
        try {
            dimRegionCache.refreshCache();
            return "区域缓存刷新成功";
        } catch (Exception e) {
            return "区域缓存刷新失败: " + e.getMessage();
        }
    }
}
