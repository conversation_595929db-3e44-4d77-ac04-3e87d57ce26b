package com.iflytek.cdc.province.controller;

import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import com.github.pagehelper.PageHelper;
import com.iflytek.cdc.province.model.vo.PatientTransferInfoVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.dto.edr.CheckPermissionDTO;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessLogVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessSimpleInfoVO;
import com.iflytek.cdc.province.entity.ads.AdsMsEdrOperateRecord;
import com.iflytek.cdc.province.entity.bu.TbCdcewIllnessRecordBrowseLogs;
import com.iflytek.cdc.province.enums.MaintenanceOperateEnum;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.edr.dto.MaintenanceLogsQueryDTO;
import com.iflytek.cdc.province.model.edr.dto.RetrievalLogsQueryDTO;
import com.iflytek.cdc.province.model.edr.vo.MedicalPatientDetailInfo;
import com.iflytek.cdc.province.model.edr.vo.RecordMaintenanceStatVO;
import com.iflytek.cdc.province.model.edr.vo.RetrievalLogsStatVO;
import com.iflytek.cdc.province.model.vo.EdrIdentityVO;
import com.iflytek.cdc.province.service.EDRService;
import com.iflytek.cdc.province.service.EdrDataSearchService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@Api("edr - 个人疾病档案操作以及日志查看")
@RequestMapping("/pt/{version}/edr")
public class EDRController {

    @Resource
    private EDRService edrService;

    @Resource
    private EdrDataSearchService edrDataSearchService;

    @GetMapping("/getRecordLifeCycle")
    @ApiOperation("查询个人档案记录的生命周期时间轴")
    public List<MsProcessLogVO> getRecordLifeCycle(@RequestParam(required = false) String id){

        return edrService.getRecordLifeCycle(id);
    }

    @PostMapping("/editIllnessRecord")
    @ApiOperation("编辑疾病档案")
    public void editIllnessRecord(@RequestParam String loginUserId,
                                  @RequestBody CheckPermissionDTO dto){

        edrService.editIllnessRecord(loginUserId, dto);
    }

    @GetMapping("/insertRetrievalLog")
    @ApiOperation("插入档案调阅日志")
    public void insertRetrievalLog(@RequestParam String id,
                                   @RequestParam Integer status){

        edrService.insertRetrievalLog(id, status);
    }

    @GetMapping("/insertMaintenanceLog")
    @ApiOperation("插入档案更新日志")
    public void insertMaintenanceLog(@RequestParam String loginUserId,
                                     @RequestBody CheckPermissionDTO dto){

        dto.setOperatingType(MaintenanceOperateEnum.MODIFY.getCode());
        edrService.insertMaintenanceLog(loginUserId, dto);
    }

    @PostMapping("/retrievalLogsStat")
    @ApiOperation("调阅日志统计")
    public RetrievalLogsStatVO retrievalLogsStat(@RequestBody RetrievalLogsQueryDTO dto){

        return edrService.retrievalLogsStat(dto);
    }

    @GetMapping("/maintenanceLogsStat")
    @ApiOperation("维护更新记录统计")
    public RecordMaintenanceStatVO maintenanceLogsStat(MaintenanceLogsQueryDTO dto){

        return edrService.maintenanceLogsStat(dto);
    }

    @PostMapping("/getRetrievalLogs")
    @ApiOperation("查询调阅日志")
    public PageInfo<TbCdcewIllnessRecordBrowseLogs> getRetrievalLogs(@RequestBody RetrievalLogsQueryDTO dto){

        return edrService.getRetrievalLogs(dto);
    }

    @PostMapping("/exportRetrievalLogs")
    @ApiOperation("导出调阅日志")
    public TbCdcmrExportTask exportRetrievalLogs(@RequestParam String loginUserId,
                                                 @RequestBody RetrievalLogsQueryDTO dto){

        return edrService.exportRetrievalLogs(dto);
    }

    @PostMapping("/getMaintenanceLogs")
    @ApiOperation("查询维护更新日志")
    public PageInfo<AdsMsEdrOperateRecord> getMaintenanceLogs(@RequestBody MaintenanceLogsQueryDTO dto){

        return edrService.getMaintenanceLogs(dto);
    }

    @PostMapping("/exportMaintenanceLogs")
    @ApiOperation("导出维护更新日志")
    public TbCdcmrExportTask exportMaintenanceLogs(@RequestParam String loginUserId,
                                                   @RequestBody MaintenanceLogsQueryDTO dto){

        return edrService.exportMaintenanceLogs(dto);
    }
    @PostMapping("/medicalPatientPageList")
    @ApiOperation("edr 数据列表分页查询")
    public PageInfo<MsProcessSimpleInfoVO> medicalPatientPageList(@RequestParam String processType, @RequestBody MsProcessSimpleInfoQueryDTO queryDTO) {
        return edrDataSearchService.pageList(processType, queryDTO);
    }
    
    @PostMapping("/medicalPatientDetail")
    @ApiOperation("edr 患者详情查询")
    public MedicalPatientDetailInfo medicalPatientDetail(@RequestParam String patientId, @RequestBody List<String> eventIds) {
        return edrDataSearchService.loadByPatientId(patientId, eventIds);
    }

    @GetMapping("/findEdrIdentityByEmpiId")
    @ApiOperation("根据empiId查询个人档案标识信息")
    public EdrIdentityVO findEdrIdentityByEmpiId(@RequestParam String empiId, @RequestParam(required = false) String residentIdCard){
        return edrDataSearchService.findEdrIdentityByEmpiId(empiId, residentIdCard);
    }

    @PostMapping("/pageListTransfer")
    @ApiOperation("分院查询出入院信息")
    public PageInfo<PatientTransferInfoVO> pageListTransfer(@RequestBody  MsProcessSimpleInfoQueryDTO dto) {
        return edrDataSearchService.pageListTransfer(dto);
    }
}
