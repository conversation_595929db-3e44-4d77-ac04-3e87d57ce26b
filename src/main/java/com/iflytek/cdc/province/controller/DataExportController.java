package com.iflytek.cdc.province.controller;

import cn.hutool.core.util.StrUtil;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.service.ModelDataExportService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 模型数据导出
 */
@RestController
@Api(tags = "模型数据导出")
@Slf4j
public class DataExportController {

    @Resource
    private ModelDataExportService dataExportService;

    /**
     * 模型明细数据导出，并更新任务
     */
    @PostMapping("pt/{version}/dataExport/detail/task")
    @ApiOperation("模型明细数据导出")
    public TbCdcmrExportTask addAndRunDetailExportTask(@RequestBody ExportTaskDTO dto) {
        if (StrUtil.isBlank(dto.getTaskParam())) {
            throw new MedicalBusinessException("任务参数为空");
        }
        return dataExportService.addAndRunDetailExportTask(dto);
    }
}
