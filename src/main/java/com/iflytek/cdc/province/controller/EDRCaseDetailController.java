package com.iflytek.cdc.province.controller;

import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrDiseaseDetailVO;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrLisInfoVO;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrRisInfoVO;
import com.iflytek.cdc.province.model.vo.edrcase.AdsEdrPersonInfoVO;
import com.iflytek.cdc.province.entity.ads.AdsEdrVaccinateHistory;
import com.iflytek.cdc.province.model.vo.edrcase.*;
import com.iflytek.cdc.province.service.EDRCaseDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Api("edr - 个案详情查看")
@RequestMapping("/pt/{version}/edrCaseDetail")
public class EDRCaseDetailController {

    @Resource
    EDRCaseDetailService edrCaseDetailService;

    @GetMapping("/getAdsEdrProcessTagList")
    @ApiOperation("查询病种列表")
    public List<AdsEdrProcessTagVO> getAdsEdrProcessTagList(@RequestParam String empId) {
        return edrCaseDetailService.getAdsEdrProcessTagList(empId);
    }

    @GetMapping("/getAdsEdrPersonStatusInfoList")
    @ApiOperation("查询个人状态信息列表")
    public List<AdsEdrPersonStatusInfoVO> getAdsEdrPersonStatusInfoList(@RequestParam String empiId, String[] eventIds) {
        return edrCaseDetailService.getAdsEdrPersonStatusInfoList(empiId,eventIds);
    }

    @GetMapping("/getAdsEdrPersonInfo")
    @ApiOperation("查询个人主数据")
    public AdsEdrPersonInfoVO getAdsEdrPersonInfo(@RequestParam String empiId) {
        return edrCaseDetailService.getAdsEdrPersonInfo(empiId);
    }



    @GetMapping("/getRisByEventId")
    @ApiOperation("查询患者edr影像学检查信息")
    public List<AdsEdrRisInfoVO> getRisByEventId(@RequestParam("eventId") String eventId){
        return edrCaseDetailService.getRisByEventId(eventId);
    }

    @GetMapping("/getLisByEventId")
    @ApiOperation("查询患者edr实验室检测信息")
    public List<AdsEdrLisInfoVO> getLisByEventId(@RequestParam("eventId") String eventId){
        return edrCaseDetailService.getLisByEventId(eventId);
    }


    @GetMapping("/getAdsEdrDiseaseDetail")
    @ApiOperation("查询患者疾病详情信息")
    public AdsEdrDiseaseDetailVO getAdsEdrDiseaseDetail(String eventId){
        return edrCaseDetailService.getAdsEdrDiseaseDetail(eventId);
    }


    @GetMapping("/findReportByempiIdList")
    @ApiOperation("根据empiId查询传染病报卡信息")
    public List<AdsRepInfectReportInfoVO> findReportByempiIdList(String empiId){
        return edrCaseDetailService.findReportByempiIdList(empiId);
    }



    @GetMapping("/getAdsEdrRegisterManageInfoList")
    @ApiOperation("查询个人登记管理情况信息")
    public List<AdsEdrRegisterManageInfoVO> getAdsEdrRegisterManageInfoList(@RequestParam String empiId, String[] eventIds) {
        return edrCaseDetailService.getAdsEdrRegisterManageInfoList(empiId,eventIds);
    }

    @GetMapping("/getAdsEdrFollowInfoVOList")
    @ApiOperation("查询个人随访登记情况信息")
    public List<AdsEdrFollowInfoVO> getAdsEdrFollowInfoVOList(@RequestParam String empiId, String[] eventIds) {
        return edrCaseDetailService.getAdsEdrFollowInfoVOList(empiId,eventIds);
    }

    @GetMapping("/getAdsEdrDeadInfo")
    @ApiOperation("查询个人死亡情况信息")
    public AdsEdrDeadInfoVO getAdsEdrDeadInfo(@RequestParam String empiId, String[] eventIds) {
        return edrCaseDetailService.getAdsEdrDeadInfo(empiId,eventIds);
    }
    @GetMapping("/getAdsEdrVaccinateHistoryList")
    @ApiOperation("查询预防接种史信息")
    public List<AdsEdrVaccinateHistory> getAdsEdrVaccinateHistoryList(@RequestParam String empiId, String[] eventIds) {
        return edrCaseDetailService.getAdsEdrVaccinateHistoryList(empiId,eventIds);
    }

    @GetMapping("/getDwsProcessTagList")
    @ApiOperation("查询全生命周期 ")
    public List<DwsProcessTagVO> getDwsProcessTagList(@RequestParam String empiId) {
        return edrCaseDetailService.getDwsProcessTagList(empiId);
    }
}
