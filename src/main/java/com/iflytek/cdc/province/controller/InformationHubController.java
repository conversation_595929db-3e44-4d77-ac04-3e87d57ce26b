package com.iflytek.cdc.province.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.province.model.dto.dm.InformationHubQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.service.InformationHubService;
import com.iflytek.cdc.edr.vo.pandemic.MedicalInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Api(tags = "智能信息中枢")
public class InformationHubController {

    @Resource
    private InformationHubService informationHubService;

    /**
     * 查询原始门诊病历数据
     * */
    @PostMapping("pt/{version}/informationHub/getOutpatientMedicalInfo")
    @ApiOperation("查询原始门诊病历数据")
    @Deprecated
    public PageInfo<MedicalInfoVO> getOutpatientMedicalInfo(@RequestBody InformationHubQueryDTO dto) {

        return informationHubService.getOriginMedicalInfo(dto);
    }

    @PostMapping("pt/{version}/informationHub/getOutpatientMedicalInfoExport")
    @ApiOperation("查询原始门诊病历数据-导出")
    public TbCdcmrExportTask getOutpatientMedicalInfoExport(@RequestParam String loginUserId, @RequestBody InformationHubQueryDTO dto) {

        return informationHubService.getOutpatientMedicalInfoExport(loginUserId,dto);
    }

    /**
     * 查询原始住院病历数据
     * */
    @PostMapping("pt/{version}/informationHub/getInpatientMedicalInfo")
    @ApiOperation("查询原始住院病历数据")
    @Deprecated
    public PageInfo<MedicalInfoVO> getInpatientMedicalInfo(@RequestBody InformationHubQueryDTO dto) {

        return informationHubService.getInpatientMedicalInfo(dto);
    }

    @PostMapping("pt/{version}/informationHub/getInpatientMedicalInfoExport")
    @ApiOperation("查询原始住院病历数据-导出")
    public TbCdcmrExportTask getInpatientMedicalInfoExport(@RequestParam String loginUserId,@RequestBody InformationHubQueryDTO dto) {

        return informationHubService.getInpatientMedicalInfoExport(loginUserId,dto);
    }

}
