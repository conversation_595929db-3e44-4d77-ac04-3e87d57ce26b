package com.iflytek.cdc.province.controller;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.iflytek.cdc.province.model.pathogen.PathogenAnalysisReqDTO;
import com.iflytek.cdc.province.model.pathogen.PathogenMultiClassStatVO;
import com.iflytek.cdc.province.model.pathogen.PathogenNameGroupVO;
import com.iflytek.cdc.province.model.pathogen.PathogenTwofoldStatVO;
import com.iflytek.cdc.province.service.PathogenAnalysisService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 改造后的病原谱分析新接口
 *
 * @since 2025-04-07 症候群病原谱分析先使用这些新接口，其他木块的旧接口将陆续改造
 */
@RestController
@Api(tags = "病原谱分析")
public class PathogenAnalysisController {

    @Resource
    private PathogenAnalysisService pathogenAnalysisService;

    @PostMapping("/pt/{version}/pathogenAnalysis/positive/groupByName")
    @ApiOperation("病原谱分析-病原阳性结果-按病原体名称分组")
    public List<PathogenNameGroupVO> groupPositiveByName(@RequestBody PathogenAnalysisReqDTO req,
            @RequestParam String loginUserName) {
        return pathogenAnalysisService.groupPositiveByName(req, loginUserName);
    }

    @PostMapping("/pt/{version}/pathogenAnalysis/positive/top20ByRate")
    @ApiOperation("病原谱分析-病原阳性率TOP20-按病原体名称分组")
    public List<PathogenNameGroupVO> getTop20ByPositiveRate(@RequestBody PathogenAnalysisReqDTO req,
            @RequestParam String loginUserName) {
        List<PathogenNameGroupVO> results = pathogenAnalysisService.groupPositiveByName(req, loginUserName);
        return results.stream()
                .sorted((a, b) -> b.getProcessPositiveRate().compareTo(a.getProcessPositiveRate()))
                .limit(20)
                .collect(Collectors.toList());
    }

    @PostMapping("/pt/{version}/pathogenAnalysis/positive/top20ByYearGrowth")
    @ApiOperation("病原谱分析-病原阳性同比增长TOP20-按病原体名称分组")
    public List<PathogenNameGroupVO> getTop20ByPositiveYearGrowth(@RequestBody PathogenAnalysisReqDTO req,
            @RequestParam String loginUserName) {
        List<PathogenNameGroupVO> results = pathogenAnalysisService.groupPositiveByName(req, loginUserName);
        return results.stream()
                .sorted((a, b) -> b.getProcessPositiveYearGrowth().compareTo(a.getProcessPositiveYearGrowth()))
                .limit(20)
                .collect(Collectors.toList());
    }

    @PostMapping("/pt/{version}/pathogenAnalysis/twofold/groupByName")
    @ApiOperation("病原谱分析-病原复合感染-按病原体名称分组")
    public List<PathogenTwofoldStatVO> groupTwofoldByName(@RequestBody PathogenAnalysisReqDTO req,
            @RequestParam String loginUserName) {
        return pathogenAnalysisService.groupTwofoldByName(req, loginUserName);
    }

    @PostMapping("/pt/{version}/pathogenAnalysis/multiClass/groupByName")
    @ApiOperation("病原谱分析-病原多重感染情况-按病原体名称分组")
    public List<PathogenMultiClassStatVO> groupMultiClassByName(@RequestBody PathogenAnalysisReqDTO req,
            @RequestParam String loginUserName) {
        return pathogenAnalysisService.groupMultiClassByName(req, loginUserName);
    }

    @PostMapping("/pt/{version}/pathogenAnalysis/multiClass/groupByNameExport")
    @ApiOperation("病原谱分析-病原多重感染情况-按病原体名称分组-导出")
    public ResponseEntity<byte[]> groupMultiClassByNameExport(@RequestBody PathogenAnalysisReqDTO req,
            @RequestParam String loginUserName) {
        return pathogenAnalysisService.groupMultiClassByNameExport(req, loginUserName);
    }
}
