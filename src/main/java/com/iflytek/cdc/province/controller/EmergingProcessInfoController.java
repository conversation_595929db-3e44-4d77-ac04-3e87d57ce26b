package com.iflytek.cdc.province.controller;

import com.iflytek.cdc.province.model.dto.PathogenCombinationQueryDTO;
import com.iflytek.cdc.province.model.pathogen.PathogenInfectionSituationVO;
import com.iflytek.cdc.province.model.vo.PathogenCheckVO;
import com.iflytek.cdc.province.service.EmergingProcessInfoService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RequestMapping("/pt/{version}/emerging/processInfo")
@RestController
public class EmergingProcessInfoController {

    @Resource
    private EmergingProcessInfoService processInfoService;

    @PostMapping("/listEmergingPathogenCheckResult")
    @ApiOperation("查询新发突发病例病原检测结果")
    public List<PathogenCheckVO> listEmergingPathogenCheckResult(@RequestBody PathogenCombinationQueryDTO dto){

        return processInfoService.listEmergingPathogenCheckResult(dto);
    }

    @PostMapping("/listEmergingPositivePathogenCheckResult")
    @ApiOperation("查询新发突发病例阳性病原检测")
    public List<PathogenCheckVO> listEmergingPositivePathogenCheckResult(@RequestBody PathogenCombinationQueryDTO dto){

        return processInfoService.listEmergingPositivePathogenCheckResult(dto);
    }

    @PostMapping("/listEmergingPathogenInfectionSituation")
    @ApiOperation("查询新发突发病例病原感染情况")
    public List<PathogenInfectionSituationVO> listEmergingPathogenInfectionSituation(@RequestBody PathogenCombinationQueryDTO dto){

        return processInfoService.listEmergingPathogenInfectionSituation(dto);
    }

}
