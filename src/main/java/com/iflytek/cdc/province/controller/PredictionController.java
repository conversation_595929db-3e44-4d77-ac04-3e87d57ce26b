package com.iflytek.cdc.province.controller;

import com.iflytek.cdc.province.service.PredictionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/prediction")
public class PredictionController {

    @Autowired
    private PredictionService predictionService;

    @PostMapping("/predict")
    public ResponseEntity<List<Double>> predict(
            @RequestParam List<Double> inputData,
            @RequestParam Integer predictionDays) {
        List<Double> predictions = predictionService.predict(inputData, predictionDays);
        return ResponseEntity.ok(predictions);
    }
} 