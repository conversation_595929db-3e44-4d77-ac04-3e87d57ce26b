package com.iflytek.cdc.province.controller;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.enums.DateDimEnum;
import com.iflytek.cdc.province.model.dto.PatientQueryDTO;
import com.iflytek.cdc.province.model.dto.dm.KeyWordSearchDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.vo.PatientListVO;
import com.iflytek.cdc.province.model.vo.PatientMedicalInfoVO;
import com.iflytek.cdc.province.service.PatientMasterDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 患者主数据
 * */
@RestController
@Api(tags = "患者数据池")
@RequestMapping("/pt/{version}/patientMasterData")
public class PatientMasterDataController {

    @Resource
    private PatientMasterDataService patientMasterDataService;

    @PostMapping("/getPatientList")
    @ApiOperation("查询患者列表")
    public PageInfo<PatientListVO> getPatientList(@RequestBody PatientQueryDTO dto){

        return patientMasterDataService.getPatientList(dto);
    }

    @PostMapping("/getPatientMedicalList")
    @ApiOperation("查询患者原始病历信息")
    public List<PatientMedicalInfoVO> getPatientMedicalList(@RequestParam String patientId){

        return patientMasterDataService.getPatientMedicalList(patientId);
    }

    @PostMapping("/patientListExport")
    @ApiOperation("患者列表-导出")
    public TbCdcmrExportTask patientListExport(@RequestBody PatientQueryDTO dto) {

        return patientMasterDataService.patientListExport(dto);
    }
}
