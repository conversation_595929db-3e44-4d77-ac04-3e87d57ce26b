package com.iflytek.cdc.province.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.common.MsConstants;
import com.iflytek.cdc.edr.dto.DimInfectedInfoQuery;
import com.iflytek.cdc.province.entity.ads.AdsMsMedicalInfo;
import com.iflytek.cdc.province.model.brief.ManualBriefRecordVO;
import com.iflytek.cdc.province.model.dto.AdsMsProcessReqDTO;
import com.iflytek.cdc.province.model.dto.MsMedicalQuery;
import com.iflytek.cdc.province.model.dto.MsProcessSimpleInfoConditionQueryDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.model.vo.SyndromeOrgProcessStatsVO;
import com.iflytek.cdc.province.model.vo.VisitPersonInfoVO;
import com.iflytek.cdc.province.service.AdsMsProcessStatService;
import com.iflytek.cdc.province.service.AdsMsMedicalService;
import com.iflytek.cdc.province.service.DimInfectedInfoService;
import com.iflytek.cdc.edr.utils.FileUtils;
import com.iflytek.cdc.edr.vo.ConstantVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 监测病例量统计
 */
@RestController
@Api(tags = "监测病例量统计，疫情概况统计")
public class MsMedicalStatsController {
    @Resource
    private AdsMsMedicalService adsMsMedicalService;

    @Resource
    private AdsMsProcessStatService adsMsProcessStatService;

    @Resource
    private DimInfectedInfoService dimInfectedInfoService;
    
    @PostMapping("/pt/{version}/ms/medical/listMsMedicalInfo")
    @ApiOperation("病历信息查询")
    public List<AdsMsMedicalInfo> listMsMedicalInfo(@RequestBody MsMedicalQuery msMedicalQuery){
        return adsMsMedicalService.listMsMedicalInfo(msMedicalQuery);
    }

    @PostMapping("/pt/{version}/ms/medical/visitStat")
    @ApiOperation("就诊人次概况")
    public MsMedicalVisitStatVO visitStat(@RequestBody MsMedicalQuery msMedicalQuery , @RequestParam String loginUserName){
        return adsMsMedicalService.medicalVisitStat(msMedicalQuery, loginUserName);
    }

    @PostMapping("/pt/{version}/ms/medical/visitStatLineChart")
    @ApiOperation("就诊人次概况-折线图")
    public List<MsMedicalVisitStatVO> visitStatLineChart(@RequestBody MsMedicalQuery msMedicalQuery, @RequestParam String loginUserName){
        return adsMsMedicalService.visitStatLineChart(msMedicalQuery, loginUserName);
    }

    @PostMapping("/pt/{version}/ms/medical/processStat")
    @ApiOperation("监测病例量总览")
    public AdsMsProcessRespVO infectProcessStat(@RequestBody AdsMsProcessReqDTO queryParam,
                                                @RequestParam String loginUserName){
        return adsMsProcessStatService.processStat(queryParam, loginUserName);
    }

    @PostMapping({"/pt/{version}/ms/medical/processStatLineChart", "/pt/{version}/ms/medical/processStat/groupTime"})
    @ApiOperation("法定传染病-疫情同比增长TOP10-时间分布（病例总览折线图数据）")
    public List<AdsMsProcessRespVO> processStatLineChart(@RequestBody AdsMsProcessReqDTO queryParam,
                                                         @RequestParam String loginUserName){
        return adsMsProcessStatService.processStatLineChart(queryParam, loginUserName);
    }

    @PostMapping("/pt/{version}/ms/medical/processStat/groupArea")
    @ApiOperation("法定传染病-疫情同比增长TOP10-地区分布")
    public List<AdsMsProcessRespVO> processStatGroupArea(@RequestBody AdsMsProcessReqDTO queryParam,
                                                         @RequestParam String loginUserId,
                                                         @RequestParam String loginUserName){
        // 复用疾病专题分析的查询
        return adsMsProcessStatService.groupArea(queryParam, loginUserId, loginUserName);
    }
    @PostMapping("/pt/{version}/ms/medical/processStat/groupAge")
    @ApiOperation("法定传染病-疫情同比增长TOP10-人群分布-患者年龄分布")
    public List<AdsMsProcessRespVO> processStatGroupAge(@RequestBody AdsMsProcessReqDTO queryParam,
                                                        @RequestParam String loginUserId,
                                                        @RequestParam String loginUserName){
        // 复用疾病专题分析的查询
        return adsMsProcessStatService.groupAge(queryParam, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/ms/medical/processStat/groupSex")
    @ApiOperation("法定传染病-疫情同比增长TOP10-人群分布-患者性别分布")
    public List<AdsMsProcessRespVO> processStatGroupSex(@RequestBody AdsMsProcessReqDTO queryParam,
                                                        @RequestParam String loginUserId,
                                                        @RequestParam String loginUserName){
        // 复用疾病专题分析的查询
        return adsMsProcessStatService.groupSex(queryParam, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/ms/medical/processStat/groupJob")
    @ApiOperation("法定传染病-疫情同比增长TOP10-人群分布-患者职业分布")
    public List<AdsMsProcessRespVO> processStatGroupJob(@RequestBody AdsMsProcessReqDTO queryParam,
                                                        @RequestParam String loginUserId,
                                                        @RequestParam String loginUserName){
        // 复用疾病专题分析的查询
        return adsMsProcessStatService.groupJob(queryParam, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/ms/medical/infect/notifiableInfectTypeStat")
    @ApiOperation("法定传染病-疫情概况总览")
    public MsInfectTypeStatVO notifiableInfectTypeStat(@RequestBody AdsMsProcessReqDTO queryParam,
                                                             @RequestParam String loginUserName){
        return adsMsProcessStatService.notifiableInfectTypeStat(queryParam, loginUserName);
    }

    @PostMapping("/pt/{version}/ms/medical/infect/groupInfectNewCntTop10")
    @ApiOperation("传染病疾病发病数top10统计")
    public List<MsInfectNewCntVO> groupInfectNewCntTop10(@RequestBody AdsMsProcessReqDTO queryParam,
                                                         @RequestParam String loginUserName){
        return adsMsProcessStatService.groupInfectNewCntTop10(queryParam, loginUserName);
    }

    @PostMapping("/pt/{version}/ms/medical/infect/groupInfectNewCntDeclineTop10")
    @ApiOperation("传染病疾病发病数下降top10统计")
    public List<MsInfectNewCntVO> groupInfectNewCntDeclineTop10(@RequestBody AdsMsProcessReqDTO queryParam,
                                                                @RequestParam String loginUserName) {
        return adsMsProcessStatService.groupInfectNewCntDeclineTop10(queryParam, loginUserName);
    }


    @PostMapping("/pt/{version}/ms/medical/infect/groupInfectCnt")
    @ApiOperation("疫情概况明细")
    public List<MsInfectCntVO> groupInfectCnt(@RequestBody AdsMsProcessReqDTO queryParam,
                                              @RequestParam String loginUserName){
        return adsMsProcessStatService.groupInfectCnt(queryParam, loginUserName);
    }
    @PostMapping("/pt/{version}/ms/medical/infect/groupInfectCntPage")
    @ApiOperation("疫情概况明细-分页查询")
    public PageInfo<MsInfectCntVO> groupInfectCntPage(@RequestBody AdsMsProcessReqDTO queryParam,
                                                      @RequestParam String loginUserName){
        return adsMsProcessStatService.groupInfectCntPage(queryParam, loginUserName);
    }

    @PostMapping("/pt/{version}/ms/medical/infect/groupInfectCntExport")
    @ApiOperation("疫情概况明细导出-法定传染病")
    public ResponseEntity<byte[]> groupInfectCntExport(@RequestBody AdsMsProcessReqDTO queryParam,
                                                       @RequestParam String loginUserName){
        ManualBriefRecordVO vo = adsMsProcessStatService.groupInfectCntExport(queryParam, loginUserName);
        HttpHeaders httpHeaders = FileUtils.getHttpHeaders(vo.getTitle() + ".docx");
        return new ResponseEntity<>(vo.getFileBytes(), httpHeaders, HttpStatus.CREATED);
    }

    @PostMapping("/pt/{version}/ms/medical/infect/groupOtherInfectCntExport")
    @ApiOperation("疫情概况明细导出-其它传染病")
    public ResponseEntity<byte[]> groupOtherInfectCntExport(@RequestBody AdsMsProcessReqDTO queryParam,
                                                            @RequestParam String loginUserName){
        ManualBriefRecordVO vo = adsMsProcessStatService.groupOtherInfectCntExport(queryParam, loginUserName);
        HttpHeaders httpHeaders = FileUtils.getHttpHeaders(vo.getTitle() + ".docx");
        return new ResponseEntity<>(vo.getFileBytes(), httpHeaders, HttpStatus.CREATED);
    }

    @PostMapping("/pt/{version}/ms/medical/infect/groupInfectTransmitTypeCnt")
    @ApiOperation("传播途径")
    public List<MsInfectTransmitTypeCntVO>  groupInfectTransmitTypeCnt(@RequestBody AdsMsProcessReqDTO queryParam,
                                                                       @RequestParam String loginUserName){
        return adsMsProcessStatService.groupInfectTransmitTypeCnt(queryParam, loginUserName);
    }

    @PostMapping("/pt/{version}/ms/medical/infect/groupInfectTransmitTypeCntExport")
    @ApiOperation("传播途径-导出")
    public ResponseEntity<byte[]> groupInfectTransmitTypeCntExport(@RequestBody AdsMsProcessReqDTO queryParam,
                                                                   @RequestParam String loginUserName){
        HttpHeaders httpHeaders = FileUtils.getHttpHeaders("疫情概况明细导出.xlsx");
        return new ResponseEntity<>(adsMsProcessStatService.groupInfectTransmitTypeCntExport(queryParam, loginUserName),
                httpHeaders, HttpStatus.CREATED);
    }

    @PostMapping("/pt/{version}/ms/medical/infect/listNewCntPre")
    @ApiOperation("获取预测数据")
    public List<AdsMsProcessRespVO>  listNewCntPre(@RequestBody AdsMsProcessReqDTO queryParam,
                                                @RequestParam String loginUserName){
        return adsMsProcessStatService.listNewCntPre(queryParam, loginUserName);
    }

    @PostMapping("/pt/{version}/ms/medical/infect/getConstants")
    @ApiOperation("常量获取")
    public Map<String, Object> getConstants(){
        Map<String, Object> constants = new HashMap<>();
        constants.put("addressType", MsConstants.AddressTypeEnum.values());
        constants.put("timeType", MsConstants.TimeTypeEnum.values());
        constants.put("infectClass", MsConstants.InfectClassEnum.values());
        constants.put("infectType", MsConstants.InfectTypeEnum.values());
        constants.put("infectTransmitType", MsConstants.InfectTransmitTypeEnum.values());
        constants.put("orgClass", MsConstants.OrgClassEnum.values());
        return constants;
    }

    @PostMapping("/pt/{version}/ms/medical/infect/listInfectInfo")
    @ApiOperation("获取疾病信息")
    public List<ConstantVO> listInfectInfo(@RequestBody DimInfectedInfoQuery query){
        return dimInfectedInfoService.listInfectedInfoBy(query);
    }

    @PostMapping("/pt/{version}/ms/medical/visitPersonDetail")
    @ApiOperation("就诊详情")
    public PageInfo<VisitPersonInfoVO> visitPersonDetail(@RequestBody MsMedicalQuery msMedicalQuery){

        return adsMsMedicalService.visitPersonDetail(msMedicalQuery);
    }

    @PostMapping("/pt/{version}/ms/medical/visitPersonDetailExport")
    @ApiOperation("就诊详情导出")
    public TbCdcmrExportTask visitPersonDetailExport(@RequestBody MsMedicalQuery msMedicalQuery){

        return adsMsMedicalService.visitPersonDetailExport(msMedicalQuery);
    }

    @PostMapping("/pt/{version}/ms/medical/infect/groupInfectSexCnt")
    @ApiOperation("性别 - 大模型演示加上的接口")
    public List<MsInfectCntVO> groupInfectSexCnt(@RequestBody AdsMsProcessReqDTO queryParam,
                                                 @RequestParam String loginUserName){

        return adsMsProcessStatService.groupInfectSexCnt(queryParam, loginUserName);
    }

    @PostMapping("/pt/{version}/ms/medical/infect/groupInfectAgeCnt")
    @ApiOperation("年龄 - 大模型演示加上的接口")
    public List<MsInfectCntVO> groupInfectAgeCnt(@RequestBody AdsMsProcessReqDTO queryParam,
                                                 @RequestParam String loginUserName){

        return adsMsProcessStatService.groupInfectAgeCnt(queryParam, loginUserName);
    }

    @PostMapping("/pt/{version}/ms/medical/listSyndromeOrgProcessStats")
    @ApiOperation("症候群机构病程统计")
    public PageInfo<SyndromeOrgProcessStatsVO> listSyndromeOrgProcessStats(@RequestBody  AdsMsProcessReqDTO queryDTO, @RequestParam String loginUserId) {
        return adsMsProcessStatService.listSyndromeOrgProcessStats(queryDTO);
    }


    @PostMapping("/pt/{version}/ms/medical/infect/groupInfectDiseaseCnt")
    @ApiOperation("按病种统计")
    public List<MsInfectCntVO> groupInfectDiseaseCnt(@RequestBody AdsMsProcessReqDTO queryParam,
                                                     @RequestParam String loginUserName) {

        return adsMsProcessStatService.groupInfectDiseaseCnt(queryParam, loginUserName);
    }


    @PostMapping("/pt/{version}/ms/medical/infect/groupInfectAreaCnt")
    @ApiOperation("按地区医疗机构统计")
    public List<AdsMsProcessRespVO> groupInfectAreaCnt(@RequestBody AdsMsProcessReqDTO queryParam,
                                                  @RequestParam String loginUserName) {

        return adsMsProcessStatService.groupInfectAreaCnt(queryParam, loginUserName);
    }


    @PostMapping("/pt/{version}/ms/medical/infect/groupInfectAreaCntExport")
    @ApiOperation("按地区医疗机构统计-导出")
    public ResponseEntity<byte[]> groupInfectAreaCntExport(@RequestBody AdsMsProcessReqDTO queryParam) {
        HttpHeaders httpHeaders = FileUtils.getHttpHeaders("按地区医疗机构统计导出.xlsx");
        return new ResponseEntity<>(adsMsProcessStatService.groupInfectAreaCntExport(queryParam), httpHeaders, HttpStatus.CREATED);
    }


    @PostMapping("/pt/{version}/ms/medical/infect/groupInfectAreaOrgCnt")
    @ApiOperation("按地区医疗机构统计-下钻医疗机构")
    public List<AdsMsProcessRespVO> groupInfectAreaOrgCnt(@RequestBody AdsMsProcessReqDTO queryParam,
                                                       @RequestParam String loginUserName) {

        return adsMsProcessStatService.groupInfectAreaOrgCnt(queryParam, loginUserName);
    }

    @PostMapping("/pt/{version}/ms/medical/infect/groupInfectAreaOrgCntExport")
    @ApiOperation("按地区医疗机构统计-下钻医疗机构-导出")
    public ResponseEntity<byte[]> groupInfectAreaOrgCntExport(@RequestBody AdsMsProcessReqDTO queryParam) {
        HttpHeaders httpHeaders = FileUtils.getHttpHeaders("按地区医疗机构统计导出.xlsx");
        return new ResponseEntity<>(adsMsProcessStatService.groupInfectAreaOrgCntExport(queryParam), httpHeaders, HttpStatus.CREATED);
    }


}
