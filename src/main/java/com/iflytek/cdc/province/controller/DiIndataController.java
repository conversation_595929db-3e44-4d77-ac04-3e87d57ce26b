package com.iflytek.cdc.province.controller;

import com.iflytek.cdc.province.model.dto.DiIndataQueryParam;
import com.iflytek.cdc.province.service.AdsDiIndataStatService;
import com.iflytek.cdc.edr.vo.DiIndataStatVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = "数据资产总览")
@RestController
public class DiIndataController {
    @Resource
    private AdsDiIndataStatService adsDiIndataStatService;

    @PostMapping("/pt/{version}/di/indata/getLastCount")
    @ApiOperation("获取最终统计的数据")
    public DiIndataStatVO getLastCount(@RequestBody DiIndataQueryParam queryParam){
        return adsDiIndataStatService.getLastCount(queryParam);
    }

    @PostMapping("/pt/{version}/di/indata/statLineChart")
    @ApiOperation("折线图统计")
    public List<DiIndataStatVO> statLineChart(@RequestBody DiIndataQueryParam queryParam){

        return adsDiIndataStatService.statLineChart(queryParam);
    }
}
