package com.iflytek.cdc.province.interceptor;

import com.iflytek.cdc.edr.apiService.UapServiceApi;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

/**
 * 登录人员信息拦截器
 * date : 2024-05-14
 * */
@Slf4j
@Component
public class UserInfoInterceptor implements HandlerInterceptor {

    @Resource
    private UapServiceApi uapServiceApi;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {

        String requestUri = request.getRequestURI();
        if (requestUri.contains("swagger") || StringUtils.isBlank(request.getParameter("loginUserId"))) {
//            UapUserPo uapUserPo = new UapUserPo();
//            uapUserPo.setId("1269507793125376");
//            uapUserPo.setId("149516027846197367");
//            userInfo.set(uapUserPo);
            userInfo.set(new UapUserPo("未获取到登录人信息"));
        }else {
            String loginUserId = request.getParameter("loginUserId");
            userInfo.set(uapServiceApi.getUser(loginUserId));
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        userInfo.remove();
    }
}
