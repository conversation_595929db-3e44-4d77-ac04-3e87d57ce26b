package com.iflytek.cdc;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import com.iflytek.cdc.edr.configuration.EsModelConfigProperties;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import com.iflytek.cdc.edr.vo.DesensitizedVO;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * CDC-EDR查询服务
 *
 * <AUTHOR>
 * @date 2021/12/2 15:42
 */

@SpringBootApplication(exclude = {
        RedisRepositoriesAutoConfiguration.class
})
@EnableSwagger2
@EnableKnife4j
@EnableConfigurationProperties(EsModelConfigProperties.class)
@EnableScheduling
@MapperScan("com.iflytek.cdc.**.mapper")
public class CdcDataServiceApplication {
    //线程共享数据
//    public static final ThreadLocal<Boolean> threadLocal = new ThreadLocal<>();
    public static final ThreadLocal<DesensitizedVO> threadLocal = new ThreadLocal<>();

    //后续可扩展用户的其他信息
    public static final ThreadLocal<UapUserPo> userInfo = new ThreadLocal<>();

    public static void main(String[] args) {
        SpringApplication.run(CdcDataServiceApplication.class, args);
    }
}
