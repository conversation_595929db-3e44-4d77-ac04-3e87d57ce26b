package com.iflytek.cdc.reportcard.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.apiService.CdcPlatformApi;
import com.iflytek.cdc.edr.apiService.UapServiceApi;
import com.iflytek.cdc.edr.dto.AwarenessWithCalibrateDTO;
import com.iflytek.cdc.edr.enums.AreaLevelEnum;
import com.iflytek.cdc.edr.enums.DateDimEnum;
import com.iflytek.cdc.edr.utils.DataUtils;
import com.iflytek.cdc.edr.utils.DateFormatUtil;
import com.iflytek.cdc.edr.utils.DimDateUtils;
import com.iflytek.cdc.edr.vo.AwarenessResultVO;
import com.iflytek.cdc.edr.vo.PopulationDataInfoVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.EpidemicDistributionRespVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsProcessStatVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.ProcessAwarenessRespVO;
import com.iflytek.cdc.province.model.dto.AdsMsProcessReqDTO;
import com.iflytek.cdc.province.model.dto.DateDim;
import com.iflytek.cdc.province.model.vo.TreeNode;
import com.iflytek.cdc.province.service.CommonUtilService;
import com.iflytek.cdc.province.service.PredictionService;
import com.iflytek.cdc.province.utils.StatUtils;
import com.iflytek.cdc.reportcard.mapper.bu.ReportMsProcessStatMapper;
import com.iflytek.cdc.reportcard.mapper.pg.AdsReportMsProcessStatMapper;
import com.iflytek.cdc.reportcard.service.ReportMsProcessStatService;
import com.iflytek.cdc.reportcard.vo.*;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

/**
 * 报卡督导疾病专题分析服务实现类
 * 参考 AdsMsProcessStatServiceImpl 的实现方式，使用 ReportMsProcessStatMapper 进行数据库查询
 */
@RefreshScope
@Slf4j
@Service
public class ReportMsProcessStatServiceImpl implements ReportMsProcessStatService {

    @Resource
    private DataUtils dataUtils;

    @Resource
    private DimDateUtils dimDateUtils;

    @Resource
    private ReportMsProcessStatMapper reportMsProcessStatMapper;

    @Resource
    private AdsReportMsProcessStatMapper adsReportMsProcessStatMapper;

    @Resource
    private CdcAdminServiceApi cdcAdminServiceApi;

    @Resource
    private UapServiceApi uapServiceApi;

    @Resource
    private CdcPlatformApi cdcPlatformApi;

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Resource
    private PredictionService predictionService;


    @Value("${report.record.type:app}")
    private String reportRecordType;

    /**
     * 计算百分比
     */
    private static void setRatio(List<AdsMsProcessRespVO> vos) {
        final int total = vos.stream().mapToInt(MsProcessStatVO::getProcessNewCnt).filter(Objects::nonNull).sum();
        for (AdsMsProcessRespVO vo : vos) {
            vo.calculateAllRates(total);
        }
    }

    /**
     * 设置区域信息多选
     */
    private void setAreaInfoList(AdsMsProcessReqDTO queryDTO, String loginUserName) {
        uapServiceApi.setProvinceAndCityAndDistrict(loginUserName, queryDTO,
                AdsMsProcessReqDTO::setProvinceCodes,
                AdsMsProcessReqDTO::setCityCodes,
                AdsMsProcessReqDTO::setDistrictCodes,
                (d, areaLevel) -> {
                    if (d.getAreaLevel() == null) {
                        d.setAreaLevel(areaLevel);
                    }
                });
        // 融合权限和过滤条件
        if (CollectionUtil.isNotEmpty(queryDTO.getDistrictCodeList())) {
            if (CollUtil.isEmpty(queryDTO.getDistrictCodes())) {
                queryDTO.setDistrictCodes(queryDTO.getDistrictCodeList());
            } else {
                queryDTO.setDistrictCodes(CommonUtilService.intersection(queryDTO.getDistrictCodeList(), queryDTO.getDistrictCodes()));
            }
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getCityCodeList())) {
            if (CollUtil.isEmpty(queryDTO.getCityCodes())) {
                queryDTO.setCityCodes(queryDTO.getCityCodeList());
            } else {
                queryDTO.setCityCodes(CommonUtilService.intersection(queryDTO.getCityCodeList(), queryDTO.getCityCodes()));
            }
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getStreetCodeList())) {
            if (CollUtil.isEmpty(queryDTO.getStreetCodes())) {
                queryDTO.setStreetCodes(queryDTO.getStreetCodeList());
            } else {
                queryDTO.setStreetCodes(CommonUtilService.intersection(queryDTO.getStreetCodeList(), queryDTO.getStreetCodes()));
            }
        }
        else{
            queryDTO.setStreetCodes(new ArrayList<>());
        }
        // 针对传染病 查询亚型
        // 先判断时候传入了传染病
        String infectCode = queryDTO.getInfectCode();
        if (StrUtil.isNotBlank(infectCode)){
            queryDTO.setInfectCodeSubtypeList(queryInfectCodeList(infectCode));
        } else {
            queryDTO.setInfectCodeSubtypeList(new ArrayList<>());
        }

    }

    private List<String> queryInfectCodeList(String infectCode){
        List<TreeNode> diseaseCodeTree = adminServiceApi.getInfectedInfo(null);

        List<String> diseaseCodeList = new ArrayList<>();
        if(StrUtil.isNotBlank(infectCode)) {
            for (TreeNode node : diseaseCodeTree) {
                TreeNode.getAllNodeBy(diseaseCodeList, node, infectCode, TreeNode::getValue, TreeNode::getValue);
            }
        }
        if (diseaseCodeList.isEmpty()){
            diseaseCodeList.add(infectCode);
        }
        return diseaseCodeList;
    }

    /**
     * 设置人口数
     */
    private void setPopulation(AdsMsProcessReqDTO reqDTO, List<AdsMsProcessRespVO> respVOS) {
        if (StrUtil.isNotBlank(reqDTO.getStreetCode())) {
            // 街道无人口数据
            return;
        }
        final List<String> provinceCodes = reqDTO.getProvinceCodes();
        final List<String> cityCodes = reqDTO.getCityCodes();
        final List<String> districtCodes = reqDTO.getDistrictCodes();
        final List<PopulationDataInfoVO> areaPopulations = cdcAdminServiceApi.listByAreaCodes(reqDTO.getEndDate(), provinceCodes, cityCodes, districtCodes);
        final int sum = areaPopulations.stream().mapToInt(PopulationDataInfoVO::getResidentPopulation).sum();
        for (AdsMsProcessRespVO respVO : respVOS) {
            respVO.setPopulation(sum);
        }
    }

    @Override
    public List<AdsMsProcessRespVO> monthTimeTrend(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        // 此处需求变更，起始月的时间会被过滤掉，现在的需求是要包含起始月
        reqDTO.setStartDate(DateUtils.addMonths(reqDTO.getStartDate(), -1));
        reqDTO.setDateDims(dimDateUtils.getDateDims(DateDimEnum.MONTH.getCode(), reqDTO.getStartDate(), reqDTO.getEndDate()));

        List<AdsMsProcessRespVO> monthDescVO = getDataByType(reportRecordType,
                () -> reportMsProcessStatMapper.findMonthDistribution(reqDTO),
                () -> buildStat(reqDTO, adsReportMsProcessStatMapper::findMonthDistribution,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription));
        setPopulation(reqDTO, monthDescVO);
        setRatio(monthDescVO);
        return monthDescVO;
    }

    private List<AdsMsProcessRespVO> buildStat(AdsMsProcessReqDTO reqDTO,
                                               Function<AdsMsProcessReqDTO, List<AdsMsProcessRespVO>> queryFunction,
                                               Function<AdsMsProcessRespVO, String> descriptionGetter,
                                               BiConsumer<AdsMsProcessRespVO, String> descriptionSetter) {

        return StatUtils.build(reqDTO,
                               AdsMsProcessRespVO::new,
                               descriptionGetter,
                               descriptionSetter,
                               queryFunction,
                               AdsMsProcessRespVO.buildCommonOperations(),
                               AdsMsProcessRespVO.buildOtherProperties());
    }

    @Override
    public ResponseEntity<byte[]> monthTimeTrendExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.monthTimeTrend(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByReportCard( vos, ReportCardInfectMonthExcelVO.class);
    }

    @Override
    public List<AdsMsProcessRespVO> quarterTimeTrend(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        // 此处需求变更，起始月的时间会被过滤掉，现在的需求是要包含起始月
        reqDTO.setStartDate(DateUtils.addMonths(reqDTO.getStartDate(), -3));
        reqDTO.setDateDims(dimDateUtils.getDateDims(DateDimEnum.QUARTER.getCode(), reqDTO.getStartDate(), reqDTO.getEndDate()));
        List<AdsMsProcessRespVO> quarterDescVO = getDataByType(reportRecordType,
                () -> reportMsProcessStatMapper.findQuarterDistribution(reqDTO),
                () -> buildStat(reqDTO, adsReportMsProcessStatMapper::findQuarterDistribution,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription));
        setPopulation(reqDTO, quarterDescVO);
        setRatio(quarterDescVO);
        return quarterDescVO;
    }

    @Override
    public ResponseEntity<byte[]> quarterTimeTrendExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.quarterTimeTrend(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByReportCard(vos, ReportCardInfectQuarterExcelVO.class);
    }

    @Override
    public List<AdsMsProcessRespVO> yearTimeTrend(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        reqDTO.setDateDims(dimDateUtils.getDateDims(DateDimEnum.YEAR.getCode(), reqDTO.getStartDate(), reqDTO.getEndDate()));
        List<AdsMsProcessRespVO> yearDescVO = getDataByType(reportRecordType,
                () -> reportMsProcessStatMapper.findYearDistribution(reqDTO),
                () -> buildStat(reqDTO, adsReportMsProcessStatMapper::findYearDistribution,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription));
        setPopulation(reqDTO, yearDescVO);
        setRatio(yearDescVO);
        return yearDescVO;
    }

    @Override
    public ResponseEntity<byte[]> yearTimeTrendExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.yearTimeTrend(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByReportCard(vos, ReportCardInfectYearExcelVO.class);
    }

    @Override
    public List<AdsMsProcessRespVO> halfYearTimeTrend(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        resetDateRange(reqDTO);
        reqDTO.setStartDate(org.apache.commons.lang.time.DateUtils.addMonths(reqDTO.getStartDate(), -1));
        reqDTO.setDateDims(dimDateUtils.getDateDims(DateDimEnum.HALF_YEAR.getCode(), reqDTO.getStartDate(), reqDTO.getEndDate()));
        List<AdsMsProcessRespVO> monthDescVOList = getDataByType(reportRecordType,
                () -> reportMsProcessStatMapper.findMonthDistribution(reqDTO),
                () -> buildStat(reqDTO, adsReportMsProcessStatMapper::findHalfYearDistribution,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription));
        setPopulation(reqDTO, monthDescVOList);
        setRatio(monthDescVOList);
        return monthDescVOList;
    }

    @Override
    public ResponseEntity<byte[]> halfYearTimeTrendExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.halfYearTimeTrend(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByReportCard(vos, ReportCardInfectHalfYearExcelVO.class);
    }

    @Override
    public List<AdsMsProcessRespVO> groupArea(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> respVOS = getDataByType(reportRecordType
                , () -> StatUtils.fulfillPeriodValue(reqDTO, reportMsProcessStatMapper::groupArea,
                        (last, curr) -> curr.getAreaCode().equals(last.getAreaCode()), AdsMsProcessRespVO.currValueAndLastValueMappingList())
                , () -> buildStat(reqDTO, adsReportMsProcessStatMapper::groupArea,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription)
        );

        setPopulation(reqDTO, respVOS);
        setRatio(respVOS);
        return respVOS;
    }

    @Override
    public List<AdsMsProcessRespVO> groupAreaDetail(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> respVOS = new ArrayList<>();
        List<AdsMsProcessRespVO> currentRespVOS = getDataByType(reportRecordType
                , () -> StatUtils.fulfillPeriodValue(reqDTO, reportMsProcessStatMapper::groupAreaCurrentLevel,
                        (last, curr) -> curr.getAreaCode().equals(last.getAreaCode()), AdsMsProcessRespVO.currValueAndLastValueMappingList()),
                  () -> buildStat(reqDTO, adsReportMsProcessStatMapper::groupAreaCurrentLevel, AdsMsProcessRespVO::getDescription, AdsMsProcessRespVO::setDescription, AdsMsProcessRespVO.buildOtherProperties())
                );

        List<AdsMsProcessRespVO> subRespVOS = getDataByType(reportRecordType
        , () -> StatUtils.fulfillPeriodValue(reqDTO,reportMsProcessStatMapper::groupArea, (last, curr) -> curr.getAreaCode().equals(last.getAreaCode()), AdsMsProcessRespVO.currValueAndLastValueMappingList())
        , () -> buildStat(reqDTO, adsReportMsProcessStatMapper::groupArea, AdsMsProcessRespVO::getDescription, AdsMsProcessRespVO::setDescription, AdsMsProcessRespVO.buildOtherProperties()));

        respVOS.addAll(currentRespVOS);
        respVOS.addAll(subRespVOS);

        List<String> provinceCodes = reqDTO.getProvinceCodes();
        List<String> cityCodes = new ArrayList<>(reqDTO.getCityCodes());
        List<String> districtCodes = new ArrayList<>(reqDTO.getDistrictCodes());
        List<String> streetCodes = new ArrayList<>(reqDTO.getStreetCodes());

        List<String> dCodes = respVOS.stream().filter(v -> AreaLevelEnum.DISTRICT.getValue().equals(v.getAreaLevel())).map(AdsMsProcessRespVO::getAreaCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(dCodes)) {
            districtCodes.addAll(dCodes);
        }
        List<String> cCodes = respVOS.stream().filter(v -> AreaLevelEnum.CITY.getValue().equals(v.getAreaLevel())).map(AdsMsProcessRespVO::getAreaCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(cCodes)) {
            cityCodes.addAll(cCodes);
        }

        List<String> sCodes = respVOS.stream().filter(v -> AreaLevelEnum.STREET.getValue().equals(v.getAreaLevel())).map(AdsMsProcessRespVO::getAreaCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(sCodes)) {
            streetCodes.addAll(sCodes);
        }
        final Map<String, PopulationDataInfoVO> areaPopulations = cdcAdminServiceApi.groupByAreaCodesIncludeStreet(reqDTO.getEndDate(), provinceCodes, cityCodes, districtCodes, streetCodes);
        for (AdsMsProcessRespVO respVO : respVOS) {
            final PopulationDataInfoVO populationDataInfoVO = areaPopulations.get(respVO.getAreaCode());
            if (populationDataInfoVO != null) {
                respVO.setPopulation(populationDataInfoVO.getResidentPopulation());
            }
        }
        setRatio(respVOS);
        return respVOS;
    }

    private List<AdsMsProcessRespVO> buildStat(AdsMsProcessReqDTO reqDTO,
                                               Function<AdsMsProcessReqDTO, List<AdsMsProcessRespVO>> queryFunction,
                                               Function<AdsMsProcessRespVO, String> descriptionGetter,
                                               BiConsumer<AdsMsProcessRespVO, String> descriptionSetter,
                                               List<Pair<Function<AdsMsProcessRespVO, ?>, BiConsumer<AdsMsProcessRespVO, ?>>> otherProperties) {

        return StatUtils.build(reqDTO,
                AdsMsProcessRespVO::new,
                descriptionGetter,
                descriptionSetter,
                queryFunction,
                AdsMsProcessRespVO.buildCommonOperations(),
                otherProperties);
    }

    @Override
    public ResponseEntity<byte[]> groupAreaExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.groupArea(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByReportCard(vos, ReportCardInfectAreaExcelVO.class);
    }

    @Override
    public ResponseEntity<byte[]> groupAreaDetailExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.groupAreaDetail(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByReportCard(vos,ReportCardInfectAreaExcelVO.class);
    }

    @Override
    public List<AdsMsProcessRespVO> groupAddrType(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> respVOS = getDataByType(reportRecordType,
                () ->  StatUtils.fulfillPeriodValue(reqDTO, reportMsProcessStatMapper::groupAddrType,
                        (last, curr) -> curr.getDescription().equals(last.getDescription()), AdsMsProcessRespVO.currValueAndLastValueMappingList()),
                () -> buildStat(reqDTO, adsReportMsProcessStatMapper::groupAddrType,
                        AdsMsProcessRespVO::getDescription,
                        AdsMsProcessRespVO::setDescription));

        setPopulation(reqDTO, respVOS);
        setRatio(respVOS);
        return respVOS;
    }

    @Override
    public ResponseEntity<byte[]> groupAddrTypeExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.groupAddrType(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByReportCard(vos,ReportCardInfectAddrTypeExcelVO.class);
    }

    @Override
    public List<AdsMsProcessRespVO> groupAge(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> respVOS = getDataByType(reportRecordType
                , ()-> StatUtils.fulfillPeriodValue(reqDTO, reportMsProcessStatMapper::groupAge,
                        (last, curr) -> curr.getDescription().equals(last.getDescription()), AdsMsProcessRespVO.currValueAndLastValueMappingList())
                , () -> buildStat(reqDTO, adsReportMsProcessStatMapper::groupAge,
                        AdsMsProcessRespVO::getPatientAgeGroup,
                        AdsMsProcessRespVO::setPatientAgeGroup,
                        AdsMsProcessRespVO.buildOtherProperties())
        );

        setPopulation(reqDTO, respVOS);
        setRatio(respVOS);
        return respVOS;
    }

    @Override
    public ResponseEntity<byte[]> groupAgeExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.groupAge(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByReportCard(vos,ReportCardInfectAgeExcelVO.class);
    }

    @Override
    public List<AdsMsProcessRespVO> groupSex(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> respVOS = getDataByType(reportRecordType
                , () -> StatUtils.fulfillPeriodValue(reqDTO, reportMsProcessStatMapper::groupSex,
                        (last, curr) -> curr.getDescription().equals(last.getDescription()), AdsMsProcessRespVO.currValueAndLastValueMappingList())
                , () -> buildStat(reqDTO, adsReportMsProcessStatMapper::groupSex,
                        AdsMsProcessRespVO::getPatientSexName,
                        AdsMsProcessRespVO::setPatientSexName)
        );

        setPopulation(reqDTO, respVOS);
        setRatio(respVOS);
        return respVOS;
    }

    @Override
    public ResponseEntity<byte[]> groupSexExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.groupSex(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByReportCard(vos,ReportCardInfectSexExcelVO.class);
    }

    @Override
    public List<AdsMsProcessRespVO> groupJob(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> respVOS = getDataByType(reportRecordType
                , () -> StatUtils.fulfillPeriodValue(reqDTO, reportMsProcessStatMapper::groupJob,
                        (last, curr) -> curr.getDescription().equals(last.getDescription()), AdsMsProcessRespVO.currValueAndLastValueMappingList())
                , () -> buildStat(reqDTO, adsReportMsProcessStatMapper::groupJob,
                        AdsMsProcessRespVO::getPatientJob,
                        AdsMsProcessRespVO::setPatientJob)
                );

        setPopulation(reqDTO, respVOS);
        setRatio(respVOS);
        return respVOS;
    }

    @Override
    public ResponseEntity<byte[]> groupJobExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.groupJob(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByReportCard(vos,ReportCardInfectJobExcelVO.class);
    }

    @Override
    public List<AdsMsProcessRespVO> overall(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        if (DateDimEnum.YEAR.getCode().equals(reqDTO.getDateDimType()) && null != reqDTO.getBeforeYear()) {
            return overallForYear(reqDTO, loginUserId, loginUserName);
        }
        setAreaInfoList(reqDTO, loginUserName);
        final Date reqStartDate = reqDTO.getStartDate();
        final Date reqEndDate = reqDTO.getEndDate();
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), reqStartDate, reqEndDate));
        List<AdsMsProcessRespVO> respVOS = getDataByType(reportRecordType,
                () -> getInfectOverAll(reqDTO),
                () -> getAdsInfectOverAll(reqDTO));
        if (reqDTO.getBeforeYear() != null && reqStartDate != null && reqEndDate != null && CollUtil.isNotEmpty(respVOS)) {
            this.coverBeforeYearNewCnt(reqDTO, reqStartDate, reqEndDate, respVOS);
        }
        // 设置预测值（在原统计时间范围后追加）
        DateDim.PreDate preDate = DateDim.getPreDate(reqDTO.getDateDimType(), reqEndDate, 3);
        reqDTO.setStartDate(preDate.getStartDate());
        reqDTO.setEndDate(preDate.getEndDate());
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), preDate.getStartDate(), preDate.getEndDate()));
        List<AdsMsProcessRespVO> preVOS = getDataByType(reportRecordType,
                () -> getInfectOverAll(reqDTO),
                () -> getAdsInfectOverAll(reqDTO));

        for (AdsMsProcessRespVO preVO : preVOS) {
            preVO.setProcessNewCnt(preVO.getProcessNewCntPre());
        }
        respVOS.addAll(preVOS);

        List<DateDim> dateDims = dimDateUtils.getDateDims(reqDTO.getDateDimType(), reqStartDate, preDate.getEndDate());
        respVOS = dimDateUtils.fillTimeData(reqDTO.getDateDimType(), AdsMsProcessRespVO.class, dateDims, respVOS);
        return respVOS;
    }

    private void coverBeforeYearNewCnt(AdsMsProcessReqDTO reqDTO, Date reqStartDate, Date reqEndDate, List<AdsMsProcessRespVO> respVOS) {
        int offset = reqDTO.getBeforeYear() - DateUtil.year(reqStartDate);
        // 上一年的自带了，不需要额外查询
        if (offset != -1) {
            Date beforeYearStart = DateUtil.offset(reqStartDate, DateField.YEAR, offset);
            Date beforeYearEnd = DateUtil.offset(reqEndDate, DateField.YEAR, offset);
            beforeYearEnd = DateUtil.offset(beforeYearEnd, DateField.DAY_OF_YEAR, -1);

            // 重新设置时间范围查询
            reqDTO.setStartDate(beforeYearStart);
            reqDTO.setEndDate(beforeYearEnd);
            reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), beforeYearStart, beforeYearEnd));
            Map<String, AdsMsProcessRespVO> beforeMap = getInfectOverAll(reqDTO).stream().collect(Collectors.toMap(vo ->{
                if ("year".equals(reqDTO.getDateDimType())){
                    return vo.getStatDate();
                }
                return vo.getStatDate().substring(4);} , Function.identity()));
            respVOS.forEach(respVO -> {
                AdsMsProcessRespVO beforeVO = beforeMap.get(respVO.getStatDate().substring(4));
                if (beforeVO != null) {
                    respVO.setProcessNewCntLastY(beforeVO.getProcessNewCnt());
                } else {
                    respVO.setProcessNewCntLastY(null);
                }
            });
        }
    }

    private List<AdsMsProcessRespVO> overallForYear(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        final Date reqStartDate = reqDTO.getStartDate();
        Integer beforeYear = reqDTO.getBeforeYear();
        Date newStartDate;
        if (beforeYear >= 3 && beforeYear <= 10) {
            newStartDate = DateUtils.setYears(reqStartDate, DateUtil.year(new Date()) - beforeYear + 1);
        } else {
            newStartDate = DateUtils.setYears(reqStartDate, beforeYear);
        }
        final Date reqEndDate = reqDTO.getEndDate();
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), newStartDate, reqEndDate));
        List<AdsMsProcessRespVO> respVOS = getDataByType(reportRecordType,
                () -> getInfectOverAll(reqDTO),
                () -> getAdsInfectOverAll(reqDTO));

        // 设置预测值（在原统计时间范围后追加）
        DateDim.PreDate preDate = DateDim.getPreDate(reqDTO.getDateDimType(), reqEndDate, 3);
        reqDTO.setStartDate(preDate.getStartDate());
        reqDTO.setEndDate(preDate.getEndDate());
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), preDate.getStartDate(), preDate.getEndDate()));
        List<AdsMsProcessRespVO> preVOS = getDataByType(reportRecordType,
                () -> getInfectOverAll(reqDTO),
                () -> getAdsInfectOverAll(reqDTO));


        for (AdsMsProcessRespVO preVO : preVOS) {
            preVO.setProcessNewCnt(preVO.getProcessNewCntPre());
        }
        respVOS.addAll(preVOS);

        List<DateDim> dateDims = dimDateUtils.getDateDims(reqDTO.getDateDimType(), newStartDate, preDate.getEndDate());
        respVOS = dimDateUtils.fillTimeData(reqDTO.getDateDimType(), AdsMsProcessRespVO.class, dateDims, respVOS);
        return respVOS;
    }

    @Override
    public String overallAwareness(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        // 获取统计数据
        final String reqEndDate = DateFormatUtil.parseDate(reqDTO.getEndDate(), DateFormatUtil.SHORT_DATE_FORMAT);
        List<AdsMsProcessRespVO> respVOS = this.overall(reqDTO, loginUserId, loginUserName);
        if (respVOS.isEmpty()) {
            return null;
        }
        // 填充校准数据，用于生成实时再生数
        Integer startInfected = respVOS.get(0).getProcessNewCnt();
        Integer endInfected = null;
        String startDay = respVOS.get(0).getStatDate();
        String endDay = respVOS.get(respVOS.size() - 1).getStatDate();

        List<AwarenessWithCalibrateDTO.CalibrationData> calibrationData = new ArrayList<>(respVOS.size());
        boolean contentEmpty = true;
        for (AdsMsProcessRespVO vo : respVOS) {
            AwarenessWithCalibrateDTO.CalibrationData data = new AwarenessWithCalibrateDTO.CalibrationData();
            data.setDate(vo.getStatDate());
            data.setNewInfections(DataUtils.null2Zero(vo.getProcessNewCnt()));
            data.setNewDeaths(DataUtils.null2Zero(vo.getProcessDeadCnt()));
            calibrationData.add(data);

            if (data.getNewInfections() > 0 || data.getNewDeaths() > 0) {
                contentEmpty = false;
            }
            endInfected = vo.getProcessNewCnt();
            if (vo.getStatDate().equals(reqEndDate)) {
                break;
            }
        }
        // 没有内容就不调用算法
        if (contentEmpty) {
            log.debug("统计数据里没有大于0的值，不调用实时再生数相关算法");
            return null;
        }
        // 在 overall 方法里提前处理过区划信息了，这里直接调用
        PopulationDataInfoVO population = cdcAdminServiceApi.statByAreaCodes(reqDTO.getEndDate(), reqDTO.getProvinceCodes(),
                reqDTO.getCityCodes(), reqDTO.getDistrictCodes());
        // 设置尽可能少的算法参数，以后有需要再增加
        AwarenessWithCalibrateDTO req = new AwarenessWithCalibrateDTO();
        req.setStartDay(DateFormatUtil.formatDate(startDay, DateFormatUtil.SHORT_DATE_FORMAT));
        req.setEndDay(DateFormatUtil.formatDate(endDay, DateFormatUtil.SHORT_DATE_FORMAT));
        req.setPopSize(DataUtils.null2Zero(population.getResidentPopulation()));
        req.setCalibrationData(calibrationData);

        // 初始感染数尽量不为 0
        req.setPopInfected(DataUtils.firstNonzero(endInfected, startInfected, 100));
        req.setBeta(0.016d);
        req.setAsympFactor(1d);
        req.setIfCalibrate(true);
        req.setIfContour(false);
        req.setPredDay(endDay);

        return cdcPlatformApi.awareness(req, loginUserId);
    }

    @Override
    public List<ProcessAwarenessRespVO> loadAwarenessResult(AdsMsProcessReqDTO reqDTO, String loginUserId) {
        AwarenessResultVO resp;
        try {
            AwarenessWithCalibrateDTO req = new AwarenessWithCalibrateDTO(reqDTO.getAwarenessTaskId(), reqDTO.getStartDate(), reqDTO.getEndDate());
            resp = cdcPlatformApi.loadAwarenessResult(req, loginUserId);
        } catch (Exception ex) {
            log.error("加载预警结果失败", ex);
            throw new MedicalBusinessException("获取结果错误");
        }
        if (resp == null) {
            return null;
        }
        return resp.getCalibrateResult().stream()
                .map(data -> {
                    ProcessAwarenessRespVO vo = new ProcessAwarenessRespVO();
                    vo.setStatDate(data.getDate());
                    vo.setRtValue(data.getRtValue());
                    vo.setRtCiLowerBound(data.getRtCiLowerBound());
                    vo.setRtCiUpperBound(data.getRtCiUpperBound());
                    return vo;
                })
                .collect(Collectors.toList());
    }

    @Override
    public Collection<List<AdsMsProcessRespVO>> areaChange(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        if (CollectionUtils.isEmpty(reqDTO.getProvinceCodes())
                && CollectionUtils.isEmpty(reqDTO.getCityCodes())
                && CollectionUtils.isEmpty(reqDTO.getDistrictCodes())) {
            setAreaInfoList(reqDTO, loginUserName);
        } else if (!CollectionUtils.isEmpty(reqDTO.getStreetCodes()) || CollectionUtil.isNotEmpty(reqDTO.getStreetCodeList())) {
            //街道按区域进行分组
            reqDTO.setAreaLevel(AreaLevelEnum.DISTRICT.getValue());
        } else {
            reqDTO.setAreaLevel(reqDTO.getAreaLevel() - 1);
        }

        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), reqDTO.getStartDate(), reqDTO.getEndDate()));
        // 先查询出数据局
        List<AdsMsProcessRespVO> respVOS = getDataByType(reportRecordType
                ,() -> getInfectAreaChange(reqDTO)
                ,() -> getAdsInfectAreaChange(reqDTO)
                );

        Date startDate = reqDTO.getStartDate();
        //设置预测值
        DateDim.PreDate preDate = DateDim.getPreDate(reqDTO.getDateDimType(), reqDTO.getEndDate(), 3);
        reqDTO.setStartDate(preDate.getStartDate());
        reqDTO.setEndDate(preDate.getEndDate());
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), preDate.getStartDate(), preDate.getEndDate()));
        List<AdsMsProcessRespVO> preVOS = getDataByType(reportRecordType
                ,() -> getInfectAreaChange(reqDTO)
                ,() -> getAdsInfectAreaChange(reqDTO)
        );

        for (AdsMsProcessRespVO preVO : preVOS) {
            preVO.setProcessNewCnt(preVO.getProcessNewCntPre());
        }
        respVOS.addAll(preVOS);

        Collection<List<AdsMsProcessRespVO>> values = respVOS.stream()
                                                             .filter(e -> StringUtils.isNotBlank(e.getAreaCode()))
                                                             .collect(Collectors.groupingBy(AdsMsProcessRespVO::getAreaCode))
                                                             .values();
        List<DateDim> dateDims = dimDateUtils.getDateDims(reqDTO.getDateDimType(), startDate, preDate.getEndDate());
        Collection<List<AdsMsProcessRespVO>> ret = new ArrayList<>();
        for (List<AdsMsProcessRespVO> value : values) {
            String areaCode = value.get(0).getAreaCode();
            String areaName = value.get(0).getAreaName();
            List<AdsMsProcessRespVO> voList = dimDateUtils.fillTimeData(reqDTO.getDateDimType(), AdsMsProcessRespVO.class, dateDims, value);
            voList.forEach(vo -> {
                vo.setAreaName(areaCode);
                vo.setAreaName(areaName);
                vo.setDescription(areaName);
            });
            ret.add(voList);
        }
        return ret;
    }

    @Override
    public Collection<List<AdsMsProcessRespVO>> sexChange(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        Date startDate = reqDTO.getStartDate();
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), reqDTO.getStartDate(), reqDTO.getEndDate()));
        // 先查询出数据局
        List<AdsMsProcessRespVO> respVOS = getDataByType(reportRecordType,
                () -> getInfectSexChange(reqDTO),
                () -> getAdsInfectSexChange(reqDTO)
        );
        //设置预测值
        DateDim.PreDate preDate = DateDim.getPreDate(reqDTO.getDateDimType(), reqDTO.getEndDate(), 3);
        reqDTO.setStartDate(preDate.getStartDate());
        reqDTO.setEndDate(preDate.getEndDate());
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), preDate.getStartDate(), preDate.getEndDate()));
        List<AdsMsProcessRespVO> preVOS = getDataByType(reportRecordType,
                () -> getInfectSexChange(reqDTO),
                () -> getAdsInfectSexChange(reqDTO)
        );
        for (AdsMsProcessRespVO preVO : preVOS) {
            preVO.setProcessNewCnt(preVO.getProcessNewCntPre());
        }
        respVOS.addAll(preVOS);
        Collection<List<AdsMsProcessRespVO>> values = respVOS.stream().collect(Collectors.groupingBy(AdsMsProcessRespVO::getPatientSexName)).values();
        List<DateDim> dateDims = dimDateUtils.getDateDims(reqDTO.getDateDimType(), startDate, preDate.getEndDate());
        Collection<List<AdsMsProcessRespVO>> ret = new ArrayList<>();
        for (List<AdsMsProcessRespVO> value : values) {
            String patientSexName = value.get(0).getPatientSexName();
            List<AdsMsProcessRespVO> voList = dimDateUtils.fillTimeData(reqDTO.getDateDimType(), AdsMsProcessRespVO.class, dateDims, value);
            voList.forEach(vo -> {
                vo.setPatientSexName(patientSexName);
                vo.setDescription(patientSexName);
            });
            ret.add(voList);
        }
        return ret;
    }

    @Override
    public Collection<List<AdsMsProcessRespVO>> ageChange(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        Date startDate = reqDTO.getStartDate();
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), reqDTO.getStartDate(), reqDTO.getEndDate()));
        // 先查询出数据局
        List<AdsMsProcessRespVO> respVOS = getDataByType(reportRecordType
                , () -> getInfectAgeChange(reqDTO)
                , () -> getAdsInfectAgeChange(reqDTO));
        //设置预测值
        DateDim.PreDate preDate = DateDim.getPreDate(reqDTO.getDateDimType(), reqDTO.getEndDate(), 3);
        reqDTO.setStartDate(preDate.getStartDate());
        reqDTO.setEndDate(preDate.getEndDate());
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), preDate.getStartDate(), preDate.getEndDate()));
        List<AdsMsProcessRespVO> preVOS = getDataByType(reportRecordType
                , () -> getInfectAgeChange(reqDTO)
                , () -> getAdsInfectAgeChange(reqDTO));

        for (AdsMsProcessRespVO preVO : preVOS) {
            preVO.setProcessNewCnt(preVO.getProcessNewCntPre());
        }
        respVOS.addAll(preVOS);
        Map<String, List<AdsMsProcessRespVO>> map =  respVOS.stream().collect(Collectors.groupingBy(AdsMsProcessRespVO::getPatientAgeGroup));
        LinkedHashMap<String, List<AdsMsProcessRespVO>> listLinkedHashMap = map.entrySet().stream().sorted(Comparator.comparing(entry -> {
            String[] parts = entry.getKey().split("-");
            if (parts.length < 1){
                return Integer.MAX_VALUE;
            }
            try {
                return Integer.parseInt(parts[0]);
            } catch (NumberFormatException e) {
                return Integer.MAX_VALUE;
            }
        })).collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                (oldValue, newValue) -> oldValue,
                LinkedHashMap::new));
        Collection<List<AdsMsProcessRespVO>> values = listLinkedHashMap.values();
        List<DateDim> dateDims = dimDateUtils.getDateDims(reqDTO.getDateDimType(), startDate, preDate.getEndDate());
        Collection<List<AdsMsProcessRespVO>> ret = new ArrayList<>();
        for (List<AdsMsProcessRespVO> value : values) {
            String patientAgeGroup = value.get(0).getPatientAgeGroup();
            List<AdsMsProcessRespVO> voList = dimDateUtils.fillTimeData(reqDTO.getDateDimType(), AdsMsProcessRespVO.class, dateDims, value);
            voList.forEach(vo -> {
                vo.setPatientAgeGroup(patientAgeGroup);
                vo.setDescription(patientAgeGroup);
            });
            ret.add(voList);
        }
        return ret;
    }

    @Override
    public List<AdsMsProcessRespVO> qOQTrendChart(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        List<DateDim> dateDims = dimDateUtils.getDateDims(reqDTO.getDateDimType(), reqDTO.getStartDate(), reqDTO.getEndDate());
        reqDTO.setDateDims(dateDims);

        List<AdsMsProcessRespVO> respVOS = getDataByType(reportRecordType,
                () -> getQOQTrendChart(reqDTO)
                , () -> getAdsQOQTrendChart(reqDTO));

        if (respVOS.isEmpty()) {
            return new ArrayList<>();
        }

        // 计算环比
        for (int i = 0; i < respVOS.size(); i++) {
            if (i == 0) {
                respVOS.get(i).calQOQRates(respVOS.get(i).getProcessNewCnt(), 0);
            } else {
                respVOS.get(i).calQOQRates(respVOS.get(i).getProcessNewCnt(), respVOS.get(i - 1).getProcessNewCnt());
            }
        }
        return respVOS;
    }

    @Override
    public List<AdsMsProcessRespVO> areaMedDateCount(AdsMsProcessReqDTO reqDTO) {
        setAreaInfoList(reqDTO, userInfo.get().getLoginName());
        return getDataByType(reportRecordType
                ,() -> getInfectAreaChange(reqDTO)
                ,() -> getAdsInfectAreaChange(reqDTO));

    }

    @Override
    public List<AdsMsProcessRespVO> groupIdentifyClass(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> resp = getDataByType(reportRecordType
                , ArrayList::new
                , () -> adsReportMsProcessStatMapper.groupIdentifyClass(reqDTO));
        setPopulation(reqDTO, resp);
        setRatio(resp);
        return resp;
    }

    @Override
    public List<AdsMsProcessRespVO> groupOutcomeStatus(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> resp = getDataByType(reportRecordType
                ,ArrayList::new
                ,() -> adsReportMsProcessStatMapper.groupOutcomeStatus(reqDTO));
        setPopulation(reqDTO, resp);
        setRatio(resp);
        return resp;
    }

    @Override
    public List<AdsMsProcessRespVO> groupSymptomFirst(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> resp = getDataByType(reportRecordType
                ,ArrayList::new
                ,() -> adsReportMsProcessStatMapper.groupSymptomFirst(reqDTO));
        setPopulation(reqDTO, resp);
        setRatio(resp);
        return resp;
    }

    @Override
    public List<AdsMsProcessRespVO> groupHistoryBefore(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> resp = getDataByType(reportRecordType
                ,ArrayList::new
                ,() -> adsReportMsProcessStatMapper.groupHistoryBefore(reqDTO));
        setPopulation(reqDTO, resp);
        setRatio(resp);
        return resp;
    }

    @Override
    public EpidemicDistributionRespVO groupPathogenResNominal(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> details = getDataByType(reportRecordType
                , ArrayList::new
                , () -> adsReportMsProcessStatMapper.groupPathogenResNominal(reqDTO));
        setPopulation(reqDTO, details);
        setRatio(details);

        // 构建返回对象
        EpidemicDistributionRespVO respVO = new EpidemicDistributionRespVO();
        respVO.setDetails(details);
        respVO.setProcessTotal(details.stream().mapToInt(AdsMsProcessRespVO::getProcessNewCnt).sum());
        respVO.setPathogenTotal(details.stream().filter(vo -> !"无病原学结果".equals(vo.getDescription())).mapToInt(AdsMsProcessRespVO::getProcessNewCnt).sum());
        respVO.setPathogenPositiveCnt(details.stream().filter(vo -> "病原学结果阳性".equals(vo.getDescription())).mapToInt(AdsMsProcessRespVO::getProcessNewCnt).sum());
        respVO.setPathogenPositiveRate(DataUtils.divide(respVO.getPathogenPositiveCnt(), respVO.getPathogenTotal()));
        return respVO;
    }

    @Override
    public List<AdsMsProcessRespVO> groupDiseaseName(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        setAreaInfoList(reqDTO, loginUserName);
        List<AdsMsProcessRespVO> resp = getDataByType(reportRecordType
                , () -> reportMsProcessStatMapper.groupDiseaseName(reqDTO)
                , () -> adsReportMsProcessStatMapper.groupDiseaseName(reqDTO));
        setPopulation(reqDTO, resp);
        setRatio(resp);
        return resp;
    }

    @Override
    public ResponseEntity<byte[]> groupDiseaseNameExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName) {
        List<AdsMsProcessRespVO> vos = this.groupDiseaseName(reqDTO, loginUserId, loginUserName);
        return DataUtils.exportBytesByReportCard(vos,ReportCardInfectNameExcelVO.class);
    }

    @Override
    public List<AdsMsProcessRespVO> getDiseaseDistributionBox(String loginUserName) {
        // 创建一个基本的查询参数
        AdsMsProcessReqDTO reqDTO = new AdsMsProcessReqDTO();
        setAreaInfoList(reqDTO, loginUserName);
        return reportMsProcessStatMapper.groupDiseaseCodeAndName(reqDTO);
    }

    @Override
    public List<AdsMsProcessRespVO> timeJudgeModelTrend(AdsMsProcessReqDTO reqDTO, String loginUserName) {
        adminServiceApi.setSyndromeCode(reqDTO);
        setAreaInfoList(reqDTO, loginUserName);
        final Date reqStartDate = reqDTO.getStartDate();
        final Date reqEndDate = reqDTO.getEndDate();
        reqDTO.setDateDims(dimDateUtils.getDateDims(reqDTO.getDateDimType(), reqStartDate, reqEndDate));
        List<AdsMsProcessRespVO> respVOS = getDataByType(reportRecordType,
                () -> getInfectOverAll(reqDTO),
                () -> getAdsInfectOverAll(reqDTO));

        List<DateDim> dateDims = dimDateUtils.getDateDims(reqDTO.getDateDimType(), reqStartDate, reqEndDate);
        respVOS = dimDateUtils.fillTimeData(reqDTO.getDateDimType(), AdsMsProcessRespVO.class, dateDims, respVOS);

        // 设置指定年份的同期值（在原统计时间范围内补充）
        if (reqDTO.getBeforeYear() != null && reqStartDate != null && reqEndDate != null && CollUtil.isNotEmpty(respVOS)) {
            this.coverBeforeYearNewCnt(reqDTO, reqStartDate, reqEndDate, respVOS);
        }

        List<Double> processNewCntInputList = respVOS.stream()
                .map(item -> item.getProcessNewCnt() == null ? 0 : new Double(item.getProcessNewCnt().toString()))
                .collect(Collectors.toList());
        List<Double> processNewCntPredictList = predictionService.predict(processNewCntInputList, 14);

        List<AdsMsProcessRespVO> predictDataList = new ArrayList<>();       //
        Date preStartDate = org.apache.commons.lang.time.DateUtils.addDays(reqDTO.getEndDate(), 1);
        Date preEndDate = org.apache.commons.lang.time.DateUtils.addDays(preStartDate, 13);
        List<DateDim> dateDimsPre = dimDateUtils.getDateDims(DateDimEnum.DAY.getCode(), preStartDate, preEndDate);

        for (int i = 0; i < dateDimsPre.size(); i++) {
            DateDim dateDim = dateDimsPre.get(i);
            AdsMsProcessRespVO predictAdsMsProcessRespVO = new AdsMsProcessRespVO();
            predictAdsMsProcessRespVO.setProcessNewCnt((int)Math.floor(processNewCntPredictList.get(i)));
            predictAdsMsProcessRespVO.setStatDate(dateDim.getDate());
            predictAdsMsProcessRespVO.setHalfYear(dateDim.getHalfYear());
            predictDataList.add(predictAdsMsProcessRespVO);
        }
        respVOS.addAll(predictDataList);
        return respVOS;
    }

    private List<AdsMsProcessRespVO> getInfectAreaChange(AdsMsProcessReqDTO reqDTO) {
        return dataUtils.getDateDimData(reqDTO.getDateDimType(),
                () -> reportMsProcessStatMapper.dayAreaChange(reqDTO),
                () -> reportMsProcessStatMapper.weekAreaChange(reqDTO),
                () -> reportMsProcessStatMapper.meadowAreaChange(reqDTO),
                () -> reportMsProcessStatMapper.monthAreaChange(reqDTO),
                () -> reportMsProcessStatMapper.quarterAreaChange(reqDTO),
                () -> reportMsProcessStatMapper.yearAreaChange(reqDTO)
        );
    }

    private List<AdsMsProcessRespVO> getAdsInfectAreaChange(AdsMsProcessReqDTO reqDTO) {
        return dataUtils.getDateDimData(reqDTO.getDateDimType(),
                () -> adsReportMsProcessStatMapper.dayAreaChange(reqDTO),
                () -> adsReportMsProcessStatMapper.weekAreaChange(reqDTO),
                () -> adsReportMsProcessStatMapper.meadowAreaChange(reqDTO),
                () -> adsReportMsProcessStatMapper.monthAreaChange(reqDTO),
                () -> adsReportMsProcessStatMapper.quarterAreaChange(reqDTO),
                () -> adsReportMsProcessStatMapper.yearAreaChange(reqDTO)
        );
    }

    private List<AdsMsProcessRespVO> getInfectSexChange(AdsMsProcessReqDTO reqDTO) {
        return dataUtils.getDateDimData(reqDTO.getDateDimType(),
                () -> reportMsProcessStatMapper.daySexChange(reqDTO),
                () -> reportMsProcessStatMapper.weekSexChange(reqDTO),
                () -> reportMsProcessStatMapper.meadowSexChange(reqDTO),
                () -> reportMsProcessStatMapper.monthSexChange(reqDTO),
                () -> reportMsProcessStatMapper.quarterSexChange(reqDTO),
                () -> reportMsProcessStatMapper.yearSexChange(reqDTO)
        );
    }

    private List<AdsMsProcessRespVO> getAdsInfectSexChange(AdsMsProcessReqDTO reqDTO) {
        return dataUtils.getDateDimData(reqDTO.getDateDimType(),
                () -> adsReportMsProcessStatMapper.daySexChange(reqDTO),
                () -> adsReportMsProcessStatMapper.weekSexChange(reqDTO),
                () -> adsReportMsProcessStatMapper.meadowSexChange(reqDTO),
                () -> adsReportMsProcessStatMapper.monthSexChange(reqDTO),
                () -> adsReportMsProcessStatMapper.quarterSexChange(reqDTO),
                () -> adsReportMsProcessStatMapper.yearSexChange(reqDTO)
        );
    }

    private List<AdsMsProcessRespVO> getInfectAgeChange(AdsMsProcessReqDTO reqDTO) {
        return dataUtils.getDateDimData(reqDTO.getDateDimType(),
                () -> reportMsProcessStatMapper.dayAgeChange(reqDTO),
                () -> reportMsProcessStatMapper.weekAgeChange(reqDTO),
                () -> reportMsProcessStatMapper.meadowAgeChange(reqDTO),
                () -> reportMsProcessStatMapper.monthAgeChange(reqDTO),
                () -> reportMsProcessStatMapper.quarterAgeChange(reqDTO),
                () -> reportMsProcessStatMapper.yearAgeChange(reqDTO)
        );
    }

    private List<AdsMsProcessRespVO> getAdsInfectAgeChange(AdsMsProcessReqDTO reqDTO) {
        return dataUtils.getDateDimData(reqDTO.getDateDimType(),
                () -> adsReportMsProcessStatMapper.dayAgeChange(reqDTO),
                () -> adsReportMsProcessStatMapper.weekAgeChange(reqDTO),
                () -> adsReportMsProcessStatMapper.meadowAgeChange(reqDTO),
                () -> adsReportMsProcessStatMapper.monthAgeChange(reqDTO),
                () -> adsReportMsProcessStatMapper.quarterAgeChange(reqDTO),
                () -> adsReportMsProcessStatMapper.yearAgeChange(reqDTO)
        );
    }

    private List<AdsMsProcessRespVO> getInfectOverAll(AdsMsProcessReqDTO reqDTO){
        return dataUtils.getDateDimData(reqDTO.getDateDimType(),
                () -> reportMsProcessStatMapper.dayOverAll(reqDTO),
                () -> reportMsProcessStatMapper.weekOverAll(reqDTO),
                () -> reportMsProcessStatMapper.meadowOverAll(reqDTO),
                () -> reportMsProcessStatMapper.monthOverAll(reqDTO),
                () -> reportMsProcessStatMapper.quarterOverAll(reqDTO),
                () -> reportMsProcessStatMapper.yearOverAll(reqDTO)
        );
    }

    private List<AdsMsProcessRespVO> getAdsInfectOverAll(AdsMsProcessReqDTO reqDTO){
        return dataUtils.getDateDimData(reqDTO.getDateDimType(),
                () -> adsReportMsProcessStatMapper.dayOverAll(reqDTO),
                () -> adsReportMsProcessStatMapper.weekOverAll(reqDTO),
                () -> adsReportMsProcessStatMapper.meadowOverAll(reqDTO),
                () -> adsReportMsProcessStatMapper.monthOverAll(reqDTO),
                () -> adsReportMsProcessStatMapper.quarterOverAll(reqDTO),
                () -> adsReportMsProcessStatMapper.yearOverAll(reqDTO)
        );
    }

    private List<AdsMsProcessRespVO> getQOQTrendChart(AdsMsProcessReqDTO reqDTO) {
        return dimDateUtils.timeTrendData(reqDTO,
                AdsMsProcessRespVO.class,
                () -> reportMsProcessStatMapper.dayQOQTrendChart(reqDTO),
                () -> reportMsProcessStatMapper.weekQOQTrendChart(reqDTO),
                () -> reportMsProcessStatMapper.meadowQOQTrendChart(reqDTO),
                () -> reportMsProcessStatMapper.monthQOQTrendChart(reqDTO),
                () -> reportMsProcessStatMapper.quarterQOQTrendChart(reqDTO),
                () -> reportMsProcessStatMapper.yearQOQTrendChart(reqDTO));

    }

    private List<AdsMsProcessRespVO> getAdsQOQTrendChart(AdsMsProcessReqDTO reqDTO) {
        return dimDateUtils.timeTrendData(reqDTO,
                AdsMsProcessRespVO.class,
                () -> adsReportMsProcessStatMapper.dayQOQTrendChart(reqDTO),
                () -> adsReportMsProcessStatMapper.weekQOQTrendChart(reqDTO),
                () -> adsReportMsProcessStatMapper.meadowQOQTrendChart(reqDTO),
                () -> adsReportMsProcessStatMapper.monthQOQTrendChart(reqDTO),
                () -> adsReportMsProcessStatMapper.quarterQOQTrendChart(reqDTO),
                () -> adsReportMsProcessStatMapper.yearQOQTrendChart(reqDTO));

    }

    private <T> List<T> getDataByType(String reportRecordType, DataUtils.DataGetterT<T> appReportGetter,
                                      DataUtils.DataGetterT<T> adsReportGetter) {
        switch (reportRecordType){
            case "app":
                return appReportGetter.get();
            case "ads":
                return adsReportGetter.get();
            default:
                return new ArrayList<>();
        }
    }

    private void resetDateRange(AdsMsProcessReqDTO reqDTO) {
        int startYear = DateUtil.year(reqDTO.getStartDate());
        int endYear = DateUtil.year(reqDTO.getEndDate());

        int startMonth = DateUtil.month(reqDTO.getStartDate()) + 1;
        int endMonth = DateUtil.month(reqDTO.getEndDate()) + 1;
        // 日期范围选在一个月内的情况
        if (startYear == endYear && startMonth == endMonth) {
            if (startMonth + endMonth <= 12) {
                Date startDate = reqDTO.getStartDate();
                Date newStartDate = com.iflytek.cdc.province.utils.DateUtils.addMonths(startDate, 1 - startMonth);
                reqDTO.setStartDate(newStartDate);
            } else {
                Date endDate = reqDTO.getEndDate();
                Date newEndDate = com.iflytek.cdc.province.utils.DateUtils.addMonths(endDate, 12 - endMonth);
                reqDTO.setEndDate(newEndDate);
            }
        }
    }

    private List<AdsMsProcessRespVO> getLastHalfYearData(AdsMsProcessReqDTO reqDTO) {
        reqDTO.setStartDate(DateUtils.addMonths(reqDTO.getStartDate(), -6));
        reqDTO.setEndDate(DateUtils.addMonths(reqDTO.getEndDate(), -6));
        reqDTO.setDateDims(dimDateUtils.getDateDims(DateDimEnum.HALF_YEAR.getCode(), reqDTO.getStartDate(), reqDTO.getEndDate()));
        List<AdsMsProcessRespVO> monthDescVOList = getDataByType(reportRecordType,
                () -> reportMsProcessStatMapper.findMonthDistribution(reqDTO),
                () -> adsReportMsProcessStatMapper.findMonthDistribution(reqDTO));

        reqDTO.setDateDimType(DateDimEnum.HALF_YEAR.getCode());
        return dimDateUtils.fillTimeData(reqDTO.getDateDimType(), AdsMsProcessRespVO.class, reqDTO.getDateDims(), monthDescVOList);
    }
}
