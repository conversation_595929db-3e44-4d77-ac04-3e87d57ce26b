package com.iflytek.cdc.reportcard.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.reportcard.dto.ExchangeInfoRecordQueryDto;
import com.iflytek.cdc.reportcard.entity.bu.ExchangeInfoRecord;
import com.iflytek.cdc.reportcard.vo.ExchangeInfoRecordVO;

public interface ExchangeInfoRecordService extends IService<ExchangeInfoRecord> {
    ExchangeInfoRecord createRecord(ExchangeInfoRecord record, String loginUserId);

    ExchangeInfoRecordVO queryRecordById(String recordId);

    PageInfo<ExchangeInfoRecordVO> queryRecordList(ExchangeInfoRecordQueryDto dto, String loginUserId);
}
