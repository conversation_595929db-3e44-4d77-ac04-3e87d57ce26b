package com.iflytek.cdc.reportcard.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.reportcard.dto.ExchangeInfoRecordQueryDto;
import com.iflytek.cdc.reportcard.dto.ExchangeInfoTemplateQueryDto;
import com.iflytek.cdc.reportcard.entity.bu.ExchangeInfoTemplate;
import com.iflytek.cdc.reportcard.vo.ExchangeInfoTemplateVO;

import java.util.List;

public interface ExchangeInfoTemplateService extends IService< ExchangeInfoTemplate> {
    ExchangeInfoTemplate createTemplate(ExchangeInfoTemplate template, String userId);

    ExchangeInfoTemplateVO queryTemplateById(String templateId);

    PageInfo<ExchangeInfoTemplate> queryTemplateList(ExchangeInfoTemplateQueryDto dto,String loginUserId);

    List<ExchangeInfoTemplate> queryTemplateSelectList(String loginUserId);


}
