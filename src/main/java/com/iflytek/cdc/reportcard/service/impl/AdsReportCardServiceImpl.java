package com.iflytek.cdc.reportcard.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.province.mapper.pg.DimInfectedInfoMapper;
import com.iflytek.cdc.reportcard.dto.ReportCardOperateDto;
import com.iflytek.cdc.reportcard.dto.ReportUploadQueryDTO;
import com.iflytek.cdc.reportcard.entity.ads.ReportUploadRecord;
import com.iflytek.cdc.reportcard.mapper.pg.ReportUploadRecordMapper;
import com.iflytek.cdc.reportcard.service.AdsReportCardService;
import com.iflytek.cdc.reportcard.vo.ReportCardArchiveVO;
import com.iflytek.cdc.reportcard.vo.ReportUploadRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AdsReportCardServiceImpl implements AdsReportCardService {


    private ReportUploadRecordMapper reportUploadRecordMapper;

    @Resource
    private DimInfectedInfoMapper dimInfectedInfoMapper;

    @Autowired
    public void setReportUploadRecordMapper(ReportUploadRecordMapper reportUploadRecordMapper) {
        this.reportUploadRecordMapper = reportUploadRecordMapper;
    }

    @Override
    public PageInfo<ReportUploadRecordVO> dataPageInfo(ReportUploadQueryDTO queryDTO) {
        try (Page<?> ignored = PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize())) {
            List<ReportUploadRecordVO> list = reportUploadRecordMapper.findDyqwList(queryDTO);
            return new PageInfo<>(list);
        }
    }

    @Override
    public List<ReportUploadRecord> queryByIds(List<String> idList) {
        List<ReportUploadRecordVO> reportUploadRecordVOList = this.reportUploadRecordMapper.queryDyqwBatchIds(idList);
        if (reportUploadRecordVOList == null || reportUploadRecordVOList.isEmpty()){
            return new ArrayList<>();
        }
        List<ReportUploadRecord> reportUploadRecordList = new ArrayList<>(reportUploadRecordVOList.size());
        for (ReportUploadRecordVO reportUploadRecordVO : reportUploadRecordVOList) {
            ReportUploadRecord reportUploadRecord = new ReportUploadRecord();
            BeanUtils.copyProperties(reportUploadRecordVO,reportUploadRecord);
            reportUploadRecordList.add(reportUploadRecord);
        }
        return reportUploadRecordList;
    }

    @Override
    public ReportUploadRecordVO findByReportCardId(String reportCardId) {
        return this.reportUploadRecordMapper.findDyqwByReportCardId(reportCardId);
    }

    @Override
    public ReportUploadRecord findById(String id) {
        ReportUploadRecordVO reportUploadRecordVO = this.reportUploadRecordMapper.selectDyqwById(id);
        ReportUploadRecord reportUploadRecord = new ReportUploadRecord();
        BeanUtils.copyProperties(reportUploadRecordVO,reportUploadRecord);
        return reportUploadRecord;
    }

    @Override
    public ReportCardArchiveVO queryArchiveById(String reportRecordId) {
        ReportUploadRecord reportUploadRecord = this.reportUploadRecordMapper.selectById(reportRecordId);
        if (reportUploadRecord == null) {
            return new ReportCardArchiveVO();
        }
        String validCertNumber = reportUploadRecord.getValidCertNumber();
        ReportCardArchiveVO reportCardArchiveVO = this.reportUploadRecordMapper.queryArchiveByCertNum(validCertNumber);
        if (reportCardArchiveVO == null){
            reportCardArchiveVO = new ReportCardArchiveVO();
        }
        return reportCardArchiveVO;
    }

    @Override
    public ReportCardArchiveVO queryArchiveByCertNum(String validCertNumber) {
        ReportCardArchiveVO reportCardArchiveVO = this.reportUploadRecordMapper.queryArchiveByCertNum(validCertNumber);
        if (reportCardArchiveVO == null){
            reportCardArchiveVO = new ReportCardArchiveVO();
        }
        return reportCardArchiveVO;
    }

    @Override
    public Boolean ReportCardOperateDto(ReportCardOperateDto dto) {
        if (dto == null){
            return false;
        }
        String id = dto.getId();
        if (StrUtil.isBlank(id)){
            log.error("缺少id");
            return false;
        }
        Integer operateType = dto.getOperateType();
        if (operateType == null){
            log.error("操作类型为空");
            return false;
        }
        if (!(operateType == 1 || operateType == 2)){
            log.error("操作类型错误");
            return false;
        }
        String operateRemark = dto.getOperateRemark();
        if (StrUtil.isBlank(operateRemark)){
            log.error("操作备注为空");
            return false;
        }
        if (StrUtil.length(operateRemark) > 150){
            log.error("操作备注在150字符以内");
            return false;
        }
        int i = reportUploadRecordMapper.updateReportCardOperate(dto);
        return i > 0;
    }

    @Override
    public PageInfo<ReportUploadRecordVO> dataPageInfoByGX(ReportUploadQueryDTO queryDTO) {
        if (CollUtil.isNotEmpty(queryDTO.getInfectCodeList())){
            queryDTO.setInfectCodeList(dimInfectedInfoMapper.getInfectCodeList(queryDTO));
        }
        if (StringUtils.isEmpty(queryDTO.getSourceId())) {
            queryDTO.setSourceId("11");
        }
        try (Page<?> ignored = PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize())) {
            List<ReportUploadRecordVO> list = reportUploadRecordMapper.findDyqwListByGX(queryDTO);
            return new PageInfo<>(list);
        }
    }
    @Override
    public Integer tobeReviewedCard(ReportUploadQueryDTO queryDTO) {
        if (CollUtil.isNotEmpty(queryDTO.getInfectCodeList())){
            queryDTO.setInfectCodeList(dimInfectedInfoMapper.getInfectCodeList(queryDTO));
        }
        return  reportUploadRecordMapper.tobeReviewedCardCount(queryDTO);
    }

    @Override
    public PageInfo<HashMap<String, String>> reportingUnitInquiry(ReportUploadQueryDTO queryDTO) {
        try (Page<?> ignored = PageHelper.startPage(queryDTO.getPageIndex(), queryDTO.getPageSize())) {
            List<String> list = reportUploadRecordMapper.reportingUnitInquiry(queryDTO);
            if (CollUtil.isNotEmpty(list)){
                // 前端需要结构
                return new PageInfo<>(list.stream().map(res-> MapUtil.of("unitName",res)).collect(Collectors.toList()));
            }
            return new PageInfo<>();
        }
    }

}
