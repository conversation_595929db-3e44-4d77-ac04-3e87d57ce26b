package com.iflytek.cdc.reportcard.service;

import com.iflytek.cdc.reportcard.dto.ReportUploadStatsQueryDTO;
import com.iflytek.cdc.reportcard.vo.stats.*;

import java.util.List;

public interface AdsReportStatsService {
    ReportUploadSummaryVO notifiableInfectTypeStat(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsVO> groupInfectNewCntTop10(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsVO> groupInfectNewCntDeclineTop10(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsDateDimVO> groupDateDimCnt(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsAreaVO> groupAreaCnt(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsAgeVO> groupAgeCnt(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsGenderVO> groupGenderCnt(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsJobVO> groupJobCnt(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsVO> groupInfectCnt(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsVO> groupInfectTransmitTypeCnt(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsVO> groupInfectDiseaseCnt(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsVO> groupInfectAreaCnt(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsVO> groupInfectAreaOrgCnt(ReportUploadStatsQueryDTO dto);

}
