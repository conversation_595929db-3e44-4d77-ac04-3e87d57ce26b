package com.iflytek.cdc.reportcard.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.apiService.UapServiceApi;
import com.iflytek.cdc.edr.entity.TbCdcAttachment;
import com.iflytek.cdc.edr.entity.uap.UapOrgPo;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import com.iflytek.cdc.reportcard.dto.ExchangeInfoTemplateQueryDto;
import com.iflytek.cdc.reportcard.entity.bu.ExchangeInfoTemplate;
import com.iflytek.cdc.reportcard.mapper.bu.ExchangeInfoTemplateMapper;
import com.iflytek.cdc.reportcard.mapper.bu.TbCdcMrAttachmentMapper;
import com.iflytek.cdc.reportcard.service.ExchangeInfoTemplateService;
import com.iflytek.cdc.reportcard.vo.ExchangeInfoTemplateVO;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class ExchangeInfoTemplateServiceImpl extends ServiceImpl<ExchangeInfoTemplateMapper,ExchangeInfoTemplate> implements ExchangeInfoTemplateService  {

    private UapServiceApi uapServiceApi;

    @Autowired
    public void setUapServiceApi(UapServiceApi uapServiceApi) {
        this.uapServiceApi = uapServiceApi;
    }

    private BatchUidService batchUidService;

    @Autowired
    public void setBatchUidService(BatchUidService batchUidService) {
        this.batchUidService = batchUidService;
    }


    private ExchangeInfoTemplateMapper exchangeInfoTemplateMapper;

    @Autowired
    public void setExchangeInfoTemplateMapper(ExchangeInfoTemplateMapper exchangeInfoTemplateMapper) {
        this.exchangeInfoTemplateMapper = exchangeInfoTemplateMapper;
    }

    private TbCdcMrAttachmentMapper tbCdcMrAttachmentMapper;

    @Autowired
    public void setTbCdcMrAttachmentMapper(TbCdcMrAttachmentMapper tbCdcMrAttachmentMapper) {
        this.tbCdcMrAttachmentMapper = tbCdcMrAttachmentMapper;
    }
    @Override
    public ExchangeInfoTemplate createTemplate(ExchangeInfoTemplate template, String loginUserId) {
        if (template == null){
            log.error("缺少模板对象");
            return new ExchangeInfoTemplate();
        }
        UapUserPo user = uapServiceApi.getUser(loginUserId);
        template.setId(String.valueOf(batchUidService.getUid(ExchangeInfoTemplate.TB_NAME)));
        template.setCreatorId(user.getId());
        template.setCreator(user.getName());
        template.setCreateTime(new Date());
        template.setUpdatorId(user.getId());
        template.setUpdator(user.getName());
        template.setUpdateTime(new Date());


        UapOrgPo userOrgpo = uapServiceApi.getUserOrg(user.getLoginName());
        String district = userOrgpo.getDistrict();
        if (StrUtil.isNotBlank(district)) {
            template.setDistrictName(district);
            template.setDistrictCode(userOrgpo.getDistrictCode());
        }

        String city = userOrgpo.getCity();
        if (StrUtil.isNotBlank(city)) {
            template.setCityName(city);
            template.setCityCode(userOrgpo.getCityCode());
        }

        String province = userOrgpo.getProvince();
        if (StrUtil.isNotBlank(province)) {
            template.setProvinceName(province);
            template.setProvinceCode(userOrgpo.getProvinceCode());
        }
        try {
            this.save(template);
            return template;
        } catch (Exception e) {
            throw new MedicalBusinessException("新增交换信息模板失败");
        }
    }

    @Override
    public ExchangeInfoTemplateVO queryTemplateById(String templateId) {
        if (StrUtil.isBlank(templateId)){
            log.error("缺少templateId");
            return new ExchangeInfoTemplateVO();
        }
        ExchangeInfoTemplate exchangeInfoTemplate = this.getById(templateId);
        if (exchangeInfoTemplate == null){
            log.error("未查询到信息交换模板数据");
            return new ExchangeInfoTemplateVO();
        }
        ExchangeInfoTemplateVO exchangeInfoTemplateVO = new ExchangeInfoTemplateVO();
        BeanUtils.copyProperties(exchangeInfoTemplate,exchangeInfoTemplateVO);
        String attachmentId = exchangeInfoTemplate.getAttachmentId();
        if (StrUtil.isNotBlank(attachmentId)){
            TbCdcAttachment tbCdcAttachment = tbCdcMrAttachmentMapper.selectByPrimaryKey(attachmentId);
            if (tbCdcAttachment != null){

                exchangeInfoTemplateVO.setAttachmentName(tbCdcAttachment.getAttachmentName());
                exchangeInfoTemplateVO.setAttachmentPath(tbCdcAttachment.getAttachmentPath());
                return exchangeInfoTemplateVO;
            }
        }
        return exchangeInfoTemplateVO;
    }

    @Override
    public PageInfo<ExchangeInfoTemplate> queryTemplateList(ExchangeInfoTemplateQueryDto dto,String loginUserId) {
        if (dto == null){
            log.error("缺少查询条件");
            return new PageInfo<>();
        }
        UapUserPo user = uapServiceApi.getUser(loginUserId);
        UapOrgPo userOrgpo = uapServiceApi.getUserOrg(user.getLoginName());

        String district = userOrgpo.getDistrict();
        if (StrUtil.isNotBlank(district)) {

            dto.setDistrictCode(userOrgpo.getDistrictCode());
        }

        String city = userOrgpo.getCity();
        if (StrUtil.isNotBlank(city)) {
            dto.setCityCode(userOrgpo.getCityCode());
        }

        String province = userOrgpo.getProvince();
        if (StrUtil.isNotBlank(province)) {
            dto.setProvinceCode(userOrgpo.getProvinceCode());
        }
        LambdaQueryWrapper<ExchangeInfoTemplate> wrapper = new LambdaQueryWrapper<>();
        String templateId = dto.getTemplateId();
        if (StrUtil.isNotBlank(templateId)){
            wrapper.like(ExchangeInfoTemplate::getId,templateId);
        }
        String templateTitle = dto.getTemplateTitle();
        if (StrUtil.isNotBlank(templateTitle)){
            wrapper.like(ExchangeInfoTemplate::getTemplateTitle,templateTitle);
        }
        Integer templateStatus = dto.getTemplateStatus();
        if (templateStatus != null){
            wrapper.eq(ExchangeInfoTemplate::getTemplateStatus,templateStatus);
        }

        String provinceCode = dto.getProvinceCode();
        if (StrUtil.isNotBlank(provinceCode)){
            wrapper.eq(ExchangeInfoTemplate::getProvinceCode,provinceCode);
        }
        String cityCode = dto.getCityCode();
        if (StrUtil.isNotBlank(cityCode)){
            wrapper.eq(ExchangeInfoTemplate::getCityCode,cityCode);
        }
        String districtCode = dto.getDistrictCode();
        if (StrUtil.isNotBlank(districtCode)){
            wrapper.eq(ExchangeInfoTemplate::getDistrictCode,districtCode);
        }


        Date startDate = dto.getStartDate();
        if (startDate != null){
            wrapper.ge(ExchangeInfoTemplate::getCreateTime,startDate);
        }
        Date endDate = dto.getEndDate();
        if (endDate != null){
            wrapper.le(ExchangeInfoTemplate::getCreateTime,endDate);
        }
        wrapper.orderByDesc(ExchangeInfoTemplate::getCreateTime);
        List<ExchangeInfoTemplate> list = this.list(wrapper);
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return  new PageInfo<>(list);
    }


    @Override
    public List<ExchangeInfoTemplate> queryTemplateSelectList(String loginUserId) {
        UapUserPo user = uapServiceApi.getUser(loginUserId);
        UapOrgPo userOrgpo = uapServiceApi.getUserOrg(user.getLoginName());
        LambdaQueryWrapper<ExchangeInfoTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExchangeInfoTemplate::getTemplateStatus,1);

        String district = userOrgpo.getDistrict();
        if (StrUtil.isNotBlank(district)) {

            wrapper.eq(ExchangeInfoTemplate::getDistrictCode,userOrgpo.getDistrictCode());
        }

        String city = userOrgpo.getCity();
        if (StrUtil.isNotBlank(city)) {
            wrapper.eq(ExchangeInfoTemplate::getCityCode,userOrgpo.getCityCode());
        }

        String province = userOrgpo.getProvince();
        if (StrUtil.isNotBlank(province)) {
            wrapper.eq(ExchangeInfoTemplate::getProvinceCode,userOrgpo.getProvinceCode());
        }

        return this.list(wrapper);
    }
}
