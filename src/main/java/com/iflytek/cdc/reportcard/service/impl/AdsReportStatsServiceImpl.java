package com.iflytek.cdc.reportcard.service.impl;

import com.iflytek.cdc.reportcard.dto.ReportUploadStatsQueryDTO;
import com.iflytek.cdc.reportcard.mapper.pg.ReportUploadStatsMapper;
import com.iflytek.cdc.reportcard.service.AdsReportStatsService;
import com.iflytek.cdc.reportcard.vo.stats.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class AdsReportStatsServiceImpl implements AdsReportStatsService {

    private ReportUploadStatsMapper reportUploadStatsMapper;

    @Autowired
    public void setReportUploadStatsMapper(ReportUploadStatsMapper reportUploadStatsMapper) {
        this.reportUploadStatsMapper = reportUploadStatsMapper;
    }

    @Override
    public ReportUploadSummaryVO notifiableInfectTypeStat(ReportUploadStatsQueryDTO dto) {
        dto.dealDate(dto);
        return this.reportUploadStatsMapper.notifiableInfectTypeStat(dto);
    }

    @Override
    public List<ReportUploadStatsVO> groupInfectNewCntTop10(ReportUploadStatsQueryDTO dto) {
        dto.dealDate(dto);
        return this.reportUploadStatsMapper.groupInfectNewCntTop10(dto);
    }

    @Override
    public List<ReportUploadStatsVO> groupInfectNewCntDeclineTop10(ReportUploadStatsQueryDTO dto) {
        dto.dealDate(dto);
        return this.reportUploadStatsMapper.groupInfectNewCntDeclineTop10(dto);
    }

    @Override
    public List<ReportUploadStatsDateDimVO> groupDateDimCnt(ReportUploadStatsQueryDTO dto) {
        dto.dealDate(dto);
        return this.reportUploadStatsMapper.groupDateDimCnt(dto);
    }

    @Override
    public List<ReportUploadStatsAreaVO> groupAreaCnt(ReportUploadStatsQueryDTO dto) {
        return this.reportUploadStatsMapper.groupAreaCnt(dto);
    }

    @Override
    public List<ReportUploadStatsAgeVO> groupAgeCnt(ReportUploadStatsQueryDTO dto) {
        return this.reportUploadStatsMapper.groupAgeCnt(dto);
    }

    @Override
    public List<ReportUploadStatsGenderVO> groupGenderCnt(ReportUploadStatsQueryDTO dto) {
        return this.reportUploadStatsMapper.groupGenderCnt(dto);
    }

    @Override
    public List<ReportUploadStatsJobVO> groupJobCnt(ReportUploadStatsQueryDTO dto) {
        return this.reportUploadStatsMapper.groupJobCnt(dto);
    }

    @Override
    public List<ReportUploadStatsVO> groupInfectCnt(ReportUploadStatsQueryDTO dto) {
        dto.dealDate(dto);
        List<ReportUploadStatsVO> reportUploadStatsVOS = this.reportUploadStatsMapper.groupInfectCnt(dto);
        ReportUploadStatsVO.sortTreeBy(reportUploadStatsVOS, ReportUploadStatsVO::getOrderFlag);
        return reportUploadStatsVOS;
    }

    @Override
    public List<ReportUploadStatsVO> groupInfectTransmitTypeCnt(ReportUploadStatsQueryDTO dto) {
        dto.dealDate(dto);
        return this.reportUploadStatsMapper.groupInfectTransmitTypeCnt(dto);
    }

    @Override
    public List<ReportUploadStatsVO> groupInfectDiseaseCnt(ReportUploadStatsQueryDTO dto) {
        return this.reportUploadStatsMapper.groupInfectDiseaseCnt(dto);
    }

    @Override
    public List<ReportUploadStatsVO> groupInfectAreaCnt(ReportUploadStatsQueryDTO dto) {
        return this.reportUploadStatsMapper.groupInfectAreaCnt(dto);
    }

    @Override
    public List<ReportUploadStatsVO> groupInfectAreaOrgCnt(ReportUploadStatsQueryDTO dto) {
        return this.reportUploadStatsMapper.groupInfectAreaOrgCnt(dto);
    }
}
