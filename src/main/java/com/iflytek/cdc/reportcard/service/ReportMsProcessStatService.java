package com.iflytek.cdc.reportcard.service;

import com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.EpidemicDistributionRespVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.ProcessAwarenessRespVO;
import com.iflytek.cdc.province.model.dto.AdsMsProcessReqDTO;
import org.springframework.http.ResponseEntity;

import java.util.Collection;
import java.util.List;

/**
 * 报卡督导疾病专题分析服务接口
 */
public interface ReportMsProcessStatService {

    // 流行强度描述-时间分布
    List<AdsMsProcessRespVO> monthTimeTrend(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    ResponseEntity<byte[]> monthTimeTrendExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    List<AdsMsProcessRespVO> quarterTimeTrend(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    ResponseEntity<byte[]> quarterTimeTrendExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    List<AdsMsProcessRespVO> yearTimeTrend(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    ResponseEntity<byte[]> yearTimeTrendExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    List<AdsMsProcessRespVO> halfYearTimeTrend(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    ResponseEntity<byte[]> halfYearTimeTrendExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    // 流行强度描述-地区分布
    List<AdsMsProcessRespVO> groupArea(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    List<AdsMsProcessRespVO> groupAreaDetail(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    ResponseEntity<byte[]> groupAreaExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    ResponseEntity<byte[]> groupAreaDetailExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    List<AdsMsProcessRespVO> groupAddrType(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    ResponseEntity<byte[]> groupAddrTypeExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    // 流行强度描述-人群分布
    List<AdsMsProcessRespVO> groupAge(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    ResponseEntity<byte[]> groupAgeExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    List<AdsMsProcessRespVO> groupSex(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    ResponseEntity<byte[]> groupSexExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    List<AdsMsProcessRespVO> groupJob(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    ResponseEntity<byte[]> groupJobExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    // 流行动态变化
    List<AdsMsProcessRespVO> overall(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    String overallAwareness(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    List<ProcessAwarenessRespVO> loadAwarenessResult(AdsMsProcessReqDTO reqDTO, String loginUserId);
    Collection<List<AdsMsProcessRespVO>> areaChange(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    Collection<List<AdsMsProcessRespVO>> sexChange(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    Collection<List<AdsMsProcessRespVO>> ageChange(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    List<AdsMsProcessRespVO> qOQTrendChart(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    List<AdsMsProcessRespVO> areaMedDateCount(AdsMsProcessReqDTO reqDTO);

    // 病情分布分析
    List<AdsMsProcessRespVO> groupIdentifyClass(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    List<AdsMsProcessRespVO> groupOutcomeStatus(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    List<AdsMsProcessRespVO> groupSymptomFirst(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    List<AdsMsProcessRespVO> groupHistoryBefore(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    EpidemicDistributionRespVO groupPathogenResNominal(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);

    // 疾病分布分析
    List<AdsMsProcessRespVO> groupDiseaseName(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    ResponseEntity<byte[]> groupDiseaseNameExport(AdsMsProcessReqDTO reqDTO, String loginUserId, String loginUserName);
    List<AdsMsProcessRespVO> getDiseaseDistributionBox(String loginUserName);


    /**
     * 时间研判模型
     */
    List<AdsMsProcessRespVO> timeJudgeModelTrend(AdsMsProcessReqDTO reqDTO, String loginUserName);


}
