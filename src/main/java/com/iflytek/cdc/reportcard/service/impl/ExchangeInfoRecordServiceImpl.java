package com.iflytek.cdc.reportcard.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.edr.apiService.UapServiceApi;
import com.iflytek.cdc.edr.entity.uap.UapOrgPo;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import com.iflytek.cdc.reportcard.dto.ExchangeInfoRecordQueryDto;
import com.iflytek.cdc.reportcard.dto.ExchangeUserData;
import com.iflytek.cdc.reportcard.entity.bu.ExchangeInfoRecord;
import com.iflytek.cdc.reportcard.mapper.bu.ExchangeInfoRecordMapper;
import com.iflytek.cdc.reportcard.service.ExchangeInfoRecordService;
import com.iflytek.cdc.reportcard.service.ExchangeInfoTemplateService;
import com.iflytek.cdc.reportcard.vo.ExchangeInfoRecordVO;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.medicalboot.core.id.BatchUidService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class ExchangeInfoRecordServiceImpl extends ServiceImpl<ExchangeInfoRecordMapper, ExchangeInfoRecord> implements ExchangeInfoRecordService {

    private UapServiceApi uapServiceApi;

    @Autowired
    public void setUapServiceApi(UapServiceApi uapServiceApi) {
        this.uapServiceApi = uapServiceApi;
    }

    private BatchUidService batchUidService;

    @Autowired
    public void setBatchUidService(BatchUidService batchUidService) {
        this.batchUidService = batchUidService;
    }


    private ExchangeInfoTemplateService exchangeInfoTemplateService;

    @Autowired
    public void setExchangeInfoTemplateService(ExchangeInfoTemplateService exchangeInfoTemplateService) {
        this.exchangeInfoTemplateService = exchangeInfoTemplateService;
    }

    private ExchangeInfoRecordMapper exchangeInfoRecordMapper;

    @Autowired
    public void setExchangeInfoRecordMapper(ExchangeInfoRecordMapper exchangeInfoRecordMapper) {
        this.exchangeInfoRecordMapper = exchangeInfoRecordMapper;
    }

    @Override
    public ExchangeInfoRecord createRecord(ExchangeInfoRecord record, String loginUserId) {
        if (record == null){
            log.error("缺少信息交换记录对象");
            return new ExchangeInfoRecord();
        }
        UapUserPo user = uapServiceApi.getUser(loginUserId);
        String name = user.getName();


        record.setId(String.valueOf(batchUidService.getUid(ExchangeInfoRecord.TB_NAME)));
        record.setCreatorId(user.getId());
        record.setCreator(user.getName());
        record.setCreateTime(new Date());
        record.setUpdatorId(user.getId());
        record.setUpdator(user.getName());
        record.setUpdateTime(new Date());

        UapOrgPo userOrgpo = uapServiceApi.getUserOrg(user.getLoginName());
        String district = userOrgpo.getDistrict();
        if (StrUtil.isNotBlank(district)) {
            record.setDistrictName(district);
            record.setDistrictCode(userOrgpo.getDistrictCode());
        }

        String city = userOrgpo.getCity();
        if (StrUtil.isNotBlank(city)) {
            record.setCityName(city);
            record.setCityCode(userOrgpo.getCityCode());
        }

        String province = userOrgpo.getProvince();
        if (StrUtil.isNotBlank(province)) {
            record.setProvinceName(province);
            record.setProvinceCode(userOrgpo.getProvinceCode());
        }

        ExchangeUserData sender = new ExchangeUserData();
        sender.setUserId(loginUserId);
        sender.setUserName(name);

        record.setSender(sender);

        // 随机出现 0 和 1
        record.setExchangeStatus((int) (Math.random() * 2));
        try {
            this.save(record);
            return record;
        } catch (Exception e) {
            throw new MedicalBusinessException("新增交换信息模板失败");
        }
    }

    @Override
    public ExchangeInfoRecordVO queryRecordById(String exchangeId) {
        if (StrUtil.isBlank(exchangeId)){
            log.error("缺少记录id");
            return new ExchangeInfoRecordVO();
        }
        ExchangeInfoRecordVO exchangeInfoRecordVO = exchangeInfoRecordMapper.queryRecordById(exchangeId);
        return exchangeInfoRecordVO;
    }

    @Override
    public PageInfo<ExchangeInfoRecordVO> queryRecordList(ExchangeInfoRecordQueryDto dto, String loginUserId) {
        if (dto == null){
            log.error("缺少查询参数");
            return new PageInfo<>();
        }
        UapUserPo user = uapServiceApi.getUser(loginUserId);
        UapOrgPo userOrgpo = uapServiceApi.getUserOrg(user.getLoginName());
        String district = userOrgpo.getDistrict();
        if (StrUtil.isNotBlank(district)) {
            dto.setDistrictCode(userOrgpo.getDistrictCode());
        }
        String city = userOrgpo.getCity();
        if (StrUtil.isNotBlank(city)) {
            dto.setCityCode(userOrgpo.getCityCode());
        }
        String province = userOrgpo.getProvince();
        if (StrUtil.isNotBlank(province)) {
            dto.setProvinceCode(userOrgpo.getProvinceCode());
        }
        List<ExchangeInfoRecordVO> list = exchangeInfoRecordMapper.queryRecordList(dto);
        PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        return  new PageInfo<>(list);
    }
}
