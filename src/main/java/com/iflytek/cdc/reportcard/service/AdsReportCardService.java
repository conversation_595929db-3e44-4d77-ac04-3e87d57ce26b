package com.iflytek.cdc.reportcard.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.reportcard.dto.ReportCardOperateDto;
import com.iflytek.cdc.reportcard.dto.ReportUploadQueryDTO;
import com.iflytek.cdc.reportcard.entity.ads.ReportUploadRecord;
import com.iflytek.cdc.reportcard.vo.ReportCardArchiveVO;
import com.iflytek.cdc.reportcard.vo.ReportUploadRecordVO;

import java.util.HashMap;
import java.util.List;

public interface AdsReportCardService {
    PageInfo<ReportUploadRecordVO> dataPageInfo(ReportUploadQueryDTO queryDTO);

    List<ReportUploadRecord> queryByIds(List<String> idList);

    ReportUploadRecordVO findByReportCardId(String reportCardId);

    ReportUploadRecord findById(String id);

    ReportCardArchiveVO queryArchiveById(String reportCardId);

    ReportCardArchiveVO queryArchiveByCertNum(String validCertNumber);

    Boolean ReportCardOperateDto(ReportCardOperateDto dto);

    Integer tobeReviewedCard(ReportUploadQueryDTO queryDTO);

    PageInfo<HashMap<String, String>> reportingUnitInquiry(ReportUploadQueryDTO queryDTO);

    PageInfo<ReportUploadRecordVO> dataPageInfoByGX(ReportUploadQueryDTO queryDTO);
}
