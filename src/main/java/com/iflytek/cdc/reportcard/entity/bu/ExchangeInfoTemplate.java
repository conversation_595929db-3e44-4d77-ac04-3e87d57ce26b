package com.iflytek.cdc.reportcard.entity.bu;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ExchangeInfoTemplate对象", description="交换信息模板表")
@TableName(value = "tb_cdcmr_exchange_info_template",schema = "app")
public class ExchangeInfoTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final String TB_NAME="tb_cdcmr_exchange_info_template";

    private String id;

    /**
     * 模板标题
     */
    private String templateTitle;

    /**
     * 文件id
     */
    private String attachmentId;

    /**
     * 模板状态 0: 停用 1: 启用
     */
    private Integer templateStatus;

    /**
     * 模板描述
     */
    private String templateDescription;

    private String provinceCode;

    private String provinceName;

    private String cityCode;

    private String cityName;

    private String districtCode;

    private String districtName;

    private String creatorId;

    private String creator;

    private Date createTime;

    private String updatorId;

    private String updator;

    private Date updateTime;
}
