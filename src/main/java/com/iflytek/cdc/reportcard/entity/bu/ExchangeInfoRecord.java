package com.iflytek.cdc.reportcard.entity.bu;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.iflytek.cdc.reportcard.dto.ExchangeOrgData;
import com.iflytek.cdc.reportcard.dto.ExchangeUserData;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ExchangeInfoRecord对象", description="交换信息记录表")
@TableName(value = "tb_cdcmr_exchange_info_record",schema = "app")
public class ExchangeInfoRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final String TB_NAME="tb_cdcmr_exchange_info_record";

    private String id;

    /**
     * 交换标题
     */
    private String exchangeTitle;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 交换状态 0: 未交换 1: 已交换
     */
    private Integer exchangeStatus;

    /**
     * 机构信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ExchangeOrgData orgInfo;

    /**
     * 接收方
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ExchangeUserData recipient;

    /**
     * 发送方
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ExchangeUserData sender;

    private String provinceCode;

    private String provinceName;

    private String cityCode;

    private String cityName;

    private String districtCode;

    private String districtName;

    private String creatorId;

    private String creator;

    private Date createTime;

    private String updatorId;

    private String updator;

    private Date updateTime;







    private String exchangeDescription;
}
