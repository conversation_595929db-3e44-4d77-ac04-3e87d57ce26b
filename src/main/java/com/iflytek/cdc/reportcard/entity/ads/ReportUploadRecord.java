package com.iflytek.cdc.reportcard.entity.ads;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 上传报告卡记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="ReportUploadRecord对象", description="上传报告卡记录")
@TableName(value = "ads_report_upload_record",schema = "ads")
public class ReportUploadRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    public static final String TB_NAME="ads_report_upload_record";

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "附件id")
    private String attachmentId;

    @ApiModelProperty(value = "卡片id")
    private String reportCardId;

    @ApiModelProperty(value = "卡片状态")
    private String status;

    @ApiModelProperty(value = "患者姓名")
    private String name;

    @ApiModelProperty(value = "患儿家长姓名")
    private String parentName;

    @ApiModelProperty(value = "有效证件类型")
    private String validCertType;

    @ApiModelProperty(value = "有效证件号")
    private String validCertNumber;

    @ApiModelProperty(value = "患者工作单位")
    private String company;

    private String phone;

    @ApiModelProperty(value = "病人属于")
    private String attribution;

    private String addressCode;

    @ApiModelProperty(value = "现住详细地址")
    private String addressName;

    @ApiModelProperty(value = "人群分类")
    private String humanCategory;

    @ApiModelProperty(value = "病例分类")
    private String casesCategory;

    private String casesCategory2;

    @ApiModelProperty(value = "发病日期")
    private Date onsetDate;

    @ApiModelProperty(value = "诊断时间")
    private Date diagnoseTime;

    @ApiModelProperty(value = "死亡日期")
    private Date deathDate;

    @ApiModelProperty(value = "疾病名称")
    private String diseaseName;

    private String diseaseCode;

    @ApiModelProperty(value = "订正前病种")
    private String revisedPreviousDisease;

    @ApiModelProperty(value = "订正前诊断时间")
    private Date revisedPreviousDiagnoseTime;

    @ApiModelProperty(value = "订正前终审时间")
    private Date revisedPreviousCheckTime;

    @ApiModelProperty(value = "填卡医生")
    private String fillDoctor;

    @ApiModelProperty(value = "医生填卡日期")
    private Date fillDate;

    @ApiModelProperty(value = "报告单位地区编码")
    private String unitCode;

    @ApiModelProperty(value = "报告单位")
    private String unitName;

    @ApiModelProperty(value = "单位类型")
    private String unitType;

    @ApiModelProperty(value = "报告卡录入时间")
    private Date recordTime;

    @ApiModelProperty(value = "录卡用户")
    private String recordUser;

    @ApiModelProperty(value = "录卡用户所属单位")
    private String recordUserCompany;

    @ApiModelProperty(value = "县区审核时间")
    private Date districtCheckTime;

    private Date cityCheckTime;

    private Date provinceCheckTime;

    @ApiModelProperty(value = "审核状态")
    private String checkStatus;

    @ApiModelProperty(value = "订正报告时间")
    private Date revisedReportTime;

    @ApiModelProperty(value = "订正终审时间")
    private Date revisedFinalCheckTime;

    @ApiModelProperty(value = "终审死亡时间")
    private Date finalCheckDeathTime;

    @ApiModelProperty(value = "订正用户")
    private String revisedUser;

    @ApiModelProperty(value = "订正用户所属单位")
    private String revisedUserCompany;

    private Date deleteTime;

    private String deleteUser;

    private String deleteUserCompany;

    private String deleteReason;

    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    private String creatorId;

    private String creator;

    @ApiModelProperty(value = "主索引id")
    private String globalPersonId;

    @ApiModelProperty(value = "卡片编号")
    private String reportCardCode;

    @ApiModelProperty(value = "性别")
    private String sexDesc;

    @ApiModelProperty(value = "出生日期")
    private Date birthday;

    @ApiModelProperty(value = "年龄")
    private String age;

    @ApiModelProperty(value = "年龄单位")
    private String ageUnit;

    private String provinceCode;

    private String provinceName;

    private String cityCode;

    private String cityName;

    private String districtCode;

    private String districtName;

    @ApiModelProperty(value = "报告类别")
    private String reportClass;
    
    @TableField(exist = false)
    @ApiModelProperty(value = "重卡id")
    private String repeatId;

    @TableField(exist = false)
    @ApiModelProperty(value = "重卡处理状态 0-未处理；1-已处理")
    private Integer repeatDisposeStatus;

    @TableField(exist = false)
    @ApiModelProperty(value = "处理删除 0未删除 1删除")
    private Integer disposeDelete;

    @ApiModelProperty(value = "加工字段：报告单位地址-省编码")
    private String reportOrgAddrProvinceCode;

    @ApiModelProperty(value = "加工字段：报告单位地址-省名称")
    private String reportOrgAddrProvince;

    @ApiModelProperty(value = "加工字段：报告单位地址-市编码")
    private String reportOrgAddrCityCode;

    @ApiModelProperty(value = "加工字段：报告单位地址-市名称")
    private String reportOrgAddrCity;

    @ApiModelProperty(value = "加工字段：报告单位地址-区编码")
    private String reportOrgAddrDistrictCode;

    @ApiModelProperty(value = "加工字段：报告单位地址-区名称")
    private String reportOrgAddrDistrict;

    @ApiModelProperty(value = "加工字段：EDR生命周期ID")
    private String lifeId;

    @ApiModelProperty(value = "加工字段：EDR档案ID") 
    private String archiveId;

    @ApiModelProperty(value = "加工字段：年龄分布") 
    private String ageGroup;

}
