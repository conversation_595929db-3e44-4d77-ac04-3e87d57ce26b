package com.iflytek.cdc.reportcard.controller.bu;

import com.iflytek.cdc.edr.annotation.OperationLogAnnotation;
import com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.EpidemicDistributionRespVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.ProcessAwarenessRespVO;
import com.iflytek.cdc.province.model.dto.AdsMsProcessReqDTO;
import com.iflytek.cdc.reportcard.service.ReportMsProcessStatService;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 报卡督导疾病专题分析 按照业务库的数据库表进行统计
 * 后续有改动 按照实际修改
 * 该类可以与MsProcessDiseaseAnalysisController进行整合, 减少重复
 */
@RestController
@Api(tags = "报卡督导疾病专题分析")
public class ReportMsProcessDiseaseAnalysisController {

    @Resource
    private ReportMsProcessStatService reportMsProcessStatService;

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/monthTimeTrend")
    @ApiOperation("流行强度描述-时间分布-月度")
    public List<AdsMsProcessRespVO> monthTimeTrend(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                   @RequestParam String loginUserId,
                                                   @RequestParam String loginUserName) {
        return reportMsProcessStatService.monthTimeTrend(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/monthTimeTrendExport")
    @ApiOperation("流行强度描述-时间分布-月度-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> monthTimeTrendExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                       @RequestParam String loginUserId,
                                                       @RequestParam String loginUserName) {
        return reportMsProcessStatService.monthTimeTrendExport(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/quarterTimeTrend")
    @ApiOperation("流行强度描述-时间分布-季度")
    public List<AdsMsProcessRespVO> quarterTimeTrend(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                     @RequestParam String loginUserId,
                                                     @RequestParam String loginUserName) {
        return reportMsProcessStatService.quarterTimeTrend(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/quarterTimeTrendExport")
    @ApiOperation("流行强度描述-时间分布-季度-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> quarterTimeTrendExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                         @RequestParam String loginUserId,
                                                         @RequestParam String loginUserName) {
        return reportMsProcessStatService.quarterTimeTrendExport(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/yearTimeTrend")
    @ApiOperation("流行强度描述-时间分布-年度")
    public List<AdsMsProcessRespVO> yearTimeTrend(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                  @RequestParam String loginUserId,
                                                  @RequestParam String loginUserName) {
        return reportMsProcessStatService.yearTimeTrend(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/yearTimeTrendExport")
    @ApiOperation("流行强度描述-时间分布-年度-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> yearTimeTrendExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                      @RequestParam String loginUserId,
                                                      @RequestParam String loginUserName) {
        return reportMsProcessStatService.yearTimeTrendExport(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/halfYearTimeTrend")
    @ApiOperation("流行强度描述-时间分布-半年")
    public List<AdsMsProcessRespVO> halfYearTimeTrend(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                  @RequestParam String loginUserId,
                                                  @RequestParam String loginUserName) {
        return reportMsProcessStatService.halfYearTimeTrend(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/halfYearTimeTrendExport")
    @ApiOperation("流行强度描述-时间分布-半年-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> halfYearTimeTrendExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                      @RequestParam String loginUserId,
                                                      @RequestParam String loginUserName) {
        return reportMsProcessStatService.halfYearTimeTrendExport(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/groupArea")
    @ApiOperation("流行强度描述-地区分布-不同行政区疾病特征")
    public List<AdsMsProcessRespVO> groupArea(@RequestBody AdsMsProcessReqDTO reqDTO,
                                              @RequestParam String loginUserId,
                                              @RequestParam String loginUserName) {
        return reportMsProcessStatService.groupArea(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/groupAreaDetail")
    @ApiOperation("流行强度描述-地区分布-地区分布（下钻到区县）")
    public List<AdsMsProcessRespVO> processStatGroupAreaDetail(@RequestBody AdsMsProcessReqDTO queryParam,
                                                               @RequestParam String loginUserId,
                                                               @RequestParam String loginUserName){
        return reportMsProcessStatService.groupAreaDetail(queryParam, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/groupAreaExport")
    @ApiOperation("流行强度描述-地区分布-不同行政区疾病特征-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> groupAreaExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                  @RequestParam String loginUserId,
                                                  @RequestParam String loginUserName) {
        return reportMsProcessStatService.groupAreaExport(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/groupAreaDetailExport")
    @ApiOperation("流行强度描述-地区分布-不同行政区疾病特征-导出（下钻到区县）")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> groupAreaDetailExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                  @RequestParam String loginUserId,
                                                  @RequestParam String loginUserName) {
        return reportMsProcessStatService.groupAreaDetailExport(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/groupAddrType")
    @ApiOperation("流行强度描述-地区分布-城乡分布特征")
    public List<AdsMsProcessRespVO> groupAddrType(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                  @RequestParam String loginUserId,
                                                  @RequestParam String loginUserName) {
        // TODO: 城乡分布 - 当前无法计算
        return new ArrayList<>();
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/groupAddrTypeExport")
    @ApiOperation("流行强度描述-地区分布-城乡分布特征-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> groupAddrTypeExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                      @RequestParam String loginUserId,
                                                      @RequestParam String loginUserName) {
        // TODO: 城乡分布 - 当前无法计算
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/groupAge")
    @ApiOperation("流行强度描述-人群分布-不同年龄段疾病特征")
    public List<AdsMsProcessRespVO> groupAge(@RequestBody AdsMsProcessReqDTO reqDTO,
                                             @RequestParam String loginUserId,
                                             @RequestParam String loginUserName) {
        return reportMsProcessStatService.groupAge(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/groupAgeExport")
    @ApiOperation("流行强度描述-人群分布-不同年龄段疾病特征-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> groupAgeExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                 @RequestParam String loginUserId,
                                                 @RequestParam String loginUserName) {
        return reportMsProcessStatService.groupAgeExport(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/groupSex")
    @ApiOperation("流行强度描述-人群分布-不同性别疾病特征")
    public List<AdsMsProcessRespVO> groupSex(@RequestBody AdsMsProcessReqDTO reqDTO,
                                      @RequestParam String loginUserId,
                                      @RequestParam String loginUserName) {
        return reportMsProcessStatService.groupSex(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/groupSexExport")
    @ApiOperation("流行强度描述-人群分布-不同性别疾病特征-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> groupSexExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                 @RequestParam String loginUserId,
                                                 @RequestParam String loginUserName) {
        return reportMsProcessStatService.groupSexExport(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/groupJob")
    @ApiOperation("流行强度描述-人群分布-不同职业疾病特征")
    public List<AdsMsProcessRespVO> groupJob(@RequestBody AdsMsProcessReqDTO reqDTO,
                                      @RequestParam String loginUserId,
                                      @RequestParam String loginUserName) {
        return reportMsProcessStatService.groupJob(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/intensityDescription/groupJobExport")
    @ApiOperation("流行强度描述-人群分布-不同职业疾病特征-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> groupJobExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                 @RequestParam String loginUserId,
                                                 @RequestParam String loginUserName) {
        return reportMsProcessStatService.groupJobExport(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/change/overall")
    @ApiOperation("流行动态变化-总体变化趋势")
    public List<AdsMsProcessRespVO> overall(@RequestBody AdsMsProcessReqDTO reqDTO,
                                            @RequestParam String loginUserId,
                                            @RequestParam String loginUserName) {
        return reportMsProcessStatService.overall(reqDTO, loginUserId, loginUserName);
    }

    /**
     * 流行动态变化-态势感知推演（实时再生数，无缓存）
     */
    @PostMapping("/pt/{version}/zt/reportcard/change/overall/awareness")
    @ApiOperation("流行动态变化-态势感知推演（实时再生数，无缓存）")
    public String overallAwareness(@RequestBody AdsMsProcessReqDTO reqDTO,
                                   @RequestParam String loginUserId,
                                   @RequestParam String loginUserName) {
        return reportMsProcessStatService.overallAwareness(reqDTO, loginUserId, loginUserName);
    }

    /**
     * 流行动态变化-获取态势感知结果（实时再生数）
     */
    @PostMapping("/pt/{version}/zt/reportcard/change/overall/loadAwarenessResult")
    @ApiOperation("流行动态变化-获取态势感知结果（实时再生数）")
    public List<ProcessAwarenessRespVO> loadAwarenessResult(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                      @RequestParam String loginUserId) {
        if (reqDTO.getAwarenessTaskId() == null || reqDTO.getAwarenessTaskId().isEmpty()) {
            throw new MedicalBusinessException("任务ID不可为空");
        }
        return reportMsProcessStatService.loadAwarenessResult(reqDTO, loginUserId);
    }

    @PostMapping("/pt/{version}/zt/reportcard/change/area")
    @ApiOperation("流行动态变化-分地区变化趋势")
    public Collection<List<AdsMsProcessRespVO>> area(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                     @RequestParam String loginUserId,
                                                     @RequestParam String loginUserName) {
        return reportMsProcessStatService.areaChange(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/change/sex")
    @ApiOperation("流行动态变化-分性别变化趋势")
    public Collection<List<AdsMsProcessRespVO>> sex(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                    @RequestParam String loginUserId,
                                                    @RequestParam String loginUserName) {
        return reportMsProcessStatService.sexChange(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/change/age")
    @ApiOperation("流行动态变化-分年龄变化趋势")
    public Collection<List<AdsMsProcessRespVO>> age(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                    @RequestParam String loginUserId,
                                                    @RequestParam String loginUserName) {
        return reportMsProcessStatService.ageChange(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/change/qOQTrendChart")
    @ApiOperation("流行动态变化-环比趋势图")
    public List<AdsMsProcessRespVO> qOQTrendChart(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                    @RequestParam String loginUserId,
                                                    @RequestParam String loginUserName) {
        return reportMsProcessStatService.qOQTrendChart(reqDTO, loginUserId, loginUserName);
    }

    /**
     * 各个区域的病例数相关统计
     */
    @PostMapping("/pt/{version}/zt/reportcard/change/areaMedDateCount")
    @ApiOperation("各个区域的病例数相关统计")
    public List<AdsMsProcessRespVO> areaMedDateCount(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserId) {
        return reportMsProcessStatService.areaMedDateCount(reqDTO);
    }

    @PostMapping("/pt/{version}/zt/reportcard/epidemicDistribution/identifyClass")
    @ApiOperation("病情分布分析-诊断类型（确诊类型）")
    public List<AdsMsProcessRespVO> groupIdentifyClass(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserId,
                                                       @RequestParam String loginUserName) {

        return reportMsProcessStatService.groupIdentifyClass(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/epidemicDistribution/outcomeStatus")
    @ApiOperation("病情分布分析-病情转归（转归类型）")
    public List<AdsMsProcessRespVO> groupOutcomeStatus(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserId,
                                                       @RequestParam String loginUserName) {

        return reportMsProcessStatService.groupOutcomeStatus(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/epidemicDistribution/symptomFirst")
    @ApiOperation("病情分布分析-首发症状")
    public List<AdsMsProcessRespVO> groupSymptomFirst(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserId,
                                                      @RequestParam String loginUserName) {

        return reportMsProcessStatService.groupSymptomFirst(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/epidemicDistribution/historyBefore")
    @ApiOperation("病情分布分析-既往疾病")
    public List<AdsMsProcessRespVO> groupHistoryBefore(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserId,
                                                       @RequestParam String loginUserName) {

        return reportMsProcessStatService.groupHistoryBefore(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/epidemicDistribution/pathogenResNominal")
    @ApiOperation("病情分布分析-病原阳性分析（病原检测定性结果）")
    public EpidemicDistributionRespVO groupPathogenResNominal(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserId,
                                                              @RequestParam String loginUserName) {

        return reportMsProcessStatService.groupPathogenResNominal(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/diseaseDistribution")
    @ApiOperation("不同疾病占比分析（该接口不接受疾病过滤条件）")
    public List<AdsMsProcessRespVO> groupDiseaseName(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserId,
                                                     @RequestParam String loginUserName) {
        // 清空疾病过滤条件
        reqDTO.setInfectCode(null);
        return reportMsProcessStatService.groupDiseaseName(reqDTO, loginUserId, loginUserName);
    }

    @PostMapping("/pt/{version}/zt/reportcard/diseaseDistribution/export")
    @ApiOperation("不同疾病占比分析（该接口不接受疾病过滤条件）-导出")
    @OperationLogAnnotation
    public ResponseEntity<byte[]> groupDiseaseNameExport(@RequestBody AdsMsProcessReqDTO reqDTO,
                                                         @RequestParam String loginUserId,
                                                         @RequestParam String loginUserName) {
        reqDTO.setInfectCode(null);
        return reportMsProcessStatService.groupDiseaseNameExport(reqDTO, loginUserId, loginUserName);
    }


    @PostMapping("/pt/{version}/zt/reportcard/timeJudgeModelTrend")
    @ApiOperation("时间研判模型-折线图")
    public List<AdsMsProcessRespVO> timeJudgeModelTrend(@RequestBody AdsMsProcessReqDTO reqDTO, @RequestParam String loginUserName) {

        return reportMsProcessStatService.timeJudgeModelTrend(reqDTO, loginUserName);
    }



}
