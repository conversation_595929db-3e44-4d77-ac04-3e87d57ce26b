package com.iflytek.cdc.reportcard.controller.ads;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.reportcard.dto.ReportCardOperateDto;
import com.iflytek.cdc.reportcard.dto.ReportUploadQueryDTO;
import com.iflytek.cdc.reportcard.entity.ads.ReportUploadRecord;
import com.iflytek.cdc.reportcard.service.AdsReportCardService;
import com.iflytek.cdc.reportcard.vo.ReportCardArchiveVO;
import com.iflytek.cdc.reportcard.vo.ReportUploadRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 传染病报告卡 数仓查询
 */
@RestController
@RequestMapping("/pt/{version}/ads/reportUpload/report")
@Api(tags = "传染病报告卡-数仓查询")
public class AdsReportCardController {

    private AdsReportCardService adsReportCardService;

    @Autowired
    public void setAdsReportCardService(AdsReportCardService adsReportCardService) {
        this.adsReportCardService = adsReportCardService;
    }

    @PostMapping("/dataPageInfo")
    @ApiOperation("获取报告卡列表")
    public PageInfo<ReportUploadRecordVO> dataPageInfo(@RequestBody ReportUploadQueryDTO queryDTO){
        return adsReportCardService.dataPageInfo(queryDTO);
    }

    @PostMapping("/dataPageInfoByGX")
    @ApiOperation("获取报告卡列表")
    public PageInfo<ReportUploadRecordVO> dataPageInfoByGX(@RequestBody ReportUploadQueryDTO queryDTO){
        return adsReportCardService.dataPageInfoByGX(queryDTO);
    }

    @PostMapping("/tobeReviewedCard")
    @ApiOperation("待审核卡片数量")
    public Integer tobeReviewedCard(@RequestBody ReportUploadQueryDTO queryDTO) {
        return adsReportCardService.tobeReviewedCard(queryDTO);
    }

    @PostMapping("/reportingUnitInquiry")
    @ApiOperation("报告卡报告单位查询")
    public PageInfo<HashMap<String, String>> reportingUnitInquiry(@RequestBody ReportUploadQueryDTO queryDTO) {
        return adsReportCardService.reportingUnitInquiry(queryDTO);
    }

    @PostMapping("/queryByIds")
    @ApiOperation("获取报告卡列表根据Id集合")
    public List<ReportUploadRecord> queryByIds( @RequestBody List<String> idList){
        return adsReportCardService.queryByIds(idList);
    }

    @GetMapping("/findById")
    @ApiOperation("通过id获取报告卡")
    public ReportUploadRecord findById(@RequestParam String id) {
        return adsReportCardService.findById(id);
    }

    @GetMapping("/findByReportCardId")
    @ApiOperation("通过reportCardId获取报告卡")
    public ReportUploadRecordVO findByReportCardId(@RequestParam String reportCardId) {
        return adsReportCardService.findByReportCardId(reportCardId);
    }

    @GetMapping("/queryArchiveById")
    @ApiOperation("通过查询报告卡档案id")
    public ReportCardArchiveVO queryArchiveById(@RequestParam String reportRecordId) {
        return adsReportCardService.queryArchiveById(reportRecordId);
    }

    @GetMapping("/queryArchiveByCertNum")
    @ApiOperation("通过报告卡id获取报告卡")
    public ReportCardArchiveVO queryArchiveByCertNum(@RequestParam String validCertNumber) {
        return adsReportCardService.queryArchiveByCertNum(validCertNumber);
    }

    @PostMapping("/operateReportCard")
    @ApiOperation("操作报告卡 删除 排除")
    public Boolean operateReportCard(@RequestBody ReportCardOperateDto dto){
        return adsReportCardService.ReportCardOperateDto(dto);
    }
}
