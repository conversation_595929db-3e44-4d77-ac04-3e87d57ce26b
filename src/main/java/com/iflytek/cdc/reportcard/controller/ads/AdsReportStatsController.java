package com.iflytek.cdc.reportcard.controller.ads;


import com.iflytek.cdc.reportcard.dto.ReportUploadStatsQueryDTO;
import com.iflytek.cdc.reportcard.service.AdsReportStatsService;
import com.iflytek.cdc.reportcard.vo.stats.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 传染病报卡统计查询
 */
@RestController
@RequestMapping("/pt/{version}/ads/reportUpload/stats")
@Api(tags = "传染病报告卡统计-数仓查询")
public class AdsReportStatsController {
    private AdsReportStatsService adsReportStatsService;

    @Autowired
    public void setAdsReportStatsService(AdsReportStatsService adsReportStatsService) {
        this.adsReportStatsService = adsReportStatsService;
    }

    /**
     * 概况总览
     * @param dto
     * @return
     */
    @PostMapping("/notifiableInfectTypeStat")
    @ApiOperation("法定传染病-疫情概况总览")
    public ReportUploadSummaryVO notifiableInfectTypeStat(@RequestBody ReportUploadStatsQueryDTO dto) {
        return adsReportStatsService.notifiableInfectTypeStat(dto);
    }

    /**
     * 法定传染病-疫情同比增长TOP10
     */
    @PostMapping("/groupInfectNewCntTop10")
    @ApiOperation("法定传染病-疫情同比增长TOP10")
    public List<ReportUploadStatsVO> groupInfectNewCntTop10(@RequestBody ReportUploadStatsQueryDTO dto) {
        return adsReportStatsService.groupInfectNewCntTop10(dto);
    }

    /**
     * 法定传染病-疫情同比下降TOP10
     */
    @PostMapping("/groupInfectNewCntDeclineTop10")
    @ApiOperation("法定传染病-疫情同比下降TOP10")
    public List<ReportUploadStatsVO> groupInfectNewCntDeclineTop10(@RequestBody ReportUploadStatsQueryDTO dto) {
        return adsReportStatsService.groupInfectNewCntDeclineTop10(dto);
    }

    /**
     *法定传染病-疫情同比增长TOP10-时间分布（病例总览折线图数据）
     */
    @PostMapping({ "/processStatLineChart", "/groupDateDimCnt" })
    @ApiOperation("法定传染病-疫情同比增长TOP10-时间分布（病例总览折线图数据）")
    public List<ReportUploadStatsDateDimVO> groupDateDimCnt(@RequestBody ReportUploadStatsQueryDTO dto) {
        return adsReportStatsService.groupDateDimCnt(dto);
    }

    /**
     * 法定传染病-疫情同比增长TOP10-地区分布
     *
     */
    @PostMapping("/groupAreaCnt")
    @ApiOperation("法定传染病-疫情同比增长TOP10-地区分布")
    public List<ReportUploadStatsAreaVO> groupAreaCnt(@RequestBody ReportUploadStatsQueryDTO dto) {
        return adsReportStatsService.groupAreaCnt(dto);
    }

    /**
     * 法定传染病-疫情同比增长TOP10-人群分布-患者年龄分布
     */
    @PostMapping("/groupAgeCnt")
    @ApiOperation("法定传染病-疫情同比增长TOP10-人群分布-患者年龄分布")
    public List<ReportUploadStatsAgeVO> groupAgeCnt(@RequestBody ReportUploadStatsQueryDTO dto) {
        return adsReportStatsService.groupAgeCnt(dto);
    }

    /**
     * 法定传染病-疫情同比增长TOP10-人群分布-患者性别分布
     */
    @PostMapping("/groupSexCnt")
    @ApiOperation("法定传染病-疫情同比增长TOP10-人群分布-患者性别分布")
    public List<ReportUploadStatsGenderVO> groupGenderCnt(@RequestBody ReportUploadStatsQueryDTO dto) {
        return adsReportStatsService.groupGenderCnt(dto);
    }

    /**
     * 法定传染病-疫情同比增长TOP10-人群分布-患者职业分布
     */
    @PostMapping("/groupJobCnt")
    @ApiOperation("法定传染病-疫情同比增长TOP10-人群分布-患者职业分布")
    public List<ReportUploadStatsJobVO> groupJobCnt(@RequestBody ReportUploadStatsQueryDTO dto) {
        return adsReportStatsService.groupJobCnt(dto);
    }

    /**
     * 法定传染病-疫情概况明细
     */
    @PostMapping("/groupInfectCnt")
    @ApiOperation("法定传染病-疫情概况明细")
    public List<ReportUploadStatsVO> groupInfectCnt(@RequestBody ReportUploadStatsQueryDTO dto) {
        return adsReportStatsService.groupInfectCnt(dto);
    }

    /**
     * 法定传染病-甲乙类传染病按传播途径统计
     */
    @PostMapping("/groupInfectTransmitTypeCnt")
    @ApiOperation("法定传染病-甲乙类传染病按传播途径统计")
    public List<ReportUploadStatsVO> groupInfectTransmitTypeCnt(@RequestBody ReportUploadStatsQueryDTO dto) {
        return adsReportStatsService.groupInfectTransmitTypeCnt(dto);
    }

    /**
     * 法定传染病-按病种统计
     */
    @PostMapping("/groupInfectDiseaseCnt")
    @ApiOperation("法定传染病-按病种统计")
    public List<ReportUploadStatsVO> groupInfectDiseaseCnt(@RequestBody ReportUploadStatsQueryDTO dto) {
        return adsReportStatsService.groupInfectDiseaseCnt(dto);
    }

    /**
     * 法定传染病-按地区医疗
     */
    @PostMapping("/groupInfectAreaCnt")
    @ApiOperation("法定传染病-按地区医疗机构统计")
    public List<ReportUploadStatsVO> groupInfectAreaCnt(@RequestBody ReportUploadStatsQueryDTO dto) {
        return adsReportStatsService.groupInfectAreaCnt(dto);
    }

    /**
     * 法定传染病-按病种统计
     */
    @PostMapping("/groupInfectAreaOrgCnt")
    @ApiOperation("法定传染病-按地区医疗机构统计-下钻医疗机构")
    public List<ReportUploadStatsVO> groupInfectAreaOrgCnt(@RequestBody ReportUploadStatsQueryDTO dto) {
        return adsReportStatsService.groupInfectAreaOrgCnt(dto);
    }

}
