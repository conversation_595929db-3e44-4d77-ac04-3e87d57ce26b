package com.iflytek.cdc.reportcard.controller.bu;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.reportcard.dto.ExchangeInfoRecordQueryDto;
import com.iflytek.cdc.reportcard.dto.ExchangeInfoTemplateQueryDto;
import com.iflytek.cdc.reportcard.entity.bu.ExchangeInfoRecord;
import com.iflytek.cdc.reportcard.entity.bu.ExchangeInfoTemplate;
import com.iflytek.cdc.reportcard.service.ExchangeInfoRecordService;
import com.iflytek.cdc.reportcard.service.ExchangeInfoTemplateService;
import com.iflytek.cdc.reportcard.vo.ExchangeInfoRecordVO;
import com.iflytek.cdc.reportcard.vo.ExchangeInfoTemplateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/pt/{version}/reportcard/info")
@Api(tags = "报告卡信息交换")
public class ReportCardExchangeController {


    private ExchangeInfoTemplateService exchangeInfoTemplateService;

    @Autowired
    public void setExchangeInfoTemplateService(ExchangeInfoTemplateService exchangeInfoTemplateService) {
        this.exchangeInfoTemplateService = exchangeInfoTemplateService;
    }

    private ExchangeInfoRecordService exchangeInfoRecordService;

    @Autowired
    public void setExchangeInfoRecordService(ExchangeInfoRecordService exchangeInfoRecordService) {
        this.exchangeInfoRecordService = exchangeInfoRecordService;
    }

    @ApiOperation("创建报告卡信息交换模板")
    @PostMapping("/template/create")
    public ExchangeInfoTemplate createTemplate(@RequestBody ExchangeInfoTemplate template,
                                       @RequestParam String loginUserId){
        return exchangeInfoTemplateService.createTemplate(template,loginUserId);
    }

    @ApiOperation("根据id查看信息交换模板详情")
    @GetMapping("/template/queryById")
    public ExchangeInfoTemplateVO queryById(@RequestParam String templateId){
        return exchangeInfoTemplateService.queryTemplateById(templateId);
    }

    @ApiOperation("查询报告卡信息交换模板下拉列表")
    @GetMapping("/template/selectlist")
    public List<ExchangeInfoTemplate> querySelectList(
                                                    @RequestParam String loginUserId){
        return exchangeInfoTemplateService.queryTemplateSelectList(loginUserId);
    }


    @ApiOperation("查询报告卡信息交换模板列表")
    @PostMapping("/template/list")
    public PageInfo<ExchangeInfoTemplate> queryTemplateList(@RequestBody ExchangeInfoTemplateQueryDto dto,
                                                    @RequestParam String loginUserId){
        return exchangeInfoTemplateService.queryTemplateList(dto,loginUserId);
    }


    @ApiOperation("创建报告卡信息交换记录")
    @PostMapping("/record/create")
    public ExchangeInfoRecord create(@RequestBody ExchangeInfoRecord record,
                                       @RequestParam String loginUserId){
        return exchangeInfoRecordService.createRecord(record,loginUserId);
    }


    @ApiOperation("根据id查看信息交换模板详情")
    @GetMapping("/record/queryById")
    public ExchangeInfoRecordVO queryRecordById(@RequestParam String exchangeId){
        return exchangeInfoRecordService.queryRecordById(exchangeId);
    }


    @ApiOperation("查询报告卡信息交换模板列表")
    @PostMapping("/record/list")
    public PageInfo<ExchangeInfoRecordVO> queryRecordList(@RequestBody ExchangeInfoRecordQueryDto dto,
                                                    @RequestParam String loginUserId){
        return exchangeInfoRecordService.queryRecordList(dto,loginUserId);
    }
}
