package com.iflytek.cdc.reportcard.mapper.pg;


import com.iflytek.cdc.edr.vo.CommonDateStatVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO;
import com.iflytek.cdc.province.model.dto.AdsMsProcessReqDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface AdsReportMsProcessStatMapper {
    List<AdsMsProcessRespVO> findMonthDistribution(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> findQuarterDistribution(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> findYearDistribution(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupArea(AdsMsProcessReqDTO adsMsProcessReqDTO);

    List<AdsMsProcessRespVO> groupAreaCurrentLevel(AdsMsProcessReqDTO adsMsProcessReqDTO);

    List<AdsMsProcessRespVO> groupAge(AdsMsProcessReqDTO adsMsProcessReqDTO);

    List<AdsMsProcessRespVO> groupSex(AdsMsProcessReqDTO adsMsProcessReqDTO);

    List<AdsMsProcessRespVO> groupJob(AdsMsProcessReqDTO adsMsProcessReqDTO);

    List<AdsMsProcessRespVO> dayOverAll(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> weekOverAll(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> meadowOverAll(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> monthOverAll(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> quarterOverAll(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> yearOverAll(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> dayAreaChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> weekAreaChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> meadowAreaChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> monthAreaChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> quarterAreaChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> yearAreaChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> daySexChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> weekSexChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> meadowSexChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> monthSexChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> quarterSexChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> yearSexChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> dayAgeChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> weekAgeChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> meadowAgeChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> monthAgeChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> quarterAgeChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> yearAgeChange(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> dayQOQTrendChart(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> weekQOQTrendChart(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> meadowQOQTrendChart(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> monthQOQTrendChart(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> quarterQOQTrendChart(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> yearQOQTrendChart(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupDiseaseName(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupIdentifyClass(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupOutcomeStatus(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupHistoryBefore(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupPathogenResNominal(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupSymptomFirst(AdsMsProcessReqDTO reqDTO);

    List<AdsMsProcessRespVO> groupAddrType(AdsMsProcessReqDTO adsMsProcessReqDTO);

    List<AdsMsProcessRespVO> findHalfYearDistribution(AdsMsProcessReqDTO adsMsProcessReqDTO);
}
