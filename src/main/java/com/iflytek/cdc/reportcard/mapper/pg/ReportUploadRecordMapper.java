package com.iflytek.cdc.reportcard.mapper.pg;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.reportcard.dto.ReportCardOperateDto;
import com.iflytek.cdc.reportcard.dto.ReportUploadQueryDTO;
import com.iflytek.cdc.reportcard.entity.ads.ReportUploadRecord;
import com.iflytek.cdc.reportcard.vo.ReportCardArchiveVO;
import com.iflytek.cdc.reportcard.vo.ReportUploadRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ReportUploadRecordMapper extends BaseMapper<ReportUploadRecord> {

    List<ReportUploadRecordVO> findList(ReportUploadQueryDTO queryDTO);

    ReportUploadRecordVO findByReportCardId( @Param("reportCardId") String reportCardId);


    ReportCardArchiveVO queryArchiveByCertNum(@Param("validCertNumber") String validCertNumber);

    void updateReportCardArchive(@Param("reportRecordId") String reportRecordId, @Param("reportCardArchiveVO") ReportCardArchiveVO reportCardArchiveVO);

    List<ReportUploadRecordVO> findDyqwList(ReportUploadQueryDTO queryDTO);

    List<ReportUploadRecordVO> queryDyqwBatchIds(List<String> idList);

    ReportUploadRecordVO selectDyqwById(String id);

    ReportUploadRecordVO findDyqwByReportCardId(String reportCardId);

    int updateReportCardOperate(ReportCardOperateDto dto);

    Integer tobeReviewedCardCount(ReportUploadQueryDTO queryDTO);

    List<String> reportingUnitInquiry(ReportUploadQueryDTO queryDTO);

    List<ReportUploadRecordVO> findDyqwListByGX(ReportUploadQueryDTO queryDTO);
}
