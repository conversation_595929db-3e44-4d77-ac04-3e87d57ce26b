package com.iflytek.cdc.reportcard.mapper.bu;

import com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.ProcessAwarenessRespVO;
import com.iflytek.cdc.province.model.dto.AdsMsProcessReqDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 报卡督导疾病专题分析 Mapper 接口
 */
@Mapper
public interface ReportMsProcessStatMapper {

    // 时间分布相关方法
    List<AdsMsProcessRespVO> findMonthDistribution(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> findQuarterDistribution(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> findYearDistribution(AdsMsProcessReqDTO reqDTO);

    // 地区分布相关方法
    List<AdsMsProcessRespVO> groupArea(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> groupAreaCurrentLevel(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> groupAreaDripDetail(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> groupAddrType(AdsMsProcessReqDTO reqDTO);

    // 人群分布相关方法
    List<AdsMsProcessRespVO> groupAge(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> groupSex(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> groupJob(AdsMsProcessReqDTO reqDTO);

    // 流行动态变化相关方法
    List<AdsMsProcessRespVO> dayOverAll(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> weekOverAll(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> meadowOverAll(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> monthOverAll(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> quarterOverAll(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> yearOverAll(AdsMsProcessReqDTO reqDTO);

    // 地区变化趋势
    List<AdsMsProcessRespVO> dayAreaChange(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> weekAreaChange(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> meadowAreaChange(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> monthAreaChange(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> quarterAreaChange(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> yearAreaChange(AdsMsProcessReqDTO reqDTO);

    // 性别变化趋势
    List<AdsMsProcessRespVO> daySexChange(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> weekSexChange(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> meadowSexChange(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> monthSexChange(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> quarterSexChange(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> yearSexChange(AdsMsProcessReqDTO reqDTO);

    // 年龄变化趋势
    List<AdsMsProcessRespVO> dayAgeChange(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> weekAgeChange(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> meadowAgeChange(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> monthAgeChange(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> quarterAgeChange(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> yearAgeChange(AdsMsProcessReqDTO reqDTO);

    // 环比趋势图
    List<AdsMsProcessRespVO> dayQOQTrendChart(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> weekQOQTrendChart(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> meadowQOQTrendChart(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> monthQOQTrendChart(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> quarterQOQTrendChart(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> yearQOQTrendChart(AdsMsProcessReqDTO reqDTO);

    // 病情分布分析相关方法
    List<AdsMsProcessRespVO> groupIdentifyClass(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> groupOutcomeStatus(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> groupSymptomFirst(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> groupHistoryBefore(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> groupPathogenResNominal(AdsMsProcessReqDTO reqDTO);

    // 疾病分布分析相关方法
    List<AdsMsProcessRespVO> groupDiseaseName(AdsMsProcessReqDTO reqDTO);
    List<AdsMsProcessRespVO> groupDiseaseCodeAndName(AdsMsProcessReqDTO reqDTO);

    // 态势感知相关方法
    List<ProcessAwarenessRespVO> loadAwarenessResult(AdsMsProcessReqDTO reqDTO);

    // 区域病例数统计
    List<AdsMsProcessRespVO> areaMedDateCount(AdsMsProcessReqDTO reqDTO);
}
