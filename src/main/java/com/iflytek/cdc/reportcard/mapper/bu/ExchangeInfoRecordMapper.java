package com.iflytek.cdc.reportcard.mapper.bu;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.iflytek.cdc.reportcard.dto.ExchangeInfoRecordQueryDto;
import com.iflytek.cdc.reportcard.entity.bu.ExchangeInfoRecord;
import com.iflytek.cdc.reportcard.vo.ExchangeInfoRecordVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ExchangeInfoRecordMapper extends BaseMapper<ExchangeInfoRecord> {
    ExchangeInfoRecordVO queryRecordById(String recordId);


    List<ExchangeInfoRecordVO> queryRecordList(ExchangeInfoRecordQueryDto dto);

}
