package com.iflytek.cdc.reportcard.mapper.pg;

import com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO;
import com.iflytek.cdc.province.model.dto.AdsMsProcessReqDTO;
import com.iflytek.cdc.reportcard.dto.ReportUploadStatsQueryDTO;
import com.iflytek.cdc.reportcard.vo.stats.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ReportUploadStatsMapper {
    /**
     * 法定传染病-疫情概况总览
     *
     * @param dto 查询条件
     * @return 疫情概况总览数据
     */
    ReportUploadSummaryVO notifiableInfectTypeStat(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsVO> groupInfectNewCntTop10(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsVO> groupInfectNewCntDeclineTop10(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsDateDimVO> groupDateDimCnt(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsAreaVO> groupAreaCnt(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsAgeVO> groupAgeCnt(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsGenderVO> groupGenderCnt(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsJobVO> groupJobCnt(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsVO> groupInfectCnt(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsVO> groupInfectTransmitTypeCnt(ReportUploadStatsQueryDTO dto);

    List<ReportUploadStatsVO> groupInfectDiseaseCnt(ReportUploadStatsQueryDTO dto);

    /**
     * 按地区统计发病数, 发病率, 病死率  统计原表
     */
    List<ReportUploadStatsVO> groupInfectAreaCnt(ReportUploadStatsQueryDTO reqDTO);

    /**
     * 按地区下的医疗机构统计 发病数, 发病率, 病死率  下钻到医疗机构 统计原表
     */
    List<ReportUploadStatsVO> groupInfectAreaOrgCnt(ReportUploadStatsQueryDTO reqDTO);


}
