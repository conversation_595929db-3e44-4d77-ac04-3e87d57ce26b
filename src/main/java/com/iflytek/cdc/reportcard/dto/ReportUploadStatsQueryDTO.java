package com.iflytek.cdc.reportcard.dto;


import com.iflytek.cdc.reportcard.dto.common.CommonQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("报卡督导管理子系统-报卡数据统计-疫情概况统计-查询条件")
public class ReportUploadStatsQueryDTO extends CommonQueryDTO {

    /**
     * 地址类型 livingAddress-病例现住址 orgAddress-监测单位
     */
    @ApiModelProperty("地址类型 livingAddress-病例现住址 orgAddress-监测单位")
    private String addressType;

    @ApiModelProperty("传染病大类（法定传染病、其他传染病）")
    private String infectClass;

    @ApiModelProperty("传染病类型（甲类传染病、乙类传染病、丙类传染病等）")
    private String infectType;

    @ApiModelProperty("传染病类型列表")
    private List<String> infectTypes;

    @ApiModelProperty("传染病编码")
    private String infectCode;

    @ApiModelProperty("传染病名称")
    private String infectName;

    @ApiModelProperty("需要过滤掉的传染病编码，例如新型冠状病毒感染")
    private List<String> filterCodes;

    @ApiModelProperty("传播途径分类")
    private String infectTransmitType;

    @ApiModelProperty("统计指标标记: newCnt newRate deathRate")
    private String statFlag;

    @ApiModelProperty(value = "传染病编码集")
    private List<String> infectCodeList;
    
    public List<String> getFilterInfectCodes() {
        return filterCodes;
    }

    public void setFilterInfectCodes(List<String> filterInfectCodes) {
        this.filterCodes = filterInfectCodes;
    }
}
