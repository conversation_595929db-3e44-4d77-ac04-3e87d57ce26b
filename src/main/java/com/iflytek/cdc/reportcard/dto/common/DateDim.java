package com.iflytek.cdc.reportcard.dto.common;

import cn.hutool.core.date.DateUtil;
import lombok.Data;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Data
public class DateDim {

    private Integer year;
    private Integer quarter;
    private Integer month;
    private Integer week;
    private String meadow;
    private String halfYear;

    /**
     * 根据开始时间，结束时间获取年
     */
    public static List<DateDim> year(Date startDate, Date endDate) {
        long startTime = startDate.getTime();
        long endTime = endDate.getTime();

        List<DateDim> years = new ArrayList<>();
        final Calendar cal = DateUtil.calendar(startDate);
        while (startTime <= endTime) {
            DateDim dateDim = new DateDim();
            dateDim.setYear(cal.get(Calendar.YEAR));
            // 如果开始时间超出结束时间，让结束时间为开始时间，处理完后结束循环
            years.add(dateDim);
            cal.add(Calendar.YEAR, 1);
            startTime = cal.getTimeInMillis();
        }

        return years;
    }

    /**
     * 根据开始时间，结束时间获取年和半年
     */
    public static List<DateDim> yearAndHalfYear(Date startDate, Date endDate) {
        long startTime = startDate.getTime();
        long endTime = endDate.getTime();

        List<DateDim> halfYears = new ArrayList<>();
        final Calendar cal = DateUtil.calendar(startDate);
        while (startTime <= endTime) {
            DateDim dateDim = new DateDim();
            dateDim.setYear(cal.get(Calendar.YEAR));
            dateDim.setHalfYear(halfYear(cal.get(Calendar.MONTH)));
            // 如果开始时间超出结束时间，让结束时间为开始时间，处理完后结束循环
            halfYears.add(dateDim);
            cal.add(Calendar.MONTH, 6);
            startTime = cal.getTimeInMillis();
        }

        return halfYears;
    }

    /**
     * 根据开始时间，结束时间获取年和季度
     */
    public static List<DateDim> yearAndQuarter(Date startDate, Date endDate) {
        long startTime = startDate.getTime();
        long endTime = endDate.getTime();

        List<DateDim> quarters = new ArrayList<>();
        final Calendar cal = DateUtil.calendar(startDate);
        while (startTime <= endTime) {
            DateDim dateDim = new DateDim();
            dateDim.setYear(cal.get(Calendar.YEAR));
            dateDim.setQuarter(cal.get(Calendar.MONTH) / 3 + 1);
            // 如果开始时间超出结束时间，让结束时间为开始时间，处理完后结束循环
            quarters.add(dateDim);
            cal.add(Calendar.MONTH, 3);
            startTime = cal.getTimeInMillis();
        }

        return quarters;
    }

    /**
     * 根据开始时间，结束时间获取年，月
     */
    public static List<DateDim> yearAndMonth(Date startDate, Date endDate) {
        long startTime = startDate.getTime();
        long endTime = endDate.getTime();

        List<DateDim> months = new ArrayList<>();
        final Calendar cal = DateUtil.calendar(startDate);
        while (startTime <= endTime) {
            DateDim dateDim = new DateDim();
            dateDim.setYear(cal.get(Calendar.YEAR));
            dateDim.setMonth(cal.get(Calendar.MONTH) + 1);
            // 如果开始时间超出结束时间，让结束时间为开始时间，处理完后结束循环
            months.add(dateDim);
            cal.add(Calendar.MONTH, 1);
            startTime = cal.getTimeInMillis();
        }

        return months;
    }

    /**
     * 根据开始时间，结束时间获取月和旬
     */
    public static List<DateDim> yearAndMonthAndMeadow(Date startDate, Date endDate) {
        long startTime = startDate.getTime();
        long endTime = endDate.getTime();

        List<DateDim> meadows = new ArrayList<>();
        final Calendar cal = DateUtil.calendar(startDate);
        while (startTime <= endTime) {
            DateDim dateDim = new DateDim();
            dateDim.setYear(cal.get(Calendar.YEAR));
            dateDim.setMonth(cal.get(Calendar.MONTH) + 1);
            dateDim.setMeadow(meadow(cal.get(Calendar.DAY_OF_MONTH)));
            // 如果开始时间超出结束时间，让结束时间为开始时间，处理完后结束循环
            meadows.add(dateDim);
            cal.add(Calendar.DAY_OF_MONTH, 10);
            startTime = cal.getTimeInMillis();
        }

        return meadows;
    }

    /**
     * 根据开始时间，结束时间获取年和周
     */
    public static List<DateDim> yearAndWeek(Date startDate, Date endDate) {
        long startTime = startDate.getTime();
        long endTime = endDate.getTime();

        List<DateDim> weeks = new ArrayList<>();
        final Calendar cal = DateUtil.calendar(startDate);
        while (startTime <= endTime) {
            DateDim dateDim = new DateDim();
            dateDim.setYear(cal.get(Calendar.YEAR));
            dateDim.setWeek(cal.get(Calendar.WEEK_OF_YEAR));
            // 如果开始时间超出结束时间，让结束时间为开始时间，处理完后结束循环
            weeks.add(dateDim);
            cal.add(Calendar.WEEK_OF_YEAR, 1);
            startTime = cal.getTimeInMillis();
        }

        return weeks;
    }

    private static String halfYear(int month) {
        if (month < 6) {
            return "U";
        } else {
            return "D";
        }
    }

    private static String meadow(int day) {
        if (day <= 10) {
            return "1";
        } else if (day <= 20) {
            return "2";
        } else {
            return "3";
        }
    }
}
