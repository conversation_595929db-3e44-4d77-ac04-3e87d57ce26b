package com.iflytek.cdc.reportcard.dto.common;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 导出任务 公共参数
 * */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExportTaskCommonDTO extends CommonQueryDTO {

    //导出参数
    @ApiModelProperty("导出任务id")
    private String exportTaskId;
    @ApiModelProperty(value = "任务名称")
    private String taskName;
    @ApiModelProperty(value = "导出任务URL")
    private String taskUrl;
    @ApiModelProperty(value = "导出任务请求参数")
    private String taskParam;
    @ApiModelProperty(value = "模块")
    private String moduleType;
    @ApiModelProperty("导出类型")
    private String exportType;
    @ApiModelProperty("系统code")
    private String appCode;

}
