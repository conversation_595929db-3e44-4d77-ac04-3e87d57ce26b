package com.iflytek.cdc.reportcard.dto.common;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


@ApiModel("公用查询")
@Data
public class CommonQueryDTO {
    @ApiModelProperty(
            value = "页码 从1开始",
            example = "1"
    )
    public Integer pageIndex = 1;
    @ApiModelProperty(
            value = "每页条数",
            example = "20"
    )
    public Integer pageSize = 20;
    @ApiModelProperty("所在省")
    private String provinceCode;
    @ApiModelProperty("所在市")
    private String cityCode;
    @ApiModelProperty("所在县区")
    private String districtCode;
    @ApiModelProperty("所在街道")
    private String streetCode;

    @ApiModelProperty("排序字段")
    private String property;
    @ApiModelProperty("排序顺序")
    private String direction;
    
    @ApiModelProperty(value = "排序字段sql", hidden = true)
    private String orderPropertySql;

    @ApiModelProperty("开始时间")
    private Date startDate;
    @ApiModelProperty("结束日期")
    private Date endDate;

    @ApiModelProperty("状态")
    private String status;

    private List<String> provinceCodes;
    private List<String> cityCodes;
    private List<String> districtCodes;
    private List<String> streetCodes;
    private Integer areaLevel;
    private String areaCode;

    @ApiModelProperty( "时间指标类型")
    private String dateDimType;

    @ApiModelProperty("关键词搜索")
    private String queryKey;

    private List<DateDim> dateDims;

    @ApiModelProperty("上期开始时间")
    private Date lastStartDate;
    @ApiModelProperty("上期结束日期")
    private Date lastEndDate;

    @ApiModelProperty("同期开始时间")
    private Date lastYStartDate;
    @ApiModelProperty("同期结束日期")
    private Date lastYEndDate;

    public void dealDate(CommonQueryDTO queryParam){
        long offset = queryParam.getEndDate().getTime() - queryParam.getStartDate().getTime() + 1;
        DateTime lastYStartDate = DateUtil.offset(queryParam.getStartDate(), DateField.YEAR, -1);
        DateTime lastYEndDate = DateUtil.offset(queryParam.getEndDate(), DateField.YEAR, -1);
        queryParam.setLastStartDate(new Date(queryParam.getStartDate().getTime() - offset));
        queryParam.setLastEndDate(new Date(queryParam.getEndDate().getTime() - offset));
        queryParam.setLastYStartDate(lastYStartDate);
        queryParam.setLastYEndDate(lastYEndDate);
    }
}
