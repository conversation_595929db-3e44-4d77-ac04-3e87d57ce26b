package com.iflytek.cdc.reportcard.vo.stats;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 传染病报告卡统计总览数据
 */
@Data
public class ReportUploadSummaryVO {

    @ApiModelProperty("法定传染病发病数")
    public Integer notifiableDiseaseNewCnt;

    @ApiModelProperty("法定传染病发病数-上期")
    public Integer notifiableDiseaseNewCntLast;

    @ApiModelProperty("法定传染病发病数-同期")
    public Integer notifiableDiseaseNewCntLastY;

    @ApiModelProperty("法定传染病发病数-同比")
    public String notifiableDiseaseNewCntYearGrowth;

    @ApiModelProperty("法定传染病发病数-环比")
    public String notifiableDiseaseNewCntChain;

    @ApiModelProperty("甲乙类传染病发病数")
    public Integer classAAndBNewCnt;

    @ApiModelProperty("甲乙类传染病发病数-上期")
    public Integer classAAndBNewCntLast;

    @ApiModelProperty("甲乙类传染病发病数-同期")
    public Integer classAAndBNewCntLastY;

    @ApiModelProperty("甲乙类传染病发病数-同比")
    public String classAAndBNewCntYearGrowth;

    @ApiModelProperty("甲乙类传染病发病数-环比")
    public String classAAndBNewCntChain;

    @ApiModelProperty("丙类传染病发病数")
    public Integer classCNewCnt;

    @ApiModelProperty("丙类传染病发病数-上期")
    public Integer classCNewCntLast;

    @ApiModelProperty("丙类传染病发病数-同期")
    public Integer classCNewCntLastY;

    @ApiModelProperty("丙类传染病发病数-同比")
    public String classCNewCntYearGrowth;

    @ApiModelProperty("丙类传染病发病数-环比")
    public String classCNewCntChain;

    @ApiModelProperty("其它传染病发病数")
    public Integer otherNewCnt;

    @ApiModelProperty("其它传染病发病数-上期")
    public Integer otherNewCntLast;

    @ApiModelProperty("其它传染病发病数-同期")
    public Integer otherNewCntLastY;

    @ApiModelProperty("其它传染病发病数-同比")
    public String otherNewCntYearGrowth;

    @ApiModelProperty("其它传染病发病数-环比")
    public String otherNewCntChain;

    @ApiModelProperty("法定传染病死亡数")
    public Integer notifiableDiseaseDeadCnt;

    @ApiModelProperty("法定传染病死亡数-上期")
    public Integer notifiableDiseaseDeadCntLast;

    @ApiModelProperty("法定传染病死亡数-同期")
    public Integer notifiableDiseaseDeadCntLastY;

    @ApiModelProperty("法定传染病死亡数-同比")
    public String notifiableDiseaseDeadCntYearGrowth;

    @ApiModelProperty("法定传染病死亡数-环比")
    public String notifiableDiseaseDeadCntChain;

    @ApiModelProperty("甲乙类传染病死亡数")
    public Integer classAAndBDeadCnt;

    @ApiModelProperty("甲乙类传染病死亡数-上期")
    public Integer classAAndBDeadCntLast;

    @ApiModelProperty("甲乙类传染病死亡数-同期")
    public Integer classAAndBDeadCntLastY;

    @ApiModelProperty("甲乙类传染病死亡数-同比")
    public String classAAndBDeadCntYearGrowth;

    @ApiModelProperty("甲乙类传染病死亡数-环比")
    public String classAAndBDeadCntChain;

    @ApiModelProperty("丙类传染病死亡数")
    public Integer classCDeadCnt;

    @ApiModelProperty("丙类传染病死亡数-上期")
    public Integer classCDeadCntLast;

    @ApiModelProperty("丙类传染病死亡数-同期")
    public Integer classCDeadCntLastY;

    @ApiModelProperty("丙类传染病死亡数-同比")
    public String classCDeadCntYearGrowth;

    @ApiModelProperty("丙类传染病死亡数-环比")
    public String classCDeadCntChain;

    @ApiModelProperty("其它传染病死亡数")
    public Integer otherDeadCnt;

    @ApiModelProperty("其它传染病死亡数-上期")
    public Integer otherDeadCntLast;

    @ApiModelProperty("其它传染病死亡数-同期")
    public Integer otherDeadCntLastY;

    @ApiModelProperty("其它传染病死亡数-同比")
    public String otherDeadCntYearGrowth;

    @ApiModelProperty("其它传染病死亡数-环比")
    public String otherDeadCntChain;

}
