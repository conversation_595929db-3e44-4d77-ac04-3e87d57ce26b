package com.iflytek.cdc.reportcard.vo.stats;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 传染病报告卡统计数据-按性别统计
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReportUploadStatsGenderVO extends ReportUploadIndicators {

    @ApiModelProperty("患者性别")
    private String patientSexName;

    @ApiModelProperty("描述")
    private String description;
}
