package com.iflytek.cdc.reportcard.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ContentStyle(
        horizontalAlignment = HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
)
@Data
public class ReportCardInfectExcelVO {

    @ApiModelProperty(value = "病例数_新发_本期")
    @ExcelProperty({"发病数", "本期"})
    private Integer processNewCnt;

    @ApiModelProperty(value = "病例数_新发_去年同期")
    @ExcelProperty({"发病数", "同期"})
    private Integer processNewCntLastY;

    @ApiModelProperty("发病数-同比")
    @ExcelProperty({"发病数", "同比"})
    private String processNewCntYearGrowth;

    @ApiModelProperty(value = "病例数_新发_上期")
    @ExcelProperty({"发病数", "上期"})
    private Integer processNewCntLast;

    @ApiModelProperty("发病数-环比")
    @ExcelProperty({"发病数", "环比"})
    private String processNewCntChain;

    @ApiModelProperty(value = "病例数_死亡_本期")
    @ExcelProperty({"死亡数", "本期"})
    private Integer processDeadCnt;

    @ApiModelProperty(value = "病例数_死亡_去年同期")
    @ExcelProperty({"死亡数", "同期"})
    private Integer processDeadCntLastY;

    @ApiModelProperty("发病数-同比")
    @ExcelProperty({"死亡数", "同比"})
    private String processDeadCntYearGrowth;

    @ApiModelProperty(value = "病例数_死亡_上期")
    @ExcelProperty({"死亡数", "上期"})
    private Integer processDeadCntLast;

    @ApiModelProperty("发病数-环比")
    @ExcelProperty({"死亡数", "环比"})
    private String processDeadCntChain;

    @ApiModelProperty("常住人口数")
    @ExcelProperty("常住人口数")
    @ColumnWidth(22)
    private Integer population;

    @ApiModelProperty("本期发病率")
    @ExcelProperty("本期发病率")
    @ColumnWidth(22)
    private String currentRate;

    @ApiModelProperty("本期死亡率")
    @ExcelProperty("本期死亡率")
    @ColumnWidth(22)
    private String deathRate;

}
