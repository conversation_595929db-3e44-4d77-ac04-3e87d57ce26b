package com.iflytek.cdc.reportcard.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@ContentStyle(
        horizontalAlignment = HorizontalAlignmentEnum.CENTER,
        verticalAlignment =  VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
)
@Data
public class ReportCardInfectAreaExcelVO extends  ReportCardInfectExcelVO{

    @ApiModelProperty("地区")
    @ExcelProperty(value = "地区",index = 0)
    @ColumnWidth(25)
    private String description;
}
