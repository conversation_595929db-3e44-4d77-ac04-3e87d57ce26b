package com.iflytek.cdc.reportcard.vo.stats;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 传染病报告卡统计数据-按行政区划统计
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReportUploadStatsAreaVO extends ReportUploadIndicators {

    @ApiModelProperty("行政区划编码")
    private String areaCode;

    @ApiModelProperty("行政区划名称")
    private String areaName;

    @ApiModelProperty("区域级别")
    private Integer areaLevel;

    @ApiModelProperty("描述")
    private String description;
}
