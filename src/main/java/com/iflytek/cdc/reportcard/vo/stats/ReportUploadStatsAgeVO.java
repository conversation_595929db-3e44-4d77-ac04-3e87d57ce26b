package com.iflytek.cdc.reportcard.vo.stats;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 传染病报告卡统计数据-按年龄统计
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReportUploadStatsAgeVO extends ReportUploadIndicators {

    @ApiModelProperty("患者年龄分组")
    private String patientAgeGroup;

    @ApiModelProperty("描述")
    private String description;
}
