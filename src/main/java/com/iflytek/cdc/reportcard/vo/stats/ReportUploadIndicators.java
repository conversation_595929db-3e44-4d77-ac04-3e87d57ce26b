package com.iflytek.cdc.reportcard.vo.stats;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 传染病报告卡统计数据-统计指标基础项
 */
@Data
public class ReportUploadIndicators {

    @ApiModelProperty("新发病例数")
    private Integer processNewCnt;

    @ApiModelProperty("新发病例数-七日平均")
    private Integer processNewCntAvgS;

    @ApiModelProperty("新发病例数-上期")
    private Integer processNewCntLast;

    @ApiModelProperty("新发病例数-同期")
    private Integer processNewCntLastY;

    @ApiModelProperty("新发病例数-同比")
    private String processNewCntYearGrowth;

    @ApiModelProperty("新发病例数-环比")
    private String processNewCntChain;

    @ApiModelProperty("新发病例数-预测值")
    private Integer processNewCntPre;

    @ApiModelProperty("病死数-本期")
    private Integer processDeadIllnessCnt;

    @ApiModelProperty("死亡病例数")
    private Integer processDeadCnt;

    @ApiModelProperty("死亡病例数-七日平均")
    private Integer processDeadCntAvgS;

    @ApiModelProperty("死亡病例数-上期")
    private Integer processDeadCntLast;

    @ApiModelProperty("死亡病例数-同期")
    private Integer processDeadCntLastY;

    @ApiModelProperty("死亡病例数-同比")
    private String processDeadCntYearGrowth;

    @ApiModelProperty("死亡病例数-环比")
    private String processDeadCntChain;

    @ApiModelProperty("常住人口数")
    private Integer residentPopulation;

    @ApiModelProperty("本期发病率，单位 1/10万")
    private String newRate;

    @ApiModelProperty("本期死亡率，单位 1/10万")
    private String deadRate;
    
    @ApiModelProperty("占比")
    private String rateStr;

}
