package com.iflytek.cdc.reportcard.vo.stats;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Comparator;
import java.util.List;
import java.util.function.Function;

/**
 * 传染病报告卡统计数据-通用类-按传染病统计
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReportUploadStatsVO extends ReportUploadIndicators {

    @ApiModelProperty("传染病大类")
    private String infectClass;

    @ApiModelProperty("传染病类型")
    private String infectType;

    @ApiModelProperty("传染病编码")
    private String infectCode;

    @ApiModelProperty("传染病名称")
    private String infectName;

    @ApiModelProperty("传播途径分类")
    private String infectTransmitType;

    @ApiModelProperty("父级病种id")
    private String parentDiseaseId;

    @ApiModelProperty("排序字段")
    private Integer orderFlag;

    @ApiModelProperty("亚型集合")
    private List<ReportUploadStatsVO> children;

    @ApiModelProperty("地区编码")
    private String areaCode;

    @ApiModelProperty("description")
    private String description;


    /**
     * 树 根据排序字段
     */
    public static void sortTreeBy(List<ReportUploadStatsVO> root, Function<ReportUploadStatsVO, Integer> sortFunction) {
        if (root == null || root.isEmpty()) {
            return;
        }
        //对当前节点的子节点进行排序
        root.sort(Comparator.nullsLast(Comparator.comparing(sortFunction, Comparator.nullsLast(Comparator.naturalOrder())))
                            .thenComparing(ReportUploadStatsVO::getInfectCode, Comparator.nullsLast(Comparator.naturalOrder())));
        // 遍历所有根节点
        for (ReportUploadStatsVO node : root) {
            // 递归对子节点进行排序
            List<ReportUploadStatsVO> children = node.getChildren();
            sortTreeBy(children, sortFunction);
            // 排序后的子节点需要重新设置回当前节点
            node.setChildren(children);
        }
    }
}
