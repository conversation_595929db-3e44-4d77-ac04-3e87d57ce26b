package com.iflytek.cdc.indicator.controller;




import cn.hutool.core.util.StrUtil;
import com.iflytek.cdc.indicator.dto.IndicatorQueryDTO;
import com.iflytek.cdc.indicator.service.IndicatorQueryService;
import com.iflytek.cdc.indicator.vo.IndicatorQueryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static com.iflytek.cdc.indicator.util.BusinessTypeEnum.REPORT_CARD;
import static com.iflytek.cdc.indicator.util.BusinessTypeEnum.REPORT_CARD_ADS;

@RefreshScope
@Slf4j
@Api(tags = "指标查询")
@RestController
public class IndicatorController {

    @Resource
    private IndicatorQueryService indicatorQueryService;

    private final static String ADS_STR = "ads";

    @Value("${report.record.type:app}")
    private String reportRecordType;
    /**
     * 指标批量查询
     */
    @PostMapping("/v1/pt/indicator/query/{businessType}")
    @ApiOperation("指标查询")
    public List<IndicatorQueryVO> indicatorQuery(@RequestBody List<IndicatorQueryDTO> queryList, @PathVariable String businessType) {
        if (StrUtil.equals(reportRecordType,ADS_STR) && REPORT_CARD.getCode().equals(businessType)) {
            businessType = REPORT_CARD_ADS.getCode();
        }
        return indicatorQueryService.query(queryList, businessType);
    }
}
