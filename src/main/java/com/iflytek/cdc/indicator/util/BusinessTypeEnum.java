package com.iflytek.cdc.indicator.util;

import java.util.Arrays;

import lombok.Getter;

/**
 * 简报业务类型枚举
 */
@Getter
public enum BusinessTypeEnum {

    /**
     * 传染病疫情监测报告系统
     */
    INFECTED("infected", false),

    /**
     * 症候群监测报告系统
     */
    SYNDROME("syndrome", false),

    /**
     * 传染病报卡督导管理子系统
     */
    REPORT_CARD("reportCard", true),


    /**
     * 传染病报卡督导管理子系统 查询数仓
     */
    REPORT_CARD_ADS("reportCardAds", false),

    /**
     * 突发公共卫生事件管理系统
     */
    EMERGENCY("emergency", true);

    private final String code;

    /**
     * 是否业务库数据源
     */
    private final boolean businessSource;

    BusinessTypeEnum(String code, boolean businessSource) {
        this.code = code;
        this.businessSource = businessSource;
    }

    /**
     * 是否业务库数据源
     * 
     * @param businessType 业务类型
     * @return 是否业务库数据源
     */
    public static boolean isBusinessSource(String businessType) {
        return Arrays.stream(BusinessTypeEnum.values())
                .anyMatch(type -> type.getCode().equals(businessType) && type.isBusinessSource());
    }
}
