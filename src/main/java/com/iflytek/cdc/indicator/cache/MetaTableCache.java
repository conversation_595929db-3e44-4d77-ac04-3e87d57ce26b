package com.iflytek.cdc.indicator.cache;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.iflytek.cdc.indicator.entity.AnalyzeMeta;
import com.iflytek.cdc.indicator.entity.DimensionMeta;
import com.iflytek.cdc.indicator.mapper.bu.AnalyzeMetaMapper;
import com.iflytek.cdc.indicator.mapper.bu.DimensionMetaMapper;

import cn.hutool.core.util.StrUtil;

@Component
public class MetaTableCache {

    private final Cache<String, List<DimensionMeta>> dimensionMetaCache = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build();

    private final LoadingCache<String, List<AnalyzeMeta>> analyzeMetaCacheById = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build(this::loadAnalyzeMetaByRelationId);

    private final LoadingCache<String, List<AnalyzeMeta>> analyzeMetaCacheByTable = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .build(this::loadAnalyzeMetaByTableName);

    @Resource
    private DimensionMetaMapper dimensionMetaMapper;

    @Resource
    private AnalyzeMetaMapper analyzeMetaMapper;

    private List<AnalyzeMeta> loadAnalyzeMetaByRelationId(String relationId) {
        return analyzeMetaMapper.selectList(new LambdaQueryWrapper<AnalyzeMeta>().eq(AnalyzeMeta::getRelationId, relationId));
    }

    private List<AnalyzeMeta> loadAnalyzeMetaByTableName(String tableName) {
        return analyzeMetaMapper.selectList(new LambdaQueryWrapper<AnalyzeMeta>().eq(AnalyzeMeta::getTableName, tableName));
    }

    public List<DimensionMeta> getDimMetas(String dimId) {
        if (StrUtil.isBlank(dimId)) {
            return Collections.emptyList();
        }
        return dimensionMetaCache.get(dimId, key -> {
            return dimensionMetaMapper
                    .selectList(new LambdaQueryWrapper<DimensionMeta>().eq(DimensionMeta::getDimId, key));
        });
    }

    /**
     * 根据指标id和业务类型获取分析元数据
     * 
     * @param indicatorId  指标id
     * @param businessType 业务类型，参见 {@link com.iflytek.cdc.indicator.util.BusinessTypeEnum}
     * @return 分析元数据
     */
    public List<AnalyzeMeta> getAnalyzeMetaList(String indicatorId, String businessType) {
        if (StrUtil.isBlank(indicatorId) || StrUtil.isBlank(businessType)) {
            return Collections.emptyList();
        }
        return analyzeMetaCacheById.get(indicatorId).stream()
                .filter(item -> StrUtil.equals(item.getBusinessType(), businessType))
                .collect(Collectors.toList());
    }

    /**
     * 根据表名、关系id获取分析元数据
     * 
     * @param tableName  表名
     * @param relationId 指标或维度ID
     * @return 分析元数据
     */
    public AnalyzeMeta getByTableAndRelation(String tableName, String relationId) {
        if (StrUtil.isBlank(tableName) || StrUtil.isBlank(relationId)) {
            return null;
        }
        return analyzeMetaCacheByTable.get(tableName).stream()
                .filter(item -> StrUtil.equals(item.getRelationId(), relationId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据表名、列名获取分析元数据
     * 
     * @param tableName  表名
     * @param columnName 列名，可能是物理列名或者虚拟列名
     * @return 分析元数据
     */
    public AnalyzeMeta getByTableAndColumnName(String tableName, String columnName) {
        if (StrUtil.isBlank(tableName) || StrUtil.isBlank(columnName)) {
            return null;
        }
        return analyzeMetaCacheByTable.get(tableName).stream()
                .filter(item -> StrUtil.equals(item.getColumnName(), columnName))
                .findFirst()
                .orElse(null);
    }
}
