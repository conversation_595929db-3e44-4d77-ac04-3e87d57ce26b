package com.iflytek.cdc.indicator.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@TableName("tb_cdcmr_analysis_dim_rollup_value_rel")
public class DimRollValueRel {

    private String id; // 主键
    private String analysisDimId;
    private String dimAttrValueId;
    private String rollupDimId;
    private String rollupAttrValueId;
    private String deleteFlag; // 默认值为 '0'
    private java.sql.Timestamp createTime;
    private java.sql.Timestamp updateTime;
}
