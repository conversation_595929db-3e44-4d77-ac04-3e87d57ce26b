package com.iflytek.cdc.indicator.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@TableName("app.tb_cdcdm_data_dict_value")
public class DataDictValue {

    private String id;
    private String dataDictId;
    private String code;
    private String name;
    private String description;
    private String parentId;
    private String notes;
    private Short status;
    private String deleteFlag;
    private java.sql.Timestamp createTime;
    private java.sql.Timestamp updateTime;
    private String creatorId;
    private String creator;
    private String updaterId;
    private String updater;
}
