package com.iflytek.cdc.indicator.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("app.tb_cdcmr_analysis_dim_value")
public class AnalyzeDImValue {

    private String id;
    private String analysisDimId;
    private String dataAttrValueId;
    private String analysisFlag;
    private String deleteFlag;
    private Timestamp createTime;
    private Timestamp updateTime;
}
