package com.iflytek.cdc.indicator.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("app.tb_cdcmr_indicator_ads_meta")
public class AnalyzeMeta {

    /**
     * 表主键，没有业务含义
     */
    private String id;
    
    private java.sql.Timestamp createTime;
    
    private java.sql.Timestamp updateTime;
    
    private Short deleteFlag;
    
    private String creator;
    
    private String updater;
    
    private String creatorName;
    
    private String updaterName;
    
    private Integer sort;

    /**
     * 中文备注，便于研发人员阅读和配置
     */
    private String memo;

    /**
     * 数仓分析表名，带 schema
     */
    private String tableName;

    /**
     * 数据查询中的列名：可以是数仓分析表实际列名，也可以是 columnFunction 的别名
     */
    private String columnName;

    /**
     * 数据查询中的列类型
     */
    private String columnType;

    /**
     * 数据最终返回时的数据类型，在java接口最终返回前做处理
     */
    private String dataType;

    /**
     * 数据库的列值类型：1 指标值 2 维度值 3 维度表外键
     */
    private Short columnValueType;

    /**
     * 关联ID类型 1 指标 2 维度
     */
    private Short relationIdType;

    /**
     * 关联ID（指标ID或维度ID）
     */
    private String relationId;

    /**
     * 列数据的转换函数
     */
    private String columnFunction;

    /**
     * 数据查询额外返回的列，多个用英文逗号分隔
     */
    private String extraColumns;

    /**
     * 复合指标多层查询用的标识
     */
    private String level;

    /**
     * 数据查询的额外过滤函数
     */
    private String filterFunction;

    /**
     * 复合指标多层查询所依赖的列
     */
    private String dependColumn;

    /**
     * 业务类型 infected 传染病 syndrome 症候群 reportCard 报告卡
     */
    private String businessType;
}
