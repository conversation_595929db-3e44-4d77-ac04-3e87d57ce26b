package com.iflytek.cdc.indicator.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@TableName("app.tb_cdcmr_indicator_dim_meta")
public class DimensionMeta {

    private String id;
    private java.sql.Timestamp createTime;
    private java.sql.Timestamp updateTime;
    private Short deleteFlag;
    private String creator;
    private String updater;
    private String creatorName;
    private String updaterName;
    private Integer sort;
    private String tableName;
    private String columnName;
    private String columnType;
    private String dataType;
    private String dimId;
}
