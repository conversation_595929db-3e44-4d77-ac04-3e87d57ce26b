package com.iflytek.cdc.indicator.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("app.tb_cdcmr_analysis_dimension")
public class AnalyzeDimension {

    private String id;
    private String dimCode;
    private String dimensionName;
    private String dataAttrId;
    private String dimDefinition;
    private String notes;
    private Short status;
    private String deleteFlag;
    private Timestamp createTime;
    private Timestamp updateTime;
    private String creatorId;
    private String creator;
    private String updaterId;
    private String updater;
    private Integer seqNum;
}
