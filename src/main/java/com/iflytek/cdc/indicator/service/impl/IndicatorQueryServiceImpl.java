package com.iflytek.cdc.indicator.service.impl;

import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.cdc.indicator.cache.MetaTableCache;
import com.iflytek.cdc.indicator.dto.CityEnum;
import com.iflytek.cdc.indicator.dto.Dimension;
import com.iflytek.cdc.indicator.dto.DimensionEnum;
import com.iflytek.cdc.indicator.dto.DimensionSql;
import com.iflytek.cdc.indicator.dto.DimensionTimeEnum;
import com.iflytek.cdc.indicator.dto.DistrictEnum;
import com.iflytek.cdc.indicator.dto.IndicatorQueryDTO;
import com.iflytek.cdc.indicator.dto.IndicatorQueryPo;
import com.iflytek.cdc.indicator.dto.IndicatorSql;
import com.iflytek.cdc.indicator.dto.IndicatorSqlResult;
import com.iflytek.cdc.indicator.dto.JoinType;
import com.iflytek.cdc.indicator.dto.ProcessArgResult;
import com.iflytek.cdc.indicator.dto.ProvinceEnum;
import com.iflytek.cdc.indicator.entity.AnalyzeDImValue;
import com.iflytek.cdc.indicator.entity.AnalyzeDimension;
import com.iflytek.cdc.indicator.entity.AnalyzeMeta;
import com.iflytek.cdc.indicator.entity.DataDictValue;
import com.iflytek.cdc.indicator.entity.DimensionMeta;
import com.iflytek.cdc.indicator.mapper.bu.AnalyzeDimValueMapper;
import com.iflytek.cdc.indicator.mapper.bu.AnalyzeDimensionMapper;
import com.iflytek.cdc.indicator.mapper.bu.DataDictValueMapper;
import com.iflytek.cdc.indicator.service.IndicatorQueryExecuteService;
import com.iflytek.cdc.indicator.service.IndicatorQueryService;
import com.iflytek.cdc.indicator.vo.IndicatorQueryVO;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class IndicatorQueryServiceImpl implements IndicatorQueryService {

    @Resource
    private DataDictValueMapper dataDictValueMapper;

    @Resource
    private AnalyzeDimensionMapper analyzeDimensionMapper;

    @Resource
    private AnalyzeDimValueMapper analyzeDimValueMapper;

    @Resource
    private IndicatorQueryExecuteService indicatorQueryExecuteService;

    @Resource
    private MetaTableCache metaTableCache;

    @Override
    public boolean check(List<IndicatorQueryDTO> indicatorQueryDTOS, String businessType) {
        boolean isComposite = false;
        for (IndicatorQueryDTO indicatorQueryDTO : indicatorQueryDTOS) {
            if (indicatorQueryDTO.getIndicatorList() == null || indicatorQueryDTO.getIndicatorList().isEmpty()) {
                throw new MedicalBusinessException("指标id不能为空");
            }
            for (String indicatorId : indicatorQueryDTO.getIndicatorList()) {
                List<AnalyzeMeta> analyzeMetaList = metaTableCache.getAnalyzeMetaList(indicatorId, businessType);
                for (AnalyzeMeta analyzeMeta : analyzeMetaList) {
                    if (analyzeMeta.getLevel() != null
                            && (analyzeMeta.getLevel().equals("2") || analyzeMeta.getLevel().equals("3"))) {
                        isComposite = true;
                        break;
                    }
                }
                if (analyzeMetaList.isEmpty()) {
                    throw new MedicalBusinessException(MessageFormat.format("指标: {0}, {1} 未找到配置", indicatorId, businessType));
                }
            }
            if (indicatorQueryDTO.getDimensionList() != null && !indicatorQueryDTO.getDimensionList().isEmpty()) {
                List<Dimension> dimensionList = indicatorQueryDTO.getDimensionList();
                dimensionList.forEach(item -> {
                    checkDimArgs(item);
                    checkMeta(item, businessType);
                });
                // 排序方便后面重组
                indicatorQueryDTO.getDimensionList().sort(Comparator.comparing(Dimension::getDimensionId));
            }
        }
        return isComposite;
    }

    @Override
    public List<IndicatorQueryVO> query(List<IndicatorQueryDTO> indicatorQueryDTOS, String businessType) {
        ArrayList<IndicatorQueryPo> indicatorQueryPOS = new ArrayList<>();
        // 预处理查询指标和维度
        processDefault(indicatorQueryDTOS);
        // 检查入参，顺便返回是否复合指标查询
        boolean isCompositeQuery = this.check(indicatorQueryDTOS, businessType);
        // 原子指标查询，进行参训参数重组，减少查库次数
        List<IndicatorQueryDTO> restructure = indicatorQueryDTOS;
        if (!isCompositeQuery) {
            restructure = restructure(indicatorQueryDTOS, businessType);
        }
        for (IndicatorQueryDTO indicatorQueryDTO : restructure) {
            List<String> indicatorList = indicatorQueryDTO.getIndicatorList();
            String level3 = filterLevel3(indicatorList, businessType);
            String tableName = "";
            // 原子指标都是数字
            String nullValue = "0";
            if (StringUtils.isNotBlank(level3)) {
                tableName = getTableName(indicatorList.get(0), indicatorQueryDTO.getDimensionList(), businessType);
                AnalyzeMeta analyzeMeta = metaTableCache.getByTableAndRelation(tableName, level3);
                nullValue = this.getDataTypeNullValue(analyzeMeta.getDataType());

                String dependColumn = analyzeMeta.getDependColumn();
                List<String> list = Arrays.asList(dependColumn.split(","));
                String finalTableName = tableName;
                List<AnalyzeMeta> collect = list.stream()
                        .map(item -> metaTableCache.getByTableAndRelation(finalTableName, item))
                        .collect(Collectors.toList());
                List<AnalyzeMeta> indList = collect.stream().filter(item -> item.getRelationIdType() == 1)
                        .collect(Collectors.toList());
                List<AnalyzeMeta> dimList = collect.stream().filter(item -> item.getRelationIdType() == 2)
                        .collect(Collectors.toList());
                indicatorQueryDTO.setIndicatorList(
                        indList.stream().map(AnalyzeMeta::getRelationId).collect(Collectors.toList()));
                List<Dimension> dimensionList = indicatorQueryDTO.getDimensionList();
                dimensionList.addAll(dimList.stream()
                        .map(item -> Dimension.builder().dimensionId(item.getRelationId()).type(DimensionEnum.VALUE)
                                .values(Collections.emptyList()).build())
                        .filter(item -> !dimensionList.stream().map(Dimension::getDimensionId)
                                .collect(Collectors.toList()).contains(item.getDimensionId()))
                        .collect(Collectors.toList()));
            }
            indicatorList = indicatorQueryDTO.getIndicatorList();
            String level2 = filterLevel2(indicatorList, businessType);
            if (StringUtils.isNotBlank(level2)) {
                if (StringUtils.isBlank(level3)) {
                    tableName = getTableName(indicatorList.get(0), indicatorQueryDTO.getDimensionList(), businessType);
                }
                AnalyzeMeta analyzeMeta = metaTableCache.getByTableAndRelation(tableName, level2);
                nullValue = this.getDataTypeNullValue(analyzeMeta.getDataType());

                String dependColumn = analyzeMeta.getDependColumn();
                List<String> list = Arrays.asList(dependColumn.split(","));
                String finalTableName = tableName;
                List<AnalyzeMeta> collect = list.stream()
                        .map(item -> metaTableCache.getByTableAndRelation(finalTableName, item))
                        .collect(Collectors.toList());
                List<AnalyzeMeta> indList = collect.stream().filter(item -> item.getRelationIdType() == 1)
                        .collect(Collectors.toList());
                List<AnalyzeMeta> dimList = collect.stream().filter(item -> item.getRelationIdType() == 2)
                        .collect(Collectors.toList());
                indicatorQueryDTO.setIndicatorList(
                        indList.stream().map(AnalyzeMeta::getRelationId).collect(Collectors.toList()));
                List<Dimension> dimensionList = indicatorQueryDTO.getDimensionList();
                dimensionList.addAll(dimList.stream()
                        .map(item -> Dimension.builder().dimensionId(item.getRelationId()).type(DimensionEnum.VALUE)
                                .values(Collections.emptyList()).build())
                        .filter(item -> !dimensionList.stream().map(Dimension::getDimensionId)
                                .collect(Collectors.toList()).contains(item.getDimensionId()))
                        .collect(Collectors.toList()));
            }
            ProcessArgResult processArgResult = processIndicatorArgs(indicatorQueryDTO, businessType);
            processArgResult.setLevel2(level2);
            processArgResult.setLevel3(level3);
            IndicatorSqlResult execute = indicatorQueryExecuteService.execute(processArgResult, businessType);
            execute.setNullValue(nullValue);

            if (!isCompositeQuery) {
                for (IndicatorQueryDTO queryDTO : indicatorQueryDTOS) {
                    List<String> dimHash = this.getDimHash(queryDTO.getIndicatorList(), queryDTO.getDimensionList(), businessType);
                    String exDimHash = execute.getDimHash();
                    if (dimHash.contains(exDimHash)) {
                        IndicatorQueryPo indicatorQuerypo = new IndicatorQueryPo();
                        indicatorQuerypo.setData(new ArrayList<>(execute.getData()));
                        indicatorQuerypo.setIndicatorSqlResult(execute);
                        indicatorQuerypo.setDimHash(exDimHash);
                        indicatorQueryPOS.add(indicatorQuerypo);
                    }
                }

                mergeIndicator(indicatorQueryPOS);
            } else {
                // 复合指标查询返回的是单列，并且和入参对象是1比1关系，所以在最外层给 map 补上维度列
                for (int i = 0, len = execute.getData().size(); i < len; i++) {
                    final int index = i;
                    Map<String, String> dimMap = indicatorQueryDTO.getDimensionList()
                            .stream()
                            .filter(Dimension::hasValue)
                            .collect(Collectors.toMap(
                                    dim -> "dim_" + dim.getDimensionId(),
                                    dim -> dim.safeGetValue(index),
                                    (v1, v2) -> v1));

                    execute.getData().get(i).putAll(dimMap);
                }
                String exDimHash = execute.getDimHash();
                IndicatorQueryPo indicatorQuerypo = new IndicatorQueryPo();
                indicatorQuerypo.setData(new ArrayList<>(execute.getData()));
                indicatorQuerypo.setIndicatorSqlResult(execute);
                indicatorQuerypo.setDimHash(exDimHash);
                indicatorQueryPOS.add(indicatorQuerypo);
            }
        }

        return indicatorQueryPOS.stream().map(item -> {
            IndicatorQueryVO indicatorQueryVO = new IndicatorQueryVO();
            indicatorQueryVO.setData(item.getData());
            indicatorQueryVO.setDimHash(item.getIndicatorSqlResult().getDimHash());
            indicatorQueryVO.setNullValue(item.getIndicatorSqlResult().getNullValue());
            return indicatorQueryVO;
        }).collect(Collectors.toList());
    }

    private List<String> getDimHash(List<String> indicatorList, List<Dimension> dimensionList, String businessType) {
        ArrayList<String> list = new ArrayList<>();
        for (String indicator : indicatorList) {
            String tableName = this.getTableName(indicator, dimensionList, businessType);
            String finalTableName = tableName;
            List<DimensionSql> dimensionSqls = dimensionList.stream().map(item -> {
                AnalyzeMeta analyzeMeta = metaTableCache.getByTableAndRelation(finalTableName, item.getDimensionId());
                return this.convertDimension(item, analyzeMeta);
            }).collect(Collectors.toList());
            list.add(String.valueOf(Objects.hash(tableName, dimensionSqls)));
        }
        return list;
    }

    private void processDefault(List<IndicatorQueryDTO> indicatorQueryDTOS) {
        for (IndicatorQueryDTO dto : indicatorQueryDTOS) {
            List<Dimension> dimensionList = dto.getDimensionList();
            if (!StringUtils.isBlank(dto.getDay())) {
                Dimension day = Dimension.builder().dimensionId(DimensionTimeEnum.DAY.getDimensionId())
                        .type(DimensionEnum.VALUE)
                        .values(Collections.singletonList(dto.getDay()))
                        .build();
                dimensionList.add(day);
                dimensionList.addAll(createDestDimension("day", dto));
            } else if (!StringUtils.isBlank(dto.getWeek())) {
                dimensionList.addAll(createDestDimension("week", dto));
                Dimension week = Dimension.builder().dimensionId(DimensionTimeEnum.WEEK.getDimensionId())
                        .type(DimensionEnum.VALUE)
                        .values(Collections.singletonList(dto.getWeek()))
                        .build();
                Dimension year = Dimension.builder().dimensionId(DimensionTimeEnum.YEAR_W.getDimensionId())
                        .type(DimensionEnum.VALUE)
                        .values(Collections.singletonList(dto.getYear()))
                        .build();
                dimensionList.addAll(Arrays.asList(week, year));
            } else if (!StringUtils.isBlank(dto.getMeadow())){
                dimensionList.addAll(createDestDimension("meadow", dto));
                Dimension meadow = Dimension.builder().dimensionId(DimensionTimeEnum.MEADOW.getDimensionId())
                        .type(DimensionEnum.VALUE)
                        .values(Collections.singletonList(dto.getMeadow()))
                        .build();
                Dimension monthMW = Dimension.builder().dimensionId(DimensionTimeEnum.MONTH_MW.getDimensionId())
                        .type(DimensionEnum.VALUE)
                        .values(Collections.singletonList(dto.getMonth()))
                        .build();
                Dimension year = Dimension.builder().dimensionId(DimensionTimeEnum.YEAR_Y.getDimensionId())
                        .type(DimensionEnum.VALUE)
                        .values(Collections.singletonList(dto.getYear()))
                        .build();
                dimensionList.addAll(Arrays.asList(year,meadow, monthMW));
            }
            else if (!StringUtils.isBlank(dto.getMonth())) {
                dimensionList.addAll(createDestDimension("month", dto));
                Dimension month = Dimension.builder().dimensionId(DimensionTimeEnum.MONTH.getDimensionId())
                        .type(DimensionEnum.VALUE)
                        .values(Collections.singletonList(dto.getMonth()))
                        .build();
                Dimension year = Dimension.builder().dimensionId(DimensionTimeEnum.YEAR_M.getDimensionId())
                        .type(DimensionEnum.VALUE)
                        .values(Collections.singletonList(dto.getYear()))
                        .build();
                dimensionList.addAll(Arrays.asList(month, year));
            } else if (!StringUtils.isBlank(dto.getQuarter())){
                dimensionList.addAll(createDestDimension("quarter", dto));
                Dimension quarter = Dimension.builder().dimensionId(DimensionTimeEnum.QUARTER.getDimensionId())
                        .type(DimensionEnum.VALUE)
                        .values(Collections.singletonList(dto.getQuarter()))
                        .build();
                Dimension yearQ = Dimension.builder().dimensionId(DimensionTimeEnum.YEAR_Q.getDimensionId())
                        .type(DimensionEnum.VALUE)
                        .values(Collections.singletonList(dto.getYear()))
                        .build();
                dimensionList.addAll(Arrays.asList(quarter, yearQ));
            }else {
                dimensionList.addAll(createDestDimension("year", dto));
                Dimension year = Dimension.builder().dimensionId(DimensionTimeEnum.YEAR_Y.getDimensionId())
                        .type(DimensionEnum.VALUE)
                        .values(Collections.singletonList(dto.getYear()))
                        .build();
                dimensionList.add(year);
            }
        }
    }

    private List<Dimension> createDestDimension(String dimensionTime, IndicatorQueryDTO indicatorQueryDTO) {
        ArrayList<Dimension> dimensions = new ArrayList<>();
        if (!StringUtils.isBlank(indicatorQueryDTO.getDistrict())) {
            DistrictEnum districtEnum = DistrictEnum.getForDimensionId(dimensionTime);
            dimensions.add(Dimension.builder().dimensionId(districtEnum.getDimensionId()).type(DimensionEnum.VALUE)
                    .values(Collections.singletonList(indicatorQueryDTO.getDistrict())).build());
        }
        if (!StringUtils.isBlank(indicatorQueryDTO.getProvince())) {
            ProvinceEnum provinceEnum = ProvinceEnum.getForDimensionId(dimensionTime);
            dimensions.add(Dimension.builder().dimensionId(provinceEnum.getDimensionId()).type(DimensionEnum.VALUE)
                    .values(Collections.singletonList(indicatorQueryDTO.getProvince())).build());
        }
        if (!StringUtils.isBlank(indicatorQueryDTO.getCity())) {
            CityEnum cityEnum = CityEnum.getForDimensionId(dimensionTime);
            dimensions.add(Dimension.builder().dimensionId(cityEnum.getDimensionId()).type(DimensionEnum.VALUE)
                    .values(Collections.singletonList(indicatorQueryDTO.getCity())).build());
        }
        return dimensions;
    }

    // 根据维度列排序后，比较维度列相同的维度值，相同则合并指标列
    private void mergeIndicator(ArrayList<IndicatorQueryPo> indicatorQueryPOS) {
        indicatorQueryPOS.sort((a, b) -> {
            List<String> dimensionColumns = a.getIndicatorSqlResult().getDimensionColumns();
            List<String> dimensionColumns1 = b.getIndicatorSqlResult().getDimensionColumns();
            HashSet<String> strings = new HashSet<>(dimensionColumns);
            HashSet<String> strings1 = new HashSet<>(dimensionColumns1);
            a.setDimHash(String.valueOf(strings.hashCode()));
            b.setDimHash(String.valueOf(strings1.hashCode()));
            return String.valueOf(strings.hashCode()).compareTo(String.valueOf(strings1.hashCode()));
        });

        for (int i = indicatorQueryPOS.size() - 1; i > 0; i--) {
            IndicatorQueryPo last = indicatorQueryPOS.get(i);
            IndicatorQueryPo pre = indicatorQueryPOS.get(i - 1);
            if (last.getDimHash().equals(pre.getDimHash())) {
                List<String> indicatorColumns = last.getIndicatorSqlResult().getIndicatorColumns();
                List<String> indicatorColumns1 = pre.getIndicatorSqlResult().getIndicatorColumns();
                List<Map<String, String>> data = last.getData();
                List<Map<String, String>> data1 = pre.getData();
                for (Map<String, String> stringStringMap : data) {
                    Map<String, String> d1 = new HashMap<>(stringStringMap);
                    indicatorColumns.forEach(d1::remove);
                    for (Map<String, String> stringStringMap1 : data1) {
                        Map<String, String> d2 = new HashMap<>(stringStringMap1);
                        indicatorColumns1.forEach(d2::remove);
                        if (d1.values().equals(d2.values())) {
                            for (String indicatorColumn : indicatorColumns) {
                                String s = stringStringMap.get(indicatorColumn);
                                stringStringMap1.putIfAbsent(indicatorColumn, s);
                            }
                            for (String indicatorColumn : indicatorColumns1) {
                                String s = stringStringMap1.get(indicatorColumn);
                                stringStringMap.putIfAbsent(indicatorColumn, s);
                            }
                        }
                    }
                }
            }
        }

    }

    // 重组，将分析表和维度相同的重组
    public List<IndicatorQueryDTO> restructure(List<IndicatorQueryDTO> indicatorQueryDTOS, String businessType) {
        Map<String, ArrayList<IndicatorQueryDTO>> indicatorQueryDTOHashMap = new HashMap<>();
        List<IndicatorQueryDTO> dtos = new ArrayList<>(indicatorQueryDTOS);
        for (IndicatorQueryDTO indicatorQueryDTO : dtos) {
            List<Dimension> dimensionList = indicatorQueryDTO.getDimensionList();
            List<String> indicatorList = indicatorQueryDTO.getIndicatorList();
            for (String indicator : indicatorList) {
                String tableName = getTableName(indicator, dimensionList, businessType);
                if (indicatorQueryDTOHashMap.get(tableName) == null) {
                    ArrayList<IndicatorQueryDTO> args = new ArrayList<>();
                    IndicatorQueryDTO indicatorQueryDTOBuilder = IndicatorQueryDTO.builder()
                            .indicatorList(new ArrayList<>(Collections.singletonList(indicator)))
                            .dimensionList(indicatorQueryDTO.getDimensionList()).build();
                    args.add(indicatorQueryDTOBuilder);
                    indicatorQueryDTOHashMap.put(tableName, args);
                } else {
                    boolean isFlag = false;
                    List<IndicatorQueryDTO> args = indicatorQueryDTOHashMap.get(tableName);
                    for (IndicatorQueryDTO arg : args) {
                        List<Dimension> dimensionList1 = arg.getDimensionList();
                        if (dimensionList1.equals(dimensionList)) {
                            isFlag = true;
                            arg.getIndicatorList().add(indicator);
                        }
                    }
                    if (!isFlag) {
                        IndicatorQueryDTO indicatorQueryDTOBuilder = IndicatorQueryDTO.builder()
                                .indicatorList(new ArrayList<>(Collections.singletonList(indicator)))
                                .dimensionList(indicatorQueryDTO.getDimensionList()).build();
                        args.add(indicatorQueryDTOBuilder);
                    }
                }
            }
        }
        ArrayList<IndicatorQueryDTO> res = new ArrayList<>();
        Collection<ArrayList<IndicatorQueryDTO>> values = indicatorQueryDTOHashMap.values();
        for (ArrayList<IndicatorQueryDTO> value : values) {
            res.addAll(value);
        }
        return res;
    }

    private String getTableName(String indicator, List<Dimension> dimensionList, String businessType) {
        Set<String> collect = metaTableCache.getAnalyzeMetaList(indicator, businessType)
                .stream().map(AnalyzeMeta::getTableName)
                .collect(Collectors.toSet());
        Set<String> dimTableName = new HashSet<>();
        for (Dimension dimension : dimensionList) {
            List<AnalyzeMeta> analyzeMetaList = metaTableCache.getAnalyzeMetaList(dimension.getDimensionId(), businessType);
            Set<String> collect1 = analyzeMetaList.stream().map(AnalyzeMeta::getTableName).collect(Collectors.toSet());
            if (dimTableName.isEmpty()) {
                dimTableName = collect1;
            } else {
                dimTableName.retainAll(collect1);
            }
        }
        collect.retainAll(dimTableName);
        if (collect.size() > 1) {
            throw new MedicalBusinessException(String.format("分析表存在多个 %s, %s", indicator, dimensionList));
        }
        if (collect.isEmpty()) {
            throw new MedicalBusinessException(String.format("未定位到分析表 %s, %s", indicator, dimensionList));
        }
        return collect.iterator().next();
    }

    private void checkMeta(Dimension item, String businessType) {
        List<DimensionMeta> dimMetas = metaTableCache.getDimMetas(item.getDimensionId());
        List<AnalyzeMeta> analyzeMetaList = metaTableCache.getAnalyzeMetaList(item.getDimensionId(), businessType);

//        if (dimMetas.isEmpty() && analyzeMetaList.isEmpty()) {
//            throw new MedicalBusinessException(MessageFormat.format("维度:{0}未找到配置", item.getDimensionId()));
//        }
        if (!dimMetas.isEmpty()) {
            DimensionMeta dimensionMeta = dimMetas.get(0);
            if (dimensionMeta.getDataType().equals("INTEGER")) {
                item.getValues().forEach(v -> {
                    if (!isNumber(v)) {
                        throw new MedicalBusinessException("维度值类型错误");
                    }
                });
            } else if (dimensionMeta.getDataType().equals("DATE")) {
                item.getValues().forEach(v -> {
                    if (!isDate(v)) {
                        throw new MedicalBusinessException("维度值类型错误");
                    }
                });
            }
        }
    }

    private void checkDimArgs(Dimension item) {
        if (StringUtils.isBlank(item.getDimensionId())) {
            throw new MedicalBusinessException(String.format("[%s]维度id不能为空 ", item.getDimensionId()));
        }
        if (item.getType() == null) {
            throw new MedicalBusinessException(String.format("[%s]维度值类型不能为空 ", item.getDimensionId()));
        }
        item.getValues().forEach(v -> {
            if (StringUtils.isBlank(v)) {
                throw new MedicalBusinessException(String.format("[%s]维度值不能为空 ", item.getDimensionId()));
            }
        });
        if (item.getType().equals(DimensionEnum.RANGE)) {
            List<String> list = item.getValues();

            // 校验列表大小
            if (list.size() < 2) {
                throw new MedicalBusinessException(String.format("[%s]维度值类型错误 ", item.getDimensionId()));
            }

            // 校验第一个和最后一个元素是否为数字
            boolean isFirstElementNumber = isNumber(list.get(0));
            boolean isLastElementNumber = isNumber(list.get(list.size() - 1));

            // 校验第一个和最后一个元素是否为日期
            boolean isFirstElementDate = isDate(list.get(0));
            boolean isLastElementDate = isDate(list.get(list.size() - 1));

            // 检查类型一致性
            if (!(isFirstElementNumber && isLastElementNumber) && !(isFirstElementDate && isLastElementDate)) {
                throw new MedicalBusinessException(String.format("[%s]维度值类型错误 ", item.getDimensionId()));
            }
        }
    }

    private boolean isNumber(String value) {
        try {
            Integer.parseInt(value);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    private boolean isDate(String value) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            dateFormat.setLenient(false);
            dateFormat.parse(value);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    private DimensionSql convertDimension(Dimension dimension, AnalyzeMeta analyzeMeta) {
        DimensionMeta dimensionMeta = new DimensionMeta();
        // 分析表
        String columnName;
        JoinType joinType = null;
        if (analyzeMeta != null) {
            if (analyzeMeta.getColumnFunction() != null) {
                columnName = analyzeMeta.getColumnFunction() + " as " + analyzeMeta.getColumnName();
            } else {
                columnName = analyzeMeta.getColumnName();
            }
            if (analyzeMeta.getColumnValueType() == 3) {
                joinType = new JoinType();
                joinType.setColumn(analyzeMeta.getColumnName());
                AnalyzeDimension analyzeDimension = analyzeDimensionMapper
                        .selectOne(new LambdaQueryWrapper<AnalyzeDimension>().eq(AnalyzeDimension::getId,
                                analyzeMeta.getRelationId()));
                dimensionMeta = metaTableCache.getDimMetas(analyzeMeta.getRelationId()).get(0);
                joinType.setTableName(dimensionMeta.getTableName());
                joinType.setColumn(dimensionMeta.getColumnName());
                List<String> dimensionAttrValues = getDimensionAttrValues(analyzeDimension.getId());
                joinType.setValues(dimensionAttrValues);
                joinType.setDataType(dimensionMeta.getDataType());
            }
        } else {
            analyzeMeta = new AnalyzeMeta();
            columnName = "";
        }

        return DimensionSql.builder().analyzeDimName(columnName)
                .dimensionColumnName(dimensionMeta.getColumnName())
                .dataType(dimensionMeta.getDataType())
                .values(dimension.getValues())
                .type(dimension.getType())
                .dimensionName(dimensionMeta.getTableName())
                .hasJoin(analyzeMeta.getColumnValueType() == 3)
                .joinType(joinType).build();
    }

    private List<String> getDimensionAttrValues(String dimensionId) {
        // 获取维度值
        List<AnalyzeDImValue> analyzeDImValues = analyzeDimValueMapper.selectList(
                new LambdaQueryWrapper<AnalyzeDImValue>().eq(AnalyzeDImValue::getAnalysisDimId, dimensionId));
        List<String> attrValueIds = analyzeDImValues.stream().map(AnalyzeDImValue::getDataAttrValueId)
                .collect(Collectors.toList());
        List<DataDictValue> dataDictValues = dataDictValueMapper
                .selectList(new LambdaQueryWrapper<DataDictValue>().in(DataDictValue::getId, attrValueIds));
        return dataDictValues.stream().map(DataDictValue::getName).collect(Collectors.toList());
    }

    private ProcessArgResult processIndicatorArgs(IndicatorQueryDTO indicatorQueryDTO, String businessType) {
        ProcessArgResult processArgResult = new ProcessArgResult();
        List<String> indicatorList = indicatorQueryDTO.getIndicatorList();
        List<Dimension> dimensionList1 = indicatorQueryDTO.getDimensionList();
        String tableName = getTableName(indicatorList.get(0), dimensionList1, businessType);
        ArrayList<String> extraColumns = new ArrayList<>();
        ArrayList<DimensionSql> dimensionSqls = new ArrayList<>();
        indicatorList = indicatorQueryDTO.getIndicatorList();
        ArrayList<String> columns = new ArrayList<>();
        for (String indicatorId : indicatorList) {
            AnalyzeMeta analyzeMeta = metaTableCache.getByTableAndRelation(tableName, indicatorId);
            String indicatorColumnName = analyzeMeta.getColumnName();
            if (analyzeMeta.getColumnFunction() != null) {
                indicatorColumnName = analyzeMeta.getColumnFunction() + " as " + indicatorColumnName;
            }
            columns.add(indicatorColumnName);
        }
        List<Dimension> dimensionList = indicatorQueryDTO.getDimensionList();
        HashSet<Dimension> dimensions = new HashSet<>(dimensionList);
        String dimHash = String.valueOf(Objects.hash(tableName, dimensions));
        JoinType joinType = null;
        boolean singleRow = true;
        for (Dimension dimension : dimensionList) {
            if (dimension.getValues().size() > 1) {
                singleRow = false;
            }
            AnalyzeMeta analyzeMeta = metaTableCache.getByTableAndRelation(tableName, dimension.getDimensionId());
            if (analyzeMeta.getColumnValueType() != 3 && !StringUtils.isBlank(analyzeMeta.getExtraColumns())) {
                extraColumns.addAll(Arrays.asList(analyzeMeta.getExtraColumns().split(",")));
            }
            DimensionSql dimensionSql = convertDimension(dimension, analyzeMeta);
            if (dimensionSql.isHasJoin()) {
                joinType = dimensionSql.getJoinType();
            }
            dimensionSqls.add(dimensionSql);
        }

        IndicatorSql indicatorSql = IndicatorSql.builder().dimensionSqlList(dimensionSqls).tableName(tableName)
                .columns(columns).dimHash(dimHash).build();
        processArgResult.setIndicatorSql(indicatorSql);
        processArgResult.setJoinType(joinType);
        processArgResult.setExtraColumns(extraColumns);
        processArgResult.setSingleRow(singleRow);
        return processArgResult;
    }

    private String filterLevel2(List<String> indicatorList, String businessType) {
        for (String indicator : indicatorList) {
            List<AnalyzeMeta> analyzeMetaList = metaTableCache.getAnalyzeMetaList(indicator, businessType);
            for (AnalyzeMeta analyzeMeta : analyzeMetaList) {
                if (analyzeMeta.getLevel().equals("2"))
                    return indicator;
            }
        }
        return "";
    }

    private String filterLevel3(List<String> indicatorList, String businessType) {
        for (String indicator : indicatorList) {
            List<AnalyzeMeta> analyzeMetaList = metaTableCache.getAnalyzeMetaList(indicator, businessType);
            for (AnalyzeMeta analyzeMeta : analyzeMetaList) {
                if (analyzeMeta.getLevel().equals("3"))
                    return indicator;
            }
        }
        return "";
    }

    private String getDataTypeNullValue(String dataType) {
        if (dataType != null) {
            if (StrUtil.containsAnyIgnoreCase(dataType, "integer", "long", "int")) {
                return "0";
            }
            if (StrUtil.containsAnyIgnoreCase(dataType, "float", "double", "numeric", "decimal")) {
                return "0.0";
            }
            if (StrUtil.containsAnyIgnoreCase(dataType, "percentage")) {
                return "0.0%";
            }
        }
        return "无";
    }
}
