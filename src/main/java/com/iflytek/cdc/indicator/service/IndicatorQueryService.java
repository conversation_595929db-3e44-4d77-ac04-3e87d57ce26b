package com.iflytek.cdc.indicator.service;

import java.util.List;

import com.iflytek.cdc.indicator.dto.IndicatorQueryDTO;
import com.iflytek.cdc.indicator.vo.IndicatorQueryVO;

public interface IndicatorQueryService {

    boolean check(List<IndicatorQueryDTO> indicatorQueryDTOS, String businessType);

    List<IndicatorQueryVO> query(List<IndicatorQueryDTO> indicatorQueryDTOS, String businessType);
}
