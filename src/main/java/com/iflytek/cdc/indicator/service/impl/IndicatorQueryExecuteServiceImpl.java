package com.iflytek.cdc.indicator.service.impl;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.iflytek.cdc.indicator.cache.MetaTableCache;
import com.iflytek.cdc.indicator.dto.DataTypeDecoratorEnum;
import com.iflytek.cdc.indicator.dto.IndicatorSql;
import com.iflytek.cdc.indicator.dto.IndicatorSqlResult;
import com.iflytek.cdc.indicator.dto.ProcessArgResult;
import com.iflytek.cdc.indicator.entity.AnalyzeMeta;
import com.iflytek.cdc.indicator.service.IndicatorQueryExecuteService;
import com.iflytek.cdc.indicator.service.SqlSerialize;
import com.iflytek.cdc.indicator.util.BusinessTypeEnum;
import com.iflytek.medicalboot.core.exception.MedicalFatalException;

@Service
public class IndicatorQueryExecuteServiceImpl implements IndicatorQueryExecuteService {

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private JdbcTemplate buJdbcTemplate;

    @Resource
    private SqlSerialize sqlSerialize;

    @Resource
    private MetaTableCache metaTableCache;

    @Override
    public IndicatorSqlResult execute(ProcessArgResult processArgResult, String businessType) {
        IndicatorSql indicatorSql = processArgResult.getIndicatorSql();
        // 数仓分析层的表
        final String adsTable = indicatorSql.getTableName();

        String sql = sqlSerialize.build(processArgResult);
        IndicatorSqlResult sqlResult = new IndicatorSqlResult();
        List<String> indicatorColumns = indicatorSql.getColumns().stream().map(item -> {
            if (item.contains(" as ")) {
                return item.substring(item.indexOf(" as ") + 4);
            }
            return item;
        }).collect(Collectors.toList());
        List<String> dimensionColumns = indicatorSql.getDimensionSqlList().stream().map(
                item -> {
                    if (item.getAnalyzeDimName().contains(" as ")) {
                        return item.getAnalyzeDimName().substring(item.getAnalyzeDimName().indexOf(" as ") + 4);
                    }
                    return item.getAnalyzeDimName();
                }).sorted().collect(Collectors.toList());
        sqlResult.setDimHash(String.valueOf(Objects.hash(adsTable, indicatorSql.getDimensionSqlList())));
        sqlResult.setIndicatorColumns(indicatorColumns);
        sqlResult.setDimensionColumns(dimensionColumns);

        List<Map<String, String>> data;
        // 3级复合指标查询
        if (StringUtils.isNotBlank(processArgResult.getLevel3())) {
            AnalyzeMeta analyzeMeta = metaTableCache.getByTableAndRelation(adsTable, processArgResult.getLevel3());
            sqlResult.getIndicatorColumns().add(analyzeMeta.getColumnName());

            String[] split = (analyzeMeta.getColumnName()
                    + (analyzeMeta.getExtraColumns() == null ? "" : "," + analyzeMeta.getExtraColumns())).split(",");
            List<String> collect = Arrays.stream(split).collect(Collectors.toList());

            data = chooseJdbcTemplate(businessType).query(sql, (rs, rowNum) -> {
                HashMap<String, String> rowData = new HashMap<>();
                collect.forEach(item -> {
                    try {
                        String rawValue = rs.getString(item);
                        String[] keyValue = decorateResult(sqlResult, item, adsTable, rawValue);
                        rowData.put(keyValue[0], keyValue[1]);
                    } catch (SQLException e) {
                        throw new MedicalFatalException(String.valueOf(e));
                    }
                });
                return rowData;
            });
        } else if (!StringUtils.isBlank(processArgResult.getLevel2())) {
            // 2级复合指标查询
            AnalyzeMeta analyzeMeta = metaTableCache.getByTableAndRelation(adsTable, processArgResult.getLevel2());
            sqlResult.getIndicatorColumns().add(analyzeMeta.getColumnName());

            String[] split = (analyzeMeta.getColumnName()
                    + (analyzeMeta.getExtraColumns() == null ? "" : "," + analyzeMeta.getExtraColumns())).split(",");
            List<String> collect = Arrays.stream(split).collect(Collectors.toList());

            data = chooseJdbcTemplate(businessType).query(sql, (rs, rowNum) -> {
                HashMap<String, String> rowData = new HashMap<>();
                collect.forEach(item -> {
                    try {
                        String rawValue = rs.getString(item);
                        String[] keyValue = decorateResult(sqlResult, item, adsTable, rawValue);
                        rowData.put(keyValue[0], keyValue[1]);
                    } catch (SQLException e) {
                        throw new MedicalFatalException(String.valueOf(e));
                    }
                });
                return rowData;
            });
        } else {
            // 1级原子指标查询
            data = chooseJdbcTemplate(businessType).query(sql, (rs, rowNum) -> {
                HashMap<String, String> rowData = new HashMap<>();
                indicatorColumns.forEach(item -> {
                    try {
                        String rawValue = rs.getString(item);
                        String[] keyValue = decorateResult(sqlResult, item, adsTable, rawValue);
                        rowData.put(keyValue[0], keyValue[1]);
                    } catch (SQLException e) {
                        throw new MedicalFatalException(String.valueOf(e));
                    }
                });
                processArgResult.getExtraColumns().forEach(item -> {
                    try {
                        String value = rs.getString(item);
                        rowData.put("dim_" + item, value);
                    } catch (SQLException e) {
                        throw new MedicalFatalException(String.valueOf(e));
                    }
                });
                dimensionColumns.forEach(item -> {
                    try {
                        String rawValue = rs.getString(item);
                        String[] keyValue = decorateResult(sqlResult, item, adsTable, rawValue);
                        rowData.put(keyValue[0], keyValue[1]);
                    } catch (SQLException e) {
                        throw new MedicalFatalException(String.valueOf(e));
                    }
                });
                return rowData;
            });
        }

        if (processArgResult.isSingleRow()) {
            sqlResult.setData(mergeToSingleRow(data));
        } else {
            sqlResult.setData(data);
        }

        return sqlResult;
    }

    private String[] decorateResult(IndicatorSqlResult indicatorSqlResult, String column, String table, String value) {
        AnalyzeMeta analyzeMeta = metaTableCache.getByTableAndColumnName(table, column);
        if (analyzeMeta == null) {
            return new String[] { column, value };
        }
        String newKey = indicatorSqlResult.buildMapKey(column, analyzeMeta.getRelationId().split("_")[0]);
        // 目前仅实现对 percentage4 的格式化，以后有需求再扩展
        String newValue = DataTypeDecoratorEnum.decorate(analyzeMeta.getDataType(), value);

        return new String[] { newKey, newValue };
    }

    private List<Map<String, String>> mergeToSingleRow(List<Map<String, String>> data) {
        if (data == null || data.size() <= 1) {
            return data;
        }
        Map<String, String> first = data.get(0);
        Set<String> indexKeys = first.keySet().stream().filter(item -> item.startsWith("ind_"))
                .collect(Collectors.toSet());
        for (int i = 1, len = data.size(); i < len; i++) {
            Map<String, String> map = data.get(i);
            for (String key : indexKeys) {
                first.put(key, first.get(key) + "、" + map.get(key));
            }
        }
        return Collections.singletonList(first);
    }

    private JdbcTemplate chooseJdbcTemplate(String businessType) {
        if (BusinessTypeEnum.isBusinessSource(businessType)) {
            return buJdbcTemplate;
        }
        return jdbcTemplate;
    }
}
