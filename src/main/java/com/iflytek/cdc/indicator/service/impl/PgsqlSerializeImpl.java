package com.iflytek.cdc.indicator.service.impl;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.iflytek.cdc.edr.utils.ArrayUtils;
import com.iflytek.cdc.indicator.cache.MetaTableCache;
import com.iflytek.cdc.indicator.dto.DimensionEnum;
import com.iflytek.cdc.indicator.dto.DimensionSql;
import com.iflytek.cdc.indicator.dto.IndicatorSql;
import com.iflytek.cdc.indicator.dto.JoinType;
import com.iflytek.cdc.indicator.dto.ProcessArgResult;
import com.iflytek.cdc.indicator.entity.AnalyzeMeta;
import com.iflytek.cdc.indicator.service.SqlSerialize;

@Service
public class PgsqlSerializeImpl implements SqlSerialize {

    @Resource
    private MetaTableCache metaTableCache;

    @Override
    public String build(ProcessArgResult processArgResult) {
        IndicatorSql indicatorSql = processArgResult.getIndicatorSql();

        JoinType joinType = processArgResult.getJoinType();
        List<String> extraColumns = processArgResult.getExtraColumns();
        String extra = String.join(",", extraColumns);
        String selectColumns = indicatorSql.getColumns().stream().filter(item -> !extraColumns.contains(item)).collect(Collectors.joining(","));
        String dimColumns = indicatorSql.getDimensionSqlList().stream().map(DimensionSql::getAnalyzeDimName).filter(item -> !selectColumns.contains(item) && !extraColumns.contains(item)).collect(Collectors.joining(","));
        List<DimensionSql> dimensionSqlList = indicatorSql.getDimensionSqlList();
        String dimensionColumns = dimensionSqlList.stream().map(DimensionSql::getAnalyzeDimName).filter(analyzeDimName -> !analyzeDimName.contains(AS)).collect(Collectors.joining(","));
        Map<String, Object> map = dimensionSqlList.stream().filter(item -> !item.getType().equals(DimensionEnum.RANGE) && item.getAnalyzeDimName() != null)
                .distinct().collect(Collectors.toMap(DimensionSql::getAnalyzeDimName, DimensionSql::getValues));
        Map<String, List<String>> rangeMap = dimensionSqlList.stream().filter(item -> item.getType().equals(DimensionEnum.RANGE) && item.getAnalyzeDimName() != null)
                .collect(Collectors.toMap(DimensionSql::getAnalyzeDimName, DimensionSql::getValues));
        String whereClause = buildWhereClause(map, joinType);
        String range = buildRangeClause(rangeMap);
        String extraClause = (StringUtils.isBlank(extra) ? "" : extra);
        String sql = SELECT + selectColumns + (extraClause == "" ? "" : "," + extraClause) + "," + dimColumns + FROM + indicatorSql.getTableName() + buildJoinClause(joinType)
                + whereClause + (whereClause != null && !range.isEmpty() ? (AND + range) : range) + GROUP_BY + (extraClause == "" ? "" : extraClause + ",") + dimensionColumns;
        if (!StringUtils.isBlank(processArgResult.getLevel2())) {
            AnalyzeMeta analyzeMeta = metaTableCache.getByTableAndRelation(processArgResult.getIndicatorSql().getTableName(), processArgResult.getLevel2());
            String columnFunction = analyzeMeta.getColumnFunction() + AS + analyzeMeta.getColumnName() + (analyzeMeta.getExtraColumns() == null ? "" : "," + analyzeMeta.getExtraColumns());
            String filterFunction = analyzeMeta.getFilterFunction();
            sql = SELECT + columnFunction + FROM + LEFT_BRACKET + sql + RIGHT_BRACKET + AS + "t " + (filterFunction == null ? "" : filterFunction);
        }
        if (StringUtils.isNotBlank(processArgResult.getLevel3())) {
            AnalyzeMeta analyzeMeta = metaTableCache.getByTableAndRelation(processArgResult.getIndicatorSql().getTableName(), processArgResult.getLevel3());
            String columnFunction = analyzeMeta.getColumnFunction() + AS + analyzeMeta.getColumnName() + (analyzeMeta.getExtraColumns() == null ? "" : "," + analyzeMeta.getExtraColumns());
            String filterFunction = analyzeMeta.getFilterFunction();
            sql = SELECT + columnFunction + FROM + LEFT_BRACKET + sql + RIGHT_BRACKET + AS + "tt " + (filterFunction == null ? "" : filterFunction);
        }
        return sql;
    }

    private  String buildRangeClause(Map<String, List<String>> rangeMap) {
        if(rangeMap == null || rangeMap.isEmpty()) return "";
        return rangeMap.entrySet().stream()
                .map(e -> buildRangeCondition(e.getKey().contains(AS) ? e.getKey().substring(0, e.getKey().indexOf(AS)) : e.getKey(), e.getValue()))
                .collect(Collectors.joining(AND));

    }

    private static String buildJoinClause(JoinType joinType){
        if(joinType == null) return "";
        return INNER_JOIN + joinType.getTableName() + AS + DIMENSION_ALIAS + ON + joinType.getColumn() + Equals + joinType.getJoinColumn();
    }

    private  String buildWhereClause(Map<String, Object> map, JoinType joinType) {

        String whereClause = map.entrySet().stream()
                .map(e -> buildCondition(e.getKey().contains(AS) ? e.getKey().substring(0, e.getKey().indexOf(AS)) : e.getKey(), e.getValue()))
                .filter(StringUtils::isNotBlank).collect(Collectors.joining(AND));

        String res = whereClause.isEmpty() ? "" : WHERE + whereClause;
        if(joinType != null){
            res =  res + buildCondition(DIMENSION_ALIAS.trim() + POINT + joinType.getColumn(), joinType.getValues());
        }
        return res;
    }

    private  String buildCondition(String key, Object value) {
        if (value == null) {
            return "";
        }

        if (value instanceof Collection || value.getClass().isArray()) {
            Stream<?> stream = value instanceof Collection ? ((Collection<?>) value).stream() : Arrays.stream(ArrayUtils.toObjectArray(value));
            String inValues = stream.map(PgsqlSerializeImpl::bindVal).collect(Collectors.joining(",", LEFT_BRACKET, RIGHT_BRACKET));
            return inValues.equals(LEFT_BRACKET + RIGHT_BRACKET) ? "" : key + IN + inValues;
        }

        return key + EQ + bindVal(value);

    }

    private  String buildRangeCondition(String key, List<String> value) {
        if (value == null) {
            return key + IS_NULL;
        }


        return key + GE + bindVal(value.get(0)) + AND + key + LE + bindVal(value.get(1));

    }

    private static String bindVal(Object value) {
        return (value instanceof String) ? (SINGLE_QUOTE + value + SINGLE_QUOTE) : String.valueOf(value);
    }
    @Override
    public void serialize() {

    }

}
