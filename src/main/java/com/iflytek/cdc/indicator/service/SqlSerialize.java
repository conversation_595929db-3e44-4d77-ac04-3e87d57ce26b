package com.iflytek.cdc.indicator.service;

import com.iflytek.cdc.indicator.dto.ProcessArgResult;

public interface SqlSerialize {

     String SELECT = " select ";
     String FROM = " from ";
     String WHERE = " where ";

     String GROUP_BY = " group by ";
     String ORDER_BY = " order by ";
     String ORDER_BY_ASC = " asc ";
     String ORDER_BY_DESC = " desc ";

     String LIMIT = " limit ";
     String OFFSET = " offset ";
     String LE = "  <= ";
     String GE = " >= ";

     String SINGLE_QUOTE = "'";

     String LEFT_BRACKET = " ( ";
     String RIGHT_BRACKET = " ) ";

     String AS = " as ";
     String AND = " and ";
     String OR = " or ";
     String NOT = " not ";
     String Equals = " = ";


     String BETWEEN = " between ";
     String DISTINCT = " distinct ";

     String IN = " in ";
     String EQ = " = ";
     String IS = " is ";
     String IS_NULL = " is null ";
     String IS_NOT_NULL = " is not null ";
     String INNER_JOIN = " inner join ";
     String ON = " on ";
     String DIMENSION_ALIAS = " DimB ";
     String POINT = ".";
    void serialize();

     String build(ProcessArgResult processArgResult);
}
