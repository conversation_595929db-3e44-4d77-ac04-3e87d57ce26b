package com.iflytek.cdc.indicator.vo;

import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndicatorQueryVO {

    @ApiModelProperty("dimHash")
    private String dimHash;

    @ApiModelProperty("指标查询结果")
    private List<Map<String, String>> data;

    /**
     * 当 data 为空的时候使用此值作为空值
     */
    private String nullValue;
}
