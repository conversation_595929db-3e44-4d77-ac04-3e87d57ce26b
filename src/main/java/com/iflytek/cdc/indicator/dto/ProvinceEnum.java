package com.iflytek.cdc.indicator.dto;


import lombok.Getter;

@Getter
public enum ProvinceEnum {


    YEAR("province_y"),
    MONTH("province_m"),
    WEEK("province_w"),
    QUARTER("province_q"),
    MEADOW("province_mw"),
    DAY("province_d");


    private final String dimensionId;

    ProvinceEnum(String dimensionId) {
        this.dimensionId = dimensionId;
    }

    public static ProvinceEnum getForDimensionId(String dimensionId) {
        for (ProvinceEnum value : ProvinceEnum.values()) {
            if (value.name().toLowerCase().equals(dimensionId)) {
                return value;
            }
        }
        return null;
    }
}
