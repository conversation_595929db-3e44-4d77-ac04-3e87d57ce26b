package com.iflytek.cdc.indicator.dto;


import lombok.Getter;

@Getter
public enum DistrictEnum {


    YEAR("dist_y"),
    MONTH("dist_m"),
    WEEK("dist_w"),
    DAY("dist_d");


    private final String dimensionId;


    DistrictEnum(String dimensionId) {
        this.dimensionId = dimensionId;
    }

    public static DistrictEnum getForDimensionId(String dimensionId) {
        for (DistrictEnum value : DistrictEnum.values()) {
            if (value.name().toLowerCase().equals(dimensionId)) {
                return value;
            }
        }
        return null;
    }
}
