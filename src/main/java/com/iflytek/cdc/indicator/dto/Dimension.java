package com.iflytek.cdc.indicator.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Data
@EqualsAndHashCode
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Dimension {

    @ApiModelProperty(value = "维度id")
    private String dimensionId;

    @ApiModelProperty(value = "维度值类型")
    private DimensionEnum type;

    @ApiModelProperty(value = "维度值")
    private List<String> values;


    public String dimHash() {
        Set<String> valueSet = new HashSet<>(values);
        return String.valueOf(Objects.hash(dimensionId, valueSet));
    }

    /**
     * 安全获取指定序号的维度值，如果超过长度则取最后一个
     *
     * @param index 序号，从 0 开始
     * @return 维度值
     */
    public String safeGetValue(int index) {
        return hasValue() ? values.get(Math.min(index, values.size() - 1)) : null;
    }

    public boolean hasValue() {
        return values != null && !values.isEmpty();
    }
}
