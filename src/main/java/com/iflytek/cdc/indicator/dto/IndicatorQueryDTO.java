package com.iflytek.cdc.indicator.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class IndicatorQueryDTO {


    @ApiModelProperty(value = "指标id")
    private List<String> indicatorList;

    @ApiModelProperty(value = "维度")
    private List<Dimension> dimensionList;

    @ApiModelProperty(value = "日期")
    private String day;

    @ApiModelProperty(value = "年")
    private String year;

    @ApiModelProperty(value = "季")
    private String quarter;

    @ApiModelProperty(value = "月")
    private String month;

    @ApiModelProperty(value = "旬")
    private String meadow;

    @ApiModelProperty(value = "周")
    private String week;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "区/县")
    private String district;
}