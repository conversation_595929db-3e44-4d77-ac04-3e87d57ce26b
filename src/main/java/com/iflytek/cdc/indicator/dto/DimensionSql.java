package com.iflytek.cdc.indicator.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;



@Data
@Builder
public class DimensionSql  {

    private String analyzeDimName;

    private DimensionEnum type;

    private String dataType;

    private List<String> values;

    private boolean hasJoin;

    private String dimensionName;

    private String dimensionColumnName;

    private JoinType joinType;



}
