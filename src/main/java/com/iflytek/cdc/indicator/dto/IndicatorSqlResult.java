package com.iflytek.cdc.indicator.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class IndicatorSqlResult {

    private String dimHash;

    private List<Map<String, String>> data;

    private List<String> indicatorColumns;

    private List<String> dimensionColumns;

    /**
     * 当 data 为空的时候使用此值作为空值
     */
    private String nullValue;

    public String buildMapKey(String column, String id) {
        if (indicatorColumns.contains(column)) {
            return "ind_" + id;
        } else if (dimensionColumns.contains(column)) {
            return "dim_" + id;
        } else {
            return column + "_" + id;
        }
    }
}
