package com.iflytek.cdc.indicator.dto;

import lombok.Getter;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.function.Function;

/**
 * 数据类型装饰器枚举类
 */
@Getter
public enum DataTypeDecoratorEnum {

    /**
     * 转换为百分比，保留4位精度
     */
    PERCENTAGE_4("percentage4", (value) -> {
        if (value == null || value.isEmpty()) {
            return value;
        }
        DecimalFormat df = new DecimalFormat("0.00%");
        try {
            return df.format(new BigDecimal(value));
        } catch (NumberFormatException e) {
            return value;
        }
    });

    /**
     * 编码，对应指标 meta 表配置的数据类型（data_type）
     */
    private final String code;

    /**
     * 转换器
     */
    private final Function<String, String> decorator;

    DataTypeDecoratorEnum(String code, Function<String, String> decorator) {
        this.code = code;
        this.decorator = decorator;
    }

    /**
     * 转换用静态方法，起到工厂类的作用
     *
     * @param code  数据类型编码
     * @param value 转换前的值
     * @return 转换后的值
     */
    public static String decorate(String code, String value) {
        if (PERCENTAGE_4.code.equals(code)) {
            return PERCENTAGE_4.decorator.apply(value);
        }
        return value;
    }
}
