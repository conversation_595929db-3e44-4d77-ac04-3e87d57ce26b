package com.iflytek.cdc.indicator.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;


import java.util.List;
@EqualsAndHashCode(callSuper = true)
@Data
public class IndicatorSqlPo extends IndicatorSql{

    private String column;

    private List<List<String>> values;

    public IndicatorSqlPo(){

    }

    IndicatorSqlPo(String dimHash, String tableName, List<String> columns, List<DimensionSql> dimensionSqlList) {
        super(dimHash, tableName, columns, dimensionSqlList);
    }

}
