package com.iflytek.cdc.indicator.dto;


import lombok.Getter;

@Getter
public enum CityEnum {


    YEAR("city_y"),
    MONTH("city_m"),
    WEEK("city_w"),
    DAY("city_d");
    private final String dimensionId;


    CityEnum(String dimensionId) {
        this.dimensionId = dimensionId;
    }


    public static CityEnum getForDimensionId(String dimensionId) {
        for (CityEnum value : CityEnum.values()) {
            if (value.name().toLowerCase().equals(dimensionId)) {
                return value;
            }
        }
        return null;
    }
}
