package com.iflytek.cdc.indicator.dto;

import lombok.Getter;

@Getter
public enum DimensionTimeEnum {

    /**
     * 年表的年字段
     */
    YEAR_Y("year_y"),

    /**
     * 月表的年字段
     */
    YEAR_M("year_m"),

    /**
     * 周表的年字段
     */
    YEAR_W("year_w"),

    /**
     * 月表的月字段
     */
    MONTH("month_m"),

    /**
     * 年表的季
     */
    YEAR_Q("year_q"),

    /**
     * 季
     */
    QUARTER("quarter"),

    /**
     * 月表的旬
     */
    MONTH_MW("month_mw"),

    /**
     * 旬
     */
    MEADOW("meadow"),

    /**
     * 周表的周字段
     */
    WEEK("week"),

    /**
     * 日表的日字段
     */
    DAY("day");

    /**
     * 存到 meta 表 relation_id 字段的虚拟维度ID
     */
    private final String dimensionId;

    DimensionTimeEnum(String dimensionId) {
        this.dimensionId = dimensionId;
    }
}
