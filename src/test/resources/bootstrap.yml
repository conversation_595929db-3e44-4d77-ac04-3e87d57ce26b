#common:
  #数据库配置
  #    db:
  #        type: postgresql
  #        ip: *************
  #        port: 5432
  #        username: u_cdc_system
  #        password: BH_Y36XJJa757Xw
  #        driver: org.postgresql.Driver
#  db:
#    type: postgresql
#    ip: ************
#    port: 5432
#    username: u_cdc
#    password: NL_dTELoJeo3266
#    driver: org.postgresql.Driver

spring:
  application:
    name: cdc-data-service
  cloud:
    nacos:
      config:
#        server-addr: ************:8848
#        namespace: cdc-test2-5-0
#        username: nacos
#        password: Config@Nacos2090
        server-addr: **************:8848
        namespace: dev-demo2
        username: nacos
        password: afeTW1123_id@#ad
        refreshable-dataids: application.yml,cdc-data-service.yml
      discovery:
        enabled: true
        register-enabled: false

dataService:
  version: v2

cdc:
  elasticsearch:
    enabled: false

logging:
  level:
    com.iflytek.cdc.province.service: debug
