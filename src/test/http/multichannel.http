
###
# @title 呼吸道传染病发病概况
POST /pt/v1/multichannel/processStat/getEpidemiologicalProfile
Content-Type: application/json

{
    "provinceCode": "340000",
    "pickeryType": "0",
    "startDate": "2024-05-25T16:00:00.000Z",
    "endDate": "2025-05-26T15:59:59.999Z",
    "pageIndex": 1,
    "pageSize": 20,
    "dateDimType": "day",
    "warningType": "multichannel",
    "warningScenario": null
}

?? status == 200


###
# @title 呼吸道传染病发病趋势
POST /pt/v1/multichannel/processStat/getDiseaseTrend
Content-Type: application/json

{
    "provinceCode": "340000",
    "pickeryType": "0",
    "startDate": "2024-05-25T16:00:00.000Z",
    "endDate": "2025-05-26T15:59:59.999Z",
    "pageIndex": 1,
    "pageSize": 20,
    "dateDimType": "day",
    "warningType": "multichannel",
    "warningScenario": null
}

###
# @title 呼吸道症候群确诊情况
POST /pt/v1/multichannel/processStat/getConfirmationOfSyndrome
Content-Type: application/json

{
    "provinceCode": "340000",
    "pickeryType": "0",
    "startDate": "2024-05-25T16:00:00.000Z",
    "endDate": "2025-05-26T15:59:59.999Z",
    "pageIndex": 1,
    "pageSize": 20,
    "dateDimType": "day",
    "warningType": "multichannel",
    "warningScenario": null
}

###
# @title 呼吸道感染病例监测流向
POST /pt/v1/multichannel/processStat/getMonitorProcessFlow
Content-Type: application/json

{
    "provinceCode": "340000",
    "pickeryType": "0",
    "startDate": "2024-05-25T16:00:00.000Z",
    "endDate": "2025-05-26T15:59:59.999Z",
    "pageIndex": 1,
    "pageSize": 20,
    "dateDimType": "day",
    "warningType": "multichannel",
    "warningScenario": null
}

###
# @title 肺炎发生情况
POST /pt/v1/multichannel/processStat/getPneumoniaOccursSituation
Content-Type: application/json

{
    "provinceCode": "340000",
    "pickeryType": "0",
    "startDate": "2024-05-25T16:00:00.000Z",
    "endDate": "2025-05-26T15:59:59.999Z",
    "pageIndex": 1,
    "pageSize": 20,
    "dateDimType": "day",
    "warningType": "multichannel",
    "warningScenario": null
}

###
# @title 现存病例量变化
POST /pt/v1/multichannel/processStat/getProcessChanges
Content-Type: application/json

{
    "provinceCode": "340000",
    "pickeryType": "0",
    "startDate": "2024-05-25T16:00:00.000Z",
    "endDate": "2025-05-26T15:59:59.999Z",
    "pageIndex": 1,
    "pageSize": 20,
    "dateDimType": "day",
    "warningType": "multichannel",
    "warningScenario": null
}
