
###
# @title 态势感知推演（实时再生数）
# @description 根据入参查询统计数据，作为算法入参，提交任务并返回任务ID
POST /pt/v1/zt/change/overall/awareness?loginUserId={{loginUserId}}&loginUserName={{loginUserName}}
Content-Type: application/json

{
  "infectCode": null,
  "warningType": "infected",
  "filterCodes": [],
  "startDate": "2024-04-24T16:00:00.000Z",
  "endDate": "2025-04-25T15:59:59.999Z",
  "dateDimType": "day",
  "areaLevel": 1,
  "addressType": "livingAddress",
  "provinceCode": "340000",
  "beforeYear": 2024
}

?? status == 200 && body.length == 32

{{
  // post request script
  $global.awarenessTaskId = response.body;
}}


###
# @title 获取态势感知结果（实时再生数）
# @description 根据任务ID查询结果，因为任务是异步执行的，需要轮询结果
# @sleep 1000
POST /pt/v1/zt/change/overall/loadAwarenessResult?loginUserId={{loginUserId}}&loginUserName={{loginUserName}}
Content-Type: application/json

{
  "awarenessTaskId": "{{$global.awarenessTaskId}}",
  "startDate": "2024-04-24T16:00:00.000Z",
  "endDate": "2025-04-25T15:59:59.999Z"
}

