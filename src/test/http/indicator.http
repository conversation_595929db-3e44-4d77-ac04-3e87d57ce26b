
### 
# 指标查询-简报正文
POST {{host}}/v1/pt/indicator/query/infected
Content-Type: application/json

[
    {
        "year": "2025",
        "province": "340000",
        "indicatorList": [
            "178647656069857458"
        ],
        "dimensionList": [
            {
                "hasSub": false,
                "values": [
                    "乙类传染病"
                ],
                "type": "VALUE",
                "dimensionId": "169661655699947654"
            }
        ]
    },
    {
        "year": "2025",
        "province": "340000",
        "indicatorList": [
            "178574481638293681"
        ],
        "dimensionList": [
            {
                "hasSub": false,
                "values": [
                    "乙类传染病"
                ],
                "type": "VALUE",
                "dimensionId": "169661655699947654"
            }
        ]
    },
    {
        "year": "2025",
        "province": "340000",
        "indicatorList": [
            "170941044853047442"
        ],
        "dimensionList": [
            {
                "hasSub": false,
                "values": [
                    "00061"
                ],
                "type": "VALUE",
                "dimensionId": "169661642815045766"
            }
        ]
    },
    {
        "year": "2025",
        "province": "340000",
        "indicatorList": [
            "178647520778387634"
        ],
        "dimensionList": [
            {
                "hasSub": false,
                "values": [
                    "甲类传染病"
                ],
                "type": "VALUE",
                "dimensionId": "169661655699947654"
            }
        ]
    },
    {
        "year": "2025",
        "province": "340000",
        "indicatorList": [
            "178571610452656305"
        ],
        "dimensionList": [
            {
                "hasSub": false,
                "values": [
                    "甲类传染病"
                ],
                "type": "VALUE",
                "dimensionId": "169661655699947654"
            }
        ]
    },
    {
        "year": "2025",
        "province": "340000",
        "indicatorList": [
            "178647474607489202"
        ],
        "dimensionList": [
            {
                "hasSub": false,
                "values": [
                    "甲类传染病"
                ],
                "type": "VALUE",
                "dimensionId": "169661655699947654"
            }
        ]
    },
    {
        "year": "2025",
        "province": "340000",
        "indicatorList": [
            "178574481638293681"
        ],
        "dimensionList": [
            {
                "hasSub": false,
                "values": [
                    "丙类传染病"
                ],
                "type": "VALUE",
                "dimensionId": "169661655699947654"
            }
        ]
    },
    {
        "year": "2025",
        "province": "340000",
        "indicatorList": [
            "145630836937457780"
        ],
        "dimensionList": [
            {
                "hasSub": false,
                "values": [
                    "00008"
                ],
                "type": "VALUE",
                "dimensionId": "169661642815045766"
            }
        ]
    }
]


### 
# 指标查询-简报附件
POST {{host}}/v1/pt/indicator/query/infected
Content-Type: application/json

[
    {
        "year": "2025",
        "province": "340000",
        "indicatorList": [
            "145630865928487028",
            "178645617034133682",
            "145630836937457780",
            "178645519323627698"
        ],
        "dimensionList": [
            {
                "hasSub": false,
                "values": [
                    "其他传染病",
                    "甲类传染病",
                    "乙类传染病",
                    "丙类传染病"
                ],
                "type": "VALUE",
                "dimensionId": "169661655699947654"
            }
        ]
    }
]

