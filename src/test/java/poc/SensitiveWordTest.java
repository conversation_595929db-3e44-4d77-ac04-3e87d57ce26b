package poc;

import cn.hutool.core.codec.Base64;
import com.iflytek.cdc.CdcDataServiceApplication;
import com.iflytek.cdc.edr.utils.fileUpload.MinioClientUtil;
import com.iflytek.cdc.edr.utils.fileUpload.UploadResult;
import com.iflytek.cdc.province.cache.SensitiveWordCache;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcDataServiceApplication.class)
public class SensitiveWordTest {

    @Resource
    private SensitiveWordCache sensitiveWordCache;

    private static void loadFile() {
        File file = new File("D:\\SensitiveWordList.txt");
        try {
            if (file.isFile() && file.exists()) {
                InputStreamReader inputStreamReader = new InputStreamReader(Files.newInputStream(file.toPath()), StandardCharsets.UTF_8);
                BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
                String line;
                int offset = 1;
                while ((line = bufferedReader.readLine()) != null) {
                    String sql = "insert into app.tb_cdcdm_sensitive_word (id, tag_value) values ('" +
                            (offset++) +
                            "', '" +
                            Base64.encode(line) +
                            "');";
                    System.out.println(sql);
                }
                bufferedReader.close();
                inputStreamReader.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        loadFile();
    }

    @Test
    public void check() {
        System.out.println("TMD: " + sensitiveWordCache.contains("TMD"));
    }

    @Test
    public void uploadFile() throws IOException {
        InputStream is = Files.newInputStream(new File("D:\\SensitiveWordList.txt").toPath());
        UploadResult res = new MinioClientUtil().uploadFile(is, "cdc", "SensitiveWordList.txt", is.available());
        System.out.println(res.getFullPath());

        new MinioClientUtil().deleteFile("cdc", "SensitiveWordList.txt");
    }
}
