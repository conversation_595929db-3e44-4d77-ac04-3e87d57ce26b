package poc;

import com.google.gson.Gson;
import com.iflytek.cdc.CdcDataServiceApplication;
import com.iflytek.cdc.province.dm.DataModel;
import com.iflytek.cdc.province.dm.FilterParam;
import com.iflytek.cdc.province.dm.FormGroup;
import com.iflytek.cdc.province.dm.ModelForm;
import com.iflytek.cdc.province.dm.engine.Column;
import com.iflytek.cdc.province.dm.engine.Convertor;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcDataServiceApplication.class)
public class DataModelTest {

    @Resource
    Convertor convertor;

    private static DataModel mockModel() {

//        例子：
//        模型 = 原始门诊病历
//        表单 = A:门诊病历（不可重复）、B:检查结果（可重复）
//        组   = A:门诊病历 -> A1：患者就诊信息（不可重复）dwd_ch_medical_record；A2: 病历信息；A2: 诊断日志（可重复）dwd_ch_medical_history_log；/ B:检查结果 -> B1：检查项目，检查结果（可重复）dwd_ch_inspection_report
//        字段 = A:门诊病历 ->  A1：门诊就诊信息（不可重复）->A11：姓名，性别，年龄，就诊时间，就诊科室；
//                           A2: 病历信息（不可重复）-> A21：主诉、现病史、诊断；
//                           A3: 诊断（可重复）-> A21：诊断名称，诊断医生；
//              B:检查结果 -> B1：检查结果（可重复）-> B11: 检查项目名称，检查结果

        //字段
        List<Column> columnA1 = new ArrayList<>();
        columnA1.add(Column.builder().columnCode("patient_name").columnName("姓名").fieldName("patient_name").build());
        columnA1.add(Column.builder().columnCode("sex_name").columnName("性别").fieldName("sex_name").build());
        columnA1.add(Column.builder().columnCode("age").columnName("年龄").fieldName("age").build());
        columnA1.add(Column.builder().columnCode("age_unit_name").columnName("年龄单位").fieldName("age_unit_name").build());
        columnA1.add(Column.builder().columnCode("visit_datetime").columnName("就诊时间").fieldName("visit_datetime").build());
        columnA1.add(Column.builder().columnCode("dept_name").columnName("就诊科室").fieldName("dept_name").build());

        List<Column> columnA2 = new ArrayList<>();
        columnA2.add(Column.builder().columnCode("action_in_chief").columnName("主诉").fieldName("action_in_chief").build());
        columnA2.add(Column.builder().columnCode("medical_history_now").columnName("现病史").fieldName("medical_history_now").build());
        columnA2.add(Column.builder().columnCode("diagnose_name").columnName("诊断").fieldName("diagnose_name").build());

        List<Column> columnA3 = new ArrayList<>();
        columnA3.add(Column.builder().columnCode("diagnose_name").columnName("诊断结果").fieldName("diagnose_name").build());
        columnA3.add(Column.builder().columnCode("visit_datetime").columnName("就诊时间").fieldName("visit_datetime").build());

        List<Column> columnB1 = new ArrayList<>();
        columnB1.add(Column.builder().columnCode("exam_item_name").columnName("检查项目").fieldName("exam_item_name").build());
        columnB1.add(Column.builder().columnCode("result").columnName("检查结果").fieldName("result").build());

        //组
        List<FormGroup> groupsA = new ArrayList<>();
        groupsA.add(FormGroup.builder().id("A1").isRepeat(false).name("dw.dwd_ch_medical_record").columns(columnA1).build());
        groupsA.add(FormGroup.builder().id("A2").isRepeat(false).name("dw.dwd_ch_medical_record").columns(columnA2).build());
        groupsA.add(FormGroup.builder().id("A3").isRepeat(true).name("dw.dwd_ch_medical_history_log").columns(columnA3).build());
        List<FormGroup> groupsB = new ArrayList<>();
        groupsA.add(FormGroup.builder().id("B1").isRepeat(true).name("dw.dwd_ch_inspection_report").columns(columnB1).build());

        //表单
        List<ModelForm> formVOS = new ArrayList<>();
        formVOS.add(ModelForm.builder().modelFormId("A").formName("门诊病历").isRepeat(false).groups(groupsA).build());
        formVOS.add(ModelForm.builder().modelFormId("B").formName("检查结果").isRepeat(true).groups(groupsB).build());

        //数据模型
        return DataModel.builder()
                .modelId("1")
                .modelName("门诊病历")
                .modelType("原始数据")
                .modelCode("A001")
                .modelLabel("原始门诊数据")
                .modelDesc("门诊病历的相关数据")
                .formList(formVOS)
                .build();
    }

    @Test
    public void testDataConvert() {
        System.out.println(new Gson().toJson(mockModel()));
        FilterParam filterVO = new FilterParam().put("", "68778503545290869");
        DataModel modelVO = convertor.buildModelData(mockModel(), filterVO);
        System.out.println(new Gson().toJson(modelVO));
    }
}
