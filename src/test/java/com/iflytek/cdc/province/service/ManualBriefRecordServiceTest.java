package com.iflytek.cdc.province.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.AttachmentType;
import com.deepoove.poi.data.Attachments;
import com.deepoove.poi.policy.AttachmentRenderPolicy;
import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.CdcDataServiceApplication;
import com.iflytek.cdc.edr.common.Common;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import com.iflytek.cdc.edr.vo.DesensitizedVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsInfectCntExcelVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.MsInfectCntVO;
import com.iflytek.cdc.province.enums.BriefDateTypeEnum;
import com.iflytek.cdc.province.enums.BriefStatisticsSourceEnum;
import com.iflytek.cdc.province.model.brief.BriefRecordExportModel;
import com.iflytek.cdc.province.model.brief.BriefRecordImportVO;
import com.iflytek.cdc.province.model.brief.ManualBriefGenerateInfo;
import com.iflytek.cdc.province.model.brief.ManualBriefQueryDTO;
import com.iflytek.cdc.province.model.brief.ManualBriefRangeDTO;
import com.iflytek.cdc.province.model.brief.ManualBriefRecordVO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.iflytek.cdc.CdcDataServiceApplication.threadLocal;
import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcDataServiceApplication.class)
public class ManualBriefRecordServiceTest {

    @Resource
    private ManualBriefRecordService manualBriefRecordService;

    private static void renderExcelInWord(List<MsInfectCntVO> statsData, String outFilePath) throws IOException {
        BriefRecordExportModel model = new BriefRecordExportModel();
        model.setRegionName("中书省");
        model.setStartDate("1373年03月01日");
        model.setEndDate("1380年03月01日");
        model.setResidentPopulation(statsData.get(0).getResidentPopulation());
        model.setGenerateTime(DateUtil.formatChineseDate(DateUtil.date(), false, true));

        ByteArrayOutputStream excel = new ByteArrayOutputStream();
        EasyExcel.write(excel, MsInfectCntExcelVO.class).sheet(model.getGenerateTime()).doWrite(
                statsData.stream().map(MsInfectCntExcelVO::of).collect(Collectors.toList()));
        model.setAttachment(Attachments.ofBytes(excel.toByteArray(), AttachmentType.XLSX).create());

        InputStream template = Objects.requireNonNull(Thread.currentThread()
                .getContextClassLoader()
                .getResourceAsStream("template/briefInfectTemplate.docx"));
        Configure build = Configure.builder()
                .bind("attachment", new AttachmentRenderPolicy())
                .useSpringEL()
                .build();
        XWPFTemplate.compile(template, build)
                .render(model)
                .writeToFile(outFilePath);
    }

    private static List<BriefRecordImportVO> readExcel(String filePath) throws IOException {
        MultipartFile file = new MockMultipartFile("brief_in.xlsx", FileUtil.readBytes(filePath));
        return EasyExcel.read(file.getInputStream())
                .head(BriefRecordImportVO.class)
                .headRowNumber(2)
                .sheet()
                .doReadSync();
    }

    private static List<MsInfectCntVO> calculateImportData(List<BriefRecordImportVO> importData, int residentPopulation) {
        List<MsInfectCntVO> result = new ArrayList<>();

        MsInfectCntVO amountVo = new MsInfectCntVO();
        result.add(amountVo);
        for (BriefRecordImportVO item : importData) {
            MsInfectCntVO vo = new MsInfectCntVO();
            BeanUtils.copyProperties(item, vo);
            vo.setResidentPopulation(residentPopulation);
            // 计算合计和比率
            vo.amountTo(amountVo);
            vo.calculateAllRates();
            result.add(vo);
        }
        amountVo.setInfectClass(Common.AMOUNT_TO);
        amountVo.setInfectType(Common.AMOUNT_TO);
        amountVo.setInfectName(Common.AMOUNT_TO);
        amountVo.setResidentPopulation(residentPopulation);
        amountVo.calculateAllRates();

        return result;
    }

    public static void main(String[] args) throws IOException {
        int population = 12345;
        String inFilePath = "D:\\Working\\2024_卫生侧\\2024_Q4_疾控业务支撑\\基线研发工作\\手动导出简报及简报数据导入\\法定传染病疫情概况统计数据模板.xlsx";
        String outFilePath = "D:\\brief_out.docx";

        List<BriefRecordImportVO> importData = readExcel(inFilePath);
        importData.forEach(System.out::println);

        List<MsInfectCntVO> statsData = calculateImportData(importData, population);
        statsData.forEach(System.out::println);

        renderExcelInWord(statsData, outFilePath);
    }

    @Before
    public void setUp() {
        // 伪造用户权限
        threadLocal.set(DesensitizedVO.getDefault());
        userInfo.set(UapUserPo.builder()
                .id("127893589446361218")
                .name("zhuangli5")
                .loginName("17671433479")
                .build());
    }

    @After
    public void tearDown() {
        threadLocal.remove();
        userInfo.remove();
    }

    @Test
    public void generateByImport() {
        ManualBriefGenerateInfo info = new ManualBriefGenerateInfo();
        info.setProvinceCode("340000");
        info.setEndDate(DateUtil.date());
        info.setStartDate(DateUtil.offsetMonth(info.getEndDate(), -13));
        info.setStatisticsSource(BriefStatisticsSourceEnum.IMPORT.getCode());
        info.setCreator(userInfo.get().getLoginName());
        info.setCreatorId(userInfo.get().getId());

        MultipartFile file = new MockMultipartFile("brief_in.xlsx", FileUtil.readBytes("C:\\Users\\<USER>\\Desktop\\法定传染病疫情概况统计数据模板.xlsx"));

        ManualBriefRecordVO vo = manualBriefRecordService.generateByImport(info, file);
        System.out.println(JSON.toJSONString(vo, true));
    }

    @Test
    public void queryList() {
        ManualBriefQueryDTO queryParam = new ManualBriefQueryDTO();
        queryParam.setPageIndex(1);
        queryParam.setPageSize(20);
        queryParam.setDateType(BriefDateTypeEnum.STATISTICS.getCode());
        queryParam.setProvinceCode("340000");
        queryParam.setCreatorId(userInfo.get().getId());

        PageInfo<ManualBriefRecordVO> pageInfo = manualBriefRecordService.queryList(queryParam);
        System.out.println("结果条数: " + pageInfo.getSize());
        if (pageInfo.hasContent()) {
            pageInfo.getList().forEach(System.out::println);
        }
    }

    @Test
    public void getTotalPopulation() {
        ManualBriefRangeDTO range = new ManualBriefRangeDTO();
        range.setProvinceCode("340200");
        range.setEndDate(DateUtil.date());
        range.setStartDate(DateUtil.offsetMonth(range.getEndDate(), -12));

        System.out.println("条件: " + JSON.toJSONString(range));
        System.out.println("人口数: " + manualBriefRecordService.getTotalPopulation(range));
    }
}
