package com.iflytek.cdc.province.service;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.CdcDataServiceApplication;
import com.iflytek.cdc.edr.common.Common;
import com.iflytek.cdc.edr.configuration.EsModelConfigProperties;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import com.iflytek.cdc.edr.enums.LogicEnum;
import com.iflytek.cdc.edr.vo.DesensitizedVO;
import com.iflytek.cdc.province.enums.OperatorTypeEnum;
import com.iflytek.cdc.province.model.dto.dm.CombinationConditionDTO;
import com.iflytek.cdc.province.model.dto.dm.CombinationQueryDTO;
import com.iflytek.cdc.province.model.dto.dm.KeyWordSearchDTO;
import com.iflytek.cdc.province.model.dto.dm.SuggestSearchDTO;
import com.iflytek.cdc.province.model.vo.SuggestWordVO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Collections;

import static com.iflytek.cdc.CdcDataServiceApplication.threadLocal;
import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcDataServiceApplication.class)
public class ModelDataSearchServiceTest {

    @Resource
    private EsModelConfigProperties esModelConfigProperties;

    @Resource
    private ModelDataSearchService modelDataSearchService;

    @Before
    public void setUp() {
        // 伪造用户权限
        threadLocal.set(DesensitizedVO.getDefault());
        userInfo.set(UapUserPo.builder()
                .id("17671433479")
                .name("zhuangli5")
                .orgProvinceCode("340000")
//                .orgCityCode("340100,340200")
//                .orgDistrictCode("340104,340281")
//                .orgStreetCode("340104001,340281100")
                .build());

        if (esModelConfigProperties.isEnabled()) {
            System.out.println("\n=== 当前使用 es 查询 ===\n");
        } else {
            System.out.println("\n=== 当前使用 pg 查询 ===\n");
        }
    }

    @After
    public void tearDown() {
        threadLocal.remove();
        userInfo.remove();
    }

    @Test
    public void retrieveTableSearchByKey() {
        KeyWordSearchDTO dto = new KeyWordSearchDTO();
        dto.setModelId("b45a0b317ab2f0882eba1e5fc2293b46");
        dto.setDiseaseName("腹泻症候群");
        dto.setKeyWord("霍乱");
        dto.setPageIndex(1);
        dto.setPageSize(10);

        dto.setProvinceCode("340000");
//        dto.setCityCode("340200");
//        dto.setDistrictCode("340281");

        PageInfo<String> pageInfo = modelDataSearchService.retrieveTableSearchByKey(dto);
        System.out.println("total: " + pageInfo.getTotal());
        System.out.println("fetch: " + pageInfo.getList().size());
        pageInfo.getList().forEach(System.out::println);
    }

    @Test
    public void retrieveTableSearchByConditions() {
        CombinationConditionDTO condition = new CombinationConditionDTO();
        condition.setModelId("083de6da172833fe8e81e5342661f30b");
        condition.setFormId("449caee737245156d251a97fcaa70835");
        condition.setGroupId("4sF4QLEQWZ1");
        condition.setFieldsId("8zVGgkaPCQc");
        condition.setValue("马晓奇");
        condition.setFieldsType("varchar");
        condition.setOperator(OperatorTypeEnum.EQUAL_TO.getDesc());
        condition.setLogicType(LogicEnum.AND.getName());
        condition.setType(Common.CONDITION);

        CombinationQueryDTO dto = new CombinationQueryDTO();
        dto.setModelId(condition.getModelId());
        dto.setConditionDTOList(Collections.singletonList(condition));
        dto.setPageIndex(1);
        dto.setPageSize(10);

        dto.setProvinceCode("340000");
//        dto.setCityCode("340100");

        PageInfo<String> pageInfo = modelDataSearchService.retrieveTableSearchByConditions(dto);
        System.out.println("total: " + pageInfo.getTotal());
        System.out.println("fetch: " + pageInfo.getList().size());
        pageInfo.getList().forEach(System.out::println);
    }

    @Test
    public void getSuggestWords() {
        SuggestSearchDTO dto = new SuggestSearchDTO();
        dto.setSearchAfter(new Object[]{6, 1, 4.357632});
        dto.setPageIndex(1);
        dto.setPageSize(10);
        dto.setText("风");

        PageInfo<SuggestWordVO> suggests = modelDataSearchService.getSuggestWords(dto);
        System.out.println("total: " + suggests.getTotal());
        System.out.println("fetch: " + suggests.getList().size());
        suggests.getList().forEach(System.out::println);
    }
}
