package com.iflytek.cdc.province.service;

import cn.hutool.core.date.DateUtil;
import com.iflytek.cdc.CdcDataServiceApplication;
import com.iflytek.cdc.edr.entity.uap.UapUserPo;
import com.iflytek.cdc.edr.enums.WarningTypeCodeEnum;
import com.iflytek.cdc.edr.vo.DesensitizedVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.AdsMsProcessRespVO;
import com.iflytek.cdc.edr.vo.adsMsProcess.ProcessAwarenessRespVO;
import com.iflytek.cdc.province.model.dto.AdsMsProcessReqDTO;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

import static com.iflytek.cdc.CdcDataServiceApplication.threadLocal;
import static com.iflytek.cdc.CdcDataServiceApplication.userInfo;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcDataServiceApplication.class)
public class AdsMsProcessServiceTest {

    @Resource
    private AdsMsProcessStatService adsMsProcessStatService;

    @Before
    public void setUp() {
        // 伪造用户权限
        threadLocal.set(DesensitizedVO.getDefault());
        userInfo.set(UapUserPo.builder()
                .id("149516027846197367")
                .name("admin")
                .loginName("13502870513")
                .orgProvinceCode("340000")
                .build());
    }

    @After
    public void tearDown() {
        threadLocal.remove();
        userInfo.remove();
    }

    @Test
    public void groupDiseaseName() {
        AdsMsProcessReqDTO req = new AdsMsProcessReqDTO();
        req.setProvinceCode("340000");
        req.setEndDate(DateUtil.date());
        req.setStartDate(DateUtil.offsetMonth(req.getEndDate(), -12));
        req.setWarningType(WarningTypeCodeEnum.SYNDROME.getName());

        List<AdsMsProcessRespVO> resp = adsMsProcessStatService.groupDiseaseName(req, userInfo.get().getId(), userInfo.get().getLoginName());
        System.out.println("结果条数: " + resp.size());
        resp.forEach(System.out::println);
    }

    @Test
    public void overallAwareness() throws InterruptedException {
        AdsMsProcessReqDTO req = new AdsMsProcessReqDTO();
        req.setAddressType("livingAddress");
        req.setWarningType(WarningTypeCodeEnum.INFECTIOUS.getName());
        req.setAreaLevel(1);
        req.setProvinceCode("340000");
        req.setDateDimType("day");
        req.setStartDate(DateUtil.parseDate("2024-8-27"));
        req.setEndDate(DateUtil.parseDate("2024-10-27"));

        String taskId = adsMsProcessStatService.overallAwareness(req, userInfo.get().getId(), userInfo.get().getLoginName());
        System.out.println("任务id: " + taskId);

        if (taskId != null) {
            Thread.sleep(10_000);

            AdsMsProcessReqDTO resultReq = new AdsMsProcessReqDTO();
            req.setAwarenessTaskId(taskId);
            List<ProcessAwarenessRespVO> results = adsMsProcessStatService.loadAwarenessResult(resultReq, userInfo.get().getId());
            System.out.println("结果条数: " + results.size());
            results.forEach(System.out::println);
        }
    }
}
