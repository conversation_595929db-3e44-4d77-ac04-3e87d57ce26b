package com.iflytek.cdc.province.service;

import cn.hutool.core.io.FileUtil;
import com.iflytek.cdc.CdcDataServiceApplication;
import com.iflytek.cdc.edr.apiService.CdcAdminServiceApi;
import com.iflytek.cdc.edr.vo.DesensitizedVO;
import com.iflytek.cdc.province.model.dto.exportTask.ExportTaskDTO;
import com.iflytek.cdc.province.model.dto.exportTask.TbCdcmrExportTask;
import com.iflytek.cdc.province.service.impl.ModelDataExportServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static com.iflytek.cdc.CdcDataServiceApplication.threadLocal;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcDataServiceApplication.class)
public class ModelDataExportServiceTest {

    @Resource
    private ModelDataExportServiceImpl modelDataExportServiceImpl;

    @Resource
    private ExportTaskService exportTaskService;

    @Resource
    private CdcAdminServiceApi adminServiceApi;

    @Test
    public void testBuildBytes() {
        threadLocal.set(DesensitizedVO.getDefault());

        String taskParam = FileUtil.readUtf8String("D:/test.json");

        TbCdcmrExportTask task = new TbCdcmrExportTask();
        task.setTaskParam(taskParam);
        byte[] bytes = modelDataExportServiceImpl.buildBytes(task);

        FileUtil.writeBytes(bytes, "D:/test.xlsx");
    }

    @Test
    public void rerunTask() throws InterruptedException {
        threadLocal.set(DesensitizedVO.getDefault());

        ExportTaskDTO taskDTO = new ExportTaskDTO();
        taskDTO.setId("173432696041635997");
        TbCdcmrExportTask task = adminServiceApi.checkExistTask(taskDTO, "127893589446361218");

        byte[] bytes = modelDataExportServiceImpl.buildBytes(task);

        exportTaskService.runTaskAndUploadFile(bytes, task);

        Thread.sleep(adminServiceApi.getMaxCsvTimeoutTask() * 1000);
    }
}
