package com.iflytek.cdc.indicator.service;

import cn.hutool.core.collection.ListUtil;
import com.google.gson.Gson;
import com.iflytek.cdc.CdcDataServiceApplication;
import com.iflytek.cdc.indicator.dto.Dimension;
import com.iflytek.cdc.indicator.dto.DimensionEnum;
import com.iflytek.cdc.indicator.dto.IndicatorQueryDTO;
import com.iflytek.cdc.indicator.vo.IndicatorQueryVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CdcDataServiceApplication.class)
public class IndicatorQueryServiceTest {

    @Resource
    private IndicatorQueryService indicatorQueryService;

    @Test
    public void queryTest() {
        // 传染病统计
        // 145630836937457780 【原子】传染病新发病例数 (level = 1)
        // 170941044853047442 【复合】发病数与死亡数不为0的传染病数量 (level = 2)
        // 170942373071683730 【复合】新发病例数TOP5的传染病新发病例数占比 (level = 3)

        List<Dimension> dimensionList = new ArrayList<>();
        dimensionList.add(Dimension.builder()
                .dimensionId("169661655699947654")
                .type(DimensionEnum.VALUE)
                .values(ListUtil.toList("丙类"))
                .build());

        List<String> indicatorList = ListUtil.toList("170942373071683730");

        IndicatorQueryDTO dto = IndicatorQueryDTO.builder()
                .dimensionList(dimensionList)
                .indicatorList(indicatorList)
                .year("2024")
                .province("340000")
                .build();

        List<IndicatorQueryDTO> condition = new ArrayList<>();
        condition.add(dto);

        List<IndicatorQueryVO> result = indicatorQueryService.query(condition);
        System.out.println(new Gson().toJson(result));
    }
}
