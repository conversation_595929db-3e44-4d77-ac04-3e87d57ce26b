import com.iflytek.cdc.edr.utils.DataUtils;
import com.iflytek.cdc.province.model.dto.DateDim;
import com.iflytek.fpva.common.utils.DateUtil;
import org.junit.jupiter.api.Test;

import java.util.List;

public class UtilTests {
    @Test
    public void testDateDimQuarters() {
//        List<DateDim> quarters = DateDim.yearAndQuarter(DateUtil.parseDate("2024-04-03", "yyyy-MM-dd"), DateUtil.parseDate("2024-04-11", "yyyy-MM-dd"));
//        quarters.forEach(s -> {
//            System.out.println(s.getYear() + String.valueOf(s.getQuarter()));
//        });
    }

    @Test
    public void testDateDimMeadow() {
//        List<DateDim> quarters = DateDim.yearAndMonthAndMeadow(DateUtil.parseDate("2023-04-9", "yyyy-MM-dd"), DateUtil.parseDate("2024-04-12", "yyyy-MM-dd"));
//        quarters.forEach(s -> {
//            System.out.println(s.getYear() + " " + (s.getMonth() + 1) + s.getMeadow());
//        });
    }

    @Test
    public void hundredThousandth() {
        System.out.println(DataUtils.hundredThousandth(3, 88000));
    }
}
